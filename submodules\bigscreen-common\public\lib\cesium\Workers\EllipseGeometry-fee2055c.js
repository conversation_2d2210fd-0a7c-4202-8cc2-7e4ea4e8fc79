/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./EllipseGeometryLibrary-07d021fe","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryOffsetAttribute-5a4c2801","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./VertexFormat-86c096b8"],(function(t,e,i,n,r,o,a,s,l,u,m,c,p,y,d){"use strict";const f=new i.Cartesian3,A=new i.Cartesian3,x=new i.Cartesian3,h=new i.Cartesian3,g=new i.Cartesian2,b=new n.Matrix3,_=new n.Matrix3,C=new e.Quaternion,w=new i.Cartesian3,M=new i.Cartesian3,E=new i.Cartesian3,I=new n.Cartographic,T=new i.Cartesian3,G=new i.Cartesian2,N=new i.Cartesian2;function P(t,u,c){const p=u.vertexFormat,y=u.center,d=u.semiMajorAxis,h=u.semiMinorAxis,P=u.ellipsoid,v=u.stRotation,V=c?t.length/3*2:t.length/3,F=u.shadowVolume,D=p.st?new Float32Array(2*V):void 0,O=p.normal?new Float32Array(3*V):void 0,S=p.tangent?new Float32Array(3*V):void 0,L=p.bitangent?new Float32Array(3*V):void 0,R=F?new Float32Array(3*V):void 0;let j=0,z=w,k=M,B=E;const Y=new e.GeographicProjection(P),H=Y.project(P.cartesianToCartographic(y,I),T),U=P.scaleToGeodeticSurface(y,f);P.geodeticSurfaceNormal(U,U);let Q=b,W=_;if(0!==v){let t=e.Quaternion.fromAxisAngle(U,v,C);Q=n.Matrix3.fromQuaternion(t,Q),t=e.Quaternion.fromAxisAngle(U,-v,C),W=n.Matrix3.fromQuaternion(t,W)}else Q=n.Matrix3.clone(n.Matrix3.IDENTITY,Q),W=n.Matrix3.clone(n.Matrix3.IDENTITY,W);const J=i.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,G),q=i.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,N);let Z=t.length;const K=c?Z:0,X=K/3*2;for(let e=0;e<Z;e+=3){const r=e+1,o=e+2,a=i.Cartesian3.fromArray(t,e,f);if(p.st){const t=n.Matrix3.multiplyByVector(Q,a,A),e=Y.project(P.cartesianToCartographic(t,I),x);i.Cartesian3.subtract(e,H,e),g.x=(e.x+d)/(2*d),g.y=(e.y+h)/(2*h),J.x=Math.min(g.x,J.x),J.y=Math.min(g.y,J.y),q.x=Math.max(g.x,q.x),q.y=Math.max(g.y,q.y),c&&(D[j+X]=g.x,D[j+1+X]=g.y),D[j++]=g.x,D[j++]=g.y}(p.normal||p.tangent||p.bitangent||F)&&(z=P.geodeticSurfaceNormal(a,z),F&&(R[e+K]=-z.x,R[r+K]=-z.y,R[o+K]=-z.z),(p.normal||p.tangent||p.bitangent)&&((p.tangent||p.bitangent)&&(k=i.Cartesian3.normalize(i.Cartesian3.cross(i.Cartesian3.UNIT_Z,z,k),k),n.Matrix3.multiplyByVector(W,k,k)),p.normal&&(O[e]=z.x,O[r]=z.y,O[o]=z.z,c&&(O[e+K]=-z.x,O[r+K]=-z.y,O[o+K]=-z.z)),p.tangent&&(S[e]=k.x,S[r]=k.y,S[o]=k.z,c&&(S[e+K]=-k.x,S[r+K]=-k.y,S[o+K]=-k.z)),p.bitangent&&(B=i.Cartesian3.normalize(i.Cartesian3.cross(z,k,B),B),L[e]=B.x,L[r]=B.y,L[o]=B.z,c&&(L[e+K]=B.x,L[r+K]=B.y,L[o+K]=B.z))))}if(p.st){Z=D.length;for(let t=0;t<Z;t+=2)D[t]=(D[t]-J.x)/(q.x-J.x),D[t+1]=(D[t+1]-J.y)/(q.y-J.y)}const $=new l.GeometryAttributes;if(p.position){const e=a.EllipseGeometryLibrary.raisePositionsToHeight(t,u,c);$.position=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e})}if(p.st&&($.st=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),p.normal&&($.normal=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O})),p.tangent&&($.tangent=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),p.bitangent&&($.bitangent=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),F&&($.extrudeDirection=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),c&&o.defined(u.offsetAttribute)){let t=new Uint8Array(V);if(u.offsetAttribute===m.GeometryOffsetAttribute.TOP)t=t.fill(1,0,V/2);else{const e=u.offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;t=t.fill(e)}$.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}return $}function v(t){const e=new Array(t*(t+1)*12-6);let i,n,r,o,a,s=0;for(i=0,r=1,o=0;o<3;o++)e[s++]=r++,e[s++]=i,e[s++]=r;for(o=2;o<t+1;++o){for(r=o*(o+1)-1,i=(o-1)*o-1,e[s++]=r++,e[s++]=i,e[s++]=r,n=2*o,a=0;a<n-1;++a)e[s++]=r,e[s++]=i++,e[s++]=i,e[s++]=r++,e[s++]=i,e[s++]=r;e[s++]=r++,e[s++]=i,e[s++]=r}for(n=2*t,++r,++i,o=0;o<n-1;++o)e[s++]=r,e[s++]=i++,e[s++]=i,e[s++]=r++,e[s++]=i,e[s++]=r;for(e[s++]=r,e[s++]=i++,e[s++]=i,e[s++]=r++,e[s++]=i++,e[s++]=i,++i,o=t-1;o>1;--o){for(e[s++]=i++,e[s++]=i,e[s++]=r,n=2*o,a=0;a<n-1;++a)e[s++]=r,e[s++]=i++,e[s++]=i,e[s++]=r++,e[s++]=i,e[s++]=r;e[s++]=i++,e[s++]=i++,e[s++]=r++}for(o=0;o<3;o++)e[s++]=i++,e[s++]=i,e[s++]=r;return e}let V=new i.Cartesian3;const F=new e.BoundingSphere,D=new e.BoundingSphere;function O(t){const y=t.center,d=t.ellipsoid,_=t.semiMajorAxis;let V=i.Cartesian3.multiplyByScalar(d.geodeticSurfaceNormal(y,f),t.height,f);F.center=i.Cartesian3.add(y,V,F.center),F.radius=_,V=i.Cartesian3.multiplyByScalar(d.geodeticSurfaceNormal(y,V),t.extrudedHeight,V),D.center=i.Cartesian3.add(y,V,D.center),D.radius=_;const O=a.EllipseGeometryLibrary.computeEllipsePositions(t,!0,!0),S=O.positions,L=O.numPts,R=O.outerPositions,j=e.BoundingSphere.union(F,D),z=P(S,t,!0);let k=v(L);const B=k.length;k.length=2*B;const Y=S.length/3;for(let t=0;t<B;t+=3)k[t+B]=k[t+2]+Y,k[t+1+B]=k[t+1]+Y,k[t+2+B]=k[t]+Y;const H=p.IndexDatatype.createTypedArray(2*Y/3,k),U=new s.Geometry({attributes:z,indices:H,primitiveType:s.PrimitiveType.TRIANGLES}),Q=function(t,a){const u=a.vertexFormat,c=a.center,p=a.semiMajorAxis,y=a.semiMinorAxis,d=a.ellipsoid,_=a.height,P=a.extrudedHeight,v=a.stRotation,V=t.length/3*2,F=new Float64Array(3*V),D=u.st?new Float32Array(2*V):void 0,O=u.normal?new Float32Array(3*V):void 0,S=u.tangent?new Float32Array(3*V):void 0,L=u.bitangent?new Float32Array(3*V):void 0,R=a.shadowVolume,j=R?new Float32Array(3*V):void 0;let z=0,k=w,B=M,Y=E;const H=new e.GeographicProjection(d),U=H.project(d.cartesianToCartographic(c,I),T),Q=d.scaleToGeodeticSurface(c,f);d.geodeticSurfaceNormal(Q,Q);const W=e.Quaternion.fromAxisAngle(Q,v,C),J=n.Matrix3.fromQuaternion(W,b),q=i.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,G),Z=i.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,N);let K=t.length;const X=K/3*2;for(let e=0;e<K;e+=3){const r=e+1,o=e+2;let a,s=i.Cartesian3.fromArray(t,e,f);if(u.st){const t=n.Matrix3.multiplyByVector(J,s,A),e=H.project(d.cartesianToCartographic(t,I),x);i.Cartesian3.subtract(e,U,e),g.x=(e.x+p)/(2*p),g.y=(e.y+y)/(2*y),q.x=Math.min(g.x,q.x),q.y=Math.min(g.y,q.y),Z.x=Math.max(g.x,Z.x),Z.y=Math.max(g.y,Z.y),D[z+X]=g.x,D[z+1+X]=g.y,D[z++]=g.x,D[z++]=g.y}s=d.scaleToGeodeticSurface(s,s),a=i.Cartesian3.clone(s,A),k=d.geodeticSurfaceNormal(s,k),R&&(j[e+K]=-k.x,j[r+K]=-k.y,j[o+K]=-k.z);let l=i.Cartesian3.multiplyByScalar(k,_,h);if(s=i.Cartesian3.add(s,l,s),l=i.Cartesian3.multiplyByScalar(k,P,l),a=i.Cartesian3.add(a,l,a),u.position&&(F[e+K]=a.x,F[r+K]=a.y,F[o+K]=a.z,F[e]=s.x,F[r]=s.y,F[o]=s.z),u.normal||u.tangent||u.bitangent){Y=i.Cartesian3.clone(k,Y);const n=i.Cartesian3.fromArray(t,(e+3)%K,h);i.Cartesian3.subtract(n,s,n);const l=i.Cartesian3.subtract(a,s,x);k=i.Cartesian3.normalize(i.Cartesian3.cross(l,n,k),k),u.normal&&(O[e]=k.x,O[r]=k.y,O[o]=k.z,O[e+K]=k.x,O[r+K]=k.y,O[o+K]=k.z),u.tangent&&(B=i.Cartesian3.normalize(i.Cartesian3.cross(Y,k,B),B),S[e]=B.x,S[r]=B.y,S[o]=B.z,S[e+K]=B.x,S[e+1+K]=B.y,S[e+2+K]=B.z),u.bitangent&&(L[e]=Y.x,L[r]=Y.y,L[o]=Y.z,L[e+K]=Y.x,L[r+K]=Y.y,L[o+K]=Y.z)}}if(u.st){K=D.length;for(let t=0;t<K;t+=2)D[t]=(D[t]-q.x)/(Z.x-q.x),D[t+1]=(D[t+1]-q.y)/(Z.y-q.y)}const $=new l.GeometryAttributes;if(u.position&&($.position=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:F})),u.st&&($.st=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),u.normal&&($.normal=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O})),u.tangent&&($.tangent=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),u.bitangent&&($.bitangent=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),R&&($.extrudeDirection=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),o.defined(a.offsetAttribute)){let t=new Uint8Array(V);if(a.offsetAttribute===m.GeometryOffsetAttribute.TOP)t=t.fill(1,0,V/2);else{const e=a.offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;t=t.fill(e)}$.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}return $}(R,t);k=function(t){const e=t.length/3,i=p.IndexDatatype.createTypedArray(e,6*e);let n=0;for(let t=0;t<e;t++){const r=t,o=t+e,a=(r+1)%e,s=a+e;i[n++]=r,i[n++]=o,i[n++]=a,i[n++]=a,i[n++]=o,i[n++]=s}return i}(R);const W=p.IndexDatatype.createTypedArray(2*R.length/3,k),J=new s.Geometry({attributes:Q,indices:W,primitiveType:s.PrimitiveType.TRIANGLES}),q=c.GeometryPipeline.combineInstances([new u.GeometryInstance({geometry:U}),new u.GeometryInstance({geometry:J})]);return{boundingSphere:j,attributes:q[0].attributes,indices:q[0].indices}}function S(t,e,r,o,s,l,u){const m=a.EllipseGeometryLibrary.computeEllipsePositions({center:t,semiMajorAxis:e,semiMinorAxis:r,rotation:o,granularity:s},!1,!0).outerPositions,c=m.length/3,p=new Array(c);for(let t=0;t<c;++t)p[t]=i.Cartesian3.fromArray(m,3*t);const d=n.Rectangle.fromCartesianArray(p,l,u);return d.width>y.CesiumMath.PI&&(d.north=d.north>0?y.CesiumMath.PI_OVER_TWO-y.CesiumMath.EPSILON7:d.north,d.south=d.south<0?y.CesiumMath.EPSILON7-y.CesiumMath.PI_OVER_TWO:d.south,d.east=y.CesiumMath.PI,d.west=-y.CesiumMath.PI),d}function L(t){const e=(t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT)).center,r=o.defaultValue(t.ellipsoid,n.Ellipsoid.WGS84),a=t.semiMajorAxis,s=t.semiMinorAxis,l=o.defaultValue(t.granularity,y.CesiumMath.RADIANS_PER_DEGREE),u=o.defaultValue(t.vertexFormat,d.VertexFormat.DEFAULT),m=o.defaultValue(t.height,0),c=o.defaultValue(t.extrudedHeight,m);this._center=i.Cartesian3.clone(e),this._semiMajorAxis=a,this._semiMinorAxis=s,this._ellipsoid=n.Ellipsoid.clone(r),this._rotation=o.defaultValue(t.rotation,0),this._stRotation=o.defaultValue(t.stRotation,0),this._height=Math.max(c,m),this._granularity=l,this._vertexFormat=d.VertexFormat.clone(u),this._extrudedHeight=Math.min(c,m),this._shadowVolume=o.defaultValue(t.shadowVolume,!1),this._workerName="createEllipseGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}L.packedLength=i.Cartesian3.packedLength+n.Ellipsoid.packedLength+d.VertexFormat.packedLength+9,L.pack=function(t,e,r){return r=o.defaultValue(r,0),i.Cartesian3.pack(t._center,e,r),r+=i.Cartesian3.packedLength,n.Ellipsoid.pack(t._ellipsoid,e,r),r+=n.Ellipsoid.packedLength,d.VertexFormat.pack(t._vertexFormat,e,r),r+=d.VertexFormat.packedLength,e[r++]=t._semiMajorAxis,e[r++]=t._semiMinorAxis,e[r++]=t._rotation,e[r++]=t._stRotation,e[r++]=t._height,e[r++]=t._granularity,e[r++]=t._extrudedHeight,e[r++]=t._shadowVolume?1:0,e[r]=o.defaultValue(t._offsetAttribute,-1),e};const R=new i.Cartesian3,j=new n.Ellipsoid,z=new d.VertexFormat,k={center:R,ellipsoid:j,vertexFormat:z,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};L.unpack=function(t,e,r){e=o.defaultValue(e,0);const a=i.Cartesian3.unpack(t,e,R);e+=i.Cartesian3.packedLength;const s=n.Ellipsoid.unpack(t,e,j);e+=n.Ellipsoid.packedLength;const l=d.VertexFormat.unpack(t,e,z);e+=d.VertexFormat.packedLength;const u=t[e++],m=t[e++],c=t[e++],p=t[e++],y=t[e++],f=t[e++],A=t[e++],x=1===t[e++],h=t[e];return o.defined(r)?(r._center=i.Cartesian3.clone(a,r._center),r._ellipsoid=n.Ellipsoid.clone(s,r._ellipsoid),r._vertexFormat=d.VertexFormat.clone(l,r._vertexFormat),r._semiMajorAxis=u,r._semiMinorAxis=m,r._rotation=c,r._stRotation=p,r._height=y,r._granularity=f,r._extrudedHeight=A,r._shadowVolume=x,r._offsetAttribute=-1===h?void 0:h,r):(k.height=y,k.extrudedHeight=A,k.granularity=f,k.stRotation=p,k.rotation=c,k.semiMajorAxis=u,k.semiMinorAxis=m,k.shadowVolume=x,k.offsetAttribute=-1===h?void 0:h,new L(k))},L.computeRectangle=function(t,e){const i=(t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT)).center,r=o.defaultValue(t.ellipsoid,n.Ellipsoid.WGS84),a=t.semiMajorAxis,s=t.semiMinorAxis,l=o.defaultValue(t.granularity,y.CesiumMath.RADIANS_PER_DEGREE);return S(i,a,s,o.defaultValue(t.rotation,0),l,r,e)},L.createGeometry=function(t){if(t._semiMajorAxis<=0||t._semiMinorAxis<=0)return;const n=t._height,l=t._extrudedHeight,u=!y.CesiumMath.equalsEpsilon(n,l,0,y.CesiumMath.EPSILON2);t._center=t._ellipsoid.scaleToGeodeticSurface(t._center,t._center);const c={center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,ellipsoid:t._ellipsoid,rotation:t._rotation,height:n,granularity:t._granularity,vertexFormat:t._vertexFormat,stRotation:t._stRotation};let d;if(u)c.extrudedHeight=l,c.shadowVolume=t._shadowVolume,c.offsetAttribute=t._offsetAttribute,d=O(c);else if(d=function(t){const n=t.center;V=i.Cartesian3.multiplyByScalar(t.ellipsoid.geodeticSurfaceNormal(n,V),t.height,V),V=i.Cartesian3.add(n,V,V);const r=new e.BoundingSphere(V,t.semiMajorAxis),o=a.EllipseGeometryLibrary.computeEllipsePositions(t,!0,!1),s=o.positions,l=o.numPts,u=P(s,t,!1);let m=v(l);return m=p.IndexDatatype.createTypedArray(s.length/3,m),{boundingSphere:r,attributes:u,indices:m}}(c),o.defined(t._offsetAttribute)){const e=d.attributes.position.values.length,i=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(e/3).fill(i);d.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new s.Geometry({attributes:d.attributes,indices:d.indices,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:d.boundingSphere,offsetAttribute:t._offsetAttribute})},L.createShadowVolume=function(t,e,i){const n=t._granularity,r=t._ellipsoid,o=e(n,r),a=i(n,r);return new L({center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,ellipsoid:r,rotation:t._rotation,stRotation:t._stRotation,granularity:n,extrudedHeight:o,height:a,vertexFormat:d.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(L.prototype,{rectangle:{get:function(){return o.defined(this._rectangle)||(this._rectangle=S(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return o.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){const e=-t._stRotation;if(0===e)return[0,0,0,1,1,0];const n=a.EllipseGeometryLibrary.computeEllipsePositions({center:t._center,semiMajorAxis:t._semiMajorAxis,semiMinorAxis:t._semiMinorAxis,rotation:t._rotation,granularity:t._granularity},!1,!0).outerPositions,r=n.length/3,o=new Array(r);for(let t=0;t<r;++t)o[t]=i.Cartesian3.fromArray(n,3*t);const l=t._ellipsoid,u=t.rectangle;return s.Geometry._textureCoordinateRotationPoints(o,e,l,u)}(this)),this._textureCoordinateRotationPoints}}}),t.EllipseGeometry=L}));
