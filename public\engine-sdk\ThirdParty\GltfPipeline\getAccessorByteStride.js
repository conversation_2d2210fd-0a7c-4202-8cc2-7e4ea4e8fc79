import numberOfComponentsForType from"./numberOfComponentsForType.js";import ComponentDatatype from"../../Core/ComponentDatatype.js";import defined from"../../Core/defined.js";function getAccessorByteStride(e,t){var r=t.bufferView;if(defined(r)){var o=e.bufferViews[r];if(defined(o.byteStride)&&o.byteStride>0)return o.byteStride}return ComponentDatatype.getSizeInBytes(t.componentType)*numberOfComponentsForType(t.type)}export default getAccessorByteStride;