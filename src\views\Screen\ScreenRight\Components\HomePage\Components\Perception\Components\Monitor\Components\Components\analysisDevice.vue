<template>
  <div class="analysis-container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="subtitle-container">
      机电监测分析
      <SelectList
        class="btn-wrap-list"
        :optionList="optionList"
        v-model:selectValue="deviceSystemId"
        :clearable="false"
        width="150px"
        :propertyName="propertyName"
        filterable></SelectList>
      <SwitchButton class="btn-wrap" width="200px" height="22px" v-model="btnList"></SwitchButton>
    </div>
    <div class="relative">
      <MyChart width="600px" height="240px" :option="options" style="position: sticky; z-index: 3; margin-top: 10px"></MyChart>
      <div class="chartBg"></div>
    </div>
    <el-popover placement="top" popper-class="analysis-summary-popover" :width="300" trigger="hover">
      <span>参考《在非旋转部件上测量和评价机器的机械振动》(GB6075.3-2016)<br />阈值：一级阈值4.5mm/s，二级阈值7.1mm/s</span>
      <template #reference>
        <el-icon class="!c-white cursor-pointer !absolute top-45 left-110 z-3"><QuestionFilled /></el-icon>
      </template>
    </el-popover>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import SelectList from '@/components/SelectList/index.vue'
  import SwitchButton from '@/components/SwitchButton/index.vue'
  import MyChart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib'
  const btnList = ref([
    { id: 1, name: '水泵振动', selected: true, subList: [], code: 'sbzd' },
    {
      id: 2,
      name: '风机振动',
      selected: false,
      code: 'fjzd'
    }
  ])
  const propertyName = ref({
    id: 'code',
    name: 'deviceName'
  })
  const optionList = ref([]) // 设备列表
  const deviceSystemId = ref(null) // 设备code

  const selectedBtn = computed(() => {
    return btnList.value.find((_) => _.selected)
  })

  const options = ref({
    grid: {
      top: '35px',
      bottom: '0px',
      left: '20px',
      right: '0px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (e) {
        let str = ``
        e.map((item) => {
          str += `<div><span >${item.marker}${item.seriesName}</span><br />${item.name}时：${item.value.toFixed(2) ?? '-'}mm/s</div>`
        })
        return str
      }
    },
    legend: {
      top: '0',
      right: '0',
      // data: ['速度'],
      textStyle: {
        color: '#D4EFFF',
        fontSize: 14
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: true, // 坐标轴两边留白
      data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
      axisLabel: {
        // interval: 0,
        //	margin:15,
        textStyle: {
          color: '#D4EFFF',
          fontSize: 16
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        splitNumber: 5,
        axisLabel: {
          textStyle: {
            color: '#D4EFFF',
            fontSize: 16
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(166, 218, 255, 0.32)',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        name: '单位:mm/s',
        nameTextStyle: {
          color: '#D4EFFF',
          fontSize: 16
        }
      }
    ],
    series: []
  })

  const generateArray = (arr, n) => {
    return arr.slice(0, n).concat(Array(24 - n).fill(null))
  }

  // 设备列表
  const machineMonitorDeviceList = () => {
    lib.api.bigscreenApi
      .machineMonitorDeviceList({
        code: selectedBtn.value.code
      })
      .then((res) => {
        if (res.success) {
          optionList.value = res.result
          deviceSystemId.value = res.result[0].code
        }
      })
  }
  //  机电监测-数据
  const machineMonitorData = () => {
    const params = {
      code: deviceSystemId.value,
      type: selectedBtn.value.code
    }

    lib.api.bigscreenApi.machineMonitorData(params).then((res) => {
      const colors = ['#2384f3', '#3823f3', '#1DD4AE']
      const colors1 = ['rgba(35, 132, 243,0)', 'rgba(33, 27, 118,0)', 'rgba(29, 212, 174,0)']
      const colors2 = ['rgba(35, 132, 243,0.3)', 'rgba(56, 35, 243,0.3)', 'rgba(29, 212, 174,0.3)']
      if (res.success) {
        const arr = res.result.map((item, index) => {
          const keys = item?.data[0] ? Object.keys(item.data[0]) : null
          const obj = {
            name: item.name,
            type: 'line',
            smooth: true,
            symbol: 'none',
            symbolSize: 5,
            itemStyle: {
              normal: {
                color: colors[index % 3],
                lineStyle: {
                  color: colors[index % 3],
                  width: 2
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: colors1[index % 3]
                    },
                    {
                      offset: 1,
                      color: colors2[index % 3]
                    }
                  ])
                }
              }
            },
            data: keys ? item.data.map((item) => item[keys[0]]) : []
          }
          return obj
        })
        options.value.series = arr
        options.value.xAxis.data = res.result[0]?.data.map((item) => moment(item.time).format('HH:mm'))

        // options.value.series[0].name = res.result[0].name
        // options.value.series[0].data = res.result[0].data.map(item => item[300269])
        // options.value.legend.data = [res.result[0].name]
      }
    })
  }
  watch(
    () => selectedBtn.value,
    () => {
      machineMonitorDeviceList()
    },
    {
      immediate: true
    }
  )
  watch(
    () => [selectedBtn.value, deviceSystemId.value],
    () => {
      // 类型 设备
      if (selectedBtn.value && deviceSystemId.value) {
        machineMonitorData()
      }
    },
    {
      immediate: true
    }
  )
  onMounted(() => {
    // machineMonitorDeviceList()
    init()
  })
  const pauseTime = ref(null)
  const resumeTime = ref(null)
  const init = () => {
    const { pause, resume } = useIntervalFn(async () => {
      const index = btnList.value.findIndex((item) => item.code == selectedBtn.value.code)
      let selectIndex = index + 1
      if (selectIndex > 1) {
        selectIndex = 0
      }
      const selectItem = btnList.value[selectIndex]
      btnList.value.map((item) => (item.selected = false))
      selectItem.selected = true
      selectedBtn.value = selectItem
    }, 10000)
    pauseTime.value = pause
    resumeTime.value = resume
  }
  const handleMouseEnter = () => {
    pauseTime.value && pauseTime.value()
  }
  const handleMouseLeave = () => {
    resumeTime.value && resumeTime.value()
  }
</script>

<style lang="scss" scoped>
  .analysis-container {
    position: relative;
    width: 100%;
    height: 300px;
    margin-top: -24px;

    // height: 200px;
    .subtitle-container {
      width: 628px;
      height: 31px;
      padding-left: 26px;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      font-weight: 400;
      line-height: 14px;
      color: #ecf6ff;
      background: url('@/assets/subheadline2.png') no-repeat;
      .btn-wrap-list {
        position: absolute;
        top: -10px;
        right: 270px;
      }
      .btn-wrap {
        position: absolute;
        top: -5px;
        right: 35px;
      }
    }
    .chartBg {
      position: absolute;
      bottom: -28px;
      z-index: 1;
      width: 653px;
      height: 68px;
      pointer-events: none;
      background: url('@/assets/ScreenRight/Perception/Structure/lineBg.png') no-repeat;
    }
  }
</style>
