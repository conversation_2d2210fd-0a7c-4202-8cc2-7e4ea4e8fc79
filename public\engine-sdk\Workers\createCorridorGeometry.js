define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./arrayRemoveDuplicates-7c710eac","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./PolylineVolumeGeometryLibrary-b0c96b85","./EllipsoidGeodesic-5ac97652","./PolylinePipeline-6bd12257","./CorridorGeometryLibrary-342330d9"],(function(t,e,r,a,i,o,n,s,l,d,u,m,f,y,c,p,h,g,C,b,v,A,_,w,T,G,E){"use strict";var V=new r.Cartesian3,x=new r.Cartesian3,F=new r.Cartesian3,L=new r.Cartesian3,P=new r.Cartesian3,N=new r.Cartesian3,D=new r.Cartesian3,M=new r.Cartesian3;function O(t,e){for(var r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function I(t,e,a,i,o,n){var s=t.normals,l=t.tangents,d=t.bitangents,u=r.Cartesian3.normalize(r.Cartesian3.cross(a,e,D),D);n.normal&&E.CorridorGeometryLibrary.addAttribute(s,e,i,o),n.tangent&&E.CorridorGeometryLibrary.addAttribute(l,u,i,o),n.bitangent&&E.CorridorGeometryLibrary.addAttribute(d,a,i,o)}function k(e,a,i){var o,n,s,l=e.positions,m=e.corners,c=e.endPositions,p=e.lefts,h=e.normals,g=new f.GeometryAttributes,C=0,b=0,v=0;for(n=0;n<l.length;n+=2)s=l[n].length-3,C+=s,v+=2*s,b+=l[n+1].length-3;for(C+=3,b+=3,n=0;n<m.length;n++){o=m[n];var A=m[n].leftPositions;t.defined(A)?(s=A.length,C+=s,v+=s):(s=m[n].rightPositions.length,b+=s,v+=s)}var _,w=t.defined(c);w&&(_=c[0].length-3,C+=_,b+=_,_/=3,v+=6*_);var T,G,P,O,k,S,R=C+b,H=new Float64Array(R),z=a.normal?new Float32Array(R):void 0,B=a.tangent?new Float32Array(R):void 0,U=a.bitangent?new Float32Array(R):void 0,Y={normals:z,tangents:B,bitangents:U},W=0,q=R-1,J=V,j=x,K=_/2,Q=y.IndexDatatype.createTypedArray(R/3,v),X=0;if(w){S=F,k=L;var Z=c[0];for(J=r.Cartesian3.fromArray(h,0,J),j=r.Cartesian3.fromArray(p,0,j),n=0;n<K;n++)S=r.Cartesian3.fromArray(Z,3*(K-1-n),S),k=r.Cartesian3.fromArray(Z,3*(K+n),k),E.CorridorGeometryLibrary.addAttribute(H,k,W),E.CorridorGeometryLibrary.addAttribute(H,S,void 0,q),I(Y,J,j,W,q,a),G=W/3,O=G+1,T=(q-2)/3,P=T-1,Q[X++]=T,Q[X++]=G,Q[X++]=P,Q[X++]=P,Q[X++]=G,Q[X++]=O,W+=3,q-=3}var $,tt,et=0,rt=0,at=l[et++],it=l[et++];for(H.set(at,W),H.set(it,q-it.length+1),j=r.Cartesian3.fromArray(p,rt,j),s=it.length-3,n=0;n<s;n+=3)$=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(at,n,D),D),tt=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(it,s-n,M),M),J=r.Cartesian3.normalize(r.Cartesian3.add($,tt,J),J),I(Y,J,j,W,q,a),G=W/3,O=G+1,T=(q-2)/3,P=T-1,Q[X++]=T,Q[X++]=G,Q[X++]=P,Q[X++]=P,Q[X++]=G,Q[X++]=O,W+=3,q-=3;for($=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(at,s,D),D),tt=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(it,s,M),M),J=r.Cartesian3.normalize(r.Cartesian3.add($,tt,J),J),rt+=3,n=0;n<m.length;n++){var ot;o=m[n];var nt,st,lt=o.leftPositions,dt=o.rightPositions,ut=N,mt=F,ft=L;if(J=r.Cartesian3.fromArray(h,rt,J),t.defined(lt)){for(I(Y,J,j,void 0,q,a),q-=3,nt=O,st=P,ot=0;ot<lt.length/3;ot++)ut=r.Cartesian3.fromArray(lt,3*ot,ut),Q[X++]=nt,Q[X++]=st-ot-1,Q[X++]=st-ot,E.CorridorGeometryLibrary.addAttribute(H,ut,void 0,q),mt=r.Cartesian3.fromArray(H,3*(st-ot-1),mt),ft=r.Cartesian3.fromArray(H,3*nt,ft),j=r.Cartesian3.normalize(r.Cartesian3.subtract(mt,ft,j),j),I(Y,J,j,void 0,q,a),q-=3;ut=r.Cartesian3.fromArray(H,3*nt,ut),mt=r.Cartesian3.subtract(r.Cartesian3.fromArray(H,3*st,mt),ut,mt),ft=r.Cartesian3.subtract(r.Cartesian3.fromArray(H,3*(st-ot),ft),ut,ft),j=r.Cartesian3.normalize(r.Cartesian3.add(mt,ft,j),j),I(Y,J,j,W,void 0,a),W+=3}else{for(I(Y,J,j,W,void 0,a),W+=3,nt=P,st=O,ot=0;ot<dt.length/3;ot++)ut=r.Cartesian3.fromArray(dt,3*ot,ut),Q[X++]=nt,Q[X++]=st+ot,Q[X++]=st+ot+1,E.CorridorGeometryLibrary.addAttribute(H,ut,W),mt=r.Cartesian3.fromArray(H,3*nt,mt),ft=r.Cartesian3.fromArray(H,3*(st+ot),ft),j=r.Cartesian3.normalize(r.Cartesian3.subtract(mt,ft,j),j),I(Y,J,j,W,void 0,a),W+=3;ut=r.Cartesian3.fromArray(H,3*nt,ut),mt=r.Cartesian3.subtract(r.Cartesian3.fromArray(H,3*(st+ot),mt),ut,mt),ft=r.Cartesian3.subtract(r.Cartesian3.fromArray(H,3*st,ft),ut,ft),j=r.Cartesian3.normalize(r.Cartesian3.negate(r.Cartesian3.add(ft,mt,j),j),j),I(Y,J,j,void 0,q,a),q-=3}for(at=l[et++],it=l[et++],at.splice(0,3),it.splice(it.length-3,3),H.set(at,W),H.set(it,q-it.length+1),s=it.length-3,rt+=3,j=r.Cartesian3.fromArray(p,rt,j),ot=0;ot<it.length;ot+=3)$=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(at,ot,D),D),tt=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(it,s-ot,M),M),J=r.Cartesian3.normalize(r.Cartesian3.add($,tt,J),J),I(Y,J,j,W,q,a),O=W/3,G=O-1,P=(q-2)/3,T=P+1,Q[X++]=T,Q[X++]=G,Q[X++]=P,Q[X++]=P,Q[X++]=G,Q[X++]=O,W+=3,q-=3;W-=3,q+=3}if(J=r.Cartesian3.fromArray(h,h.length-3,J),I(Y,J,j,W,q,a),w){W+=3,q-=3,S=F,k=L;var yt=c[1];for(n=0;n<K;n++)S=r.Cartesian3.fromArray(yt,3*(_-n-1),S),k=r.Cartesian3.fromArray(yt,3*n,k),E.CorridorGeometryLibrary.addAttribute(H,S,void 0,q),E.CorridorGeometryLibrary.addAttribute(H,k,W),I(Y,J,j,W,q,a),O=W/3,G=O-1,P=(q-2)/3,T=P+1,Q[X++]=T,Q[X++]=G,Q[X++]=P,Q[X++]=P,Q[X++]=G,Q[X++]=O,W+=3,q-=3}if(g.position=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:H}),a.st){var ct,pt,ht=new Float32Array(R/3*2),gt=0;if(w){C/=3,b/=3;var Ct,bt=Math.PI/(_+1);pt=1/(C-_+1),ct=1/(b-_+1);var vt=_/2;for(n=vt+1;n<_+1;n++)Ct=r.CesiumMath.PI_OVER_TWO+bt*n,ht[gt++]=ct*(1+Math.cos(Ct)),ht[gt++]=.5*(1+Math.sin(Ct));for(n=1;n<b-_+1;n++)ht[gt++]=n*ct,ht[gt++]=0;for(n=_;n>vt;n--)Ct=r.CesiumMath.PI_OVER_TWO-n*bt,ht[gt++]=1-ct*(1+Math.cos(Ct)),ht[gt++]=.5*(1+Math.sin(Ct));for(n=vt;n>0;n--)Ct=r.CesiumMath.PI_OVER_TWO-bt*n,ht[gt++]=1-pt*(1+Math.cos(Ct)),ht[gt++]=.5*(1+Math.sin(Ct));for(n=C-_;n>0;n--)ht[gt++]=n*pt,ht[gt++]=1;for(n=1;n<vt+1;n++)Ct=r.CesiumMath.PI_OVER_TWO+bt*n,ht[gt++]=pt*(1+Math.cos(Ct)),ht[gt++]=.5*(1+Math.sin(Ct))}else{for(C/=3,b/=3,pt=1/(C-1),ct=1/(b-1),n=0;n<b;n++)ht[gt++]=n*ct,ht[gt++]=0;for(n=C;n>0;n--)ht[gt++]=(n-1)*pt,ht[gt++]=1}g.st=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:ht})}return a.normal&&(g.normal=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Y.normals})),a.tangent&&(g.tangent=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Y.tangents})),a.bitangent&&(g.bitangent=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Y.bitangents})),{attributes:g,indices:Q}}function S(t,e){if(!e.normal&&!e.tangent&&!e.bitangent&&!e.st)return t;var a,i,o=t.position.values;(e.normal||e.bitangent)&&(a=t.normal.values,i=t.bitangent.values);var n,s=t.position.values.length/18,l=3*s,d=2*s,u=2*l;if(e.normal||e.bitangent||e.tangent){var m=e.normal?new Float32Array(6*l):void 0,f=e.tangent?new Float32Array(6*l):void 0,y=e.bitangent?new Float32Array(6*l):void 0,c=V,p=x,h=F,g=L,C=P,b=N,v=u;for(n=0;n<l;n+=3){var A=v+u;c=r.Cartesian3.fromArray(o,n,c),p=r.Cartesian3.fromArray(o,n+l,p),h=r.Cartesian3.fromArray(o,(n+3)%l,h),p=r.Cartesian3.subtract(p,c,p),h=r.Cartesian3.subtract(h,c,h),g=r.Cartesian3.normalize(r.Cartesian3.cross(p,h,g),g),e.normal&&(E.CorridorGeometryLibrary.addAttribute(m,g,A),E.CorridorGeometryLibrary.addAttribute(m,g,A+3),E.CorridorGeometryLibrary.addAttribute(m,g,v),E.CorridorGeometryLibrary.addAttribute(m,g,v+3)),(e.tangent||e.bitangent)&&(b=r.Cartesian3.fromArray(a,n,b),e.bitangent&&(E.CorridorGeometryLibrary.addAttribute(y,b,A),E.CorridorGeometryLibrary.addAttribute(y,b,A+3),E.CorridorGeometryLibrary.addAttribute(y,b,v),E.CorridorGeometryLibrary.addAttribute(y,b,v+3)),e.tangent&&(C=r.Cartesian3.normalize(r.Cartesian3.cross(b,g,C),C),E.CorridorGeometryLibrary.addAttribute(f,C,A),E.CorridorGeometryLibrary.addAttribute(f,C,A+3),E.CorridorGeometryLibrary.addAttribute(f,C,v),E.CorridorGeometryLibrary.addAttribute(f,C,v+3))),v+=6}if(e.normal){for(m.set(a),n=0;n<l;n+=3)m[n+l]=-a[n],m[n+l+1]=-a[n+1],m[n+l+2]=-a[n+2];t.normal.values=m}else t.normal=void 0;if(e.bitangent?(y.set(i),y.set(i,l),t.bitangent.values=y):t.bitangent=void 0,e.tangent){var _=t.tangent.values;f.set(_),f.set(_,l),t.tangent.values=f}}if(e.st){var w=t.st.values,T=new Float32Array(6*d);T.set(w),T.set(w,d);for(var G=2*d,D=0;D<2;D++){for(T[G++]=w[0],T[G++]=w[1],n=2;n<d;n+=2){var M=w[n],O=w[n+1];T[G++]=M,T[G++]=O,T[G++]=M,T[G++]=O}T[G++]=w[0],T[G++]=w[1]}t.st.values=T}return t}function R(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(var a=3;a<t.length;a+=3){var i=t[a],o=t[a+1],n=t[a+2];r[e++]=i,r[e++]=o,r[e++]=n,r[e++]=i,r[e++]=o,r[e++]=n}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function H(e,r){var a=new g.VertexFormat({position:r.position,normal:r.normal||r.bitangent||e.shadowVolume,tangent:r.tangent,bitangent:r.normal||r.bitangent,st:r.st}),i=e.ellipsoid,o=E.CorridorGeometryLibrary.computePositions(e),n=k(o,a,i),s=e.height,l=e.extrudedHeight,m=n.attributes,f=n.indices,c=m.position.values,p=c.length,C=new Float64Array(6*p),b=new Float64Array(p);b.set(c);var v,A=new Float64Array(4*p);c=_.PolygonPipeline.scaleToGeodeticHeight(c,s,i),A=R(c,0,A),b=_.PolygonPipeline.scaleToGeodeticHeight(b,l,i),A=R(b,2*p,A),C.set(c),C.set(b,p),C.set(A,2*p),m.position.values=C,m=S(m,r);var w=p/3;if(e.shadowVolume){var T=m.normal.values;p=T.length;var G=new Float32Array(6*p);for(v=0;v<p;v++)T[v]=-T[v];G.set(T,p),G=R(T,4*p,G),m.extrudeDirection=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G}),r.normal||(m.normal=void 0)}if(t.defined(e.offsetAttribute)){var V=new Uint8Array(6*w);if(e.offsetAttribute===h.GeometryOffsetAttribute.TOP)V=h.arrayFill(V,1,0,w),V=h.arrayFill(V,1,2*w,4*w);else{var x=e.offsetAttribute===h.GeometryOffsetAttribute.NONE?0:1;V=h.arrayFill(V,x)}m.applyOffset=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:V})}var F=f.length,L=w+w,P=y.IndexDatatype.createTypedArray(C.length/3,2*F+3*L);P.set(f);var N,D,M,O,I=F;for(v=0;v<F;v+=3){var H=f[v],z=f[v+1],B=f[v+2];P[I++]=B+w,P[I++]=z+w,P[I++]=H+w}for(v=0;v<L;v+=2)N=v+L,D=N+L,M=N+1,O=D+1,P[I++]=N,P[I++]=D,P[I++]=M,P[I++]=M,P[I++]=D,P[I++]=O;return{attributes:m,indices:P}}var z=new r.Cartesian3,B=new r.Cartesian3,U=new a.Cartographic;function Y(t,e,a,i,o,n){var s=r.Cartesian3.subtract(e,t,z);r.Cartesian3.normalize(s,s);var l=a.geodeticSurfaceNormal(t,B),d=r.Cartesian3.cross(s,l,z);r.Cartesian3.multiplyByScalar(d,i,d);var u=o.latitude,m=o.longitude,f=n.latitude,y=n.longitude;r.Cartesian3.add(t,d,B),a.cartesianToCartographic(B,U);var c=U.latitude,p=U.longitude;u=Math.min(u,c),m=Math.min(m,p),f=Math.max(f,c),y=Math.max(y,p),r.Cartesian3.subtract(t,d,B),a.cartesianToCartographic(B,U),c=U.latitude,p=U.longitude,u=Math.min(u,c),m=Math.min(m,p),f=Math.max(f,c),y=Math.max(y,p),o.latitude=u,o.longitude=m,n.latitude=f,n.longitude=y}var W=new r.Cartesian3,q=new r.Cartesian3,J=new a.Cartographic,j=new a.Cartographic;function K(e,i,o,n,s){e=O(e,i);var l=C.arrayRemoveDuplicates(e,r.Cartesian3.equalsEpsilon),d=l.length;if(d<2||o<=0)return new a.Rectangle;var u,m,f=.5*o;if(J.latitude=Number.POSITIVE_INFINITY,J.longitude=Number.POSITIVE_INFINITY,j.latitude=Number.NEGATIVE_INFINITY,j.longitude=Number.NEGATIVE_INFINITY,n===w.CornerType.ROUNDED){var y=l[0];r.Cartesian3.subtract(y,l[1],W),r.Cartesian3.normalize(W,W),r.Cartesian3.multiplyByScalar(W,f,W),r.Cartesian3.add(y,W,q),i.cartesianToCartographic(q,U),u=U.latitude,m=U.longitude,J.latitude=Math.min(J.latitude,u),J.longitude=Math.min(J.longitude,m),j.latitude=Math.max(j.latitude,u),j.longitude=Math.max(j.longitude,m)}for(var c=0;c<d-1;++c)Y(l[c],l[c+1],i,f,J,j);var p=l[d-1];r.Cartesian3.subtract(p,l[d-2],W),r.Cartesian3.normalize(W,W),r.Cartesian3.multiplyByScalar(W,f,W),r.Cartesian3.add(p,W,q),Y(p,q,i,f,J,j),n===w.CornerType.ROUNDED&&(i.cartesianToCartographic(q,U),u=U.latitude,m=U.longitude,J.latitude=Math.min(J.latitude,u),J.longitude=Math.min(J.longitude,m),j.latitude=Math.max(j.latitude,u),j.longitude=Math.max(j.longitude,m));var h=t.defined(s)?s:new a.Rectangle;return h.north=j.latitude,h.south=J.latitude,h.east=j.longitude,h.west=J.longitude,h}function Q(i){i=t.defaultValue(i,t.defaultValue.EMPTY_OBJECT);var o=i.positions,n=i.width;e.Check.defined("options.positions",o),e.Check.defined("options.width",n);var s=t.defaultValue(i.height,0),l=t.defaultValue(i.extrudedHeight,s);this._positions=o,this._ellipsoid=a.Ellipsoid.clone(t.defaultValue(i.ellipsoid,a.Ellipsoid.WGS84)),this._vertexFormat=g.VertexFormat.clone(t.defaultValue(i.vertexFormat,g.VertexFormat.DEFAULT)),this._width=n,this._height=Math.max(s,l),this._extrudedHeight=Math.min(s,l),this._cornerType=t.defaultValue(i.cornerType,w.CornerType.ROUNDED),this._granularity=t.defaultValue(i.granularity,r.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=t.defaultValue(i.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=i.offsetAttribute,this._rectangle=void 0,this.packedLength=1+o.length*r.Cartesian3.packedLength+a.Ellipsoid.packedLength+g.VertexFormat.packedLength+7}Q.pack=function(i,o,n){e.Check.defined("value",i),e.Check.defined("array",o),n=t.defaultValue(n,0);var s=i._positions,l=s.length;o[n++]=l;for(var d=0;d<l;++d,n+=r.Cartesian3.packedLength)r.Cartesian3.pack(s[d],o,n);return a.Ellipsoid.pack(i._ellipsoid,o,n),n+=a.Ellipsoid.packedLength,g.VertexFormat.pack(i._vertexFormat,o,n),n+=g.VertexFormat.packedLength,o[n++]=i._width,o[n++]=i._height,o[n++]=i._extrudedHeight,o[n++]=i._cornerType,o[n++]=i._granularity,o[n++]=i._shadowVolume?1:0,o[n]=t.defaultValue(i._offsetAttribute,-1),o};var X=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),Z=new g.VertexFormat,$={positions:void 0,ellipsoid:X,vertexFormat:Z,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};function tt(e,r){return t.defined(r)&&(e=Q.unpack(e,r)),e._ellipsoid=a.Ellipsoid.clone(e._ellipsoid),Q.createGeometry(e)}return Q.unpack=function(i,o,n){e.Check.defined("array",i),o=t.defaultValue(o,0);for(var s=i[o++],l=new Array(s),d=0;d<s;++d,o+=r.Cartesian3.packedLength)l[d]=r.Cartesian3.unpack(i,o);var u=a.Ellipsoid.unpack(i,o,X);o+=a.Ellipsoid.packedLength;var m=g.VertexFormat.unpack(i,o,Z);o+=g.VertexFormat.packedLength;var f=i[o++],y=i[o++],c=i[o++],p=i[o++],h=i[o++],C=1===i[o++],b=i[o];return t.defined(n)?(n._positions=l,n._ellipsoid=a.Ellipsoid.clone(u,n._ellipsoid),n._vertexFormat=g.VertexFormat.clone(m,n._vertexFormat),n._width=f,n._height=y,n._extrudedHeight=c,n._cornerType=p,n._granularity=h,n._shadowVolume=C,n._offsetAttribute=-1===b?void 0:b,n):($.positions=l,$.width=f,$.height=y,$.extrudedHeight=c,$.cornerType=p,$.granularity=h,$.shadowVolume=C,$.offsetAttribute=-1===b?void 0:b,new Q($))},Q.computeRectangle=function(r,i){r=t.defaultValue(r,t.defaultValue.EMPTY_OBJECT);var o=r.positions,n=r.width;e.Check.defined("options.positions",o),e.Check.defined("options.width",n);var s=t.defaultValue(r.ellipsoid,a.Ellipsoid.WGS84),l=t.defaultValue(r.cornerType,w.CornerType.ROUNDED);return K(o,s,n,l,i)},Q.createGeometry=function(e){var a=e._positions,o=e._width,n=e._ellipsoid;a=O(a,n);var s=C.arrayRemoveDuplicates(a,r.Cartesian3.equalsEpsilon);if(!(s.length<2||o<=0)){var l,f=e._height,y=e._extrudedHeight,c=!r.CesiumMath.equalsEpsilon(f,y,0,r.CesiumMath.EPSILON2),p=e._vertexFormat,g={ellipsoid:n,positions:s,width:o,cornerType:e._cornerType,granularity:e._granularity,saveAttributes:!0};if(c)g.height=f,g.extrudedHeight=y,g.shadowVolume=e._shadowVolume,g.offsetAttribute=e._offsetAttribute,l=H(g,p);else{var b=E.CorridorGeometryLibrary.computePositions(g);if(l=k(b,p,n),l.attributes.position.values=_.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,f,n),t.defined(e._offsetAttribute)){var v=e._offsetAttribute===h.GeometryOffsetAttribute.NONE?0:1,A=l.attributes.position.values.length,w=new Uint8Array(A/3);h.arrayFill(w,v),l.attributes.applyOffset=new u.GeometryAttribute({componentDatatype:d.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:w})}}var T=l.attributes,G=i.BoundingSphere.fromVertices(T.position.values,void 0,3);return p.position||(l.attributes.position.values=void 0),new u.Geometry({attributes:T,indices:l.indices,primitiveType:m.PrimitiveType.TRIANGLES,boundingSphere:G,offsetAttribute:e._offsetAttribute})}},Q.createShadowVolume=function(t,e,r){var a=t._granularity,i=t._ellipsoid,o=e(a,i),n=r(a,i);return new Q({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:o,height:n,vertexFormat:g.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(Q.prototype,{rectangle:{get:function(){return t.defined(this._rectangle)||(this._rectangle=K(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),tt}));