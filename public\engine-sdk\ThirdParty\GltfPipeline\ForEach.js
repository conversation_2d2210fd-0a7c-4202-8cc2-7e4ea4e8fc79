import hasExtension from"./hasExtension.js";import defined from"../../Core/defined.js";function ForEach(){}ForEach.objectLegacy=function(e,r){if(defined(e))for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var t=e[n],i=r(t,n);if(defined(i))return i}},ForEach.object=function(e,r){if(defined(e))for(var n=e.length,t=0;t<n;t++){var i=e[t],o=r(i,t);if(defined(o))return o}},ForEach.topLevel=function(e,r,n){var t=e[r];return defined(t)&&!Array.isArray(t)?ForEach.objectLegacy(t,n):ForEach.object(t,n)},ForEach.accessor=function(e,r){return ForEach.topLevel(e,"accessors",r)},ForEach.accessorWithSemantic=function(e,r,n){var t={};return ForEach.mesh(e,(function(e){return ForEach.meshPrimitive(e,(function(e){var i=ForEach.meshPrimitiveAttribute(e,(function(e,i){if(0===i.indexOf(r)&&!defined(t[e])){t[e]=!0;var o=n(e);if(defined(o))return o}}));return defined(i)?i:ForEach.meshPrimitiveTarget(e,(function(e){return ForEach.meshPrimitiveTargetAttribute(e,(function(e,i){if(0===i.indexOf(r)&&!defined(t[e])){t[e]=!0;var o=n(e);if(defined(o))return o}}))}))}))}))},ForEach.accessorContainingVertexAttributeData=function(e,r){var n={};return ForEach.mesh(e,(function(e){return ForEach.meshPrimitive(e,(function(e){var t=ForEach.meshPrimitiveAttribute(e,(function(e){if(!defined(n[e])){n[e]=!0;var t=r(e);if(defined(t))return t}}));return defined(t)?t:ForEach.meshPrimitiveTarget(e,(function(e){return ForEach.meshPrimitiveTargetAttribute(e,(function(e){if(!defined(n[e])){n[e]=!0;var t=r(e);if(defined(t))return t}}))}))}))}))},ForEach.accessorContainingIndexData=function(e,r){var n={};return ForEach.mesh(e,(function(e){return ForEach.meshPrimitive(e,(function(e){var t=e.indices;if(defined(t)&&!defined(n[t])){n[t]=!0;var i=r(t);if(defined(i))return i}}))}))},ForEach.animation=function(e,r){return ForEach.topLevel(e,"animations",r)},ForEach.animationChannel=function(e,r){var n=e.channels;return ForEach.object(n,r)},ForEach.animationSampler=function(e,r){var n=e.samplers;return ForEach.object(n,r)},ForEach.buffer=function(e,r){return ForEach.topLevel(e,"buffers",r)},ForEach.bufferView=function(e,r){return ForEach.topLevel(e,"bufferViews",r)},ForEach.camera=function(e,r){return ForEach.topLevel(e,"cameras",r)},ForEach.image=function(e,r){return ForEach.topLevel(e,"images",r)},ForEach.compressedImage=function(e,r){if(defined(e.extras)){var n=e.extras.compressedImage3DTiles;for(var t in n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=n[t],o=r(i,t);if(defined(o))return o}}},ForEach.material=function(e,r){return ForEach.topLevel(e,"materials",r)},ForEach.materialValue=function(e,r){var n=e.values;for(var t in defined(e.extensions)&&defined(e.extensions.KHR_techniques_webgl)&&(n=e.extensions.KHR_techniques_webgl.values),n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=r(n[t],t);if(defined(i))return i}},ForEach.mesh=function(e,r){return ForEach.topLevel(e,"meshes",r)},ForEach.meshPrimitive=function(e,r){var n=e.primitives;if(defined(n))for(var t=n.length,i=0;i<t;i++){var o=n[i],a=r(o,i);if(defined(a))return a}},ForEach.meshPrimitiveAttribute=function(e,r){var n=e.attributes;for(var t in n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=r(n[t],t);if(defined(i))return i}},ForEach.meshPrimitiveTarget=function(e,r){var n=e.targets;if(defined(n))for(var t=n.length,i=0;i<t;++i){var o=r(n[i],i);if(defined(o))return o}},ForEach.meshPrimitiveTargetAttribute=function(e,r){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var t=e[n],i=r(t,n);if(defined(i))return i}},ForEach.node=function(e,r){return ForEach.topLevel(e,"nodes",r)},ForEach.nodeInTree=function(e,r,n){var t=e.nodes;if(defined(t))for(var i=r.length,o=0;o<i;o++){var a=r[o],c=t[a];if(defined(c)){var f=n(c,a);if(defined(f))return f;var u=c.children;if(defined(u)&&(f=ForEach.nodeInTree(e,u,n),defined(f)))return f}}},ForEach.nodeInScene=function(e,r,n){var t=r.nodes;if(defined(t))return ForEach.nodeInTree(e,t,n)},ForEach.program=function(e,r){return hasExtension(e,"KHR_techniques_webgl")?ForEach.object(e.extensions.KHR_techniques_webgl.programs,r):ForEach.topLevel(e,"programs",r)},ForEach.sampler=function(e,r){return ForEach.topLevel(e,"samplers",r)},ForEach.scene=function(e,r){return ForEach.topLevel(e,"scenes",r)},ForEach.shader=function(e,r){return hasExtension(e,"KHR_techniques_webgl")?ForEach.object(e.extensions.KHR_techniques_webgl.shaders,r):ForEach.topLevel(e,"shaders",r)},ForEach.skin=function(e,r){return ForEach.topLevel(e,"skins",r)},ForEach.skinJoint=function(e,r){var n=e.joints;if(defined(n))for(var t=n.length,i=0;i<t;i++){var o=n[i],a=r(o);if(defined(a))return a}},ForEach.techniqueAttribute=function(e,r){var n=e.attributes;for(var t in n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=r(n[t],t);if(defined(i))return i}},ForEach.techniqueUniform=function(e,r){var n=e.uniforms;for(var t in n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=r(n[t],t);if(defined(i))return i}},ForEach.techniqueParameter=function(e,r){var n=e.parameters;for(var t in n)if(Object.prototype.hasOwnProperty.call(n,t)){var i=r(n[t],t);if(defined(i))return i}},ForEach.technique=function(e,r){return hasExtension(e,"KHR_techniques_webgl")?ForEach.object(e.extensions.KHR_techniques_webgl.techniques,r):ForEach.topLevel(e,"techniques",r)},ForEach.texture=function(e,r){return ForEach.topLevel(e,"textures",r)};export default ForEach;