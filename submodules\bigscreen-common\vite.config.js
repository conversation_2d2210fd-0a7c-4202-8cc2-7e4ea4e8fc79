import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import htmlConfig from 'vite-plugin-html-config'
import { viteExternalsPlugin } from 'vite-plugin-externals'
import UnoCss from 'unocss/vite'


// https://vitejs.dev/config/
export default ({ mode: VITE_MODE }) => {
  const env = loadEnv(VITE_MODE, process.cwd())

  const plugins = [
    vue(),
    vueJsx(),
    UnoCss()
  ]

  const externalConfig = viteExternalsPlugin({
    cesium: 'Cesium'
  })
  const htmlConfigs = htmlConfig({
    headScripts: [
      {
        src: '/lib/cesium/Cesium.js'
      }
    ],
    links: [
      {
        // ******* Always use external CesiumJS's style util official package output. *******
        // ******* 直到官方 CesiumJS 包导出样式文件前，都使用外部样式 *******
        rel: 'stylesheet',
        href: '/lib/cesium/Widgets/widgets.css'
      }
    ]
  })
  plugins.push(externalConfig, htmlConfigs)

  return defineConfig({
    root: './',
    base: '/bigscreen-common/',
    build: {
      assetsDir: './',
      minify: !['false'].includes(env.VITE_IS_MINIFY)
    },
    plugins: plugins,
    resolve: {
      alias: {
        '@Common': fileURLToPath(new URL('./src', import.meta.url))
      }
    }
  })
}
