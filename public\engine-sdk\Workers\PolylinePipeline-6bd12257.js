define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./Matrix4-a50b021f","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./EllipsoidRhumbLine-f49ff2c9","./EllipsoidGeodesic-5ac97652"],(function(a,e,r,t,i,n,o,s,c,l){"use strict";var u={getTriangleArea:function(a){let e=t.Cartesian3.subtract(a[0],a[1],new t.Cartesian3),r=t.Cartesian3.subtract(a[0],a[2],new t.Cartesian3),i=t.Cartesian3.cross(e,r,new t.Cartesian3);return.5*t.Cartesian3.magnitude(i)},numberOfPoints:function(a,e,r){var i=t.Cartesian3.distance(a,e);return Math.ceil(i/r)},numberOfPointsRhumbLine:function(a,e,r){var t=Math.pow(a.longitude-e.longitude,2)+Math.pow(a.latitude-e.latitude,2);return Math.ceil(Math.sqrt(t/(r*r)))}},f=new i.Cartographic;u.extractHeights=function(a,e){for(var r=a.length,t=new Array(r),i=0;i<r;i++){var n=a[i];t[i]=e.cartesianToCartographic(n,f).height}return t};var h=new n.Matrix4,C=new t.Cartesian3,d=new t.Cartesian3,g=new s.Plane(t.Cartesian3.UNIT_X,0),p=new t.Cartesian3,v=new s.Plane(t.Cartesian3.UNIT_X,0),m=new t.Cartesian3,w=new t.Cartesian3,T=[];function P(a,e,r){var t,i=T;if(i.length=a,e===r){for(t=0;t<a;t++)i[t]=e;return i}var n=r-e,o=n/a;for(t=0;t<a;t++){var s=e+t*o;i[t]=s}return i}var y=new i.Cartographic,b=new i.Cartographic,A=new t.Cartesian3,E=new t.Cartesian3,R=new t.Cartesian3,S=new l.EllipsoidGeodesic,D=new c.EllipsoidRhumbLine;function M(a,e,r,i,n,o,s,c){var l=i.scaleToGeodeticSurface(a,E),f=i.scaleToGeodeticSurface(e,R),h=u.numberOfPoints(a,e,r),C=i.cartesianToCartographic(l,y),d=i.cartesianToCartographic(f,b),g=P(h,n,o);S.setEndPoints(C,d);var p=S.surfaceDistance/h,v=c;C.height=n;var m=i.cartographicToCartesian(C,A);t.Cartesian3.pack(m,s,v),v+=3;for(var w=1;w<h;w++){var T=S.interpolateUsingSurfaceDistance(w*p,b);T.height=g[w],m=i.cartographicToCartesian(T,A),t.Cartesian3.pack(m,s,v),v+=3}return v}function x(a,e,r,i,n,o,s,l){var f=i.scaleToGeodeticSurface(a,E),h=i.scaleToGeodeticSurface(e,R),C=i.cartesianToCartographic(f,y),d=i.cartesianToCartographic(h,b),g=u.numberOfPointsRhumbLine(C,d,r),p=P(g,n,o);D.ellipsoid.equals(i)||(D=new c.EllipsoidRhumbLine(void 0,void 0,i)),D.setEndPoints(C,d);var v=D.surfaceDistance/g,m=l;C.height=n;var w=i.cartographicToCartesian(C,A);t.Cartesian3.pack(w,s,m),m+=3;for(var T=1;T<g;T++){var S=D.interpolateUsingSurfaceDistance(T*v,b);S.height=p[T],w=i.cartographicToCartesian(S,A),t.Cartesian3.pack(w,s,m),m+=3}return m}u.wrapLongitude=function(a,r){var i=[],c=[];if(e.defined(a)&&a.length>0){r=e.defaultValue(r,n.Matrix4.IDENTITY);var l=n.Matrix4.inverseTransformation(r,h),u=n.Matrix4.multiplyByPoint(l,t.Cartesian3.ZERO,C),f=t.Cartesian3.normalize(n.Matrix4.multiplyByPointAsVector(l,t.Cartesian3.UNIT_Y,d),d),T=s.Plane.fromPointNormal(u,f,g),P=t.Cartesian3.normalize(n.Matrix4.multiplyByPointAsVector(l,t.Cartesian3.UNIT_X,p),p),y=s.Plane.fromPointNormal(u,P,v),b=1;i.push(t.Cartesian3.clone(a[0]));for(var A=i[0],E=a.length,R=1;R<E;++R){var S=a[R];if(s.Plane.getPointDistance(y,A)<0||s.Plane.getPointDistance(y,S)<0){var D=o.IntersectionTests.lineSegmentPlane(A,S,T,m);if(e.defined(D)){var M=t.Cartesian3.multiplyByScalar(f,5e-9,w);s.Plane.getPointDistance(T,A)<0&&t.Cartesian3.negate(M,M),i.push(t.Cartesian3.add(D,M,new t.Cartesian3)),c.push(b+1),t.Cartesian3.negate(M,M),i.push(t.Cartesian3.add(D,M,new t.Cartesian3)),b=1}}i.push(t.Cartesian3.clone(a[R])),b++,A=S}c.push(b)}return{positions:i,lengths:c}},u.generateArc=function(a){e.defined(a)||(a={});var n=a.positions;if(!e.defined(n))throw new r.DeveloperError("options.positions is required.");var o=n.length,s=e.defaultValue(a.ellipsoid,i.Ellipsoid.WGS84),c=e.defaultValue(a.height,0),l=Array.isArray(c);if(o<1)return[];if(1===o){var f=s.scaleToGeodeticSurface(n[0],E);if(c=l?c[0]:c,0!==c){var h=s.geodeticSurfaceNormal(f,A);t.Cartesian3.multiplyByScalar(h,c,h),t.Cartesian3.add(f,h,f)}return[f.x,f.y,f.z]}var C=a.minDistance;if(!e.defined(C)){var d=e.defaultValue(a.granularity,t.CesiumMath.RADIANS_PER_DEGREE);C=t.CesiumMath.chordLength(d,s.maximumRadius)}var g,p=0;for(g=0;g<o-1;g++)p+=u.numberOfPoints(n[g],n[g+1],C);var v=3*(p+1),m=new Array(v),w=0;for(g=0;g<o-1;g++){var P=n[g],b=n[g+1],R=l?c[g]:c,S=l?c[g+1]:c;w=M(P,b,C,s,R,S,m,w)}T.length=0;var D=n[o-1],x=s.cartesianToCartographic(D,y);x.height=l?c[o-1]:c;var G=s.cartographicToCartesian(x,A);return t.Cartesian3.pack(G,m,v-3),m};var G=new i.Cartographic,N=new i.Cartographic;u.generateRhumbArc=function(a){e.defined(a)||(a={});var n=a.positions;if(!e.defined(n))throw new r.DeveloperError("options.positions is required.");var o=n.length,s=e.defaultValue(a.ellipsoid,i.Ellipsoid.WGS84),c=e.defaultValue(a.height,0),l=Array.isArray(c);if(o<1)return[];if(1===o){var f=s.scaleToGeodeticSurface(n[0],E);if(c=l?c[0]:c,0!==c){var h=s.geodeticSurfaceNormal(f,A);t.Cartesian3.multiplyByScalar(h,c,h),t.Cartesian3.add(f,h,f)}return[f.x,f.y,f.z]}var C,d,g=e.defaultValue(a.granularity,t.CesiumMath.RADIANS_PER_DEGREE),p=0,v=s.cartesianToCartographic(n[0],G);for(C=0;C<o-1;C++)d=s.cartesianToCartographic(n[C+1],N),p+=u.numberOfPointsRhumbLine(v,d,g),v=i.Cartographic.clone(d,G);var m=3*(p+1),w=new Array(m),P=0;for(C=0;C<o-1;C++){var b=n[C],R=n[C+1],S=l?c[C]:c,D=l?c[C+1]:c;P=x(b,R,g,s,S,D,w,P)}T.length=0;var M=n[o-1],I=s.cartesianToCartographic(M,y);I.height=l?c[o-1]:c;var k=s.cartographicToCartesian(I,A);return t.Cartesian3.pack(k,w,m-3),w},u.generateCartesianArc=function(a){for(var e=u.generateArc(a),r=e.length/3,i=new Array(r),n=0;n<r;n++)i[n]=t.Cartesian3.unpack(e,3*n);return i},u.generateCartesianRhumbArc=function(a){for(var e=u.generateRhumbArc(a),r=e.length/3,i=new Array(r),n=0;n<r;n++)i[n]=t.Cartesian3.unpack(e,3*n);return i},a.PolylinePipeline=u}));