import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import FrameRateMonitor from"../../Scene/FrameRateMonitor.js";import knockout from"../../ThirdParty/knockout.js";import createCommand from"../createCommand.js";function PerformanceWatchdogViewModel(e){if(!defined(e)||!defined(e.scene))throw new DeveloperError("options.scene is required.");this._scene=e.scene,this.lowFrameRateMessage=defaultValue(e.lowFrameRateMessage,"This application appears to be performing poorly on your system.  Please try using a different web browser or updating your video drivers."),this.lowFrameRateMessageDismissed=!1,this.showingLowFrameRateMessage=!1,knockout.track(this,["lowFrameRateMessage","lowFrameRateMessageDismissed","showingLowFrameRateMessage"]);var s=this;this._dismissMessage=createCommand((function(){s.showingLowFrameRateMessage=!1,s.lowFrameRateMessageDismissed=!0}));var r=FrameRateMonitor.fromScene(e.scene);this._unsubscribeLowFrameRate=r.lowFrameRate.addEventListener((function(){s.lowFrameRateMessageDismissed||(s.showingLowFrameRateMessage=!0)})),this._unsubscribeNominalFrameRate=r.nominalFrameRate.addEventListener((function(){s.showingLowFrameRateMessage=!1}))}Object.defineProperties(PerformanceWatchdogViewModel.prototype,{scene:{get:function(){return this._scene}},dismissMessage:{get:function(){return this._dismissMessage}}}),PerformanceWatchdogViewModel.prototype.destroy=function(){return this._unsubscribeLowFrameRate(),this._unsubscribeNominalFrameRate(),destroyObject(this)};export default PerformanceWatchdogViewModel;