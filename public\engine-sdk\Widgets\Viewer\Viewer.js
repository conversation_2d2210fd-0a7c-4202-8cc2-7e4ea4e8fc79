import BoundingSphere from"../../Core/BoundingSphere.js";import Cartesian3 from"../../Core/Cartesian3.js";import Cartographic from"../../Core/Cartographic.js";import Clock from"../../Core/Clock.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import Event from"../../Core/Event.js";import EventHelper from"../../Core/EventHelper.js";import HeadingPitchRange from"../../Core/HeadingPitchRange.js";import Matrix4 from"../../Core/Matrix4.js";import ScreenSpaceEventType from"../../Core/ScreenSpaceEventType.js";import BoundingSphereState from"../../DataSources/BoundingSphereState.js";import ConstantPositionProperty from"../../DataSources/ConstantPositionProperty.js";import DataSourceCollection from"../../DataSources/DataSourceCollection.js";import DataSourceDisplay from"../../DataSources/DataSourceDisplay.js";import Entity from"../../DataSources/Entity.js";import EntityView from"../../DataSources/EntityView.js";import Property from"../../DataSources/Property.js";import Cesium3DTileset from"../../Scene/Cesium3DTileset.js";import computeFlyToLocationForRectangle from"../../Scene/computeFlyToLocationForRectangle.js";import ImageryLayer from"../../Scene/ImageryLayer.js";import SceneMode from"../../Scene/SceneMode.js";import TimeDynamicPointCloud from"../../Scene/TimeDynamicPointCloud.js";import knockout from"../../ThirdParty/knockout.js";import when from"../../ThirdParty/when.js";import Animation from"../Animation/Animation.js";import AnimationViewModel from"../Animation/AnimationViewModel.js";import BaseLayerPicker from"../BaseLayerPicker/BaseLayerPicker.js";import createDefaultImageryProviderViewModels from"../BaseLayerPicker/createDefaultImageryProviderViewModels.js";import createDefaultTerrainProviderViewModels from"../BaseLayerPicker/createDefaultTerrainProviderViewModels.js";import CesiumWidget from"../CesiumWidget/CesiumWidget.js";import ClockViewModel from"../ClockViewModel.js";import FullscreenButton from"../FullscreenButton/FullscreenButton.js";import Geocoder from"../Geocoder/Geocoder.js";import getElement from"../getElement.js";import HomeButton from"../HomeButton/HomeButton.js";import InfoBox from"../InfoBox/InfoBox.js";import NavigationHelpButton from"../NavigationHelpButton/NavigationHelpButton.js";import ProjectionPicker from"../ProjectionPicker/ProjectionPicker.js";import SceneModePicker from"../SceneModePicker/SceneModePicker.js";import SelectionIndicator from"../SelectionIndicator/SelectionIndicator.js";import subscribeAndEvaluate from"../subscribeAndEvaluate.js";import Timeline from"../Timeline/Timeline.js";import VRButton from"../VRButton/VRButton.js";import PolygonGeometry from"../../Core/PolygonGeometry.js";import PolygonOutlineGeometry from"../../Core/PolygonOutlineGeometry.js";var boundingSphereScratch=new BoundingSphere;function onTimelineScrubfunction(e){var t=e.clock;t.currentTime=e.timeJulian,t.shouldAnimate=!1}function pickEntity(e,t){var i=e.scene.pick(t.position);if(defined(i)){var o=defaultValue(i.id,i.primitive.id);if(o instanceof Entity)return o}if(defined(e.scene.globe))return pickImageryLayerFeature(e,t.position)}function trackDataSourceClock(e,t,i){if(defined(i)){var o=i.clock;defined(o)&&(o.getValue(t),defined(e)&&(e.updateFromClock(),e.zoomTo(o.startTime,o.stopTime)))}}var cartesian3Scratch=new Cartesian3;function pickImageryLayerFeature(e,t){var i=e.scene,o=i.camera.getPickRay(t),n=i.imageryLayers.pickImageryLayerFeatures(o,i);if(defined(n)){var r=new Entity({id:"Loading...",description:"Loading feature information..."});return when(n,(function(t){if(e.selectedEntity===r)if(defined(t)&&0!==t.length){var i=t[0],o=new Entity({id:i.name,description:i.description});if(defined(i.position)){var n=e.scene.globe.ellipsoid.cartographicToCartesian(i.position,cartesian3Scratch);o.position=new ConstantPositionProperty(n)}e.selectedEntity=o}else e.selectedEntity=createNoFeaturesEntity()}),(function(){e.selectedEntity===r&&(e.selectedEntity=createNoFeaturesEntity())})),r}}function createNoFeaturesEntity(){return new Entity({id:"None",description:"No features found."})}function enableVRUI(e,t){var i=e._geocoder,o=e._homeButton,n=e._sceneModePicker,r=e._projectionPicker,a=e._baseLayerPicker,s=e._animation,c=e._timeline,d=e._fullscreenButton,l=e._infoBox,u=e._selectionIndicator,h=t?"hidden":"visible";if(defined(i)&&(i.container.style.visibility=h),defined(o)&&(o.container.style.visibility=h),defined(n)&&(n.container.style.visibility=h),defined(r)&&(r.container.style.visibility=h),defined(a)&&(a.container.style.visibility=h),defined(s)&&(s.container.style.visibility=h),defined(c)&&(c.container.style.visibility=h),defined(d)&&d.viewModel.isFullscreenEnabled&&(d.container.style.visibility=h),defined(l)&&(l.container.style.visibility=h),defined(u)&&(u.container.style.visibility=h),e._container){var m=t||!defined(d)?0:d.container.clientWidth;e._vrButton.container.style.right=m+"px",e.forceResize()}}function Viewer(e,t){if(!defined(e))throw new DeveloperError("container is required.");e=getElement(e),t=defaultValue(t,defaultValue.EMPTY_OBJECT);var i=(!defined(t.globe)||!1!==t.globe)&&(!defined(t.baseLayerPicker)||!1!==t.baseLayerPicker);if(!i&&defined(t.selectedImageryProviderViewModel))throw new DeveloperError("options.selectedImageryProviderViewModel is not available when not using the BaseLayerPicker widget. Either specify options.imageryProvider instead or set options.baseLayerPicker to true.");if(!i&&defined(t.selectedTerrainProviderViewModel))throw new DeveloperError("options.selectedTerrainProviderViewModel is not available when not using the BaseLayerPicker widget. Either specify options.terrainProvider instead or set options.baseLayerPicker to true.");var o=this,n=document.createElement("div");n.className="cesium-viewer",e.appendChild(n);var r=document.createElement("div");r.className="cesium-viewer-cesiumWidgetContainer",n.appendChild(r);var a=document.createElement("div");a.className="cesium-viewer-bottom",n.appendChild(a);var s,c,d=defaultValue(t.scene3DOnly,!1),l=!1;defined(t.clockViewModel)?(c=t.clockViewModel,s=c.clock):(s=new Clock,c=new ClockViewModel(s),l=!0),defined(t.shouldAnimate)&&(s.shouldAnimate=t.shouldAnimate);var u=new CesiumWidget(r,{imageryProvider:!i&&!defined(t.imageryProvider)&&void 0,clock:s,skyBox:t.skyBox,skyAtmosphere:t.skyAtmosphere,sceneMode:t.sceneMode,mapProjection:t.mapProjection,globe:t.globe,orderIndependentTranslucency:t.orderIndependentTranslucency,contextOptions:t.contextOptions,useDefaultRenderLoop:t.useDefaultRenderLoop,targetFrameRate:t.targetFrameRate,showRenderLoopErrors:t.showRenderLoopErrors,useBrowserRecommendedResolution:t.useBrowserRecommendedResolution,creditContainer:defined(t.creditContainer)?t.creditContainer:a,creditViewport:t.creditViewport,scene3DOnly:d,terrainExaggeration:t.terrainExaggeration,shadows:t.shadows,terrainShadows:t.terrainShadows,mapMode2D:t.mapMode2D,requestRenderMode:t.requestRenderMode,maximumRenderTimeChange:t.maximumRenderTimeChange,isPlaneMode:t.isPlaneMode});PolygonGeometry.isPlaneMode=t.isPlaneMode,PolygonOutlineGeometry.isPlaneMode=t.isPlaneMode;var h=t.dataSources,m=!1;defined(h)||(h=new DataSourceCollection,m=!0);var f,p,y=u.scene,g=new DataSourceDisplay({scene:y,dataSourceCollection:h}),v=new EventHelper;if(v.add(s.onTick,Viewer.prototype._onTick,this),v.add(y.morphStart,Viewer.prototype._clearTrackedObject,this),!defined(t.selectionIndicator)||!1!==t.selectionIndicator){var _=document.createElement("div");_.className="cesium-viewer-selectionIndicatorContainer",n.appendChild(_),f=new SelectionIndicator(_,y)}if(!defined(t.infoBox)||!1!==t.infoBox){var S=document.createElement("div");S.className="cesium-viewer-infoBoxContainer",n.appendChild(S),p=new InfoBox(S);var w=p.viewModel;v.add(w.cameraClicked,Viewer.prototype._onInfoBoxCameraClicked,this),v.add(w.closeClicked,Viewer.prototype._onInfoBoxClockClicked,this)}var k,E,C,P,T,B,b,D,V,M,I,j,x,L,R,A=document.createElement("div");if(A.className="cesium-viewer-toolbar",n.appendChild(A),!defined(t.geocoder)||!1!==t.geocoder){var z,H=document.createElement("div");H.className="cesium-viewer-geocoderContainer",A.appendChild(H),defined(t.geocoder)&&"boolean"!==typeof t.geocoder&&(z=Array.isArray(t.geocoder)?t.geocoder:[t.geocoder]),k=new Geocoder({container:H,geocoderServices:z,scene:y}),v.add(k.viewModel.search.beforeExecute,Viewer.prototype._clearObjects,this)}if(defined(t.homeButton)&&!1===t.homeButton||(E=new HomeButton(A,y),defined(k)&&v.add(E.viewModel.command.afterExecute,(function(){var e=k.viewModel;e.searchText="",e.isSearchInProgress&&e.search()})),v.add(E.viewModel.command.beforeExecute,Viewer.prototype._clearTrackedObject,this)),!0===t.sceneModePicker&&d)throw new DeveloperError("options.sceneModePicker is not available when options.scene3DOnly is set to true.");if(d||defined(t.sceneModePicker)&&!1===t.sceneModePicker||(C=new SceneModePicker(A,y)),t.projectionPicker&&(P=new ProjectionPicker(A,y)),i){var F=defaultValue(t.imageryProviderViewModels,createDefaultImageryProviderViewModels()),W=defaultValue(t.terrainProviderViewModels,createDefaultTerrainProviderViewModels());T=new BaseLayerPicker(A,{globe:y.globe,imageryProviderViewModels:F,selectedImageryProviderViewModel:t.selectedImageryProviderViewModel,terrainProviderViewModels:W,selectedTerrainProviderViewModel:t.selectedTerrainProviderViewModel});var N=A.getElementsByClassName("cesium-baseLayerPicker-dropDown");B=N[0]}if(defined(t.imageryProvider)&&!1!==t.imageryProvider&&(i&&(T.viewModel.selectedImagery=void 0),y.imageryLayers.removeAll(),y.imageryLayers.addImageryProvider(t.imageryProvider)),defined(t.terrainProvider)&&(i&&(T.viewModel.selectedTerrain=void 0),y.terrainProvider=t.terrainProvider),!defined(t.navigationHelpButton)||!1!==t.navigationHelpButton){var O=!0;try{if(defined(window.localStorage)){var U=window.localStorage.getItem("cesium-hasSeenNavHelp");defined(U)&&Boolean(U)?O=!1:window.localStorage.setItem("cesium-hasSeenNavHelp","true")}}catch(K){}b=new NavigationHelpButton({container:A,instructionsInitiallyVisible:defaultValue(t.navigationInstructionsInitiallyVisible,O)})}if(!defined(t.animation)||!1!==t.animation){var G=document.createElement("div");G.className="cesium-viewer-animationContainer",n.appendChild(G),D=new Animation(G,new AnimationViewModel(c))}if(!defined(t.timeline)||!1!==t.timeline){var Z=document.createElement("div");Z.className="cesium-viewer-timelineContainer",n.appendChild(Z),V=new Timeline(Z,s),V.addEventListener("settime",onTimelineScrubfunction,!1),V.zoomTo(s.startTime,s.stopTime)}if(defined(t.fullscreenButton)&&!1===t.fullscreenButton||(j=document.createElement("div"),j.className="cesium-viewer-fullscreenContainer",n.appendChild(j),M=new FullscreenButton(j,t.fullscreenElement),I=subscribeAndEvaluate(M.viewModel,"isFullscreenEnabled",(function(e){j.style.display=e?"block":"none",defined(V)&&(V.container.style.right=j.clientWidth+"px",V.resize())}))),t.vrButton){var q=document.createElement("div");q.className="cesium-viewer-vrContainer",n.appendChild(q),x=new VRButton(q,y,t.fullScreenElement),L=subscribeAndEvaluate(x.viewModel,"isVREnabled",(function(e){q.style.display=e?"block":"none",defined(M)&&(q.style.right=j.clientWidth+"px"),defined(V)&&(V.container.style.right=q.clientWidth+"px",V.resize())})),R=subscribeAndEvaluate(x.viewModel,"isVRMode",(function(e){enableVRUI(o,e)}))}this._baseLayerPickerDropDown=B,this._fullscreenSubscription=I,this._vrSubscription=L,this._vrModeSubscription=R,this._dataSourceChangedListeners={},this._automaticallyTrackDataSourceClocks=defaultValue(t.automaticallyTrackDataSourceClocks,!0),this._container=e,this._bottomContainer=a,this._element=n,this._cesiumWidget=u,this._selectionIndicator=f,this._infoBox=p,this._dataSourceCollection=h,this._destroyDataSourceCollection=m,this._dataSourceDisplay=g,this._clockViewModel=c,this._destroyClockViewModel=l,this._toolbar=A,this._homeButton=E,this._sceneModePicker=C,this._projectionPicker=P,this._baseLayerPicker=T,this._navigationHelpButton=b,this._animation=D,this._timeline=V,this._fullscreenButton=M,this._vrButton=x,this._geocoder=k,this._eventHelper=v,this._lastWidth=0,this._lastHeight=0,this._allowDataSourcesToSuspendAnimation=!0,this._entityView=void 0,this._enableInfoOrSelection=defined(p)||defined(f),this._clockTrackedDataSource=void 0,this._trackedEntity=void 0,this._needTrackedEntityUpdate=!1,this._selectedEntity=void 0,this._clockTrackedDataSource=void 0,this._zoomIsFlight=!1,this._zoomTarget=void 0,this._zoomPromise=void 0,this._zoomOptions=void 0,this._selectedEntityChanged=new Event,this._trackedEntityChanged=new Event,knockout.track(this,["_trackedEntity","_selectedEntity","_clockTrackedDataSource"]),v.add(h.dataSourceAdded,Viewer.prototype._onDataSourceAdded,this),v.add(h.dataSourceRemoved,Viewer.prototype._onDataSourceRemoved,this),v.add(y.postUpdate,Viewer.prototype.resize,this),v.add(y.postRender,Viewer.prototype._postRender,this);for(var Y=h.length,J=0;J<Y;J++)this._dataSourceAdded(h,h.get(J));this._dataSourceAdded(void 0,g.defaultDataSource),v.add(h.dataSourceAdded,Viewer.prototype._dataSourceAdded,this),v.add(h.dataSourceRemoved,Viewer.prototype._dataSourceRemoved,this)}function zoomToOrFly(e,t,i,o){if(!defined(t))throw new DeveloperError("zoomTarget is required.");cancelZoom(e);var n=when.defer();return e._zoomPromise=n,e._zoomIsFlight=o,e._zoomOptions=i,when(t,(function(t){if(e._zoomPromise===n)if(t instanceof ImageryLayer)t.getViewableRectangle().then((function(t){return computeFlyToLocationForRectangle(t,e.scene)})).then((function(t){e._zoomPromise===n&&(e._zoomTarget=t)}));else if(t instanceof Cesium3DTileset)e._zoomTarget=t;else if(t instanceof TimeDynamicPointCloud)e._zoomTarget=t;else if(t.isLoading&&defined(t.loadingEvent))var i=t.loadingEvent.addEventListener((function(){i(),e._zoomPromise===n&&(e._zoomTarget=t.entities.values.slice(0))}));else Array.isArray(t)?e._zoomTarget=t.slice(0):(t=defaultValue(t.values,t),defined(t.entities)&&(t=t.entities.values),Array.isArray(t)?e._zoomTarget=t.slice(0):e._zoomTarget=[t])})),e.scene.requestRender(),n.promise}function clearZoom(e){e._zoomPromise=void 0,e._zoomTarget=void 0,e._zoomOptions=void 0}function cancelZoom(e){var t=e._zoomPromise;defined(t)&&(clearZoom(e),t.resolve(!1))}function updateZoomTarget(e){var t=e._zoomTarget;if(defined(t)&&e.scene.mode!==SceneMode.MORPHING){var i,o,n=e.scene,r=n.camera,a=e._zoomPromise,s=defaultValue(e._zoomOptions,{});if(t instanceof Cesium3DTileset)return t.readyPromise.then((function(){var o=t.boundingSphere;defined(s.offset)||(s.offset=new HeadingPitchRange(0,-.5,o.radius)),i={offset:s.offset,duration:s.duration,maximumHeight:s.maximumHeight,complete:function(){a.resolve(!0)},cancel:function(){a.resolve(!1)}},e._zoomIsFlight?r.flyToBoundingSphere(t.boundingSphere,i):(r.viewBoundingSphere(o,s.offset),r.lookAtTransform(Matrix4.IDENTITY),a.resolve(!0)),clearZoom(e)}));if(t instanceof TimeDynamicPointCloud)return t.readyPromise.then((function(){var o=t.boundingSphere;defined(s.offset)||(s.offset=new HeadingPitchRange(0,-.5,o.radius)),i={offset:s.offset,duration:s.duration,maximumHeight:s.maximumHeight,complete:function(){a.resolve(!0)},cancel:function(){a.resolve(!1)}},e._zoomIsFlight?r.flyToBoundingSphere(o,i):(r.viewBoundingSphere(o,s.offset),r.lookAtTransform(Matrix4.IDENTITY),a.resolve(!0)),clearZoom(e)}));if(t instanceof Cartographic)return i={destination:n.mapProjection.ellipsoid.cartographicToCartesian(t),duration:s.duration,maximumHeight:s.maximumHeight,complete:function(){a.resolve(!0)},cancel:function(){a.resolve(!1)}},e._zoomIsFlight?r.flyTo(i):(r.setView(i),a.resolve(!0)),void clearZoom(e);for(var c=t,d=[],l=0,u=c.length;l<u;l++){var h=e._dataSourceDisplay.getBoundingSphere(c[l],!1,boundingSphereScratch);if(h===BoundingSphereState.PENDING)return;h!==BoundingSphereState.FAILED&&d.push(BoundingSphere.clone(boundingSphereScratch))}0!==d.length?(e.trackedEntity=void 0,o=BoundingSphere.fromBoundingSpheres(d),e._zoomIsFlight?(clearZoom(e),r.flyToBoundingSphere(o,{duration:s.duration,maximumHeight:s.maximumHeight,complete:function(){a.resolve(!0)},cancel:function(){a.resolve(!1)},offset:s.offset})):(r.viewBoundingSphere(o,s.offset),r.lookAtTransform(Matrix4.IDENTITY),clearZoom(e),a.resolve(!0))):cancelZoom(e)}}function updateTrackedEntity(e){if(e._needTrackedEntityUpdate){var t=e._trackedEntity,i=e.clock.currentTime,o=Property.getValueOrUndefined(t.position,i);if(defined(o)){var n=e.scene,r=e._dataSourceDisplay.getBoundingSphere(t,!1,boundingSphereScratch);if(r!==BoundingSphereState.PENDING){var a=n.mode;a!==SceneMode.COLUMBUS_VIEW&&a!==SceneMode.SCENE2D||(n.screenSpaceCameraController.enableTranslate=!1),a!==SceneMode.COLUMBUS_VIEW&&a!==SceneMode.SCENE3D||(n.screenSpaceCameraController.enableTilt=!1);var s=r!==BoundingSphereState.FAILED?boundingSphereScratch:void 0;e._entityView=new EntityView(t,n,n.mapProjection.ellipsoid),e._entityView.update(i,s),e._needTrackedEntityUpdate=!1}}}}Object.defineProperties(Viewer.prototype,{container:{get:function(){return this._container}},bottomContainer:{get:function(){return this._bottomContainer}},cesiumWidget:{get:function(){return this._cesiumWidget}},selectionIndicator:{get:function(){return this._selectionIndicator}},infoBox:{get:function(){return this._infoBox}},geocoder:{get:function(){return this._geocoder}},homeButton:{get:function(){return this._homeButton}},sceneModePicker:{get:function(){return this._sceneModePicker}},projectionPicker:{get:function(){return this._projectionPicker}},baseLayerPicker:{get:function(){return this._baseLayerPicker}},navigationHelpButton:{get:function(){return this._navigationHelpButton}},animation:{get:function(){return this._animation}},timeline:{get:function(){return this._timeline}},fullscreenButton:{get:function(){return this._fullscreenButton}},vrButton:{get:function(){return this._vrButton}},dataSourceDisplay:{get:function(){return this._dataSourceDisplay}},entities:{get:function(){return this._dataSourceDisplay.defaultDataSource.entities}},dataSources:{get:function(){return this._dataSourceCollection}},canvas:{get:function(){return this._cesiumWidget.canvas}},scene:{get:function(){return this._cesiumWidget.scene}},shadows:{get:function(){return this.scene.shadowMap.enabled},set:function(e){this.scene.shadowMap.enabled=e}},terrainShadows:{get:function(){return this.scene.globe.shadows},set:function(e){this.scene.globe.shadows=e}},shadowMap:{get:function(){return this.scene.shadowMap}},imageryLayers:{get:function(){return this.scene.imageryLayers}},terrainProvider:{get:function(){return this.scene.terrainProvider},set:function(e){this.scene.terrainProvider=e}},camera:{get:function(){return this.scene.camera}},postProcessStages:{get:function(){return this.scene.postProcessStages}},clock:{get:function(){return this._clockViewModel.clock}},clockViewModel:{get:function(){return this._clockViewModel}},screenSpaceEventHandler:{get:function(){return this._cesiumWidget.screenSpaceEventHandler}},targetFrameRate:{get:function(){return this._cesiumWidget.targetFrameRate},set:function(e){this._cesiumWidget.targetFrameRate=e}},useDefaultRenderLoop:{get:function(){return this._cesiumWidget.useDefaultRenderLoop},set:function(e){this._cesiumWidget.useDefaultRenderLoop=e}},resolutionScale:{get:function(){return this._cesiumWidget.resolutionScale},set:function(e){this._cesiumWidget.resolutionScale=e}},useBrowserRecommendedResolution:{get:function(){return this._cesiumWidget.useBrowserRecommendedResolution},set:function(e){this._cesiumWidget.useBrowserRecommendedResolution=e}},allowDataSourcesToSuspendAnimation:{get:function(){return this._allowDataSourcesToSuspendAnimation},set:function(e){this._allowDataSourcesToSuspendAnimation=e}},trackedEntity:{get:function(){return this._trackedEntity},set:function(e){if(this._trackedEntity!==e){this._trackedEntity=e,cancelZoom(this);var t=this.scene,i=t.mode;defined(e)&&defined(e.position)?this._needTrackedEntityUpdate=!0:(this._needTrackedEntityUpdate=!1,i!==SceneMode.COLUMBUS_VIEW&&i!==SceneMode.SCENE2D||(t.screenSpaceCameraController.enableTranslate=!0),i!==SceneMode.COLUMBUS_VIEW&&i!==SceneMode.SCENE3D||(t.screenSpaceCameraController.enableTilt=!0),this._entityView=void 0,this.camera.lookAtTransform(Matrix4.IDENTITY)),this._trackedEntityChanged.raiseEvent(e),this.scene.requestRender()}}},selectedEntity:{get:function(){return this._selectedEntity},set:function(e){if(this._selectedEntity!==e){this._selectedEntity=e;var t=defined(this._selectionIndicator)?this._selectionIndicator.viewModel:void 0;defined(e)?defined(t)&&t.animateAppear():defined(t)&&t.animateDepart(),this._selectedEntityChanged.raiseEvent(e)}}},selectedEntityChanged:{get:function(){return this._selectedEntityChanged}},trackedEntityChanged:{get:function(){return this._trackedEntityChanged}},clockTrackedDataSource:{get:function(){return this._clockTrackedDataSource},set:function(e){this._clockTrackedDataSource!==e&&(this._clockTrackedDataSource=e,trackDataSourceClock(this._timeline,this.clock,e))}},ambientLight:{get:function(){return this.scene.ambientLight},set:function(e){this.scene.ambientLight=e}},lightScaleFactor:{get:function(){return this.scene.lightScaleFactor},set:function(e){this.scene.lightScaleFactor=e}}}),Viewer.prototype.extend=function(e,t){if(!defined(e))throw new DeveloperError("mixin is required.");e(this,t)},Viewer.prototype.resize=function(){var e=this._cesiumWidget,t=this._container,i=t.clientWidth,o=t.clientHeight,n=defined(this._animation),r=defined(this._timeline);if(e.resize(),i!==this._lastWidth||o!==this._lastHeight){var a=o-125,s=this._baseLayerPickerDropDown;if(defined(s)&&(s.style.maxHeight=a+"px"),defined(this._geocoder)){var c=this._geocoder.searchSuggestionsContainer;c.style.maxHeight=a+"px"}defined(this._infoBox)&&(this._infoBox.viewModel.maxHeight=a);var d,l=this._timeline,u=0,h=0,m=0;if(n&&"hidden"!==window.getComputedStyle(this._animation.container).visibility){var f=this._lastWidth;d=this._animation.container,i>900?(u=169,f<=900&&(d.style.width="169px",d.style.height="112px",this._animation.resize())):i>=600?(u=136,(f<600||f>900)&&(d.style.width="136px",d.style.height="90px",this._animation.resize())):(u=106,(f>600||0===f)&&(d.style.width="106px",d.style.height="70px",this._animation.resize())),h=u+5}if(r&&"hidden"!==window.getComputedStyle(this._timeline.container).visibility){var p=this._fullscreenButton,y=this._vrButton,g=l.container,v=g.style;m=g.clientHeight+3,v.left=u+"px";var _=0;defined(p)&&(_+=p.container.clientWidth),defined(y)&&(_+=y.container.clientWidth),v.right=_+"px",l.resize()}this._bottomContainer.style.left=h+"px",this._bottomContainer.style.bottom=m+"px",this._lastWidth=i,this._lastHeight=o}},Viewer.prototype.forceResize=function(){this._lastWidth=0,this.resize()},Viewer.prototype.render=function(){this._cesiumWidget.render()},Viewer.prototype.isDestroyed=function(){return!1},Viewer.prototype.destroy=function(){var e;this.screenSpaceEventHandler.removeInputAction(ScreenSpaceEventType.LEFT_CLICK),this.screenSpaceEventHandler.removeInputAction(ScreenSpaceEventType.LEFT_DOUBLE_CLICK);var t=this.dataSources,i=t.length;for(e=0;e<i;e++)this._dataSourceRemoved(t,t.get(e));return this._dataSourceRemoved(void 0,this._dataSourceDisplay.defaultDataSource),this._container.removeChild(this._element),void 0!==this._toolbar&&this._element.removeChild(this._toolbar),this._eventHelper.removeAll(),defined(this._geocoder)&&(this._geocoder=this._geocoder.destroy()),defined(this._homeButton)&&(this._homeButton=this._homeButton.destroy()),defined(this._sceneModePicker)&&(this._sceneModePicker=this._sceneModePicker.destroy()),defined(this._projectionPicker)&&(this._projectionPicker=this._projectionPicker.destroy()),defined(this._baseLayerPicker)&&(this._baseLayerPicker=this._baseLayerPicker.destroy()),defined(this._animation)&&(this._element.removeChild(this._animation.container),this._animation=this._animation.destroy()),defined(this._timeline)&&(this._timeline.removeEventListener("settime",onTimelineScrubfunction,!1),this._element.removeChild(this._timeline.container),this._timeline=this._timeline.destroy()),defined(this._fullscreenButton)&&(this._fullscreenSubscription.dispose(),this._element.removeChild(this._fullscreenButton.container),this._fullscreenButton=this._fullscreenButton.destroy()),defined(this._vrButton)&&(this._vrSubscription.dispose(),this._vrModeSubscription.dispose(),this._element.removeChild(this._vrButton.container),this._vrButton=this._vrButton.destroy()),defined(this._infoBox)&&(this._element.removeChild(this._infoBox.container),this._infoBox=this._infoBox.destroy()),defined(this._selectionIndicator)&&(this._element.removeChild(this._selectionIndicator.container),this._selectionIndicator=this._selectionIndicator.destroy()),this._destroyClockViewModel&&(this._clockViewModel=this._clockViewModel.destroy()),this._dataSourceDisplay=this._dataSourceDisplay.destroy(),this._cesiumWidget=this._cesiumWidget&&this._cesiumWidget.destroy(),this._destroyDataSourceCollection&&(this._dataSourceCollection=this._dataSourceCollection.destroy()),this._element=void 0,this._toolbar=void 0,this._bottomContainer=void 0,destroyObject(this)},Viewer.prototype._dataSourceAdded=function(e,t){var i=t.entities;i.collectionChanged.addEventListener(Viewer.prototype._onEntityCollectionChanged,this)},Viewer.prototype._dataSourceRemoved=function(e,t){var i=t.entities;i.collectionChanged.removeEventListener(Viewer.prototype._onEntityCollectionChanged,this),defined(this.trackedEntity)&&i.getById(this.trackedEntity.id)===this.trackedEntity&&(this.trackedEntity=void 0),defined(this.selectedEntity)&&i.getById(this.selectedEntity.id)===this.selectedEntity&&(this.selectedEntity=void 0)},Viewer.prototype._onTick=function(e){var t=e.currentTime,i=this._dataSourceDisplay.update(t);this._allowDataSourcesToSuspendAnimation&&(this._clockViewModel.canAnimate=i);var o,n=this._entityView;if(defined(n)){var r=this._trackedEntity,a=this._dataSourceDisplay.getBoundingSphere(r,!1,boundingSphereScratch);a===BoundingSphereState.DONE&&n.update(t,boundingSphereScratch)}var s=!1,c=this.selectedEntity,d=defined(c)&&this._enableInfoOrSelection;if(d&&c.isShowing&&c.isAvailable(t)){var l=this._dataSourceDisplay.getBoundingSphere(c,!0,boundingSphereScratch);l!==BoundingSphereState.FAILED?o=boundingSphereScratch.center:defined(c.position)&&(o=c.position.getValue(t,o)),s=defined(o)}var u=defined(this._selectionIndicator)?this._selectionIndicator.viewModel:void 0;defined(u)&&(u.position=Cartesian3.clone(o,u.position),u.showSelection=d&&s,u.update());var h=defined(this._infoBox)?this._infoBox.viewModel:void 0;defined(h)&&(h.showInfo=d,h.enableCamera=s,h.isCameraTracking=this.trackedEntity===this.selectedEntity,d?(h.titleText=defaultValue(c.name,c.id),h.description=Property.getValueOrDefault(c.description,t,"")):(h.titleText="",h.description=""))},Viewer.prototype._onEntityCollectionChanged=function(e,t,i){for(var o=i.length,n=0;n<o;n++){var r=i[n];this.trackedEntity===r&&(this.trackedEntity=void 0),this.selectedEntity===r&&(this.selectedEntity=void 0)}},Viewer.prototype._onInfoBoxCameraClicked=function(e){if(e.isCameraTracking&&this.trackedEntity===this.selectedEntity)this.trackedEntity=void 0;else{var t=this.selectedEntity,i=t.position;defined(i)?this.trackedEntity=this.selectedEntity:this.zoomTo(this.selectedEntity)}},Viewer.prototype._clearTrackedObject=function(){this.trackedEntity=void 0},Viewer.prototype._onInfoBoxClockClicked=function(e){this.selectedEntity=void 0},Viewer.prototype._clearObjects=function(){this.trackedEntity=void 0,this.selectedEntity=void 0},Viewer.prototype._onDataSourceChanged=function(e){this.clockTrackedDataSource===e&&trackDataSourceClock(this.timeline,this.clock,e)},Viewer.prototype._onDataSourceAdded=function(e,t){this._automaticallyTrackDataSourceClocks&&(this.clockTrackedDataSource=t);var i=t.entities.id,o=this._eventHelper.add(t.changedEvent,Viewer.prototype._onDataSourceChanged,this);this._dataSourceChangedListeners[i]=o},Viewer.prototype._onDataSourceRemoved=function(e,t){var i=this.clockTrackedDataSource===t,o=t.entities.id;if(this._dataSourceChangedListeners[o](),this._dataSourceChangedListeners[o]=void 0,i){var n=e.length;this._automaticallyTrackDataSourceClocks&&n>0?this.clockTrackedDataSource=e.get(n-1):this.clockTrackedDataSource=void 0}},Viewer.prototype.zoomTo=function(e,t){var i={offset:t};return zoomToOrFly(this,e,i,!1)},Viewer.prototype.flyTo=function(e,t){return zoomToOrFly(this,e,t,!0)},Viewer.prototype._postRender=function(){updateZoomTarget(this),updateTrackedEntity(this)};export default Viewer;