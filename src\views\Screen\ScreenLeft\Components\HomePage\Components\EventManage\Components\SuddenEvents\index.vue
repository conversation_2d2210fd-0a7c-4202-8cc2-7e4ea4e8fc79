<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 10:26:24
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-23 14:58:52
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\SuddenEvents\index.vue
 * @Description:
 *
-->
<template>
  <div class="sudden-events-container">
    <div class="relative">
      <div class="head" v-show-ai-question @click="handleTitleClick">突发事件</div>
      <DatePicker v-model="date" type="month" @change="handleDateChange" :disabled-date="disabledDate" style="top: -16px; right: 0"></DatePicker>
    </div>
    <div class="tab">
      <div class="event-sum cursor-pointer" @click="setEventList('all')">
        <div>事件总数</div>
        <div>{{ total }}</div>
      </div>
      <div class="event-unfinish">
        <div>未终报</div>
        <div class="c-#FF6D21">{{ notFinalized }}</div>
      </div>
    </div>
    <div class="table-content">
      <!-- <tableList :head-list="eventHeadList" :list="eventList" list-data-height="108px" @click-item="tableClick($event)"></tableList> -->
      <div class="prev" @click="prev"></div>
      <div class="next" @click="next"></div>
      <el-carousel ref="refCarousel" class="imgs" height="156px" indicator-position="none" arrow="never" :autoplay="false">
        <!-- 循环生成每页的 carousel-item -->
        <el-carousel-item v-for="(group, pageIndex) in groupedImgList" :key="pageIndex">
          <div class="image-group">
            <!-- 直接遍历当前页的事件项 -->
            <div v-for="item in group" :key="item.id || item.imgUrl" class="image">
              <el-image :src="item.imgUrl" @click="handleImg(item)">
                <!-- 可以添加加载中的占位图 -->
                <template #placeholder>
                  <div class="c-#fff text-14 lh-130 text-center">加载中...</div>
                </template>
                <!-- 可以添加加载失败的占位图 -->
                <template #error>
                  <div class="flex items-center justify-center w-260 h-142 c-#ccc">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <!-- <img width="260" height="142" :src="item.imgUrl" alt="" @click="handleImg(item.imgUrl)" /> -->
              <ElTooltip effect="dark" :content="item.type" placement="top">
                <div class="title" @click="tableClick(item)">
                  {{ lib.utils.getMiddleEllipsis(item.type, 15) }}
                </div>
              </ElTooltip>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div class="content-bottom">
      <eventType :event-type-list="eventTypeList"></eventType>
      <disposalEfficiency :disposal-efficiency-list="disposalEfficiencyList" :event-type-name="eventTypeName"></disposalEfficiency>
    </div>
  </div>
</template>

<script setup lang="ts">
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import disposalEfficiency from './disposalEfficiency.vue'
  import eventType from './eventType.vue'
  import DatePicker from '@/components/DatePicker/index.vue'
  import tableList from '@/components/TableList/index.vue'
  import EventList from '@/views/Screen/MapPopupWindow/Components/EventList/index.vue'
  import { toUe5 } from '@/hooks/useUE/tools.js'
  import lib from '@/utils/lib'
  import { closePopWindowByTag, PopWindowProps, usePopWindow } from 'znyg-frontend-common'

  const { showPopWindow } = usePopWindow()
  const { appContext } = getCurrentInstance()

  const date = ref('')
  const total = ref()
  const notFinalized = ref(0)
  const eventTypeName = ref('')
  const eventTypeList = ref([])
  const dispositionRateMapList = ref([])
  const disposalEfficiencyList = ref([])

  onMounted(() => {
    date.value = moment().format('YYYY-MM-DD')
    // getData(date.value)
    useIntervalFn(
      () => {
        getData(date.value)
      },
      1000 * 60 * 2,
      { immediateCallback: true }
    )
  })
  lib.bus.busChangeEventType.on((name) => {
    eventTypeName.value = name
    disposalEfficiencyList.value = []
    const list = dispositionRateMapList.value.find((_) => _.name === name)?.typeDataMapList
    list.forEach((data) => {
      if (data.count) {
        disposalEfficiencyList.value.push({
          value: data.count,
          name: data.name,
          pro: data.pro
        })
      }
    })
  })
  const eventHeadList = ref([
    { label: '发现时间', prop: 'findTime', formatDate: 'YYYY-MM-DD' },
    { label: '发现位置', prop: 'sdetailPosition' },
    { label: '事件类型', prop: 'type' },
    { label: '事件等级', prop: 'level' },
    { label: '处置时间', prop: 'handlingTime', unit: '分钟' },
    { label: '事件状态', prop: 'status' }
  ])
  const eventList = ref([])
  const handleDateChange = (val) => {
    getData()
  }
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
  const refCarousel = ref(null)
  // 切换轮播图
  const prev = () => {
    refCarousel.value?.prev()
    triggerLazyLoad()
  }

  const next = () => {
    refCarousel.value?.next()
    triggerLazyLoad()
  }
  const triggerLazyLoad = () => {
    // 模拟滚动事件，触发懒加载
    setTimeout(() => {
      window.dispatchEvent(new Event('scroll'))
    }, 100)
  }
  const groupedImgList = computed(() => {
    const result = []
    const size = 2
    for (let i = 0; i < eventList.value.length; i += size) {
      result.push(eventList.value.slice(i, i + size))
    }
    return result
  })
  const getData = () => {
    // total.value = 0
    // notFinalized.value = 0
    // eventList.value = []
    // eventTypeList.value = []
    dispositionRateMapList.value = []
    // disposalEfficiencyList.value = []
    const eventTypeData = []
    const disposalEfficiencyData = []
    // const startDate = moment(date.value).format('YYYY-MM-DD 00:00:00')
    // const endDate = moment(date.value).format('YYYY-MM-DD 23:59:59')
    const startDate = moment(date.value).format('YYYY-MM-01 00:00:00')
    const endDate = moment(date.value).endOf('month').format('YYYY-MM-DD 23:59:59')
    lib.api.bigscreenApi.emergencyStatistics({ startDate, endDate, emergencySourceGroup: false, projectId: 28 }).then(async (response: any) => {
      if (response.success && response.result.allSource) {
        total.value = response.result.allSource.total
        notFinalized.value = response.result.allSource.notFinalized
        eventList.value = response.result.allSource.thisEmergencyList
        eventList.value.forEach((item) => {
          ;(item.type = item.subType == '无' ? item.type : item.subType), (item.imgUrl = '')
        })
        dispositionRateMapList.value = response.result.allSource.dispositionRateMapList

        // doc转存，获取图片列表
        const urlList = []
        const eventWithImageIndices = []
        eventList.value.forEach((item, index) => {
          if (item.imagePathList && item.imagePathList.length) {
            urlList.push(item.imagePathList[0])
            eventWithImageIndices.push(index)
          }
        })

        try {
          const { result } = await lib.api.docDiyDiyApi.getDocsFromUrl({ urlList })
          result.forEach((docItem) => {
            const originalUrl = docItem.originalUrl
            const targetIndex = eventList.value.findIndex((item) => item.imagePathList && item.imagePathList.length && item.imagePathList[0] === originalUrl)
            if (targetIndex !== -1) {
              eventList.value[targetIndex].imgUrl = import.meta.env.VITE_DOC_URL + 'img/' + docItem.fileName
            }
          })
          console.log('eventList', eventList.value)
        } catch (error) {
          console.error(error)
        }
        if (response.result.allSource.dispositionRateMapList.length) {
          response.result.allSource.dispositionRateMapList.forEach((data) => {
            eventTypeData.push({
              value: data.count,
              name: data.name,
              pro: data.pro
            })
          })
          eventTypeList.value = eventTypeData
          if (response.result.allSource.dispositionRateMapList[0].typeDataMapList.length) {
            eventTypeName.value = response.result.allSource.dispositionRateMapList[0].name
            response.result.allSource.dispositionRateMapList[0].typeDataMapList.forEach((val) => {
              if (val.count) {
                disposalEfficiencyData.push({ value: val.count, name: val.name, pro: val.pro })
              }
            })
            disposalEfficiencyList.value = disposalEfficiencyData
          }
        }
      }
    })
  }
  const typeId = lib.typeIdMap.突发事件
  // const clickModel = lib.provideTools.clickModel.inject()

  // watch(
  //   () => clickModel.data,
  //   (val) => {
  //     const obj = val.object
  //     if (obj && obj.typeId === typeId) {
  //       const id = obj.id
  //       toUe5('openCustomPOIWindow', {
  //         id: id,
  //         isOpen: true
  //       })
  //       const data = JSON.parse(obj.window?.jsonParameter)
  //       lib.popWindow.createPopWindow(
  //         './Components/EventsView/index.vue',
  //         {
  //           // left: 1420,
  //           // top: 280,
  //           tag: 'eventsWindow',
  //           appContext,
  //           appendParent: 'player',
  //           // followPoint: { typeId: obj.typeId, id },
  //           closeFunc: () => {
  //             // toUe5('openCustomPOIWindow', {
  //             //   id: id,
  //             //   isOpen: false
  //             // })
  //           }
  //         },
  //         data
  //       )
  //     }
  //   },
  //   {
  //     immediate: true,
  //     deep: true
  //   }
  // )
  lib.bus.busTreeTableStructure.on((type) => {
    if (type === 'clear') {
      eventList.value.forEach((_) => {
        _.isOn = false
      })
    }
  })
  const tableClick = async (item) => {
    lib._engineController.clearSelect()
    item.isOn = !item.isOn
    // toUe5('customPOIList', {
    //   isOn: false, // 是否开启标签
    //   typeId: typeId // 设施设备类型ID（为0时指全部类型）
    // })
    lib._engineController.clearCzml()
    lib.popWindow.removeDialog('eventsWindow')
    console.log('item----', item)
    // 没有里程号默认撒在路面上
    if (!item.edetailPosition) {
      item.edetailPosition = 'EK53+825'
      // item.structureType = 4
    }
    lib.api.getPointCode
      .getModeCodeFromMileage({
        mileage: item.edetailPosition,
        structureType: 4 // 4代表路面，默认查4
      })
      .then(async (res) => {
        if (res.success) {
          item.modelCode = res.result.code
          const bimInfo = await lib._engineController.getBimInfoByCode(item.modelCode)
          if (bimInfo) {
            const icon = await lib.utils.convertImageToBase64('images/eventIcon.png')
            const cartesian = bimInfo.position

            const czml = {
              id: lib.utils.getRandomString(10),
              name: lib.utils.getRandomString(5),
              billboard: {
                // 图片
                image: icon,
                scale: 0.5,
                disableDepthTestDistance: 999999,
                horizontalOrigin: 'CENTER',
                verticalOrigin: 'BOTTOM'
              },
              position: {
                cartesian: [0, 0, 0]
              },
              onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
                lib.popWindow.removeDialog('eventsWindow')
                lib.popWindow.createPopWindow(
                  './Components/EventsView/index.vue',
                  {
                    left: clickClinetX,
                    top: clickClinetY,
                    tag: 'eventsWindow',
                    appContext,
                    appendParent: 'player',
                    // followPoint: { typeId: obj.typeId, id },
                    closeFunc: () => {}
                  },
                  item
                )
                lib._engineController.flyToBimId(bimInfo.bimId, -15)
              }
            }
            lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
            lib._engineController.flyToBimId(bimInfo.bimId, -15)
          }
        }
      })
    // setTimeout(() => {
    //   const obj = {
    //     isOn: item.isOn, // 是否开启标签
    //     typeId: typeId,
    //     POIList: [
    //       {
    //         code: item.modelCode, // 构件编码，,坐标定位时可以不传
    //         type: 'eventPoint', // 设施设备类型名称【※】
    //         typeId: typeId, // 设施设备类型ID【※】
    //         id: parseInt(item.id), // ID【※】
    //         poseType: 0, // 定位类型（0：构件定位，1：坐标定位） 【※】
    //         alwaysShowLabel: false, // 是否永远显示title, true:显示title(默认), false:不显示title
    //         showLabelRange: [0, 2000], // POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:alwaysShowLabel属性优先于此属性)
    //         animationType: '', // 动画类型((bounce:弹出式; stretch:伸缩式; wipe:展开式)，默认bounce
    //         durationTime: 0.5, // 规定完成动画所花费的时间(单位:秒)
    //         marker: {
    //           size: [64, 75], // marker大小(宽,高 单位:像素)
    //           images: {
    //             normalURL: import.meta.env.VITE_IMG_URL + `images/eventIcon.png`, // normal 图片地址
    //             // normalURL: 'https://tech.suitbim.com/bigscreen-pddd-view/static/carPopUpBg-ebb8ff43.png', // normal 图片地址
    //             activateURL: '' // hover, active 图片地址，可为空
    //           }
    //         },
    //         window: {
    //           jsonParameter: JSON.stringify(item)
    //         }
    //       }
    //     ]
    //   }
    //   toUe5('customPOIList', obj)
    //   // 视角转换到点附近
    //   toUe5('focusCustomPOI', { id: item.id, distanceOff: 0.1 })
    // }, 500)
  }
  onUnmounted(() => {
    // toUe5('customPOIList', {
    //   isOn: false, // 是否开启标签
    //   typeId: typeId // 设施设备类型ID（为0时指全部类型）
    // })
    lib._engineController.clearCzml()
    lib.popWindow.removeDialog('eventsWindow')
  })

  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleTitleClick = () => {
    if (showAIQuestion.value) {
      lib.utils.sendQuestionToAIChat('突发事件')
    }
  }
  const handleImg = async (item) => {
    const res: any = await lib.api.docDiyDiyApi.getDocsFromUrl({ urlList: item.imagePathList })
    if (res.success && res.result.length) {
      // 创建一个映射表，将原始 URL 映射到处理后的 URL
      const urlMap = new Map()
      res.result.forEach((docItem) => {
        const originalUrl = docItem.originalUrl
        const processedUrl = import.meta.env.VITE_DOC_URL + 'img/' + docItem.fileName
        urlMap.set(originalUrl, processedUrl)
      })

      // 按照 item.imagePathList 的顺序构建 urlList
      const urlList = item.imagePathList.map((originalUrl) => {
        return urlMap.get(originalUrl) || originalUrl
      })

      lib.utils.openImageZoom(urlList, item.type)
    } else {
      lib.utils.openImageZoom([item.imgUrl], item.type)
    }
  }
  // 打开事件列表
  const setEventList = (type) => {
    closePopWindowByTag(lib.enumMap.PopWindowTag.事件列表)
    // 事件列表
    const op: PopWindowProps = {
      left: 1596,
      top: 205,
      tag: lib.enumMap.PopWindowTag.事件列表,
      zIndex: 999,
      draggable: true
    }
    showPopWindow(op, EventList, { data: eventList.value })
  }
</script>

<style lang="scss" scoped>
  .sudden-events-container {
    .head {
      width: 216px;
      height: 50px;
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      font-weight: 400;
      line-height: 45px;
      color: #d4e9ff;
      text-align: center;
      background: url('@/assets/ScreenRight/Perception/Device/head.png') no-repeat;
      background-size: 100% 100%;
    }
    .tab {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .event-sum {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 223px;
        height: 38px;
        font-weight: 400;
        color: #ffffff;
        background: url('@/assets/ScreenLeft/EventManage/eventSum.png');
        background-size: cover;
        :nth-child(1) {
          margin-left: 10px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
        }
        :nth-child(2) {
          font-family: PangMenZhengDao;
          font-size: 36px;
        }
      }
      .event-unfinish {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 223px;
        height: 38px;
        font-weight: 400;
        color: #ffffff;
        background: url('@/assets/ScreenLeft/EventManage/eventUnfinish.png');
        background-size: cover;
        :nth-child(1) {
          margin-left: 10px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 24px;
        }
        :nth-child(2) {
          font-family: PangMenZhengDao;
          font-size: 36px;
        }
      }
    }
    .table-content {
      position: relative;
      height: 156px;
      margin-top: 13px;
      .prev {
        position: absolute;
        left: 0;
        width: 21px;
        height: 156px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/EventManage/prev.png') no-repeat;
        background-size: 100% 100%;
      }
      .next {
        position: absolute;
        right: 0;
        width: 21px;
        height: 156px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/EventManage/next.png') no-repeat;
        background-size: 100% 100%;
      }
      .imgs {
        position: absolute;
        top: 2px;
        left: 31px;
        width: 558px;
        .image-group {
          display: flex;
          flex-wrap: wrap; // 必要时换行
          gap: 5px; // 图片之间的间距;
          justify-content: space-between; // 让四个图片在水平方向上均匀分布
        }
        .image {
          position: relative;
          width: 269px;
          height: 151px;
          background: url('@/assets/ScreenLeft/DynamicMaintenance/imgbg.png') no-repeat;
          background-size: 100% 100%;
          img {
            border-radius: 10px;
          }
          :deep(.el-image__inner) {
            width: 260px;
            height: 142px;
            border-radius: 10px;
          }
          .title {
            position: absolute;
            bottom: 0;
            width: 259px;
            height: 30px;
            overflow: hidden;
            font-family: 'Source Han Sans CN';
            font-size: 16px;
            font-weight: 400;
            line-height: 22px;
            color: #ffffff;
            text-align: center;
            cursor: pointer;
            background: rgb(1 52 78 / 68%);
            border-radius: 0 0 5px 5px;
          }
        }
      }
    }
    .content-bottom {
      display: flex;
      justify-content: space-between;
      width: 617px;
      margin-top: 37px;
    }
  }
</style>
