/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./IndexDatatype-00859b8b","./Math-b5f4d889","./WallGeometryLibrary-bb4c5687","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./arrayRemoveDuplicates-5b666c82","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,i,t,n,o,a,s,r,l,d,m,u,p,c,f,h,g,y,b,_){"use strict";const E=new n.Cartesian3,C=new n.Cartesian3;function H(t){const o=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions,a=t.maximumHeights,s=t.minimumHeights,r=e.defaultValue(t.granularity,l.CesiumMath.RADIANS_PER_DEGREE),d=e.defaultValue(t.ellipsoid,i.Ellipsoid.WGS84);this._positions=o,this._minimumHeights=s,this._maximumHeights=a,this._granularity=r,this._ellipsoid=i.Ellipsoid.clone(d),this._workerName="createWallOutlineGeometry";let m=1+o.length*n.Cartesian3.packedLength+2;e.defined(s)&&(m+=s.length),e.defined(a)&&(m+=a.length),this.packedLength=m+i.Ellipsoid.packedLength+1}H.pack=function(t,o,a){let s;a=e.defaultValue(a,0);const r=t._positions;let l=r.length;for(o[a++]=l,s=0;s<l;++s,a+=n.Cartesian3.packedLength)n.Cartesian3.pack(r[s],o,a);const d=t._minimumHeights;if(l=e.defined(d)?d.length:0,o[a++]=l,e.defined(d))for(s=0;s<l;++s)o[a++]=d[s];const m=t._maximumHeights;if(l=e.defined(m)?m.length:0,o[a++]=l,e.defined(m))for(s=0;s<l;++s)o[a++]=m[s];return i.Ellipsoid.pack(t._ellipsoid,o,a),o[a+=i.Ellipsoid.packedLength]=t._granularity,o};const A=i.Ellipsoid.clone(i.Ellipsoid.UNIT_SPHERE),k={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:A,granularity:void 0};return H.unpack=function(t,o,a){let s;o=e.defaultValue(o,0);let r=t[o++];const l=new Array(r);for(s=0;s<r;++s,o+=n.Cartesian3.packedLength)l[s]=n.Cartesian3.unpack(t,o);let d,m;if(r=t[o++],r>0)for(d=new Array(r),s=0;s<r;++s)d[s]=t[o++];if(r=t[o++],r>0)for(m=new Array(r),s=0;s<r;++s)m[s]=t[o++];const u=i.Ellipsoid.unpack(t,o,A),p=t[o+=i.Ellipsoid.packedLength];return e.defined(a)?(a._positions=l,a._minimumHeights=d,a._maximumHeights=m,a._ellipsoid=i.Ellipsoid.clone(u,a._ellipsoid),a._granularity=p,a):(k.positions=l,k.minimumHeights=d,k.maximumHeights=m,k.granularity=p,new H(k))},H.fromConstantHeights=function(i){const t=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).positions;let n,o;const a=i.minimumHeight,s=i.maximumHeight,r=e.defined(a),l=e.defined(s);if(r||l){const e=t.length;n=r?new Array(e):void 0,o=l?new Array(e):void 0;for(let i=0;i<e;++i)r&&(n[i]=a),l&&(o[i]=s)}return new H({positions:t,maximumHeights:o,minimumHeights:n,ellipsoid:i.ellipsoid})},H.createGeometry=function(i){const m=i._positions,u=i._minimumHeights,p=i._maximumHeights,c=i._granularity,f=i._ellipsoid,h=d.WallGeometryLibrary.computePositions(f,m,p,u,c,!1);if(!e.defined(h))return;const g=h.bottomPositions,y=h.topPositions;let b=y.length,_=2*b;const H=new Float64Array(_);let A,k=0;for(b/=3,A=0;A<b;++A){const e=3*A,i=n.Cartesian3.fromArray(y,e,E),t=n.Cartesian3.fromArray(g,e,C);H[k++]=t.x,H[k++]=t.y,H[k++]=t.z,H[k++]=i.x,H[k++]=i.y,H[k++]=i.z}const w=new s.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:H})}),G=_/3;_=2*G-4+G;const L=r.IndexDatatype.createTypedArray(G,_);let x=0;for(A=0;A<G-2;A+=2){const e=A,i=A+2,t=n.Cartesian3.fromArray(H,3*e,E),o=n.Cartesian3.fromArray(H,3*i,C);if(n.Cartesian3.equalsEpsilon(t,o,l.CesiumMath.EPSILON10))continue;const a=A+1,s=A+3;L[x++]=a,L[x++]=e,L[x++]=a,L[x++]=s,L[x++]=e,L[x++]=i}return L[x++]=G-2,L[x++]=G-1,new a.Geometry({attributes:w,indices:L,primitiveType:a.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere.fromVertices(H)})},function(t,n){return e.defined(n)&&(t=H.unpack(t,n)),t._ellipsoid=i.Ellipsoid.clone(t._ellipsoid),H.createGeometry(t)}}));
