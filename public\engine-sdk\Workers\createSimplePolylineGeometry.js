define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./ArcType-10662e8b","./EllipsoidRhumbLine-f49ff2c9","./EllipsoidGeodesic-5ac97652","./PolylinePipeline-6bd12257","./Color-eff039bb"],(function(e,o,r,t,a,i,l,n,s,d,p,f,c,y,u,h,C,g,T,v,m){"use strict";function b(e,o,r,t,a,i,l){var n,s=v.PolylinePipeline.numberOfPoints(e,o,a),d=r.red,p=r.green,f=r.blue,c=r.alpha,y=t.red,u=t.green,h=t.blue,C=t.alpha;if(m.Color.equals(r,t)){for(n=0;n<s;n++)i[l++]=m.Color.floatToByte(d),i[l++]=m.Color.floatToByte(p),i[l++]=m.Color.floatToByte(f),i[l++]=m.Color.floatToByte(c);return l}var g=(y-d)/s,T=(u-p)/s,b=(h-f)/s,E=(C-c)/s,P=l;for(n=0;n<s;n++)i[P++]=m.Color.floatToByte(d+n*g),i[P++]=m.Color.floatToByte(p+n*T),i[P++]=m.Color.floatToByte(f+n*b),i[P++]=m.Color.floatToByte(c+n*E);return P}function E(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var i=a.positions,l=a.colors,n=e.defaultValue(a.colorsPerVertex,!1);if(!e.defined(i)||i.length<2)throw new o.DeveloperError("At least two positions are required.");if(e.defined(l)&&(n&&l.length<i.length||!n&&l.length<i.length-1))throw new o.DeveloperError("colors has an invalid length.");this._positions=i,this._colors=l,this._colorsPerVertex=n,this._arcType=e.defaultValue(a.arcType,C.ArcType.GEODESIC),this._granularity=e.defaultValue(a.granularity,r.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(a.ellipsoid,t.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";var s=1+i.length*r.Cartesian3.packedLength;s+=e.defined(l)?1+l.length*m.Color.packedLength:1,this.packedLength=s+t.Ellipsoid.packedLength+3}E.pack=function(a,i,l){if(!e.defined(a))throw new o.DeveloperError("value is required");if(!e.defined(i))throw new o.DeveloperError("array is required");var n;l=e.defaultValue(l,0);var s=a._positions,d=s.length;for(i[l++]=d,n=0;n<d;++n,l+=r.Cartesian3.packedLength)r.Cartesian3.pack(s[n],i,l);var p=a._colors;for(d=e.defined(p)?p.length:0,i[l++]=d,n=0;n<d;++n,l+=m.Color.packedLength)m.Color.pack(p[n],i,l);return t.Ellipsoid.pack(a._ellipsoid,i,l),l+=t.Ellipsoid.packedLength,i[l++]=a._colorsPerVertex?1:0,i[l++]=a._arcType,i[l]=a._granularity,i},E.unpack=function(a,i,l){if(!e.defined(a))throw new o.DeveloperError("array is required");var n;i=e.defaultValue(i,0);var s=a[i++],d=new Array(s);for(n=0;n<s;++n,i+=r.Cartesian3.packedLength)d[n]=r.Cartesian3.unpack(a,i);s=a[i++];var p=s>0?new Array(s):void 0;for(n=0;n<s;++n,i+=m.Color.packedLength)p[n]=m.Color.unpack(a,i);var f=t.Ellipsoid.unpack(a,i);i+=t.Ellipsoid.packedLength;var c=1===a[i++],y=a[i++],u=a[i];return e.defined(l)?(l._positions=d,l._colors=p,l._ellipsoid=f,l._colorsPerVertex=c,l._arcType=y,l._granularity=u,l):new E({positions:d,colors:p,ellipsoid:f,colorsPerVertex:c,arcType:y,granularity:u})};var P=new Array(2),_=new Array(2),B={positions:P,height:_,ellipsoid:void 0,minDistance:void 0,granularity:void 0};function A(o,r){return e.defined(r)&&(o=E.unpack(o,r)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),E.createGeometry(o)}return E.createGeometry=function(o){var t,i,l,n,s,u=o._positions,h=o._colors,g=o._colorsPerVertex,T=o._arcType,E=o._granularity,A=o._ellipsoid,w=r.CesiumMath.chordLength(E,A.maximumRadius),k=e.defined(h)&&!g,D=u.length,G=0;if(T===C.ArcType.GEODESIC||T===C.ArcType.RHUMB){var L,V,x;T===C.ArcType.GEODESIC?(L=r.CesiumMath.chordLength(E,A.maximumRadius),V=v.PolylinePipeline.numberOfPoints,x=v.PolylinePipeline.generateArc):(L=E,V=v.PolylinePipeline.numberOfPointsRhumbLine,x=v.PolylinePipeline.generateRhumbArc);var S=v.PolylinePipeline.extractHeights(u,A),R=B;if(T===C.ArcType.GEODESIC?R.minDistance=w:R.granularity=E,R.ellipsoid=A,k){var I=0;for(t=0;t<D-1;t++)I+=V(u[t],u[t+1],L)+1;i=new Float64Array(3*I),n=new Uint8Array(4*I),R.positions=P,R.height=_;var O=0;for(t=0;t<D-1;++t){P[0]=u[t],P[1]=u[t+1],_[0]=S[t],_[1]=S[t+1];var M=x(R);if(e.defined(h)){var U=M.length/3;s=h[t];for(var q=0;q<U;++q)n[O++]=m.Color.floatToByte(s.red),n[O++]=m.Color.floatToByte(s.green),n[O++]=m.Color.floatToByte(s.blue),n[O++]=m.Color.floatToByte(s.alpha)}i.set(M,G),G+=M.length}}else if(R.positions=u,R.height=S,i=new Float64Array(x(R)),e.defined(h)){for(n=new Uint8Array(i.length/3*4),t=0;t<D-1;++t){var N=u[t],F=u[t+1],H=h[t],W=h[t+1];G=b(N,F,H,W,w,n,G)}var Y=h[D-1];n[G++]=m.Color.floatToByte(Y.red),n[G++]=m.Color.floatToByte(Y.green),n[G++]=m.Color.floatToByte(Y.blue),n[G++]=m.Color.floatToByte(Y.alpha)}}else{l=k?2*D-2:D,i=new Float64Array(3*l),n=e.defined(h)?new Uint8Array(4*l):void 0;var z=0,J=0;for(t=0;t<D;++t){var j=u[t];if(k&&t>0&&(r.Cartesian3.pack(j,i,z),z+=3,s=h[t-1],n[J++]=m.Color.floatToByte(s.red),n[J++]=m.Color.floatToByte(s.green),n[J++]=m.Color.floatToByte(s.blue),n[J++]=m.Color.floatToByte(s.alpha)),k&&t===D-1)break;r.Cartesian3.pack(j,i,z),z+=3,e.defined(h)&&(s=h[t],n[J++]=m.Color.floatToByte(s.red),n[J++]=m.Color.floatToByte(s.green),n[J++]=m.Color.floatToByte(s.blue),n[J++]=m.Color.floatToByte(s.alpha))}}var K=new c.GeometryAttributes;K.position=new p.GeometryAttribute({componentDatatype:d.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:i}),e.defined(h)&&(K.color=new p.GeometryAttribute({componentDatatype:d.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:n,normalize:!0})),l=i.length/3;var Q=2*(l-1),X=y.IndexDatatype.createTypedArray(l,Q),Z=0;for(t=0;t<l-1;++t)X[Z++]=t,X[Z++]=t+1;return new p.Geometry({attributes:K,indices:X,primitiveType:f.PrimitiveType.LINES,boundingSphere:a.BoundingSphere.fromPoints(u)})},A}));