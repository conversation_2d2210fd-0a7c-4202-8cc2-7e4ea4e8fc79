<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-15 16:43:00
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-07-10 10:12:31
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\Components\HomePage\Components\Perception\Components\Traffic\Components\Components\tabs.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="tabs-container">
    <div
      @click="handleClick(item, index)"
      class="tabs-item"
      :style="{
        backgroundImage: `url(${lib.utils.getAssetsFile('ScreenRight/Perception/Traffic/btn' + (index + 1) + (item.selected ? 'Selected' : '') + '.png')})`
      }"
      v-for="(item, index) in carTopList"
      :key="item.id">
      <div class="num">
        <div>{{ lib.utils.formatNumberText(item.data) }}</div>
        <span>辆</span>
      </div>
      <div class="name">{{ item.pro }}</div>
    </div>
  </div>
</template>

<script setup>
  import { watch, watchEffect } from 'vue'

  import lib from '@/utils/lib.ts'
  // import {    } from '@/utils'
  const props = defineProps({
    carTotalNumber: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: 'day'
    },
    lineName: {
      type: String,
      default: '东线'
    },
    date: {
      type: String,
      default: ''
    },
    carTopList: {
      type: Array,
      default: () => []
    }
  })

  defineOptions({
    name: 'Tabs'
  })

  const handleClick = (val, ind) => {
    // 不直接修改props，而是通过事件通知父组件
    const selected = !val.selected
    // 发送选中状态变化事件
    lib.bus.trafficMonitorTab.emit({
      ...val,
      selected: selected,
      index: ind
    })
  }
</script>

<style lang="scss" scoped>
  .tabs-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 75px;
    .tabs-item {
      box-sizing: border-box;
      width: 222px;
      height: 101px;
      padding: 8px 15px 8px 60px;
      background-size: cover;
      .num {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 12px;
        div {
          font-family: YouSheBiaoTiHei;
          font-size: 24px;
          font-weight: bold;
          line-height: 24px;
          color: rgb(18 227 255 / 80%);
          text-align: right;
        }
        span {
          margin-left: 5px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-weight: bold;
          line-height: 24px;
          color: #afd6ed;
        }
      }
      .name {
        margin-top: 12px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #afd6ed;
        text-align: right;
      }
      &.selected {
        .num {
          color: #00ffff;
        }
        .name {
          color: #ffffff;
        }
      }
    }
  }
</style>
