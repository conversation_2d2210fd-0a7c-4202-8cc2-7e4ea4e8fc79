<template>
  <div class="prop-picture-container" :style="{ width }">
    <template v-if="data.length">
      <div class="row">
        <div class="name">照片：</div>
        <div class="value scroll-bar-style" :style="data.length > 2 ? 'overflow: hidden;overflow-x: scroll;' : ''">
          <img
            v-for="(item, index) in data"
            :key="index"
            :src="`${item.url.startsWith('http') ? item.url : getAssetsFile(`CommonPopup/${item.url}`)}`"
            @click="clickImage(item.url, index)" />
        </div>
      </div>
    </template>
    <div class="row" v-else>
      <div class="name">照片：</div>
      <div class="no-data">暂无照片</div>
      <!-- <div class="value">
        <img src="@/assets/CommonPopup/picture.png" @click="clickImage()" />
        <img src="@/assets/CommonPopup/picture.png" @click="clickImage()" />
      </div> -->
    </div>
  </div>
</template>
<script setup lang="ts">
  import useStore from '@/store'
  import { getAssetsFile } from '@/utils'
  import { type ImageViewerProps, useImageViewer } from 'znyg-frontend-common'
  import lib from '@/utils/lib'
  const { storeDictionary } = useStore()
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '100%'
    }
  })
  const clickImage = (img, index) => {
    // const curImg = getAssetsFile(`CommonPopup/${img}`)
    // const curImg = img.startsWith('http') ? img : getAssetsFile(`CommonPopup/${img}`)
    // storeDictionary.DIALOG_IMG([curImg])
    // const op: ImageViewerProps = {
    //   urlList: props.data.map((_) => (_.url.startsWith('http') ? _.url : getAssetsFile(`CommonPopup/${_.url}`))),
    //   hideOnClickModal: true,
    //   initialIndex: index
    // }
    // useImageViewer(op)
    lib.utils.openImageZoom(
      props.data.map((_:any) => (_.url.startsWith('http') ? _.url : getAssetsFile(`CommonPopup/${_.url}`)))
    )
  }
</script>

<style lang="scss" scoped>
  .prop-picture-container {
    .row {
      display: flex;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .name {
        width: 62px;
        font-family: Alibaba-PuHuiTi;
        font-size: 14px;
        font-weight: normal;
        line-height: 16px;
        color: #ffffff;
      }
      .value {
        display: flex;
        flex: 1;

        // flex-wrap: wrap;
        justify-content: space-between;
        img {
          width: 165px;
          height: 95px;
          margin-bottom: 10px;
          cursor: pointer;

          // &:nth-child(odd) {
          //   margin-right: 31px;
          // }
        }
      }
      .no-data {
        flex: 1;
        font-size: 14px;
        color: #cccccc;
      }
    }
  }
</style>
