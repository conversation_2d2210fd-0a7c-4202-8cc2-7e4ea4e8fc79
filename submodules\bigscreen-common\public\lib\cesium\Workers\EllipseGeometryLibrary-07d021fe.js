/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Math-b5f4d889","./Rectangle-9bffefe4","./Transforms-42ed7720"],(function(t,a,e,n,i){"use strict";const r={},s=new a.Cartesian3,o=new a.Cartesian3,l=new i.Quaternion,c=new n.Matrix3;function C(t,e,r,C,y,u,m,h,x,M){const f=t+e;a.Cartesian3.multiplyByScalar(C,Math.cos(f),s),a.Cartesian3.multiplyByScalar(r,Math.sin(f),o),a.Cartesian3.add(s,o,s);let z=Math.cos(t);z*=z;let d=Math.sin(t);d*=d;const _=u/Math.sqrt(m*z+y*d)/h;return i.Quaternion.fromAxisAngle(s,_,l),n.Matrix3.fromQuaternion(l,c),n.Matrix3.multiplyByVector(c,x,M),a.Cartesian3.normalize(M,M),a.Cartesian3.multiplyByScalar(M,h,M),M}const y=new a.Cartesian3,u=new a.Cartesian3,m=new a.Cartesian3,h=new a.Cartesian3;r.raisePositionsToHeight=function(t,e,n){const i=e.ellipsoid,r=e.height,s=e.extrudedHeight,o=n?t.length/3*2:t.length/3,l=new Float64Array(3*o),c=t.length,C=n?c:0;for(let e=0;e<c;e+=3){const o=e+1,c=e+2,x=a.Cartesian3.fromArray(t,e,y);i.scaleToGeodeticSurface(x,x);const M=a.Cartesian3.clone(x,u),f=i.geodeticSurfaceNormal(x,h),z=a.Cartesian3.multiplyByScalar(f,r,m);a.Cartesian3.add(x,z,x),n&&(a.Cartesian3.multiplyByScalar(f,s,z),a.Cartesian3.add(M,z,M),l[e+C]=M.x,l[o+C]=M.y,l[c+C]=M.z),l[e]=x.x,l[o]=x.y,l[c]=x.z}return l};const x=new a.Cartesian3,M=new a.Cartesian3,f=new a.Cartesian3;r.computeEllipsePositions=function(t,n,i){const r=t.semiMinorAxis,s=t.semiMajorAxis,o=t.rotation,l=t.center,c=8*t.granularity,h=r*r,z=s*s,d=s*r,_=a.Cartesian3.magnitude(l),O=a.Cartesian3.normalize(l,x);let p=a.Cartesian3.cross(a.Cartesian3.UNIT_Z,l,M);p=a.Cartesian3.normalize(p,p);const w=a.Cartesian3.cross(O,p,f);let P=1+Math.ceil(e.CesiumMath.PI_OVER_TWO/c);const T=e.CesiumMath.PI_OVER_TWO/(P-1);let g=e.CesiumMath.PI_OVER_TWO-P*T;g<0&&(P-=Math.ceil(Math.abs(g)/T));const I=n?new Array(3*(P*(P+2)*2)):void 0;let E=0,R=y,V=u;const A=4*P*3;let W=A-1,S=0;const b=i?new Array(A):void 0;let B,v,Q,G,H;for(g=e.CesiumMath.PI_OVER_TWO,R=C(g,o,w,p,h,d,z,_,O,R),n&&(I[E++]=R.x,I[E++]=R.y,I[E++]=R.z),i&&(b[W--]=R.z,b[W--]=R.y,b[W--]=R.x),g=e.CesiumMath.PI_OVER_TWO-T,B=1;B<P+1;++B){if(R=C(g,o,w,p,h,d,z,_,O,R),V=C(Math.PI-g,o,w,p,h,d,z,_,O,V),n){for(I[E++]=R.x,I[E++]=R.y,I[E++]=R.z,Q=2*B+2,v=1;v<Q-1;++v)G=v/(Q-1),H=a.Cartesian3.lerp(R,V,G,m),I[E++]=H.x,I[E++]=H.y,I[E++]=H.z;I[E++]=V.x,I[E++]=V.y,I[E++]=V.z}i&&(b[W--]=R.z,b[W--]=R.y,b[W--]=R.x,b[S++]=V.x,b[S++]=V.y,b[S++]=V.z),g=e.CesiumMath.PI_OVER_TWO-(B+1)*T}for(B=P;B>1;--B){if(g=e.CesiumMath.PI_OVER_TWO-(B-1)*T,R=C(-g,o,w,p,h,d,z,_,O,R),V=C(g+Math.PI,o,w,p,h,d,z,_,O,V),n){for(I[E++]=R.x,I[E++]=R.y,I[E++]=R.z,Q=2*(B-1)+2,v=1;v<Q-1;++v)G=v/(Q-1),H=a.Cartesian3.lerp(R,V,G,m),I[E++]=H.x,I[E++]=H.y,I[E++]=H.z;I[E++]=V.x,I[E++]=V.y,I[E++]=V.z}i&&(b[W--]=R.z,b[W--]=R.y,b[W--]=R.x,b[S++]=V.x,b[S++]=V.y,b[S++]=V.z)}g=e.CesiumMath.PI_OVER_TWO,R=C(-g,o,w,p,h,d,z,_,O,R);const N={};return n&&(I[E++]=R.x,I[E++]=R.y,I[E++]=R.z,N.positions=I,N.numPts=P),i&&(b[W--]=R.z,b[W--]=R.y,b[W--]=R.x,N.outerPositions=b),N};var z=r;t.EllipseGeometryLibrary=z}));
