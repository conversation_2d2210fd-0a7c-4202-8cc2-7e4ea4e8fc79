/**
 * @description: 创建动画 https://developer.mozilla.org/zh-CN/docs/Web/API/Animation
 * @param {*} dom元素
 * @param {*} keyframes 动画关键帧，https://developer.mozilla.org/zh-CN/docs/Web/API/Web_Animations_API/Keyframe_Formats
 * @param {*} opt 动画配置项
 * @return {*}
 */
export default function useAnimation(el, keyframes, opt) {
  const defaultOption = {
    iterations: Infinity,
    direction: 'alternate',
    duration: 2000,
    easing: 'ease-in-out'
  }
  const option = {
    ...defaultOption,
    ...opt
  }

  const animate = el.animate(keyframes, option)
  const play = () => {
    animate && animate.play()
  }
  const cancel = () => {
    animate && animate.cancel()
  }
  const pause = () => {
    animate && animate.pause()
  }
  const finish = () => {
    animate && animate.finish()
  }

  return {
    animate,
    play,
    cancel,
    pause,
    finish
  }
}
