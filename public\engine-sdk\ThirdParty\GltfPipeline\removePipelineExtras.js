import ForEach from"./ForEach.js";import defined from"../../Core/defined.js";function removePipelineExtras(e){return ForEach.shader(e,(function(e){removeExtras(e)})),ForEach.buffer(e,(function(e){removeExtras(e)})),ForEach.image(e,(function(e){removeExtras(e),ForEach.compressedImage(e,(function(e){removeExtras(e)}))})),removeExtras(e),e}function removeExtras(e){defined(e.extras)&&(defined(e.extras._pipeline)&&delete e.extras._pipeline,0===Object.keys(e.extras).length&&delete e.extras)}export default removePipelineExtras;