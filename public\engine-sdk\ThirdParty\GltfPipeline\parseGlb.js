import addPipelineExtras from"./addPipelineExtras.js";import removeExtensionsUsed from"./removeExtensionsUsed.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import getMagic from"../../Core/getMagic.js";import getStringFromTypedArray from"../../Core/getStringFromTypedArray.js";import RuntimeError from"../../Core/RuntimeError.js";var sizeOfUint32=4;function parseGlb(r){var e=getMagic(r);if("glTF"!==e)throw new RuntimeError("File is not valid binary glTF");var i=readHeader(r,0,5),a=i[1];if(1!==a&&2!==a)throw new RuntimeError("Binary glTF version is not 1 or 2");return 1===a?parseGlbVersion1(r,i):parseGlbVersion2(r,i)}function readHeader(r,e,i){for(var a=new DataView(r.buffer),n=new Array(i),t=0;t<i;++t)n[t]=a.getUint32(r.byteOffset+e+t*sizeOfUint32,!0);return n}function parseGlbVersion1(r,e){var i=e[2],a=e[3],n=e[4];if(0!==n)throw new RuntimeError("Binary glTF scene format is not JSON");var t=20,o=t+a,s=getStringFromTypedArray(r,t,a),f=JSON.parse(s);addPipelineExtras(f);var d=r.subarray(o,i),l=f.buffers;if(defined(l)&&Object.keys(l).length>0){var p=defaultValue(l.binary_glTF,l.KHR_binary_glTF);defined(p)&&(p.extras._pipeline.source=d)}return removeExtensionsUsed(f,"KHR_binary_glTF"),f}function parseGlbVersion2(r,e){var i,a,n=e[2],t=12;while(t<n){var o=readHeader(r,t,2),s=o[0],f=o[1];t+=8;var d=r.subarray(t,t+s);if(t+=s,1313821514===f){var l=getStringFromTypedArray(d);i=JSON.parse(l),addPipelineExtras(i)}else 5130562===f&&(a=d)}if(defined(i)&&defined(a)){var p=i.buffers;if(defined(p)&&p.length>0){var m=p[0];m.extras._pipeline.source=a}}return i}export default parseGlb;