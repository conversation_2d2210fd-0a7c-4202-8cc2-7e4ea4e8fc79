/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./ArcType-e42cfb05","./arrayRemoveDuplicates-5b666c82","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./EllipsoidRhumbLine-d5e7f3db","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./Transforms-42ed7720"],(function(e,t,n,i,o,r,a,s,c,l,u,h,d,p,f){"use strict";function y(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(y.prototype,{length:{get:function(){return this._length}}}),y.prototype.enqueue=function(e){this._array.push(e),this._length++},y.prototype.dequeue=function(){if(0===this._length)return;const e=this._array;let t=this._offset;const n=e[t];return e[t]=void 0,t++,t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,n},y.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},y.prototype.contains=function(e){return-1!==this._array.indexOf(e)},y.prototype.clear=function(){this._array.length=this._offset=this._length=0},y.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};const g={computeHierarchyPackedLength:function(e,t){let n=0;const i=[e];for(;i.length>0;){const e=i.pop();if(!a.defined(e))continue;n+=2;const o=e.positions,r=e.holes;if(a.defined(o)&&o.length>0&&(n+=o.length*t.packedLength),a.defined(r)){const e=r.length;for(let t=0;t<e;++t)i.push(r[t])}}return n},packPolygonHierarchy:function(e,t,n,i){const o=[e];for(;o.length>0;){const e=o.pop();if(!a.defined(e))continue;const r=e.positions,s=e.holes;if(t[n++]=a.defined(r)?r.length:0,t[n++]=a.defined(s)?s.length:0,a.defined(r)){const e=r.length;for(let o=0;o<e;++o,n+=i.packedLength)i.pack(r[o],t,n)}if(a.defined(s)){const e=s.length;for(let t=0;t<e;++t)o.push(s[t])}}return n},unpackPolygonHierarchy:function(e,t,n){const i=e[t++],o=e[t++],r=new Array(i),a=o>0?new Array(o):void 0;for(let o=0;o<i;++o,t+=n.packedLength)r[o]=n.unpack(e,t);for(let i=0;i<o;++i)a[i]=g.unpackPolygonHierarchy(e,t,n),t=a[i].startingIndex,delete a[i].startingIndex;return{positions:r,holes:a,startingIndex:t}}},m=new i.Cartesian2;function C(e,t,n,o){return i.Cartesian2.subtract(t,e,m),i.Cartesian2.multiplyByScalar(m,n/o,m),i.Cartesian2.add(e,m,m),[m.x,m.y]}const b=new i.Cartesian3;function T(e,t,n,o){return i.Cartesian3.subtract(t,e,b),i.Cartesian3.multiplyByScalar(b,n/o,b),i.Cartesian3.add(e,b,b),[b.x,b.y,b.z]}g.subdivideLineCount=function(e,t,n){const o=i.Cartesian3.distance(e,t)/n,r=Math.max(0,Math.ceil(d.CesiumMath.log2(o)));return Math.pow(2,r)};const v=new o.Cartographic,w=new o.Cartographic,x=new o.Cartographic,A=new i.Cartesian3,L=new s.EllipsoidRhumbLine;g.subdivideRhumbLineCount=function(e,t,n,i){const o=e.cartesianToCartographic(t,v),r=e.cartesianToCartographic(n,w),a=new s.EllipsoidRhumbLine(o,r,e).surfaceDistance/i,c=Math.max(0,Math.ceil(d.CesiumMath.log2(a)));return Math.pow(2,c)},g.subdivideTexcoordLine=function(e,t,n,o,r,a){const s=g.subdivideLineCount(n,o,r),c=i.Cartesian2.distance(e,t),l=c/s,u=a;u.length=2*s;let h=0;for(let n=0;n<s;n++){const i=C(e,t,n*l,c);u[h++]=i[0],u[h++]=i[1]}return u},g.subdivideLine=function(e,t,n,o){const r=g.subdivideLineCount(e,t,n),s=i.Cartesian3.distance(e,t),c=s/r;a.defined(o)||(o=[]);const l=o;l.length=3*r;let u=0;for(let n=0;n<r;n++){const i=T(e,t,n*c,s);l[u++]=i[0],l[u++]=i[1],l[u++]=i[2]}return l},g.subdivideTexcoordRhumbLine=function(e,t,n,o,r,a,s){const c=n.cartesianToCartographic(o,v),l=n.cartesianToCartographic(r,w);L.setEndPoints(c,l);const u=L.surfaceDistance/a,h=Math.max(0,Math.ceil(d.CesiumMath.log2(u))),p=Math.pow(2,h),f=i.Cartesian2.distance(e,t),y=f/p,g=s;g.length=2*p;let m=0;for(let n=0;n<p;n++){const i=C(e,t,n*y,f);g[m++]=i[0],g[m++]=i[1]}return g},g.subdivideRhumbLine=function(e,t,n,i,o){const r=e.cartesianToCartographic(t,v),c=e.cartesianToCartographic(n,w),l=new s.EllipsoidRhumbLine(r,c,e),u=l.surfaceDistance/i,h=Math.max(0,Math.ceil(d.CesiumMath.log2(u))),p=Math.pow(2,h),f=l.surfaceDistance/p;a.defined(o)||(o=[]);const y=o;y.length=3*p;let g=0;for(let t=0;t<p;t++){const n=l.interpolateUsingSurfaceDistance(t*f,x),i=e.cartographicToCartesian(n,A);y[g++]=i.x,y[g++]=i.y,y[g++]=i.z}return y};const E=new i.Cartesian3,I=new i.Cartesian3,P=new i.Cartesian3,D=new i.Cartesian3;g.scaleToGeodeticHeightExtruded=function(e,t,n,r,s){r=a.defaultValue(r,o.Ellipsoid.WGS84);const c=E;let l=I;const u=P;let h=D;if(a.defined(e)&&a.defined(e.attributes)&&a.defined(e.attributes.position)){const o=e.attributes.position.values,a=o.length/2;for(let e=0;e<a;e+=3)i.Cartesian3.fromArray(o,e,u),r.geodeticSurfaceNormal(u,c),h=r.scaleToGeodeticSurface(u,h),l=i.Cartesian3.multiplyByScalar(c,n,l),l=i.Cartesian3.add(h,l,l),o[e+a]=l.x,o[e+1+a]=l.y,o[e+2+a]=l.z,s&&(h=i.Cartesian3.clone(u,h)),l=i.Cartesian3.multiplyByScalar(c,t,l),l=i.Cartesian3.add(h,l,l),o[e]=l.x,o[e+1]=l.y,o[e+2]=l.z}return e},g.polygonOutlinesFromHierarchy=function(e,t,o){const r=[],s=new y;let c,l,u;for(s.enqueue(e);0!==s.length;){const e=s.dequeue();let h=e.positions;if(t)for(u=h.length,c=0;c<u;c++)o.scaleToGeodeticSurface(h[c],h[c]);if(h=n.arrayRemoveDuplicates(h,i.Cartesian3.equalsEpsilon,!0),h.length<3)continue;const d=e.holes?e.holes.length:0;for(c=0;c<d;c++){const h=e.holes[c];let d=h.positions;if(t)for(u=d.length,l=0;l<u;++l)o.scaleToGeodeticSurface(d[l],d[l]);if(d=n.arrayRemoveDuplicates(d,i.Cartesian3.equalsEpsilon,!0),d.length<3)continue;r.push(d);let p=0;for(a.defined(h.holes)&&(p=h.holes.length),l=0;l<p;l++)s.enqueue(h.holes[l])}r.push(h)}return r},g.polygonsFromHierarchy=function(e,t,o,r,s){const c=[],l=[],u=new y;for(u.enqueue(e);0!==u.length;){const e=u.dequeue();let h=e.positions;const d=e.holes;let f,y;if(r)for(y=h.length,f=0;f<y;f++)s.scaleToGeodeticSurface(h[f],h[f]);if(t||(h=n.arrayRemoveDuplicates(h,i.Cartesian3.equalsEpsilon,!0)),h.length<3)continue;let g=o(h);if(!a.defined(g))continue;const m=[];let C=p.PolygonPipeline.computeWindingOrder2D(g);C===p.WindingOrder.CLOCKWISE&&(g.reverse(),h=h.slice().reverse());let b=h.slice();const T=a.defined(d)?d.length:0,v=[];let w;for(f=0;f<T;f++){const e=d[f];let c=e.positions;if(r)for(y=c.length,w=0;w<y;++w)s.scaleToGeodeticSurface(c[w],c[w]);if(t||(c=n.arrayRemoveDuplicates(c,i.Cartesian3.equalsEpsilon,!0)),c.length<3)continue;const l=o(c);if(!a.defined(l))continue;C=p.PolygonPipeline.computeWindingOrder2D(l),C===p.WindingOrder.CLOCKWISE&&(l.reverse(),c=c.slice().reverse()),v.push(c),m.push(b.length),b=b.concat(c),g=g.concat(l);let h=0;for(a.defined(e.holes)&&(h=e.holes.length),w=0;w<h;w++)u.enqueue(e.holes[w])}c.push({outerRing:h,holes:v}),l.push({positions:b,positions2D:g,holes:m})}return{hierarchy:c,polygons:l}};const M=new i.Cartesian2,_=new i.Cartesian3,G=new f.Quaternion,R=new o.Matrix3;g.computeBoundingRectangle=function(e,t,n,r,s){const c=f.Quaternion.fromAxisAngle(e,r,G),l=o.Matrix3.fromQuaternion(c,R);let u=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,d=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY;const y=n.length;for(let e=0;e<y;++e){const r=i.Cartesian3.clone(n[e],_);o.Matrix3.multiplyByVector(l,r,r);const s=t(r,M);a.defined(s)&&(u=Math.min(u,s.x),h=Math.max(h,s.x),d=Math.min(d,s.y),p=Math.max(p,s.y))}return s.x=u,s.y=d,s.width=h-u,s.height=p-d,s},g.createGeometryFromPositions=function(e,n,o,s,l,h,d){let f=p.PolygonPipeline.triangulate(n.positions2D,n.holes);f.length<3&&(f=[0,1,2]);const y=n.positions,g=a.defined(o),m=g?o.positions:void 0;if(l){const e=y.length,t=new Array(3*e);let n=0;for(let i=0;i<e;i++){const e=y[i];t[n++]=e.x,t[n++]=e.y,t[n++]=e.z}const o={attributes:{position:new c.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})},indices:f,primitiveType:c.PrimitiveType.TRIANGLES};g&&(o.attributes.st=new c.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:i.Cartesian2.packArray(m)}));const a=new c.Geometry(o);return h.normal?u.GeometryPipeline.computeNormal(a):a}return d===t.ArcType.GEODESIC?p.PolygonPipeline.computeSubdivision(e,y,f,m,s):d===t.ArcType.RHUMB?p.PolygonPipeline.computeRhumbLineSubdivision(e,y,f,m,s):void 0};const S=[],N=[],O=new i.Cartesian3,q=new i.Cartesian3;g.computeWallGeometry=function(e,n,o,s,u,p){let f,y,m,C,b,T,v,w,x,A=e.length,L=0,E=0;const I=a.defined(n),P=I?n.positions:void 0;if(u)for(y=3*A*2,f=new Array(2*y),I&&(x=2*A*2,w=new Array(2*x)),m=0;m<A;m++)C=e[m],b=e[(m+1)%A],f[L]=f[L+y]=C.x,++L,f[L]=f[L+y]=C.y,++L,f[L]=f[L+y]=C.z,++L,f[L]=f[L+y]=b.x,++L,f[L]=f[L+y]=b.y,++L,f[L]=f[L+y]=b.z,++L,I&&(T=P[m],v=P[(m+1)%A],w[E]=w[E+x]=T.x,++E,w[E]=w[E+x]=T.y,++E,w[E]=w[E+x]=v.x,++E,w[E]=w[E+x]=v.y,++E);else{const n=d.CesiumMath.chordLength(s,o.maximumRadius);let i=0;if(p===t.ArcType.GEODESIC)for(m=0;m<A;m++)i+=g.subdivideLineCount(e[m],e[(m+1)%A],n);else if(p===t.ArcType.RHUMB)for(m=0;m<A;m++)i+=g.subdivideRhumbLineCount(o,e[m],e[(m+1)%A],n);for(y=3*(i+A),f=new Array(2*y),I&&(x=2*(i+A),w=new Array(2*x)),m=0;m<A;m++){let i,r;C=e[m],b=e[(m+1)%A],I&&(T=P[m],v=P[(m+1)%A]),p===t.ArcType.GEODESIC?(i=g.subdivideLine(C,b,n,N),I&&(r=g.subdivideTexcoordLine(T,v,C,b,n,S))):p===t.ArcType.RHUMB&&(i=g.subdivideRhumbLine(o,C,b,n,N),I&&(r=g.subdivideTexcoordRhumbLine(T,v,o,C,b,n,S)));const a=i.length;for(let e=0;e<a;++e,++L)f[L]=i[e],f[L+y]=i[e];if(f[L]=b.x,f[L+y]=b.x,++L,f[L]=b.y,f[L+y]=b.y,++L,f[L]=b.z,f[L+y]=b.z,++L,I){const e=r.length;for(let t=0;t<e;++t,++E)w[E]=r[t],w[E+x]=r[t];w[E]=v.x,w[E+x]=v.x,++E,w[E]=v.y,w[E+x]=v.y,++E}}}A=f.length;const D=h.IndexDatatype.createTypedArray(A/3,A-6*e.length);let M=0;for(A/=6,m=0;m<A;m++){const e=m,t=e+1,n=e+A,o=n+1;C=i.Cartesian3.fromArray(f,3*e,O),b=i.Cartesian3.fromArray(f,3*t,q),i.Cartesian3.equalsEpsilon(C,b,d.CesiumMath.EPSILON10,d.CesiumMath.EPSILON10)||(D[M++]=e,D[M++]=n,D[M++]=t,D[M++]=t,D[M++]=n,D[M++]=o)}const _={attributes:new l.GeometryAttributes({position:new c.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f})}),indices:D,primitiveType:c.PrimitiveType.TRIANGLES};I&&(_.attributes.st=new c.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:w}));return new c.Geometry(_)};var k=g;e.PolygonGeometryLibrary=k}));
