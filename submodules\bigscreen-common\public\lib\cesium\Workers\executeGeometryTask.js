/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./createTaskProcessorWorker","./OglParser-bd379888","./Geometry-a94d02e6","./ComponentDatatype-dad47320","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./defined-3b3eb2ba","./AttributeCompression-d661357e","./Math-b5f4d889","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./RuntimeError-592f0d41","./WebGLConstants-433debbf","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./EncodedCartesian3-6d30a00c","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(t,e,n,i,o,a,r,s,u,c,d,f,p,l,m,h,y,v,b){"use strict";const A={};A.getAttributeSemantic=function(t){let e;switch(t){case"position":e="POSITION";break;case"normal":e="NORMAL";break;case"st":e="TEXCOORD";break;case"featureId":e="_FEATURE_ID";break;default:e=t}return e},A.getAttributeType=function(t){let e;switch(t){case"position":case"normal":e="VEC3";break;case"st":e="VEC2";break;case"featureId":e="SCALAR";break;default:e=t}return e},A.getAttributeName=function(t){let e;switch(t){case"position":e="POSITION";break;case"normal":e="NORMAL";break;case"st":e="TEXCOORD";break;case"featureId":e="_FEATURE_ID";break;default:e=t}return e},A.getAttributeSetIndex=function(t){let e;"st"!==t&&"featureId"!==t||(e=0);return e},A.getAttributeComponentDatatype=function(t){let e;switch(t){case"position":case"normal":case"st":default:e=i.ComponentDatatype.FLOAT;break;case"featureId":e=i.ComponentDatatype.UNSIGNED_SHORT}return e},A.getAttributeComponentsPerAttribute=function(t){let e;switch(t){case"position":case"normal":default:e=3;break;case"st":e=2;break;case"featureId":e=1}return e};var g=A;const T={};function D(t,e,n){return{componentDatatype:n,componentsPerAttribute:e,values:i.ComponentDatatype.createTypedArray(n,t*e),count:t,max:void 0,min:void 0,quantization:void 0,interleave:void 0}}T.optimizeGeometryVertexCache=function(t){o.GeometryPipeline.reorderForPreVertexCache(t),o.GeometryPipeline.reorderForPostVertexCache(t);const e=n.Geometry.computeNumberOfVertices(t);return t.indices=a.IndexDatatype.createTypedArray(e,t.indices),t},T.findAttributesMaxMin=function(t){const e=t.componentsPerAttribute,n=new Array(e).fill(Number.POSITIVE_INFINITY),i=new Array(e).fill(Number.NEGATIVE_INFINITY),o=t.values,a=Math.max,r=Math.min;for(let t=0,s=o.length;t<s;t+=e)for(let s=0;s<e;s++)n[s]=r(n[s],o[t+s]),i[s]=a(i[s],o[t+s]);return{max:i,min:n}},T.allocateGeometry=function(t,e,o){return{attributes:{position:D(t,3,i.ComponentDatatype.FLOAT),normal:D(t,3,i.ComponentDatatype.FLOAT),st:o?D(t,2,i.ComponentDatatype.FLOAT):void 0,featureId:D(t,1,i.ComponentDatatype.UNSIGNED_SHORT)},indices:a.IndexDatatype.createTypedArray(t,e),primitiveType:n.PrimitiveType.TRIANGLES}},T.releaseAttributes=function(t){for(const e in t)t.hasOwnProperty(e)&&r.defined(t[e])&&r.defined(t[e].values)&&(t[e].values=void 0)},T.parseToSimpleGeometry=function(t){const e=t.attributes,n={};for(const t in e)if(e.hasOwnProperty(t)&&r.defined(e[t])&&r.defined(e[t].values)){const i=e[t];n[t]=i.values}return{attributes:n,indices:t.indices,maxMin:t.maxMin}},T.parseToCZMGeometry=function(t){const e=t.attributes,i={};for(const t in e)if(e.hasOwnProperty(t)&&r.defined(e[t])&&r.defined(e[t].values)){const o=e[t];i[t]=new n.GeometryAttribute({componentDatatype:g.getAttributeComponentDatatype(t),componentsPerAttribute:g.getAttributeComponentsPerAttribute(t),values:o})}return{attributes:i,indices:t.indices,maxMin:t.maxMin,primitiveType:n.PrimitiveType.TRIANGLES}},T.toTypedArrayGeometry=function(t){let e,o;for(let s=0,u=t.length;s<u;s++){e=t[s],o=e.attributes;for(const t in o)if(o.hasOwnProperty(t)&&r.defined(o[t])&&r.defined(o[t].values)){const e=o[t];e.values=i.ComponentDatatype.createTypedArray(e.componentDatatype,e.values)}const u=n.Geometry.computeNumberOfVertices(e);e.indices=a.IndexDatatype.createTypedArray(u,e.indices)}return t};var I=T;var O=Object.freeze({SLICE_OGL:0,OPTIMIZE:1});var E=Object.freeze({OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"});const S={};function k(){this.credits=[]}function C(){this.baseColorTexture=void 0,this.metallicRoughnessTexture=void 0,this.baseColorFactor=d.Cartesian4.clone(C.DEFAULT_BASE_COLOR_FACTOR),this.metallicFactor=C.DEFAULT_METALLIC_FACTOR,this.roughnessFactor=C.DEFAULT_ROUGHNESS_FACTOR}function P(){this.diffuseTexture=void 0,this.specularGlossinessTexture=void 0,this.diffuseFactor=d.Cartesian4.clone(P.DEFAULT_DIFFUSE_FACTOR),this.specularFactor=c.Cartesian3.clone(P.DEFAULT_SPECULAR_FACTOR),this.glossinessFactor=P.DEFAULT_GLOSSINESS_FACTOR}function L(){this.metallicRoughness=new C,this.specularGlossiness=void 0,this.emissiveTexture=void 0,this.normalTexture=void 0,this.occlusionTexture=void 0,this.emissiveFactor=c.Cartesian3.clone(L.DEFAULT_EMISSIVE_FACTOR),this.alphaMode=E.OPAQUE,this.alphaCutoff=.5,this.doubleSided=!1,this.unlit=!1}C.DEFAULT_BASE_COLOR_FACTOR=d.Cartesian4.ONE,C.DEFAULT_METALLIC_FACTOR=1,C.DEFAULT_ROUGHNESS_FACTOR=1,P.DEFAULT_DIFFUSE_FACTOR=d.Cartesian4.ONE,P.DEFAULT_SPECULAR_FACTOR=c.Cartesian3.ONE,P.DEFAULT_GLOSSINESS_FACTOR=1,L.DEFAULT_EMISSIVE_FACTOR=c.Cartesian3.ZERO,S.Quantization=function(){this.octEncoded=!1,this.octEncodedZXY=!1,this.normalizationRange=void 0,this.quantizedVolumeOffset=void 0,this.quantizedVolumeDimensions=void 0,this.quantizedVolumeStepSize=void 0,this.componentDatatype=void 0,this.type=void 0},S.Attribute=function(){this.name=void 0,this.semantic=void 0,this.setIndex=void 0,this.componentDatatype=void 0,this.type=void 0,this.normalized=!1,this.count=void 0,this.min=void 0,this.max=void 0,this.constant=void 0,this.quantization=void 0,this.typedArray=void 0,this.buffer=void 0,this.byteOffset=0,this.byteStride=void 0},S.Indices=function(){this.indexDatatype=void 0,this.count=void 0,this.buffer=void 0,this.typedArray=void 0},S.FeatureIdAttribute=function(){this.featureCount=void 0,this.nullFeatureId=void 0,this.propertyTableId=void 0,this.setIndex=void 0,this.label=void 0,this.positionalLabel=void 0},S.FeatureIdTexture=function(){this.featureCount=void 0,this.nullFeatureId=void 0,this.propertyTableId=void 0,this.textureReader=void 0,this.label=void 0,this.positionalLabel=void 0},S.FeatureIdImplicitRange=function(){this.featureCount=void 0,this.nullFeatureId=void 0,this.propertyTableId=void 0,this.offset=0,this.repeat=void 0,this.label=void 0,this.positionalLabel=void 0},S.MorphTarget=function(){this.attributes=[]},S.Primitive=function(){this.attributes=[],this.morphTargets=[],this.indices=void 0,this.material=void 0,this.primitiveType=void 0,this.featureIds=[],this.propertyTextureIds=[],this.propertyAttributeIds=[],this.outlineCoordinates=void 0},S.Instances=function(){this.attributes=[],this.featureIds=[],this.transformInWorldSpace=!1},S.Skin=function(){this.index=void 0,this.joints=[],this.inverseBindMatrices=[]},S.Node=function(){this.name=void 0,this.index=void 0,this.children=[],this.primitives=[],this.instances=void 0,this.skin=void 0,this.matrix=void 0,this.translation=void 0,this.rotation=void 0,this.scale=void 0,this.morphWeights=[],this.articulationName=void 0},S.Scene=function(){this.nodes=[]},S.AnimatedPropertyType=Object.freeze({TRANSLATION:"translation",ROTATION:"rotation",SCALE:"scale",WEIGHTS:"weights"}),S.AnimationSampler=function(){this.input=[],this.interpolation=void 0,this.output=[]},S.AnimationTarget=function(){this.node=void 0,this.path=void 0},S.AnimationChannel=function(){this.sampler=void 0,this.target=void 0},S.Animation=function(){this.name=void 0,this.samplers=[],this.channels=[]},S.ArticulationStage=function(){this.name=void 0,this.type=void 0,this.minimumValue=void 0,this.maximumValue=void 0,this.initialValue=void 0},S.Articulation=function(){this.name=void 0,this.stages=[]},S.Asset=k,S.Components=function(){this.asset=new k,this.scene=void 0,this.nodes=[],this.skins=[],this.animations=[],this.articulations=[],this.structuralMetadata=void 0,this.upAxis=void 0,this.forwardAxis=void 0,this.transform=d.Matrix4.clone(d.Matrix4.IDENTITY)},S.TextureReader=function(){this.texture=void 0,this.index=void 0,this.texCoord=0,this.transform=d.Matrix3.clone(d.Matrix3.IDENTITY),this.channels=void 0},S.MetallicRoughness=C,S.SpecularGlossiness=P,S.Material=L;const z=S.Quantization,x={};x.quantizeAttribute=function(t){const e=t.componentsPerAttribute,n={componentDatatype:i.ComponentDatatype.UNSIGNED_SHORT,componentsPerAttribute:e,values:new Uint16Array(t.values.length),quantization:void 0},o=t.max,a=function(t,e,n){const o=t.componentsPerAttribute,a=s.AttributeType.fromNumberOfComponents(o),r=new Array(o),u=new Array(o),c=new Array(o),d=new Array(o);for(let t=0;t<o;t++)c[t]=65535,r[t]=e[t],u[t]=n[t]-e[t],d[t]=15259021896696422e-21*u[t];const f=new z;return f.componentDatatype=i.ComponentDatatype.UNSIGNED_SHORT,f.normalizationRange=c,f.octEncoded=!1,f.octEncodedZXY=!0,f.quantizedVolumeDimensions=u,f.quantizedVolumeOffset=r,f.quantizedVolumeStepSize=d,f.type=a,f}(t,t.min,o),r=a.quantizedVolumeStepSize,u=new Array(e);for(let t=0;t<e;t++)u[t]=1/r[t];const c=a.quantizedVolumeOffset;return function(t,e,n,i,o){const a=Math.floor;for(let r=0,s=t.length;r<s;r+=n)for(let s=0;s<n;s++)e[r+s]=a((t[r+s]-i[s])*o[s])}(t.values,n.values,e,c,u),n.quantization=a,n},x.octEncodeAttribute=function(t){const e={componentDatatype:i.ComponentDatatype.UNSIGNED_SHORT,componentsPerAttribute:2,values:new Uint16Array(t.values.length/3*2),quantization:void 0};!function(t,e){const n=t.length;let i,o,a,r,s,c,d=-1;const f=Math.abs,p=Math.round,l=u.CesiumMath.clamp;let m,h;for(let u=0;u<n;u+=3)d++,i=t[u],o=t[u+1],a=t[u+2],r=i/(f(i)+f(o)+f(a)),s=o/(f(i)+f(o)+f(a)),a<0&&(r=(1-f(s))*(r>=0?1:-1),s=(1-f(r))*(s>=0?1:-1),c=1-f(r)-f(s),c>0&&(c+=.001,r+=r>0?c/2:-c/2,s+=s>0?c/2:-c/2)),m=p(65535*(.5*l(r,-1,1)+.5)),h=p(65535*(.5*l(s,-1,1)+.5)),e[2*d]=m,e[2*d+1]=h}(t.values,e.values);const n=new z;return n.componentDatatype=i.ComponentDatatype.UNSIGNED_SHORT,n.normalizationRange=65535,n.octEncoded=!0,n.octEncodedZXY=!1,n.type=s.AttributeType.VEC2,e.quantization=n,e},x.compressAttributes=function(t){const e={};let n=x.quantizeAttribute(t.position);return e.position=n,n=x.octEncodeAttribute(t.normal),e.normal=n,r.defined(t.st)&&(n=x.quantizeAttribute(t.st),e.st=n),e.featureId={componentDatatype:t.featureId.componentDatatype,componentsPerAttribute:t.featureId.componentsPerAttribute,values:t.featureId.values,quantization:void 0},e};var F=x;const R={};function N(t){return t.values.length/t.componentsPerAttribute}function w(t){return i.ComponentDatatype.getSizeInBytes(t.componentDatatype)*t.componentsPerAttribute}R.interleaveAttributes=function(t){const e=function(t){let e,n,o;const a=[];for(n in t)t.hasOwnProperty(n)&&r.defined(t[n])&&r.defined(t[n].values)&&(a.push(n),t[n].componentDatatype===i.ComponentDatatype.DOUBLE&&(t[n].componentDatatype=i.ComponentDatatype.FLOAT,t[n].values=i.ComponentDatatype.createTypedArray(i.ComponentDatatype.FLOAT,t[n].values)));let s;const u=a.length;if(u>0)for(s=N(t[a[0]]),e=1;e<u;++e){const n=N(t[a[e]]);if(n!==s)throw new f.RuntimeError(`Each attribute list must have the same number of vertices.  Attribute ${a[e]} has a different number of vertices (${n.toString()}) than attribute ${a[0]} (${s.toString()}).`)}a.sort((function(e,n){return i.ComponentDatatype.getSizeInBytes(t[n].componentDatatype)-i.ComponentDatatype.getSizeInBytes(t[e].componentDatatype)}));let c=0;const d={};for(e=0;e<u;++e)n=a[e],o=t[n],d[n]=c,c+=w(o);if(c>0){const r=i.ComponentDatatype.getSizeInBytes(t[a[0]].componentDatatype),f=c%r;0!==f&&(c+=r-f);const p=new ArrayBuffer(s*c),l={};for(e=0;e<u;++e){n=a[e];const o=i.ComponentDatatype.getSizeInBytes(t[n].componentDatatype);l[n]={pointer:i.ComponentDatatype.createTypedArray(t[n].componentDatatype,p),index:d[n]/o,strideInComponentType:c/o}}for(e=0;e<s;++e)for(let i=0;i<u;++i){n=a[i],o=t[n];const r=o.values,s=l[n],u=s.pointer,c=o.componentsPerAttribute;for(let t=0;t<c;++t)u[s.index+t]=r[e*c+t];s.index+=s.strideInComponentType}return{buffer:p,offsetsInBytes:d,vertexSizeInBytes:c}}}(t);return e};var _=R;function V(){this.indexDatatype=void 0,this.count=void 0,this.buffer=void 0}function G(t,e,n){this.byteOffset=t,this.byteStride=e,this.buffer=n}function q(){this.componentDatatype=void 0,this.normalizationRange=void 0,this.octEncoded=void 0,this.octEncodedZXY=void 0,this.quantizedVolumeDimensions=void 0,this.quantizedVolumeOffset=void 0,this.quantizedVolumeStepSize=void 0,this.type=void 0}function M(t,e,n,i){for(let o=0;o<t;o++)e[i++]=n[o]}function U(){this.componentDatatype=void 0,this.componentsPerAttribute=void 0,this.count=void 0,this.values=void 0,this.max=void 0,this.min=void 0,this.interleave=void 0,this.quantization=void 0}function B(t,e,n,i){for(let o=0;o<t;o++)e[i++]=n[o]}function Y(t,e,n,i){for(let o=0;o<t;o++)n[o]=e[i++]}function X(){this.attributes=void 0,this.indices=void 0,this.primitiveType=void 0}V.fromGeometryIndices=function(t,e){const n=t;return(e=r.defaultValue(e,new V)).indexDatatype=2===n.BYTES_PER_ELEMENT?a.IndexDatatype.UNSIGNED_SHORT:a.IndexDatatype.UNSIGNED_INT,e.count=n.length,e.buffer=n.buffer,e},V.pack=function(t,e,n,i){if(!r.defined(e)){const i=V.getPackedLength(t);e=new Float32Array(i),n=0}r.defined(i)||(i=[]);let o=n;return e[o++]=t.indexDatatype,e[o++]=t.count,e[o++]=function(t,e){let n=t.indexOf(e);if(-1!==n)return n;return n=t.length,t.push(e),n}(i,t.buffer),{packedData:e,buffers:i,packedOffset:n}},V.unpack=function(t,e,n){const i=new V;let o=e;return i.indexDatatype=t[o++],i.count=t[o++],i.buffer=n[t[o++]],i},V.getPackedLength=function(t){return 3},G.pack=function(t,e,n,i){if(!r.defined(e)){const i=G.getPackedLength(t);e=new Float32Array(i),n=0}r.defined(i)||(i=[]);let o=n;return e[o++]=t.byteOffset,e[o++]=t.byteStride,e[o++]=function(t,e){let n=t.indexOf(e);if(-1!==n)return n;return n=t.length,t.push(e),n}(i,t.buffer),{packedData:e,buffers:i,packedOffset:n}},G.unpack=function(t,e,n){const i=new G;let o=e;return i.byteOffset=t[o++],i.byteStride=t[o++],i.buffer=n[t[o++]],i},G.getPackedLength=function(t){return 3},q.fromQuantization=function(t,e){return(e=r.defaultValue(e,new q)).componentDatatype=t.componentDatatype,e.normalizationRange=t.normalizationRange,e.octEncoded=t.octEncoded,e.octEncodedZXY=t.octEncodedZXY,e.quantizedVolumeDimensions=t.quantizedVolumeDimensions,e.quantizedVolumeOffset=t.quantizedVolumeOffset,e.quantizedVolumeStepSize=t.quantizedVolumeStepSize,e.type=t.type,e},q.pack=function(t,e,n){if(!r.defined(e)){const i=q.getPackedLength(t,t.octEncoded);e=new Float32Array(i),n=0}const i=s.AttributeType.getNumberOfComponents(t.type);let o=n;return e[o++]=t.octEncoded?1:0,e[o++]=i,e[o++]=t.octEncodedZXY?1:0,e[o++]=t.componentDatatype,t.octEncoded?(e[o]=t.normalizationRange,e):(M(i,e,t.normalizationRange,o),o+=i,M(i,e,t.quantizedVolumeDimensions,o),o+=i,M(i,e,t.quantizedVolumeOffset,o),o+=i,M(i,e,t.quantizedVolumeStepSize,o),e)},q.unpack=function(t,e){r.defined(e)||(e=0);const n=new q;let i=e;n.octEncoded=1===t[i++];const o=t[i++];if(n.type=s.AttributeType.fromNumberOfComponents(o),n.octEncodedZXY=1===t[i++],n.componentDatatype=t[i++],n.octEncoded)return n.normalizationRange=t[i],n;const a=s.AttributeType.getMathType(n.type);return n.normalizationRange=a.unpack(t,i),i+=a.packedLength,n.quantizedVolumeDimensions=a.unpack(t,i),i+=a.packedLength,n.quantizedVolumeOffset=a.unpack(t,i),i+=a.packedLength,n.quantizedVolumeStepSize=a.unpack(t,i),n},q.getPackedLength=function(t){if(t.octEncoded)return 5;let e=0;return e+=4*s.AttributeType.getNumberOfComponents(t.type)+4,e},U.fromGeometryAttribute=function(t,e){(e=r.defaultValue(e,new U)).componentsPerAttribute=t.componentsPerAttribute,e.componentDatatype=t.componentDatatype,e.count=t.values.length/t.componentsPerAttribute,e.values=t.values;const{max:n,min:i}=I.findAttributesMaxMin(t);return e.max=n,e.min=i,e},U.pack=function(t,e,n,i){if(!r.defined(e)){const i=U.getPackedLength(t);e=new Float32Array(i),n=0}r.defined(i)||(i=[]);let o=n;e[o++]=t.componentsPerAttribute,e[o++]=t.componentDatatype,e[o++]=t.count;const a=t.quantization;r.defined(a)?(e[o++]=1,q.pack(a,e,o,i),o+=q.getPackedLength(a)):e[o++]=0;const s=t.values;r.defined(s)?(e[o++]=1,e[o++]=function(t,e){let n=t.indexOf(e);if(-1!==n)return n;return n=t.length,t.push(e),n}(i,s.buffer)):e[o++]=0;const u=t.componentsPerAttribute,c=t.max;r.defined(c)?(e[o++]=1,B(u,e,c,o),o+=u):e[o++]=0;const d=t.min;r.defined(d)?(e[o++]=1,B(u,e,d,o),o+=u):e[o++]=0;const f=t.interleave;return r.defined(f)?(e[o++]=1,G.pack(f,e,o,i),o+=G.getPackedLength(f)):e[o++]=0,{packedData:e,buffers:i,packedOffset:n}},U.unpack=function(t,e,n){const o=new U;let a=e;if(o.componentsPerAttribute=t[a++],o.componentDatatype=t[a++],o.count=t[a++],1===t[a++]&&(o.quantization=q.unpack(t,a),a+=q.getPackedLength(o.quantization)),1===t[a++]){const e=n[t[a++]];let s,u;s=r.defined(o.quantization)?o.quantization.componentDatatype:o.componentDatatype,s===i.ComponentDatatype.UNSIGNED_SHORT&&(u=new Uint16Array(e)),s===i.ComponentDatatype.FLOAT&&(u=new Float32Array(e)),o.values=i.ComponentDatatype.createTypedArray(s,u)}const s=o.componentsPerAttribute;return 1===t[a++]&&(o.max=new Array(s),Y(s,t,o.max,a),a+=s),1===t[a++]&&(o.min=new Array(s),Y(s,t,o.min,a),a+=s),1===t[a++]&&(o.interleave=G.unpack(t,a,n),a+=G.getPackedLength(o.interleave)),o},U.getPackedLength=function(t){let e=0;e+=3;const n=t.quantization;e+=1,r.defined(n)&&(e+=q.getPackedLength(n));const i=t.values;e+=1,r.defined(i)&&(e+=1);const o=t.componentsPerAttribute,a=t.max;e+=1,r.defined(a)&&(e+=o);const s=t.min;e+=1,r.defined(s)&&(e+=o);const u=t.interleave;return e+=1,r.defined(u)&&(e+=G.getPackedLength(u)),e},X.prototype.setAttributeInterleave=function(t,e){const n=this.attributes;n[t].interleave=e,n[t].values=void 0},X.prototype.setAttributeQuantization=function(t,e){const n=this.attributes;n[t].values=e.values,n[t].quantization=e.quantization},X.fromGeometry=function(t,e){const n=r.defaultValue(e,new X),i={},o=t.attributes;for(const t in o)o.hasOwnProperty(t)&&r.defined(o[t])&&r.defined(o[t].values)&&(i[t]=U.fromGeometryAttribute(o[t],new U));return n.attributes=i,n.indices=V.fromGeometryIndices(t.indices),n.primitiveType=t.primitiveType,n},X.pack=function(t,e,n,i){if(!r.defined(e)){const i=X.getPackedLength(t);e=new Float32Array(i),n=0}r.defined(i)||(i=[]);let o=n;e[o++]=t.primitiveType;const a=t.indices;r.defined(a)?(e[o++]=1,V.pack(a,e,o,i),o+=V.getPackedLength(a)):e[o++]=0;const s=t.attributes;return r.defined(s.position)?(e[o++]=1,U.pack(s.position,e,o,i),o+=U.getPackedLength(s.position)):e[o++]=0,r.defined(s.normal)?(e[o++]=1,U.pack(s.normal,e,o,i),o+=U.getPackedLength(s.normal)):e[o++]=0,r.defined(s.st)?(e[o++]=1,U.pack(s.st,e,o,i),o+=U.getPackedLength(s.st)):e[o++]=0,r.defined(s.featureId)?(e[o++]=1,U.pack(s.featureId,e,o,i),o+=U.getPackedLength(s.featureId)):e[o++]=0,{packedData:e,buffers:i,packedOffset:n}},X.unpack=function(t,e,n){const i=new X;let o=e;i.primitiveType=t[o++],1===t[o++]&&(i.indices=V.unpack(t,o,n),o+=V.getPackedLength(i.indices));const a={};return 1===t[o++]&&(a.position=U.unpack(t,o,n),o+=U.getPackedLength(a.position)),1===t[o++]&&(a.normal=U.unpack(t,o,n),o+=U.getPackedLength(a.normal)),1===t[o++]&&(a.st=U.unpack(t,o,n),o+=U.getPackedLength(a.st)),1===t[o++]&&(a.featureId=U.unpack(t,o,n),o+=U.getPackedLength(a.featureId)),i.attributes=a,i},X.getPackedLength=function(t){let e=0;e+=1;const n=t.indices;e+=1,r.defined(n)&&(e+=V.getPackedLength(n)),e+=4;const i=t.attributes;for(const t in i)i.hasOwnProperty(t)&&r.defined(i[t])&&(e+=U.getPackedLength(i[t]));return e};const Z={positions:void 0,normals:void 0,indices:void 0};function H(t,e,i){const o=function(t,e){let i=0,o=0;t.forEach((function(t){const e=t.geometry;i+=n.Geometry.computeNumberOfVertices(e),o+=e.indices.length}));const a=I.allocateGeometry(i,o,e),r=function(t,e){let n,i,o,a,r,s,u,c,d,f,p,l,m,h,y,v;const b=e.attributes,A=e.indices,g=b.position.values,T=b.normal.values,D=b.featureId.values,I=b.st?b.st.values:void 0;let O=-1,E=-1;for(let e=0,b=t.length;e<b;e++){const b={},S=t[e],k=S.matrix,C=S.featureId,P=S.geometry,L=P.indices,z=P.attributes,x=z.position.values,F=z.normal.values,R=I?z.st.values:void 0;let N,w,_,V,G,q,M,U;n=k[0],i=k[1],o=k[2],a=k[3],r=k[4],s=k[5],u=k[6],c=k[7],d=k[8],f=k[9],p=k[10],l=k[11],m=k[12],h=k[13],y=k[14],v=k[15];for(let t=0,e=L.length;t<e;t++)U=L[t],void 0===b[U]&&(b[U]=++O,N=x[3*U],w=x[3*U+1],_=x[3*U+2],V=N*n+w*i+_*o+a,G=N*r+w*s+_*u+c,q=N*d+w*f+_*p+l,M=N*m+w*h+_*y+v,g[3*O]=V/M,g[3*O+1]=G/M,g[3*O+2]=q/M,N=F[3*U],w=F[3*U+1],_=F[3*U+2],V=N*n+w*i+_*o,G=N*r+w*s+_*u,q=N*d+w*f+_*p,T[3*O]=V,T[3*O+1]=G,T[3*O+2]=q,I&&(I[2*O]=R[2*U],I[2*O+1]=R[2*U+1]),D[O]=C),A[++E]=b[U]}return e}(t,a);return r}(t,e);i.curveXYZ&&function(t){const e=6372421.8877179315;let n,i;const o=t.length;let a,r,s,u,c,d;for(let f=0;f<o;f+=3)a=t[f],r=t[f+1],0===a&&0===r||(s=t[f+2],n=Math.sqrt(a*a+r*r),i=n/e,u=a*(e+s)*Math.sin(i)/n,c=r*(e+s)*Math.sin(i)/n,d=Math.sqrt((s+e)*(s+e)-u*u-c*c)-e,t[f]=u,t[f+1]=c,t[f+2]=d)}(o.attributes.position.values);const a=X.fromGeometry(o);let s;if(i.compressVertices){Z.positions=a.attributes.position.values.buffer,Z.normals=a.attributes.normal.values.buffer,Z.indices=a.indices.buffer,s=F.compressAttributes(a.attributes);for(const t in s)s.hasOwnProperty(t)&&r.defined(s[t])&&a.setAttributeQuantization(t,s[t])}if(i.useInterleave){const t=_.interleaveAttributes(r.defaultValue(s,a.attributes)),e=t.offsetsInBytes;for(const n in e)if(e.hasOwnProperty(n)&&r.defined(e[n])){const i=new G(e[n],t.vertexSizeInBytes,t.buffer);a.setAttributeInterleave(n,i)}}return a}return t((function(t,i){let o;return t.taskType===O.SLICE_OGL&&(o=function(t,n){const i=e.OglParser.spliceBuffer(t);let o;try{o=function(t){const n=[];let i;return t.forEach((function(t){i=e.OglParser.parseGeometry(t),i=I.optimizeGeometryVertexCache(i),n.push(e.OglParser.writeOglBuffer(i))})),n}(i),i.length=0}catch(t){o=i}return n.push.apply(n,o),o}(t.buffer,i)),t.taskType===O.OPTIMIZE&&(o=function(t,i){const o=t.buffers,a=function(t,n){const i=t,o=new Array(i[0]);let a=0,r=1;for(;r<i.length;){const t=n[i[r]],s=i[r+1];r+=2;const u=new Float32Array(i.buffer,4*r,16);r+=16,o[a++]={geometry:e.OglParser.parseGeometry(t),featureId:s,matrix:u}}return o}(t.instances,o),r=function(t){const e=[];let i,o,a=[],r=0;for(let s=0,c=t.length;s<c;s++)i=t[s],o=n.Geometry.computeNumberOfVertices(i.geometry),o>u.CesiumMath.SIXTY_FOUR_KILOBYTES?e.push([i]):(r+o>u.CesiumMath.SIXTY_FOUR_KILOBYTES&&(e.push(a),a=[],r=0),a.push(i),r+=o);0!==a.length&&e.push(a);return e}(a),s={curveXYZ:!0,compressVertices:!0,useInterleave:!1},c=[],d=t.hasTexture;for(let t=0,e=r.length;t<e;t++){const e=H(r[t],d,s);c.push(e)}const f=function(t){const e=t;let n=1;const i=e.length;for(let t=0;t<i;t++){const i=e[t];n+=X.getPackedLength(i)}let o=0;const a=new Float32Array(n),r=[];a[o++]=i;for(let t=0;t<i;t++)X.pack(e[t],a,o,r),o+=X.getPackedLength(e[t]);return{packedData:a,buffers:r}}(c),p=f.buffers;i.push.apply(i,p),i.push.apply(i,o);return{packedData:f.packedData,packedBuffers:p,buffers:o,positions:Z.positions,normals:Z.normals,indices:Z.indices}}(t,i)),o}))}));
