/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./arrayRemoveDuplicates-5b666c82","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./PolylineVolumeGeometryLibrary-fad92191","./CorridorGeometryLibrary-e676cbda","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./VertexFormat-86c096b8","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./WebGLConstants-433debbf","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db"],(function(t,e,r,a,i,o,n,s,l,d,u,c,m,f,y,p,g,h,b,C,A,_,w,v,T,G){"use strict";const E=new r.Cartesian3,V=new r.Cartesian3,L=new r.Cartesian3,x=new r.Cartesian3,P=new r.Cartesian3,F=new r.Cartesian3,N=new r.Cartesian3,D=new r.Cartesian3;function M(t,e){for(let r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function O(t,e,a,i,o,s){const l=t.normals,d=t.tangents,u=t.bitangents,c=r.Cartesian3.normalize(r.Cartesian3.cross(a,e,N),N);s.normal&&n.CorridorGeometryLibrary.addAttribute(l,e,i,o),s.tangent&&n.CorridorGeometryLibrary.addAttribute(d,c,i,o),s.bitangent&&n.CorridorGeometryLibrary.addAttribute(u,a,i,o)}function I(t,e,a){const o=t.positions,u=t.corners,f=t.endPositions,y=t.lefts,p=t.normals,g=new d.GeometryAttributes;let h,b,C,A=0,_=0,w=0;for(b=0;b<o.length;b+=2)C=o[b].length-3,A+=C,w+=2*C,_+=o[b+1].length-3;for(A+=3,_+=3,b=0;b<u.length;b++){h=u[b];const t=u[b].leftPositions;s.defined(t)?(C=t.length,A+=C,w+=C):(C=u[b].rightPositions.length,_+=C,w+=C)}const v=s.defined(f);let T;v&&(T=f[0].length-3,A+=T,_+=T,T/=3,w+=6*T);const G=A+_,P=new Float64Array(G),M={normals:e.normal?new Float32Array(G):void 0,tangents:e.tangent?new Float32Array(G):void 0,bitangents:e.bitangent?new Float32Array(G):void 0};let I,S,R,k,H,z,B=0,U=G-1,Y=E,W=V;const q=T/2,J=c.IndexDatatype.createTypedArray(G/3,w);let j=0;if(v){z=L,H=x;const t=f[0];for(Y=r.Cartesian3.fromArray(p,0,Y),W=r.Cartesian3.fromArray(y,0,W),b=0;b<q;b++)z=r.Cartesian3.fromArray(t,3*(q-1-b),z),H=r.Cartesian3.fromArray(t,3*(q+b),H),n.CorridorGeometryLibrary.addAttribute(P,H,B),n.CorridorGeometryLibrary.addAttribute(P,z,void 0,U),O(M,Y,W,B,U,e),S=B/3,k=S+1,I=(U-2)/3,R=I-1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3}let K,Q,X=0,Z=0,$=o[X++],tt=o[X++];for(P.set($,B),P.set(tt,U-tt.length+1),W=r.Cartesian3.fromArray(y,Z,W),C=tt.length-3,b=0;b<C;b+=3)K=a.geodeticSurfaceNormal(r.Cartesian3.fromArray($,b,N),N),Q=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,C-b,D),D),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),O(M,Y,W,B,U,e),S=B/3,k=S+1,I=(U-2)/3,R=I-1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3;for(K=a.geodeticSurfaceNormal(r.Cartesian3.fromArray($,C,N),N),Q=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,C,D),D),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),Z+=3,b=0;b<u.length;b++){let t;h=u[b];const i=h.leftPositions,l=h.rightPositions;let d,c,m=F,f=L,g=x;if(Y=r.Cartesian3.fromArray(p,Z,Y),s.defined(i)){for(O(M,Y,W,void 0,U,e),U-=3,d=k,c=R,t=0;t<i.length/3;t++)m=r.Cartesian3.fromArray(i,3*t,m),J[j++]=d,J[j++]=c-t-1,J[j++]=c-t,n.CorridorGeometryLibrary.addAttribute(P,m,void 0,U),f=r.Cartesian3.fromArray(P,3*(c-t-1),f),g=r.Cartesian3.fromArray(P,3*d,g),W=r.Cartesian3.normalize(r.Cartesian3.subtract(f,g,W),W),O(M,Y,W,void 0,U,e),U-=3;m=r.Cartesian3.fromArray(P,3*d,m),f=r.Cartesian3.subtract(r.Cartesian3.fromArray(P,3*c,f),m,f),g=r.Cartesian3.subtract(r.Cartesian3.fromArray(P,3*(c-t),g),m,g),W=r.Cartesian3.normalize(r.Cartesian3.add(f,g,W),W),O(M,Y,W,B,void 0,e),B+=3}else{for(O(M,Y,W,B,void 0,e),B+=3,d=R,c=k,t=0;t<l.length/3;t++)m=r.Cartesian3.fromArray(l,3*t,m),J[j++]=d,J[j++]=c+t,J[j++]=c+t+1,n.CorridorGeometryLibrary.addAttribute(P,m,B),f=r.Cartesian3.fromArray(P,3*d,f),g=r.Cartesian3.fromArray(P,3*(c+t),g),W=r.Cartesian3.normalize(r.Cartesian3.subtract(f,g,W),W),O(M,Y,W,B,void 0,e),B+=3;m=r.Cartesian3.fromArray(P,3*d,m),f=r.Cartesian3.subtract(r.Cartesian3.fromArray(P,3*(c+t),f),m,f),g=r.Cartesian3.subtract(r.Cartesian3.fromArray(P,3*c,g),m,g),W=r.Cartesian3.normalize(r.Cartesian3.negate(r.Cartesian3.add(g,f,W),W),W),O(M,Y,W,void 0,U,e),U-=3}for($=o[X++],tt=o[X++],$.splice(0,3),tt.splice(tt.length-3,3),P.set($,B),P.set(tt,U-tt.length+1),C=tt.length-3,Z+=3,W=r.Cartesian3.fromArray(y,Z,W),t=0;t<tt.length;t+=3)K=a.geodeticSurfaceNormal(r.Cartesian3.fromArray($,t,N),N),Q=a.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,C-t,D),D),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),O(M,Y,W,B,U,e),k=B/3,S=k-1,R=(U-2)/3,I=R+1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3;B-=3,U+=3}if(Y=r.Cartesian3.fromArray(p,p.length-3,Y),O(M,Y,W,B,U,e),v){B+=3,U-=3,z=L,H=x;const t=f[1];for(b=0;b<q;b++)z=r.Cartesian3.fromArray(t,3*(T-b-1),z),H=r.Cartesian3.fromArray(t,3*b,H),n.CorridorGeometryLibrary.addAttribute(P,z,void 0,U),n.CorridorGeometryLibrary.addAttribute(P,H,B),O(M,Y,W,B,U,e),k=B/3,S=k-1,R=(U-2)/3,I=R+1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3}if(g.position=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:P}),e.st){const t=new Float32Array(G/3*2);let e,r,a=0;if(v){A/=3,_/=3;const i=Math.PI/(T+1);let o;r=1/(A-T+1),e=1/(_-T+1);const n=T/2;for(b=n+1;b<T+1;b++)o=m.CesiumMath.PI_OVER_TWO+i*b,t[a++]=e*(1+Math.cos(o)),t[a++]=.5*(1+Math.sin(o));for(b=1;b<_-T+1;b++)t[a++]=b*e,t[a++]=0;for(b=T;b>n;b--)o=m.CesiumMath.PI_OVER_TWO-b*i,t[a++]=1-e*(1+Math.cos(o)),t[a++]=.5*(1+Math.sin(o));for(b=n;b>0;b--)o=m.CesiumMath.PI_OVER_TWO-i*b,t[a++]=1-r*(1+Math.cos(o)),t[a++]=.5*(1+Math.sin(o));for(b=A-T;b>0;b--)t[a++]=b*r,t[a++]=1;for(b=1;b<n+1;b++)o=m.CesiumMath.PI_OVER_TWO+i*b,t[a++]=r*(1+Math.cos(o)),t[a++]=.5*(1+Math.sin(o))}else{for(A/=3,_/=3,r=1/(A-1),e=1/(_-1),b=0;b<_;b++)t[a++]=b*e,t[a++]=0;for(b=A;b>0;b--)t[a++]=(b-1)*r,t[a++]=1}g.st=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:t})}return e.normal&&(g.normal=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:M.normals})),e.tangent&&(g.tangent=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:M.tangents})),e.bitangent&&(g.bitangent=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:M.bitangents})),{attributes:g,indices:J}}function S(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(let a=3;a<t.length;a+=3){const i=t[a],o=t[a+1],n=t[a+2];r[e++]=i,r[e++]=o,r[e++]=n,r[e++]=i,r[e++]=o,r[e++]=n}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function R(t,e){const a=new y.VertexFormat({position:e.position,normal:e.normal||e.bitangent||t.shadowVolume,tangent:e.tangent,bitangent:e.normal||e.bitangent,st:e.st}),o=t.ellipsoid,d=I(n.CorridorGeometryLibrary.computePositions(t),a,o),m=t.height,p=t.extrudedHeight;let g=d.attributes;const h=d.indices;let b=g.position.values,C=b.length;const A=new Float64Array(6*C);let _=new Float64Array(C);_.set(b);let w,v=new Float64Array(4*C);b=f.PolygonPipeline.scaleToGeodeticHeight(b,m,o),v=S(b,0,v),_=f.PolygonPipeline.scaleToGeodeticHeight(_,p,o),v=S(_,2*C,v),A.set(b),A.set(_,C),A.set(v,2*C),g.position.values=A,g=function(t,e){if(!(e.normal||e.tangent||e.bitangent||e.st))return t;const a=t.position.values;let i,o;(e.normal||e.bitangent)&&(i=t.normal.values,o=t.bitangent.values);const s=t.position.values.length/18,l=3*s,d=2*s,u=2*l;let c;if(e.normal||e.bitangent||e.tangent){const s=e.normal?new Float32Array(6*l):void 0,d=e.tangent?new Float32Array(6*l):void 0,m=e.bitangent?new Float32Array(6*l):void 0;let f=E,y=V,p=L,g=x,h=P,b=F,C=u;for(c=0;c<l;c+=3){const t=C+u;f=r.Cartesian3.fromArray(a,c,f),y=r.Cartesian3.fromArray(a,c+l,y),p=r.Cartesian3.fromArray(a,(c+3)%l,p),y=r.Cartesian3.subtract(y,f,y),p=r.Cartesian3.subtract(p,f,p),g=r.Cartesian3.normalize(r.Cartesian3.cross(y,p,g),g),e.normal&&(n.CorridorGeometryLibrary.addAttribute(s,g,t),n.CorridorGeometryLibrary.addAttribute(s,g,t+3),n.CorridorGeometryLibrary.addAttribute(s,g,C),n.CorridorGeometryLibrary.addAttribute(s,g,C+3)),(e.tangent||e.bitangent)&&(b=r.Cartesian3.fromArray(i,c,b),e.bitangent&&(n.CorridorGeometryLibrary.addAttribute(m,b,t),n.CorridorGeometryLibrary.addAttribute(m,b,t+3),n.CorridorGeometryLibrary.addAttribute(m,b,C),n.CorridorGeometryLibrary.addAttribute(m,b,C+3)),e.tangent&&(h=r.Cartesian3.normalize(r.Cartesian3.cross(b,g,h),h),n.CorridorGeometryLibrary.addAttribute(d,h,t),n.CorridorGeometryLibrary.addAttribute(d,h,t+3),n.CorridorGeometryLibrary.addAttribute(d,h,C),n.CorridorGeometryLibrary.addAttribute(d,h,C+3))),C+=6}if(e.normal){for(s.set(i),c=0;c<l;c+=3)s[c+l]=-i[c],s[c+l+1]=-i[c+1],s[c+l+2]=-i[c+2];t.normal.values=s}else t.normal=void 0;if(e.bitangent?(m.set(o),m.set(o,l),t.bitangent.values=m):t.bitangent=void 0,e.tangent){const e=t.tangent.values;d.set(e),d.set(e,l),t.tangent.values=d}}if(e.st){const e=t.st.values,r=new Float32Array(6*d);r.set(e),r.set(e,d);let a=2*d;for(let t=0;t<2;t++){for(r[a++]=e[0],r[a++]=e[1],c=2;c<d;c+=2){const t=e[c],i=e[c+1];r[a++]=t,r[a++]=i,r[a++]=t,r[a++]=i}r[a++]=e[0],r[a++]=e[1]}t.st.values=r}return t}(g,e);const T=C/3;if(t.shadowVolume){const t=g.normal.values;C=t.length;let r=new Float32Array(6*C);for(w=0;w<C;w++)t[w]=-t[w];r.set(t,C),r=S(t,4*C,r),g.extrudeDirection=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:r}),e.normal||(g.normal=void 0)}if(s.defined(t.offsetAttribute)){let e=new Uint8Array(6*T);if(t.offsetAttribute===u.GeometryOffsetAttribute.TOP)e=e.fill(1,0,T).fill(1,2*T,4*T);else{const r=t.offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;e=e.fill(r)}g.applyOffset=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}const G=h.length,N=T+T,D=c.IndexDatatype.createTypedArray(A.length/3,2*G+3*N);D.set(h);let M,O,R,k,H=G;for(w=0;w<G;w+=3){const t=h[w],e=h[w+1],r=h[w+2];D[H++]=r+T,D[H++]=e+T,D[H++]=t+T}for(w=0;w<N;w+=2)M=w+N,O=M+N,R=M+1,k=O+1,D[H++]=M,D[H++]=O,D[H++]=R,D[H++]=R,D[H++]=O,D[H++]=k;return{attributes:g,indices:D}}const k=new r.Cartesian3,H=new r.Cartesian3,z=new a.Cartographic;function B(t,e,a,i,o,n){const s=r.Cartesian3.subtract(e,t,k);r.Cartesian3.normalize(s,s);const l=a.geodeticSurfaceNormal(t,H),d=r.Cartesian3.cross(s,l,k);r.Cartesian3.multiplyByScalar(d,i,d);let u=o.latitude,c=o.longitude,m=n.latitude,f=n.longitude;r.Cartesian3.add(t,d,H),a.cartesianToCartographic(H,z);let y=z.latitude,p=z.longitude;u=Math.min(u,y),c=Math.min(c,p),m=Math.max(m,y),f=Math.max(f,p),r.Cartesian3.subtract(t,d,H),a.cartesianToCartographic(H,z),y=z.latitude,p=z.longitude,u=Math.min(u,y),c=Math.min(c,p),m=Math.max(m,y),f=Math.max(f,p),o.latitude=u,o.longitude=c,n.latitude=m,n.longitude=f}const U=new r.Cartesian3,Y=new r.Cartesian3,W=new a.Cartographic,q=new a.Cartographic;function J(e,i,n,l,d){e=M(e,i);const u=t.arrayRemoveDuplicates(e,r.Cartesian3.equalsEpsilon),c=u.length;if(c<2||n<=0)return new a.Rectangle;const m=.5*n;let f,y;if(W.latitude=Number.POSITIVE_INFINITY,W.longitude=Number.POSITIVE_INFINITY,q.latitude=Number.NEGATIVE_INFINITY,q.longitude=Number.NEGATIVE_INFINITY,l===o.CornerType.ROUNDED){const t=u[0];r.Cartesian3.subtract(t,u[1],U),r.Cartesian3.normalize(U,U),r.Cartesian3.multiplyByScalar(U,m,U),r.Cartesian3.add(t,U,Y),i.cartesianToCartographic(Y,z),f=z.latitude,y=z.longitude,W.latitude=Math.min(W.latitude,f),W.longitude=Math.min(W.longitude,y),q.latitude=Math.max(q.latitude,f),q.longitude=Math.max(q.longitude,y)}for(let t=0;t<c-1;++t)B(u[t],u[t+1],i,m,W,q);const p=u[c-1];r.Cartesian3.subtract(p,u[c-2],U),r.Cartesian3.normalize(U,U),r.Cartesian3.multiplyByScalar(U,m,U),r.Cartesian3.add(p,U,Y),B(p,Y,i,m,W,q),l===o.CornerType.ROUNDED&&(i.cartesianToCartographic(Y,z),f=z.latitude,y=z.longitude,W.latitude=Math.min(W.latitude,f),W.longitude=Math.min(W.longitude,y),q.latitude=Math.max(q.latitude,f),q.longitude=Math.max(q.longitude,y));const g=s.defined(d)?d:new a.Rectangle;return g.north=q.latitude,g.south=W.latitude,g.east=q.longitude,g.west=W.longitude,g}function j(t){const e=(t=s.defaultValue(t,s.defaultValue.EMPTY_OBJECT)).positions,i=t.width,n=s.defaultValue(t.height,0),l=s.defaultValue(t.extrudedHeight,n);this._positions=e,this._ellipsoid=a.Ellipsoid.clone(s.defaultValue(t.ellipsoid,a.Ellipsoid.WGS84)),this._vertexFormat=y.VertexFormat.clone(s.defaultValue(t.vertexFormat,y.VertexFormat.DEFAULT)),this._width=i,this._height=Math.max(n,l),this._extrudedHeight=Math.min(n,l),this._cornerType=s.defaultValue(t.cornerType,o.CornerType.ROUNDED),this._granularity=s.defaultValue(t.granularity,m.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=s.defaultValue(t.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*r.Cartesian3.packedLength+a.Ellipsoid.packedLength+y.VertexFormat.packedLength+7}j.pack=function(t,e,i){i=s.defaultValue(i,0);const o=t._positions,n=o.length;e[i++]=n;for(let t=0;t<n;++t,i+=r.Cartesian3.packedLength)r.Cartesian3.pack(o[t],e,i);return a.Ellipsoid.pack(t._ellipsoid,e,i),i+=a.Ellipsoid.packedLength,y.VertexFormat.pack(t._vertexFormat,e,i),i+=y.VertexFormat.packedLength,e[i++]=t._width,e[i++]=t._height,e[i++]=t._extrudedHeight,e[i++]=t._cornerType,e[i++]=t._granularity,e[i++]=t._shadowVolume?1:0,e[i]=s.defaultValue(t._offsetAttribute,-1),e};const K=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),Q=new y.VertexFormat,X={positions:void 0,ellipsoid:K,vertexFormat:Q,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};return j.unpack=function(t,e,i){e=s.defaultValue(e,0);const o=t[e++],n=new Array(o);for(let a=0;a<o;++a,e+=r.Cartesian3.packedLength)n[a]=r.Cartesian3.unpack(t,e);const l=a.Ellipsoid.unpack(t,e,K);e+=a.Ellipsoid.packedLength;const d=y.VertexFormat.unpack(t,e,Q);e+=y.VertexFormat.packedLength;const u=t[e++],c=t[e++],m=t[e++],f=t[e++],p=t[e++],g=1===t[e++],h=t[e];return s.defined(i)?(i._positions=n,i._ellipsoid=a.Ellipsoid.clone(l,i._ellipsoid),i._vertexFormat=y.VertexFormat.clone(d,i._vertexFormat),i._width=u,i._height=c,i._extrudedHeight=m,i._cornerType=f,i._granularity=p,i._shadowVolume=g,i._offsetAttribute=-1===h?void 0:h,i):(X.positions=n,X.width=u,X.height=c,X.extrudedHeight=m,X.cornerType=f,X.granularity=p,X.shadowVolume=g,X.offsetAttribute=-1===h?void 0:h,new j(X))},j.computeRectangle=function(t,e){const r=(t=s.defaultValue(t,s.defaultValue.EMPTY_OBJECT)).positions,i=t.width;return J(r,s.defaultValue(t.ellipsoid,a.Ellipsoid.WGS84),i,s.defaultValue(t.cornerType,o.CornerType.ROUNDED),e)},j.createGeometry=function(a){let o=a._positions;const d=a._width,c=a._ellipsoid;o=M(o,c);const y=t.arrayRemoveDuplicates(o,r.Cartesian3.equalsEpsilon);if(y.length<2||d<=0)return;const p=a._height,g=a._extrudedHeight,h=!m.CesiumMath.equalsEpsilon(p,g,0,m.CesiumMath.EPSILON2),b=a._vertexFormat,C={ellipsoid:c,positions:y,width:d,cornerType:a._cornerType,granularity:a._granularity,saveAttributes:!0};let A;if(h)C.height=p,C.extrudedHeight=g,C.shadowVolume=a._shadowVolume,C.offsetAttribute=a._offsetAttribute,A=R(C,b);else{if(A=I(n.CorridorGeometryLibrary.computePositions(C),b,c),A.attributes.position.values=f.PolygonPipeline.scaleToGeodeticHeight(A.attributes.position.values,p,c),s.defined(a._offsetAttribute)){const t=a._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,e=A.attributes.position.values.length,r=new Uint8Array(e/3).fill(t);A.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:r})}}const _=A.attributes,w=e.BoundingSphere.fromVertices(_.position.values,void 0,3);return b.position||(A.attributes.position.values=void 0),new l.Geometry({attributes:_,indices:A.indices,primitiveType:l.PrimitiveType.TRIANGLES,boundingSphere:w,offsetAttribute:a._offsetAttribute})},j.createShadowVolume=function(t,e,r){const a=t._granularity,i=t._ellipsoid,o=e(a,i),n=r(a,i);return new j({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:o,height:n,vertexFormat:y.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(j.prototype,{rectangle:{get:function(){return s.defined(this._rectangle)||(this._rectangle=J(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),function(t,e){return s.defined(e)&&(t=j.unpack(t,e)),t._ellipsoid=a.Ellipsoid.clone(t._ellipsoid),j.createGeometry(t)}}));
