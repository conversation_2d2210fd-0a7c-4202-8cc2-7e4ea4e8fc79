<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-11 15:47:15
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-24 14:07:18
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\Components\HomePage\Components\Perception\Components\Monitor\index.vue
 * @Description:
 *
-->
<template>
  <div class="perception-container">
    <div class="content">
      <SubTitle :tabs="['健康监测', '视频监测']" @tab-change="changeTab($event)"></SubTitle>
      <div class="content flex items-center justify-center">
        <StructureMonitor v-if="selectedTabId === 0" :type="selectedTabName"></StructureMonitor>
        <VideoMonitor v-if="selectedTabId === 1"></VideoMonitor>
      </div>
    </div>
  </div>
</template>

<script setup>
  import StructureMonitor from './Components/structureMonitor.vue'
  import SubTitle from '@/components/SubTitle/index.vue'
  import VideoMonitor from './Components/VideoMonitor/index.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  defineOptions({
    name: 'Monitor'
  })

  const selectedTabId = ref(0)

  const changeTab = (val) => {
    selectedTabId.value = val
  }
  // const selectedTabName = computed(() => {
  //   return btns.value.find((item) => item.selected).name
  // })
</script>

<style lang="scss" scoped>
  .perception-container {
    .tab-container {
      display: flex;
      .tab-item {
        position: relative;
        width: 158px;
        height: 34px;
        font-weight: 400;
        color: #8ec1ef;
        text-align: center;
        cursor: pointer;
        &::after {
          position: absolute;
          top: 7px;
          right: 0;
          width: 2px;
          height: 24px;
          content: '';
          background: #276897;
        }
        &:last-child::after {
          display: none;
        }
        &.selected {
          font-weight: 500;
          color: #d5ebff;
        }
      }
    }
    .content {
      width: 100%;
      height: 630px;
      margin-left: 20px;
    }
  }
</style>
