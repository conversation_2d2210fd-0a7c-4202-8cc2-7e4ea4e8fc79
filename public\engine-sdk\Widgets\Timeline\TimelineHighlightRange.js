import defaultValue from"../../Core/defaultValue.js";import JulianDate from"../../Core/JulianDate.js";function TimelineHighlightRange(t,i,e){this._color=t,this._height=i,this._base=defaultValue(e,0)}TimelineHighlightRange.prototype.getHeight=function(){return this._height},TimelineHighlightRange.prototype.getBase=function(){return this._base},TimelineHighlightRange.prototype.getStartTime=function(){return this._start},TimelineHighlightRange.prototype.getStopTime=function(){return this._stop},TimelineHighlightRange.prototype.setRange=function(t,i){this._start=t,this._stop=i},TimelineHighlightRange.prototype.render=function(t){var i="";if(this._start&&this._stop&&this._color){var e=JulianDate.secondsDifference(this._start,t.epochJulian),h=Math.round(t.timeBarWidth*t.getAlpha(e)),n=JulianDate.secondsDifference(this._stop,t.epochJulian),o=Math.round(t.timeBarWidth*t.getAlpha(n))-h;h<0&&(o+=h,h=0),h+o>t.timeBarWidth&&(o=t.timeBarWidth-h),o>0&&(i='<span class="cesium-timeline-highlight" style="left: '+h.toString()+"px; width: "+o.toString()+"px; bottom: "+this._base.toString()+"px; height: "+this._height+"px; background-color: "+this._color+';"></span>')}return i};export default TimelineHighlightRange;