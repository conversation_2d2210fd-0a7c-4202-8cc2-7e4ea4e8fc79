/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./defined-3b3eb2ba","./Rectangle-9bffefe4","./AttributeCompression-d661357e","./ComponentDatatype-dad47320","./Math-b5f4d889"],(function(t,e,i,o,a,n,r,s){"use strict";function c(t,e){this._ellipsoid=t,this._cameraPosition=new i.Cartesian3,this._cameraPositionInScaledSpace=new i.Cartesian3,this._distanceToLimbInScaledSpaceSquared=0,o.defined(e)&&(this.cameraPosition=e)}Object.defineProperties(c.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(t){const e=this._ellipsoid.transformPositionToScaledSpace(t,this._cameraPositionInScaledSpace),o=i.Cartesian3.magnitudeSquared(e)-1;i.Cartesian3.clone(t,this._cameraPosition),this._cameraPositionInScaledSpace=e,this._distanceToLimbInScaledSpaceSquared=o}}});const d=new i.Cartesian3;c.prototype.isPointVisible=function(t){return C(this._ellipsoid.transformPositionToScaledSpace(t,d),this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)},c.prototype.isScaledSpacePointVisible=function(t){return C(t,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};const u=new i.Cartesian3;c.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(t,e){const i=this._ellipsoid;let a,n;return o.defined(e)&&e<0&&i.minimumRadius>-e?(n=u,n.x=this._cameraPosition.x/(i.radii.x+e),n.y=this._cameraPosition.y/(i.radii.y+e),n.z=this._cameraPosition.z/(i.radii.z+e),a=n.x*n.x+n.y*n.y+n.z*n.z-1):(n=this._cameraPositionInScaledSpace,a=this._distanceToLimbInScaledSpaceSquared),C(t,n,a)},c.prototype.computeHorizonCullingPoint=function(t,e,i){return p(this._ellipsoid,t,e,i)};const l=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE);c.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(t,e,i,o){return p(f(this._ellipsoid,i,l),t,e,o)},c.prototype.computeHorizonCullingPointFromVertices=function(t,e,i,o,a){return S(this._ellipsoid,t,e,i,o,a)},c.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(t,e,i,o,a,n){return S(f(this._ellipsoid,a,l),t,e,i,o,n)};const m=[];c.prototype.computeHorizonCullingPointFromRectangle=function(t,o,n){const r=a.Rectangle.subsample(t,o,0,m),s=e.BoundingSphere.fromPoints(r);if(!(i.Cartesian3.magnitude(s.center)<.1*o.minimumRadius))return this.computeHorizonCullingPoint(s.center,r,n)};const h=new i.Cartesian3;function f(t,e,n){if(o.defined(e)&&e<0&&t.minimumRadius>-e){const o=i.Cartesian3.fromElements(t.radii.x+e,t.radii.y+e,t.radii.z+e,h);t=a.Ellipsoid.fromCartesian3(o,n)}return t}function p(t,e,a,n){o.defined(n)||(n=new i.Cartesian3);const r=T(t,e);let s=0;for(let e=0,i=a.length;e<i;++e){const i=N(t,a[e],r);if(i<0)return;s=Math.max(s,i)}return b(r,s,n)}const x=new i.Cartesian3;function S(t,e,a,n,r,s){o.defined(s)||(s=new i.Cartesian3),n=o.defaultValue(n,3),r=o.defaultValue(r,i.Cartesian3.ZERO);const c=T(t,e);let d=0;for(let e=0,i=a.length;e<i;e+=n){x.x=a[e]+r.x,x.y=a[e+1]+r.y,x.z=a[e+2]+r.z;const i=N(t,x,c);if(i<0)return;d=Math.max(d,i)}return b(c,d,s)}function C(t,e,o){const a=e,n=o,r=i.Cartesian3.subtract(t,a,d),s=-i.Cartesian3.dot(r,a);return!(n<0?s>0:s>n&&s*s/i.Cartesian3.magnitudeSquared(r)>n)}const g=new i.Cartesian3,y=new i.Cartesian3;function N(t,e,o){const a=t.transformPositionToScaledSpace(e,g);let n=i.Cartesian3.magnitudeSquared(a),r=Math.sqrt(n);const s=i.Cartesian3.divideByScalar(a,r,y);n=Math.max(1,n),r=Math.max(1,r);const c=1/r;return 1/(i.Cartesian3.dot(s,o)*c-i.Cartesian3.magnitude(i.Cartesian3.cross(s,o,s))*(Math.sqrt(n-1)*c))}function b(t,e,o){if(!(e<=0||e===1/0||e!=e))return i.Cartesian3.multiplyByScalar(t,e,o)}const M=new i.Cartesian3;function T(t,e){return i.Cartesian3.equals(e,i.Cartesian3.ZERO)?e:(t.transformPositionToScaledSpace(e,M),i.Cartesian3.normalize(M,M))}const P={getHeight:function(t,e,i){return(t-i)*e+i}},z=new i.Cartesian3;P.getPosition=function(t,e,o,a,n){const r=e.cartesianToCartographic(t,z),s=P.getHeight(r.height,o,a);return i.Cartesian3.fromRadians(r.longitude,r.latitude,s,e,n)};var _=P;var E=Object.freeze({NONE:0,BITS12:1});const H=new i.Cartesian3,w=new i.Cartesian3,A=new i.Cartesian2,I=new a.Matrix4,q=new a.Matrix4,V=Math.pow(2,12);function G(t,e,n,r,s,c,d,u,l,m){let h,f,p=E.NONE;if(o.defined(e)&&o.defined(n)&&o.defined(r)&&o.defined(s)){const t=e.minimum,o=e.maximum,c=i.Cartesian3.subtract(o,t,w),d=r-n;p=Math.max(i.Cartesian3.maximumComponent(c),d)<V-1?E.BITS12:E.NONE,h=a.Matrix4.inverseTransformation(s,new a.Matrix4);const u=i.Cartesian3.negate(t,H);a.Matrix4.multiply(a.Matrix4.fromTranslation(u,I),h,h);const l=H;l.x=1/c.x,l.y=1/c.y,l.z=1/c.z,a.Matrix4.multiply(a.Matrix4.fromScale(l,I),h,h),f=a.Matrix4.clone(s),a.Matrix4.setTranslation(f,i.Cartesian3.ZERO,f),s=a.Matrix4.clone(s,new a.Matrix4);const m=a.Matrix4.fromTranslation(t,I),x=a.Matrix4.fromScale(c,q),S=a.Matrix4.multiply(m,x,I);a.Matrix4.multiply(s,S,s),a.Matrix4.multiply(f,S,f)}this.quantization=p,this.minimumHeight=n,this.maximumHeight=r,this.center=i.Cartesian3.clone(t),this.toScaledENU=h,this.fromScaledENU=s,this.matrix=f,this.hasVertexNormals=c,this.hasWebMercatorT=o.defaultValue(d,!1),this.hasGeodeticSurfaceNormals=o.defaultValue(u,!1),this.exaggeration=o.defaultValue(l,1),this.exaggerationRelativeHeight=o.defaultValue(m,0),this.stride=0,this._offsetGeodeticSurfaceNormal=0,this._offsetVertexNormal=0,this._calculateStrideAndOffsets()}G.prototype.encode=function(t,e,o,r,c,d,u,l){const m=r.x,h=r.y;if(this.quantization===E.BITS12){(o=a.Matrix4.multiplyByPoint(this.toScaledENU,o,H)).x=s.CesiumMath.clamp(o.x,0,1),o.y=s.CesiumMath.clamp(o.y,0,1),o.z=s.CesiumMath.clamp(o.z,0,1);const r=this.maximumHeight-this.minimumHeight,d=s.CesiumMath.clamp((c-this.minimumHeight)/r,0,1);i.Cartesian2.fromElements(o.x,o.y,A);const l=n.AttributeCompression.compressTextureCoordinates(A);i.Cartesian2.fromElements(o.z,d,A);const f=n.AttributeCompression.compressTextureCoordinates(A);i.Cartesian2.fromElements(m,h,A);const p=n.AttributeCompression.compressTextureCoordinates(A);if(t[e++]=l,t[e++]=f,t[e++]=p,this.hasWebMercatorT){i.Cartesian2.fromElements(u,0,A);const o=n.AttributeCompression.compressTextureCoordinates(A);t[e++]=o}}else i.Cartesian3.subtract(o,this.center,H),t[e++]=H.x,t[e++]=H.y,t[e++]=H.z,t[e++]=c,t[e++]=m,t[e++]=h,this.hasWebMercatorT&&(t[e++]=u);return this.hasVertexNormals&&(t[e++]=n.AttributeCompression.octPackFloat(d)),this.hasGeodeticSurfaceNormals&&(t[e++]=l.x,t[e++]=l.y,t[e++]=l.z),e};const O=new i.Cartesian3,B=new i.Cartesian3;G.prototype.addGeodeticSurfaceNormals=function(t,e,i){if(this.hasGeodeticSurfaceNormals)return;const o=this.stride,a=t.length/o;this.hasGeodeticSurfaceNormals=!0,this._calculateStrideAndOffsets();const n=this.stride;for(let r=0;r<a;r++){for(let i=0;i<o;i++){const a=r*o+i;e[r*n+i]=t[a]}const a=this.decodePosition(e,r,O),s=i.geodeticSurfaceNormal(a,B),c=r*n+this._offsetGeodeticSurfaceNormal;e[c]=s.x,e[c+1]=s.y,e[c+2]=s.z}},G.prototype.removeGeodeticSurfaceNormals=function(t,e){if(!this.hasGeodeticSurfaceNormals)return;const i=this.stride,o=t.length/i;this.hasGeodeticSurfaceNormals=!1,this._calculateStrideAndOffsets();const a=this.stride;for(let n=0;n<o;n++)for(let o=0;o<a;o++){const r=n*i+o;e[n*a+o]=t[r]}},G.prototype.decodePosition=function(t,e,r){if(o.defined(r)||(r=new i.Cartesian3),e*=this.stride,this.quantization===E.BITS12){const i=n.AttributeCompression.decompressTextureCoordinates(t[e],A);r.x=i.x,r.y=i.y;const o=n.AttributeCompression.decompressTextureCoordinates(t[e+1],A);return r.z=o.x,a.Matrix4.multiplyByPoint(this.fromScaledENU,r,r)}return r.x=t[e],r.y=t[e+1],r.z=t[e+2],i.Cartesian3.add(r,this.center,r)},G.prototype.getExaggeratedPosition=function(t,e,i){i=this.decodePosition(t,e,i);const o=this.exaggeration,a=this.exaggerationRelativeHeight;if(1!==o&&this.hasGeodeticSurfaceNormals){const n=this.decodeGeodeticSurfaceNormal(t,e,B),r=this.decodeHeight(t,e),s=_.getHeight(r,o,a)-r;i.x+=n.x*s,i.y+=n.y*s,i.z+=n.z*s}return i},G.prototype.decodeTextureCoordinates=function(t,e,a){return o.defined(a)||(a=new i.Cartesian2),e*=this.stride,this.quantization===E.BITS12?n.AttributeCompression.decompressTextureCoordinates(t[e+2],a):i.Cartesian2.fromElements(t[e+4],t[e+5],a)},G.prototype.decodeHeight=function(t,e){if(e*=this.stride,this.quantization===E.BITS12){return n.AttributeCompression.decompressTextureCoordinates(t[e+1],A).y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight}return t[e+3]},G.prototype.decodeWebMercatorT=function(t,e){return e*=this.stride,this.quantization===E.BITS12?n.AttributeCompression.decompressTextureCoordinates(t[e+3],A).x:t[e+6]},G.prototype.getOctEncodedNormal=function(t,e,o){const a=t[e=e*this.stride+this._offsetVertexNormal]/256,n=Math.floor(a),r=256*(a-n);return i.Cartesian2.fromElements(n,r,o)},G.prototype.decodeGeodeticSurfaceNormal=function(t,e,i){return e=e*this.stride+this._offsetGeodeticSurfaceNormal,i.x=t[e],i.y=t[e+1],i.z=t[e+2],i},G.prototype._calculateStrideAndOffsets=function(){let t=0;if(this.quantization===E.BITS12)t+=3;else t+=6;this.hasWebMercatorT&&(t+=1),this.hasVertexNormals&&(this._offsetVertexNormal=t,t+=1),this.hasGeodeticSurfaceNormals&&(this._offsetGeodeticSurfaceNormal=t,t+=3),this.stride=t};const R={position3DAndHeight:0,textureCoordAndEncodedNormals:1,geodeticSurfaceNormal:2},U={compressed0:0,compressed1:1,geodeticSurfaceNormal:2};G.prototype.getAttributes=function(t){const e=r.ComponentDatatype.FLOAT,i=r.ComponentDatatype.getSizeInBytes(e),o=this.stride*i;let a=0;const n=[];function s(r,s){n.push({index:r,vertexBuffer:t,componentDatatype:e,componentsPerAttribute:s,offsetInBytes:a,strideInBytes:o}),a+=s*i}if(this.quantization===E.NONE){s(R.position3DAndHeight,4);let t=2;t+=this.hasWebMercatorT?1:0,t+=this.hasVertexNormals?1:0,s(R.textureCoordAndEncodedNormals,t),this.hasGeodeticSurfaceNormals&&s(R.geodeticSurfaceNormal,3)}else{const t=this.hasWebMercatorT||this.hasVertexNormals,e=this.hasWebMercatorT&&this.hasVertexNormals;s(U.compressed0,t?4:3),e&&s(U.compressed1,1),this.hasGeodeticSurfaceNormals&&s(U.geodeticSurfaceNormal,3)}return n},G.prototype.getAttributeLocations=function(){return this.quantization===E.NONE?R:U},G.clone=function(t,e){if(o.defined(t))return o.defined(e)||(e=new G),e.quantization=t.quantization,e.minimumHeight=t.minimumHeight,e.maximumHeight=t.maximumHeight,e.center=i.Cartesian3.clone(t.center),e.toScaledENU=a.Matrix4.clone(t.toScaledENU),e.fromScaledENU=a.Matrix4.clone(t.fromScaledENU),e.matrix=a.Matrix4.clone(t.matrix),e.hasVertexNormals=t.hasVertexNormals,e.hasWebMercatorT=t.hasWebMercatorT,e.hasGeodeticSurfaceNormals=t.hasGeodeticSurfaceNormals,e.exaggeration=t.exaggeration,e.exaggerationRelativeHeight=t.exaggerationRelativeHeight,e._calculateStrideAndOffsets(),e},t.EllipsoidalOccluder=c,t.TerrainEncoding=G}));
