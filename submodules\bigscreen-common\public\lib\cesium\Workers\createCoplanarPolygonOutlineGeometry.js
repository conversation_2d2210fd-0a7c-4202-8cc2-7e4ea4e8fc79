/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./arrayRemoveDuplicates-5b666c82","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./CoplanarPolygonGeometryLibrary-a05e7473","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./PolygonGeometryLibrary-1842a245","./Rectangle-9bffefe4","./Math-b5f4d889","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./WebGLConstants-433debbf","./OrientedBoundingBox-e47c7a90","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./ArcType-e42cfb05","./EllipsoidRhumbLine-d5e7f3db","./PolygonPipeline-805d6577"],(function(e,t,n,o,r,i,a,y,s,c,l,d,p,u,m,f,b,g,h,P,G,C,L,T,E,H,k,A){"use strict";function w(e){const t=e.length,n=new Float64Array(3*t),r=l.IndexDatatype.createTypedArray(t,2*t);let i=0,s=0;for(let o=0;o<t;o++){const a=e[o];n[i++]=a.x,n[i++]=a.y,n[i++]=a.z,r[s++]=o,r[s++]=(o+1)%t}const c=new y.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:n})});return new a.Geometry({attributes:c,indices:r,primitiveType:a.PrimitiveType.LINES})}function I(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy;this._polygonHierarchy=t,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=d.PolygonGeometryLibrary.computeHierarchyPackedLength(t,n.Cartesian3)+1}I.fromPositions=function(e){return new I({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions}})},I.pack=function(e,t,o){return o=i.defaultValue(o,0),t[o=d.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,o,n.Cartesian3)]=e.packedLength,t};const _={polygonHierarchy:{}};return I.unpack=function(e,t,o){t=i.defaultValue(t,0);const r=d.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,n.Cartesian3);t=r.startingIndex,delete r.startingIndex;const a=e[t];return i.defined(o)||(o=new I(_)),o._polygonHierarchy=r,o.packedLength=a,o},I.createGeometry=function(o){const i=o._polygonHierarchy;let y=i.positions;if(y=e.arrayRemoveDuplicates(y,n.Cartesian3.equalsEpsilon,!0),y.length<3)return;if(!r.CoplanarPolygonGeometryLibrary.validOutline(y))return;const l=d.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(i,!1);if(0===l.length)return;const p=[];for(let e=0;e<l.length;e++){const t=new s.GeometryInstance({geometry:w(l[e])});p.push(t)}const u=c.GeometryPipeline.combineInstances(p)[0],m=t.BoundingSphere.fromPoints(i.positions);return new a.Geometry({attributes:u.attributes,indices:u.indices,primitiveType:u.primitiveType,boundingSphere:m})},function(e,t){return i.defined(t)&&(e=I.unpack(e,t)),e._ellipsoid=p.Ellipsoid.clone(e._ellipsoid),I.createGeometry(e)}}));
