<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 10:23:50
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-03 22:13:51
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\EmergencyResources\index.vue
 * @Description:
 *
-->
<template>
  <div class="emergency-resources-container">
    <div class="head" v-show-ai-question @click="handleTitleClick">应急资源</div>
    <div class="content-box">
      <div class="content-top">
        <team></team>
        <material></material>
      </div>
      <div class="content-bottom">
        <car></car>
        <device></device>
      </div>
    </div>
  </div>
</template>

<script setup>
  import car from './car.vue'
  import device from './device.vue'
  import material from './material.vue'
  import team from './team.vue'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'
  import lib from '@/utils/lib'
  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleTitleClick = () => {
    if (showAIQuestion.value) {
      lib.utils.sendQuestionToAIChat('应急资源')
    }
  }
</script>

<style lang="scss" scoped>
  .emergency-resources-container {
    width: 635px;
    height: 443px;
    .head {
      width: 216px;
      height: 50px;
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      font-weight: 400;
      line-height: 45px;
      color: #d4e9ff;
      text-align: center;
      background: url('@/assets/ScreenRight/Perception/Device/head.png') no-repeat;
      background-size: 100% 100%;
    }
    .content-top {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 221px;
      margin-top: 18px;
    }
    .content-bottom {
      display: flex;
      justify-content: space-between;
      margin-top: 22px;
    }
  }
</style>
