<template>
  <div :class="`ScreenBottom justify-center ${screen.activePage}`">
    <div class="screen-bottom-main justify-between">
      <div class="screen-botom-item" :class="{ active: item.isActive }" v-for="(item, index) in state.list" :key="index" @click="handleClick(item, index)">
        <div class="screen-botom-item-icon">
          <img :src="getAssetsFile(`ScreenBottom/btn${index + 1}${item.isActive ? 'Selected' : ''}.png`)" />
        </div>
        <div class="screen-botom-item-text">
          {{ item.name }}
        </div>
      </div>
    </div>
    <!-- <ul class="screen-bottom-ul">
      <li :class="`screen-bottom-ul-li ${item.isActive ? 'active' : ''}`" v-for="(item, index) in state.list" :key="index" @click="handleClick(item, index)">
        <span class="img">
          <img v-show="item.isActive" :src="getAssetsFile(`ScreenBottom/activeIcon${index + 1}.svg`)" />
          <img v-show="!item.isActive" :src="getAssetsFile(`ScreenBottom/noActiveIcon${index + 1}.svg`)" />
        </span>

        <span class="img"></span>
        <span class="text">{{ item.name }}</span>
      </li>
    </ul> -->
  </div>
</template>

<script>
  export default {
    name: 'ScreenBottom'
  }
</script>
<script setup>
  import { computed, reactive, ref, toRefs } from 'vue'

  import useStore from '@/store'

  import { getAssetsFile } from '@/utils/index'
  const { screen } = useStore()
  const state = reactive({
    list: [
      { name: '综合管理', isActive: true, code: 'HomePage' },
      { name: '事件管理', isActive: false, code: 'Emergency' },
      { name: '市政管理', isActive: false, code: 'Municipal' },
      { name: '地下设施', isActive: false, code: 'Water' },
      { name: '市容环境', isActive: false, code: 'Green' }
    ]
    // list: [
    //   { name: '综合管理', isActive: true, code: 'HomePage' },
    //   { name: '应急管理', isActive: false, code: 'Emergency' },
    //   { name: '地下管网', isActive: false, code: 'Water' },
    //   { name: '市政管理', isActive: false, code: 'Municipal' },
    //   { name: '市容环境', isActive: false, code: 'Green' }
    // ]
  })
  // methods
  const handleClick = (item, index) => {
    state.list.forEach((itemCon, indexCon) => {
      itemCon.isActive = index == indexCon
    })
    screen.setActivePage(item.code)
  }
</script>
<style lang="scss" scoped>
  .ScreenBottom {
    position: absolute;
    bottom: 0;
    left: 50%;
    z-index: 999;
    width: 1720px;
    height: 109px;
    color: #ffffff;
    // background: url('@/assets/ScreenBottom/bg.png') no-repeat center;
    // background-size: 100% 100%;
    transform: translateX(-50%);

    //&.HomePage {
    //  left: 851px;
    //}
    //&.Emergency {
    //left: 50%;
    //transform: translateX(-50%);
    //}
    .screen-bottom-main {
      width: 813px;
      height: 120px;
      .screen-botom-item {
        position: relative;
        top: -15px;
        cursor: pointer;
        &-icon {
          width: 110px;
          height: 84px;
          img {
            width: 110px;
            height: 84px;
          }
        }
        &-text {
          width: 100%;
          height: 20px;
          font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
          font-size: 28px;
          font-weight: normal;
          line-height: 20px;
          color: #bbdfff;
          text-align: center;
        }
        &.active {
          top: -25px;
          .screen-botom-item-icon {
            height: 93px;
            img {
              height: 93px;
            }
          }
          .screen-botom-item-text {
            font-size: 32px;
            line-height: 20px;
            color: #ffffff;
          }
        }
      }
    }
  }
</style>
