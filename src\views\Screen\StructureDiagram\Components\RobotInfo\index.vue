<template>
  <div class="robot-info-container relative">
    <div class="title popup-title cursor-move">机器人巡检</div>
    <div class="close-btn" @click="handleClose"><img src="@/assets/StructureDiagram/robot/close.png" w12 h12 /></div>
    <div class="subtitle"><span class="position-absolute -top-3">巡检路线</span></div>
    <div class="inspect-route-container route cursor-pointer" @click="showLargeImg"></div>
    <div class="subtitle"><span class="position-absolute -top-3">巡检画面</span></div>
    <div class="inspect-route-container cursor-pointer" @click="showLargeVideo">
      <WSPlayer
        width="276px"
        height="135px"
        url="wss://*************/stream/?url=rtsp://admin:xm123456@*************:554/h265/ch1/sub/av_stream&width=276&height=135&bitrate=800"></WSPlayer>
    </div>
  </div>
</template>

<script setup>
  import WSPlayer from '@/components/WSPlayer/index.vue'

  import lib from '@/utils/lib.ts'
  const { appContext } = getCurrentInstance()

  const handleClose = lib.provideTools.handleClose.inject()

  const showLargeImg = () => {
    lib.popWindow.createPopWindow('../StructureDiagram/Components/RobotInfo/inspectRoute.vue', {
      left: 420,
      top: 280,
      tag: 'inspectRoute',
      appContext
    })
  }

  const showLargeVideo = () => {
    lib.popWindow.createPopWindow('../StructureDiagram/Components/RobotInfo/largeVideo.vue', {
      left: 420,
      top: 280,
      tag: 'largeVideo',
      appContext
    })
  }

  onUnmounted(() => {
    lib.popWindow.removeDialog()
  })
</script>

<style lang="scss" scoped>
  .robot-info-container {
    width: 337px;
    height: 482px;
    background: url('@/assets/StructureDiagram/robot/robotBg.png');
    background-size: cover;
    .title {
      width: 100%;
      height: 40px;
      padding-left: 17px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      line-height: 40px;
      color: #ffffff;
    }
    .close-btn {
      position: absolute;
      top: 8px;
      right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      cursor: pointer;
    }
    .subtitle {
      position: relative;
      width: 306px;
      height: 24px;
      padding-left: 28px;
      margin: 25px auto 0; /* 顶部 25px, 水平自动，底部 0 */
      font-family: PangMenZhengDao-Regular, PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      line-height: 21px;
      color: #ffffff;
      background: url('@/assets/StructureDiagram/robot/titleBg.png');
      background-size: cover;
    }
    .inspect-route-container {
      width: 276px;
      height: 135px;
      margin: 10px auto 0; /* 顶部 25px, 水平自动，底部 0 */
      background: #ffffff;
      &.route {
        background: url('@/assets/StructureDiagram/robot/route.png');
        background-size: contain;
      }
    }
  }
</style>
