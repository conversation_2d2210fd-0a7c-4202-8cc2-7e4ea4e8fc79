define(["exports","./Cartesian2-d051b2ac"],(function(r,t){"use strict";var a={computePositions:function(r,a,e,i,n){var o,s=.5*r,c=-s,u=i+i,f=n?2*u:u,y=new Float64Array(3*f),d=0,h=0,m=n?3*u:0,v=n?3*(u+i):3*i;for(o=0;o<i;o++){var C=o/i*t.CesiumMath.TWO_PI,M=Math.cos(C),b=Math.sin(C),l=M*e,p=b*e,P=M*a,w=b*a;y[h+m]=l,y[h+m+1]=p,y[h+m+2]=c,y[h+v]=P,y[h+v+1]=w,y[h+v+2]=s,h+=3,n&&(y[d++]=l,y[d++]=p,y[d++]=c,y[d++]=P,y[d++]=w,y[d++]=s)}return y}};r.CylinderGeometryLibrary=a}));