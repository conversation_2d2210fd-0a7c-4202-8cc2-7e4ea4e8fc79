import Color from"../../Core/Color.js";import defined from"../../Core/defined.js";import JulianDate from"../../Core/JulianDate.js";function TimelineTrack(t,e,i,a){this.interval=t,this.height=e,this.color=i||new Color(.5,.5,.5,1),this.backgroundColor=a||new Color(0,0,0,0)}TimelineTrack.prototype.render=function(t,e){var i=this.interval.start,a=this.interval.stop,r=e.start<PERSON>ulian,l=JulianDate.addSeconds(e.start<PERSON>ulian,e.duration,new JulianDate);if(JulianDate.lessThan(i,r)&&JulianDate.greaterThan(a,l))t.fillStyle=this.color.toCssColorString(),t.fillRect(0,e.y,e.timeBarWidth,this.height);else if(JulianDate.lessThanOrEquals(i,l)&&JulianDate.greaterThanOrEquals(a,r)){var n,o,s;for(n=0;n<e.timeBarWidth;++n){var h=JulianDate.addSeconds(e.start<PERSON><PERSON>an,n/e.timeBarWidth*e.duration,new JulianDate);!defined(o)&&JulianDate.greaterThanOrEquals(h,i)?o=n:!defined(s)&&JulianDate.greaterThanOrEquals(h,a)&&(s=n)}t.fillStyle=this.backgroundColor.toCssColorString(),t.fillRect(0,e.y,e.timeBarWidth,this.height),defined(o)&&(defined(s)||(s=e.timeBarWidth),t.fillStyle=this.color.toCssColorString(),t.fillRect(o,e.y,Math.max(s-o,1),this.height))}};export default TimelineTrack;