<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-07 14:18:37
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-04-28 19:02:31
 * @FilePath: \bigscreen-qj-web\src\components\HeadLine\index.vue
 * @Description:
 *
-->
<template>
  <div class="headline-container">
    <div class="headline-title" v-show-ai-question @click="handleClick">
      <slot name="title"></slot>
    </div>

    <div class="headline-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
  import { getAssetsFile } from '@/utils'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'
  import lib from '@/utils/lib'

  defineOptions({
    name: 'HeadLine'
  })

  const props = defineProps({
    /**
     * @description: 标题背景图片路径
     */
    titleBg: {
      type: String,
      default: 'headline.png'
    }
  })
  const bg = computed(() => {
    return `url(${getAssetsFile(props.titleBg)})`
  })

  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleClick = (event) => {
    if (showAIQuestion.value) {
      console.log('开始提问')
      // 获取当前点击的标题内容
      const titleSlot = event.currentTarget?.textContent?.trim()
      if (titleSlot) {
        lib.utils.sendQuestionToAIChat(titleSlot)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .headline-container {
    position: relative;
    width: 1313px;
    .headline-title {
      width: 1313px;
      height: 60px;
      padding-left: 129px;
      margin-left: -21px;
      font-family: YouSheBiaoTiHei;
      font-size: 40px;
      font-weight: 400;
      line-height: 40px;
      color: #ffffff;
      text-shadow: 1px 1px 6px #2479db;
      letter-spacing: 8px;
      background: v-bind('bg');
      background-size: 1313px 77px;

      // background: url('@/assets/headline.png');
    }
    .headline-content {
      position: relative;
      margin-top: 16px;
    }
    :deep(.el-input__inner) {
      height: 22px;
      min-height: 22px !important;
      padding: 0 0 0 10px;
      margin-left: 10px;
      font-family: Arial, Arial-Regular;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #8dd8ff;
      cursor: pointer;

      // background: #1a91f2;
      background: none;
      background-size: cover;
      border: none;
    }
  }
</style>
