<template>
  <div class="video-monitor-container">
    <img src="@/assets/ScreenRight/Perception/goPre.png" class="w28 h45 absolute top-260 -left-20 cursor-pointer z-99" @click="gotoPre" />
    <img src="@/assets/ScreenRight/Perception/goNext.png" class="w28 h45 absolute top-260 right-0 cursor-pointer z-99" @click="gotoNext" />
    <el-carousel height="600px" arrow="never" ref="refCarousel" indicator-position="none" :interval="60000">
      <el-carousel-item v-for="item in list" :key="item.id">
        <div class="flex justify-between flex-wrap h590 w565">
          <div class="videoBg" v-for="video in item.videos" :key="video.name">
            <div class="video-container overflow-hidden" @click="clickVideo(video)">
              <videoPlay type="m3u8" width="572px" height="280px" :src="video.url" auto-play muted :control="false"></videoPlay>
              <!-- <VideoPlayer :height="280" :code="video.code" :name="video.name"></VideoPlayer> -->
            </div>
            <!-- <div @click="clickVideo(video)" class="video-title w-1/1 h54 color-#A8D6FF font-size-18 line-height-54 pl6 cursor-pointer">{{ video.name }}</div> -->
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
  import { videoPlay } from 'vue3-video-play'
  // import VideoPlayer from '@/views/Screen/StructureDiagram/Components/VideoWall/components/videoPlayer.vue'

  import { closePopWindowByTag, PopWindowProps, usePopWindow } from 'znyg-frontend-common'
  import VideoPopup from '@/views/Screen/PopupWindow/VideoPopup/index.vue'
  // import VideoPopup from './Components/VideoPopup/index.vue'
  import lib from '@/utils/lib'
  const { showPopWindow } = usePopWindow()

  const refCarousel = ref(null)
  const list = ref([
    {
      id: 1,
      videos: [
        // { name: '江北三车道短焦', url: 'http://***********:10000/hls/test14.m3u8' },
        // { name: '江北三车道长焦', url: 'http://***********:10000/hls/test14-2.m3u8' },
        // { name: '江北一车道短焦', url: 'http://***********:10000/hls/test15.m3u8' },
        { name: '江北一车道长焦', url: 'http://***********:10000/hls/test15-2.m3u8', code: 'NX-Y1' },
        // { name: '江南三车道长焦', url: 'http://***********:10000/hls/test16-2.m3u8' },
        { name: '江南一车道长焦', url: 'http://***********:10000/hls/test17-2.m3u8', code: 'SX-Y1' }
      ]
    }
    // {
    //   id: 2,
    //   videos: [
    //     { name: '江南三车道短焦', url: 'http://***********:10000/hls/test16.m3u8' },
    //     { name: '江南三车道长焦', url: 'http://***********:10000/hls/test16-2.m3u8' },
    //     { name: '江南一车道短焦', url: 'http://***********:10000/hls/test17.m3u8' },
    //     { name: '江南一车道长焦', url: 'http://***********:10000/hls/test17-2.m3u8' }
    //   ]
    // }
  ])
  const gotoPre = () => {
    refCarousel.value.prev()
  }
  const gotoNext = () => {
    refCarousel.value.next()
  }
  const clickVideo = (video) => {
    closePopWindowByTag(lib.enumMap.PopWindowTag.视频弹窗)
    // 视频弹窗
    const op: PopWindowProps = {
      left: 1816,
      top: 330,
      tag: lib.enumMap.PopWindowTag.视频弹窗,
      // zIndex: 999,
      draggable: true
    }
    showPopWindow(op, VideoPopup, { url: video.url, title: video.name })
  }
  onUnmounted(() => {
    console.log('unmounted')
    list.value[0].videos.forEach((item) => {
      item.url = ''
    })
  })
</script>

<style lang="scss" scoped>
  .video-monitor-container {
    position: relative;
    width: 600px;
    height: 600px;
    .video-title {
      font-family: Alibaba-PuHuiTi;
    }
    .videoBg {
      width: 603px;
      height: 283px;
      background: url('@/assets/ScreenRight/Perception/Structure/videoBg.png') no-repeat;
    }
    :deep(.d-player-wrap) {
      pointer-events: none !important;
      background-color: transparent !important;
    }
  }
</style>
