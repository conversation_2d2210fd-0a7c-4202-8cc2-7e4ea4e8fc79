define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./CylinderGeometryLibrary-03a8620f"],(function(e,t,i,r,a,o,n,s,u,f,d,b,c,m,p,l){"use strict";var y=new i.Cartesian2;function h(i){i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT);var r=i.length,a=i.topRadius,o=i.bottomRadius,n=e.defaultValue(i.slices,128),s=Math.max(e.defaultValue(i.numberOfVerticalLines,16),0);if(t.Check.typeOf.number("options.positions",r),t.Check.typeOf.number("options.topRadius",a),t.Check.typeOf.number("options.bottomRadius",o),t.Check.typeOf.number.greaterThanOrEquals("options.slices",n,3),e.defined(i.offsetAttribute)&&i.offsetAttribute===p.GeometryOffsetAttribute.TOP)throw new t.DeveloperError("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._length=r,this._topRadius=a,this._bottomRadius=o,this._slices=n,this._numberOfVerticalLines=s,this._offsetAttribute=i.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}h.packedLength=6,h.pack=function(i,r,a){return t.Check.typeOf.object("value",i),t.Check.defined("array",r),a=e.defaultValue(a,0),r[a++]=i._length,r[a++]=i._topRadius,r[a++]=i._bottomRadius,r[a++]=i._slices,r[a++]=i._numberOfVerticalLines,r[a]=e.defaultValue(i._offsetAttribute,-1),r};var _={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};function v(t,i){return e.defined(i)&&(t=h.unpack(t,i)),h.createGeometry(t)}return h.unpack=function(i,r,a){t.Check.defined("array",i),r=e.defaultValue(r,0);var o=i[r++],n=i[r++],s=i[r++],u=i[r++],f=i[r++],d=i[r];return e.defined(a)?(a._length=o,a._topRadius=n,a._bottomRadius=s,a._slices=u,a._numberOfVerticalLines=f,a._offsetAttribute=-1===d?void 0:d,a):(_.length=o,_.topRadius=n,_.bottomRadius=s,_.slices=u,_.numberOfVerticalLines=f,_.offsetAttribute=-1===d?void 0:d,new h(_))},h.createGeometry=function(t){var r=t._length,o=t._topRadius,n=t._bottomRadius,s=t._slices,u=t._numberOfVerticalLines;if(!(r<=0||o<0||n<0||0===o&&0===n)){var h,_=2*s,v=l.CylinderGeometryLibrary.computePositions(r,o,n,s,!1),A=2*s;if(u>0){var O=Math.min(u,s);h=Math.round(s/O),A+=O}var C,R=m.IndexDatatype.createTypedArray(_,2*A),G=0;for(C=0;C<s-1;C++)R[G++]=C,R[G++]=C+1,R[G++]=C+s,R[G++]=C+1+s;if(R[G++]=s-1,R[G++]=0,R[G++]=s+s-1,R[G++]=s,u>0)for(C=0;C<s;C+=h)R[G++]=C,R[G++]=C+s;var g=new c.GeometryAttributes;g.position=new d.GeometryAttribute({componentDatatype:f.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v}),y.x=.5*r,y.y=Math.max(n,o);var V=new a.BoundingSphere(i.Cartesian3.ZERO,i.Cartesian2.magnitude(y));if(e.defined(t._offsetAttribute)){r=v.length;var k=new Uint8Array(r/3),L=t._offsetAttribute===p.GeometryOffsetAttribute.NONE?0:1;p.arrayFill(k,L),g.applyOffset=new d.GeometryAttribute({componentDatatype:f.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:k})}return new d.Geometry({attributes:g,indices:R,primitiveType:b.PrimitiveType.LINES,boundingSphere:V,offsetAttribute:t._offsetAttribute})}},v}));