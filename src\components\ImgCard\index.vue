<template>
  <div class="img-card-com" @click="handleImg">
    <!-- 根据 onlyOne 参数决定渲染全部图片还是仅第一张 -->
    <el-image v-for="(item, index) in onlyOne && picList.length > 1 ? [picList[0]] : picList" :key="index" :src="item.url" lazy />
    <ElTooltip effect="dark" :content="name" placement="top">
      <div class="title">{{ lib.utils.getMiddleEllipsis(name, 15) }}</div>
    </ElTooltip>
  </div>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { vTooltip } from 'znyg-frontend-common'

  import lib from '@/utils/lib'
  import { ElImage } from 'element-plus'
  const { storeDictionary } = lib.store()
  const props = defineProps({
    docId: {
      type: [Number, String],
      default: null
    },
    name: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: ''
    },
    // 新增 onlyOne 参数
    onlyOne: {
      type: Boolean,
      default: false
    }
  })
  const myDocids = ref([])

  const picList = ref([])
  const getImageUrl = () => {
    myDocids.value.forEach((item) => {
      lib.api.commonalityApi.getPicture({ docId: item }).then((res) => {
        if (res.success) {
          const arr = [
            ...picList.value,
            ...res.result.map((item) => {
              item.isImage = item.contentType === 'image/jpeg' || item.contentType === 'image/png'
              const type = item.isImage ? 'img/' : 'doc/'
              item.url = `${storeDictionary.fileUrl}${type}${item.fileName}`
              item.name = item.originalFileName
              return item
            })
          ]
          picList.value = arr
        }
      })
    })
  }
  watch(
    () => props.docId,
    (value) => {
      if (value) {
        myDocids.value = (props.docId + '').split(',')
        // picList.value = []
        getImageUrl()
      }
    },
    {
      immediate: true
    }
  )
  onMounted(() => {
    // getImageUrl()
    // myDocids.value = (props.docId + '').split(',')
    // // picList.value = []
    // getImageUrl()
  })
  const handleImg = () => {
    const urlList = picList.value.map((item) => item.url)
    lib.utils.openImageZoom(urlList, props.name)
  }
</script>

<style scoped lang="scss">
  .img-card-com {
    position: relative;
    display: inline-block;
    width: v-bind(width);
    height: v-bind(height);
    .el-image {
      width: 237px;
      height: 132px;
      border-radius: 7px;
    }
    .title {
      position: absolute;
      bottom: 7px;
      left: 0;
      width: 237px;
      height: 21px;
      overflow: hidden;
      font-family: 'Source Han Sans CN';
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
      background: rgb(1 52 78 / 68%);
      border-radius: 0 0 5px 5px;
    }
  }
</style>
