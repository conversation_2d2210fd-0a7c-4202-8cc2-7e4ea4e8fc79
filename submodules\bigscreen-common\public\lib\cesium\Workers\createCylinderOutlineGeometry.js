/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./CylinderGeometryLibrary-76efdd62","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Rectangle-9bffefe4","./Math-b5f4d889","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf"],(function(t,e,i,n,o,r,a,s,u,f,d,b,c,l,m){"use strict";const p=new e.Cartesian2;function y(t){const e=(t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT)).length,i=t.topRadius,n=t.bottomRadius,r=o.defaultValue(t.slices,128),a=Math.max(o.defaultValue(t.numberOfVerticalLines,16),0);this._length=e,this._topRadius=i,this._bottomRadius=n,this._slices=r,this._numberOfVerticalLines=a,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderOutlineGeometry"}y.packedLength=6,y.pack=function(t,e,i){return i=o.defaultValue(i,0),e[i++]=t._length,e[i++]=t._topRadius,e[i++]=t._bottomRadius,e[i++]=t._slices,e[i++]=t._numberOfVerticalLines,e[i]=o.defaultValue(t._offsetAttribute,-1),e};const _={length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};return y.unpack=function(t,e,i){e=o.defaultValue(e,0);const n=t[e++],r=t[e++],a=t[e++],s=t[e++],u=t[e++],f=t[e];return o.defined(i)?(i._length=n,i._topRadius=r,i._bottomRadius=a,i._slices=s,i._numberOfVerticalLines=u,i._offsetAttribute=-1===f?void 0:f,i):(_.length=n,_.topRadius=r,_.bottomRadius=a,_.slices=s,_.numberOfVerticalLines=u,_.offsetAttribute=-1===f?void 0:f,new y(_))},y.createGeometry=function(f){let d=f._length;const b=f._topRadius,c=f._bottomRadius,l=f._slices,m=f._numberOfVerticalLines;if(d<=0||b<0||c<0||0===b&&0===c)return;const y=2*l,_=n.CylinderGeometryLibrary.computePositions(d,b,c,l,!1);let h,A=2*l;if(m>0){const t=Math.min(m,l);h=Math.round(l/t),A+=t}const R=u.IndexDatatype.createTypedArray(y,2*A);let G,O=0;for(G=0;G<l-1;G++)R[O++]=G,R[O++]=G+1,R[O++]=G+l,R[O++]=G+1+l;if(R[O++]=l-1,R[O++]=0,R[O++]=l+l-1,R[O++]=l,m>0)for(G=0;G<l;G+=h)R[O++]=G,R[O++]=G+l;const V=new a.GeometryAttributes;V.position=new r.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:_}),p.x=.5*d,p.y=Math.max(c,b);const g=new t.BoundingSphere(e.Cartesian3.ZERO,e.Cartesian2.magnitude(p));if(o.defined(f._offsetAttribute)){d=_.length;const t=f._offsetAttribute===s.GeometryOffsetAttribute.NONE?0:1,e=new Uint8Array(d/3).fill(t);V.applyOffset=new r.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}return new r.Geometry({attributes:V,indices:R,primitiveType:r.PrimitiveType.LINES,boundingSphere:g,offsetAttribute:f._offsetAttribute})},function(t,e){return o.defined(e)&&(t=y.unpack(t,e)),y.createGeometry(t)}}));
