<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-10 16:26:45
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-01 15:42:08
 * @FilePath: \bigscreen-qj-web\src\views\Screen\MapPopupWindow\Components\MaintenanceDetails\index.vue
 * @Description:
 *
-->
<template>
  <div class="maintenance-details-container">
    <PopupBg title="养护详情" width="450px" height="587px" @close="handlerClose">
      <PopupSubTitle title="作业信息"></PopupSubTitle>
      <!-- <div class="info-box">
        <div class="info-item" v-for="(item, index) in infoData" :key="index">
          <div style="width: 73px; color: #dbefff">{{ item.name }}</div>
          <div style="flex: 1; color: #5bb5ff" v-tooltip>{{ item.value }}</div>
        </div>
      </div> -->
      <div class="info-box gap-10">
        <div class="w-50% flex" v-for="i in 2" :key="i">
          <div class="name-block">
            <div v-for="(item, index) in i == 1 ? infoData.slice(0, 5) : infoData.slice(5)" :key="index" style="width: 73px; color: #dbefff">
              {{ item.name }}:
            </div>
          </div>
          <div class="value-block">
            <div v-for="(item, index) in i == 1 ? infoData.slice(0, 5) : infoData.slice(5)" :key="index">
              <el-tooltip class="box-item" effect="dark" :content="item.value" placement="top">
                <div style="width: 80px; height: 24px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ item.value }}</div>
              </el-tooltip>
              <!-- <div v-tooltip="{ content: item.value }">{{ item.value }}</div> -->
            </div>
          </div>
        </div>
      </div>
      <PopupSubTitle title="作业照片" class="mt12"></PopupSubTitle>
      <div class="img-box">
        <PropPicture :data="pictureInfo" width="300px"></PropPicture>
      </div>
      <PopupSubTitle title="作业流程" class="mt12"></PopupSubTitle>
      <div class="time-line-box scroll-bar-style" :style="timeData.length > 3 ? 'overflow: hidden;overflow-x: scroll;' : ''">
        <div class="item" v-for="(item, index) in timeData" :key="index">
          <div class="spot" @mousemove="showDetail(true, item)" @mouseleave="showDetail(false)">
            <div class="circle" :class="{ active: index == timeData.length - 1 }"></div>
            <div class="circle2" v-if="index == timeData.length - 1"></div>
            <div class="name" :style="{ color: index == timeData.length - 1 ? '#5bb5ff' : '#dbefff' }" v-tooltip>
              {{ getNameByCode(item.code) }}
            </div>
          </div>
          <div class="line" v-if="index !== timeData.length - 1"></div>
        </div>
      </div>
      <div class="details" v-if="selectItem && showMore">
        <span>{{ moment(selectItem.createDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
        <span style="margin-left: 20px">{{ selectItem.userName }}</span>
      </div>
    </PopupBg>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import moment from 'moment'
  import PopupBg from '@/components/PopupBg/index.vue'
  import PopupSubTitle from '@/components/PopupSubTitle/index.vue'
  import PropPicture from '@/components/PropPicture/index.vue'
  import { vTooltip } from 'znyg-frontend-common'
  import lib from '@/utils/lib.ts'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const infoData = ref([
    { name: '当前状态', value: '---' },
    { name: '作业名称', value: '---' },
    { name: '作业类型', value: '---' },
    { name: '养护项目', value: '---' },
    { name: '作业时间', value: '---' },
    { name: '作业位置', value: '---' },
    { name: '作业车辆', value: '---' },
    { name: '作业时长', value: '---' },
    { name: '作业班组', value: '---' }
  ])
  const pictureInfo = ref([])
  const timeData = ref([])
  // const dicMap = ref([])
  const getNameByCode = (code) => {
    if (code == 'acceptance2') {
      return '巡检二级验收'
    } else if (code == 'acceptance1') {
      return '巡检一级验收'
    }
    return lib.store().storeDictionary.dictMap.find((item) => item.code == code)?.name
  }
  const handlerClose = () => {
    lib.popWindow.removeDialog('maintenanceWindow')
  }
  watch(
    () => props.data,
    (newVal) => {
      let paramsId = newVal.id
      if (newVal?.subTitle === '养护作业列表') {
        paramsId = newVal?.dllWorkOrderId
      }
      lib.api.workOrderDetailApi.getMaintenanceOrderDetails({ id: paramsId }).then(async (res) => {
        if (res.success && res.result) {
          infoData.value[0].value = res.result.status
          infoData.value[1].value = res.result.name
          infoData.value[2].value = res.result.type
          infoData.value[3].value = res.result.maintenanceProject
          infoData.value[4].value = res.result.timeRange
          infoData.value[5].value = res.result.location
          infoData.value[6].value = res.result.vehicle
          infoData.value[7].value = (res.result.duration ?? '-') + '小时'
          infoData.value[8].value = res.result.group
          pictureInfo.value = []
          const { picture1, picture2 } = res.result
          Object.keys(picture1).map((key) => {
            pictureInfo.value = [
              ...pictureInfo.value,
              ...picture1[key].map((_) => {
                return { url: _ }
              })
            ]
          })
          Object.keys(picture2).map((key) => {
            pictureInfo.value = [
              ...pictureInfo.value,
              ...picture2[key].map((_) => {
                return { url: _ }
              })
            ]
          })
          const picRes = await lib.api.docDiyDiyApi.getDocsFromUrl({ urlList: pictureInfo.value.map((_) => _.url) })
          if (picRes.success) {
            picRes.result.forEach((item, index) => {
              pictureInfo.value[index].url = import.meta.env.VITE_DOC_URL + 'img/' + item.fileName
            })
          }
        }
      })

      // lib.api.commonalityApi.getDictMap({}).then((res) => {
      //   if (res.success && res.result) {
      //     dicMap.value = res.result.filter((_) => _.parentId == 695)
      //   }
      // })
      lib.api.workOrderDetailApi
        .processData({
          relationId: newVal.dllWorkOrderId ? newVal.dllWorkOrderId : newVal.id,
          relationTable: 'WorkOrder'
        })
        .then((res) => {
          if (res.success && res.result) {
            timeData.value = res.result
          }
        })
    },
    { immediate: true, deep: true }
  )
  const showMore = ref(false)
  const selectItem = ref({})
  const showDetail = (bool, item) => {
    if (bool) {
      showMore.value = true
      selectItem.value = item
    } else {
      showMore.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .maintenance-details-container {
    width: 450px;
    height: 587px;

    // background: url('@/assets/CommonPopup/popupBg2.png');
    // background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7px 17px 0;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .sub-title {
      width: 343px;
      height: 24px;
      padding-left: 28px;
      margin: 14px 21px 0 14px;
      font-family: PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/CommonPopup/title.png');
      background-size: 343px 24px;
    }
    .info-box {
      display: flex;

      // flex-wrap: wrap;
      margin: 11px 21px 0 0;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      .name-block,
      .value-block {
        display: flex;
        flex: 1;
        flex-direction: column;
        gap: 10px;
        width: 203px;
        height: 180px;
        padding: 10px;
        margin-right: 10px;
        color: #5bb5ff;
        background: rgb(39 75 113 / 22%);
        border-radius: 13px;
      }
      .value-block {
        flex: 2;
        margin-right: 0;
        color: #ffffff;
      }
    }
    .img-box {
      display: flex;
      margin: 9px 18px 25px 14px;
      font-family: ' Alibaba PuHuiTi';
      font-size: 16px;
      color: #dbefff;
      :nth-child(2) {
        width: 282px;
        height: 68px;
        margin-left: 10px;
      }
    }
    .time-line-box {
      position: relative;
      display: flex;
      align-items: center;
      width: 288px;
      margin: 16px 18px 25px 35px;
      font-family: ' Alibaba PuHuiTi';
      font-size: 16px;
      color: #00b2ff;
      .item {
        display: flex;
        margin-bottom: 10px;
        .spot {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          cursor: pointer;
          .circle {
            position: relative;
            z-index: 2;
            width: 30px;
            height: 30px;
            background: url('@/assets/ScreenMiddle/circleSelected2.png');
            background-size: 100% 100%;
            &.active {
              z-index: 2;
              width: 40px;
              height: 40px;
              background: url('@/assets/ScreenMiddle/circleSelected.png');
              background-size: 100% 100%;
            }
          }
          .circle2 {
            position: absolute;
            top: 4px;
            width: 30px;
            height: 30px;
            background: url('@/assets/ScreenMiddle/circle.png');
            background-size: 100% 100%;
          }
          .name {
            width: 70px;
            margin-top: 10px;
            text-align: center;
          }
        }
        .line {
          width: 46px;
          height: 1px;
          margin-top: 15px;
          margin-left: -5px;
          background: #cccccc;
        }
      }
    }
    .details {
      width: 288px;
      height: 30px;
      margin: -20px 18px 25px 35px;
      font-family: ' Alibaba PuHuiTi';
      font-size: 16px;
      line-height: 30px;
      color: #ffffff;
      text-align: center;
      border: 1px solid #006d9b;
      border-radius: 4px;
    }
  }
</style>
