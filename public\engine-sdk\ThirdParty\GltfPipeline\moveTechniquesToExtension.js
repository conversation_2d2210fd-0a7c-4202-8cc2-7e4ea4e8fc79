import addExtensionsUsed from"./addExtensionsUsed.js";import addExtensionsRequired from"./addExtensionsRequired.js";import addToArray from"./addToArray.js";import ForEach from"./ForEach.js";import defined from"../../Core/defined.js";function moveTechniquesToExtension(e){var n=e.techniques,r={},s={};if(defined(n)){var t={programs:[],shaders:[],techniques:[]},a=e.glExtensionsUsed;delete e.glExtensionsUsed,ForEach.technique(e,(function(n,i){var d,o={name:n.name,program:void 0,attributes:{},uniforms:{}};ForEach.techniqueAttribute(n,(function(e,r){d=n.parameters[e],o.attributes[r]={semantic:d.semantic}})),ForEach.techniqueUniform(n,(function(e,s){d=n.parameters[e],o.uniforms[s]={count:d.count,node:d.node,type:d.type,semantic:d.semantic,value:d.value},r[e]=s}));var u=e.programs[n.program],c={name:u.name,fragmentShader:void 0,vertexShader:void 0,glExtensions:a},h=e.shaders[u.fragmentShader];c.fragmentShader=addToArray(t.shaders,h,!0);var m=e.shaders[u.vertexShader];c.vertexShader=addToArray(t.shaders,m,!0),o.program=addToArray(t.programs,c),s[i]=addToArray(t.techniques,o)})),t.techniques.length>0&&(defined(e.extensions)||(e.extensions={}),e.extensions.KHR_techniques_webgl=t,addExtensionsUsed(e,"KHR_techniques_webgl"),addExtensionsRequired(e,"KHR_techniques_webgl"))}return ForEach.material(e,(function(e){if(defined(e.technique)){var n={technique:s[e.technique]};ForEach.objectLegacy(e.values,(function(e,s){defined(n.values)||(n.values={});var t=r[s];n.values[t]=e})),defined(e.extensions)||(e.extensions={}),e.extensions.KHR_techniques_webgl=n}delete e.technique,delete e.values})),delete e.techniques,delete e.programs,delete e.shaders,e}export default moveTechniquesToExtension;