define(["exports","./when-1807bd8d","./Check-1951f41f","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Matrix4-a50b021f","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./GeometryAttributes-898891ba","./GeometryPipeline-f727231c","./IndexDatatype-e1c63859","./WebMercatorProjection-ab2cf572"],(function(e,t,r,n,i,o,a,s,d,p,f,u){"use strict";function c(e,r,n){e=t.defaultValue(e,0),r=t.defaultValue(r,0),n=t.defaultValue(n,0),this.value=new Float32Array([e,r,n])}function m(e,r,n){var i,a=!n,s=e.length;if(!a&&s>1){var d=e[0].modelMatrix;for(i=1;i<s;++i)if(!o.Matrix4.equals(d,e[i].modelMatrix)){a=!0;break}}if(a)for(i=0;i<s;++i)t.defined(e[i].geometry)&&p.GeometryPipeline.transformToWorldCoordinates(e[i]);else o.Matrix4.multiplyTransformation(r,e[0].modelMatrix,r)}function h(e,t){var r=e.attributes,n=r.position,i=n.values.length/n.componentsPerAttribute;r.batchId=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(i)});for(var o=r.batchId.values,d=0;d<i;++d)o[d]=t}function l(e){for(var r=e.length,n=0;n<r;++n){var i=e[n];t.defined(i.geometry)?h(i.geometry,n):t.defined(i.westHemisphereGeometry)&&t.defined(i.eastHemisphereGeometry)&&(h(i.westHemisphereGeometry,n),h(i.eastHemisphereGeometry,n))}}function g(e){var n,o,s,d=e.instances,f=e.projection,u=e.elementIndexUintSupported,c=e.scene3DOnly,h=e.vertexCacheOptimize,g=e.compressVertices,y=e.modelMatrix,v=d.length;for(n=0;n<v;++n)if(t.defined(d[n].geometry)){s=d[n].geometry.primitiveType;break}for(n=1;n<v;++n)if(t.defined(d[n].geometry)&&d[n].geometry.primitiveType!==s)throw new r.DeveloperError("All instance geometries must have the same primitiveType.");if(m(d,y,c),!c)for(n=0;n<v;++n)t.defined(d[n].geometry)&&p.GeometryPipeline.splitLongitude(d[n]);if(l(d),h)for(n=0;n<v;++n){var b=d[n];t.defined(b.geometry)?(p.GeometryPipeline.reorderForPostVertexCache(b.geometry),p.GeometryPipeline.reorderForPreVertexCache(b.geometry)):t.defined(b.westHemisphereGeometry)&&t.defined(b.eastHemisphereGeometry)&&(p.GeometryPipeline.reorderForPostVertexCache(b.westHemisphereGeometry),p.GeometryPipeline.reorderForPreVertexCache(b.westHemisphereGeometry),p.GeometryPipeline.reorderForPostVertexCache(b.eastHemisphereGeometry),p.GeometryPipeline.reorderForPreVertexCache(b.eastHemisphereGeometry))}var x=p.GeometryPipeline.combineInstances(d);for(v=x.length,n=0;n<v;++n){o=x[n];var G,S=o.attributes;if(c)for(G in S)S.hasOwnProperty(G)&&S[G].componentDatatype===a.ComponentDatatype.DOUBLE&&p.GeometryPipeline.encodeAttribute(o,G,G+"3DHigh",G+"3DLow");else for(G in S)if(S.hasOwnProperty(G)&&S[G].componentDatatype===a.ComponentDatatype.DOUBLE){var P=G+"3D",k=G+"2D";p.GeometryPipeline.projectTo2D(o,G,P,k,f),t.defined(o.boundingSphere)&&"position"===G&&(o.boundingSphereCV=i.BoundingSphere.fromVertices(o.attributes.position2D.values)),p.GeometryPipeline.encodeAttribute(o,P,P+"High",P+"Low"),p.GeometryPipeline.encodeAttribute(o,k,k+"High",k+"Low")}g&&p.GeometryPipeline.compressVertices(o)}if(!u){var C=[];for(v=x.length,n=0;n<v;++n)o=x[n],C=C.concat(p.GeometryPipeline.fitToUnsignedShortIndices(o));x=C}return x}function y(e,r,n,i){var o,a,s,d=i.length-1;if(d>=0){var p=i[d];o=p.offset+p.count,s=p.index,a=n[s].indices.length}else o=0,s=0,a=n[s].indices.length;for(var f=e.length,u=0;u<f;++u){var c=e[u],m=c[r];if(t.defined(m)){var h=m.indices.length;o+h>a&&(o=0,a=n[++s].indices.length),i.push({index:s,offset:o,count:h}),o+=h}}}function v(e,t){var r=[];return y(e,"geometry",t,r),y(e,"westHemisphereGeometry",t,r),y(e,"eastHemisphereGeometry",t,r),r}Object.defineProperties(c.prototype,{componentDatatype:{get:function(){return a.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),c.fromCartesian3=function(e){return r.Check.defined("offset",e),new c(e.x,e.y,e.z)},c.toValue=function(e,n){return r.Check.defined("offset",e),t.defined(n)||(n=new Float32Array([e.x,e.y,e.z])),n[0]=e.x,n[1]=e.y,n[2]=e.z,n};var b={};function x(e,r){var n=e.attributes;for(var i in n)if(n.hasOwnProperty(i)){var o=n[i];t.defined(o)&&t.defined(o.values)&&r.push(o.values.buffer)}t.defined(e.indices)&&r.push(e.indices.buffer)}function G(e,t){for(var r=e.length,n=0;n<r;++n)x(e[n],t)}function S(e){for(var r=1,n=e.length,o=0;o<n;o++){var a=e[o];if(++r,t.defined(a)){var s=a.attributes;for(var d in r+=7+2*i.BoundingSphere.packedLength+(t.defined(a.indices)?a.indices.length:0),s)if(s.hasOwnProperty(d)&&t.defined(s[d])){var p=s[d];r+=5+p.values.length}}}return r}function P(e,r){var n=e.length,i=new Float64Array(1+19*n),a=0;i[a++]=n;for(var s=0;s<n;s++){var d=e[s];if(o.Matrix4.pack(d.modelMatrix,i,a),a+=o.Matrix4.packedLength,t.defined(d.attributes)&&t.defined(d.attributes.offset)){var p=d.attributes.offset.value;i[a]=p[0],i[a+1]=p[1],i[a+2]=p[2]}a+=3}return r.push(i.buffer),i}function k(e){var r=e,n=new Array(r[0]),i=0,a=1;while(a<r.length){var s,d=o.Matrix4.unpack(r,a);a+=o.Matrix4.packedLength,t.defined(r[a])&&(s={offset:new c(r[a],r[a+1],r[a+2])}),a+=3,n[i++]={modelMatrix:d,attributes:s}}return n}function C(e){var r=e.length,n=1+(i.BoundingSphere.packedLength+1)*r,o=new Float32Array(n),a=0;o[a++]=r;for(var s=0;s<r;++s){var d=e[s];t.defined(d)?(o[a++]=1,i.BoundingSphere.pack(e[s],o,a)):o[a++]=0,a+=i.BoundingSphere.packedLength}return o}function w(e){var t=new Array(e[0]),r=0,n=1;while(n<e.length)1===e[n++]&&(t[r]=i.BoundingSphere.unpack(e,n)),++r,n+=i.BoundingSphere.packedLength;return t}b.combineGeometry=function(e){var r,n,o,a,s=e.instances,d=s.length,f=!1;d>0&&(r=g(e),r.length>0&&(n=p.GeometryPipeline.createAttributeLocations(r[0]),e.createPickOffsets&&(o=v(s,r))),t.defined(s[0].attributes)&&t.defined(s[0].attributes.offset)&&(a=new Array(d),f=!0));for(var u=new Array(d),c=new Array(d),m=0;m<d;++m){var h=s[m],l=h.geometry;t.defined(l)&&(u[m]=l.boundingSphere,c[m]=l.boundingSphereCV,f&&(a[m]=h.geometry.offsetAttribute));var y=h.eastHemisphereGeometry,b=h.westHemisphereGeometry;t.defined(y)&&t.defined(b)&&(t.defined(y.boundingSphere)&&t.defined(b.boundingSphere)&&(u[m]=i.BoundingSphere.union(y.boundingSphere,b.boundingSphere)),t.defined(y.boundingSphereCV)&&t.defined(b.boundingSphereCV)&&(c[m]=i.BoundingSphere.union(y.boundingSphereCV,b.boundingSphereCV)))}return{geometries:r,modelMatrix:e.modelMatrix,attributeLocations:n,pickOffsets:o,offsetInstanceExtend:a,boundingSpheres:u,boundingSpheresCV:c}},b.packCreateGeometryResults=function(e,r){var n=new Float64Array(S(e)),o=[],a={},s=e.length,d=0;n[d++]=s;for(var p=0;p<s;p++){var f=e[p],u=t.defined(f);if(n[d++]=u?1:0,u){n[d++]=f.primitiveType,n[d++]=f.geometryType,n[d++]=t.defaultValue(f.offsetAttribute,-1);var c=t.defined(f.boundingSphere)?1:0;n[d++]=c,c&&i.BoundingSphere.pack(f.boundingSphere,n,d),d+=i.BoundingSphere.packedLength;var m=t.defined(f.boundingSphereCV)?1:0;n[d++]=m,m&&i.BoundingSphere.pack(f.boundingSphereCV,n,d),d+=i.BoundingSphere.packedLength;var h=f.attributes,l=[];for(var g in h)h.hasOwnProperty(g)&&t.defined(h[g])&&(l.push(g),t.defined(a[g])||(a[g]=o.length,o.push(g)));n[d++]=l.length;for(var y=0;y<l.length;y++){var v=l[y],b=h[v];n[d++]=a[v],n[d++]=b.componentDatatype,n[d++]=b.componentsPerAttribute,n[d++]=b.normalize?1:0,n[d++]=b.values.length,n.set(b.values,d),d+=b.values.length}var x=t.defined(f.indices)?f.indices.length:0;n[d++]=x,x>0&&(n.set(f.indices,d),d+=x)}}return r.push(n.buffer),{stringTable:o,packedData:n}},b.unpackCreateGeometryResults=function(e){var t,r=e.stringTable,n=e.packedData,o=new Array(n[0]),p=0,u=1;while(u<n.length){var c=1===n[u++];if(c){var m,h,l=n[u++],g=n[u++],y=n[u++];-1===y&&(y=void 0);var v=1===n[u++];v&&(m=i.BoundingSphere.unpack(n,u)),u+=i.BoundingSphere.packedLength;var b,x,G,S=1===n[u++];S&&(h=i.BoundingSphere.unpack(n,u)),u+=i.BoundingSphere.packedLength;var P,k=new d.GeometryAttributes,C=n[u++];for(t=0;t<C;t++){var w=r[n[u++]],A=n[u++];G=n[u++];var V=0!==n[u++];b=n[u++],x=a.ComponentDatatype.createTypedArray(A,b);for(var D=0;D<b;D++)x[D]=n[u++];k[w]=new s.GeometryAttribute({componentDatatype:A,componentsPerAttribute:G,normalize:V,values:x})}if(b=n[u++],b>0){var O=x.length/G;for(P=f.IndexDatatype.createTypedArray(O,b),t=0;t<b;t++)P[t]=n[u++]}o[p++]=new s.Geometry({primitiveType:l,geometryType:g,boundingSphere:m,boundingSphereCV:h,indices:P,attributes:k,offsetAttribute:y})}else o[p++]=void 0}return o},b.packCombineGeometryParameters=function(e,t){for(var r=e.createGeometryResults,n=r.length,o=0;o<n;o++)t.push(r[o].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:P(e.instances,t),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof i.GeographicProjection,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},b.unpackCombineGeometryParameters=function(e){for(var t=k(e.packedInstances),r=e.createGeometryResults,a=r.length,s=0,d=0;d<a;d++)for(var p=b.unpackCreateGeometryResults(r[d]),f=p.length,c=0;c<f;c++){var m=p[c],h=t[s];h.geometry=m,++s}var l=n.Ellipsoid.clone(e.ellipsoid),g=e.isGeographic?new i.GeographicProjection(l):new u.WebMercatorProjection(l);return{instances:t,ellipsoid:l,projection:g,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:o.Matrix4.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},b.packCombineGeometryResults=function(e,r){t.defined(e.geometries)&&G(e.geometries,r);var n=C(e.boundingSpheres),i=C(e.boundingSpheresCV);return r.push(n.buffer,i.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:n,boundingSpheresCV:i}},b.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:w(e.boundingSpheres),boundingSpheresCV:w(e.boundingSpheresCV)}},e.PrimitivePipeline=b}));