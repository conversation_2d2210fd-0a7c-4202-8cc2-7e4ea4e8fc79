/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./ArcType-e42cfb05","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Color-bcdd0092","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolylinePipeline-1a06b90f","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,o,t,l,r,n,i,a,s,c,p,d,f,y,u,h,C,g,T,m){"use strict";function b(e,o,t,l,r,i,a){const s=d.PolylinePipeline.numberOfPoints(e,o,r);let c;const p=t.red,f=t.green,y=t.blue,u=t.alpha,h=l.red,C=l.green,g=l.blue,T=l.alpha;if(n.Color.equals(t,l)){for(c=0;c<s;c++)i[a++]=n.Color.floatToByte(p),i[a++]=n.Color.floatToByte(f),i[a++]=n.Color.floatToByte(y),i[a++]=n.Color.floatToByte(u);return a}const m=(h-p)/s,b=(C-f)/s,P=(g-y)/s,_=(T-u)/s;let B=a;for(c=0;c<s;c++)i[B++]=n.Color.floatToByte(p+c*m),i[B++]=n.Color.floatToByte(f+c*b),i[B++]=n.Color.floatToByte(y+c*P),i[B++]=n.Color.floatToByte(u+c*_);return B}function P(l){const i=(l=e.defaultValue(l,e.defaultValue.EMPTY_OBJECT)).positions,a=l.colors,s=e.defaultValue(l.colorsPerVertex,!1);this._positions=i,this._colors=a,this._colorsPerVertex=s,this._arcType=e.defaultValue(l.arcType,t.ArcType.GEODESIC),this._granularity=e.defaultValue(l.granularity,p.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(l.ellipsoid,o.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";let c=1+i.length*r.Cartesian3.packedLength;c+=e.defined(a)?1+a.length*n.Color.packedLength:1,this.packedLength=c+o.Ellipsoid.packedLength+3}P.pack=function(t,l,i){let a;i=e.defaultValue(i,0);const s=t._positions;let c=s.length;for(l[i++]=c,a=0;a<c;++a,i+=r.Cartesian3.packedLength)r.Cartesian3.pack(s[a],l,i);const p=t._colors;for(c=e.defined(p)?p.length:0,l[i++]=c,a=0;a<c;++a,i+=n.Color.packedLength)n.Color.pack(p[a],l,i);return o.Ellipsoid.pack(t._ellipsoid,l,i),i+=o.Ellipsoid.packedLength,l[i++]=t._colorsPerVertex?1:0,l[i++]=t._arcType,l[i]=t._granularity,l},P.unpack=function(t,l,i){let a;l=e.defaultValue(l,0);let s=t[l++];const c=new Array(s);for(a=0;a<s;++a,l+=r.Cartesian3.packedLength)c[a]=r.Cartesian3.unpack(t,l);s=t[l++];const p=s>0?new Array(s):void 0;for(a=0;a<s;++a,l+=n.Color.packedLength)p[a]=n.Color.unpack(t,l);const d=o.Ellipsoid.unpack(t,l);l+=o.Ellipsoid.packedLength;const f=1===t[l++],y=t[l++],u=t[l];return e.defined(i)?(i._positions=c,i._colors=p,i._ellipsoid=d,i._colorsPerVertex=f,i._arcType=y,i._granularity=u,i):new P({positions:c,colors:p,ellipsoid:d,colorsPerVertex:f,arcType:y,granularity:u})};const _=new Array(2),B=new Array(2),E={positions:_,height:B,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return P.createGeometry=function(o){const f=o._positions,y=o._colors,u=o._colorsPerVertex,h=o._arcType,C=o._granularity,g=o._ellipsoid,T=p.CesiumMath.chordLength(C,g.maximumRadius),m=e.defined(y)&&!u;let P;const A=f.length;let k,G,D,L,w=0;if(h===t.ArcType.GEODESIC||h===t.ArcType.RHUMB){let o,l,r;h===t.ArcType.GEODESIC?(o=p.CesiumMath.chordLength(C,g.maximumRadius),l=d.PolylinePipeline.numberOfPoints,r=d.PolylinePipeline.generateArc):(o=C,l=d.PolylinePipeline.numberOfPointsRhumbLine,r=d.PolylinePipeline.generateRhumbArc);const i=d.PolylinePipeline.extractHeights(f,g),a=E;if(h===t.ArcType.GEODESIC?a.minDistance=T:a.granularity=C,a.ellipsoid=g,m){let t=0;for(P=0;P<A-1;P++)t+=l(f[P],f[P+1],o)+1;k=new Float64Array(3*t),D=new Uint8Array(4*t),a.positions=_,a.height=B;let s=0;for(P=0;P<A-1;++P){_[0]=f[P],_[1]=f[P+1],B[0]=i[P],B[1]=i[P+1];const o=r(a);if(e.defined(y)){const e=o.length/3;L=y[P];for(let o=0;o<e;++o)D[s++]=n.Color.floatToByte(L.red),D[s++]=n.Color.floatToByte(L.green),D[s++]=n.Color.floatToByte(L.blue),D[s++]=n.Color.floatToByte(L.alpha)}k.set(o,w),w+=o.length}}else if(a.positions=f,a.height=i,k=new Float64Array(r(a)),e.defined(y)){for(D=new Uint8Array(k.length/3*4),P=0;P<A-1;++P){w=b(f[P],f[P+1],y[P],y[P+1],T,D,w)}const e=y[A-1];D[w++]=n.Color.floatToByte(e.red),D[w++]=n.Color.floatToByte(e.green),D[w++]=n.Color.floatToByte(e.blue),D[w++]=n.Color.floatToByte(e.alpha)}}else{G=m?2*A-2:A,k=new Float64Array(3*G),D=e.defined(y)?new Uint8Array(4*G):void 0;let o=0,t=0;for(P=0;P<A;++P){const l=f[P];if(m&&P>0&&(r.Cartesian3.pack(l,k,o),o+=3,L=y[P-1],D[t++]=n.Color.floatToByte(L.red),D[t++]=n.Color.floatToByte(L.green),D[t++]=n.Color.floatToByte(L.blue),D[t++]=n.Color.floatToByte(L.alpha)),m&&P===A-1)break;r.Cartesian3.pack(l,k,o),o+=3,e.defined(y)&&(L=y[P],D[t++]=n.Color.floatToByte(L.red),D[t++]=n.Color.floatToByte(L.green),D[t++]=n.Color.floatToByte(L.blue),D[t++]=n.Color.floatToByte(L.alpha))}}const V=new s.GeometryAttributes;V.position=new a.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:k}),e.defined(y)&&(V.color=new a.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:D,normalize:!0})),G=k.length/3;const R=2*(G-1),x=c.IndexDatatype.createTypedArray(G,R);let S=0;for(P=0;P<G-1;++P)x[S++]=P,x[S++]=P+1;return new a.Geometry({attributes:V,indices:x,primitiveType:a.PrimitiveType.LINES,boundingSphere:l.BoundingSphere.fromPoints(f)})},function(t,l){return e.defined(l)&&(t=P.unpack(t,l)),t._ellipsoid=o.Ellipsoid.clone(t._ellipsoid),P.createGeometry(t)}}));
