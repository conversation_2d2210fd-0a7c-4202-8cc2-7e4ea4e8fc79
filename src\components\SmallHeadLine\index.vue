<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-07 17:05:51
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-22 14:36:30
 * @FilePath: \bigscreen-qj-web\src\components\SmallHeadLine\index.vue
 * @Description: 
 * 
-->
<template>
  <div class="small-headline-container">
    <div class="headline-title">
      <slot name="title"></slot>
    </div>

    <div class="headline-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
  import { getAssetsFile } from '@/utils'

  const props = defineProps({
    /**
     * @description: 标题背景图片路径
     */
    titleBg: {
      type: String,
      default: 'smallheadline.png'
    },
    /**
     * @description: 标题背景图片宽度
     */
    width: {
      type: String,
      default: '385px'
    }
  })
  const bg = computed(() => {
    return `url(${getAssetsFile(props.titleBg)})`
  })
</script>
<style lang="scss" scoped>
  .small-headline-container {
    position: relative;
    width: v-bind(width);
    height: 23px;
    font-family: YouSheBiaoTiHei;
    font-size: 21px;
    font-weight: 400;
    line-height: 15px;
    color: #ecf6ff;
    background: v-bind('bg');
    background-size: v-bind(width) 23px;
    .headline-title {
      width: 140px;
      margin: 0 auto;
      text-align: center;
    }
    .headline-content {
      position: absolute;
      top: 16px;

      // margin-top: 16px;
    }
  }
</style>
