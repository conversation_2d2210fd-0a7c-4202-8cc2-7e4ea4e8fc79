/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Transforms-42ed7720","./BoxGeometry-64685411","./Cartesian3-bb0e6278","./Color-bcdd0092","./CylinderGeometry-6468d7ab","./defined-3b3eb2ba","./EllipsoidGeometry-539f6ba4","./IndexDatatype-00859b8b","./Rectangle-9bffefe4","./createTaskProcessorWorker","./Math-b5f4d889","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./VertexFormat-86c096b8","./CylinderGeometryLibrary-76efdd62"],(function(e,t,n,r,a,i,o,s,d,c,l,f,u,h,b,p,y,g,x,m,C){"use strict";function I(e){this.offset=e.offset,this.count=e.count,this.color=e.color,this.batchIds=e.batchIds}const k=new n.Cartesian3,M=d.Matrix4.packedLength+n.Cartesian3.packedLength,B=d.Matrix4.packedLength+2,w=d.Matrix4.packedLength+n.Cartesian3.packedLength,A=n.Cartesian3.packedLength+1,O={modelMatrix:new d.Matrix4,boundingVolume:new e.BoundingSphere};function L(e,t){let r=t*M;const a=n.Cartesian3.unpack(e,r,k);r+=n.Cartesian3.packedLength;const i=d.Matrix4.unpack(e,r,O.modelMatrix);d.Matrix4.multiplyByScale(i,a,i);const o=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,o.center),o.radius=Math.sqrt(3),O}function v(e,t){let r=t*B;const a=e[r++],i=e[r++],o=n.Cartesian3.fromElements(a,a,i,k),s=d.Matrix4.unpack(e,r,O.modelMatrix);d.Matrix4.multiplyByScale(s,o,s);const c=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,c.center),c.radius=Math.sqrt(2),O}function E(e,t){let r=t*w;const a=n.Cartesian3.unpack(e,r,k);r+=n.Cartesian3.packedLength;const i=d.Matrix4.unpack(e,r,O.modelMatrix);d.Matrix4.multiplyByScale(i,a,i);const o=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,o.center),o.radius=1,O}function U(e,t){let r=t*A;const a=e[r++],i=n.Cartesian3.unpack(e,r,k),o=d.Matrix4.fromTranslation(i,O.modelMatrix);d.Matrix4.multiplyByUniformScale(o,a,o);const s=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=1,O}const G=new n.Cartesian3;function S(t,a,o,s,c){if(!i.defined(a))return;const l=o.length,f=s.attributes.position.values,u=s.indices,h=t.positions,b=t.vertexBatchIds,p=t.indices,y=t.batchIds,g=t.batchTableColors,x=t.batchedIndices,m=t.indexOffsets,C=t.indexCounts,k=t.boundingVolumes,M=t.modelMatrix,B=t.center;let w=t.positionOffset,A=t.batchIdIndex,O=t.indexOffset;const L=t.batchedIndicesOffset;for(let t=0;t<l;++t){const i=c(a,t),s=i.modelMatrix;d.Matrix4.multiply(M,s,s);const l=o[t],v=f.length;for(let e=0;e<v;e+=3){const t=n.Cartesian3.unpack(f,e,G);d.Matrix4.multiplyByPoint(s,t,t),n.Cartesian3.subtract(t,B,t),n.Cartesian3.pack(t,h,3*w+e),b[A++]=l}const E=u.length;for(let e=0;e<E;++e)p[O+e]=u[e]+w;const U=t+L;x[U]=new I({offset:O,count:E,color:r.Color.fromRgba(g[l]),batchIds:[l]}),y[U]=l,m[U]=O,C[U]=E,k[U]=e.BoundingSphere.transform(i.boundingVolume,s),w+=v/3,O+=E}t.positionOffset=w,t.batchIdIndex=A,t.indexOffset=O,t.batchedIndicesOffset+=l}const R=new n.Cartesian3,T=new d.Matrix4;function V(t,n,a){const i=a.length,o=2+i*e.BoundingSphere.packedLength+1+function(e){const t=e.length;let n=0;for(let a=0;a<t;++a)n+=r.Color.packedLength+3+e[a].batchIds.length;return n}(n),s=new Float64Array(o);let d=0;s[d++]=t,s[d++]=i;for(let t=0;t<i;++t)e.BoundingSphere.pack(a[t],s,d),d+=e.BoundingSphere.packedLength;const c=n.length;s[d++]=c;for(let e=0;e<c;++e){const t=n[e];r.Color.pack(t.color,s,d),d+=r.Color.packedLength,s[d++]=t.offset,s[d++]=t.count;const a=t.batchIds,i=a.length;s[d++]=i;for(let e=0;e<i;++e)s[d++]=a[e]}return s}return c((function(e,r){const c=i.defined(e.boxes)?new Float32Array(e.boxes):void 0,l=i.defined(e.boxBatchIds)?new Uint16Array(e.boxBatchIds):void 0,f=i.defined(e.cylinders)?new Float32Array(e.cylinders):void 0,u=i.defined(e.cylinderBatchIds)?new Uint16Array(e.cylinderBatchIds):void 0,h=i.defined(e.ellipsoids)?new Float32Array(e.ellipsoids):void 0,b=i.defined(e.ellipsoidBatchIds)?new Uint16Array(e.ellipsoidBatchIds):void 0,p=i.defined(e.spheres)?new Float32Array(e.spheres):void 0,y=i.defined(e.sphereBatchIds)?new Uint16Array(e.sphereBatchIds):void 0,g=i.defined(c)?l.length:0,x=i.defined(f)?u.length:0,m=i.defined(h)?b.length:0,C=i.defined(p)?y.length:0,I=t.BoxGeometry.getUnitBox(),k=a.CylinderGeometry.getUnitCylinder(),M=o.EllipsoidGeometry.getUnitEllipsoid(),B=I.attributes.position.values,w=k.attributes.position.values,A=M.attributes.position.values;let O=B.length*g;O+=w.length*x,O+=A.length*(m+C);const G=I.indices,F=k.indices,Z=M.indices;let D=G.length*g;D+=F.length*x,D+=Z.length*(m+C);const P=new Float32Array(O),q=new Uint16Array(O/3),W=s.IndexDatatype.createTypedArray(O/3,D),_=g+x+m+C,N=new Uint16Array(_),Y=new Array(_),j=new Uint32Array(_),z=new Uint32Array(_),H=new Array(_);!function(e){const t=new Float64Array(e);let r=0;n.Cartesian3.unpack(t,r,R),r+=n.Cartesian3.packedLength,d.Matrix4.unpack(t,r,T)}(e.packedBuffer);const J={batchTableColors:new Uint32Array(e.batchTableColors),positions:P,vertexBatchIds:q,indices:W,batchIds:N,batchedIndices:Y,indexOffsets:j,indexCounts:z,boundingVolumes:H,positionOffset:0,batchIdIndex:0,indexOffset:0,batchedIndicesOffset:0,modelMatrix:T,center:R};S(J,c,l,I,L),S(J,f,u,k,v),S(J,h,b,M,E),S(J,p,y,M,U);const K=V(W.BYTES_PER_ELEMENT,Y,H);return r.push(P.buffer,q.buffer,W.buffer),r.push(N.buffer,j.buffer,z.buffer),r.push(K.buffer),{positions:P.buffer,vertexBatchIds:q.buffer,indices:W.buffer,indexOffsets:j.buffer,indexCounts:z.buffer,batchIds:N.buffer,packedBuffer:K.buffer}}))}));
