<template>
  <div class="building-info-container">
    <div class="building-info-title">
      基本信息
      <img class="absolute right-20 top-16 cursor-pointer" src="@/assets/CommonPopup/closeIcon.png" @click="handleClose" />
    </div>
    <div class="building-info-content">
      <div class="building-info-content-item" v-for="(item, index) in list" :key="index">
        <div class="building-info-content-item-label">{{ item.label }}</div>
        <div class="building-info-content-item-value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps<{
    handleClose?: () => void
  }>()

  const handleClose = () => {
    props.handleClose?.()
  }

  const list = ref([
    { label: '管理站占地面积', value: '18000平方米' },
    { label: '办公楼建筑面积', value: '5444平方米' },
    { label: '宿舍楼建筑面积', value: '2830平方米' },
    { label: '办公楼高度', value: '20.7m' },
    { label: '办公楼层数', value: '4层' },
    { label: '办公楼结构形式', value: '框架结构' },
    { label: '设计使用年限', value: '50年' },
    { label: '抗震设防烈度', value: '6度' }
  ])
</script>

<style scoped>
  .building-info-container {
    position: absolute;
    width: 381px;
    height: 353px;
    padding: 10px 15px 20px 20px;
    background: url('@/assets/CommonPopup/buildingBg.png');
    background-size: cover;
    .building-info-title {
      position: relative;
      width: 100%;
      height: 32px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      line-height: 32px;
      color: #ffffff;
    }
    .building-info-content {
      width: 300px;
      height: 280px;
      margin-top: 10px;
      overflow: hidden;
      .building-info-content-item {
        display: flex;
        gap: 8px;
        height: 30px;
        font-family: Alibaba-PuHuiTi;
        font-size: 16px;
        font-weight: 400;
        line-height: 30px;
        .building-info-content-item-label {
          color: #dbefff;
        }
        .building-info-content-item-value {
          color: #5bb5ff;
        }
      }
    }
  }
</style>
