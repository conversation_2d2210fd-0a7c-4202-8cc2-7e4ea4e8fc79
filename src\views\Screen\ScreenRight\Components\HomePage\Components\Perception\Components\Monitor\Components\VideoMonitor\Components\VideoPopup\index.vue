<template>
  <PopupBg width="1573px" height="728px">
    <!-- <videoPlay type="m3u8" width="1485px" height="630px" :src="url" auto-play muted :control="false"></videoPlay> -->
    <VideoPlayer :height="630" :code="code" :name="name"></VideoPlayer>
  </PopupBg>
</template>

<script setup lang="ts">
  import PopupBg from '@/components/PopupBg/index.vue'
  import VideoPlayer from '@/views/Screen/StructureDiagram/Components/VideoWall/components/videoPlayer.vue'

  const props = withDefaults(
    defineProps<{
      code: string
      name: string
    }>(),
    {
      code: '',
      name: ''
    }
  )
</script>

<style scoped>
  :deep(.d-player-wrap) {
    background-color: transparent !important;
  }
</style>
