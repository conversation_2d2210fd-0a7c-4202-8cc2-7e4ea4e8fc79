/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./arrayRemoveDuplicates-5b666c82","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Math-b5f4d889","./PolylinePipeline-1a06b90f"],(function(e,t,i,n,o,r,a){"use strict";const s={};function l(e,t){return r.CesiumMath.equalsEpsilon(e.latitude,t.latitude,r.CesiumMath.EPSILON10)&&r.CesiumMath.equalsEpsilon(e.longitude,t.longitude,r.CesiumMath.EPSILON10)}const h=new n.Cartographic,c=new n.Cartographic;const g=new Array(2),u=new Array(2),p={positions:void 0,height:void 0,granularity:void 0,ellipsoid:void 0};s.computePositions=function(e,s,d,f,y,m){const P=function(e,r,a,s){const g=(r=t.arrayRemoveDuplicates(r,i.Cartesian3.equalsEpsilon)).length;if(g<2)return;const u=o.defined(s),p=o.defined(a),d=new Array(g),f=new Array(g),y=new Array(g),m=r[0];d[0]=m;const P=e.cartesianToCartographic(m,h);p&&(P.height=a[0]),f[0]=P.height,y[0]=u?s[0]:0;let b=f[0]===y[0],A=1;for(let t=1;t<g;++t){const i=r[t],o=e.cartesianToCartographic(i,c);p&&(o.height=a[t]),b=b&&0===o.height,l(P,o)?P.height<o.height&&(f[A-1]=o.height):(d[A]=i,f[A]=o.height,y[A]=u?s[t]:0,b=b&&f[A]===y[A],n.Cartographic.clone(o,P),++A)}return b||A<2?void 0:(d.length=A,f.length=A,y.length=A,{positions:d,topHeights:f,bottomHeights:y})}(e,s,d,f);if(!o.defined(P))return;s=P.positions,d=P.topHeights,f=P.bottomHeights;const b=s.length,A=b-2;let C,w;const v=r.CesiumMath.chordLength(y,e.maximumRadius),M=p;if(M.minDistance=v,M.ellipsoid=e,m){let e,t=0;for(e=0;e<b-1;e++)t+=a.PolylinePipeline.numberOfPoints(s[e],s[e+1],v)+1;C=new Float64Array(3*t),w=new Float64Array(3*t);const i=g,n=u;M.positions=i,M.height=n;let o=0;for(e=0;e<b-1;e++){i[0]=s[e],i[1]=s[e+1],n[0]=d[e],n[1]=d[e+1];const t=a.PolylinePipeline.generateArc(M);C.set(t,o),n[0]=f[e],n[1]=f[e+1],w.set(a.PolylinePipeline.generateArc(M),o),o+=t.length}}else M.positions=s,M.height=d,C=new Float64Array(a.PolylinePipeline.generateArc(M)),M.height=f,w=new Float64Array(a.PolylinePipeline.generateArc(M));return{bottomPositions:w,topPositions:C,numCorners:A}};var d=s;e.WallGeometryLibrary=d}));
