<template>
  <div class="car-details-title">北线(荣成路-延东隧道)</div>

  <el-carousel height="60px" indicator-position="none">
    <el-carousel-item v-for="parent in list" :key="parent.id" arrow="never" :interval="5000">
      <div class="car-details-container">
        <div class="car-details-item" v-for="item in parent" :key="item.id">
          <div class="car-icon"></div>
          <div class="car-content">
            <div class="number">{{ item.number }}</div>
            <div class="car-name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
</template>

<script setup>
  import { ref, watch } from 'vue'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  console.log('props.data____首页的交通态势', props.data)

  const list = ref([
    [
      { id: 1, name: '燃油车（辆）', number: 234785 },
      { id: 2, name: '新能源车（辆）', number: 17178 },
      { id: 3, name: '作业车（辆）', number: '-' }
    ],
    [
      { id: 4, name: '大客车（辆）', number: '-' },
      { id: 5, name: '小客车（辆）', number: '-' },
      { id: 6, name: '违禁车（辆）', number: '-' }
    ]
  ])

  watch(
    () => props.data,
    () => {
      // console.log('watch_____首页的交通态势')
      updateList()
    }
  )

  function updateList() {
    // console.log('updateList里面的__props.data', props.data)
    // console.log('updateList里面的__props.data.oilCarCount', props.data.oilCarCount)

    // list[0].number = props.data?.oilCarCount || '-'
    // list[1].number = props.data?.newCarCount || '-'
    list.value[0][0].number = props.data?.oilCarCount ?? '-' // 燃油车（辆）
    list.value[0][1].number = props.data?.newCarCount ?? '-' // 新能源车（辆）
    list.value[0][2].number = props.data?.workCarCount ?? '-' // 作业车

    list.value[1][0].number = props.data?.bigBusCount ?? '-' // 大客车（辆）
    list.value[1][1].number = props.data?.smallBusCount ?? '-' // 小客车（辆）
    list.value[1][2].number = props.data?.violateCarCount ?? '-' // 违禁车（辆）
  }
</script>

<style lang="scss" scoped>
  .car-details-title {
    width: 480px;
    height: 20px;
    padding-left: 22px;
    font-family: Alibaba-PuHuiTi;
    font-size: 16px;
    font-weight: normal;
    line-height: 16px;
    color: #ffffff;
    background: url('@/assets/ScreenLeft/TrafficSituation/subbg.png');
  }
  .car-details-container {
    display: flex;
    justify-content: space-between;
    width: 480px;
    height: 60px;
    .car-details-item {
      display: flex;

      //width: 200px;
      flex: 1;
      height: 48px;
      margin-top: 10px;
      .car-icon {
        width: 48px;
        height: 48px;
        margin-right: 7px;
        background: url('@/assets/ScreenLeft/TrafficSituation/carIcon.png');
      }
      .car-content {
        //width: 150px;
        height: 48px;
        .number {
          font-family: Alibaba-PuHuiTi;
          font-size: 24px;
          font-weight: bold;
          line-height: 24px;
          color: #ffffff;
        }
        .car-name {
          font-family: Alibaba-PuHuiTi;
          font-size: 14px;
          font-weight: normal;
          line-height: 16px;
          color: #a8d6ff;
        }
      }
    }
  }
  :deep(.el-carousel__indicators--horizontal) {
    top: 20px;
    display: inline-block;
    margin-top: 12px;
  }
  :deep(.el-carousel__button) {
    width: 15px;
    height: 3px;
    background-color: #00bdff;
    border-radius: 10px;
  }
</style>
<style></style>
