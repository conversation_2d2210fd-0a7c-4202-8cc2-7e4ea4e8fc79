{"asset": {"version": "1.0"}, "root": {"boundingVolume": {"box": [16, 16, 16, 16, 0, 0, 0, 16, 0, 0, 0, 16]}, "children": [{"boundingVolume": {"box": [8, 8, 8, 8, 0, 0, 0, 8, 0, 0, 0, 8]}, "children": [{"boundingVolume": {"box": [4, 4, 4, 4, 0, 0, 0, 4, 0, 0, 0, 4]}, "children": [{"boundingVolume": {"box": [2, 2, 2, 2, 0, 0, 0, 2, 0, 0, 0, 2]}, "children": [{"boundingVolume": {"box": [1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1]}, "content": {"uri": "0-0_0-0_0-0_0-0/0-0_0-0_0-0_0-0_leaf.b3dm"}, "geometricError": 0.001}], "content": {"uri": "0-0_0-0_0-0_0-0/0-0_0-0_0-0.b3dm"}, "geometricError": 0.018042195912175804}, {"boundingVolume": {"box": [2, 2, 2, 2, 0, 0, 0, 2, 0, 0, 0, 2]}, "content": {"uri": "0-0_0-0_0-0_0-0/0-0_0-0_0-0_leaf.b3dm"}, "geometricError": 0.001}], "content": {"uri": "0-0_0-0_0-0_0-0/0-0_0-0.b3dm"}, "geometricError": 0.04330127018922193}, {"boundingVolume": {"box": [4, 4, 4, 4, 0, 0, 0, 4, 0, 0, 0, 4]}, "content": {"uri": "0-0_0-0_0-0_0-0/0-0_0-0_leaf.b3dm"}, "geometricError": 0.001}], "content": {"uri": "0-0_0-0_0-0_0-0/0-0.b3dm"}, "geometricError": 0.10825317547305482}, {"boundingVolume": {"box": [8, 8, 8, 8, 0, 0, 0, 8, 0, 0, 0, 8]}, "content": {"uri": "0-0_0-0_0-0_0-0/0-0_leaf.b3dm"}, "geometricError": 0.001}], "content": {"uri": "0-0_0-0_0-0_0-0.b3dm"}, "geometricError": 0.13531646934131852, "refine": "REPLACE"}}