---
description: 
globs: 
alwaysApply: true
---
# Vue 组件目录结构规范

## 核心原则

⚠️ **重要规范**：对于非单文件的子组件，必须统一放到一个文件夹中，该文件夹下 `index.vue` 为组件的入口页面。

## 目录结构规范

### 1. 单文件组件
适用于简单、功能单一的组件：
```
ComponentName.vue          # 简单的单文件组件
```

### 2. 复杂组件目录结构
适用于功能复杂、有子组件或配置文件的组件：
```
ComponentName/
├── index.vue              # 主组件入口文件（必须）
├── helper/                # 配置和数据处理目录
│   └── index.tsx          # 配置文件（表单配置、数据处理等）
├── components/            # 子组件目录
│   ├── SubComponent1/     # 复杂子组件目录
│   │   ├── index.vue      # 子组件入口
│   │   ├── helper/        # 子组件配置（如需要）
│   │   │   └── index.tsx
│   │   └── components/    # 子组件的子组件（如需要）
│   │       └── ...
│   └── SubComponent2.vue  # 简单子组件（单文件）
├── types/                 # 类型定义目录（可选）
│   └── index.ts
├── test-demo.vue          # 组件测试演示页面（可选）
└── README.md              # 组件使用文档（可选）
```

### 3. 页面级组件结构
适用于页面级的复杂组件：
```
PageName/
├── index.vue              # 页面主入口
├── helper/
│   └── index.tsx          # 页面配置（表格配置、API调用等）
├── components/            # 页面子组件
│   ├── FormComponent/     # 表单组件
│   │   ├── index.vue
│   │   └── helper/
│   │       └── index.tsx
│   ├── TableComponent/    # 表格组件
│   │   ├── index.vue
│   │   └── helper/
│   │       └── index.tsx
│   └── SimpleComponent.vue # 简单组件
├── demo.vue               # 功能演示页面（可选）
└── README.md              # 页面说明文档（可选）
```

## 实际应用示例

### 用户管理页面结构
参考 [UserManage](mdc:src/views/userManage) 的实际结构：

```
userManage/
├── index.vue              # 用户管理主页面
├── helper/
│   └── index.tsx          # 表格配置和数据处理
├── components/
│   └── UserForm/          # 用户表单组件
│       ├── index.vue      # 表单组件入口
│       ├── helper/
│       │   └── index.tsx  # 表单配置和验证规则
│       └── components/    # 表单子组件（如需要）
│           └── ...
├── demo.vue               # 功能演示页面
└── README.md              # 说明文档
```

### 表单组件结构示例
```
BaseInfoForm/
├── index.vue              # 表单主组件
├── helper/
│   └── index.tsx          # 表单配置、验证规则
├── components/
│   ├── FormSubtitle/      # 表单分组标题组件
│   │   └── index.vue
│   ├── AddressPicker/     # 地址选择器组件
│   │   ├── index.vue
│   │   └── helper/
│   │       └── index.tsx
│   └── DateRangePicker.vue # 简单日期范围选择器
└── types/
    └── index.ts           # 表单相关类型定义
```

## 文件命名规范

### 1. 目录命名
- **组件目录**：使用 PascalCase
  - ✅ `UserForm/`、`BaseInfo/`、`DataTable/`
  - ❌ `userForm/`、`baseinfo/`、`data-table/`

- **功能目录**：使用 camelCase
  - ✅ `helper/`、`components/`、`types/`
  - ❌ `Helper/`、`Components/`、`Types/`

### 2. 文件命名
- **组件文件**：使用 PascalCase
  - ✅ `UserForm.vue`、`BaseInfo.vue`
  - ❌ `userForm.vue`、`baseinfo.vue`

- **入口文件**：统一使用 `index.vue`
  - ✅ `ComponentName/index.vue`
  - ❌ `ComponentName/ComponentName.vue`

- **配置文件**：统一使用 `helper/index.tsx`
  - ✅ `helper/index.tsx`
  - ❌ `config.tsx`、`setup.tsx`

- **类型文件**：使用 `types/index.ts`
  - ✅ `types/index.ts`
  - ❌ `interface.ts`、`type.ts`

## 导入路径规范

### 1. 组件导入
```typescript
// ✅ 正确：导入复杂组件（有目录结构）
import UserForm from './components/UserForm/index.vue'
import BaseInfo from './components/BaseInfo/index.vue'

// ✅ 正确：导入简单组件（单文件）
import SimpleButton from './components/SimpleButton.vue'
import IconComponent from './components/IconComponent.vue'

// ❌ 错误：不要显式指定index.vue
import UserForm from './components/UserForm/index.vue' // 可以简化
import UserForm from './components/UserForm' // 推荐写法
```

### 2. Helper导入
```typescript
// ✅ 正确：导入helper配置
import { useUserForm } from './helper'
import { useTableConfig } from '../helper'

// ✅ 正确：导入子组件helper
import { useFormValidation } from './components/UserForm/helper'

// ❌ 错误：不要显式指定index.tsx
import { useUserForm } from './helper/index.tsx'
```

### 3. 类型导入
```typescript
// ✅ 正确：导入类型定义
import type { UserFormData, FormMode } from './types'
import type { TableColumn } from '../types'

// ✅ 正确：从组件导入类型
import type { UserFormProps } from './components/UserForm/types'
```

## 组件职责划分

### 1. 入口组件 (index.vue)
- **职责**：组件的主要逻辑和状态管理
- **包含**：
  - 组件属性定义 (Props)
  - 主要业务逻辑
  - 子组件的组合和协调
  - 对外接口 (defineExpose)

```vue
<!-- ComponentName/index.vue -->
<template>
  <div class="component-name">
    <SubComponent1 v-model="data1" />
    <SubComponent2 :config="config2" />
  </div>
</template>

<script setup lang="ts">
  import { useComponentLogic } from './helper'
  import SubComponent1 from './components/SubComponent1/index.vue'
  import SubComponent2 from './components/SubComponent2.vue'
  
  // 组件逻辑
  const { data1, config2, methods } = useComponentLogic()
  
  // 对外暴露
  defineExpose({ methods })
</script>
```

### 2. Helper文件 (helper/index.tsx)
- **职责**：配置、数据处理、业务逻辑
- **包含**：
  - 默认数据定义
  - 配置选项
  - 验证规则
  - 业务逻辑函数
  - Hook函数

```typescript
// ComponentName/helper/index.tsx
import { reactive, computed } from 'vue'

// 默认数据
export const defaultData = {
  field1: '',
  field2: 0
}

// 配置选项
export const options = {
  selectOptions: [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' }
  ]
}

// 主要Hook函数
export const useComponentLogic = () => {
  const data = reactive({ ...defaultData })
  
  const computedValue = computed(() => {
    return data.field1 + data.field2
  })
  
  const methods = {
    reset: () => Object.assign(data, defaultData),
    validate: () => { /* 验证逻辑 */ }
  }
  
  return {
    data,
    computedValue,
    methods
  }
}
```

### 3. 子组件 (components/)
- **职责**：特定功能的实现
- **原则**：
  - 单一职责
  - 可复用性
  - 支持不同模式（如查看/编辑）

## 最佳实践

### 1. 何时使用目录结构
- ✅ **使用目录结构的情况**：
  - 组件有多个子组件
  - 需要复杂的配置文件
  - 有独立的类型定义
  - 需要测试文件
  - 组件逻辑复杂

- ✅ **使用单文件的情况**：
  - 简单的展示组件
  - 功能单一的工具组件
  - 不需要子组件
  - 配置简单

### 2. 组件拆分原则
```typescript
// ✅ 正确：按功能拆分
components/
├── UserForm/           # 用户表单
├── UserTable/          # 用户表格
└── UserActions/        # 用户操作

// ❌ 错误：按技术拆分
components/
├── Forms/
├── Tables/
└── Buttons/
```

### 3. Helper文件组织
```typescript
// ✅ 正确：按功能组织
export const useUserForm = () => {
  // 表单相关逻辑
}

export const useUserValidation = () => {
  // 验证相关逻辑
}

// ❌ 错误：所有逻辑混在一起
export const useUser = () => {
  // 表单、验证、表格、API等所有逻辑
}
```

## 迁移指南

### 从单文件到目录结构
当单文件组件变得复杂时，按以下步骤迁移：

1. **创建目录结构**：
   ```bash
   mkdir ComponentName
   mkdir ComponentName/helper
   mkdir ComponentName/components
   ```

2. **移动主文件**：
   ```bash
   mv ComponentName.vue ComponentName/index.vue
   ```

3. **提取配置**：
   - 将配置、常量、工具函数移到 `helper/index.tsx`
   - 更新导入路径

4. **拆分子组件**：
   - 识别可复用的部分
   - 创建独立的子组件
   - 更新主组件的模板

5. **更新导入**：
   - 更新所有引用该组件的导入路径
   - 从 `./ComponentName.vue` 改为 `./ComponentName/index.vue`

## 质量检查清单

在创建或重构组件时，请检查：

### ✅ 目录结构
- [ ] 复杂组件使用目录结构，简单组件使用单文件
- [ ] 入口文件命名为 `index.vue`
- [ ] helper文件位于 `helper/index.tsx`
- [ ] 子组件位于 `components/` 目录

### ✅ 命名规范
- [ ] 目录名使用 PascalCase
- [ ] 文件名遵循命名规范
- [ ] 导入路径正确且简洁

### ✅ 职责划分
- [ ] 入口组件专注于组合和协调
- [ ] helper文件包含配置和业务逻辑
- [ ] 子组件职责单一且可复用

### ✅ 可维护性
- [ ] 组件结构清晰易懂
- [ ] 配置集中管理
- [ ] 类型定义完整
- [ ] 文档说明充分

遵循此规范可确保项目组件结构的一致性和可维护性。

