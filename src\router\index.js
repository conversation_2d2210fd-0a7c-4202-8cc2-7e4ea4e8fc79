import { createRouter, createWebHashHistory } from 'vue-router'

// import Screen from '@/views/Screen/index.vue'
// import BottomNavigation from '@/views/Screen/ScreenMiddlePage/Components/BottomNavigation/index.vue'
// import TractionEvaluation from '@/views/Screen/ScreenMiddlePage/Components/Emergency/Components/TractionEvaluation.vue'
// import InformationBoard from '@/views/Screen/ScreenMiddlePage/Components/Traffic/Components/Info.vue'
// import InformationBoardLane from '@/views/Screen/ScreenMiddlePage/Components/Traffic/Components/InfoLane.vue'
const routes = [
  {
    path: '/',
    name: 'Screen',
    component: () => import('@/views/Screen/index.vue')
  },
  {
    path: '/right',
    name: 'Right',
    component: () => import('@/views/Screen/ScreenRight/index.vue')
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Screen/test.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue')
  },
  {
    path: '/autoLogin',
    name: 'AutoLogin',
    component: () => import('@/views/Login/autoLogin.vue')
  },
  {
    path: '/testAI',
    name: 'TestAI',
    component: () => import('@/views/test/index.vue')
  },
  {
    path: '/dragHandlerExample',
    name: 'DragHandlerExample',
    component: () => import('@/examples/DragHandlerExample.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
