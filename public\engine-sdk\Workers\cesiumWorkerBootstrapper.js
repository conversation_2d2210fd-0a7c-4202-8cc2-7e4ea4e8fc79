function setTimeout(e){e()}var requirejs,require,define;"undefined"===typeof self&&(self={}),self.onmessage=function(e){var t=e.data;require(t.loaderConfig,[t.workerModule],(function(e){self.onmessage=e,CESIUM_BASE_URL=t.loaderConfig.baseUrl}))},function(global){var req,s,head,baseElement,dataMain,src,interactiveScript,currentlyAddingScript,mainScript,subPath,version="2.1.20",commentRegExp=/(\/\*([\s\S]*?)\*\/|([^:]|^)\/\/(.*)$)/gm,cjsRequireRegExp=/[^.]\s*require\s*\(\s*["']([^'"\s]+)["']\s*\)/g,jsSuffixRegExp=/\.js$/,currDirRegExp=/^\.\//,op=Object.prototype,ostring=op.toString,hasOwn=op.hasOwnProperty,ap=Array.prototype,isBrowser=!("undefined"===typeof window||"undefined"===typeof navigator||!window.document),isWebWorker=!isBrowser&&"undefined"!==typeof importScripts,readyRegExp=isBrowser&&"PLAYSTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/,defContextName="_",isOpera="undefined"!==typeof opera&&"[object Opera]"===opera.toString(),contexts={},cfg={},globalDefQueue=[],useInteractive=!1;function isFunction(e){return"[object Function]"===ostring.call(e)}function isArray(e){return"[object Array]"===ostring.call(e)}function each(e,t){var i;if(e)for(i=0;i<e.length;i+=1)if(e[i]&&t(e[i],i,e))break}function eachReverse(e,t){var i;if(e)for(i=e.length-1;i>-1;i-=1)if(e[i]&&t(e[i],i,e))break}function hasProp(e,t){return hasOwn.call(e,t)}function getOwn(e,t){return hasProp(e,t)&&e[t]}function eachProp(e,t){var i;for(i in e)if(hasProp(e,i)&&t(e[i],i))break}function mixin(e,t,i,r){return t&&eachProp(t,(function(t,n){!i&&hasProp(e,n)||(!r||"object"!==typeof t||!t||isArray(t)||isFunction(t)||t instanceof RegExp?e[n]=t:(e[n]||(e[n]={}),mixin(e[n],t,i,r)))})),e}function bind(e,t){return function(){return t.apply(e,arguments)}}function scripts(){return document.getElementsByTagName("script")}function defaultOnError(e){throw e}function getGlobal(e){if(!e)return e;var t=global;return each(e.split("."),(function(e){t=t[e]})),t}function makeError(e,t,i,r){var n=new Error(t+"\nhttp://requirejs.org/docs/errors.html#"+e);return n.requireType=e,n.requireModules=r,i&&(n.originalError=i),n}if("undefined"===typeof define){if("undefined"!==typeof requirejs){if(isFunction(requirejs))return;cfg=requirejs,requirejs=void 0}"undefined"===typeof require||isFunction(require)||(cfg=require,require=void 0),req=requirejs=function(e,t,i,r){var n,a,o=defContextName;return isArray(e)||"string"===typeof e||(a=e,isArray(t)?(e=t,t=i,i=r):e=[]),a&&a.context&&(o=a.context),n=getOwn(contexts,o),n||(n=contexts[o]=req.s.newContext(o)),a&&n.configure(a),n.require(e,t,i)},req.config=function(e){return req(e)},req.nextTick="undefined"!==typeof setTimeout?function(e){setTimeout(e,4)}:function(e){e()},require||(require=req),req.version=version,req.jsExtRegExp=/^\/|:|\?|\.js$/,req.isBrowser=isBrowser,s=req.s={contexts:contexts,newContext:newContext},req({}),each(["toUrl","undef","defined","specified"],(function(e){req[e]=function(){var t=contexts[defContextName];return t.require[e].apply(t,arguments)}})),isBrowser&&(head=s.head=document.getElementsByTagName("head")[0],baseElement=document.getElementsByTagName("base")[0],baseElement&&(head=s.head=baseElement.parentNode)),req.onError=defaultOnError,req.createNode=function(e,t,i){var r=e.xhtml?document.createElementNS("http://www.w3.org/1999/xhtml","html:script"):document.createElement("script");return r.type=e.scriptType||"text/javascript",r.charset="utf-8",r.async=!0,r},req.load=function(e,t,i){var r,n=e&&e.config||{};if(isBrowser)return r=req.createNode(n,t,i),n.onNodeCreated&&n.onNodeCreated(r,n,t,i),r.setAttribute("data-requirecontext",e.contextName),r.setAttribute("data-requiremodule",t),!r.attachEvent||r.attachEvent.toString&&r.attachEvent.toString().indexOf("[native code")<0||isOpera?(r.addEventListener("load",e.onScriptLoad,!1),r.addEventListener("error",e.onScriptError,!1)):(useInteractive=!0,r.attachEvent("onreadystatechange",e.onScriptLoad)),r.src=i,currentlyAddingScript=r,baseElement?head.insertBefore(r,baseElement):head.appendChild(r),currentlyAddingScript=null,r;if(isWebWorker)try{importScripts(i),e.completeLoad(t)}catch(a){e.onError(makeError("importscripts","importScripts failed for "+t+" at "+i,a,[t]))}},isBrowser&&!cfg.skipDataMain&&eachReverse(scripts(),(function(e){if(head||(head=e.parentNode),dataMain=e.getAttribute("data-main"),dataMain)return mainScript=dataMain,cfg.baseUrl||(src=mainScript.split("/"),mainScript=src.pop(),subPath=src.length?src.join("/")+"/":"./",cfg.baseUrl=subPath),mainScript=mainScript.replace(jsSuffixRegExp,""),req.jsExtRegExp.test(mainScript)&&(mainScript=dataMain),cfg.deps=cfg.deps?cfg.deps.concat(mainScript):[mainScript],!0})),define=function(e,t,i){var r,n;"string"!==typeof e&&(i=t,t=e,e=null),isArray(t)||(i=t,t=null),!t&&isFunction(i)&&(t=[],i.length&&(i.toString().replace(commentRegExp,"").replace(cjsRequireRegExp,(function(e,i){t.push(i)})),t=(1===i.length?["require"]:["require","exports","module"]).concat(t))),useInteractive&&(r=currentlyAddingScript||getInteractiveScript(),r&&(e||(e=r.getAttribute("data-requiremodule")),n=contexts[r.getAttribute("data-requirecontext")])),n?(n.defQueue.push([e,t,i]),n.defQueueMap[e]=!0):globalDefQueue.push([e,t,i])},define.amd={jQuery:!0},req.exec=function(text){return eval(text)},req(cfg)}function newContext(e){var t,i,r,n,a,o={waitSeconds:7,baseUrl:"./",paths:{},bundles:{},pkgs:{},shim:{},config:{}},s={},u={},c={},d=[],p={},f={},l={},h=1,m=1;function g(e){var t,i;for(t=0;t<e.length;t++)if(i=e[t],"."===i)e.splice(t,1),t-=1;else if(".."===i){if(0===t||1===t&&".."===e[2]||".."===e[t-1])continue;t>0&&(e.splice(t-1,2),t-=2)}}function v(e,t,i){var r,n,a,s,u,c,d,p,f,l,h,m,v=t&&t.split("/"),x=o.map,b=x&&x["*"];if(e&&(e=e.split("/"),d=e.length-1,o.nodeIdCompat&&jsSuffixRegExp.test(e[d])&&(e[d]=e[d].replace(jsSuffixRegExp,"")),"."===e[0].charAt(0)&&v&&(m=v.slice(0,v.length-1),e=m.concat(e)),g(e),e=e.join("/")),i&&x&&(v||b)){a=e.split("/");e:for(s=a.length;s>0;s-=1){if(c=a.slice(0,s).join("/"),v)for(u=v.length;u>0;u-=1)if(n=getOwn(x,v.slice(0,u).join("/")),n&&(n=getOwn(n,c),n)){p=n,f=s;break e}!l&&b&&getOwn(b,c)&&(l=getOwn(b,c),h=s)}!p&&l&&(p=l,f=h),p&&(a.splice(0,f,p),e=a.join("/"))}return r=getOwn(o.pkgs,e),r||e}function x(e){isBrowser&&each(scripts(),(function(t){if(t.getAttribute("data-requiremodule")===e&&t.getAttribute("data-requirecontext")===r.contextName)return t.parentNode.removeChild(t),!0}))}function b(e){var t=getOwn(o.paths,e);if(t&&isArray(t)&&t.length>1)return t.shift(),r.require.undef(e),r.makeRequire(null,{skipMap:!0})([e]),!0}function q(e){var t,i=e?e.indexOf("!"):-1;return i>-1&&(t=e.substring(0,i),e=e.substring(i+1,e.length)),[t,e]}function E(e,t,i,n){var a,o,s,u,c=null,d=t?t.name:null,f=e,l=!0,g="";return e||(l=!1,e="_@r"+(h+=1)),u=q(e),c=u[0],e=u[1],c&&(c=v(c,d,n),o=getOwn(p,c)),e&&(c?g=o&&o.normalize?o.normalize(e,(function(e){return v(e,d,n)})):-1===e.indexOf("!")?v(e,d,n):e:(g=v(e,d,n),u=q(g),c=u[0],g=u[1],i=!0,a=r.nameToUrl(g))),s=!c||o||i?"":"_unnormalized"+(m+=1),{prefix:c,name:g,parentMap:t,unnormalized:!!s,url:a,originalName:f,isDefine:l,id:(c?c+"!"+g:g)+s}}function w(e){var t=e.id,i=getOwn(s,t);return i||(i=s[t]=new r.Module(e)),i}function y(e,t,i){var r=e.id,n=getOwn(s,r);!hasProp(p,r)||n&&!n.defineEmitComplete?(n=w(e),n.error&&"error"===t?i(n.error):n.on(t,i)):"defined"===t&&i(p[r])}function k(e,t){var i=e.requireModules,r=!1;t?t(e):(each(i,(function(t){var i=getOwn(s,t);i&&(i.error=e,i.events.error&&(r=!0,i.emit("error",e)))})),r||req.onError(e))}function S(){globalDefQueue.length&&(each(globalDefQueue,(function(e){var t=e[0];"string"===typeof t&&(r.defQueueMap[t]=!0),d.push(e)})),globalDefQueue=[])}function M(e){delete s[e],delete u[e]}function O(e,t,i){var r=e.map.id;e.error?e.emit("error",e.error):(t[r]=!0,each(e.depMaps,(function(r,n){var a=r.id,o=getOwn(s,a);!o||e.depMatched[n]||i[a]||(getOwn(t,a)?(e.defineDep(n,p[a]),e.check()):O(o,t,i))})),i[r]=!0)}function j(){var e,i,n=1e3*o.waitSeconds,s=n&&r.startTime+n<(new Date).getTime(),c=[],d=[],p=!1,f=!0;if(!t){if(t=!0,eachProp(u,(function(e){var t=e.map,r=t.id;if(e.enabled&&(t.isDefine||d.push(e),!e.error))if(!e.inited&&s)b(r)?(i=!0,p=!0):(c.push(r),x(r));else if(!e.inited&&e.fetched&&t.isDefine&&(p=!0,!t.prefix))return f=!1})),s&&c.length)return e=makeError("timeout","Load timeout for modules: "+c,null,c),e.contextName=r.contextName,k(e);f&&each(d,(function(e){O(e,{},{})})),s&&!i||!p||!isBrowser&&!isWebWorker||a||(a=setTimeout((function(){a=0,j()}),50)),t=!1}}function P(e){hasProp(p,e[0])||w(E(e[0],null,!0)).init(e[1],e[2])}function R(e,t,i,r){e.detachEvent&&!isOpera?r&&e.detachEvent(r,t):e.removeEventListener(i,t,!1)}function A(e){var t=e.currentTarget||e.srcElement;return R(t,r.onScriptLoad,"load","onreadystatechange"),R(t,r.onScriptError,"error"),{node:t,id:t&&t.getAttribute("data-requiremodule")}}function T(){var e;S();while(d.length){if(e=d.shift(),null===e[0])return k(makeError("mismatch","Mismatched anonymous define() module: "+e[e.length-1]));P(e)}r.defQueueMap={}}return n={require:function(e){return e.require?e.require:e.require=r.makeRequire(e.map)},exports:function(e){if(e.usingExports=!0,e.map.isDefine)return e.exports?p[e.map.id]=e.exports:e.exports=p[e.map.id]={}},module:function(e){return e.module?e.module:e.module={id:e.map.id,uri:e.map.url,config:function(){return getOwn(o.config,e.map.id)||{}},exports:e.exports||(e.exports={})}}},i=function(e){this.events=getOwn(c,e.id)||{},this.map=e,this.shim=getOwn(o.shim,e.id),this.depExports=[],this.depMaps=[],this.depMatched=[],this.pluginMaps={},this.depCount=0},i.prototype={init:function(e,t,i,r){r=r||{},this.inited||(this.factory=t,i?this.on("error",i):this.events.error&&(i=bind(this,(function(e){this.emit("error",e)}))),this.depMaps=e&&e.slice(0),this.errback=i,this.inited=!0,this.ignore=r.ignore,r.enabled||this.enabled?this.enable():this.check())},defineDep:function(e,t){this.depMatched[e]||(this.depMatched[e]=!0,this.depCount-=1,this.depExports[e]=t)},fetch:function(){if(!this.fetched){this.fetched=!0,r.startTime=(new Date).getTime();var e=this.map;if(!this.shim)return e.prefix?this.callPlugin():this.load();r.makeRequire(this.map,{enableBuildCallback:!0})(this.shim.deps||[],bind(this,(function(){return e.prefix?this.callPlugin():this.load()})))}},load:function(){var e=this.map.url;f[e]||(f[e]=!0,r.load(this.map.id,e))},check:function(){if(this.enabled&&!this.enabling){var e,t,i=this.map.id,n=this.depExports,a=this.exports,o=this.factory;if(this.inited){if(this.error)this.emit("error",this.error);else if(!this.defining){if(this.defining=!0,this.depCount<1&&!this.defined){if(isFunction(o)){if(this.events.error&&this.map.isDefine||req.onError!==defaultOnError)try{a=r.execCb(i,o,n,a)}catch(s){e=s}else a=r.execCb(i,o,n,a);if(this.map.isDefine&&void 0===a&&(t=this.module,t?a=t.exports:this.usingExports&&(a=this.exports)),e)return e.requireMap=this.map,e.requireModules=this.map.isDefine?[this.map.id]:null,e.requireType=this.map.isDefine?"define":"require",k(this.error=e)}else a=o;this.exports=a,this.map.isDefine&&!this.ignore&&(p[i]=a,req.onResourceLoad&&req.onResourceLoad(r,this.map,this.depMaps)),M(i),this.defined=!0}this.defining=!1,this.defined&&!this.defineEmitted&&(this.defineEmitted=!0,this.emit("defined",this.exports),this.defineEmitComplete=!0)}}else hasProp(r.defQueueMap,i)||this.fetch()}},callPlugin:function(){var e=this.map,t=e.id,i=E(e.prefix);this.depMaps.push(i),y(i,"defined",bind(this,(function(i){var n,a,u,c=getOwn(l,this.map.id),d=this.map.name,p=this.map.parentMap?this.map.parentMap.name:null,f=r.makeRequire(e.parentMap,{enableBuildCallback:!0});return this.map.unnormalized?(i.normalize&&(d=i.normalize(d,(function(e){return v(e,p,!0)}))||""),a=E(e.prefix+"!"+d,this.map.parentMap),y(a,"defined",bind(this,(function(e){this.init([],(function(){return e}),null,{enabled:!0,ignore:!0})}))),u=getOwn(s,a.id),void(u&&(this.depMaps.push(a),this.events.error&&u.on("error",bind(this,(function(e){this.emit("error",e)}))),u.enable()))):c?(this.map.url=r.nameToUrl(c),void this.load()):(n=bind(this,(function(e){this.init([],(function(){return e}),null,{enabled:!0})})),n.error=bind(this,(function(e){this.inited=!0,this.error=e,e.requireModules=[t],eachProp(s,(function(e){0===e.map.id.indexOf(t+"_unnormalized")&&M(e.map.id)})),k(e)})),n.fromText=bind(this,(function(i,a){var s=e.name,u=E(s),c=useInteractive;a&&(i=a),c&&(useInteractive=!1),w(u),hasProp(o.config,t)&&(o.config[s]=o.config[t]);try{req.exec(i)}catch(d){return k(makeError("fromtexteval","fromText eval for "+t+" failed: "+d,d,[t]))}c&&(useInteractive=!0),this.depMaps.push(u),r.completeLoad(s),f([s],n)})),void i.load(e.name,f,n,o))}))),r.enable(i,this),this.pluginMaps[i.id]=i},enable:function(){u[this.map.id]=this,this.enabled=!0,this.enabling=!0,each(this.depMaps,bind(this,(function(e,t){var i,a,o;if("string"===typeof e){if(e=E(e,this.map.isDefine?this.map:this.map.parentMap,!1,!this.skipMap),this.depMaps[t]=e,o=getOwn(n,e.id),o)return void(this.depExports[t]=o(this));this.depCount+=1,y(e,"defined",bind(this,(function(e){this.undefed||(this.defineDep(t,e),this.check())}))),this.errback?y(e,"error",bind(this,this.errback)):this.events.error&&y(e,"error",bind(this,(function(e){this.emit("error",e)})))}i=e.id,a=s[i],hasProp(n,i)||!a||a.enabled||r.enable(e,this)}))),eachProp(this.pluginMaps,bind(this,(function(e){var t=getOwn(s,e.id);t&&!t.enabled&&r.enable(e,this)}))),this.enabling=!1,this.check()},on:function(e,t){var i=this.events[e];i||(i=this.events[e]=[]),i.push(t)},emit:function(e,t){each(this.events[e],(function(e){e(t)})),"error"===e&&delete this.events[e]}},r={config:o,contextName:e,registry:s,defined:p,urlFetched:f,defQueue:d,defQueueMap:{},Module:i,makeModuleMap:E,nextTick:req.nextTick,onError:k,configure:function(e){e.baseUrl&&"/"!==e.baseUrl.charAt(e.baseUrl.length-1)&&(e.baseUrl+="/");var t=o.shim,i={paths:!0,bundles:!0,config:!0,map:!0};eachProp(e,(function(e,t){i[t]?(o[t]||(o[t]={}),mixin(o[t],e,!0,!0)):o[t]=e})),e.bundles&&eachProp(e.bundles,(function(e,t){each(e,(function(e){e!==t&&(l[e]=t)}))})),e.shim&&(eachProp(e.shim,(function(e,i){isArray(e)&&(e={deps:e}),!e.exports&&!e.init||e.exportsFn||(e.exportsFn=r.makeShimExports(e)),t[i]=e})),o.shim=t),e.packages&&each(e.packages,(function(e){var t,i;e="string"===typeof e?{name:e}:e,i=e.name,t=e.location,t&&(o.paths[i]=e.location),o.pkgs[i]=e.name+"/"+(e.main||"main").replace(currDirRegExp,"").replace(jsSuffixRegExp,"")})),eachProp(s,(function(e,t){e.inited||e.map.unnormalized||(e.map=E(t,null,!0))})),(e.deps||e.callback)&&r.require(e.deps||[],e.callback)},makeShimExports:function(e){function t(){var t;return e.init&&(t=e.init.apply(global,arguments)),t||e.exports&&getGlobal(e.exports)}return t},makeRequire:function(t,i){function a(o,u,c){var d,f,l;return i.enableBuildCallback&&u&&isFunction(u)&&(u.__requireJsBuild=!0),"string"===typeof o?isFunction(u)?k(makeError("requireargs","Invalid require call"),c):t&&hasProp(n,o)?n[o](s[t.id]):req.get?req.get(r,o,t,a):(f=E(o,t,!1,!0),d=f.id,hasProp(p,d)?p[d]:k(makeError("notloaded",'Module name "'+d+'" has not been loaded yet for context: '+e+(t?"":". Use require([])")))):(T(),r.nextTick((function(){T(),l=w(E(null,t)),l.skipMap=i.skipMap,l.init(o,u,c,{enabled:!0}),j()})),a)}return i=i||{},mixin(a,{isBrowser:isBrowser,toUrl:function(e){var i,n=e.lastIndexOf("."),a=e.split("/")[0],o="."===a||".."===a;return-1!==n&&(!o||n>1)&&(i=e.substring(n,e.length),e=e.substring(0,n)),r.nameToUrl(v(e,t&&t.id,!0),i,!0)},defined:function(e){return hasProp(p,E(e,t,!1,!0).id)},specified:function(e){return e=E(e,t,!1,!0).id,hasProp(p,e)||hasProp(s,e)}}),t||(a.undef=function(e){S();var i=E(e,t,!0),n=getOwn(s,e);n.undefed=!0,x(e),delete p[e],delete f[i.url],delete c[e],eachReverse(d,(function(t,i){t[0]===e&&d.splice(i,1)})),delete r.defQueueMap[e],n&&(n.events.defined&&(c[e]=n.events),M(e))}),a},enable:function(e){var t=getOwn(s,e.id);t&&w(e).enable()},completeLoad:function(e){var t,i,n,a=getOwn(o.shim,e)||{},u=a.exports;S();while(d.length){if(i=d.shift(),null===i[0]){if(i[0]=e,t)break;t=!0}else i[0]===e&&(t=!0);P(i)}if(r.defQueueMap={},n=getOwn(s,e),!t&&!hasProp(p,e)&&n&&!n.inited){if(!(!o.enforceDefine||u&&getGlobal(u)))return b(e)?void 0:k(makeError("nodefine","No define call for "+e,null,[e]));P([e,a.deps||[],a.exportsFn])}j()},nameToUrl:function(e,t,i){var n,a,s,u,c,d,p,f=getOwn(o.pkgs,e);if(f&&(e=f),p=getOwn(l,e),p)return r.nameToUrl(p,t,i);if(req.jsExtRegExp.test(e))c=e+(t||"");else{for(n=o.paths,a=e.split("/"),s=a.length;s>0;s-=1)if(u=a.slice(0,s).join("/"),d=getOwn(n,u),d){isArray(d)&&(d=d[0]),a.splice(0,s,d);break}c=a.join("/"),c+=t||(/^data\:|\?/.test(c)||i?"":".js"),c=("/"===c.charAt(0)||c.match(/^[\w\+\.\-]+:/)?"":o.baseUrl)+c}return o.urlArgs?c+(-1===c.indexOf("?")?"?":"&")+o.urlArgs:c},load:function(e,t){req.load(r,e,t)},execCb:function(e,t,i,r){return t.apply(r,i)},onScriptLoad:function(e){if("load"===e.type||readyRegExp.test((e.currentTarget||e.srcElement).readyState)){interactiveScript=null;var t=A(e);r.completeLoad(t.id)}},onScriptError:function(e){var t=A(e);if(!b(t.id))return k(makeError("scripterror","Script error for: "+t.id,e,[t.id]))}},r.require=r.makeRequire(),r}function getInteractiveScript(){return interactiveScript&&"interactive"===interactiveScript.readyState||eachReverse(scripts(),(function(e){if("interactive"===e.readyState)return interactiveScript=e})),interactiveScript}}(this);