<template>
  <div class="quote-list" v-if="mergedQuoteList.length > 0">
    <div class="quote-item" v-for="(mergedQuote, index) in mergedQuoteList" :key="index">
      <!-- 鼠标悬停弹窗 -->
      <el-popover
        placement="top"
        :width="500"
        trigger="hover"
        popper-class="quote-detail-popover"
        :popper-style="{ zIndex: 9999, maxHeight: '400px', overflow: 'hidden' }"
        :show-arrow="true"
        :hide-after="200"
        :show-after="200"
        :enterable="true"
        :persistent="false">
        <template #reference>
          <div class="quote-item-wrapper">
            <el-checkbox
              :model-value="selectedFiles.some((file) => file.id === mergedQuote.id?.toString())"
              @change="(checked) => handleQuoteSelect(checked as boolean, mergedQuote)"
              :disabled="!selectedFiles.some((file) => file.id === mergedQuote.id?.toString()) && selectedFiles.length >= 5"
              class="quote-checkbox" />
            <!-- <FilesCard :name="mergedQuote.fileName" :description="`${mergedQuote.count}个片段`" @click="handleQuote(mergedQuote)" class="cursor-pointer" /> -->

            <div class="quote-item-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="quote-item-content" @click="handleQuote(mergedQuote)">
              <div class="quote-item-title">
                {{ mergedQuote.fileName }}
                <span v-if="mergedQuote.count > 1" class="quote-count">(#{{ mergedQuote.count }})</span>
              </div>
            </div>
          </div>
        </template>
        <div class="quote-detail-content">
          <div class="quote-detail-header">
            <el-icon><Document /></el-icon>
            <span class="quote-detail-title">{{ mergedQuote.fileName }}</span>
            <span class="quote-detail-count">共 {{ mergedQuote.count }} 个片段</span>
          </div>
          <div class="quote-detail-list" @click.stop @mouseenter.stop @mouseleave.stop>
            <el-collapse :model-value="[getCollapseItemName(mergedQuote.originalQuotes[0], 0)]" class="quote-collapse">
              <el-collapse-item
                v-for="(quote, idx) in mergedQuote.originalQuotes"
                :key="quote.segment_id || quote.id || idx"
                :name="getCollapseItemName(quote, idx)"
                class="quote-collapse-item">
                <template #title>
                  <div class="quote-detail-item-header">
                    <span class="quote-detail-item-index">#{{ quote.position || idx + 1 }}</span>
                    <span class="quote-detail-item-score">相关度: {{ (quote.score * 100).toFixed(1) }}%</span>
                    <span v-if="quote.word_count" class="quote-detail-item-words">{{ quote.word_count }}字</span>
                    <span v-if="quote.dataset_name" class="quote-detail-item-dataset">来源: {{ quote.dataset_name }}</span>
                  </div>
                </template>
                <div class="quote-detail-item-content">
                  <XMarkdown :markdown="quote.content || ''" class="quote-markdown-content" />
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Document } from '@element-plus/icons-vue'
  import { XMarkdown } from 'vue-element-plus-x'
  import { useMergedQuoteList, useQuoteActions } from './helper/index'

  // 组件属性
  interface Props {
    quoteList?: any[]
  }

  const props = withDefaults(defineProps<Props>(), {
    quoteList: () => []
  })

  // 双向绑定：已选择的文件列表
  const selectedFiles = defineModel<Array<{ id: string; name: string; url?: string }>>('selectedFiles', {
    default: () => []
  })

  // 使用helper中的逻辑
  const { mergedQuoteList } = useMergedQuoteList(props)
  const { handleQuoteSelect, handleQuote, getCollapseItemName } = useQuoteActions(selectedFiles)
</script>

<style scoped lang="scss">
  /* 引用列表样式 */
  .quote-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 16px;
  }
  .quote-item {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: flex-start;
    padding: 10px 14px;
    overflow: hidden;
    background: linear-gradient(135deg, rgb(255 255 255 / 90%) 0%, rgb(248 250 252 / 80%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(226 232 240 / 60%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%), 0 1px 3px rgb(0 0 0 / 10%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .quote-item-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    width: 100%;
  }
  .quote-item::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 3px;
    content: '';
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .quote-item:hover {
    border-color: rgb(102 126 234 / 30%);
    box-shadow: 0 8px 25px rgb(0 0 0 / 12%), 0 4px 10px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .quote-item:hover::before {
    opacity: 1;
  }
  .quote-item-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgb(102 126 234 / 30%);
    transition: all 0.3s ease;
  }
  .quote-item:hover .quote-item-icon {
    box-shadow: 0 4px 12px rgb(102 126 234 / 40%);
    transform: scale(1.05);
  }
  .quote-checkbox {
    flex-shrink: 0;
    margin-top: 5px;
  }
  .quote-item-content {
    flex: 1;
    min-width: 0;
    cursor: pointer;
  }
  .quote-item-title {
    display: -webkit-box;
    overflow: hidden;
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: #1e293b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .quote-count {
    display: inline-block;
    padding: 2px 6px;
    margin-left: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    background: rgb(102 126 234 / 10%);
    border-radius: 4px;
  }
  .quote-item-text {
    display: -webkit-box;
    overflow: hidden;
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* 引用详情弹窗样式 */
  :deep(.quote-detail-popover) {
    .el-popover {
      padding: 0;
      background: rgb(255 255 255 / 95%);
      backdrop-filter: blur(12px);
      border: 1px solid rgb(226 232 240 / 60%);
      border-radius: 12px;
      box-shadow: 0 10px 25px rgb(0 0 0 / 15%), 0 4px 10px rgb(0 0 0 / 10%);
    }

    /* 折叠面板深度样式 */
    .el-collapse {
      border: none;
    }
    .el-collapse-item__header {
      height: auto;
      padding: 0;
      line-height: normal;
      background: transparent;
      border: none;
    }
    .el-collapse-item__wrap {
      background: transparent;
      border: none;
    }
    .el-collapse-item__content {
      padding: 0;
    }
    .el-collapse-item__arrow {
      margin-right: 8px;
      color: #667eea;
    }
    .el-collapse-item__header:hover {
      background-color: transparent !important;
    }
  }
  .quote-detail-content {
    padding: 16px 16px 24px;
  }
  .quote-detail-header {
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid rgb(226 232 240 / 60%);
  }
  .quote-detail-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }
  .quote-detail-count {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #667eea;
    background: rgb(102 126 234 / 10%);
    border-radius: 6px;
  }
  .quote-detail-list {
    max-height: 320px;
    padding-right: 4px;
    padding-bottom: 20px;
    overflow-y: auto;
  }
  .quote-detail-list::-webkit-scrollbar {
    width: 6px;
  }
  .quote-detail-list::-webkit-scrollbar-track {
    background: rgb(248 250 252 / 60%);
    border-radius: 3px;
  }
  .quote-detail-list::-webkit-scrollbar-thumb {
    background: rgb(148 163 184 / 60%);
    border-radius: 3px;
  }
  .quote-detail-list::-webkit-scrollbar-thumb:hover {
    background: rgb(148 163 184 / 80%);
  }

  /* 折叠面板样式 */
  .quote-collapse {
    border: none;
  }
  .quote-collapse-item {
    margin-bottom: 8px;
    overflow: hidden;
    background: rgb(248 250 252 / 60%);
    border: 1px solid rgb(226 232 240 / 40%);
    border-radius: 8px;
  }
  .quote-collapse-item:last-child {
    margin-bottom: 8px;
  }

  /* 折叠面板标题区域样式 */
  .quote-detail-item-header {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 12px;
    pointer-events: none;
  }
  .quote-detail-item-index {
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
  }
  .quote-detail-item-score {
    font-size: 12px;
    color: #64748b;
  }
  .quote-detail-item-words {
    font-size: 12px;
    color: #94a3b8;
  }
  .quote-detail-item-content {
    padding: 0 12px 12px;
    background: rgb(255 255 255 / 80%);
  }
  .quote-markdown-content {
    font-size: 14px;
    line-height: 1.5;
    color: #475569;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* 弹窗中的 markdown 样式优化 */
  .quote-detail-popover .quote-markdown-content {
    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin: 8px 0 4px;
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
    }
    :deep(p) {
      margin: 4px 0;
      font-size: 14px;
      line-height: 1.5;
    }
    :deep(ul),
    :deep(ol) {
      padding-left: 16px;
      margin: 4px 0;
    }
    :deep(li) {
      margin: 2px 0;
      font-size: 14px;
      line-height: 1.4;
    }
    :deep(code) {
      padding: 2px 4px;
      font-size: 12px;
      background: rgb(248 250 252 / 80%);
      border-radius: 3px;
    }
    :deep(pre) {
      padding: 8px;
      margin: 8px 0;
      overflow-x: auto;
      background: rgb(248 250 252 / 80%);
      border-radius: 6px;
    }
    :deep(blockquote) {
      padding: 8px 12px;
      margin: 8px 0;
      background: rgb(248 250 252 / 60%);
      border-left: 3px solid #667eea;
      border-radius: 0 6px 6px 0;
    }
    :deep(table) {
      width: 100%;
      margin: 8px 0;
      font-size: 13px;
      border-collapse: collapse;
    }
    :deep(th),
    :deep(td) {
      padding: 4px 8px;
      text-align: left;
      border: 1px solid rgb(226 232 240 / 60%);
    }
    :deep(th) {
      font-weight: 600;
      background: rgb(248 250 252 / 80%);
    }
  }
  .quote-detail-item-dataset {
    font-size: 12px;
    font-style: italic;
    color: #94a3b8;
  }

  /* 响应式设计 */
  @media (width <= 768px) {
    .quote-list {
      grid-template-columns: 1fr;
    }
    .quote-item {
      gap: 10px;
      padding: 12px;
    }
    .quote-item-icon {
      width: 36px;
      height: 36px;
    }
    .quote-item-title {
      font-size: 16px;
    }
    .quote-item-text {
      font-size: 14px;
    }
  }
</style>
