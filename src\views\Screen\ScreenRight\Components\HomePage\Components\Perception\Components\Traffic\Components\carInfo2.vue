<template>
  <div class="car-info-container">
    <Tabs :type="type" :date="date" :carTotalNumber="carTotalNumber" :lineName="lineName" :carTopList="carTopList"></Tabs>
    <Mychart :option="options" width="640px" height="300px" style="margin-top: 20px"></Mychart>
  </div>
</template>

<script setup>
  import Tabs from './Components/tabs.vue'
  import Mychart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib.ts'

  const props = defineProps({
    type: {
      type: String,
      default: 'day'
    },
    lineName: {
      type: String,
      default: '东线'
    },
    date: {
      type: String,
      default: ''
    },
    resultList: {
      type: Array,
      default: () => []
    }
  })
  const carTopList = ref([])
  const carList = ref([])
  const carTotalNumber = ref(10000)
  const options = ref({
    grid: [
      {
        width: '20%',
        left: 110,
        top: '0',
        right: '0',
        bottom: '0',
        containLabel: false
      },
      {
        width: '20%',
        left: '70%',
        top: '0',
        bottom: '0',
        containLabel: false
      }
    ],
    xAxis: [
      {
        type: 'value',
        show: false,
        gridIndex: 0
      },
      {
        type: 'value',
        show: false,
        gridIndex: 1
      }
    ],
    yAxis: [
      {
        type: 'category',
        data: ['作业车1', '作业车2', '作业车3', '作业车4', '作业车5', '作业车6'],
        axisLine: { show: false },
        axisTick: { show: false },
        splitArea: { show: false },
        splitLine: { show: false },
        axisLabel: {
          fontFamily: 'Alibaba PuHuiTi',
          fontSize: 20,
          color: '#ffffff'
        },
        gridIndex: 0,
        inverse: true
      },
      {
        // 左侧柱状图的Y轴
        gridIndex: 0, // y轴所在的 grid 的索引
        splitLine: 'none',
        axisTick: 'none',
        axisLine: 'none',
        data: [4.4, 10, 34, 12, 21, 4.4],
        inverse: true,
        axisLabel: {
          show: true,
          verticalAlign: 'middle',
          textStyle: {
            color: '#fff',
            fontSize: '16'
          },
          formatter: function (value) {
            return '{x|' + (value || 0) + '%}'
          },
          rich: {
            x: {
              color: '#ffffff',
              fontSize: 16,
              fontFamily: 'PangMenZhengDao',
              fontWeight: 'bold'
            }
          }
        }
      },
      {
        type: 'category',
        data: ['货车1', '货车2', '货车3', '客车4', '客车5', '客车6'],
        axisLine: { show: false },
        axisTick: { show: false },
        splitArea: { show: false },
        splitLine: { show: false },
        axisLabel: {
          fontFamily: 'Alibaba PuHuiTi',
          fontSize: 20,
          color: '#ffffff'
        },
        gridIndex: 1,
        inverse: true
      },
      {
        gridIndex: 1,
        splitLine: 'none',
        axisTick: 'none',
        axisLine: 'none',
        data: [4.4, 10, 34, 12, 21, 4.4],
        inverse: true,
        axisLabel: {
          show: true,
          verticalAlign: 'middle',
          textStyle: {
            color: '#fff',
            fontSize: '16'
          },
          formatter: function (value) {
            return '{x|' + value + '%}'
          },
          rich: {
            x: {
              color: '#ffffff',
              fontSize: 16,
              fontFamily: 'PangMenZhengDao',
              fontWeight: 'bold'
            }
          }
        }
      }
    ],
    series: [
      {
        xAxisIndex: 0,
        yAxisIndex: 0,
        data: [
          { value: 120, itemStyle: { color: '#00D1FF' } },
          { value: 200, itemStyle: { color: '#00D1FF' } },
          { value: 150, itemStyle: { color: '#F4B410' } },
          { value: 80, itemStyle: { color: '#F4B410' } },
          { value: 70, itemStyle: { color: '#0FED9D' } },
          { value: 110, itemStyle: { color: '#0FED9D' } }
        ],
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0)',
          borderColor: '#4078B8',
          borderWidth: 1,
          borderRadius: 3
        },
        barWidth: 24,
        label: {
          show: true,
          position: 'right',
          fontSize: 14,
          fontFamily: 'PangMenZhengDao',
          color: ' #AEF5FF',
          fontWeight: 'bold',
          formatter: (params) => {
            return parseInt((carTotalNumber.value * params.value) / 100)
          }
        },
        itemStyle: {
          borderRadius: 3,
          borderWidth: 6,
          borderColor: 'rgba(255,255,255,0)'
        }
      },
      {
        xAxisIndex: 1,
        yAxisIndex: 2,
        data: [
          { value: 120, itemStyle: { color: '#00D1FF' } },
          { value: 200, itemStyle: { color: '#00D1FF' } },
          { value: 150, itemStyle: { color: '#F4B410' } },
          { value: 80, itemStyle: { color: '#F4B410' } },
          { value: 70, itemStyle: { color: '#0FED9D' } },
          { value: 110, itemStyle: { color: '#0FED9D' } }
        ],
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0)',
          borderColor: '#4078B8',
          borderWidth: 1,
          borderRadius: 3
        },
        barWidth: 24,
        label: {
          show: true,
          position: 'right',
          fontSize: 14,
          fontFamily: 'PangMenZhengDao',
          color: ' #AEF5FF',
          fontWeight: 'bold',
          formatter: (params) => {
            return parseInt((carTotalNumber.value * params.value) / 100)
          }
        },
        itemStyle: {
          borderRadius: 3,
          borderWidth: 6,
          borderColor: 'rgba(255,255,255,0)'
        }
      }
    ]
  })

  const getData = () => {
    const labelData = carList.value.map((_) => _.name)
    const data = carList.value.map((_) => {
      const color = _.name.includes('客车') ? '#0FED9D' : _.name.includes('作业车') ? '#00D1FF' : '#F4B410'
      return {
        value: _.pro.split('%')[0],
        itemStyle: { color: color }
      }
    })
    const percent = data.map((_) => _.value)

    options.value.yAxis[0].data = labelData.slice(0, 8)
    options.value.yAxis[2].data = labelData.slice(8)
    options.value.yAxis[1].data = percent.slice(0, 8)
    options.value.yAxis[3].data = percent.slice(8)
    options.value.series[0].data = data.slice(0, 8)
    options.value.series[1].data = data.slice(8)
    options.value.xAxis[0].max = 100
    options.value.xAxis[1].max = 100
  }

  // watch(
  //   () => props.lineName,
  //   () => {
  //     getData()
  //   },
  //   {
  //     immediate: true
  //   }
  // )
  watchEffect(() => {
    getData()
  })
  lib.bus.trafficMonitor.on((e) => {
    carTotalNumber.value = e
    getData()
  })
  watch(
    () => props.resultList,
    (value) => {
      const arr = value.slice(0, value.length - 4)
      carList.value = arr
      carTopList.value = JSON.parse(JSON.stringify(value.slice(value.length - 4, value.length - 1))).reverse()
    },
    {
      immediate: true
    }
  )
  onUnmounted(() => {
    lib.bus.trafficMonitor.reset()
  })
</script>

<style lang="scss" scoped>
  .car-info-container {
    width: 640px;
    height: 430px;
  }
</style>
