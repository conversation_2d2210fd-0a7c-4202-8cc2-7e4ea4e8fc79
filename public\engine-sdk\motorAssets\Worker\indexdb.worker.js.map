{"version": 3, "sources": ["webpack://Motor/webpack/bootstrap", "webpack://Motor//root/code/motor-mono/node_modules/.pnpm/dexie@3.2.4/node_modules/dexie/dist/dexie.mjs", "webpack://Motor/./src/motor/WorkerES6/indexdb.worker.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "__assign", "assign", "arguments", "length", "apply", "this", "__spread<PERSON><PERSON>y", "to", "from", "pack", "ar", "Array", "slice", "concat", "_global", "globalThis", "self", "window", "global", "keys", "isArray", "extend", "obj", "extension", "for<PERSON>ach", "Promise", "getProto", "getPrototypeOf", "_hasOwn", "hasOwn", "prop", "props", "proto", "Reflect", "ownKeys", "setProp", "functionOrGetSet", "options", "set", "configurable", "writable", "derive", "Child", "Parent", "getOwnPropertyDescriptor", "getPropertyDescriptor", "_slice", "args", "start", "end", "override", "origFunc", "overridedFactory", "assert", "b", "Error", "asap$1", "fn", "setImmediate", "setTimeout", "arrayToObject", "array", "extractor", "reduce", "result", "item", "nameAndValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyP<PERSON>", "rv", "val", "push", "period", "indexOf", "innerObj", "substr", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFrozen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>ey<PERSON>ath", "isNaN", "parseInt", "splice", "shallowClone", "flatten", "a", "intrinsicTypeNames", "split", "map", "num", "filter", "intrinsicTypes", "x", "circularRefs", "deepClone", "any", "WeakMap", "innerDeepClone", "constructor", "toString", "iteratorSymbol", "iterator", "getIteratorOf", "NO_CHAR_ARRAY", "getArrayOf", "arrayLike", "it", "next", "done", "isAsyncFunction", "debug", "location", "test", "href", "setDebug", "<PERSON><PERSON><PERSON>er", "NEEDS_THROW_FOR_STACK", "stack", "getErrorWithStack", "e", "prettyStack", "exception", "numIgnoredFrames", "message", "frame", "join", "idbDomErrorNames", "errorList", "defaultTexts", "VersionChanged", "DatabaseClosed", "Abort", "TransactionInactive", "MissingAPI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "msg", "_e", "getMultiErrorMessage", "failures", "v", "ModifyError", "successCount", "failed<PERSON>ey<PERSON>", "BulkError", "pos", "failuresByPos", "_stack", "<PERSON><PERSON><PERSON>", "BaseException", "exceptions", "fullName", "msgOrInner", "inner", "Syntax", "SyntaxError", "Type", "TypeError", "Range", "RangeError", "exceptionMap", "fullNameExceptions", "nop", "mirror", "pureFunctionChain", "f1", "f2", "callBoth", "on1", "on2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "res", "onsuccess", "onerror", "res2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hookUp<PERSON><PERSON><PERSON><PERSON>", "modifications", "reverseStoppableEventChain", "promisable<PERSON><PERSON><PERSON>", "then", "thiz", "INTERNAL", "_a$1", "globalP", "resolve", "crypto", "subtle", "nativeP", "digest", "Uint8Array", "resolvedNativePromise", "nativePromiseProto", "resolvedGlobalPromise", "nativePromiseThen", "NativePromise", "patchGlobalPromise", "stack_being_generated", "schedulePhysicalTick", "physicalTick", "MutationObserver", "hiddenDiv", "document", "createElement", "observe", "attributes", "setAttribute", "asap", "callback", "microtickQueue", "needsNewPhysicalTick", "isOutsideMicroTick", "unhandledErrors", "rejectingErrors", "currentFulfiller", "rejectionMapper", "globalPSD", "id", "ref", "unhandleds", "onunhandled", "globalError", "pgp", "env", "finalize", "uh", "PSD", "numScheduledCalls", "tickFinalizers", "<PERSON>iePromise", "_listeners", "onuncatched", "_lib", "psd", "_PSD", "_stackHolder", "_prev", "_numPrev", "_state", "_value", "handleRejection", "executePromiseTask", "thenProp", "microTaskId", "totalEchoes", "onFulfilled", "onRejected", "_this", "possibleAwait", "cleanup", "decrementExpectedAwaits", "reject", "propagateToListener", "Listener", "nativeAwaitCompatibleWrap", "linkToPreviousPromise", "zone", "promise", "shouldExecuteTick", "beginMicroTickScope", "_then", "propagateAllListeners", "endMicroTickScope", "ex", "reason", "_promise", "tryCatch", "origProp", "some", "addPossiblyUnhandledError", "listeners", "len", "finalizePhysicalTick", "listener", "cb", "callListener", "ret", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "numPrev", "wasRootExec", "callbacks", "unhandledErrs", "finalizers", "PromiseReject", "wrap", "errorCatcher", "outerScope", "switchToZone", "catch", "type", "handler", "err", "finally", "onFinally", "getStack", "stacks", "limit", "errorName", "failure", "timeout", "ms", "Infinity", "handle", "Timeout", "clearTimeout", "snapShot", "all", "values", "onPossibleParallellAsync", "remaining", "race", "newPSD", "newScope", "usePSD", "scheduler", "follow", "zoneProps", "finalizer", "run_at_end_of_this_or_next_physical_tick", "allSettled", "possiblePromises", "results", "status", "AggregateError", "task", "awaits", "echoes", "taskCounter", "zoneStack", "zoneEchoes", "zone_id_counter", "a1", "a2", "parent", "globalEnv", "PromiseProp", "nthen", "getPatchedPromiseThen", "gthen", "incrementExpectedAwaits", "possiblePromise", "rejection", "zoneEnterEcho", "targetZone", "zoneLeaveEcho", "pop", "bEnteringZone", "currentZone", "enqueueNativeMicroTask", "GlobalPromise_1", "targetEnv", "GlobalPromise", "a3", "job", "outerZone", "orig<PERSON>hen", "onResolved", "event", "eventData", "createEvent", "initEvent", "CustomEvent", "detail", "dispatchEvent", "PromiseRejectionEvent", "onunhandledrejection", "_", "defaultPrevented", "console", "warn", "maxString", "String", "fromCharCode", "INVALID_KEY_ARGUMENT", "connections", "isIEOrEdge", "navigator", "userAgent", "hasIEDeleteObjectStoreBug", "hangsOnDeleteLargeKeyRange", "dexieStackFrameFilter", "combine", "filter1", "filter2", "AnyRange", "lower", "lowerOpen", "upper", "upperOpen", "workaroundForUndefinedPrimKey", "Table", "_trans", "writeLocked", "trans", "_tx", "tableName", "checkTableInTransaction", "schema", "NotFound", "idbtrans", "db", "transless", "tempTransaction", "storeNames", "idbdb", "openComplete", "let<PERSON><PERSON><PERSON>", "_vip", "_createTransaction", "_dbSchema", "PR1398_maxLoop", "InvalidState", "isOpen", "_close", "open", "_completion", "db<PERSON>penError", "isBeingOpened", "_options", "autoOpen", "dbReadyPromise", "keyOrCrit", "where", "first", "core", "hook", "reading", "fire", "indexOrCrit", "<PERSON><PERSON><PERSON><PERSON>", "keyPaths", "equals", "compoundIndex", "indexes", "p<PERSON><PERSON><PERSON>", "ix", "compound", "every", "_max<PERSON>ey", "kp", "JSON", "stringify", "idxByName", "idb", "_deps", "indexedDB", "cmp", "_a", "prevIndex", "prevFilterFn", "index", "multi", "idx", "filterFunction", "toCollection", "and", "count", "thenShortcut", "offset", "numRows", "each", "toArray", "Collection", "orderBy", "reverse", "mapToClass", "mappedClass", "readHook", "unsubscribe", "defineClass", "content", "add", "auto", "objToAdd", "mutate", "numFailures", "lastResult", "update", "keyOrObject", "modify", "InvalidArgument", "put", "delete", "clear", "range", "bulkGet", "getMany", "bulkAdd", "objects", "keysOrOptions", "wantResults", "allKeys", "numObjects", "objectsToAdd", "bulkPut", "objectsToPut", "bulkDelete", "num<PERSON>eys", "Events", "ctx", "evs", "eventName", "subscriber", "subscribe", "addEventType", "chainFunction", "defaultFunction", "addConfiguredEvents", "context", "subscribers", "cfg", "makeClassConstructor", "isPlainKeyRange", "ignoreLimitFilter", "algorithm", "or", "justLimit", "replayFilter", "addFilter", "addReplayFilter", "factory", "isLimitFilter", "curr", "getIndexOrStore", "coreSchema", "isPrimKey", "<PERSON><PERSON><PERSON>", "getIndexByKeyPath", "<PERSON><PERSON><PERSON>", "openCursor", "coreTable", "keysOnly", "dir", "unique", "query", "iter", "coreTrans", "set_1", "union", "cursor", "advance", "stop", "fail", "_iterate", "iterate", "valueMapper", "cursorPromise", "wrappedFn", "continue", "advancer", "ta", "tb", "NaN", "al", "bl", "compareUint8Arrays", "getUint8Array", "compareArrays", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "tsTag", "buffer", "byteOffset", "byteLength", "_read", "_ctx", "error", "table", "_write", "_addAlgorithm", "clone", "raw", "Math", "min", "sortBy", "parts", "lastPart", "lastIndex", "getval", "order", "sorter", "aVal", "bVal", "sort", "valueMapper_1", "a_1", "offsetLeft", "rowsLeft", "until", "bIncludeStopEntry", "last", "isMatch", "indexName", "_ondirectionchange", "desc", "eachKey", "eachUniqueKey", "eachPrimaryKey", "primaryKeys", "uniqueKeys", "firstKey", "last<PERSON>ey", "distinct", "str<PERSON><PERSON>", "found", "changes", "modifyer", "anythingModified", "outbound", "extractKey", "modifyChunkSize", "totalFailures", "applyMutateResult", "expectedCount", "_i", "nextChunk", "cache", "addValues", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "deleteKeys", "origValue", "ctx_1", "criteria", "deleteCallback", "changeSpec", "coreRange", "simpleCompare", "simpleCompareReverse", "collectionOrWhereClause", "T", "collection", "emptyCollection", "<PERSON><PERSON><PERSON><PERSON>", "rangeEqual", "nextCasing", "lowerKey", "upperNeedle", "lowerNeedle", "llp", "lwrKeyChar", "addIgnoreCaseAlgorithm", "match", "needles", "suffix", "compare", "upperNeedles", "lowerNeedles", "direction", "nextKeySuffix", "needlesLen", "initDirection", "toUpperCase", "toLowerCase", "upperFactory", "lowerFactory", "needleBounds", "needle", "nb", "createRange", "firstPossibleNeedle", "lowestPossibleCasing", "casing", "between", "<PERSON><PERSON><PERSON><PERSON>", "includeUpper", "_cmp", "above", "aboveOrEqual", "below", "belowOrEqual", "startsWith", "str", "startsWithIgnoreCase", "equalsIgnoreCase", "anyOfIgnoreCase", "startsWithAnyOfIgnoreCase", "anyOf", "_ascending", "_descending", "notEqual", "inAnyRange", "includeLowers", "includeUppers", "noneOf", "ranges", "ascending", "descending", "_min", "max", "_max", "sortDirection", "rangeSorter", "newRange", "rangePos", "keyIsBeyondCurrentEntry", "keyIsBeforeCurrentEntry", "<PERSON><PERSON><PERSON>", "keyWithinCurrentRange", "startsWithAnyOf", "eventRejectHandler", "preventDefault", "target", "stopPropagation", "globalEvents", "Transaction", "_lock", "_reculock", "lockOwnerFor", "_unlock", "_blockedFuncs", "_locked", "fnAndPSD", "shift", "OpenFailed", "active", "transaction", "durability", "chromeTransactionDurability", "ev", "_reject", "<PERSON>ab<PERSON>", "on", "oncomplete", "_resolve", "storagemutated", "bWriteLock", "Read<PERSON>nly", "_root", "waitFor", "promiseLike", "root", "_waitingFor", "_waitingQueue", "store", "objectStore", "spin", "_spinCount", "currentWaitPromise", "abort", "memoizedTables", "_memoizedTables", "tableSchema", "transactionBoundTable", "createIndexSpec", "src", "nameFromKeyPath", "createTableSchema", "getMaxKey", "IdbKeyRange", "only", "getKeyExtractor", "getSinglePathKeyExtractor", "arrayify", "_id_counter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createDBCore", "tmpTrans", "makeIDBKeyRange", "upperBound", "lowerBound", "bound", "tables", "objectStoreNames", "autoIncrement", "indexByKeyPath", "isPrimaryKey", "indexNames", "multiEntry", "hasGetAll", "extractSchema", "isAddOrPut", "req", "reqs", "<PERSON><PERSON><PERSON><PERSON>", "args1", "args2", "keyCount", "callbackCount", "<PERSON><PERSON><PERSON><PERSON>", "_pos", "request", "nonInfinitLimit", "source", "idbKeyRange", "getAll", "getAllKeys", "count_1", "req_1", "openKeyCursor", "result_1", "___id", "_cursor<PERSON><PERSON><PERSON>ue", "_cursorContinuePrimaryKey", "continuePrimaryKey", "_cursorAdvance", "doThrowCursorIsStopped", "gotOne", "iterationPromise", "resolveIteration", "rejectIteration", "guarded<PERSON><PERSON>back", "createDbCoreTable", "tableMap", "MIN_KEY", "MAX_KEY", "createMiddlewareStacks", "middlewares", "IDBKeyRange", "dbcore", "stackImpl", "down", "createMiddlewareStack", "generateMiddlewareStacks", "_novip", "_middlewares", "tbl", "setApiOnPlace", "objs", "tableNames", "dbschema", "propDesc", "removeTablesApi", "lowerVersionFirst", "_cfg", "version", "runUpgraders", "oldVersion", "idbUpgradeTrans", "globalSchema", "_storeNames", "rejectTransaction", "createTable", "populate", "queue", "versions", "_versions", "buildGlobalSchema", "anyContentUpgraderHasRun", "runQueue", "oldSchema", "newSchema", "adjustToExistingIndexNames", "diff", "getSchemaDiff", "tuple", "change", "recreate", "Upgrade", "store_1", "addIndex", "deleteIndex", "del", "idxName", "contentUpgrade", "upgradeSchema_1", "returnValue_1", "contentUpgradeIsAsync_1", "promiseFollowed", "decrementor", "storeName", "deleteObjectStore", "deleteRemovedTables", "contains", "updateTablesAndIndexes", "oldDef", "newDef", "def", "oldIndexes", "newIndexes", "oldIdx", "newIdx", "createObjectStore", "createIndex", "j", "idbindex", "_hasGetAll", "dexieName", "indexSpec", "WorkerGlobalScope", "Version", "_parseStoresSpec", "stores", "outSchema", "indexNum", "trim", "replace", "storesSource", "storesSpec", "_allTables", "upgrade", "upgradeFunction", "getDbNamesTable", "dbNamesDB", "Dexie$1", "addons", "dbnames", "hasDatabasesNative", "databases", "_onDatabaseDeleted", "vip", "idbReady", "intervalId", "userAgentData", "tryIdb", "setInterval", "clearInterval", "dexieOpen", "state", "openCanceller", "throwIfCancelled", "resolveDbReady", "dbReadyResolve", "upgradeTransaction", "wasCreated", "dbN<PERSON>", "autoSchema", "round", "verno", "onblocked", "_fireOnBlocked", "onupgradeneeded", "allowEmptyDB", "close", "delreq", "deleteDatabase", "NoSuchDatabase", "old<PERSON><PERSON>", "pow", "readGlobalSchema", "ch", "verifyInstalledSchema", "onversionchange", "vcFired", "onclose", "_onDatabaseCreated", "onReadyBeingFired", "ready", "fireRemainders", "remainders_1", "awaitIterator", "callNext", "onSuccess", "step", "onError", "throw", "getNext", "extractTransactionArgs", "_tableArgs_", "scopeFunc", "enterTransactionScope", "parentTransaction", "returnValue", "scopeFuncIsAsync", "PrematureCommit", "pad", "virtualIndexMiddleware", "level", "indexLookup", "allVirtualIndexes", "addVirtualIndexes", "keyTail", "lowLevelIndex", "key<PERSON><PERSON><PERSON><PERSON><PERSON>", "indexList", "<PERSON><PERSON><PERSON><PERSON>", "isVirtual", "virtualIndex", "translateRequest", "createVirtualCursor", "getObjectDiff", "prfx", "ap", "bp", "apTypeName", "hooksMiddleware", "downCore", "downTable", "dxTrans", "deleting", "creating", "updating", "addPutOrDelete", "deleteNextChunk", "deleteRange", "getEffectiveKeys", "effectiveKeys", "getExistingValues", "existingValues", "contexts", "existingValue", "generatedPrimaryKey", "objectDiff", "additionalChanges_1", "requestedValue_1", "getFromTransactionCache", "cacheExistingValuesMiddleware", "cachedResult", "isEmptyRange", "node", "RangeSet", "fromOrTree", "addRange", "left", "right", "rebalance", "rightWasCutOff", "mergeRanges", "newSet", "_addRangeSet", "getRangeSetIterator", "keyProvided", "up", "_b", "rootClone", "oldRootRight", "computeDepth", "rangeSet", "<PERSON><PERSON><PERSON>", "add<PERSON><PERSON><PERSON>", "observabilityMiddleware", "FULL_RANGE", "tableClone", "mutatedParts", "getRangeSet", "part", "pkRangeSet", "delsRangeSet", "newObjs", "<PERSON><PERSON><PERSON>", "oldObjs", "add<PERSON>ey<PERSON>r<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "new<PERSON>ey", "trackAffectedIndexes", "getRange", "_c", "_d", "readSubscribers", "method", "subscr", "pkRangeSet_1", "delsRangeSet_1", "queriedIndex", "queried<PERSON><PERSON><PERSON>", "keysPromise_1", "<PERSON><PERSON><PERSON><PERSON>", "pKeys", "cursor_1", "wantValues_1", "pkey", "domDeps", "<PERSON><PERSON>", "deps", "dependencies", "cancelOpen", "bSticky", "db_1", "keyRangeGenerator", "<PERSON><PERSON><PERSON><PERSON>", "whereCtx", "readingHook", "createTableConstructor", "complete", "wasActive", "createTransactionConstructor", "versionNumber", "createVersionConstructor", "orCollection", "_IDBKeyRange", "createWhereClauseConstructor", "newVersion", "use", "addon", "versionInstance", "_whenR<PERSON>y", "unuse", "mw", "hasArguments", "doDelete", "backendDB", "hasBeenClosed", "hasFailed", "dynamicallyOpened", "_transaction", "idbMode", "onlyIfCompatible", "SubTransaction", "enterTransaction", "InvalidTable", "symbolObservable", "observable", "Observable", "_subscribe", "extendObservabilitySet", "mozIndexedDB", "webkitIndexedDB", "msIndexedDB", "webkitIDBKeyRange", "propagateLocally", "updateParts", "wasMe", "propagatingLocally", "databaseName", "exists", "getDatabaseNames", "infos", "info", "ignoreTransaction", "async", "generatorFn", "spawn", "currentTransaction", "promiseOrFunction", "optionalTimeout", "liveQuery", "querier", "hasValue", "currentValue", "observer", "closed", "accumMuts", "currentObs", "subscription", "mutationListener", "querying", "startedListening", "shouldNotify", "rangeSet1", "rangeSet2", "i1", "nextResult1", "i2", "nextResult2", "rangesOverlap", "<PERSON><PERSON><PERSON><PERSON>", "exec", "execute", "getValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "sem<PERSON><PERSON>", "max<PERSON><PERSON>", "addEventListener", "updatedParts", "event_1", "initCustomEvent", "BroadcastChannel", "bc_1", "unref", "changedParts", "postMessage", "onmessage", "data", "localStorage", "setItem", "trig", "random", "matchAll", "includeUncontrolled", "client", "parse", "newValue", "swContainer", "serviceWorker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableConfig", "bimInfo", "modelRecord", "modelIdToBlob", "modelIdToJSAry", "createDB", "dbname", "clearOldDataInDB", "dbStruct", "maxCount", "now", "Date", "threeDays", "deleteIds", "lastUseTime", "modelId", "init", "url", "taskId", "updateDate", "progress", "isExist", "accessCount", "queryDB", "blob", "blobToObj", "text", "deleteIdAry", "modelIdAry", "fetch", "json", "getDataUsingFetch", "formatData", "bimId", "bid", "box", "Blob", "saveToDB", "createDate"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G;;;;;;;;;;;;;ACzDrD,IAAIC,EAAW,WAQX,OAPAA,EAAWtB,OAAOuB,QAAU,SAAkBf,GAC1C,IAAK,IAAIa,EAAG/B,EAAI,EAAGyB,EAAIS,UAAUC,OAAQnC,EAAIyB,EAAGzB,IAE5C,IAAK,IAAI8B,KADTC,EAAIG,UAAUlC,GACOU,OAAOkB,UAAUC,eAAe1B,KAAK4B,EAAGD,KAAIZ,EAAEY,GAAKC,EAAED,IAE9E,OAAOZ,IAEKkB,MAAMC,KAAMH,YAEhC,SAASI,EAAcC,EAAIC,EAAMC,GAC7B,GAAIA,GAA6B,IAArBP,UAAUC,OAAc,IAAK,IAA4BO,EAAxB1C,EAAI,EAAGC,EAAIuC,EAAKL,OAAYnC,EAAIC,EAAGD,KACxE0C,GAAQ1C,KAAKwC,IACRE,IAAIA,EAAKC,MAAMf,UAAUgB,MAAMzC,KAAKqC,EAAM,EAAGxC,IAClD0C,EAAG1C,GAAKwC,EAAKxC,IAGrB,OAAOuC,EAAGM,OAAOH,GAAMC,MAAMf,UAAUgB,MAAMzC,KAAKqC,IAGtD,IAAIM,EAAgC,oBAAfC,WAA6BA,WAC9B,oBAATC,KAAuBA,KACR,oBAAXC,OAAyBA,OAC5BC,OAERC,EAAOzC,OAAOyC,KACdC,EAAUT,MAAMS,QAIpB,SAASC,EAAOC,EAAKC,GACjB,MAAyB,iBAAdA,GAEXJ,EAAKI,GAAWC,SAAQ,SAAUjC,GAC9B+B,EAAI/B,GAAOgC,EAAUhC,MAFd+B,EALQ,oBAAZG,SAA4BX,EAAQW,UAC3CX,EAAQW,QAAUA,SAUtB,IAAIC,EAAWhD,OAAOiD,eAClBC,EAAU,GAAG/B,eACjB,SAASgC,EAAOP,EAAKQ,GACjB,OAAOF,EAAQzD,KAAKmD,EAAKQ,GAE7B,SAASC,EAAMC,EAAOT,GACO,mBAAdA,IACPA,EAAYA,EAAUG,EAASM,MACf,oBAAZC,QAA0Bd,EAAOc,QAAQC,SAASX,GAAWC,SAAQ,SAAUjC,GACnF4C,EAAQH,EAAOzC,EAAKgC,EAAUhC,OAGtC,IAAIZ,EAAiBD,OAAOC,eAC5B,SAASwD,EAAQb,EAAKQ,EAAMM,EAAkBC,GAC1C1D,EAAe2C,EAAKQ,EAAMT,EAAOe,GAAoBP,EAAOO,EAAkB,QAA0C,mBAAzBA,EAAiBvD,IAC5G,CAAEA,IAAKuD,EAAiBvD,IAAKyD,IAAKF,EAAiBE,IAAKC,cAAc,GACtE,CAAEtD,MAAOmD,EAAkBG,cAAc,EAAMC,UAAU,GAAQH,IAEzE,SAASI,EAAOC,GACZ,MAAO,CACHlC,KAAM,SAAUmC,GAGZ,OAFAD,EAAM9C,UAAYlB,OAAOY,OAAOqD,EAAO/C,WACvCuC,EAAQO,EAAM9C,UAAW,cAAe8C,GACjC,CACHrB,OAAQU,EAAMvC,KAAK,KAAMkD,EAAM9C,cAK/C,IAAIgD,EAA2BlE,OAAOkE,yBACtC,SAASC,EAAsBvB,EAAKQ,GAChC,IACIE,EACJ,OAFSY,EAAyBtB,EAAKQ,KAEzBE,EAAQN,EAASJ,KAASuB,EAAsBb,EAAOF,GAEzE,IAAIgB,EAAS,GAAGlC,MAChB,SAASA,EAAMmC,EAAMC,EAAOC,GACxB,OAAOH,EAAO3E,KAAK4E,EAAMC,EAAOC,GAEpC,SAASC,EAASC,EAAUC,GACxB,OAAOA,EAAiBD,GAE5B,SAASE,EAAOC,GACZ,IAAKA,EACD,MAAM,IAAIC,MAAM,oBAExB,SAASC,EAAOC,GACR3C,EAAQ4C,aACRA,aAAaD,GAEbE,WAAWF,EAAI,GAEvB,SAASG,EAAcC,EAAOC,GAC1B,OAAOD,EAAME,QAAO,SAAUC,EAAQC,EAAMjG,GACxC,IAAIkG,EAAeJ,EAAUG,EAAMjG,GAGnC,OAFIkG,IACAF,EAAOE,EAAa,IAAMA,EAAa,IACpCF,IACR,IAUP,SAASG,EAAa7C,EAAK8C,GACvB,GAAIvC,EAAOP,EAAK8C,GACZ,OAAO9C,EAAI8C,GACf,IAAKA,EACD,OAAO9C,EACX,GAAuB,iBAAZ8C,EAAsB,CAE7B,IADA,IAAIC,EAAK,GACArG,EAAI,EAAGC,EAAImG,EAAQjE,OAAQnC,EAAIC,IAAKD,EAAG,CAC5C,IAAIsG,EAAMH,EAAa7C,EAAK8C,EAAQpG,IACpCqG,EAAGE,KAAKD,GAEZ,OAAOD,EAEX,IAAIG,EAASJ,EAAQK,QAAQ,KAC7B,IAAgB,IAAZD,EAAe,CACf,IAAIE,EAAWpD,EAAI8C,EAAQO,OAAO,EAAGH,IACrC,YAAoBI,IAAbF,OAAyBE,EAAYT,EAAaO,EAAUN,EAAQO,OAAOH,EAAS,KAInG,SAASK,EAAavD,EAAK8C,EAASnF,GAChC,GAAKqC,QAAmBsD,IAAZR,MAER,aAAc1F,UAAUA,OAAOoG,SAASxD,IAE5C,GAAuB,iBAAZ8C,GAAwB,WAAYA,EAAS,CACpDf,EAAwB,iBAAVpE,GAAsB,WAAYA,GAChD,IAAK,IAAIjB,EAAI,EAAGC,EAAImG,EAAQjE,OAAQnC,EAAIC,IAAKD,EACzC6G,EAAavD,EAAK8C,EAAQpG,GAAIiB,EAAMjB,QAGvC,CACD,IAAIwG,EAASJ,EAAQK,QAAQ,KAC7B,IAAgB,IAAZD,EAAe,CACf,IAAIO,EAAiBX,EAAQO,OAAO,EAAGH,GACnCQ,EAAmBZ,EAAQO,OAAOH,EAAS,GAC/C,GAAyB,KAArBQ,OACcJ,IAAV3F,EACImC,EAAQE,KAAS2D,MAAMC,SAASH,IAChCzD,EAAI6D,OAAOJ,EAAgB,UAEpBzD,EAAIyD,GAGfzD,EAAIyD,GAAkB9F,MACzB,CACD,IAAIyF,EAAWpD,EAAIyD,GACdL,GAAa7C,EAAOP,EAAKyD,KAC1BL,EAAYpD,EAAIyD,GAAkB,IACtCF,EAAaH,EAAUM,EAAkB/F,cAI/B2F,IAAV3F,EACImC,EAAQE,KAAS2D,MAAMC,SAASd,IAChC9C,EAAI6D,OAAOf,EAAS,UAEb9C,EAAI8C,GAGf9C,EAAI8C,GAAWnF,GAY/B,SAASmG,EAAa9D,GAClB,IAAI+C,EAAK,GACT,IAAK,IAAIjG,KAAKkD,EACNO,EAAOP,EAAKlD,KACZiG,EAAGjG,GAAKkD,EAAIlD,IAEpB,OAAOiG,EAEX,IAAIxD,EAAS,GAAGA,OAChB,SAASwE,EAAQC,GACb,OAAOzE,EAAOT,MAAM,GAAIkF,GAE5B,IAAIC,EAAqB,oJACpBC,MAAM,KAAK3E,OAAOwE,EAAQ,CAAC,EAAG,GAAI,GAAI,IAAII,KAAI,SAAUC,GAAO,MAAO,CAAC,MAAO,OAAQ,SAASD,KAAI,SAAUvG,GAAK,OAAOA,EAAIwG,EAAM,gBAAkBC,QAAO,SAAUzG,GAAK,OAAO4B,EAAQ5B,MAC3L0G,EAAiBL,EAAmBE,KAAI,SAAUvG,GAAK,OAAO4B,EAAQ5B,MAC1E0E,EAAc2B,GAAoB,SAAUM,GAAK,MAAO,CAACA,GAAG,MAC5D,IAAIC,EAAe,KACnB,SAASC,EAAUC,GACfF,EAAkC,oBAAZG,SAA2B,IAAIA,QACrD,IAAI5B,EAIR,SAAS6B,EAAeF,GACpB,IAAKA,GAAsB,iBAARA,EACf,OAAOA,EACX,IAAI3B,EAAKyB,GAAgBA,EAAajH,IAAImH,GAC1C,GAAI3B,EACA,OAAOA,EACX,GAAIjD,EAAQ4E,GAAM,CACd3B,EAAK,GACLyB,GAAgBA,EAAaxD,IAAI0D,EAAK3B,GACtC,IAAK,IAAIrG,EAAI,EAAGC,EAAI+H,EAAI7F,OAAQnC,EAAIC,IAAKD,EACrCqG,EAAGE,KAAK2B,EAAeF,EAAIhI,UAG9B,GAAI4H,EAAenB,QAAQuB,EAAIG,cAAgB,EAChD9B,EAAK2B,MAEJ,CACD,IAAIhE,EAAQN,EAASsE,GAGrB,IAAK,IAAIlE,KAFTuC,EAAKrC,IAAUtD,OAAOkB,UAAY,GAAKlB,OAAOY,OAAO0C,GACrD8D,GAAgBA,EAAaxD,IAAI0D,EAAK3B,GACrB2B,EACTnE,EAAOmE,EAAKlE,KACZuC,EAAGvC,GAAQoE,EAAeF,EAAIlE,KAI1C,OAAOuC,EA9BE6B,CAAeF,GAExB,OADAF,EAAe,KACRzB,EA8BX,IAAI,EAAW,GAAG+B,SAClB,SAASpH,EAAYP,GACjB,OAAO,EAASN,KAAKM,GAAGmC,MAAM,GAAI,GAEtC,IAAIyF,EAAmC,oBAAXtH,OACxBA,OAAOuH,SACP,aACAC,EAA0C,iBAAnBF,EAA8B,SAAUR,GAC/D,IAAI7H,EACJ,OAAY,MAAL6H,IAAc7H,EAAI6H,EAAEQ,KAAoBrI,EAAEoC,MAAMyF,IACvD,WAAc,OAAO,MACrBW,EAAgB,GACpB,SAASC,EAAWC,GAChB,IAAI1I,EAAGsH,EAAGO,EAAGc,EACb,GAAyB,IAArBzG,UAAUC,OAAc,CACxB,GAAIiB,EAAQsF,GACR,OAAOA,EAAU9F,QACrB,GAAIP,OAASmG,GAAsC,iBAAdE,EACjC,MAAO,CAACA,GACZ,GAAKC,EAAKJ,EAAcG,GAAa,CAEjC,IADApB,EAAI,KACIO,EAAIc,EAAGC,QAAYC,MACvBvB,EAAEf,KAAKsB,EAAE5G,OACb,OAAOqG,EAEX,GAAiB,MAAboB,EACA,MAAO,CAACA,GAEZ,GAAiB,iBADjB1I,EAAI0I,EAAUvG,QACa,CAEvB,IADAmF,EAAI,IAAI3E,MAAM3C,GACPA,KACHsH,EAAEtH,GAAK0I,EAAU1I,GACrB,OAAOsH,EAEX,MAAO,CAACoB,GAIZ,IAFA1I,EAAIkC,UAAUC,OACdmF,EAAI,IAAI3E,MAAM3C,GACPA,KACHsH,EAAEtH,GAAKkC,UAAUlC,GACrB,OAAOsH,EAEX,IAAIwB,EAAoC,oBAAX/H,OACvB,SAAU0E,GAAM,MAAkC,kBAA3BA,EAAG1E,OAAOC,cACjC,WAAc,OAAO,GAEvB+H,EAA4B,oBAAbC,UACf,6CAA6CC,KAAKD,SAASE,MAC/D,SAASC,EAASlI,EAAO0G,GACrBoB,EAAQ9H,EACRmI,EAAgBzB,EAEpB,IAAIyB,EAAgB,WAAc,OAAO,GACrCC,GAAyB,IAAI9D,MAAM,IAAI+D,MAC3C,SAASC,IACL,GAAIF,EACA,IAEI,MADAE,EAAkBrH,UACZ,IAAIqD,MAEd,MAAOiE,GACH,OAAOA,EAEf,OAAO,IAAIjE,MAEf,SAASkE,EAAYC,EAAWC,GAC5B,IAAIL,EAAQI,EAAUJ,MACtB,OAAKA,GAELK,EAAoBA,GAAoB,EACF,IAAlCL,EAAM7C,QAAQiD,EAAUnJ,QACxBoJ,IAAqBD,EAAUnJ,KAAOmJ,EAAUE,SAASpC,MAAM,MAAMrF,QAClEmH,EAAM9B,MAAM,MACd5E,MAAM+G,GACNhC,OAAOyB,GACP3B,KAAI,SAAUoC,GAAS,MAAO,KAAOA,KACrCC,KAAK,KARC,GAWf,IAkBIC,EAAmB,CACnB,UACA,aACA,OACA,sBACA,WACA,UACA,WACA,eACA,gBACA,QACA,UACA,gBACA,SACA,aAEAC,EAlCkB,CAClB,SACA,OACA,aACA,gBACA,SACA,UACA,eACA,aACA,iBACA,kBACA,iBACA,cACA,WACA,iBACA,kBACA,gBAkB4BnH,OAAOkH,GACnCE,EAAe,CACfC,eAAgB,wDAChBC,eAAgB,2BAChBC,MAAO,sBACPC,oBAAqB,8CACrBC,WAAY,oEAEhB,SAASC,EAAWhK,EAAMiK,GACtBnI,KAAKoI,GAAKlB,IACVlH,KAAK9B,KAAOA,EACZ8B,KAAKuH,QAAUY,EAWnB,SAASE,EAAqBF,EAAKG,GAC/B,OAAOH,EAAM,aAAe9J,OAAOyC,KAAKwH,GACnClD,KAAI,SAAUlG,GAAO,OAAOoJ,EAASpJ,GAAK6G,cAC1CT,QAAO,SAAUiD,EAAG5K,EAAG+B,GAAK,OAAOA,EAAE0E,QAAQmE,KAAO5K,KACpD8J,KAAK,MAEd,SAASe,EAAYL,EAAKG,EAAUG,EAAcC,GAC9C1I,KAAKoI,GAAKlB,IACVlH,KAAKsI,SAAWA,EAChBtI,KAAK0I,WAAaA,EAClB1I,KAAKyI,aAAeA,EACpBzI,KAAKuH,QAAUc,EAAqBF,EAAKG,GAG7C,SAASK,EAAUR,EAAKG,GACpBtI,KAAKoI,GAAKlB,IACVlH,KAAK9B,KAAO,YACZ8B,KAAKsI,SAAWjK,OAAOyC,KAAKwH,GAAUlD,KAAI,SAAUwD,GAAO,OAAON,EAASM,MAC3E5I,KAAK6I,cAAgBP,EACrBtI,KAAKuH,QAAUc,EAAqBF,EAAKG,GA5B7ClG,EAAO8F,GAAY/H,KAAK+C,OAAOlC,OAAO,CAClCiG,MAAO,CACHzI,IAAK,WACD,OAAOwB,KAAK8I,SACP9I,KAAK8I,OAAS9I,KAAK9B,KAAO,KAAO8B,KAAKuH,QAAUH,EAAYpH,KAAKoI,GAAI,MAGlFrC,SAAU,WAAc,OAAO/F,KAAK9B,KAAO,KAAO8B,KAAKuH,WAe3DnF,EAAOoG,GAAarI,KAAK+H,GAQzB9F,EAAOuG,GAAWxI,KAAK+H,GACvB,IAAIa,EAAWpB,EAAUjE,QAAO,SAAUzC,EAAK/C,GAAQ,OAAQ+C,EAAI/C,GAAQA,EAAO,QAAS+C,IAAS,IAChG+H,GAAgBd,EAChBe,GAAatB,EAAUjE,QAAO,SAAUzC,EAAK/C,GAC7C,IAAIgL,EAAWhL,EAAO,QACtB,SAASgK,EAAWiB,EAAYC,GAC5BpJ,KAAKoI,GAAKlB,IACVlH,KAAK9B,KAAOgL,EACPC,EAI0B,iBAAfA,GACZnJ,KAAKuH,QAAe4B,GAAeC,EAAa,MAAQA,EAAb,IAC3CpJ,KAAKoJ,MAAQA,GAAS,MAEK,iBAAfD,IACZnJ,KAAKuH,QAAU4B,EAAWjL,KAAO,IAAMiL,EAAW5B,QAClDvH,KAAKoJ,MAAQD,IATbnJ,KAAKuH,QAAUK,EAAa1J,IAASgL,EACrClJ,KAAKoJ,MAAQ,MAarB,OAFAhH,EAAO8F,GAAY/H,KAAK6I,IACxB/H,EAAI/C,GAAQgK,EACLjH,IACR,IACHgI,GAAWI,OAASC,YACpBL,GAAWM,KAAOC,UAClBP,GAAWQ,MAAQC,WACnB,IAAIC,GAAejC,EAAiBhE,QAAO,SAAUzC,EAAK/C,GAEtD,OADA+C,EAAI/C,EAAO,SAAW+K,GAAW/K,GAC1B+C,IACR,IAYH,IAAI2I,GAAqBjC,EAAUjE,QAAO,SAAUzC,EAAK/C,GAGrD,OAFmD,IAA/C,CAAC,SAAU,OAAQ,SAASkG,QAAQlG,KACpC+C,EAAI/C,EAAO,SAAW+K,GAAW/K,IAC9B+C,IACR,IAKH,SAAS4I,MACT,SAASC,GAAO7F,GAAO,OAAOA,EAC9B,SAAS8F,GAAkBC,EAAIC,GAC3B,OAAU,MAAND,GAAcA,IAAOF,GACdG,EACJ,SAAUhG,GACb,OAAOgG,EAAGD,EAAG/F,KAGrB,SAASiG,GAASC,EAAKC,GACnB,OAAO,WACHD,EAAIpK,MAAMC,KAAMH,WAChBuK,EAAIrK,MAAMC,KAAMH,YAGxB,SAASwK,GAAkBL,EAAIC,GAC3B,OAAID,IAAOH,GACAI,EACJ,WACH,IAAIK,EAAMN,EAAGjK,MAAMC,KAAMH,gBACb0E,IAAR+F,IACAzK,UAAU,GAAKyK,GACnB,IAAIC,EAAYvK,KAAKuK,UACrBC,EAAUxK,KAAKwK,QACfxK,KAAKuK,UAAY,KACjBvK,KAAKwK,QAAU,KACf,IAAIC,EAAOR,EAAGlK,MAAMC,KAAMH,WAK1B,OAJI0K,IACAvK,KAAKuK,UAAYvK,KAAKuK,UAAYL,GAASK,EAAWvK,KAAKuK,WAAaA,GACxEC,IACAxK,KAAKwK,QAAUxK,KAAKwK,QAAUN,GAASM,EAASxK,KAAKwK,SAAWA,QACpDjG,IAATkG,EAAqBA,EAAOH,GAG3C,SAASI,GAAkBV,EAAIC,GAC3B,OAAID,IAAOH,GACAI,EACJ,WACHD,EAAGjK,MAAMC,KAAMH,WACf,IAAI0K,EAAYvK,KAAKuK,UACrBC,EAAUxK,KAAKwK,QACfxK,KAAKuK,UAAYvK,KAAKwK,QAAU,KAChCP,EAAGlK,MAAMC,KAAMH,WACX0K,IACAvK,KAAKuK,UAAYvK,KAAKuK,UAAYL,GAASK,EAAWvK,KAAKuK,WAAaA,GACxEC,IACAxK,KAAKwK,QAAUxK,KAAKwK,QAAUN,GAASM,EAASxK,KAAKwK,SAAWA,IAG5E,SAASG,GAAkBX,EAAIC,GAC3B,OAAID,IAAOH,GACAI,EACJ,SAAUW,GACb,IAAIN,EAAMN,EAAGjK,MAAMC,KAAMH,WACzBmB,EAAO4J,EAAeN,GACtB,IAAIC,EAAYvK,KAAKuK,UACrBC,EAAUxK,KAAKwK,QACfxK,KAAKuK,UAAY,KACjBvK,KAAKwK,QAAU,KACf,IAAIC,EAAOR,EAAGlK,MAAMC,KAAMH,WAK1B,OAJI0K,IACAvK,KAAKuK,UAAYvK,KAAKuK,UAAYL,GAASK,EAAWvK,KAAKuK,WAAaA,GACxEC,IACAxK,KAAKwK,QAAUxK,KAAKwK,QAAUN,GAASM,EAASxK,KAAKwK,SAAWA,QACrDjG,IAAR+F,OACO/F,IAATkG,OAAqBlG,EAAYkG,EACjCzJ,EAAOsJ,EAAKG,IAGzB,SAASI,GAA2Bb,EAAIC,GACpC,OAAID,IAAOH,GACAI,EACJ,WACH,OAAkC,IAA9BA,EAAGlK,MAAMC,KAAMH,YAEZmK,EAAGjK,MAAMC,KAAMH,YAG9B,SAASiL,GAAgBd,EAAIC,GACzB,OAAID,IAAOH,GACAI,EACJ,WACH,IAAIK,EAAMN,EAAGjK,MAAMC,KAAMH,WACzB,GAAIyK,GAA2B,mBAAbA,EAAIS,KAAqB,CAEvC,IADA,IAAIC,EAAOhL,KAAMrC,EAAIkC,UAAUC,OAAQ4C,EAAO,IAAIpC,MAAM3C,GACjDA,KACH+E,EAAK/E,GAAKkC,UAAUlC,GACxB,OAAO2M,EAAIS,MAAK,WACZ,OAAOd,EAAGlK,MAAMiL,EAAMtI,MAG9B,OAAOuH,EAAGlK,MAAMC,KAAMH,YA/F9B+J,GAAmBpB,YAAcA,EACjCoB,GAAmB1B,WAAaA,EAChC0B,GAAmBjB,UAAYA,EAiG/B,IAAIsC,GAAW,GAE8BC,GAA0B,oBAAZ9J,QACvD,GACA,WACI,IAAI+J,EAAU/J,QAAQgK,UACtB,GAAsB,oBAAXC,SAA2BA,OAAOC,OACzC,MAAO,CAACH,EAAS9J,EAAS8J,GAAUA,GACxC,IAAII,EAAUF,OAAOC,OAAOE,OAAO,UAAW,IAAIC,WAAW,CAAC,KAC9D,MAAO,CACHF,EACAlK,EAASkK,GACTJ,GARR,GAUMO,GAAwBR,GAAK,GAAIS,GAAqBT,GAAK,GAAIU,GAAwBV,GAAK,GAAIW,GAAoBF,IAAsBA,GAAmBZ,KACnKe,GAAgBJ,IAAyBA,GAAsB5F,YAC/DiG,KAAuBH,GACvBI,IAAwB,EACxBC,GAAuBL,GACvB,WAAcA,GAAsBb,KAAKmB,KAErCzL,EAAQ4C,aACJA,aAAalE,KAAK,KAAM+M,IACxBzL,EAAQ0L,iBACJ,WACI,IAAIC,EAAYC,SAASC,cAAc,OACvC,IAAKH,kBAAiB,WAClBD,KACAE,EAAY,QACZG,QAAQH,EAAW,CAAEI,YAAY,IACrCJ,EAAUK,aAAa,IAAK,MAEhC,WAAcnJ,WAAW4I,GAAc,IACnDQ,GAAO,SAAUC,EAAUjK,GAC3BkK,GAAe1I,KAAK,CAACyI,EAAUjK,IAC3BmK,KACAZ,KACAY,IAAuB,IAG3BC,IAAqB,EACzBD,IAAuB,EACvBE,GAAkB,GAClBC,GAAkB,GAClBC,GAAmB,KAAMC,GAAkBpD,GACvCqD,GAAY,CACZC,GAAI,SACJvM,QAAQ,EACRwM,IAAK,EACLC,WAAY,GACZC,YAAaC,GACbC,KAAK,EACLC,IAAK,GACLC,SAAU,WACN3N,KAAKsN,WAAWnM,SAAQ,SAAUyM,GAC9B,IACIJ,GAAYI,EAAG,GAAIA,EAAG,IAE1B,MAAOzG,UAIf0G,GAAMV,GACNP,GAAiB,GACjBkB,GAAoB,EACpBC,GAAiB,GACrB,SAASC,GAAa5K,GAClB,GAAoB,iBAATpD,KACP,MAAM,IAAIwJ,UAAU,wCACxBxJ,KAAKiO,WAAa,GAClBjO,KAAKkO,YAAcrE,GACnB7J,KAAKmO,MAAO,EACZ,IAAIC,EAAOpO,KAAKqO,KAAOR,GAMvB,GALInH,IACA1G,KAAKsO,aAAepH,IACpBlH,KAAKuO,MAAQ,KACbvO,KAAKwO,SAAW,GAEF,mBAAPpL,EAAmB,CAC1B,GAAIA,IAAO6H,GACP,MAAM,IAAIzB,UAAU,kBAKxB,OAJAxJ,KAAKyO,OAAS5O,UAAU,GACxBG,KAAK0O,OAAS7O,UAAU,SACJ,IAAhBG,KAAKyO,QACLE,GAAgB3O,KAAMA,KAAK0O,SAGnC1O,KAAKyO,OAAS,KACdzO,KAAK0O,OAAS,OACZN,EAAIf,IACNuB,GAAmB5O,KAAMoD,GAE7B,IAAIyL,GAAW,CACXrQ,IAAK,WACD,IAAI4P,EAAMP,GAAKiB,EAAcC,GAC7B,SAAShE,EAAKiE,EAAaC,GACvB,IAAIC,EAAQlP,KACRmP,GAAiBf,EAAIvN,SAAWuN,IAAQP,IAAOiB,IAAgBC,IAC/DK,EAAUD,IAAkBE,KAC5BrL,EAAK,IAAIgK,IAAa,SAAU5C,EAASkE,GACzCC,GAAoBL,EAAO,IAAIM,GAASC,GAA0BT,EAAaZ,EAAKe,EAAeC,GAAUK,GAA0BR,EAAYb,EAAKe,EAAeC,GAAUhE,EAASkE,EAAQlB,OAGtM,OADA1H,GAASgJ,GAAsB1L,EAAIhE,MAC5BgE,EAGX,OADA+G,EAAKxL,UAAY0L,GACVF,GAEX9I,IAAK,SAAUrD,GACXkD,EAAQ9B,KAAM,OAAQpB,GAASA,EAAMW,YAAc0L,GAC/C4D,GACA,CACIrQ,IAAK,WACD,OAAOI,GAEXqD,IAAK4M,GAAS5M,QA0D9B,SAASuN,GAASR,EAAaC,EAAY7D,EAASkE,EAAQK,GACxD3P,KAAKgP,YAAqC,mBAAhBA,EAA6BA,EAAc,KACrEhP,KAAKiP,WAAmC,mBAAfA,EAA4BA,EAAa,KAClEjP,KAAKoL,QAAUA,EACfpL,KAAKsP,OAASA,EACdtP,KAAKoO,IAAMuB,EAgGf,SAASf,GAAmBgB,EAASxM,GACjC,IACIA,GAAG,SAAUxE,GACT,GAAuB,OAAnBgR,EAAQnB,OAAZ,CAEA,GAAI7P,IAAUgR,EACV,MAAM,IAAIpG,UAAU,6CACxB,IAAIqG,EAAoBD,EAAQzB,MAAQ2B,KACpClR,GAA+B,mBAAfA,EAAMmM,KACtB6D,GAAmBgB,GAAS,SAAUxE,EAASkE,GAC3C1Q,aAAiBoP,GACbpP,EAAMmR,MAAM3E,EAASkE,GACrB1Q,EAAMmM,KAAKK,EAASkE,OAI5BM,EAAQnB,QAAS,EACjBmB,EAAQlB,OAAS9P,EACjBoR,GAAsBJ,IAEtBC,GACAI,QACLtB,GAAgBxP,KAAK,KAAMyQ,IAElC,MAAOM,GACHvB,GAAgBiB,EAASM,IAGjC,SAASvB,GAAgBiB,EAASO,GAE9B,GADAnD,GAAgB9I,KAAKiM,GACE,OAAnBP,EAAQnB,OAAZ,CAEA,IAAIoB,EAAoBD,EAAQzB,MAAQ2B,KACxCK,EAASjD,GAAgBiD,GACzBP,EAAQnB,QAAS,EACjBmB,EAAQlB,OAASyB,EACjBzJ,GAAoB,OAAXyJ,GAAqC,iBAAXA,IAAwBA,EAAOC,UAxuBtE,SAAkBhN,EAAIoH,EAAS9H,GAC3B,IACIU,EAAGrD,MAAM,KAAM2C,GAEnB,MAAOwN,GACH1F,GAAWA,EAAQ0F,IAmuBuDG,EAAS,WACnF,IAAIC,EAAW9N,EAAsB2N,EAAQ,SAC7CA,EAAOC,SAAWR,EAClB9N,EAAQqO,EAAQ,QAAS,CACrB3R,IAAK,WACD,OAAOwN,GACHsE,IAAaA,EAAS9R,IAClB8R,EAAS9R,IAAIuB,MAAMoQ,GACnBG,EAAS1R,OACbgR,EAAQ3I,YAiJ5B,SAAmC2I,GAC1B7C,GAAgBwD,MAAK,SAAU9Q,GAAK,OAAOA,EAAEiP,SAAWkB,EAAQlB,WACjE3B,GAAgB7I,KAAK0L,GA/IzBY,CAA0BZ,GAC1BI,GAAsBJ,GAClBC,GACAI,MAER,SAASD,GAAsBJ,GAC3B,IAAIa,EAAYb,EAAQ3B,WACxB2B,EAAQ3B,WAAa,GACrB,IAAK,IAAItQ,EAAI,EAAG+S,EAAMD,EAAU3Q,OAAQnC,EAAI+S,IAAO/S,EAC/C4R,GAAoBK,EAASa,EAAU9S,IAE3C,IAAIyQ,EAAMwB,EAAQvB,OAChBD,EAAIf,KAAOe,EAAIT,WACS,IAAtBG,OACEA,GACFpB,IAAK,WAC2B,KAAtBoB,IACF6C,OACL,KAGX,SAASpB,GAAoBK,EAASgB,GAClC,GAAuB,OAAnBhB,EAAQnB,OAAZ,CAIA,IAAIoC,EAAKjB,EAAQnB,OAASmC,EAAS5B,YAAc4B,EAAS3B,WAC1D,GAAW,OAAP4B,EACA,OAAQjB,EAAQnB,OAASmC,EAASxF,QAAUwF,EAAStB,QAAQM,EAAQlB,UAEvEkC,EAASxC,IAAIf,MACbS,GACFpB,GAAKoE,GAAc,CAACD,EAAIjB,EAASgB,SAT7BhB,EAAQ3B,WAAW/J,KAAK0M,GAWhC,SAASE,GAAaD,EAAIjB,EAASgB,GAC/B,IACI3D,GAAmB2C,EACnB,IAAImB,EAAKnS,EAAQgR,EAAQlB,OACrBkB,EAAQnB,OACRsC,EAAMF,EAAGjS,IAGLoO,GAAgBlN,SAChBkN,GAAkB,IACtB+D,EAAMF,EAAGjS,IAC+B,IAApCoO,GAAgB5I,QAAQxF,IAoGxC,SAA4BgR,GACxB,IAAIjS,EAAIoP,GAAgBjN,OACxB,KAAOnC,GACH,GAAIoP,KAAkBpP,GAAG+Q,SAAWkB,EAAQlB,OAExC,YADA3B,GAAgBjI,OAAOnH,EAAG,GAvGtBqT,CAAmBpB,IAE3BgB,EAASxF,QAAQ2F,GAErB,MAAO5J,GACHyJ,EAAStB,OAAOnI,GAEpB,QACI8F,GAAmB,KACS,KAAtBa,IACF6C,OACFC,EAASxC,IAAIf,KAAOuD,EAASxC,IAAIT,YA6B3C,SAAS+B,GAAsBE,EAASqB,GACpC,IAAIC,EAAUD,EAAOA,EAAKzC,SAAW,EAAI,EACrC0C,EA1ZqB,MA2ZrBtB,EAAQrB,MAAQ0C,EAChBrB,EAAQpB,SAAW0C,GAG3B,SAAShF,KACL4D,MAAyBG,KAE7B,SAASH,KACL,IAAIqB,EAAcrE,GAGlB,OAFAA,IAAqB,EACrBD,IAAuB,EAChBsE,EAEX,SAASlB,KACL,IAAImB,EAAWzT,EAAGC,EAClB,GACI,KAAOgP,GAAe9M,OAAS,GAI3B,IAHAsR,EAAYxE,GACZA,GAAiB,GACjBhP,EAAIwT,EAAUtR,OACTnC,EAAI,EAAGA,EAAIC,IAAKD,EAAG,CACpB,IAAIiG,EAAOwN,EAAUzT,GACrBiG,EAAK,GAAG7D,MAAM,KAAM6D,EAAK,WAG5BgJ,GAAe9M,OAAS,GACjCgN,IAAqB,EACrBD,IAAuB,EAE3B,SAAS8D,KACL,IAAIU,EAAgBtE,GACpBA,GAAkB,GAClBsE,EAAclQ,SAAQ,SAAU1B,GAC5BA,EAAE4O,KAAKd,YAAYzP,KAAK,KAAM2B,EAAEiP,OAAQjP,MAI5C,IAFA,IAAI6R,EAAavD,GAAexN,MAAM,GAClC5C,EAAI2T,EAAWxR,OACZnC,GACH2T,IAAa3T,KA0BrB,SAAS4T,GAAcpB,GACnB,OAAO,IAAInC,GAAa/C,IAAU,EAAOkF,GAE7C,SAASqB,GAAKpO,EAAIqO,GACd,IAAIrD,EAAMP,GACV,OAAO,WACH,IAAIsD,EAAcrB,KAAuB4B,EAAa7D,GACtD,IAEI,OADA8D,GAAavD,GAAK,GACXhL,EAAGrD,MAAMC,KAAMH,WAE1B,MAAOsH,GACHsK,GAAgBA,EAAatK,GAEjC,QACIwK,GAAaD,GAAY,GACrBP,GACAlB,OAtXhBvO,EAAMsM,GAAazO,UAAW,CAC1BwL,KAAM8D,GACNkB,MAAO,SAAUf,EAAaC,GAC1BM,GAAoBvP,KAAM,IAAIwP,GAAS,KAAM,KAAMR,EAAaC,EAAYpB,MAEhF+D,MAAO,SAAU3C,GACb,GAAyB,IAArBpP,UAAUC,OACV,OAAOE,KAAK+K,KAAK,KAAMkE,GAC3B,IAAI4C,EAAOhS,UAAU,GAAIiS,EAAUjS,UAAU,GAC7C,MAAuB,mBAATgS,EAAsB7R,KAAK+K,KAAK,MAAM,SAAUgH,GAC1D,OAAOA,aAAeF,EAAOC,EAAQC,GAAOR,GAAcQ,MAExD/R,KAAK+K,KAAK,MAAM,SAAUgH,GACxB,OAAOA,GAAOA,EAAI7T,OAAS2T,EAAOC,EAAQC,GAAOR,GAAcQ,OAG3EC,QAAS,SAAUC,GACf,OAAOjS,KAAK+K,MAAK,SAAUnM,GAEvB,OADAqT,IACOrT,KACR,SAAUmT,GAET,OADAE,IACOV,GAAcQ,OAG7B9K,MAAO,CACHzI,IAAK,WACD,GAAIwB,KAAK8I,OACL,OAAO9I,KAAK8I,OAChB,IACIkD,IAAwB,EACxB,IACI/E,EAwOpB,SAASiL,EAAStC,EAASuC,EAAQC,GAC/B,GAAID,EAAOrS,SAAWsS,EAClB,OAAOD,EACX,IAAIlL,EAAQ,GACZ,IAAuB,IAAnB2I,EAAQnB,OAAkB,CAC1B,IAA8B4D,EAAW9K,EAArC+K,EAAU1C,EAAQlB,OACP,MAAX4D,GACAD,EAAYC,EAAQpU,MAAQ,QAC5BqJ,EAAU+K,EAAQ/K,SAAW+K,EAC7BrL,EAAQG,EAAYkL,EAAS,KAG7BD,EAAYC,EACZ/K,EAAU,IAEd4K,EAAOjO,KAAKmO,GAAa9K,EAAU,KAAOA,EAAU,IAAMN,GAE1DP,KACAO,EAAQG,EAAYwI,EAAQtB,aAAc,MACF,IAA3B6D,EAAO/N,QAAQ6C,IACxBkL,EAAOjO,KAAK+C,GACZ2I,EAAQrB,OACR2D,EAAStC,EAAQrB,MAAO4D,EAAQC,IAExC,OAAOD,EAjQkBD,CAASlS,KAAM,GApJ1B,IAqJiByH,KAAK,qBAGxB,OAFoB,OAAhBzH,KAAKyO,SACLzO,KAAK8I,OAAS7B,GACXA,EAEX,QACI+E,IAAwB,KAIpCuG,QAAS,SAAUC,EAAIrK,GACnB,IAAI+G,EAAQlP,KACZ,OAAOwS,EAAKC,IACR,IAAIzE,IAAa,SAAU5C,EAASkE,GAChC,IAAIoD,EAASpP,YAAW,WAAc,OAAOgM,EAAO,IAAIrG,GAAW0J,QAAQxK,MAAUqK,GACrFtD,EAAMnE,KAAKK,EAASkE,GAAQ0C,QAAQY,aAAazT,KAAK,KAAMuT,OAC3D1S,QAGK,oBAAXtB,QAA0BA,OAAOC,aACxCmD,EAAQkM,GAAazO,UAAWb,OAAOC,YAAa,iBACxDwO,GAAUO,IAAMmF,KAQhBnR,EAAMsM,GAAc,CAChB8E,IAAK,WACD,IAAIC,EAAS3M,EAAWrG,MAAM,KAAMF,WAC/BuF,IAAI4N,IACT,OAAO,IAAIhF,IAAa,SAAU5C,EAASkE,GACjB,IAAlByD,EAAOjT,QACPsL,EAAQ,IACZ,IAAI6H,EAAYF,EAAOjT,OACvBiT,EAAO5R,SAAQ,SAAU8D,EAAGtH,GAAK,OAAOqQ,GAAa5C,QAAQnG,GAAG8F,MAAK,SAAUvF,GAC3EuN,EAAOpV,GAAK6H,IACLyN,GACH7H,EAAQ2H,KACbzD,UAGXlE,QAAS,SAAUxM,GACf,GAAIA,aAAiBoP,GACjB,OAAOpP,EACX,GAAIA,GAA+B,mBAAfA,EAAMmM,KACtB,OAAO,IAAIiD,IAAa,SAAU5C,EAASkE,GACvC1Q,EAAMmM,KAAKK,EAASkE,MAE5B,IAAItL,EAAK,IAAIgK,GAAa/C,IAAU,EAAMrM,GAE1C,OADA8Q,GAAsB1L,EAAIiJ,IACnBjJ,GAEXsL,OAAQiC,GACR2B,KAAM,WACF,IAAIH,EAAS3M,EAAWrG,MAAM,KAAMF,WAAWuF,IAAI4N,IACnD,OAAO,IAAIhF,IAAa,SAAU5C,EAASkE,GACvCyD,EAAO3N,KAAI,SAAUxG,GAAS,OAAOoP,GAAa5C,QAAQxM,GAAOmM,KAAKK,EAASkE,UAGvFzB,IAAK,CACDrP,IAAK,WAAc,OAAOqP,IAC1B5L,IAAK,SAAUrD,GAAS,OAAOiP,GAAMjP,IAEzCmQ,YAAa,CAAEvQ,IAAK,WAAc,OAAOuQ,KACzCoE,OAAQC,GACRC,OAAQA,GACRC,UAAW,CACP9U,IAAK,WAAc,OAAOkO,IAC1BzK,IAAK,SAAUrD,GAAS8N,GAAO9N,IAEnCsO,gBAAiB,CACb1O,IAAK,WAAc,OAAO0O,IAC1BjL,IAAK,SAAUrD,GAASsO,GAAkBtO,IAE9C2U,OAAQ,SAAUnQ,EAAIoQ,GAClB,OAAO,IAAIxF,IAAa,SAAU5C,EAASkE,GACvC,OAAO8D,IAAS,SAAUhI,EAASkE,GAC/B,IAAIlB,EAAMP,GACVO,EAAId,WAAa,GACjBc,EAAIb,YAAc+B,EAClBlB,EAAIT,SAAWzD,IAAS,WACpB,IAAIgF,EAAQlP,MAyNhC,SAAkDoD,GAK9C2K,GAAe7J,MAJf,SAASuP,IACLrQ,IACA2K,GAAejJ,OAAOiJ,GAAe3J,QAAQqP,GAAY,QAG3D3F,GACFpB,IAAK,WAC2B,KAAtBoB,IACF6C,OACL,IAlOa+C,EAAyC,WACT,IAA5BxE,EAAM5B,WAAWxN,OAAesL,IAAYkE,EAAOJ,EAAM5B,WAAW,SAEzEc,EAAIT,UACPvK,MACDoQ,EAAWpI,EAASkE,SAI/BxD,KACIA,GAAc6H,YACd7R,EAAQkM,GAAc,cAAc,WAChC,IAAI4F,EAAmBxN,EAAWrG,MAAM,KAAMF,WAAWuF,IAAI4N,IAC7D,OAAO,IAAIhF,IAAa,SAAU5C,GACE,IAA5BwI,EAAiB9T,QACjBsL,EAAQ,IACZ,IAAI6H,EAAYW,EAAiB9T,OAC7B+T,EAAU,IAAIvT,MAAM2S,GACxBW,EAAiBzS,SAAQ,SAAU1B,EAAG9B,GAAK,OAAOqQ,GAAa5C,QAAQ3L,GAAGsL,MAAK,SAAUnM,GAAS,OAAOiV,EAAQlW,GAAK,CAAEmW,OAAQ,YAAalV,MAAOA,MAAY,SAAUuR,GAAU,OAAO0D,EAAQlW,GAAK,CAAEmW,OAAQ,WAAY3D,OAAQA,MACjOpF,MAAK,WAAc,QAASkI,GAAa7H,EAAQyI,eAG9D/H,GAAcnG,KAAiC,oBAAnBoO,gBAC5BjS,EAAQkM,GAAc,OAAO,WACzB,IAAI4F,EAAmBxN,EAAWrG,MAAM,KAAMF,WAAWuF,IAAI4N,IAC7D,OAAO,IAAIhF,IAAa,SAAU5C,EAASkE,GACP,IAA5BsE,EAAiB9T,QACjBwP,EAAO,IAAIyE,eAAe,KAC9B,IAAId,EAAYW,EAAiB9T,OAC7BwI,EAAW,IAAIhI,MAAM2S,GACzBW,EAAiBzS,SAAQ,SAAU1B,EAAG9B,GAAK,OAAOqQ,GAAa5C,QAAQ3L,GAAGsL,MAAK,SAAUnM,GAAS,OAAOwM,EAAQxM,MAAW,SAAU0T,GAClIhK,EAAS3K,GAAK2U,IACPW,GACH3D,EAAO,IAAIyE,eAAezL,iBAoOlD,IAAI0L,GAAO,CAAEC,OAAQ,EAAGC,OAAQ,EAAG9G,GAAI,GACnC+G,GAAc,EACdC,GAAY,GACZC,GAAa,EACbtF,GAAc,EACduF,GAAkB,EACtB,SAASlB,GAAShQ,EAAI1B,EAAO6S,EAAIC,GAC7B,IAAIC,EAAS5G,GAAKO,EAAM/P,OAAOY,OAAOwV,GACtCrG,EAAIqG,OAASA,EACbrG,EAAIf,IAAM,EACVe,EAAIvN,QAAS,EACbuN,EAAIhB,KAAOkH,GACX,IAAII,EAAYvH,GAAUO,IAC1BU,EAAIV,IAAM3B,GAAqB,CAC3B3K,QAAS4M,GACT2G,YAAa,CAAE/V,MAAOoP,GAAc9L,cAAc,EAAMC,UAAU,GAClE2Q,IAAK9E,GAAa8E,IAClBI,KAAMlF,GAAakF,KACnBS,WAAY3F,GAAa2F,WACzBhO,IAAKqI,GAAarI,IAClByF,QAAS4C,GAAa5C,QACtBkE,OAAQtB,GAAasB,OACrBsF,MAAOC,GAAsBH,EAAUE,MAAOxG,GAC9C0G,MAAOD,GAAsBH,EAAUI,MAAO1G,IAC9C,GACA1M,GACAV,EAAOoN,EAAK1M,KACd+S,EAAOpH,IACTe,EAAIT,SAAW,aACT3N,KAAKyU,OAAOpH,KAAOrN,KAAKyU,OAAO9G,YAErC,IAAI3J,EAAKqP,GAAOjF,EAAKhL,EAAImR,EAAIC,GAG7B,OAFgB,IAAZpG,EAAIf,KACJe,EAAIT,WACD3J,EAEX,SAAS+Q,KAKL,OAJKf,GAAK5G,KACN4G,GAAK5G,KAAO+G,MACdH,GAAKC,OACPD,GAAKE,QAvhB+B,IAwhB7BF,GAAK5G,GAEhB,SAASiC,KACL,QAAK2E,GAAKC,SAEY,KAAhBD,GAAKC,SACPD,GAAK5G,GAAK,GACd4G,GAAKE,OA/hB+B,IA+hBtBF,GAAKC,QACZ,GAKX,SAASjB,GAAyBgC,GAC9B,OAAIhB,GAAKE,QAAUc,GAAmBA,EAAgBlP,cAAgBgG,IAClEiJ,KACOC,EAAgBjK,MAAK,SAAUvF,GAElC,OADA6J,KACO7J,KACR,SAAU2B,GAET,OADAkI,KACO4F,GAAU9N,OAGlB6N,EAEX,SAASE,GAAcC,KACjBpG,GACGiF,GAAKE,QAA4B,KAAhBF,GAAKE,SACvBF,GAAKE,OAASF,GAAK5G,GAAK,GAE5BgH,GAAUlQ,KAAK2J,IACf8D,GAAawD,GAAY,GAE7B,SAASC,KACL,IAAIzF,EAAOyE,GAAUA,GAAUtU,OAAS,GACxCsU,GAAUiB,MACV1D,GAAahC,GAAM,GAEvB,SAASgC,GAAawD,EAAYG,GAC9B,IAAIC,EAAc1H,GAIlB,IAHIyH,GAAgBtB,GAAKE,QAAYG,MAAgBc,IAAetH,IAAOwG,MAAkBA,IAAcc,IAAetH,KACtH2H,GAAuBF,EAAgBJ,GAAc/V,KAAK,KAAMgW,GAAcC,IAE9ED,IAAetH,KAEnBA,GAAMsH,EACFI,IAAgBpI,KAChBA,GAAUO,IAAMmF,MAChB9G,IAAoB,CACpB,IAAI0J,EAAkBtI,GAAUO,IAAItM,QAChCsU,EAAYP,EAAWzH,IAC3B/B,GAAmBZ,KAAO2K,EAAUd,MACpCa,EAAgBlW,UAAUwL,KAAO2K,EAAUZ,OACvCS,EAAY1U,QAAUsU,EAAWtU,UACjCxC,OAAOC,eAAemC,EAAS,UAAWiV,EAAUf,aACpDc,EAAgB3C,IAAM4C,EAAU5C,IAChC2C,EAAgBvC,KAAOwC,EAAUxC,KACjCuC,EAAgBrK,QAAUsK,EAAUtK,QACpCqK,EAAgBnG,OAASoG,EAAUpG,OAC/BoG,EAAU/B,aACV8B,EAAgB9B,WAAa+B,EAAU/B,YACvC+B,EAAU/P,MACV8P,EAAgB9P,IAAM+P,EAAU/P,OAIhD,SAASkN,KACL,IAAI8C,EAAgBlV,EAAQW,QAC5B,OAAO2K,GAAqB,CACxB3K,QAASuU,EACThB,YAAatW,OAAOkE,yBAAyB9B,EAAS,WACtDqS,IAAK6C,EAAc7C,IACnBI,KAAMyC,EAAczC,KACpBS,WAAYgC,EAAchC,WAC1BhO,IAAKgQ,EAAchQ,IACnByF,QAASuK,EAAcvK,QACvBkE,OAAQqG,EAAcrG,OACtBsF,MAAOjJ,GAAmBZ,KAC1B+J,MAAOa,EAAcpW,UAAUwL,MAC/B,GAER,SAASsI,GAAOjF,EAAKhL,EAAImR,EAAIC,EAAIoB,GAC7B,IAAIlE,EAAa7D,GACjB,IAEI,OADA8D,GAAavD,GAAK,GACXhL,EAAGmR,EAAIC,EAAIoB,GAEtB,QACIjE,GAAaD,GAAY,IAGjC,SAAS8D,GAAuBK,GAC5BhK,GAAkB/N,KAAK4N,GAAuBmK,GAElD,SAASpG,GAA0BrM,EAAIuM,EAAMR,EAAeC,GACxD,MAAqB,mBAAPhM,EAAoBA,EAAK,WACnC,IAAI0S,EAAYjI,GACZsB,GACA4F,KACJpD,GAAahC,GAAM,GACnB,IACI,OAAOvM,EAAGrD,MAAMC,KAAMH,WAE1B,QACI8R,GAAamE,GAAW,GACpB1G,GACAoG,GAAuBnG,MAIvC,SAASwF,GAAsBkB,EAAUpG,GACrC,OAAO,SAAUqG,EAAY/G,GACzB,OAAO8G,EAASjY,KAAKkC,KAAMyP,GAA0BuG,EAAYrG,GAAOF,GAA0BR,EAAYU,MAvG3D,KAAtD,GAAK9D,IAAmBzH,QAAQ,mBACjC2Q,GAA0B1F,GAA0BxF,IA0GxD,SAAS2D,GAAYuE,EAAKnC,GACtB,IAAI5L,EACJ,IACIA,EAAK4L,EAAQ1B,YAAY6D,GAE7B,MAAO5K,IACP,IAAW,IAAPnD,EACA,IACI,IAAIiS,EAAOC,EAAY,CAAEtG,QAASA,EAASO,OAAQ4B,GAUnD,GATItR,EAAQ4L,UAAYA,SAAS8J,cAC7BF,EAAQ5J,SAAS8J,YAAY,UACvBC,UAZG,sBAY2B,GAAM,GAC1CpV,EAAOiV,EAAOC,IAETzV,EAAQ4V,aAEbrV,EADAiV,EAAQ,IAAII,YAhBH,qBAgBmC,CAAEC,OAAQJ,IACxCA,GAEdD,GAASxV,EAAQ8V,gBACjBA,cAAcN,IACTxV,EAAQ+V,uBAAyB/V,EAAQgW,sBAC1C,IACIhW,EAAQgW,qBAAqBR,GAEjC,MAAOS,IAEXhQ,GAASuP,IAAUA,EAAMU,kBACzBC,QAAQC,KAAK,yBAA2B9E,EAAI9K,OAAS8K,IAG7D,MAAO5K,KAEf,IAAI8N,GAAYjH,GAAasB,OAuC7B,IACIwH,GAAYC,OAAOC,aAAa,OAEhCC,GAAuB,oGAEvBC,GAAc,GACdC,GAAkC,oBAAdC,WAA6B,sBAAsBxQ,KAAKwQ,UAAUC,WACtFC,GAA4BH,GAC5BI,GAA6BJ,GAC7BK,GAAwB,SAAUhQ,GAAS,OAAQ,6BAA6BZ,KAAKY,IAKzF,SAASiQ,GAAQC,EAASC,GACtB,OAAOD,EACHC,EACI,WAAc,OAAOD,EAAQ3X,MAAMC,KAAMH,YAAc8X,EAAQ5X,MAAMC,KAAMH,YAC3E6X,EACJC,EAGR,IAAIC,GAAW,CACX/F,KAAM,EACNgG,OAAQpF,IACRqF,WAAW,EACXC,MAAO,CAAC,IACRC,WAAW,GAGf,SAASC,GAA8BlU,GACnC,MAA0B,iBAAZA,GAAyB,KAAK6C,KAAK7C,GAQ3C,SAAU9C,GAAO,OAAOA,GAPxB,SAAUA,GAKR,YAJqBsD,IAAjBtD,EAAI8C,IAA2BA,KAAW9C,UAC1CA,EAAMyE,EAAUzE,IACL8C,GAER9C,GAKnB,IAAIiX,GAAU,WACV,SAASA,KA6RT,OA3RAA,EAAM3Y,UAAU4Y,OAAS,SAAUrZ,EAAMsE,EAAIgV,GACzC,IAAIC,EAAQrY,KAAKsY,KAAOzK,GAAIwK,MACxBE,EAAYvY,KAAK9B,KACrB,SAASsa,EAAwBpN,EAASkE,EAAQ+I,GAC9C,IAAKA,EAAMI,OAAOF,GACd,MAAM,IAAItP,GAAWyP,SAAS,SAAWH,EAAY,4BACzD,OAAOnV,EAAGiV,EAAMM,SAAUN,GAE9B,IAAIlH,EAAcrB,KAClB,IACI,OAAOuI,GAASA,EAAMO,KAAO5Y,KAAK4Y,GAC9BP,IAAUxK,GAAIwK,MACVA,EAAMjI,SAAStR,EAAM0Z,EAAyBJ,GAC9ChF,IAAS,WAAc,OAAOiF,EAAMjI,SAAStR,EAAM0Z,EAAyBJ,KAAiB,CAAEC,MAAOA,EAAOQ,UAAWhL,GAAIgL,WAAahL,KA/F7J,SAASiL,EAAgBF,EAAI9Z,EAAMia,EAAY3V,GAC3C,GAAKwV,EAAGI,QAAWJ,EAAGnK,OAAOwK,cAAkBpL,GAAIqL,YAAeN,EAAGO,MAWhE,CACD,IAAId,EAAQO,EAAGQ,mBAAmBta,EAAMia,EAAYH,EAAGS,WACvD,IACIhB,EAAMpZ,SACN2Z,EAAGnK,OAAO6K,eAAiB,EAE/B,MAAOpJ,GACH,OAAIA,EAAGhS,OAAS6K,EAASwQ,cAAgBX,EAAGY,YAAcZ,EAAGnK,OAAO6K,eAAiB,GACjF1C,QAAQC,KAAK,4BACb+B,EAAGa,SACIb,EAAGc,OAAO3O,MAAK,WAAc,OAAO+N,EAAgBF,EAAI9Z,EAAMia,EAAY3V,OAE9E6R,GAAU/E,GAErB,OAAOmI,EAAMjI,SAAStR,GAAM,SAAUsM,EAASkE,GAC3C,OAAO8D,IAAS,WAEZ,OADAvF,GAAIwK,MAAQA,EACLjV,EAAGgI,EAASkE,EAAQ+I,SAEhCtN,MAAK,SAAUpH,GACd,OAAO0U,EAAMsB,YAAY5O,MAAK,WAAc,OAAOpH,QA9BvD,GAAIiV,EAAGnK,OAAOwK,aACV,OAAOhE,GAAU,IAAIhM,GAAWnB,eAAe8Q,EAAGnK,OAAOmL,cAE7D,IAAKhB,EAAGnK,OAAOoL,cAAe,CAC1B,IAAKjB,EAAGkB,SAASC,SACb,OAAO9E,GAAU,IAAIhM,GAAWnB,gBACpC8Q,EAAGc,OAAO9H,MAAM/H,IAEpB,OAAO+O,EAAGnK,OAAOuL,eAAejP,MAAK,WAAc,OAAO+N,EAAgBF,EAAI9Z,EAAMia,EAAY3V,MAsFxF0V,CAAgB9Y,KAAK4Y,GAAI9Z,EAAM,CAACkB,KAAK9B,MAAOsa,GAEpD,QACQrH,GACAlB,OAGZiI,EAAM3Y,UAAUf,IAAM,SAAUyb,EAAWpJ,GACvC,IAAI3B,EAAQlP,KACZ,OAAIia,GAAaA,EAAUnU,cAAgBzH,OAChC2B,KAAKka,MAAMD,GAAWE,MAAMtJ,GAChC7Q,KAAKmY,OAAO,YAAY,SAAUE,GACrC,OAAOnJ,EAAMkL,KAAK5b,IAAI,CAAE6Z,MAAOA,EAAOnZ,IAAK+a,IACtClP,MAAK,SAAUT,GAAO,OAAO4E,EAAMmL,KAAKC,QAAQC,KAAKjQ,SAC3DS,KAAK8F,IAEZqH,EAAM3Y,UAAU2a,MAAQ,SAAUM,GAC9B,GAA2B,iBAAhBA,EACP,OAAO,IAAIxa,KAAK4Y,GAAG6B,YAAYza,KAAMwa,GACzC,GAAIzZ,EAAQyZ,GACR,OAAO,IAAIxa,KAAK4Y,GAAG6B,YAAYza,KAAM,IAAMwa,EAAY/S,KAAK,KAAO,KACvE,IAAIiT,EAAW5Z,EAAK0Z,GACpB,GAAwB,IAApBE,EAAS5a,OACT,OAAOE,KACFka,MAAMQ,EAAS,IACfC,OAAOH,EAAYE,EAAS,KACrC,IAAIE,EAAgB5a,KAAKyY,OAAOoC,QAAQra,OAAOR,KAAKyY,OAAOqC,SAASxV,QAAO,SAAUyV,GACjF,OAAOA,EAAGC,UACNN,EAASO,OAAM,SAAUlX,GAAW,OAAOgX,EAAGhX,QAAQK,QAAQL,IAAY,MAC1EgX,EAAGhX,QAAQkX,OAAM,SAAUlX,GAAW,OAAO2W,EAAStW,QAAQL,IAAY,QAC/E,GACH,GAAI6W,GAAiB5a,KAAK4Y,GAAGsC,UAAYpE,GACrC,OAAO9W,KACFka,MAAMU,EAAc1c,MACpByc,OAAOC,EAAc7W,QAAQqB,KAAI,SAAU+V,GAAM,OAAOX,EAAYW,QACxEP,GAAiBlU,GAClBkQ,QAAQC,KAAK,aAAeuE,KAAKC,UAAUb,GAAe,OAASxa,KAAK9B,KAA3D,uCACawc,EAASjT,KAAK,KAAO,KACnD,IAAI6T,EAAYtb,KAAKyY,OAAO6C,UACxBC,EAAMvb,KAAK4Y,GAAG4C,MAAMC,UACxB,SAASd,EAAO1V,EAAGhC,GACf,IACI,OAAyB,IAAlBsY,EAAIG,IAAIzW,EAAGhC,GAEtB,MAAOkE,GACH,OAAO,GAGf,IAAIwU,EAAKjB,EAAShX,QAAO,SAAUiY,EAAI5X,GACnC,IAAI6X,EAAYD,EAAG,GAAIE,EAAeF,EAAG,GACrCG,EAAQR,EAAUvX,GAClBnF,EAAQ4b,EAAYzW,GACxB,MAAO,CACH6X,GAAaE,EACbF,IAAcE,EACVrE,GAAQoE,EAAcC,GAASA,EAAMC,MACjC,SAAUvW,GACN,IAAI/D,EAAOqC,EAAa0B,EAAGzB,GAC3B,OAAOhD,EAAQU,IAASA,EAAK8O,MAAK,SAAU3M,GAAQ,OAAO+W,EAAO/b,EAAOgF,OACzE,SAAU4B,GAAK,OAAOmV,EAAO/b,EAAOkF,EAAa0B,EAAGzB,MAC1D8X,KAEX,CAAC,KAAM,OAAQG,EAAML,EAAG,GAAIM,EAAiBN,EAAG,GACnD,OAAOK,EACHhc,KAAKka,MAAM8B,EAAI9d,MAAMyc,OAAOH,EAAYwB,EAAIjY,UACvCuB,OAAO2W,GACZrB,EACI5a,KAAKsF,OAAO2W,GACZjc,KAAKka,MAAMQ,GAAUC,OAAO,KAExCzC,EAAM3Y,UAAU+F,OAAS,SAAU2W,GAC/B,OAAOjc,KAAKkc,eAAeC,IAAIF,IAEnC/D,EAAM3Y,UAAU6c,MAAQ,SAAUC,GAC9B,OAAOrc,KAAKkc,eAAeE,MAAMC,IAErCnE,EAAM3Y,UAAU+c,OAAS,SAAUA,GAC/B,OAAOtc,KAAKkc,eAAeI,OAAOA,IAEtCpE,EAAM3Y,UAAU6S,MAAQ,SAAUmK,GAC9B,OAAOvc,KAAKkc,eAAe9J,MAAMmK,IAErCrE,EAAM3Y,UAAUid,KAAO,SAAU7P,GAC7B,OAAO3M,KAAKkc,eAAeM,KAAK7P,IAEpCuL,EAAM3Y,UAAUkd,QAAU,SAAUJ,GAChC,OAAOrc,KAAKkc,eAAeO,QAAQJ,IAEvCnE,EAAM3Y,UAAU2c,aAAe,WAC3B,OAAO,IAAIlc,KAAK4Y,GAAG8D,WAAW,IAAI1c,KAAK4Y,GAAG6B,YAAYza,QAE1DkY,EAAM3Y,UAAUod,QAAU,SAAUb,GAChC,OAAO,IAAI9b,KAAK4Y,GAAG8D,WAAW,IAAI1c,KAAK4Y,GAAG6B,YAAYza,KAAMe,EAAQ+a,GAChE,IAAMA,EAAMrU,KAAK,KAAO,IACxBqU,KAER5D,EAAM3Y,UAAUqd,QAAU,WACtB,OAAO5c,KAAKkc,eAAeU,WAE/B1E,EAAM3Y,UAAUsd,WAAa,SAAU/W,GACnC9F,KAAKyY,OAAOqE,YAAchX,EAC1B,IAAIiX,EAAW,SAAU9b,GACrB,IAAKA,EACD,OAAOA,EACX,IAAIqJ,EAAMjM,OAAOY,OAAO6G,EAAYvG,WACpC,IAAK,IAAIxB,KAAKkD,EACV,GAAIO,EAAOP,EAAKlD,GACZ,IACIuM,EAAIvM,GAAKkD,EAAIlD,GAEjB,MAAO2Y,IACf,OAAOpM,GAOX,OALItK,KAAKyY,OAAOsE,UACZ/c,KAAKqa,KAAKC,QAAQ0C,YAAYhd,KAAKyY,OAAOsE,UAE9C/c,KAAKyY,OAAOsE,SAAWA,EACvB/c,KAAKqa,KAAK,UAAW0C,GACdjX,GAEXoS,EAAM3Y,UAAU0d,YAAc,WAI1B,OAAOjd,KAAK6c,YAHZ,SAAeK,GACXlc,EAAOhB,KAAMkd,OAIrBhF,EAAM3Y,UAAU4d,IAAM,SAAUlc,EAAK/B,GACjC,IAAIgQ,EAAQlP,KACR2b,EAAK3b,KAAKyY,OAAOqC,QAASsC,EAAOzB,EAAGyB,KAAMrZ,EAAU4X,EAAG5X,QACvDsZ,EAAWpc,EAIf,OAHI8C,GAAWqZ,IACXC,EAAWpF,GAA8BlU,EAA9BkU,CAAuChX,IAE/CjB,KAAKmY,OAAO,aAAa,SAAUE,GACtC,OAAOnJ,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,MAAO/Q,KAAa,MAAP5B,EAAc,CAACA,GAAO,KAAM6T,OAAQ,CAACsK,QAClGtS,MAAK,SAAUT,GAAO,OAAOA,EAAIiT,YAAcvP,GAAasB,OAAOhF,EAAIhC,SAAS,IAAMgC,EAAIkT,cACxFzS,MAAK,SAAUyS,GAChB,GAAIzZ,EACA,IACIS,EAAavD,EAAK8C,EAASyZ,GAE/B,MAAO9G,IAEX,OAAO8G,MAGftF,EAAM3Y,UAAUke,OAAS,SAAUC,EAAa9S,GAC5C,GAA2B,iBAAhB8S,GAA6B3c,EAAQ2c,GAmB5C,OAAO1d,KAAKka,MAAM,OAAOS,OAAO+C,GAAaC,OAAO/S,GAlBpD,IAAI1L,EAAM4E,EAAa4Z,EAAa1d,KAAKyY,OAAOqC,QAAQ/W,SACxD,QAAYQ,IAARrF,EACA,OAAO+V,GAAU,IAAIhM,GAAW2U,gBAAgB,kDACpD,IACiC,mBAAlBhT,EACP9J,EAAK8J,GAAezJ,SAAQ,SAAU4C,GAClCS,EAAakZ,EAAa3Z,EAAS6G,EAAc7G,OAIrD6G,EAAc8S,EAAa,CAAE9e,MAAO8e,EAAa5C,QAAS5b,IAGlE,MAAOyc,IAEP,OAAO3b,KAAKka,MAAM,OAAOS,OAAOzb,GAAKye,OAAO/S,IAMpDsN,EAAM3Y,UAAUse,IAAM,SAAU5c,EAAK/B,GACjC,IAAIgQ,EAAQlP,KACR2b,EAAK3b,KAAKyY,OAAOqC,QAASsC,EAAOzB,EAAGyB,KAAMrZ,EAAU4X,EAAG5X,QACvDsZ,EAAWpc,EAIf,OAHI8C,GAAWqZ,IACXC,EAAWpF,GAA8BlU,EAA9BkU,CAAuChX,IAE/CjB,KAAKmY,OAAO,aAAa,SAAUE,GAAS,OAAOnJ,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,MAAOkB,OAAQ,CAACsK,GAAWvc,KAAa,MAAP5B,EAAc,CAACA,GAAO,UACpJ6L,MAAK,SAAUT,GAAO,OAAOA,EAAIiT,YAAcvP,GAAasB,OAAOhF,EAAIhC,SAAS,IAAMgC,EAAIkT,cAC1FzS,MAAK,SAAUyS,GAChB,GAAIzZ,EACA,IACIS,EAAavD,EAAK8C,EAASyZ,GAE/B,MAAO9G,IAEX,OAAO8G,MAGftF,EAAM3Y,UAAUue,OAAS,SAAU5e,GAC/B,IAAIgQ,EAAQlP,KACZ,OAAOA,KAAKmY,OAAO,aAAa,SAAUE,GAAS,OAAOnJ,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,SAAU/Q,KAAM,CAAC5B,QAC9G6L,MAAK,SAAUT,GAAO,OAAOA,EAAIiT,YAAcvP,GAAasB,OAAOhF,EAAIhC,SAAS,SAAM/D,MAE/F2T,EAAM3Y,UAAUwe,MAAQ,WACpB,IAAI7O,EAAQlP,KACZ,OAAOA,KAAKmY,OAAO,aAAa,SAAUE,GAAS,OAAOnJ,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,cAAemM,MAAOpG,QACnH7M,MAAK,SAAUT,GAAO,OAAOA,EAAIiT,YAAcvP,GAAasB,OAAOhF,EAAIhC,SAAS,SAAM/D,MAE/F2T,EAAM3Y,UAAU0e,QAAU,SAAUnd,GAChC,IAAIoO,EAAQlP,KACZ,OAAOA,KAAKmY,OAAO,YAAY,SAAUE,GACrC,OAAOnJ,EAAMkL,KAAK8D,QAAQ,CACtBpd,KAAMA,EACNuX,MAAOA,IACRtN,MAAK,SAAUpH,GAAU,OAAOA,EAAOyB,KAAI,SAAUkF,GAAO,OAAO4E,EAAMmL,KAAKC,QAAQC,KAAKjQ,aAGtG4N,EAAM3Y,UAAU4e,QAAU,SAAUC,EAASC,EAAerc,GACxD,IAAIkN,EAAQlP,KACRc,EAAOR,MAAMS,QAAQsd,GAAiBA,OAAgB9Z,EAEtD+Z,GADJtc,EAAUA,IAAYlB,OAAOyD,EAAY8Z,IACbrc,EAAQuc,aAAUha,EAC9C,OAAOvE,KAAKmY,OAAO,aAAa,SAAUE,GACtC,IAAIsD,EAAKzM,EAAMuJ,OAAOqC,QAASsC,EAAOzB,EAAGyB,KAAMrZ,EAAU4X,EAAG5X,QAC5D,GAAIA,GAAWjD,EACX,MAAM,IAAImI,GAAW2U,gBAAgB,gEACzC,GAAI9c,GAAQA,EAAKhB,SAAWse,EAAQte,OAChC,MAAM,IAAImJ,GAAW2U,gBAAgB,wDACzC,IAAIY,EAAaJ,EAAQte,OACrB2e,EAAe1a,GAAWqZ,EAC1BgB,EAAQhZ,IAAI6S,GAA8BlU,IAC1Cqa,EACJ,OAAOlP,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,MAAO/Q,KAAMA,EAAMiS,OAAQ0L,EAAcH,YAAaA,IAChGvT,MAAK,SAAU4Q,GAChB,IAAI4B,EAAc5B,EAAG4B,YAAa1J,EAAU8H,EAAG9H,QAAS2J,EAAa7B,EAAG6B,WAAYlV,EAAWqT,EAAGrT,SAElG,GAAoB,IAAhBiV,EACA,OAFSe,EAAczK,EAAU2J,EAGrC,MAAM,IAAI7U,EAAUuG,EAAMhR,KAAO,eAAiBqf,EAAc,OAASiB,EAAa,qBAAsBlW,UAIxH4P,EAAM3Y,UAAUmf,QAAU,SAAUN,EAASC,EAAerc,GACxD,IAAIkN,EAAQlP,KACRc,EAAOR,MAAMS,QAAQsd,GAAiBA,OAAgB9Z,EAEtD+Z,GADJtc,EAAUA,IAAYlB,OAAOyD,EAAY8Z,IACbrc,EAAQuc,aAAUha,EAC9C,OAAOvE,KAAKmY,OAAO,aAAa,SAAUE,GACtC,IAAIsD,EAAKzM,EAAMuJ,OAAOqC,QAASsC,EAAOzB,EAAGyB,KAAMrZ,EAAU4X,EAAG5X,QAC5D,GAAIA,GAAWjD,EACX,MAAM,IAAImI,GAAW2U,gBAAgB,gEACzC,GAAI9c,GAAQA,EAAKhB,SAAWse,EAAQte,OAChC,MAAM,IAAImJ,GAAW2U,gBAAgB,wDACzC,IAAIY,EAAaJ,EAAQte,OACrB6e,EAAe5a,GAAWqZ,EAC1BgB,EAAQhZ,IAAI6S,GAA8BlU,IAC1Cqa,EACJ,OAAOlP,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,MAAO/Q,KAAMA,EAAMiS,OAAQ4L,EAAcL,YAAaA,IAChGvT,MAAK,SAAU4Q,GAChB,IAAI4B,EAAc5B,EAAG4B,YAAa1J,EAAU8H,EAAG9H,QAAS2J,EAAa7B,EAAG6B,WAAYlV,EAAWqT,EAAGrT,SAElG,GAAoB,IAAhBiV,EACA,OAFSe,EAAczK,EAAU2J,EAGrC,MAAM,IAAI7U,EAAUuG,EAAMhR,KAAO,eAAiBqf,EAAc,OAASiB,EAAa,qBAAsBlW,UAIxH4P,EAAM3Y,UAAUqf,WAAa,SAAU9d,GACnC,IAAIoO,EAAQlP,KACR6e,EAAU/d,EAAKhB,OACnB,OAAOE,KAAKmY,OAAO,aAAa,SAAUE,GACtC,OAAOnJ,EAAMkL,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,SAAU/Q,KAAMA,OAChEiK,MAAK,SAAU4Q,GACd,IAAI4B,EAAc5B,EAAG4B,YAAaC,EAAa7B,EAAG6B,WAAYlV,EAAWqT,EAAGrT,SAC5E,GAAoB,IAAhBiV,EACA,OAAOC,EACX,MAAM,IAAI7U,EAAUuG,EAAMhR,KAAO,kBAAoBqf,EAAc,OAASsB,EAAU,qBAAsBvW,OAG7G4P,EA9RE,GAiSb,SAAS4G,GAAOC,GACZ,IAAIC,EAAM,GACNhb,EAAK,SAAUib,EAAWC,GAC1B,GAAIA,EAAY,CAEZ,IADA,IAAIvhB,EAAIkC,UAAUC,OAAQ4C,EAAO,IAAIpC,MAAM3C,EAAI,KACtCA,GACL+E,EAAK/E,EAAI,GAAKkC,UAAUlC,GAE5B,OADAqhB,EAAIC,GAAWE,UAAUpf,MAAM,KAAM2C,GAC9Bqc,EAEN,GAA2B,iBAAhB,EACZ,OAAOC,EAAIC,IAGnBjb,EAAGob,aAAejC,EAClB,IAAK,IAAIxf,EAAI,EAAGC,EAAIiC,UAAUC,OAAQnC,EAAIC,IAAKD,EAC3Cwf,EAAItd,UAAUlC,IAElB,OAAOqG,EACP,SAASmZ,EAAI8B,EAAWI,EAAeC,GACnC,GAAyB,iBAAdL,EACP,OAAOM,EAAoBN,GAC1BI,IACDA,EAAgBxU,IACfyU,IACDA,EAAkBzV,IACtB,IAAI2V,EAAU,CACVC,YAAa,GACblF,KAAM+E,EACNH,UAAW,SAAUtO,IACwB,IAArC2O,EAAQC,YAAYrb,QAAQyM,KAC5B2O,EAAQC,YAAYvb,KAAK2M,GACzB2O,EAAQjF,KAAO8E,EAAcG,EAAQjF,KAAM1J,KAGnDmM,YAAa,SAAUnM,GACnB2O,EAAQC,YAAcD,EAAQC,YAAYna,QAAO,SAAUlC,GAAM,OAAOA,IAAOyN,KAC/E2O,EAAQjF,KAAOiF,EAAQC,YAAY/b,OAAO2b,EAAeC,KAIjE,OADAN,EAAIC,GAAajb,EAAGib,GAAaO,EAC1BA,EAEX,SAASD,EAAoBG,GACzB5e,EAAK4e,GAAKve,SAAQ,SAAU8d,GACxB,IAAIvc,EAAOgd,EAAIT,GACf,GAAIle,EAAQ2B,GACRya,EAAI8B,EAAWS,EAAIT,GAAW,GAAIS,EAAIT,GAAW,QAEhD,IAAa,SAATvc,EAaL,MAAM,IAAIuG,GAAW2U,gBAAgB,wBAZrC,IAAI4B,EAAUrC,EAAI8B,EAAWnV,IAAQ,WAEjC,IADA,IAAInM,EAAIkC,UAAUC,OAAQ4C,EAAO,IAAIpC,MAAM3C,GACpCA,KACH+E,EAAK/E,GAAKkC,UAAUlC,GACxB6hB,EAAQC,YAAYte,SAAQ,SAAUiC,GAClCD,GAAO,WACHC,EAAGrD,MAAM,KAAM2C,iBAW3C,SAASid,GAAqBpgB,EAAWuG,GAErC,OADA1D,EAAO0D,GAAa3F,KAAK,CAAEZ,UAAWA,IAC/BuG,EAkBX,SAAS8Z,GAAgBb,EAAKc,GAC1B,QAASd,EAAIzZ,QAAUyZ,EAAIe,WAAaf,EAAIgB,MACvCF,EAAoBd,EAAIiB,WAAajB,EAAIkB,cAElD,SAASC,GAAUnB,EAAK3b,GACpB2b,EAAIzZ,OAASmS,GAAQsH,EAAIzZ,OAAQlC,GAErC,SAAS+c,GAAgBpB,EAAKqB,EAASC,GACnC,IAAIC,EAAOvB,EAAIkB,aACflB,EAAIkB,aAAeK,EAAO,WAAc,OAAO7I,GAAQ6I,IAAQF,MAAgBA,EAC/ErB,EAAIiB,UAAYK,IAAkBC,EAKtC,SAASC,GAAgBxB,EAAKyB,GAC1B,GAAIzB,EAAI0B,UACJ,OAAOD,EAAWE,WACtB,IAAI5E,EAAQ0E,EAAWG,kBAAkB5B,EAAIjD,OAC7C,IAAKA,EACD,MAAM,IAAI7S,GAAW2X,OAAO,WAAa7B,EAAIjD,MAAQ,oBAAsB0E,EAAWtiB,KAAO,mBACjG,OAAO4d,EAEX,SAAS+E,GAAW9B,EAAK+B,EAAWzI,GAChC,IAAIyD,EAAQyE,GAAgBxB,EAAK+B,EAAUrI,QAC3C,OAAOqI,EAAUD,WAAW,CACxBxI,MAAOA,EACPtF,QAASgM,EAAIgC,SACbnE,QAAqB,SAAZmC,EAAIiC,IACbC,SAAUlC,EAAIkC,OACdC,MAAO,CACHpF,MAAOA,EACPkC,MAAOe,EAAIf,SAIvB,SAASmD,GAAKpC,EAAK3b,EAAIge,EAAWN,GAC9B,IAAIxb,EAASyZ,EAAIkB,aAAexI,GAAQsH,EAAIzZ,OAAQyZ,EAAIkB,gBAAkBlB,EAAIzZ,OAC9E,GAAKyZ,EAAIgB,GAGJ,CACD,IAAIsB,EAAQ,GACRC,EAAQ,SAAU1d,EAAM2d,EAAQC,GAChC,IAAKlc,GAAUA,EAAOic,EAAQC,GAAS,SAAU7d,GAAU,OAAO4d,EAAOE,KAAK9d,MAAY,SAAUoO,GAAO,OAAOwP,EAAOG,KAAK3P,MAAU,CACpI,IAAI2O,EAAaa,EAAOb,WACpBxhB,EAAM,GAAKwhB,EACH,yBAARxhB,IACAA,EAAM,GAAK,IAAIuM,WAAWiV,IACzBlf,EAAO6f,EAAOniB,KACfmiB,EAAMniB,IAAO,EACbkE,EAAGQ,EAAM2d,EAAQC,MAI7B,OAAOpgB,QAAQ0R,IAAI,CACfiM,EAAIgB,GAAG4B,SAASL,EAAOF,GACvBQ,GAAQf,GAAW9B,EAAK+B,EAAWM,GAAYrC,EAAIe,UAAWwB,GAAQvC,EAAIgC,UAAYhC,EAAI8C,eAlB9F,OAAOD,GAAQf,GAAW9B,EAAK+B,EAAWM,GAAY3J,GAAQsH,EAAIe,UAAWxa,GAASlC,GAAK2b,EAAIgC,UAAYhC,EAAI8C,aAsBvH,SAASD,GAAQE,EAAexc,EAAQlC,EAAIye,GACxC,IACIE,EAAYvQ,GADDqQ,EAAc,SAAUrc,EAAGxH,EAAGiH,GAAK,OAAO7B,EAAGye,EAAYrc,GAAIxH,EAAGiH,IAAQ7B,GAEvF,OAAO0e,EAAc/W,MAAK,SAAUwW,GAChC,GAAIA,EACA,OAAOA,EAAO5e,OAAM,WAChB,IAAI3E,EAAI,WAAc,OAAOujB,EAAOS,YAC/B1c,IAAUA,EAAOic,GAAQ,SAAUU,GAAY,OAAOjkB,EAAIikB,KAAa,SAAUhe,GAAOsd,EAAOE,KAAKxd,GAAMjG,EAAI6L,MAAQ,SAAU1C,GAAKoa,EAAOG,KAAKva,GAAInJ,EAAI6L,OAC1JkY,EAAUR,EAAO3iB,MAAO2iB,GAAQ,SAAUU,GAAY,OAAOjkB,EAAIikB,KACrEjkB,UAMhB,SAAS0d,GAAIzW,EAAGhC,GACZ,IACI,IAAIif,EAAKrQ,GAAK5M,GACVkd,EAAKtQ,GAAK5O,GACd,GAAIif,IAAOC,EACP,MAAW,UAAPD,EACO,EACA,UAAPC,GACQ,EACD,WAAPD,EACO,EACA,WAAPC,GACQ,EACD,WAAPD,EACO,EACA,WAAPC,GACQ,EACD,SAAPD,EACO,EACA,SAAPC,EACOC,KACH,EAEZ,OAAQF,GACJ,IAAK,SACL,IAAK,OACL,IAAK,SACD,OAAOjd,EAAIhC,EAAI,EAAIgC,EAAIhC,GAAK,EAAI,EACpC,IAAK,SACD,OAoBhB,SAA4BgC,EAAGhC,GAI3B,IAHA,IAAIof,EAAKpd,EAAEnF,OACPwiB,EAAKrf,EAAEnD,OACPlC,EAAIykB,EAAKC,EAAKD,EAAKC,EACd3kB,EAAI,EAAGA,EAAIC,IAAKD,EACrB,GAAIsH,EAAEtH,KAAOsF,EAAEtF,GACX,OAAOsH,EAAEtH,GAAKsF,EAAEtF,IAAM,EAAI,EAElC,OAAO0kB,IAAOC,EAAK,EAAID,EAAKC,GAAM,EAAI,EA5BnBC,CAAmBC,GAAcvd,GAAIud,GAAcvf,IAE9D,IAAK,QACD,OAMhB,SAAuBgC,EAAGhC,GAItB,IAHA,IAAIof,EAAKpd,EAAEnF,OACPwiB,EAAKrf,EAAEnD,OACPlC,EAAIykB,EAAKC,EAAKD,EAAKC,EACd3kB,EAAI,EAAGA,EAAIC,IAAKD,EAAG,CACxB,IAAI2M,EAAMoR,GAAIzW,EAAEtH,GAAIsF,EAAEtF,IACtB,GAAY,IAAR2M,EACA,OAAOA,EAEf,OAAO+X,IAAOC,EAAK,EAAID,EAAKC,GAAM,EAAI,EAfnBG,CAAcxd,EAAGhC,IAGpC,MAAO0Y,IACP,OAAOyG,IAuBX,SAASvQ,GAAKrM,GACV,IAAI3G,SAAW2G,EACf,GAAU,WAAN3G,EACA,OAAOA,EACX,GAAI6jB,YAAYC,OAAOnd,GACnB,MAAO,SACX,IAAIod,EAAQjkB,EAAY6G,GACxB,MAAiB,gBAAVod,EAA0B,SAAWA,EAEhD,SAASJ,GAAcvd,GACnB,OAAIA,aAAawG,WACNxG,EACPyd,YAAYC,OAAO1d,GACZ,IAAIwG,WAAWxG,EAAE4d,OAAQ5d,EAAE6d,WAAY7d,EAAE8d,YAC7C,IAAItX,WAAWxG,GAG1B,IAAIyX,GAAe,WACf,SAASA,KAoYT,OAlYAA,EAAWnd,UAAUyjB,MAAQ,SAAU5f,EAAIyN,GACvC,IAAIkO,EAAM/e,KAAKijB,KACf,OAAOlE,EAAImE,MACPnE,EAAIoE,MAAMhL,OAAO,KAAMlD,GAAU9V,KAAK,KAAM4f,EAAImE,QAChDnE,EAAIoE,MAAMhL,OAAO,WAAY/U,GAAI2H,KAAK8F,IAE9C6L,EAAWnd,UAAU6jB,OAAS,SAAUhgB,GACpC,IAAI2b,EAAM/e,KAAKijB,KACf,OAAOlE,EAAImE,MACPnE,EAAIoE,MAAMhL,OAAO,KAAMlD,GAAU9V,KAAK,KAAM4f,EAAImE,QAChDnE,EAAIoE,MAAMhL,OAAO,YAAa/U,EAAI,WAE1CsZ,EAAWnd,UAAU8jB,cAAgB,SAAUjgB,GAC3C,IAAI2b,EAAM/e,KAAKijB,KACflE,EAAIe,UAAYrI,GAAQsH,EAAIe,UAAW1c,IAE3CsZ,EAAWnd,UAAUoiB,SAAW,SAAUve,EAAIge,GAC1C,OAAOD,GAAKnhB,KAAKijB,KAAM7f,EAAIge,EAAWphB,KAAKijB,KAAKE,MAAM/I,OAE1DsC,EAAWnd,UAAU+jB,MAAQ,SAAU5hB,GACnC,IAAIsC,EAAK3F,OAAOY,OAAOe,KAAK8F,YAAYvG,WAAYwf,EAAM1gB,OAAOY,OAAOe,KAAKijB,MAI7E,OAHIvhB,GACAV,EAAO+d,EAAKrd,GAChBsC,EAAGif,KAAOlE,EACH/a,GAEX0Y,EAAWnd,UAAUgkB,IAAM,WAEvB,OADAvjB,KAAKijB,KAAKpB,YAAc,KACjB7hB,MAEX0c,EAAWnd,UAAUid,KAAO,SAAUpZ,GAClC,IAAI2b,EAAM/e,KAAKijB,KACf,OAAOjjB,KAAKgjB,OAAM,SAAU3K,GAAS,OAAO8I,GAAKpC,EAAK3b,EAAIiV,EAAO0G,EAAIoE,MAAM/I,UAE/EsC,EAAWnd,UAAU6c,MAAQ,SAAUvL,GACnC,IAAI3B,EAAQlP,KACZ,OAAOA,KAAKgjB,OAAM,SAAU3K,GACxB,IAAI0G,EAAM7P,EAAM+T,KACZnC,EAAY/B,EAAIoE,MAAM/I,KAC1B,GAAIwF,GAAgBb,GAAK,GACrB,OAAO+B,EAAU1E,MAAM,CACnB/D,MAAOA,EACP6I,MAAO,CACHpF,MAAOyE,GAAgBxB,EAAK+B,EAAUrI,QACtCuF,MAAOe,EAAIf,SAEhBjT,MAAK,SAAUqR,GAAS,OAAOoH,KAAKC,IAAIrH,EAAO2C,EAAI3M,UAGtD,IAAIgK,EAAQ,EACZ,OAAO+E,GAAKpC,GAAK,WAAuB,QAAP3C,GAAc,IAAU/D,EAAOyI,GAC3D/V,MAAK,WAAc,OAAOqR,QAEpCrR,KAAK8F,IAEZ6L,EAAWnd,UAAUmkB,OAAS,SAAU3f,EAAS8M,GAC7C,IAAI8S,EAAQ5f,EAAQoB,MAAM,KAAKyX,UAAWgH,EAAWD,EAAM,GAAIE,EAAYF,EAAM7jB,OAAS,EAC1F,SAASgkB,EAAO7iB,EAAKtD,GACjB,OAAIA,EACOmmB,EAAO7iB,EAAI0iB,EAAMhmB,IAAKA,EAAI,GAC9BsD,EAAI2iB,GAEf,IAAIG,EAA0B,SAAlB/jB,KAAKijB,KAAKjC,IAAiB,GAAK,EAC5C,SAASgD,EAAO/e,EAAGhC,GACf,IAAIghB,EAAOH,EAAO7e,EAAG4e,GAAYK,EAAOJ,EAAO7gB,EAAG4gB,GAClD,OAAOI,EAAOC,GAAQH,EAAQE,EAAOC,EAAOH,EAAQ,EAExD,OAAO/jB,KAAKyc,SAAQ,SAAUxX,GAC1B,OAAOA,EAAEkf,KAAKH,MACfjZ,KAAK8F,IAEZ6L,EAAWnd,UAAUkd,QAAU,SAAU5L,GACrC,IAAI3B,EAAQlP,KACZ,OAAOA,KAAKgjB,OAAM,SAAU3K,GACxB,IAAI0G,EAAM7P,EAAM+T,KAChB,GAAgB,SAAZlE,EAAIiC,KAAkBpB,GAAgBb,GAAK,IAASA,EAAI3M,MAAQ,EAAG,CACnE,IAAIgS,EAAgBrF,EAAI8C,YACpB/F,EAAQyE,GAAgBxB,EAAKA,EAAIoE,MAAM/I,KAAK3B,QAChD,OAAOsG,EAAIoE,MAAM/I,KAAK8G,MAAM,CACxB7I,MAAOA,EACPjG,MAAO2M,EAAI3M,MACXW,QAAQ,EACRmO,MAAO,CACHpF,MAAOA,EACPkC,MAAOe,EAAIf,SAEhBjT,MAAK,SAAU4Q,GACd,IAAIhY,EAASgY,EAAGhY,OAChB,OAAOygB,EAAgBzgB,EAAOyB,IAAIgf,GAAiBzgB,KAIvD,IAAI0gB,EAAM,GACV,OAAOlD,GAAKpC,GAAK,SAAUnb,GAAQ,OAAOygB,EAAIngB,KAAKN,KAAUyU,EAAO0G,EAAIoE,MAAM/I,MAAMrP,MAAK,WAAc,OAAOsZ,OAEnHxT,IAEP6L,EAAWnd,UAAU+c,OAAS,SAAUA,GACpC,IAAIyC,EAAM/e,KAAKijB,KACf,OAAI3G,GAAU,IAEdyC,EAAIzC,QAAUA,EACVsD,GAAgBb,GAChBoB,GAAgBpB,GAAK,WACjB,IAAIuF,EAAahI,EACjB,OAAO,SAAUiF,EAAQC,GACrB,OAAmB,IAAf8C,IAEe,IAAfA,KACEA,GACK,IAEX9C,GAAQ,WACJD,EAAOC,QAAQ8C,GACfA,EAAa,MAEV,QAKfnE,GAAgBpB,GAAK,WACjB,IAAIuF,EAAahI,EACjB,OAAO,WAAc,QAAUgI,EAAa,OAvBzCtkB,MA4Bf0c,EAAWnd,UAAU6S,MAAQ,SAAUmK,GAUnC,OATAvc,KAAKijB,KAAK7Q,MAAQoR,KAAKC,IAAIzjB,KAAKijB,KAAK7Q,MAAOmK,GAC5C4D,GAAgBngB,KAAKijB,MAAM,WACvB,IAAIsB,EAAWhI,EACf,OAAO,SAAUgF,EAAQC,EAASpW,GAG9B,QAFMmZ,GAAY,GACd/C,EAAQpW,GACLmZ,GAAY,MAExB,GACIvkB,MAEX0c,EAAWnd,UAAUilB,MAAQ,SAAUvI,EAAgBwI,GAUnD,OATAvE,GAAUlgB,KAAKijB,MAAM,SAAU1B,EAAQC,EAASpW,GAC5C,OAAI6Q,EAAesF,EAAO3iB,SACtB4iB,EAAQpW,GACDqZ,MAMRzkB,MAEX0c,EAAWnd,UAAU4a,MAAQ,SAAUtJ,GACnC,OAAO7Q,KAAKoS,MAAM,GAAGqK,SAAQ,SAAUxX,GAAK,OAAOA,EAAE,MAAO8F,KAAK8F,IAErE6L,EAAWnd,UAAUmlB,KAAO,SAAU7T,GAClC,OAAO7Q,KAAK4c,UAAUzC,MAAMtJ,IAEhC6L,EAAWnd,UAAU+F,OAAS,SAAU2W,GA7S5C,IAAwB8C,EAAK3b,EAkTrB,OAJA8c,GAAUlgB,KAAKijB,MAAM,SAAU1B,GAC3B,OAAOtF,EAAesF,EAAO3iB,UA/SjBmgB,EAiTD/e,KAAKijB,KAjTC7f,EAiTK6Y,EAhT9B8C,EAAI4F,QAAUlN,GAAQsH,EAAI4F,QAASvhB,GAiTxBpD,MAEX0c,EAAWnd,UAAU4c,IAAM,SAAU7W,GACjC,OAAOtF,KAAKsF,OAAOA,IAEvBoX,EAAWnd,UAAUwgB,GAAK,SAAU6E,GAChC,OAAO,IAAI5kB,KAAK4Y,GAAG6B,YAAYza,KAAKijB,KAAKE,MAAOyB,EAAW5kB,OAE/D0c,EAAWnd,UAAUqd,QAAU,WAI3B,OAHA5c,KAAKijB,KAAKjC,IAAyB,SAAlBhhB,KAAKijB,KAAKjC,IAAiB,OAAS,OACjDhhB,KAAK6kB,oBACL7kB,KAAK6kB,mBAAmB7kB,KAAKijB,KAAKjC,KAC/BhhB,MAEX0c,EAAWnd,UAAUulB,KAAO,WACxB,OAAO9kB,KAAK4c,WAEhBF,EAAWnd,UAAUwlB,QAAU,SAAUlU,GACrC,IAAIkO,EAAM/e,KAAKijB,KAEf,OADAlE,EAAIgC,UAAYhC,EAAI4F,QACb3kB,KAAKwc,MAAK,SAAUvY,EAAKsd,GAAU1Q,EAAG0Q,EAAOriB,IAAKqiB,OAE7D7E,EAAWnd,UAAUylB,cAAgB,SAAUnU,GAE3C,OADA7Q,KAAKijB,KAAKhC,OAAS,SACZjhB,KAAK+kB,QAAQlU,IAExB6L,EAAWnd,UAAU0lB,eAAiB,SAAUpU,GAC5C,IAAIkO,EAAM/e,KAAKijB,KAEf,OADAlE,EAAIgC,UAAYhC,EAAI4F,QACb3kB,KAAKwc,MAAK,SAAUvY,EAAKsd,GAAU1Q,EAAG0Q,EAAOb,WAAYa,OAEpE7E,EAAWnd,UAAUuB,KAAO,SAAU+P,GAClC,IAAIkO,EAAM/e,KAAKijB,KACflE,EAAIgC,UAAYhC,EAAI4F,QACpB,IAAI1f,EAAI,GACR,OAAOjF,KAAKwc,MAAK,SAAU5Y,EAAM2d,GAC7Btc,EAAEf,KAAKqd,EAAOriB,QACf6L,MAAK,WACJ,OAAO9F,KACR8F,KAAK8F,IAEZ6L,EAAWnd,UAAU2lB,YAAc,SAAUrU,GACzC,IAAIkO,EAAM/e,KAAKijB,KACf,GAAgB,SAAZlE,EAAIiC,KAAkBpB,GAAgBb,GAAK,IAASA,EAAI3M,MAAQ,EAChE,OAAOpS,KAAKgjB,OAAM,SAAU3K,GACxB,IAAIyD,EAAQyE,GAAgBxB,EAAKA,EAAIoE,MAAM/I,KAAK3B,QAChD,OAAOsG,EAAIoE,MAAM/I,KAAK8G,MAAM,CACxB7I,MAAOA,EACPtF,QAAQ,EACRX,MAAO2M,EAAI3M,MACX8O,MAAO,CACHpF,MAAOA,EACPkC,MAAOe,EAAIf,YAGpBjT,MAAK,SAAU4Q,GAEd,OADaA,EAAGhY,UAEjBoH,KAAK8F,GAEZkO,EAAIgC,UAAYhC,EAAI4F,QACpB,IAAI1f,EAAI,GACR,OAAOjF,KAAKwc,MAAK,SAAU5Y,EAAM2d,GAC7Btc,EAAEf,KAAKqd,EAAOb,eACf3V,MAAK,WACJ,OAAO9F,KACR8F,KAAK8F,IAEZ6L,EAAWnd,UAAU4lB,WAAa,SAAUtU,GAExC,OADA7Q,KAAKijB,KAAKhC,OAAS,SACZjhB,KAAKc,KAAK+P,IAErB6L,EAAWnd,UAAU6lB,SAAW,SAAUvU,GACtC,OAAO7Q,KAAKoS,MAAM,GAAGtR,MAAK,SAAUmE,GAAK,OAAOA,EAAE,MAAO8F,KAAK8F,IAElE6L,EAAWnd,UAAU8lB,QAAU,SAAUxU,GACrC,OAAO7Q,KAAK4c,UAAUwI,SAASvU,IAEnC6L,EAAWnd,UAAU+lB,SAAW,WAC5B,IAAIvG,EAAM/e,KAAKijB,KAAMjH,EAAM+C,EAAIjD,OAASiD,EAAIoE,MAAM1K,OAAO6C,UAAUyD,EAAIjD,OACvE,IAAKE,IAAQA,EAAID,MACb,OAAO/b,KACX,IAAIiC,EAAM,GAOV,OANAie,GAAUlgB,KAAKijB,MAAM,SAAU1B,GAC3B,IAAIgE,EAAShE,EAAOb,WAAW3a,WAC3Byf,EAAQhkB,EAAOS,EAAKsjB,GAExB,OADAtjB,EAAIsjB,IAAU,GACNC,KAELxlB,MAEX0c,EAAWnd,UAAUoe,OAAS,SAAU8H,GACpC,IAAIvW,EAAQlP,KACR+e,EAAM/e,KAAKijB,KACf,OAAOjjB,KAAKojB,QAAO,SAAU/K,GACzB,IAAIqN,EACJ,GAAuB,mBAAZD,EACPC,EAAWD,MAEV,CACD,IAAI/K,EAAW5Z,EAAK2kB,GAChB5G,EAAUnE,EAAS5a,OACvB4lB,EAAW,SAAU9hB,GAEjB,IADA,IAAI+hB,GAAmB,EACdhoB,EAAI,EAAGA,EAAIkhB,IAAWlhB,EAAG,CAC9B,IAAIoG,EAAU2W,EAAS/c,GAAIsG,EAAMwhB,EAAQ1hB,GACrCD,EAAaF,EAAMG,KAAaE,IAChCO,EAAaZ,EAAMG,EAASE,GAC5B0hB,GAAmB,GAG3B,OAAOA,GAGf,IAAI7E,EAAY/B,EAAIoE,MAAM/I,KACtBuB,EAAKmF,EAAUrI,OAAOiI,WAAYkF,EAAWjK,EAAGiK,SAAUC,EAAalK,EAAGkK,WAC1EzT,EAAQlD,EAAM0J,GAAGkB,SAASgM,iBAAmB,IAC7CC,EAAgB,GAChBtd,EAAe,EACfC,EAAa,GACbsd,EAAoB,SAAUC,EAAe3b,GAC7C,IAAIhC,EAAWgC,EAAIhC,SAAUiV,EAAcjT,EAAIiT,YAC/C9U,GAAgBwd,EAAgB1I,EAChC,IAAK,IAAI2I,EAAK,EAAGvK,EAAK7a,EAAKwH,GAAW4d,EAAKvK,EAAG7b,OAAQomB,IAAM,CACxD,IAAItd,EAAM+S,EAAGuK,GACbH,EAAc7hB,KAAKoE,EAASM,MAGpC,OAAOsG,EAAMoU,QAAQ4B,cAAcna,MAAK,SAAUjK,GAC9C,IAAIqlB,EAAY,SAAU7J,GACtB,IAAIF,EAAQoH,KAAKC,IAAIrR,EAAOtR,EAAKhB,OAASwc,GAC1C,OAAOwE,EAAU5C,QAAQ,CACrB7F,MAAOA,EACPvX,KAAMA,EAAKP,MAAM+b,EAAQA,EAASF,GAClCgK,MAAO,cACRrb,MAAK,SAAUgI,GAKd,IAJA,IAAIsT,EAAY,GACZC,EAAY,GACZC,EAAUX,EAAW,GAAK,KAC1BY,EAAa,GACR7oB,EAAI,EAAGA,EAAIye,IAASze,EAAG,CAC5B,IAAI8oB,EAAY1T,EAAOpV,GACnB+oB,EAAQ,CACR9nB,MAAO8G,EAAU+gB,GACjB3L,QAASha,EAAKwb,EAAS3e,KAEsB,IAA7C+nB,EAAS5nB,KAAK4oB,EAAOA,EAAM9nB,MAAO8nB,KACf,MAAfA,EAAM9nB,MACN4nB,EAAWtiB,KAAKpD,EAAKwb,EAAS3e,IAExBioB,GAAoE,IAAxDlK,GAAImK,EAAWY,GAAYZ,EAAWa,EAAM9nB,SAK9D0nB,EAAUpiB,KAAKwiB,EAAM9nB,OACjBgnB,GACAW,EAAQriB,KAAKpD,EAAKwb,EAAS3e,MAN/B6oB,EAAWtiB,KAAKpD,EAAKwb,EAAS3e,IAC9B0oB,EAAUniB,KAAKwiB,EAAM9nB,SASjC,IAAI+nB,EAAW/G,GAAgBb,IAC3BA,EAAI3M,QAAUK,MACM,mBAAZgT,GAA0BA,IAAYmB,KAAmB,CACjE9K,MAAOiD,EAAIjD,MACXkC,MAAOe,EAAIf,OAEf,OAAO5c,QAAQgK,QAAQib,EAAUvmB,OAAS,GACtCghB,EAAUxD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,MAAOkB,OAAQsT,IACjDtb,MAAK,SAAUT,GAChB,IAAK,IAAI1B,KAAO0B,EAAIhC,SAChBke,EAAW1hB,OAAOD,SAAS+D,GAAM,GAErCod,EAAkBK,EAAUvmB,OAAQwK,OACpCS,MAAK,WAAc,OAAQub,EAAUxmB,OAAS,GAAM6mB,GAA+B,iBAAZlB,IAC3E3E,EAAUxD,OAAO,CACbjF,MAAOA,EACPxG,KAAM,MACN/Q,KAAMylB,EACNxT,OAAQuT,EACRK,SAAUA,EACVE,WAA+B,mBAAZpB,GACZA,IACR1a,MAAK,SAAUT,GAAO,OAAO0b,EAAkBM,EAAUxmB,OAAQwK,SAAaS,MAAK,WAAc,OAAQyb,EAAW1mB,OAAS,GAAM6mB,GAAYlB,IAAYmB,KAC9J9F,EAAUxD,OAAO,CACbjF,MAAOA,EACPxG,KAAM,SACN/Q,KAAM0lB,EACNG,SAAUA,IACX5b,MAAK,SAAUT,GAAO,OAAO0b,EAAkBQ,EAAW1mB,OAAQwK,SAAaS,MAAK,WACvF,OAAOjK,EAAKhB,OAASwc,EAASF,GAAS+J,EAAU7J,EAASlK,UAItE,OAAO+T,EAAU,GAAGpb,MAAK,WACrB,GAAIgb,EAAcjmB,OAAS,EACvB,MAAM,IAAI0I,EAAY,sCAAuCud,EAAetd,EAAcC,GAC9F,OAAO5H,EAAKhB,iBAK5B4c,EAAWnd,UAAUue,OAAS,WAC1B,IAAIiB,EAAM/e,KAAKijB,KAAMjF,EAAQe,EAAIf,MACjC,OAAI4B,GAAgBb,KACdA,EAAI0B,YAAclJ,IAA8C,IAAfyG,EAAMnM,MAElD7R,KAAKojB,QAAO,SAAU/K,GACzB,IAAIqI,EAAa3B,EAAIoE,MAAM/I,KAAK3B,OAAOiI,WACnCoG,EAAY9I,EAChB,OAAOe,EAAIoE,MAAM/I,KAAKgC,MAAM,CAAE/D,MAAOA,EAAO6I,MAAO,CAAEpF,MAAO4E,EAAY1C,MAAO8I,KAAe/b,MAAK,SAAUqR,GACzG,OAAO2C,EAAIoE,MAAM/I,KAAKkD,OAAO,CAAEjF,MAAOA,EAAOxG,KAAM,cAAemM,MAAO8I,IACpE/b,MAAK,SAAU4Q,GAChB,IAAIrT,EAAWqT,EAAGrT,SAAUqT,EAAG6B,WAAY7B,EAAG9H,QAAS,IAAI0J,EAAc5B,EAAG4B,YAC5E,GAAIA,EACA,MAAM,IAAI/U,EAAY,+BAAgCnK,OAAOyC,KAAKwH,GAAUlD,KAAI,SAAUwD,GAAO,OAAON,EAASM,MAAUwT,EAAQmB,GACvI,OAAOnB,EAAQmB,WAKxBvd,KAAK2d,OAAOiJ,KAEhBlK,EArYO,GAuYdkK,GAAiB,SAAUhoB,EAAOmgB,GAAO,OAAOA,EAAIngB,MAAQ,MAsChE,SAASmoB,GAAc9hB,EAAGhC,GACtB,OAAOgC,EAAIhC,GAAK,EAAIgC,IAAMhC,EAAI,EAAI,EAEtC,SAAS+jB,GAAqB/hB,EAAGhC,GAC7B,OAAOgC,EAAIhC,GAAK,EAAIgC,IAAMhC,EAAI,EAAI,EAGtC,SAASye,GAAKuF,EAAyBlV,EAAKmV,GACxC,IAAIC,EAAaF,aAAmCxM,GAChD,IAAIwM,EAAwBvK,WAAWuK,GACvCA,EAEJ,OADAE,EAAWlE,KAAKC,MAAQgE,EAAI,IAAIA,EAAEnV,GAAO,IAAIvI,UAAUuI,GAChDoV,EAEX,SAASC,GAAgBC,GACrB,OAAO,IAAIA,EAAY3K,WAAW2K,GAAa,WAAc,OAAOC,GAAW,OAAQlV,MAAM,GAYjG,SAASmV,GAAWroB,EAAKsoB,EAAUC,EAAaC,EAAahM,EAAKsF,GAG9D,IAFA,IAAIlhB,EAAS0jB,KAAKC,IAAIvkB,EAAIY,OAAQ4nB,EAAY5nB,QAC1C6nB,GAAO,EACFhqB,EAAI,EAAGA,EAAImC,IAAUnC,EAAG,CAC7B,IAAIiqB,EAAaJ,EAAS7pB,GAC1B,GAAIiqB,IAAeF,EAAY/pB,GAC3B,OAAI+d,EAAIxc,EAAIvB,GAAI8pB,EAAY9pB,IAAM,EACvBuB,EAAIoF,OAAO,EAAG3G,GAAK8pB,EAAY9pB,GAAK8pB,EAAYnjB,OAAO3G,EAAI,GAClE+d,EAAIxc,EAAIvB,GAAI+pB,EAAY/pB,IAAM,EACvBuB,EAAIoF,OAAO,EAAG3G,GAAK+pB,EAAY/pB,GAAK8pB,EAAYnjB,OAAO3G,EAAI,GAClEgqB,GAAO,EACAzoB,EAAIoF,OAAO,EAAGqjB,GAAOH,EAASG,GAAOF,EAAYnjB,OAAOqjB,EAAM,GAClE,KAEPjM,EAAIxc,EAAIvB,GAAIiqB,GAAc,IAC1BD,EAAMhqB,GAEd,OAAImC,EAAS4nB,EAAY5nB,QAAkB,SAARkhB,EACxB9hB,EAAMuoB,EAAYnjB,OAAOpF,EAAIY,QACpCA,EAASZ,EAAIY,QAAkB,SAARkhB,EAChB9hB,EAAIoF,OAAO,EAAGmjB,EAAY3nB,QAC7B6nB,EAAM,EAAI,KAAOzoB,EAAIoF,OAAO,EAAGqjB,GAAOD,EAAYC,GAAOF,EAAYnjB,OAAOqjB,EAAM,GAE9F,SAASE,GAAuBR,EAAaS,EAAOC,EAASC,GACzD,IAAIjQ,EAAOF,EAAOoQ,EAASC,EAAcC,EAAcC,EAAWC,EAAeC,EAAaP,EAAQjoB,OACtG,IAAKioB,EAAQ9M,OAAM,SAAUvb,GAAK,MAAoB,iBAANA,KAC5C,OAAOgiB,GAAK2F,EAxhCE,oBA0hClB,SAASkB,EAAcvH,GACnBjJ,EAvCR,SAAsBiJ,GAClB,MAAe,SAARA,EACH,SAAUthB,GAAK,OAAOA,EAAE8oB,eACxB,SAAU9oB,GAAK,OAAOA,EAAE+oB,eAoChBC,CAAa1H,GACrBnJ,EAnCR,SAAsBmJ,GAClB,MAAe,SAARA,EACH,SAAUthB,GAAK,OAAOA,EAAE+oB,eACxB,SAAU/oB,GAAK,OAAOA,EAAE8oB,eAgChBG,CAAa3H,GACrBiH,EAAmB,SAARjH,EAAiB+F,GAAgBC,GAC5C,IAAI4B,EAAeb,EAAQ3iB,KAAI,SAAUyjB,GACrC,MAAO,CAAEhR,MAAOA,EAAMgR,GAAS9Q,MAAOA,EAAM8Q,OAC7C1E,MAAK,SAAUlf,EAAGhC,GACjB,OAAOglB,EAAQhjB,EAAE4S,MAAO5U,EAAE4U,UAE9BqQ,EAAeU,EAAaxjB,KAAI,SAAU0jB,GAAM,OAAOA,EAAG/Q,SAC1DoQ,EAAeS,EAAaxjB,KAAI,SAAU0jB,GAAM,OAAOA,EAAGjR,SAC1DuQ,EAAYpH,EACZqH,EAAyB,SAARrH,EAAiB,GAAKgH,EAE3CO,EAAc,QACd,IAAIvqB,EAAI,IAAIqpB,EAAY3K,WAAW2K,GAAa,WAAc,OAAO0B,GAAYb,EAAa,GAAIC,EAAaG,EAAa,GAAKN,MACjIhqB,EAAE6mB,mBAAqB,SAAUuD,GAC7BG,EAAcH,IAElB,IAAIY,EAAsB,EA4B1B,OA3BAhrB,EAAEqlB,eAAc,SAAU9B,EAAQC,EAASpW,GACvC,IAAIlM,EAAMqiB,EAAOriB,IACjB,GAAmB,iBAARA,EACP,OAAO,EACX,IAAIsoB,EAAW3P,EAAM3Y,GACrB,GAAI4oB,EAAMN,EAAUW,EAAca,GAC9B,OAAO,EAIP,IADA,IAAIC,EAAuB,KAClBtrB,EAAIqrB,EAAqBrrB,EAAI2qB,IAAc3qB,EAAG,CACnD,IAAIurB,EAAS3B,GAAWroB,EAAKsoB,EAAUU,EAAavqB,GAAIwqB,EAAaxqB,GAAIsqB,EAASG,GACnE,OAAXc,GAA4C,OAAzBD,EACnBD,EAAsBrrB,EAAI,GACI,OAAzBsrB,GAAiChB,EAAQgB,EAAsBC,GAAU,KAC9ED,EAAuBC,GAS/B,OALI1H,EADyB,OAAzByH,EACQ,WAAc1H,EAAOS,SAASiH,EAAuBZ,IAGrDjd,IAEL,KAGRpN,EAEX,SAAS+qB,GAAYlR,EAAOE,EAAOD,EAAWE,GAC1C,MAAO,CACHnG,KAAM,EACNgG,MAAOA,EACPE,MAAOA,EACPD,UAAWA,EACXE,UAAWA,GAGnB,SAASsP,GAAW1oB,GAChB,MAAO,CACHiT,KAAM,EACNgG,MAAOjZ,EACPmZ,MAAOnZ,GAIf,IAAI6b,GAAgB,WAChB,SAASA,KAmOT,OAjOApc,OAAOC,eAAemc,EAAYlb,UAAW,aAAc,CACvDf,IAAK,WACD,OAAOwB,KAAKijB,KAAKE,MAAMvK,GAAG8D,YAE9Bne,YAAY,EACZ2D,cAAc,IAElBuY,EAAYlb,UAAU4pB,QAAU,SAAUtR,EAAOE,EAAOqR,EAAcC,GAClED,GAAgC,IAAjBA,EACfC,GAAgC,IAAjBA,EACf,IACI,OAAKrpB,KAAKspB,KAAKzR,EAAOE,GAAS,GACE,IAA5B/X,KAAKspB,KAAKzR,EAAOE,KAAiBqR,GAAgBC,MAAmBD,IAAgBC,GAC/EjC,GAAgBpnB,MACpB,IAAIA,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,GAAYlR,EAAOE,GAAQqR,GAAeC,MAEpG,MAAOliB,GACH,OAAOua,GAAK1hB,KAAMiX,MAG1BwD,EAAYlb,UAAUob,OAAS,SAAU/b,GACrC,OAAa,MAATA,EACO8iB,GAAK1hB,KAAMiX,IACf,IAAIjX,KAAK0c,WAAW1c,MAAM,WAAc,OAAOsnB,GAAW1oB,OAErE6b,EAAYlb,UAAUgqB,MAAQ,SAAU3qB,GACpC,OAAa,MAATA,EACO8iB,GAAK1hB,KAAMiX,IACf,IAAIjX,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,GAAYnqB,OAAO2F,GAAW,OAExFkW,EAAYlb,UAAUiqB,aAAe,SAAU5qB,GAC3C,OAAa,MAATA,EACO8iB,GAAK1hB,KAAMiX,IACf,IAAIjX,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,GAAYnqB,OAAO2F,GAAW,OAExFkW,EAAYlb,UAAUkqB,MAAQ,SAAU7qB,GACpC,OAAa,MAATA,EACO8iB,GAAK1hB,KAAMiX,IACf,IAAIjX,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,QAAYxkB,EAAW3F,GAAO,GAAO,OAE/F6b,EAAYlb,UAAUmqB,aAAe,SAAU9qB,GAC3C,OAAa,MAATA,EACO8iB,GAAK1hB,KAAMiX,IACf,IAAIjX,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,QAAYxkB,EAAW3F,OAEjF6b,EAAYlb,UAAUoqB,WAAa,SAAUC,GACzC,MAAmB,iBAARA,EACAlI,GAAK1hB,KA9oCF,oBA+oCPA,KAAKmpB,QAAQS,EAAKA,EAAM9S,IAAW,GAAM,IAEpD2D,EAAYlb,UAAUsqB,qBAAuB,SAAUD,GACnD,MAAY,KAARA,EACO5pB,KAAK2pB,WAAWC,GACpB/B,GAAuB7nB,MAAM,SAAUwF,EAAGP,GAAK,OAA2B,IAApBO,EAAEpB,QAAQa,EAAE,MAAc,CAAC2kB,GAAM9S,KAElG2D,EAAYlb,UAAUuqB,iBAAmB,SAAUF,GAC/C,OAAO/B,GAAuB7nB,MAAM,SAAUwF,EAAGP,GAAK,OAAOO,IAAMP,EAAE,KAAO,CAAC2kB,GAAM,KAEvFnP,EAAYlb,UAAUwqB,gBAAkB,WACpC,IAAI9nB,EAAMmE,EAAWrG,MAAMoG,EAAetG,WAC1C,OAAmB,IAAfoC,EAAInC,OACGsnB,GAAgBpnB,MACpB6nB,GAAuB7nB,MAAM,SAAUwF,EAAGP,GAAK,OAAyB,IAAlBA,EAAEb,QAAQoB,KAAcvD,EAAK,KAE9FwY,EAAYlb,UAAUyqB,0BAA4B,WAC9C,IAAI/nB,EAAMmE,EAAWrG,MAAMoG,EAAetG,WAC1C,OAAmB,IAAfoC,EAAInC,OACGsnB,GAAgBpnB,MACpB6nB,GAAuB7nB,MAAM,SAAUwF,EAAGP,GAAK,OAAOA,EAAEsL,MAAK,SAAUnR,GAAK,OAAwB,IAAjBoG,EAAEpB,QAAQhF,QAAiB6C,EAAK6U,KAE9H2D,EAAYlb,UAAU0qB,MAAQ,WAC1B,IAAI/a,EAAQlP,KACRiC,EAAMmE,EAAWrG,MAAMoG,EAAetG,WACtCooB,EAAUjoB,KAAKspB,KACnB,IACIrnB,EAAIkiB,KAAK8D,GAEb,MAAO9gB,GACH,OAAOua,GAAK1hB,KAAMiX,IAEtB,GAAmB,IAAfhV,EAAInC,OACJ,OAAOsnB,GAAgBpnB,MAC3B,IAAIhC,EAAI,IAAIgC,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,GAAY9mB,EAAI,GAAIA,EAAIA,EAAInC,OAAS,OAC5F9B,EAAE6mB,mBAAqB,SAAUuD,GAC7BH,EAAyB,SAAdG,EACPlZ,EAAMgb,WACNhb,EAAMib,YACVloB,EAAIkiB,KAAK8D,IAEb,IAAItqB,EAAI,EAkBR,OAjBAK,EAAEqlB,eAAc,SAAU9B,EAAQC,EAASpW,GAEvC,IADA,IAAIlM,EAAMqiB,EAAOriB,IACV+oB,EAAQ/oB,EAAK+C,EAAItE,IAAM,GAE1B,KADEA,IACQsE,EAAInC,OAEV,OADA0hB,EAAQpW,IACD,EAGf,OAA6B,IAAzB6c,EAAQ/oB,EAAK+C,EAAItE,MAIjB6jB,GAAQ,WAAcD,EAAOS,SAAS/f,EAAItE,QACnC,MAGRK,GAEXyc,EAAYlb,UAAU6qB,SAAW,SAAUxrB,GACvC,OAAOoB,KAAKqqB,WAAW,CAAC,EA/sCnB,IA+sC4BzrB,GAAQ,CAACA,EAAOoB,KAAK4Y,GAAGsC,UAAW,CAAEoP,eAAe,EAAOC,eAAe,KAE/G9P,EAAYlb,UAAUirB,OAAS,WAC3B,IAAIvoB,EAAMmE,EAAWrG,MAAMoG,EAAetG,WAC1C,GAAmB,IAAfoC,EAAInC,OACJ,OAAO,IAAIE,KAAK0c,WAAW1c,MAC/B,IACIiC,EAAIkiB,KAAKnkB,KAAKkqB,YAElB,MAAO/iB,GACH,OAAOua,GAAK1hB,KAAMiX,IAEtB,IAAIwT,EAASxoB,EAAIyB,QAAO,SAAU4G,EAAKrG,GAAO,OAAOqG,EACjDA,EAAI9J,OAAO,CAAC,CAAC8J,EAAIA,EAAIxK,OAAS,GAAG,GAAImE,KACrC,CAAC,EA7tCA,IA6tCSA,MAAU,MAExB,OADAwmB,EAAOvmB,KAAK,CAACjC,EAAIA,EAAInC,OAAS,GAAIE,KAAK4Y,GAAGsC,UACnClb,KAAKqqB,WAAWI,EAAQ,CAAEH,eAAe,EAAOC,eAAe,KAE1E9P,EAAYlb,UAAU8qB,WAAa,SAAUI,EAAQzoB,GACjD,IAAIkN,EAAQlP,KACR0b,EAAM1b,KAAKspB,KAAMoB,EAAY1qB,KAAKkqB,WAAYS,EAAa3qB,KAAKmqB,YAAa1G,EAAMzjB,KAAK4qB,KAAMC,EAAM7qB,KAAK8qB,KAC7G,GAAsB,IAAlBL,EAAO3qB,OACP,OAAOsnB,GAAgBpnB,MAC3B,IAAKyqB,EAAOxP,OAAM,SAAU+C,GACxB,YAAoBzZ,IAAbyZ,EAAM,SACIzZ,IAAbyZ,EAAM,IACN0M,EAAU1M,EAAM,GAAIA,EAAM,KAAO,KAErC,OAAO0D,GAAK1hB,KAAM,6HAA8HiJ,GAAW2U,iBAE/J,IAAI0M,GAAiBtoB,IAAqC,IAA1BA,EAAQsoB,cACpCC,EAAgBvoB,IAAqC,IAA1BA,EAAQuoB,cAevC,IAEItoB,EAFA8oB,EAAgBL,EACpB,SAASM,EAAY/lB,EAAGhC,GAAK,OAAO8nB,EAAc9lB,EAAE,GAAIhC,EAAE,IAE1D,KACIhB,EAAMwoB,EAAO/mB,QAlBjB,SAAkB+mB,EAAQQ,GAEtB,IADA,IAAIttB,EAAI,EAAGC,EAAI6sB,EAAO3qB,OACfnC,EAAIC,IAAKD,EAAG,CACf,IAAIqgB,EAAQyM,EAAO9sB,GACnB,GAAI+d,EAAIuP,EAAS,GAAIjN,EAAM,IAAM,GAAKtC,EAAIuP,EAAS,GAAIjN,EAAM,IAAM,EAAG,CAClEA,EAAM,GAAKyF,EAAIzF,EAAM,GAAIiN,EAAS,IAClCjN,EAAM,GAAK6M,EAAI7M,EAAM,GAAIiN,EAAS,IAClC,OAKR,OAFIttB,IAAMC,GACN6sB,EAAOvmB,KAAK+mB,GACTR,IAMuB,KAC1BtG,KAAK6G,GAEb,MAAO9a,GACH,OAAOwR,GAAK1hB,KAAMiX,IAEtB,IAAIiU,EAAW,EACXC,EAA0BZ,EAC1B,SAAUrrB,GAAO,OAAOwrB,EAAUxrB,EAAK+C,EAAIipB,GAAU,IAAM,GAC3D,SAAUhsB,GAAO,OAAOwrB,EAAUxrB,EAAK+C,EAAIipB,GAAU,KAAO,GAC5DE,EAA0Bd,EAC1B,SAAUprB,GAAO,OAAOyrB,EAAWzrB,EAAK+C,EAAIipB,GAAU,IAAM,GAC5D,SAAUhsB,GAAO,OAAOyrB,EAAWzrB,EAAK+C,EAAIipB,GAAU,KAAO,GAIjE,IAAIG,EAAWF,EACXntB,EAAI,IAAIgC,KAAK0c,WAAW1c,MAAM,WAAc,OAAO+oB,GAAY9mB,EAAI,GAAG,GAAIA,EAAIA,EAAInC,OAAS,GAAG,IAAKwqB,GAAgBC,MAqCvH,OApCAvsB,EAAE6mB,mBAAqB,SAAUuD,GACX,SAAdA,GACAiD,EAAWF,EACXJ,EAAgBL,IAGhBW,EAAWD,EACXL,EAAgBJ,GAEpB1oB,EAAIkiB,KAAK6G,IAEbhtB,EAAEqlB,eAAc,SAAU9B,EAAQC,EAASpW,GAEvC,IADA,IAAIlM,EAAMqiB,EAAOriB,IACVmsB,EAASnsB,IAEZ,KADEgsB,IACejpB,EAAInC,OAEjB,OADA0hB,EAAQpW,IACD,EAGf,QAzBJ,SAA+BlM,GAC3B,OAAQisB,EAAwBjsB,KAASksB,EAAwBlsB,GAwB7DosB,CAAsBpsB,KAGqB,IAAtCgQ,EAAMoa,KAAKpqB,EAAK+C,EAAIipB,GAAU,KAAmD,IAAtChc,EAAMoa,KAAKpqB,EAAK+C,EAAIipB,GAAU,KAI9E1J,GAAQ,WACAuJ,IAAkBL,EAClBnJ,EAAOS,SAAS/f,EAAIipB,GAAU,IAE9B3J,EAAOS,SAAS/f,EAAIipB,GAAU,QAP/B,MAYRltB,GAEXyc,EAAYlb,UAAUgsB,gBAAkB,WACpC,IAAItpB,EAAMmE,EAAWrG,MAAMoG,EAAetG,WAC1C,OAAKoC,EAAIgZ,OAAM,SAAUvb,GAAK,MAAoB,iBAANA,KAGzB,IAAfuC,EAAInC,OACGsnB,GAAgBpnB,MACpBA,KAAKqqB,WAAWpoB,EAAImD,KAAI,SAAUwkB,GAAO,MAAO,CAACA,EAAKA,EAAM9S,QAJxD4K,GAAK1hB,KAAM,8CAMnBya,EApOQ,GA0PnB,SAAS+Q,GAAmBlc,GACxB,OAAOkC,IAAK,SAAUyE,GAGlB,OAFAwV,GAAexV,GACf3G,EAAO2G,EAAMyV,OAAOxI,QACb,KAGf,SAASuI,GAAexV,GAChBA,EAAM0V,iBACN1V,EAAM0V,kBACN1V,EAAMwV,gBACNxV,EAAMwV,iBAGd,IAEIG,GAAe9M,GAAO,KAFa,kBAInC+M,GAAgB,WAChB,SAASA,KA6JT,OA3JAA,EAAYtsB,UAAUusB,MAAQ,WAK1B,OAJA9oB,GAAQ6K,GAAIhN,UACVb,KAAK+rB,UACgB,IAAnB/rB,KAAK+rB,WAAoBle,GAAIhN,SAC7BgN,GAAIme,aAAehsB,MAChBA,MAEX6rB,EAAYtsB,UAAU0sB,QAAU,WAE5B,GADAjpB,GAAQ6K,GAAIhN,QACa,KAAnBb,KAAK+rB,UAGP,IAFKle,GAAIhN,SACLgN,GAAIme,aAAe,MAChBhsB,KAAKksB,cAAcpsB,OAAS,IAAME,KAAKmsB,WAAW,CACrD,IAAIC,EAAWpsB,KAAKksB,cAAcG,QAClC,IACIhZ,GAAO+Y,EAAS,GAAIA,EAAS,IAEjC,MAAOjlB,KAGf,OAAOnH,MAEX6rB,EAAYtsB,UAAU4sB,QAAU,WAC5B,OAAOnsB,KAAK+rB,WAAale,GAAIme,eAAiBhsB,MAElD6rB,EAAYtsB,UAAUN,OAAS,SAAU0Z,GACrC,IAAIzJ,EAAQlP,KACZ,IAAKA,KAAKlB,KACN,OAAOkB,KACX,IAAIgZ,EAAQhZ,KAAK4Y,GAAGI,MAChBY,EAAc5Z,KAAK4Y,GAAGnK,OAAOmL,YAEjC,GADA5W,GAAQhD,KAAK2Y,WACRA,IAAaK,EACd,OAAQY,GAAeA,EAAY1b,MAC/B,IAAK,sBACD,MAAM,IAAI+K,GAAWnB,eAAe8R,GACxC,IAAK,kBACD,MAAM,IAAI3Q,GAAWhB,WAAW2R,EAAYrS,QAASqS,GACzD,QACI,MAAM,IAAI3Q,GAAWqjB,WAAW1S,GAG5C,IAAK5Z,KAAKusB,OACN,MAAM,IAAItjB,GAAWjB,oBAuBzB,OAtBAhF,EAAmC,OAA5BhD,KAAK2Z,YAAYlL,SACxBkK,EAAW3Y,KAAK2Y,SAAWA,IACtB3Y,KAAK4Y,GAAGwB,KACHpa,KAAK4Y,GAAGwB,KAAKoS,YAAYxsB,KAAK+Y,WAAY/Y,KAAKlB,KAAM,CAAE2tB,WAAYzsB,KAAK0sB,8BACxE1T,EAAMwT,YAAYxsB,KAAK+Y,WAAY/Y,KAAKlB,KAAM,CAAE2tB,WAAYzsB,KAAK0sB,gCAClEliB,QAAUgH,IAAK,SAAUmb,GAC9BlB,GAAekB,GACfzd,EAAM0d,QAAQjU,EAASuK,UAE3BvK,EAASkU,QAAUrb,IAAK,SAAUmb,GAC9BlB,GAAekB,GACfzd,EAAMqd,QAAUrd,EAAM0d,QAAQ,IAAI3jB,GAAWlB,MAAM4Q,EAASuK,QAC5DhU,EAAMqd,QAAS,EACfrd,EAAM4d,GAAG,SAASvS,KAAKoS,MAE3BhU,EAASoU,WAAavb,IAAK,WACvBtC,EAAMqd,QAAS,EACfrd,EAAM8d,WACF,iBAAkBrU,GAClBiT,GAAaqB,eAAe1S,KAAK5B,EAAuB,iBAGzD3Y,MAEX6rB,EAAYtsB,UAAU6Q,SAAW,SAAUtR,EAAMsE,EAAI8pB,GACjD,IAAIhe,EAAQlP,KACZ,GAAa,cAATlB,GAAsC,cAAdkB,KAAKlB,KAC7B,OAAOmW,GAAU,IAAIhM,GAAWkkB,SAAS,4BAC7C,IAAKntB,KAAKusB,OACN,OAAOtX,GAAU,IAAIhM,GAAWjB,qBACpC,GAAIhI,KAAKmsB,UACL,OAAO,IAAIne,IAAa,SAAU5C,EAASkE,GACvCJ,EAAMgd,cAAchoB,KAAK,CAAC,WAClBgL,EAAMkB,SAAStR,EAAMsE,EAAI8pB,GAAYniB,KAAKK,EAASkE,IACpDzB,QAGV,GAAIqf,EACL,OAAO9Z,IAAS,WACZ,IAAI3T,EAAI,IAAIuO,IAAa,SAAU5C,EAASkE,GACxCJ,EAAM4c,QACN,IAAI9nB,EAAKZ,EAAGgI,EAASkE,EAAQJ,GACzBlL,GAAMA,EAAG+G,MACT/G,EAAG+G,KAAKK,EAASkE,MAIzB,OAFA7P,EAAEuS,SAAQ,WAAc,OAAO9C,EAAM+c,aACrCxsB,EAAE0O,MAAO,EACF1O,KAIX,IAAIA,EAAI,IAAIuO,IAAa,SAAU5C,EAASkE,GACxC,IAAItL,EAAKZ,EAAGgI,EAASkE,EAAQJ,GACzBlL,GAAMA,EAAG+G,MACT/G,EAAG+G,KAAKK,EAASkE,MAGzB,OADA7P,EAAE0O,MAAO,EACF1O,GAGfosB,EAAYtsB,UAAU6tB,MAAQ,WAC1B,OAAOptB,KAAKyU,OAASzU,KAAKyU,OAAO2Y,QAAUptB,MAE/C6rB,EAAYtsB,UAAU8tB,QAAU,SAAUC,GACtC,IAAIC,EAAOvtB,KAAKotB,QACZxd,EAAU5B,GAAa5C,QAAQkiB,GACnC,GAAIC,EAAKC,YACLD,EAAKC,YAAcD,EAAKC,YAAYziB,MAAK,WAAc,OAAO6E,SAE7D,CACD2d,EAAKC,YAAc5d,EACnB2d,EAAKE,cAAgB,GACrB,IAAIC,EAAQH,EAAK5U,SAASgV,YAAYJ,EAAKxU,WAAW,KACrD,SAAS6U,IAEN,MADEL,EAAKM,WACAN,EAAKE,cAAc3tB,QACrBytB,EAAKE,cAAcpB,OAApB,GACAkB,EAAKC,cACLE,EAAMlvB,KAAKiU,KAAUlI,UAAYqjB,GALzC,GAQJ,IAAIE,EAAqBP,EAAKC,YAC9B,OAAO,IAAIxf,IAAa,SAAU5C,EAASkE,GACvCM,EAAQ7E,MAAK,SAAUT,GAAO,OAAOijB,EAAKE,cAAcvpB,KAAKsN,GAAKpG,EAAQjM,KAAK,KAAMmL,QAAW,SAAUyH,GAAO,OAAOwb,EAAKE,cAAcvpB,KAAKsN,GAAKlC,EAAOnQ,KAAK,KAAM4S,QAAWC,SAAQ,WAClLub,EAAKC,cAAgBM,IACrBP,EAAKC,YAAc,aAKnC3B,EAAYtsB,UAAUwuB,MAAQ,WACtB/tB,KAAKusB,SACLvsB,KAAKusB,QAAS,EACVvsB,KAAK2Y,UACL3Y,KAAK2Y,SAASoV,QAClB/tB,KAAK4sB,QAAQ,IAAI3jB,GAAWlB,SAGpC8jB,EAAYtsB,UAAU4jB,MAAQ,SAAU5K,GACpC,IAAIyV,EAAkBhuB,KAAKiuB,kBAAoBjuB,KAAKiuB,gBAAkB,IACtE,GAAIzsB,EAAOwsB,EAAgBzV,GACvB,OAAOyV,EAAezV,GAC1B,IAAI2V,EAAcluB,KAAKyY,OAAOF,GAC9B,IAAK2V,EACD,MAAM,IAAIjlB,GAAWyP,SAAS,SAAWH,EAAY,4BAEzD,IAAI4V,EAAwB,IAAInuB,KAAK4Y,GAAGV,MAAMK,EAAW2V,EAAaluB,MAGtE,OAFAmuB,EAAsB/T,KAAOpa,KAAK4Y,GAAGwB,KAAK+I,MAAM5K,GAChDyV,EAAezV,GAAa4V,EACrBA,GAEJtC,EA9JQ,GAuMnB,SAASuC,GAAgBlwB,EAAM6F,EAASkd,EAAQlF,EAAOqB,EAAMpC,EAAUyF,GACnE,MAAO,CACHviB,KAAMA,EACN6F,QAASA,EACTkd,OAAQA,EACRlF,MAAOA,EACPqB,KAAMA,EACNpC,SAAUA,EACVqT,KAAMpN,IAAWR,EAAY,IAAM,KAAO1E,EAAQ,IAAM,KAAOqB,EAAO,KAAO,IAAMkR,GAAgBvqB,IAG3G,SAASuqB,GAAgBvqB,GACrB,MAA0B,iBAAZA,EACVA,EACAA,EAAW,IAAM,GAAG0D,KAAK3J,KAAKiG,EAAS,KAAO,IAAO,GAG7D,SAASwqB,GAAkBrwB,EAAM4c,EAASD,GACtC,MAAO,CACH3c,KAAMA,EACN4c,QAASA,EACTD,QAASA,EACTiC,YAAa,KACbxB,UAAW/X,EAAcsX,GAAS,SAAUiB,GAAS,MAAO,CAACA,EAAM5d,KAAM4d,OAOjF,IAAI0S,GAAY,SAAUC,GACtB,IAGI,OAFAA,EAAYC,KAAK,CAAC,KAClBF,GAAY,WAAc,MAAO,CAAC,KAC3B,CAAC,IAEZ,MAAOrnB,GAEH,OADAqnB,GAAY,WAAc,OAAO1X,IAC1BA,KAIf,SAAS6X,GAAgB5qB,GACrB,OAAe,MAAXA,EACO,aAEiB,iBAAZA,EAOpB,SAAmCA,GAE/B,OAAqB,IADTA,EAAQoB,MAAM,KAChBrF,OACC,SAAUmB,GAAO,OAAOA,EAAI8C,IAG5B,SAAU9C,GAAO,OAAO6C,EAAa7C,EAAK8C,IAZ1C6qB,CAA0B7qB,GAG1B,SAAU9C,GAAO,OAAO6C,EAAa7C,EAAK8C,IAazD,SAAS8qB,GAASxoB,GACd,MAAO,GAAG9F,MAAMzC,KAAKuI,GAEzB,IAAIyoB,GAAc,EAClB,SAASC,GAAgBhrB,GACrB,OAAkB,MAAXA,EACH,MACmB,iBAAZA,EACHA,EACA,IAAMA,EAAQ0D,KAAK,KAAO,IAEtC,SAASunB,GAAapW,EAAI6V,EAAaQ,GAqDnC,SAASC,EAAgBlR,GACrB,GAAmB,IAAfA,EAAMnM,KACN,OAAO,KACX,GAAmB,IAAfmM,EAAMnM,KACN,MAAM,IAAI3O,MAAM,4CACpB,IAAI2U,EAAQmG,EAAMnG,MAAOE,EAAQiG,EAAMjG,MAAOD,EAAYkG,EAAMlG,UAAWE,EAAYgG,EAAMhG,UAQ7F,YAPyBzT,IAAVsT,OACDtT,IAAVwT,EACI,KACA0W,EAAYU,WAAWpX,IAASC,QAC1BzT,IAAVwT,EACI0W,EAAYW,WAAWvX,IAASC,GAChC2W,EAAYY,MAAMxX,EAAOE,IAASD,IAAaE,GAiQ3D,IAAI2D,EAjUJ,SAAuB/C,EAAIP,GACvB,IAAIiX,EAAST,GAASjW,EAAG2W,kBACzB,MAAO,CACH9W,OAAQ,CACJva,KAAM0a,EAAG1a,KACToxB,OAAQA,EAAOlqB,KAAI,SAAU+d,GAAS,OAAO9K,EAAMsV,YAAYxK,MAAW/d,KAAI,SAAUsoB,GACpF,IAAI3pB,EAAU2pB,EAAM3pB,QAASyrB,EAAgB9B,EAAM8B,cAC/CxU,EAAWja,EAAQgD,GACnB6hB,EAAsB,MAAX7hB,EACX0rB,EAAiB,GACjB9rB,EAAS,CACTzF,KAAMwvB,EAAMxvB,KACZwiB,WAAY,CACRxiB,KAAM,KACNwxB,cAAc,EACd9J,SAAUA,EACV5K,SAAUA,EACVjX,QAASA,EACTyrB,cAAeA,EACfvO,QAAQ,EACR4E,WAAY8I,GAAgB5qB,IAEhC8W,QAASgU,GAASnB,EAAMiC,YAAYvqB,KAAI,SAAUwf,GAAa,OAAO8I,EAAM5R,MAAM8I,MAC7Exf,KAAI,SAAU0W,GACf,IAAI5d,EAAO4d,EAAM5d,KAAM+iB,EAASnF,EAAMmF,OAAQ2O,EAAa9T,EAAM8T,WAAY7rB,EAAU+X,EAAM/X,QAEzFJ,EAAS,CACTzF,KAAMA,EACN8c,SAHWja,EAAQgD,GAInBA,QAASA,EACTkd,OAAQA,EACR2O,WAAYA,EACZ/J,WAAY8I,GAAgB5qB,IAGhC,OADA0rB,EAAeV,GAAgBhrB,IAAYJ,EACpCA,KAEXgd,kBAAmB,SAAU5c,GAAW,OAAO0rB,EAAeV,GAAgBhrB,MAMlF,OAJA0rB,EAAe,OAAS9rB,EAAO+c,WAChB,MAAX3c,IACA0rB,EAAeV,GAAgBhrB,IAAYJ,EAAO+c,YAE/C/c,MAGfksB,UAAWP,EAAOxvB,OAAS,GAAM,WAAYuY,EAAMsV,YAAY2B,EAAO,OAC3C,oBAAdlY,WAA6B,SAASxQ,KAAKwQ,UAAUC,aACzD,oBAAoBzQ,KAAKwQ,UAAUC,YACpC,GAAG7W,OAAO4W,UAAUC,UAAUyQ,MAAM,kBAAkB,GAAK,MAgRlEgI,CAAclX,EAAIqW,GAAWxW,EAASkD,EAAGlD,OAAQoX,EAAYlU,EAAGkU,UACrEP,EAAS7W,EAAO6W,OAAOlqB,KAAI,SAAU8oB,GAAe,OA/PxD,SAA2BA,GACvB,IAAI3V,EAAY2V,EAAYhwB,KA+L5B,MAAO,CACHA,KAAMqa,EACNE,OAAQyV,EACR5Q,OAjMJ,SAAgB3B,GACZ,IAAItD,EAAQsD,EAAGtD,MAAOxG,EAAO8J,EAAG9J,KAAM/Q,EAAO6a,EAAG7a,KAAMiS,EAAS4I,EAAG5I,OAAQiL,EAAQrC,EAAGqC,MACrF,OAAO,IAAI5c,SAAQ,SAAUgK,EAASkE,GAClClE,EAAUoG,GAAKpG,GACf,IAAIsiB,EAAQrV,EAAMsV,YAAYpV,GAC1BqN,EAA4B,MAAjB8H,EAAM3pB,QACjBgsB,EAAsB,QAATle,GAA2B,QAATA,EACnC,IAAKke,GAAuB,WAATle,GAA8B,gBAATA,EACpC,MAAM,IAAI3O,MAAM,2BAA6B2O,GACjD,IAMIme,EANAlwB,GAAUgB,GAAQiS,GAAU,CAAEjT,OAAQ,IAAKA,OAC/C,GAAIgB,GAAQiS,GAAUjS,EAAKhB,SAAWiT,EAAOjT,OACzC,MAAM,IAAIoD,MAAM,iEAEpB,GAAe,IAAXpD,EACA,OAAOsL,EAAQ,CAAEmS,YAAa,EAAGjV,SAAU,GAAIuL,QAAS,GAAI2J,gBAAYjZ,IAE5E,IAAI0rB,EAAO,GACP3nB,EAAW,GACXiV,EAAc,EACd2S,EAAe,SAAUja,KACvBsH,EACFkO,GAAexV,IAEnB,GAAa,gBAATpE,EAAwB,CACxB,GAAmB,IAAfmM,EAAMnM,KACN,OAAOzG,EAAQ,CAAEmS,YAAaA,EAAajV,SAAUA,EAAUuL,QAAS,GAAI2J,gBAAYjZ,IACzE,IAAfyZ,EAAMnM,KACNoe,EAAK/rB,KAAK8rB,EAAMtC,EAAM3P,SAEtBkS,EAAK/rB,KAAK8rB,EAAMtC,EAAM5P,OAAOoR,EAAgBlR,SAEhD,CACD,IAAIrC,EAAKoU,EACLnK,EACI,CAAC7S,EAAQjS,GACT,CAACiS,EAAQ,MACb,CAACjS,EAAM,MAAOqvB,EAAQxU,EAAG,GAAIyU,EAAQzU,EAAG,GAC5C,GAAIoU,EACA,IAAK,IAAIpyB,EAAI,EAAGA,EAAImC,IAAUnC,EAC1BsyB,EAAK/rB,KAAK8rB,EAAOI,QAAsB7rB,IAAb6rB,EAAMzyB,GAC5B+vB,EAAM7b,GAAMse,EAAMxyB,GAAIyyB,EAAMzyB,IAC5B+vB,EAAM7b,GAAMse,EAAMxyB,KACtBqyB,EAAIxlB,QAAU0lB,OAIlB,IAASvyB,EAAI,EAAGA,EAAImC,IAAUnC,EAC1BsyB,EAAK/rB,KAAK8rB,EAAMtC,EAAM7b,GAAMse,EAAMxyB,KAClCqyB,EAAIxlB,QAAU0lB,EAI1B,IAAI1pB,EAAO,SAAUyP,GACjB,IAAIuH,EAAavH,EAAMyV,OAAO/nB,OAC9BssB,EAAK9uB,SAAQ,SAAU6uB,EAAKryB,GAAK,OAAoB,MAAbqyB,EAAI9M,QAAkB5a,EAAS3K,GAAKqyB,EAAI9M,UAChF9X,EAAQ,CACJmS,YAAaA,EACbjV,SAAUA,EACVuL,QAAkB,WAAThC,EAAoB/Q,EAAOmvB,EAAK7qB,KAAI,SAAU4qB,GAAO,OAAOA,EAAIrsB,UACzE6Z,WAAYA,KAGpBwS,EAAIxlB,QAAU,SAAUyL,GACpBia,EAAaja,GACbzP,EAAKyP,IAET+Z,EAAIzlB,UAAY/D,MAgIpB0X,QAAS,SAAUvC,GACf,IAAItD,EAAQsD,EAAGtD,MAAOvX,EAAO6a,EAAG7a,KAChC,OAAO,IAAIM,SAAQ,SAAUgK,EAASkE,GAClClE,EAAUoG,GAAKpG,GAef,IAdA,IAKI4kB,EALAtC,EAAQrV,EAAMsV,YAAYpV,GAC1BzY,EAASgB,EAAKhB,OACd6D,EAAS,IAAIrD,MAAMR,GACnBuwB,EAAW,EACXC,EAAgB,EAEhBC,EAAiB,SAAUta,GAC3B,IAAI+Z,EAAM/Z,EAAMyV,OACX/nB,EAAOqsB,EAAIQ,MAAQR,EAAIrsB,SAEtB2sB,IAAkBD,GACpBjlB,EAAQzH,IAEZusB,EAAe1E,GAAmBlc,GAC7B3R,EAAI,EAAGA,EAAImC,IAAUnC,EAAG,CAElB,MADDmD,EAAKnD,MAEXqyB,EAAMtC,EAAMlvB,IAAIsC,EAAKnD,KACjB6yB,KAAO7yB,EACXqyB,EAAIzlB,UAAYgmB,EAChBP,EAAIxlB,QAAU0lB,IACZG,GAGO,IAAbA,GACAjlB,EAAQzH,OAGpBnF,IAAK,SAAUmd,GACX,IAAItD,EAAQsD,EAAGtD,MAAOnZ,EAAMyc,EAAGzc,IAC/B,OAAO,IAAIkC,SAAQ,SAAUgK,EAASkE,GAClClE,EAAUoG,GAAKpG,GACf,IACI4kB,EADQ3X,EAAMsV,YAAYpV,GACd/Z,IAAIU,GACpB8wB,EAAIzlB,UAAY,SAAU0L,GAAS,OAAO7K,EAAQ6K,EAAMyV,OAAO/nB,SAC/DqsB,EAAIxlB,QAAUghB,GAAmBlc,OAGzC4R,MArFJ,SAAe2O,GACX,OAAO,SAAUY,GACb,OAAO,IAAIrvB,SAAQ,SAAUgK,EAASkE,GAClClE,EAAUoG,GAAKpG,GACf,IAAIiN,EAAQoY,EAAQpY,MAAOtF,EAAS0d,EAAQ1d,OAAQX,EAAQqe,EAAQre,MAAO8O,EAAQuP,EAAQvP,MACvFwP,EAAkBte,IAAUK,SAAWlO,EAAY6N,EACnD0J,EAAQoF,EAAMpF,MAAOkC,EAAQkD,EAAMlD,MACnC0P,EAAQrV,EAAMsV,YAAYpV,GAC1BoY,EAAS7U,EAAM4T,aAAehC,EAAQA,EAAM5R,MAAMA,EAAM5d,MACxD0yB,EAAc1B,EAAgBlR,GAClC,GAAc,IAAV5L,EACA,OAAOhH,EAAQ,CAAEzH,OAAQ,KAC7B,GAAIksB,EAAW,CACX,IAAIG,EAAMjd,EACN4d,EAAOE,OAAOD,EAAaF,GAC3BC,EAAOG,WAAWF,EAAaF,GACnCV,EAAIzlB,UAAY,SAAU0L,GAAS,OAAO7K,EAAQ,CAAEzH,OAAQsS,EAAMyV,OAAO/nB,UACzEqsB,EAAIxlB,QAAUghB,GAAmBlc,OAEhC,CACD,IAAIyhB,EAAU,EACVC,EAAQje,KAAY,kBAAmB4d,GACvCA,EAAO9P,WAAW+P,GAClBD,EAAOM,cAAcL,GACrBM,EAAW,GACfF,EAAMzmB,UAAY,SAAU0L,GACxB,IAAIsL,EAASyP,EAAMrtB,OACnB,OAAK4d,GAEL2P,EAAShtB,KAAK6O,EAASwO,EAAO3iB,MAAQ2iB,EAAOb,cACvCqQ,IAAY3e,EACPhH,EAAQ,CAAEzH,OAAQutB,SAC7B3P,EAAOS,YAJI5W,EAAQ,CAAEzH,OAAQutB,KAMjCF,EAAMxmB,QAAUghB,GAAmBlc,QAmDxC4R,CAAM2O,GACbhP,WAxKJ,SAAoBlF,GAChB,IAAItD,EAAQsD,EAAGtD,MAAOtF,EAAS4I,EAAG5I,OAAQmO,EAAQvF,EAAGuF,MAAOtE,EAAUjB,EAAGiB,QAASqE,EAAStF,EAAGsF,OAC9F,OAAO,IAAI7f,SAAQ,SAAUgK,EAASkE,GAClClE,EAAUoG,GAAKpG,GACf,IAAI0Q,EAAQoF,EAAMpF,MAAOkC,EAAQkD,EAAMlD,MACnC0P,EAAQrV,EAAMsV,YAAYpV,GAC1BoY,EAAS7U,EAAM4T,aACfhC,EACAA,EAAM5R,MAAMA,EAAM5d,MAClBkqB,EAAYxL,EACZqE,EACI,aACA,OACJA,EACI,aACA,OACJ+O,EAAMjd,KAAY,kBAAmB4d,GACrCA,EAAO9P,WAAWqO,EAAgBlR,GAAQoK,GAC1CuI,EAAOM,cAAc/B,EAAgBlR,GAAQoK,GACjD4H,EAAIxlB,QAAUghB,GAAmBlc,GACjC0gB,EAAIzlB,UAAYiH,IAAK,SAAUmb,GAC3B,IAAIpL,EAASyO,EAAIrsB,OACjB,GAAK4d,EAAL,CAIAA,EAAO4P,QAAUrC,GACjBvN,EAAO/a,MAAO,EACd,IAAI4qB,EAAkB7P,EAAOS,SAAS7iB,KAAKoiB,GACvC8P,EAA4B9P,EAAO+P,mBACnCD,IACAA,EAA4BA,EAA0BlyB,KAAKoiB,IAC/D,IAAIgQ,EAAiBhQ,EAAOC,QAAQriB,KAAKoiB,GAErCiQ,EAAyB,WAAc,MAAM,IAAItuB,MAAM,uBAC3Dqe,EAAOlJ,MAAQA,EACfkJ,EAAOE,KAAOF,EAAOS,SAAWT,EAAO+P,mBAAqB/P,EAAOC,QAHnC,WAAc,MAAM,IAAIte,MAAM,uBAI9Dqe,EAAOG,KAAOlQ,GAAKlC,GACnBiS,EAAOhb,KAAO,WACV,IAAI2I,EAAQlP,KACRyxB,EAAS,EACb,OAAOzxB,KAAK2C,OAAM,WAAc,OAAO8uB,IAAWviB,EAAM8S,WAAa9S,EAAMuS,UAAW1W,MAAK,WAAc,OAAOmE,MAEpHqS,EAAO5e,MAAQ,SAAUgK,GACrB,IAAI+kB,EAAmB,IAAItwB,SAAQ,SAAUuwB,EAAkBC,GAC3DD,EAAmBngB,GAAKmgB,GACxB3B,EAAIxlB,QAAUghB,GAAmBoG,GACjCrQ,EAAOG,KAAOkQ,EACdrQ,EAAOE,KAAO,SAAU7iB,GACpB2iB,EAAOE,KAAOF,EAAOS,SAAWT,EAAO+P,mBAAqB/P,EAAOC,QAAUgQ,EAC7EG,EAAiB/yB,OAGrBizB,EAAkB,WAClB,GAAI7B,EAAIrsB,OACJ,IACIgJ,IAEJ,MAAOoF,GACHwP,EAAOG,KAAK3P,QAIhBwP,EAAO/a,MAAO,EACd+a,EAAO5e,MAAQ,WAAc,MAAM,IAAIO,MAAM,6BAC7Cqe,EAAOE,QAWf,OARAuO,EAAIzlB,UAAYiH,IAAK,SAAUmb,GAC3BqD,EAAIzlB,UAAYsnB,EAChBA,OAEJtQ,EAAOS,SAAWoP,EAClB7P,EAAO+P,mBAAqBD,EAC5B9P,EAAOC,QAAU+P,EACjBM,IACOH,GAEXtmB,EAAQmW,QAvDJnW,EAAQ,QAwDbkE,OA0FP8M,MAAO,SAAUT,GACb,IAAIuF,EAAQvF,EAAGuF,MAAO7I,EAAQsD,EAAGtD,MAC7ByD,EAAQoF,EAAMpF,MAAOkC,EAAQkD,EAAMlD,MACvC,OAAO,IAAI5c,SAAQ,SAAUgK,EAASkE,GAClC,IAAIoe,EAAQrV,EAAMsV,YAAYpV,GAC1BoY,EAAS7U,EAAM4T,aAAehC,EAAQA,EAAM5R,MAAMA,EAAM5d,MACxD0yB,EAAc1B,EAAgBlR,GAC9BgS,EAAMY,EAAcD,EAAOvU,MAAMwU,GAAeD,EAAOvU,QAC3D4T,EAAIzlB,UAAYiH,IAAK,SAAUmb,GAAM,OAAOvhB,EAAQuhB,EAAGjB,OAAO/nB,WAC9DqsB,EAAIxlB,QAAUghB,GAAmBlc,QAMcwiB,CAAkB5D,MAC7E6D,EAAW,GAEf,OADAzC,EAAOnuB,SAAQ,SAAUgiB,GAAS,OAAO4O,EAAS5O,EAAMjlB,MAAQilB,KACzD,CACHlc,MAAO,SACPulB,YAAa5T,EAAG4T,YAAYrtB,KAAKyZ,GACjCuK,MAAO,SAAUjlB,GAEb,IADa6zB,EAAS7zB,GAElB,MAAM,IAAIgF,MAAM,UAAYhF,EAAO,eACvC,OAAO6zB,EAAS7zB,IAEpB8zB,SAAUvf,IACVwf,QAASzD,GAAUC,GACnBhW,OAAQA,GAUhB,SAASyZ,GAAuBC,EAAanZ,EAAO2C,EAAIsT,GACpD,IAAImD,EAAczW,EAAGyW,YAErB,OAFkCzW,EAAGF,UAE9B,CACH4W,OAVR,SAA+BC,EAAWH,GACtC,OAAOA,EAAYzuB,QAAO,SAAU6uB,EAAM5W,GACtC,IAAI1c,EAAS0c,EAAG1c,OAChB,OAAQU,EAASA,EAAS,GAAI4yB,GAAOtzB,EAAOszB,MAC7CD,GAIUE,CAAsBxD,GAAahW,EAAOoZ,EAAanD,GAAWkD,EAAYE,SAK/F,SAASI,GAAyB9W,EAAIsT,GAClC,IAAIrW,EAAK+C,EAAG+W,OACR1Z,EAAQiW,EAASrW,GACjBzG,EAAS+f,GAAuBtZ,EAAG+Z,aAAc3Z,EAAOJ,EAAG4C,MAAOyT,GACtErW,EAAGwB,KAAOjI,EAAOkgB,OACjBzZ,EAAG0W,OAAOnuB,SAAQ,SAAUgiB,GACxB,IAAI5K,EAAY4K,EAAMjlB,KAClB0a,EAAGwB,KAAK3B,OAAO6W,OAAO/e,MAAK,SAAUqiB,GAAO,OAAOA,EAAI10B,OAASqa,OAChE4K,EAAM/I,KAAOxB,EAAGwB,KAAK+I,MAAM5K,GACvBK,EAAGL,aAAsBK,EAAGV,QAC5BU,EAAGL,GAAW6B,KAAO+I,EAAM/I,UAM3C,SAASyY,GAAclX,EAAImX,EAAMC,EAAYC,GACzC,IAAIpa,EAAK+C,EAAG+W,OACZK,EAAW5xB,SAAQ,SAAUoX,GACzB,IAAIE,EAASua,EAASza,GACtBua,EAAK3xB,SAAQ,SAAUF,GACnB,IAAIgyB,EAAWzwB,EAAsBvB,EAAKsX,KACrC0a,GAAa,UAAWA,QAA+B1uB,IAAnB0uB,EAASr0B,SAC1CqC,IAAQ2X,EAAGiT,YAAYtsB,WAAa0B,aAAe2X,EAAGiT,YACtD/pB,EAAQb,EAAKsX,EAAW,CACpB/Z,IAAK,WAAc,OAAOwB,KAAKmjB,MAAM5K,IACrCtW,IAAK,SAAUrD,GACXN,EAAe0B,KAAMuY,EAAW,CAAE3Z,MAAOA,EAAOuD,UAAU,EAAMD,cAAc,EAAM3D,YAAY,OAKxG0C,EAAIsX,GAAa,IAAIK,EAAGV,MAAMK,EAAWE,UAM7D,SAASya,GAAgBvX,EAAImX,GACzB,IAAIla,EAAK+C,EAAG+W,OACZI,EAAK3xB,SAAQ,SAAUF,GACnB,IAAK,IAAI/B,KAAO+B,EACRA,EAAI/B,aAAgB0Z,EAAGV,cAChBjX,EAAI/B,MAI3B,SAASi0B,GAAkBluB,EAAGhC,GAC1B,OAAOgC,EAAEmuB,KAAKC,QAAUpwB,EAAEmwB,KAAKC,QAEnC,SAASC,GAAa1a,EAAI2a,EAAYC,EAAiBlkB,GACnD,IAAImkB,EAAe7a,EAAGS,UAClBhB,EAAQO,EAAGQ,mBAAmB,YAAaR,EAAG8a,YAAaD,GAC/Dpb,EAAMpZ,OAAOu0B,GACbnb,EAAMsB,YAAY/H,MAAMtC,GACxB,IAAIqkB,EAAoBtb,EAAMuU,QAAQztB,KAAKkZ,GACvCQ,EAAYhL,GAAIgL,WAAahL,GACjCuF,IAAS,WACLvF,GAAIwK,MAAQA,EACZxK,GAAIgL,UAAYA,EACG,IAAf0a,GACAzyB,EAAK2yB,GAActyB,SAAQ,SAAUoX,GACjCqb,GAAYJ,EAAiBjb,EAAWkb,EAAalb,GAAWuC,QAAS2Y,EAAalb,GAAWsC,YAErG4X,GAAyB7Z,EAAI4a,GAC7BxlB,GAAauF,QAAO,WAAc,OAAOqF,EAAGkU,GAAG+G,SAAStZ,KAAKlC,MAAWzG,MAAM+hB,IAM1F,SAAgChY,EAAI4X,EAAYlb,EAAOmb,GACnD,IAAI5a,EAAK+C,EAAG+W,OACRoB,EAAQ,GACRC,EAAWnb,EAAGob,UACdP,EAAe7a,EAAGS,UAAY4a,GAAkBrb,EAAIA,EAAGI,MAAOwa,GAC9DU,GAA2B,EAmE/B,SAASC,IACL,OAAOL,EAAMh0B,OAASkO,GAAa5C,QAAQ0oB,EAAMzH,OAANyH,CAAczb,EAAMM,WAAW5N,KAAKopB,GAC3EnmB,GAAa5C,UAErB,OAtEgB2oB,EAASzuB,QAAO,SAAUiD,GAAK,OAAOA,EAAE6qB,KAAKC,SAAWE,KAC9DpyB,SAAQ,SAAUkyB,GACxBS,EAAM5vB,MAAK,WACP,IAAIkwB,EAAYX,EACZY,EAAYhB,EAAQD,KAAKJ,SAC7BsB,GAA2B1b,EAAIwb,EAAWZ,GAC1Cc,GAA2B1b,EAAIyb,EAAWb,GAC1CC,EAAe7a,EAAGS,UAAYgb,EAC9B,IAAIE,EAAOC,GAAcJ,EAAWC,GACpCE,EAAKpX,IAAIhc,SAAQ,SAAUszB,GACvBb,GAAYJ,EAAiBiB,EAAM,GAAIA,EAAM,GAAG3Z,QAAS2Z,EAAM,GAAG5Z,YAEtE0Z,EAAKG,OAAOvzB,SAAQ,SAAUuzB,GAC1B,GAAIA,EAAOC,SACP,MAAM,IAAI1rB,GAAW2rB,QAAQ,4CAG7B,IAAIC,EAAUrB,EAAgB7F,YAAY+G,EAAOx2B,MACjDw2B,EAAOvX,IAAIhc,SAAQ,SAAU6a,GAAO,OAAO8Y,GAASD,EAAS7Y,MAC7D0Y,EAAOA,OAAOvzB,SAAQ,SAAU6a,GAC5B6Y,EAAQE,YAAY/Y,EAAI9d,MACxB42B,GAASD,EAAS7Y,MAEtB0Y,EAAOM,IAAI7zB,SAAQ,SAAU8zB,GAAW,OAAOJ,EAAQE,YAAYE,SAG3E,IAAIC,EAAiB7B,EAAQD,KAAK8B,eAClC,GAAIA,GAAkB7B,EAAQD,KAAKC,QAAUE,EAAY,CACrDd,GAAyB7Z,EAAI4a,GAC7Bnb,EAAM4V,gBAAkB,GACxBiG,GAA2B,EAC3B,IAAIiB,EAAkBpwB,EAAasvB,GACnCE,EAAKS,IAAI7zB,SAAQ,SAAUgiB,GACvBgS,EAAgBhS,GAASiR,EAAUjR,MAEvC+P,GAAgBta,EAAI,CAACA,EAAGiT,YAAYtsB,YACpCszB,GAAcja,EAAI,CAACA,EAAGiT,YAAYtsB,WAAYuB,EAAKq0B,GAAkBA,GACrE9c,EAAMI,OAAS0c,EACf,IAIIC,EAJAC,EAA0B5uB,EAAgByuB,GAC1CG,GACAtgB,KAGJ,IAAIugB,EAAkBtnB,GAAauF,QAAO,WAEtC,IADA6hB,EAAgBF,EAAe7c,KAEvBgd,EAAyB,CACzB,IAAIE,EAAclmB,GAAwBlQ,KAAK,KAAM,MACrDi2B,EAAcrqB,KAAKwqB,EAAaA,OAI5C,OAAQH,GAA+C,mBAAvBA,EAAcrqB,KAC1CiD,GAAa5C,QAAQgqB,GAAiBE,EAAgBvqB,MAAK,WAAc,OAAOqqB,SAG5FtB,EAAM5vB,MAAK,SAAUyU,GACZub,GAA6B5c,IAsF9C,SAA6B+c,EAAW1b,GACpC,GAAGpY,MAAMzC,KAAK6a,EAASC,GAAG2W,kBAAkBpuB,SAAQ,SAAUq0B,GAC1D,OAA+B,MAAxBnB,EAAUmB,IAAsB7c,EAASC,GAAG6c,kBAAkBD,MAtF7DE,CADgBrC,EAAQD,KAAKJ,SACEra,GAEnCua,GAAgBta,EAAI,CAACA,EAAGiT,YAAYtsB,YACpCszB,GAAcja,EAAI,CAACA,EAAGiT,YAAYtsB,WAAYqZ,EAAG8a,YAAa9a,EAAGS,WACjEhB,EAAMI,OAASG,EAAGS,gBAOnB8a,IAAWppB,MAAK,WAkE3B,IAA6BspB,EAAW1b,IAjEE6a,EAkEtC1yB,EADyBuzB,EAjEDZ,GAkERtyB,SAAQ,SAAUoX,GACzBI,EAASC,GAAG2W,iBAAiBoG,SAASpd,IACvCqb,GAAYjb,EAAUJ,EAAW8b,EAAU9b,GAAWuC,QAASuZ,EAAU9b,GAAWsC,eApJpF+a,CAAuBhd,EAAI2a,EAAYlb,EAAOmb,GAAiB5hB,MAAM+hB,MAmFjF,SAASa,GAAcJ,EAAWC,GAC9B,IAKIlR,EALAoR,EAAO,CACPS,IAAK,GACL7X,IAAK,GACLuX,OAAQ,IAGZ,IAAKvR,KAASiR,EACLC,EAAUlR,IACXoR,EAAKS,IAAI9wB,KAAKif,GAEtB,IAAKA,KAASkR,EAAW,CACrB,IAAIwB,EAASzB,EAAUjR,GAAQ2S,EAASzB,EAAUlR,GAClD,GAAK0S,EAGA,CACD,IAAInB,EAAS,CACTx2B,KAAMilB,EACN4S,IAAKD,EACLnB,UAAU,EACVK,IAAK,GACL7X,IAAK,GACLuX,OAAQ,IAEZ,GACA,IAAMmB,EAAO/a,QAAQ/W,SAAW,KAAU,IAAM+xB,EAAOhb,QAAQ/W,SAAW,KACrE8xB,EAAO/a,QAAQsC,OAAS0Y,EAAOhb,QAAQsC,OAASjG,GAEjDud,EAAOC,UAAW,EAClBJ,EAAKG,OAAOxwB,KAAKwwB,OAEhB,CACD,IAAIsB,EAAaH,EAAOva,UACpB2a,EAAaH,EAAOxa,UACpB2Z,OAAU,EACd,IAAKA,KAAWe,EACPC,EAAWhB,IACZP,EAAOM,IAAI9wB,KAAK+wB,GAExB,IAAKA,KAAWgB,EAAY,CACxB,IAAIC,EAASF,EAAWf,GAAUkB,EAASF,EAAWhB,GACjDiB,EAEIA,EAAO7H,MAAQ8H,EAAO9H,KAC3BqG,EAAOA,OAAOxwB,KAAKiyB,GAFnBzB,EAAOvX,IAAIjZ,KAAKiyB,IAIpBzB,EAAOM,IAAIl1B,OAAS,GAAK40B,EAAOvX,IAAIrd,OAAS,GAAK40B,EAAOA,OAAO50B,OAAS,IACzEy0B,EAAKG,OAAOxwB,KAAKwwB,SAlCzBH,EAAKpX,IAAIjZ,KAAK,CAACif,EAAO2S,IAuC9B,OAAOvB,EAEX,SAASX,GAAYjb,EAAUJ,EAAWuC,EAASD,GAC/C,IAAI6S,EAAQ/U,EAASC,GAAGwd,kBAAkB7d,EAAWuC,EAAQ/W,QACzD,CAAEA,QAAS+W,EAAQ/W,QAASyrB,cAAe1U,EAAQsC,MACnD,CAAEoS,cAAe1U,EAAQsC,OAE7B,OADAvC,EAAQ1Z,SAAQ,SAAU6a,GAAO,OAAO8Y,GAASpH,EAAO1R,MACjD0R,EAcX,SAASoH,GAASpH,EAAO1R,GACrB0R,EAAM2I,YAAYra,EAAI9d,KAAM8d,EAAIjY,QAAS,CAAEkd,OAAQjF,EAAIiF,OAAQ2O,WAAY5T,EAAID,QAEnF,SAASkY,GAAkBrb,EAAII,EAAOiW,GAClC,IAAIwE,EAAe,GAenB,OAdmBlzB,EAAMyY,EAAMuW,iBAAkB,GACpCpuB,SAAQ,SAAUq0B,GAK3B,IAJA,IAAI9H,EAAQuB,EAAStB,YAAY6H,GAC7BzxB,EAAU2pB,EAAM3pB,QAChB+W,EAAUsT,GAAgBE,GAAgBvqB,GAAUA,GAAW,IAAI,GAAO,IAAS2pB,EAAM8B,cAAezrB,GAA8B,iBAAZA,GAAsB,GAChJ8W,EAAU,GACLyb,EAAI,EAAGA,EAAI5I,EAAMiC,WAAW7vB,SAAUw2B,EAAG,CAC9C,IAAIC,EAAW7I,EAAM5R,MAAM4R,EAAMiC,WAAW2G,IAC5CvyB,EAAUwyB,EAASxyB,QACnB,IAAI+X,EAAQsS,GAAgBmI,EAASr4B,KAAM6F,IAAWwyB,EAAStV,SAAUsV,EAAS3G,YAAY,EAAO7rB,GAA8B,iBAAZA,GAAsB,GAC7I8W,EAAQ3W,KAAK4X,GAEjB2X,EAAa+B,GAAajH,GAAkBiH,EAAW1a,EAASD,MAE7D4Y,EAcX,SAASa,GAA2B3Y,EAAIlD,EAAQE,GAG5C,IAFA,IAAIC,EAAK+C,EAAG+W,OACR3Z,EAAaJ,EAASC,GAAG2W,iBACpB5xB,EAAI,EAAGA,EAAIob,EAAWjZ,SAAUnC,EAAG,CACxC,IAAI63B,EAAYzc,EAAWpb,GACvB+vB,EAAQ/U,EAASgV,YAAY6H,GACjC5c,EAAG4d,WAAa,WAAY9I,EAC5B,IAAK,IAAI4I,EAAI,EAAGA,EAAI5I,EAAMiC,WAAW7vB,SAAUw2B,EAAG,CAC9C,IAAI1R,EAAY8I,EAAMiC,WAAW2G,GAC7BvyB,EAAU2pB,EAAM5R,MAAM8I,GAAW7gB,QACjC0yB,EAA+B,iBAAZ1yB,EAAuBA,EAAU,IAAMxD,EAAMwD,GAAS0D,KAAK,KAAO,IACzF,GAAIgR,EAAO+c,GAAY,CACnB,IAAIkB,EAAYje,EAAO+c,GAAWla,UAAUmb,GACxCC,IACAA,EAAUx4B,KAAO0mB,SACVnM,EAAO+c,GAAWla,UAAUmb,GACnChe,EAAO+c,GAAWla,UAAUsJ,GAAa8R,KAKhC,oBAAdtf,WAA6B,SAASxQ,KAAKwQ,UAAUC,aAC3D,oBAAoBzQ,KAAKwQ,UAAUC,YACpC5W,EAAQk2B,mBAAqBl2B,aAAmBA,EAAQk2B,mBACxD,GAAGn2B,OAAO4W,UAAUC,UAAUyQ,MAAM,kBAAkB,GAAK,MAC3DlP,EAAG4d,YAAa,GAYxB,IAAII,GAAY,WACZ,SAASA,KA0CT,OAxCAA,EAAQr3B,UAAUs3B,iBAAmB,SAAUC,EAAQC,GACnDj2B,EAAKg2B,GAAQ31B,SAAQ,SAAUoX,GAC3B,GAA0B,OAAtBue,EAAOve,GAAqB,CAC5B,IAAIsC,EAA2Bic,EAAOve,GAdzBpT,MAAM,KAAKC,KAAI,SAAU0W,EAAOkb,GAErD,IAAI94B,GADJ4d,EAAQA,EAAMmb,QACGC,QAAQ,eAAgB,IACrCnzB,EAAU,MAAM6C,KAAK1I,GAAQA,EAAK4pB,MAAM,cAAc,GAAG3iB,MAAM,KAAOjH,EAC1E,OAAOkwB,GAAgBlwB,EAAM6F,GAAW,KAAM,KAAK6C,KAAKkV,GAAQ,KAAKlV,KAAKkV,GAAQ,OAAOlV,KAAKkV,GAAQ/a,EAAQgD,GAAuB,IAAbizB,MAW5Glc,EAAUD,EAAQwR,QACtB,GAAIvR,EAAQiB,MACR,MAAM,IAAI9S,GAAW2X,OAAO,sCAChC/F,EAAQ1Z,SAAQ,SAAU6a,GACtB,GAAIA,EAAIoB,KACJ,MAAM,IAAInU,GAAW2X,OAAO,wDAChC,IAAK5E,EAAIjY,QACL,MAAM,IAAIkF,GAAW2X,OAAO,2DAEpCmW,EAAUxe,GAAagW,GAAkBhW,EAAWuC,EAASD,QAIzE+b,EAAQr3B,UAAUu3B,OAAS,SAAUA,GACjC,IAAIle,EAAK5Y,KAAK4Y,GACd5Y,KAAKozB,KAAK+D,aAAen3B,KAAKozB,KAAK+D,aAC/Bn2B,EAAOhB,KAAKozB,KAAK+D,aAAcL,GAC/BA,EACJ,IAAI/C,EAAWnb,EAAGob,UACdoD,EAAa,GACbpE,EAAW,GAUf,OATAe,EAAS5yB,SAAQ,SAAUkyB,GACvBryB,EAAOo2B,EAAY/D,EAAQD,KAAK+D,cAChCnE,EAAYK,EAAQD,KAAKJ,SAAW,GACpCK,EAAQwD,iBAAiBO,EAAYpE,MAEzCpa,EAAGS,UAAY2Z,EACfE,GAAgBta,EAAI,CAACA,EAAGye,WAAYze,EAAIA,EAAGiT,YAAYtsB,YACvDszB,GAAcja,EAAI,CAACA,EAAGye,WAAYze,EAAIA,EAAGiT,YAAYtsB,UAAWS,KAAKozB,KAAK9D,QAASxuB,EAAKkyB,GAAWA,GACnGpa,EAAG8a,YAAc5yB,EAAKkyB,GACfhzB,MAEX42B,EAAQr3B,UAAU+3B,QAAU,SAAUC,GAElC,OADAv3B,KAAKozB,KAAK8B,eAAiBpqB,GAAgB9K,KAAKozB,KAAK8B,gBAAkBrrB,GAAK0tB,GACrEv3B,MAEJ42B,EA3CI,GA2Df,SAASY,GAAgB/b,EAAW2W,GAChC,IAAIqF,EAAYhc,EAAsB,WAStC,OARKgc,IACDA,EAAYhc,EAAsB,WAAI,IAAIic,GA1zEjC,YA0zEqD,CAC1DC,OAAQ,GACRlc,UAAWA,EACX2W,YAAaA,KAEPiB,QAAQ,GAAGyD,OAAO,CAAEc,QAAS,SAEpCH,EAAUtU,MAAM,WAE3B,SAAS0U,GAAmBpc,GACxB,OAAOA,GAA4C,mBAAxBA,EAAUqc,UAkBzC,SAASC,GAAmBpc,EAAIzd,GAC5B,IAAIud,EAAYE,EAAGF,UAAW2W,EAAczW,EAAGyW,aAC9CyF,GAAmBpc,IAx1EP,cAy1ETvd,GACAs5B,GAAgB/b,EAAW2W,GAAatU,OAAO5f,GAAM0T,MAAM/H,IAGnE,SAASmuB,GAAI50B,GACT,OAAOgQ,IAAS,WAEZ,OADAvF,GAAIqL,YAAa,EACV9V,OAIf,SAAS60B,KACL,IAKIC,EAFJ,OAHgB9gB,UAAU+gB,eACtB,WAAWvxB,KAAKwQ,UAAUC,aACzB,iBAAiBzQ,KAAKwQ,UAAUC,YACnBoE,UAAUqc,UAGrB,IAAI12B,SAAQ,SAAUgK,GACzB,IAAIgtB,EAAS,WAAc,OAAO3c,UAAUqc,YAAY9lB,QAAQ5G,IAChE8sB,EAAaG,YAAYD,EAAQ,KACjCA,OACDpmB,SAAQ,WAAc,OAAOsmB,cAAcJ,MANnC92B,QAAQgK,UASvB,SAASmtB,GAAU3f,GACf,IAAI4f,EAAQ5f,EAAGnK,OACXgN,EAAY7C,EAAG4C,MAAMC,UACzB,GAAI+c,EAAM3e,eAAiBjB,EAAGI,MAC1B,OAAOwf,EAAMxe,eAAejP,MAAK,WAAc,OAAOytB,EAAM5e,YACxD3E,GAAUujB,EAAM5e,aAChBhB,KACRlS,IAAU8xB,EAAMC,cAAcnqB,aAAepH,KAC7CsxB,EAAM3e,eAAgB,EACtB2e,EAAM5e,YAAc,KACpB4e,EAAMvf,cAAe,EACrB,IAAIwf,EAAgBD,EAAMC,cAC1B,SAASC,IACL,GAAIF,EAAMC,gBAAkBA,EACxB,MAAM,IAAIxvB,GAAWnB,eAAe,2BAE5C,IAAI6wB,EAAiBH,EAAMI,eAC3BC,EAAqB,KAAMC,GAAa,EACxC,OAAO9qB,GAAakF,KAAK,CAACulB,GAAqC,oBAAdrhB,UAA4BpJ,GAAa5C,UAAY6sB,MAAYltB,MAAK,WAAc,OAAO,IAAIiD,IAAa,SAAU5C,EAASkE,GAExK,GADAopB,KACKjd,EACD,MAAM,IAAIxS,GAAWhB,WACzB,IAAI8wB,EAASngB,EAAG1a,KACZ8xB,EAAMwI,EAAMQ,WACZvd,EAAU/B,KAAKqf,GACftd,EAAU/B,KAAKqf,EAAQvV,KAAKyV,MAAiB,GAAXrgB,EAAGsgB,QACzC,IAAKlJ,EACD,MAAM,IAAI/mB,GAAWhB,WACzB+nB,EAAIxlB,QAAUghB,GAAmBlc,GACjC0gB,EAAImJ,UAAY3nB,GAAKoH,EAAGwgB,gBACxBpJ,EAAIqJ,gBAAkB7nB,IAAK,SAAUrK,GAEjC,GADA0xB,EAAqB7I,EAAIxD,YACrBgM,EAAMQ,aAAepgB,EAAGkB,SAASwf,aAAc,CAC/CtJ,EAAIxlB,QAAUihB,GACdoN,EAAmB9K,QACnBiC,EAAIrsB,OAAO41B,QACX,IAAIC,EAAS/d,EAAUge,eAAeV,GACtCS,EAAOjvB,UAAYivB,EAAOhvB,QAAUgH,IAAK,WACrClC,EAAO,IAAIrG,GAAWywB,eAAe,YAAcX,EAAS,yBAG/D,CACDF,EAAmBruB,QAAUghB,GAAmBlc,GAChD,IAAIqqB,EAASxyB,EAAEosB,WAAa/P,KAAKoW,IAAI,EAAG,IAAM,EAAIzyB,EAAEosB,WACpDuF,EAAaa,EAAS,EACtB/gB,EAAG8Z,OAAO1Z,MAAQgX,EAAIrsB,OACtB2vB,GAAa1a,EAAI+gB,EAAS,GAAId,EAAoBvpB,MAEvDA,GACH0gB,EAAIzlB,UAAYiH,IAAK,WACjBqnB,EAAqB,KACrB,IAj2Ba9f,EAi2BTC,EAAQJ,EAAG8Z,OAAO1Z,MAAQgX,EAAIrsB,OAC9B4rB,EAAmBhvB,EAAMyY,EAAMuW,kBACnC,GAAIA,EAAiBzvB,OAAS,EAC1B,IACI,IAAImvB,EAAWjW,EAAMwT,YAp2BZ,KADJzT,EAq2BgDwW,GAp2BvDzvB,OAAeiZ,EAAW,GAAKA,EAo2B2C,YACpEyf,EAAMQ,WA/NlC,SAA0Brd,EAAI3C,EAAOiW,GACjC,IAAIrW,EAAK+C,EAAG+W,OACZ9Z,EAAGsgB,MAAQlgB,EAAMqa,QAAU,GAC3B,IAAII,EAAe7a,EAAGS,UAAY4a,GAAkBrb,EAAII,EAAOiW,GAC/DrW,EAAG8a,YAAcnzB,EAAMyY,EAAMuW,iBAAkB,GAC/CsD,GAAcja,EAAI,CAACA,EAAGye,YAAav2B,EAAK2yB,GAAeA,GA2N/BoG,CAAiBjhB,EAAII,EAAOiW,IAE5BqF,GAA2B1b,EAAIA,EAAGS,UAAW4V,GA3NzE,SAA+BrW,EAAIqW,GAC/B,IACIsF,EAAOC,GADWP,GAAkBrb,EAAIA,EAAGI,MAAOiW,GACZrW,EAAGS,WAC7C,QAASkb,EAAKpX,IAAIrd,QAAUy0B,EAAKG,OAAOnkB,MAAK,SAAUupB,GAAM,OAAOA,EAAG3c,IAAIrd,QAAUg6B,EAAGpF,OAAO50B,WAyNlEi6B,CAAsBnhB,EAAIqW,IAC3BrY,QAAQC,KAAK,uHAGrB4b,GAAyB7Z,EAAIqW,GAEjC,MAAO9nB,IAEX+P,GAAYhT,KAAK0U,GACjBI,EAAMghB,gBAAkBxoB,IAAK,SAAUmb,GACnC6L,EAAMyB,SAAU,EAChBrhB,EAAGkU,GAAG,iBAAiBvS,KAAKoS,MAEhC3T,EAAMkhB,QAAU1oB,IAAK,SAAUmb,GAC3B/T,EAAGkU,GAAG,SAASvS,KAAKoS,MAEpBmM,GA9GpB,SAA4Bnd,EAAIzd,GAC5B,IAAIud,EAAYE,EAAGF,UAAW2W,EAAczW,EAAGyW,aAC9CyF,GAAmBpc,IAl1EP,cAm1ETvd,GACAs5B,GAAgB/b,EAAW2W,GAAavU,IAAI,CAAE3f,KAAMA,IAAQ0T,MAAM/H,IA2GtDswB,CAAmBvhB,EAAG4C,MAAOud,GACjC3tB,MACDkE,WACEvE,MAAK,WAGd,OAFA2tB,IACAF,EAAM4B,kBAAoB,GACnBpsB,GAAa5C,QAAQ4sB,IAAI,WAAc,OAAOpf,EAAGkU,GAAGuN,MAAM9f,KAAK3B,EAAGof,SAAUjtB,MAAK,SAASuvB,IAC7F,GAAI9B,EAAM4B,kBAAkBt6B,OAAS,EAAG,CACpC,IAAIy6B,EAAe/B,EAAM4B,kBAAkB12B,OAAOoH,GAAiBjB,IAEnE,OADA2uB,EAAM4B,kBAAoB,GACnBpsB,GAAa5C,QAAQ4sB,IAAI,WAAc,OAAOuC,EAAa3hB,EAAGof,SAAUjtB,KAAKuvB,UAG7FtoB,SAAQ,WACPwmB,EAAM4B,kBAAoB,KAC1B5B,EAAM3e,eAAgB,KACvB9O,MAAK,WACJ,OAAO6N,KACRhH,OAAM,SAAUG,GACfymB,EAAM5e,YAAc7H,EACpB,IACI8mB,GAAsBA,EAAmB9K,QAE7C,MAAOpS,IAIP,OAHI8c,IAAkBD,EAAMC,eACxB7f,EAAGa,SAEAxE,GAAUlD,MAClBC,SAAQ,WACPwmB,EAAMvf,cAAe,EACrB0f,OAIR,SAAS6B,GAAcv0B,GACnB,IAAIw0B,EAAW,SAAU92B,GAAU,OAAOsC,EAASM,KAAK5C,IAA0E+2B,EAAYC,EAAKF,GAAWG,EAAUD,GAA1F,SAAUzX,GAAS,OAAOjd,EAAS40B,MAAM3X,MACvH,SAASyX,EAAKG,GACV,OAAO,SAAU72B,GACb,IAAIsC,EAAOu0B,EAAQ72B,GAAMrF,EAAQ2H,EAAK3H,MACtC,OAAO2H,EAAKC,KAAO5H,EACbA,GAA+B,mBAAfA,EAAMmM,KAEpBnM,EAAMmM,KAAK2vB,EAAWE,GADtB75B,EAAQnC,GAASwC,QAAQ0R,IAAIlU,GAAOmM,KAAK2vB,EAAWE,GAAWF,EAAU97B,IAIzF,OAAO+7B,EAAKF,EAALE,GAGX,SAASI,GAAuBj8B,EAAMk8B,EAAaC,GAC/C,IAAIt9B,EAAIkC,UAAUC,OAClB,GAAInC,EAAI,EACJ,MAAM,IAAIsL,GAAW2U,gBAAgB,qBAEzC,IADA,IAAIlb,EAAO,IAAIpC,MAAM3C,EAAI,KAChBA,GACL+E,EAAK/E,EAAI,GAAKkC,UAAUlC,GAC5Bs9B,EAAYv4B,EAAK2S,MACjB,IAAIia,EAAStqB,EAAQtC,GACrB,MAAO,CAAC5D,EAAMwwB,EAAQ2L,GAE1B,SAASC,GAAsBtiB,EAAI9Z,EAAMia,EAAYoiB,EAAmBF,GACpE,OAAOjtB,GAAa5C,UAAUL,MAAK,WAC/B,IAAI8N,EAAYhL,GAAIgL,WAAahL,GAC7BwK,EAAQO,EAAGQ,mBAAmBta,EAAMia,EAAYH,EAAGS,UAAW8hB,GAC9D3nB,EAAY,CACZ6E,MAAOA,EACPQ,UAAWA,GAEf,GAAIsiB,EACA9iB,EAAMM,SAAWwiB,EAAkBxiB,cAGnC,IACIN,EAAMpZ,SACN2Z,EAAGnK,OAAO6K,eAAiB,EAE/B,MAAOpJ,GACH,OAAIA,EAAGhS,OAAS6K,EAASwQ,cAAgBX,EAAGY,YAAcZ,EAAGnK,OAAO6K,eAAiB,GACjF1C,QAAQC,KAAK,4BACb+B,EAAGa,SACIb,EAAGc,OAAO3O,MAAK,WAAc,OAAOmwB,GAAsBtiB,EAAI9Z,EAAMia,EAAY,KAAMkiB,OAE1FhmB,GAAU/E,GAGzB,IAIIkrB,EAJAC,EAAmB50B,EAAgBw0B,GACnCI,GACAtmB,KAGJ,IAAIugB,EAAkBtnB,GAAauF,QAAO,WAEtC,GADA6nB,EAAcH,EAAUn9B,KAAKua,EAAOA,GAEhC,GAAIgjB,EAAkB,CAClB,IAAI9F,EAAclmB,GAAwBlQ,KAAK,KAAM,MACrDi8B,EAAYrwB,KAAKwqB,EAAaA,OAEG,mBAArB6F,EAAY70B,MAAoD,mBAAtB60B,EAAYP,QAClEO,EAAcZ,GAAcY,MAGrC5nB,GACH,OAAQ4nB,GAA2C,mBAArBA,EAAYrwB,KACtCiD,GAAa5C,QAAQgwB,GAAarwB,MAAK,SAAUvF,GAAK,OAAO6S,EAAMkU,OAC/D/mB,EACEyP,GAAU,IAAIhM,GAAWqyB,gBAAgB,kEAC7ChG,EAAgBvqB,MAAK,WAAc,OAAOqwB,MAAiBrwB,MAAK,SAAUvF,GAG5E,OAFI21B,GACA9iB,EAAM2U,WACH3U,EAAMsB,YAAY5O,MAAK,WAAc,OAAOvF,QACpDoM,OAAM,SAAUzK,GAEf,OADAkR,EAAMuU,QAAQzlB,GACP8N,GAAU9N,SAK7B,SAASo0B,GAAIt2B,EAAGrG,EAAOwd,GAEnB,IADA,IAAIzY,EAAS5C,EAAQkE,GAAKA,EAAE1E,QAAU,CAAC0E,GAC9BtH,EAAI,EAAGA,EAAIye,IAASze,EACzBgG,EAAOO,KAAKtF,GAChB,OAAO+E,EA4GX,IAAI63B,GAAyB,CACzBv0B,MAAO,SACP/I,KAAM,yBACNu9B,MAAO,EACPx8B,OA9GJ,SAAsCszB,GAClC,OAAO5yB,EAASA,EAAS,GAAI4yB,GAAO,CAAEpP,MAAO,SAAU5K,GAC/C,IAAI4K,EAAQoP,EAAKpP,MAAM5K,GACnBE,EAAS0K,EAAM1K,OACfijB,EAAc,GACdC,EAAoB,GACxB,SAASC,EAAkB73B,EAAS83B,EAASC,GACzC,IAAIC,EAAehN,GAAgBhrB,GAC/Bi4B,EAAaN,EAAYK,GAAgBL,EAAYK,IAAiB,GACtEE,EAAuB,MAAXl4B,EAAkB,EAAuB,iBAAZA,EAAuB,EAAIA,EAAQjE,OAC5Eo8B,EAAYL,EAAU,EACtBM,EAAex8B,EAASA,EAAS,GAAIm8B,GAAgB,CAAEI,UAAWA,EAAWL,QAASA,EAASI,UAAWA,EAAWpW,WAAY8I,GAAgB5qB,GAAUkd,QAASib,GAAaJ,EAAc7a,UACnM+a,EAAU93B,KAAKi4B,GACVA,EAAazM,cACdiM,EAAkBz3B,KAAKi4B,GAEvBF,EAAY,IAIZL,EAHmC,IAAdK,EACjBl4B,EAAQ,GACRA,EAAQxD,MAAM,EAAG07B,EAAY,GACCJ,EAAU,EAAGC,GAGnD,OADAE,EAAU7X,MAAK,SAAUlf,EAAGhC,GAAK,OAAOgC,EAAE42B,QAAU54B,EAAE44B,WAC/CM,EAEX,IAAIzb,EAAakb,EAAkBnjB,EAAOiI,WAAW3c,QAAS,EAAG0U,EAAOiI,YACxEgb,EAAY,OAAS,CAAChb,GACtB,IAAK,IAAIwF,EAAK,EAAGvK,EAAKlD,EAAOoC,QAASqL,EAAKvK,EAAG7b,OAAQomB,IAAM,CACxD,IAAIpK,EAAQH,EAAGuK,GACf0V,EAAkB9f,EAAM/X,QAAS,EAAG+X,GAiBxC,SAASsgB,EAAiBpM,GACtB,IAZoBhS,EAAO6d,EAYvB/f,EAAQkU,EAAI9O,MAAMpF,MACtB,OAAOA,EAAMogB,UAAYv8B,EAASA,EAAS,GAAIqwB,GAAM,CAAE9O,MAAO,CACtDpF,MAAOA,EACPkC,OAfYA,EAeUgS,EAAI9O,MAAMlD,MAfb6d,EAeoB/f,EAAM+f,QAd9C,CACHhqB,KAAqB,IAAfmM,EAAMnM,KACR,EACAmM,EAAMnM,KACVgG,MAAO0jB,GAAIvd,EAAMnG,MAAOmG,EAAMlG,UAAYya,EAAKN,QAAUM,EAAKP,QAAS6J,GACvE/jB,WAAW,EACXC,MAAOwjB,GAAIvd,EAAMjG,MAAOiG,EAAMhG,UAAYua,EAAKP,QAAUO,EAAKN,QAAS4J,GACvE7jB,WAAW,OAQJgY,EAoDf,OAlDarwB,EAASA,EAAS,GAAIwjB,GAAQ,CAAE1K,OAAQ9Y,EAASA,EAAS,GAAI8Y,GAAS,CAAEiI,WAAYA,EAAY7F,QAAS8gB,EAAmBhb,kBAtB1I,SAAuB5c,GACnB,IAAIJ,EAAS+3B,EAAY3M,GAAgBhrB,IACzC,OAAOJ,GAAUA,EAAO,MAoBmJyY,MAAO,SAAU4T,GACxL,OAAO7M,EAAM/G,MAAMggB,EAAiBpM,KACrC9O,MAAO,SAAU8O,GAChB,OAAO7M,EAAMjC,MAAMkb,EAAiBpM,KACrCnP,WAAY,SAAUmP,GACrB,IAAIrU,EAAKqU,EAAI9O,MAAMpF,MAAO+f,EAAUlgB,EAAGkgB,QAASK,EAAYvgB,EAAGugB,UAAWD,EAAYtgB,EAAGsgB,UACzF,IAAKC,EACD,OAAO/Y,EAAMtC,WAAWmP,GAwC5B,OAAO7M,EAAMtC,WAAWub,EAAiBpM,IACpCjlB,MAAK,SAAUwW,GAAU,OAAOA,GAxCrC,SAA6BA,GAqCzB,OA1BoBljB,OAAOY,OAAOsiB,EAAQ,CACtCS,SAAU,CAAEpjB,MAXhB,SAAmBM,GACR,MAAPA,EACIqiB,EAAOS,SAASuZ,GAAIr8B,EAAK8wB,EAAIpT,QAAU2V,EAAKN,QAAUM,EAAKP,QAAS6J,IACpE7L,EAAI/O,OACAM,EAAOS,SAAST,EAAOriB,IAAIqB,MAAM,EAAG07B,GAC/Bz7B,OAAOwvB,EAAIpT,QACV2V,EAAKP,QACLO,EAAKN,QAAS4J,IACpBta,EAAOS,aAIfsP,mBAAoB,CAChB1yB,MAAO,SAAUM,EAAKwhB,GAClBa,EAAO+P,mBAAmBiK,GAAIr8B,EAAKqzB,EAAKN,QAAS4J,GAAUnb,KAGnEA,WAAY,CACRliB,IAAK,WACD,OAAO+iB,EAAOb,aAGtBxhB,IAAK,CACDV,IAAK,WACD,IAAIU,EAAMqiB,EAAOriB,IACjB,OAAqB,IAAd+8B,EACH/8B,EAAI,GACJA,EAAIqB,MAAM,EAAG07B,KAGzBr9B,MAAO,CACHJ,IAAK,WACD,OAAO+iB,EAAO3iB,UAOiBy9B,CAAoB9a,cAYvF,SAAS+a,GAAcr3B,EAAGhC,EAAGe,EAAIu4B,GA+B7B,OA9BAv4B,EAAKA,GAAM,GACXu4B,EAAOA,GAAQ,GACfz7B,EAAKmE,GAAG9D,SAAQ,SAAUM,GACtB,GAAKD,EAAOyB,EAAGxB,GAGV,CACD,IAAI+6B,EAAKv3B,EAAExD,GAAOg7B,EAAKx5B,EAAExB,GACzB,GAAkB,iBAAP+6B,GAAiC,iBAAPC,GAAmBD,GAAMC,EAAI,CAC9D,IAAIC,EAAa/9B,EAAY69B,GAEzBE,IADa/9B,EAAY89B,GAEzBz4B,EAAGu4B,EAAO96B,GAAQwB,EAAExB,GAEA,WAAfi7B,EACLJ,GAAcE,EAAIC,EAAIz4B,EAAIu4B,EAAO96B,EAAO,KAEnC+6B,IAAOC,IACZz4B,EAAGu4B,EAAO96B,GAAQwB,EAAExB,SAGnB+6B,IAAOC,IACZz4B,EAAGu4B,EAAO96B,GAAQwB,EAAExB,SAlBxBuC,EAAGu4B,EAAO96B,QAAQ8C,KAqB1BzD,EAAKmC,GAAG9B,SAAQ,SAAUM,GACjBD,EAAOyD,EAAGxD,KACXuC,EAAGu4B,EAAO96B,GAAQwB,EAAExB,OAGrBuC,EASX,IAAI24B,GAAkB,CAClB11B,MAAO,SACP/I,KAAM,kBACNu9B,MAAO,EACPx8B,OAAQ,SAAU29B,GAAY,OAAQj9B,EAASA,EAAS,GAAIi9B,GAAW,CAAEzZ,MAAO,SAAU5K,GAClF,IAAIskB,EAAYD,EAASzZ,MAAM5K,GAC3BmI,EAAamc,EAAUpkB,OAAOiI,WA6GlC,OA5GsB/gB,EAASA,EAAS,GAAIk9B,GAAY,CAAEvf,OAAQ,SAAU0S,GACpE,IAAI8M,EAAUjvB,GAAIwK,MACdsD,EAAKmhB,EAAQ3Z,MAAM5K,GAAW8B,KAAM0iB,EAAWphB,EAAGohB,SAAUC,EAAWrhB,EAAGqhB,SAAUC,EAAWthB,EAAGshB,SACtG,OAAQjN,EAAIne,MACR,IAAK,MACD,GAAImrB,EAASziB,OAAS1Q,GAClB,MACJ,OAAOizB,EAAQ1sB,SAAS,aAAa,WAAc,OAAO8sB,EAAelN,MAAS,GACtF,IAAK,MACD,GAAIgN,EAASziB,OAAS1Q,IAAOozB,EAAS1iB,OAAS1Q,GAC3C,MACJ,OAAOizB,EAAQ1sB,SAAS,aAAa,WAAc,OAAO8sB,EAAelN,MAAS,GACtF,IAAK,SACD,GAAI+M,EAASxiB,OAAS1Q,GAClB,MACJ,OAAOizB,EAAQ1sB,SAAS,aAAa,WAAc,OAAO8sB,EAAelN,MAAS,GACtF,IAAK,cACD,GAAI+M,EAASxiB,OAAS1Q,GAClB,MACJ,OAAOizB,EAAQ1sB,SAAS,aAAa,WAAc,OAqE3D,SAAqB4f,GACjB,OAEJ,SAASmN,EAAgB9kB,EAAO2F,EAAO5L,GACnC,OAAOyqB,EAAU3b,MAAM,CAAE7I,MAAOA,EAAOtF,QAAQ,EAAOmO,MAAO,CAAEpF,MAAO4E,EAAY1C,MAAOA,GAAS5L,MAAOA,IACpGrH,MAAK,SAAU4Q,GAChB,IAAIhY,EAASgY,EAAGhY,OAChB,OAAOu5B,EAAe,CAAErrB,KAAM,SAAU/Q,KAAM6C,EAAQ0U,MAAOA,IAAStN,MAAK,SAAUT,GACjF,OAAIA,EAAIiT,YAAc,EACXnc,QAAQkO,OAAOhF,EAAIhC,SAAS,IACnC3E,EAAO7D,OAASsS,EACT,CAAE9J,SAAU,GAAIiV,YAAa,EAAGC,gBAAYjZ,GAG5C44B,EAAgB9kB,EAAO1Y,EAASA,EAAS,GAAIqe,GAAQ,CAAEnG,MAAOlU,EAAOA,EAAO7D,OAAS,GAAIgY,WAAW,IAAS1F,SAbzH+qB,CAAgBnN,EAAI3X,MAAO2X,EAAIhS,MAAO,KAtEiBof,CAAYpN,MAAS,GAEvF,OAAO6M,EAAUvf,OAAO0S,GACxB,SAASkN,EAAelN,GACpB,IAAI8M,EAAUjvB,GAAIwK,MACdvX,EAAOkvB,EAAIlvB,MArCvC,SAA0B4f,EAAYsP,GAClC,MAAiB,WAAbA,EAAIne,KACGme,EAAIlvB,KACRkvB,EAAIlvB,MAAQkvB,EAAIjd,OAAO3N,IAAIsb,EAAWmF,YAkCFwX,CAAiB3c,EAAYsP,GACpD,IAAKlvB,EACD,MAAM,IAAIoC,MAAM,gBAMpB,MAJiB,YADjB8sB,EAAmB,QAAbA,EAAIne,MAA+B,QAAbme,EAAIne,KAAiBlS,EAASA,EAAS,GAAIqwB,GAAM,CAAElvB,KAAMA,IAAUnB,EAAS,GAAIqwB,IACpGne,OACJme,EAAIjd,OAAS9S,EAAc,GAAI+vB,EAAIjd,QAAQ,IAC3Cid,EAAIlvB,OACJkvB,EAAIlvB,KAAOb,EAAc,GAAI+vB,EAAIlvB,MAAM,IAgFnE,SAA2BqiB,EAAO6M,EAAKsN,GACnC,MAAoB,QAAbtN,EAAIne,KACLzQ,QAAQgK,QAAQ,IAChB+X,EAAMjF,QAAQ,CAAE7F,MAAO2X,EAAI3X,MAAOvX,KAAMw8B,EAAelX,MAAO,cAlFzCmX,CAAkBV,EAAW7M,EAAKlvB,GAAMiK,MAAK,SAAUyyB,GAC1D,IAAIC,EAAW38B,EAAKsE,KAAI,SAAUlG,EAAKvB,GACnC,IAAI+/B,EAAgBF,EAAe7/B,GAC/BohB,EAAM,CAAEvU,QAAS,KAAMD,UAAW,MACtC,GAAiB,WAAbylB,EAAIne,KACJkrB,EAASxiB,KAAKzc,KAAKihB,EAAK7f,EAAKw+B,EAAeZ,QAE3C,GAAiB,QAAb9M,EAAIne,WAAoCtN,IAAlBm5B,EAA6B,CACxD,IAAIC,EAAsBX,EAASziB,KAAKzc,KAAKihB,EAAK7f,EAAK8wB,EAAIjd,OAAOpV,GAAIm/B,GAC3D,MAAP59B,GAAsC,MAAvBy+B,IACfz+B,EAAMy+B,EACN3N,EAAIlvB,KAAKnD,GAAKuB,EACTwhB,EAAWkF,UACZphB,EAAawrB,EAAIjd,OAAOpV,GAAI+iB,EAAW3c,QAAS7E,QAIvD,CACD,IAAI0+B,EAAatB,GAAcoB,EAAe1N,EAAIjd,OAAOpV,IACrDkgC,EAAsBZ,EAAS1iB,KAAKzc,KAAKihB,EAAK6e,EAAY1+B,EAAKw+B,EAAeZ,GAClF,GAAIe,EAAqB,CACrB,IAAIC,EAAmB9N,EAAIjd,OAAOpV,GAClCU,OAAOyC,KAAK+8B,GAAqB18B,SAAQ,SAAU4C,GAC3CvC,EAAOs8B,EAAkB/5B,GACzB+5B,EAAiB/5B,GAAW85B,EAAoB95B,GAGhDS,EAAas5B,EAAkB/5B,EAAS85B,EAAoB95B,QAK5E,OAAOgb,KAEX,OAAO8d,EAAUvf,OAAO0S,GAAKjlB,MAAK,SAAU4Q,GAExC,IADA,IAAIrT,EAAWqT,EAAGrT,SAAUuL,EAAU8H,EAAG9H,QAAS0J,EAAc5B,EAAG4B,YAAaC,EAAa7B,EAAG6B,WACvF7f,EAAI,EAAGA,EAAImD,EAAKhB,SAAUnC,EAAG,CAClC,IAAImd,EAAUjH,EAAUA,EAAQlW,GAAKmD,EAAKnD,GACtCohB,EAAM0e,EAAS9/B,GACJ,MAAXmd,EACAiE,EAAIvU,SAAWuU,EAAIvU,QAAQlC,EAAS3K,IAGpCohB,EAAIxU,WAAawU,EAAIxU,UAAuB,QAAbylB,EAAIne,MAAkB2rB,EAAe7/B,GAChEqyB,EAAIjd,OAAOpV,GACXmd,GAIZ,MAAO,CAAExS,SAAUA,EAAUuL,QAASA,EAAS0J,YAAaA,EAAaC,WAAYA,MACtF5L,OAAM,SAAUsR,GAEf,OADAua,EAASt8B,SAAQ,SAAU4d,GAAO,OAAOA,EAAIvU,SAAWuU,EAAIvU,QAAQ0Y,MAC7D9hB,QAAQkO,OAAO4T,kBAiCtD,SAAS6a,GAAwBj9B,EAAMslB,EAAO9C,GAC1C,IACI,IAAK8C,EACD,OAAO,KACX,GAAIA,EAAMtlB,KAAKhB,OAASgB,EAAKhB,OACzB,OAAO,KAEX,IADA,IAAI6D,EAAS,GACJhG,EAAI,EAAG24B,EAAI,EAAG34B,EAAIyoB,EAAMtlB,KAAKhB,QAAUw2B,EAAIx1B,EAAKhB,SAAUnC,EAC3B,IAAhC+d,GAAI0K,EAAMtlB,KAAKnD,GAAImD,EAAKw1B,MAE5B3yB,EAAOO,KAAKof,EAAQ5d,EAAU0gB,EAAMrT,OAAOpV,IAAMyoB,EAAMrT,OAAOpV,MAC5D24B,GAEN,OAAO3yB,EAAO7D,SAAWgB,EAAKhB,OAAS6D,EAAS,KAEpD,MAAOgY,GACH,OAAO,MAGf,IAgCIA,GAhCAqiB,GAAgC,CAChC/2B,MAAO,SACPw0B,OAAQ,EACRx8B,OAAQ,SAAUmb,GACd,MAAO,CACH+I,MAAO,SAAU5K,GACb,IAAI4K,EAAQ/I,EAAK+I,MAAM5K,GACvB,OAAO5Y,EAASA,EAAS,GAAIwjB,GAAQ,CAAEjF,QAAS,SAAU8R,GAClD,IAAKA,EAAI5J,MACL,OAAOjD,EAAMjF,QAAQ8R,GAEzB,IAAIiO,EAAeF,GAAwB/N,EAAIlvB,KAAMkvB,EAAI3X,MAAc,OAAiB,UAAd2X,EAAI5J,OAC9E,OAAI6X,EACOjwB,GAAa5C,QAAQ6yB,GAEzB9a,EAAMjF,QAAQ8R,GAAKjlB,MAAK,SAAUT,GAKrC,OAJA0lB,EAAI3X,MAAc,OAAI,CAClBvX,KAAMkvB,EAAIlvB,KACViS,OAAsB,UAAdid,EAAI5J,MAAoB1gB,EAAU4E,GAAOA,GAE9CA,MAEZgT,OAAQ,SAAU0S,GAGjB,MAFiB,QAAbA,EAAIne,OACJme,EAAI3X,MAAc,OAAI,MACnB8K,EAAM7F,OAAO0S,UAQ5C,SAASkO,GAAaC,GAClB,QAAS,SAAUA,GAEvB,IAAIC,GAAW,SAAUC,EAAYn+B,GACjC,IAAIF,KAGC,CACD,IAAIgE,EAAK,IAAIo6B,GAIb,OAHIC,GAAe,MAAOA,GACtBr9B,EAAOgD,EAAIq6B,GAERr6B,EAPPhD,EAAOhB,KAAMH,UAAUC,OAAS,CAAE7B,EAAG,EAAGkC,KAAMk+B,EAAYn+B,GAAIL,UAAUC,OAAS,EAAII,EAAKm+B,GAAe,CAAEpgC,EAAG,KA6BtH,SAASqgC,GAAS5S,EAAQvrB,EAAMD,GAC5B,IAAIq0B,EAAO7Y,GAAIvb,EAAMD,GACrB,IAAI0E,MAAM2vB,GAAV,CAEA,GAAIA,EAAO,EACP,MAAM7qB,aACV,GAAIw0B,GAAaxS,GACb,OAAO1qB,EAAO0qB,EAAQ,CAAEvrB,KAAMA,EAAMD,GAAIA,EAAIjC,EAAG,IACnD,IAAIsgC,EAAO7S,EAAO9tB,EACd4gC,EAAQ9S,EAAOjtB,EACnB,GAAIid,GAAIxb,EAAIwrB,EAAOvrB,MAAQ,EAIvB,OAHAo+B,EACMD,GAASC,EAAMp+B,EAAMD,GACpBwrB,EAAO9tB,EAAI,CAAEuC,KAAMA,EAAMD,GAAIA,EAAIjC,EAAG,EAAGL,EAAG,KAAMa,EAAG,MACnDggC,GAAU/S,GAErB,GAAIhQ,GAAIvb,EAAMurB,EAAOxrB,IAAM,EAIvB,OAHAs+B,EACMF,GAASE,EAAOr+B,EAAMD,GACrBwrB,EAAOjtB,EAAI,CAAE0B,KAAMA,EAAMD,GAAIA,EAAIjC,EAAG,EAAGL,EAAG,KAAMa,EAAG,MACnDggC,GAAU/S,GAEjBhQ,GAAIvb,EAAMurB,EAAOvrB,MAAQ,IACzBurB,EAAOvrB,KAAOA,EACdurB,EAAO9tB,EAAI,KACX8tB,EAAOztB,EAAIugC,EAAQA,EAAMvgC,EAAI,EAAI,GAEjCyd,GAAIxb,EAAIwrB,EAAOxrB,IAAM,IACrBwrB,EAAOxrB,GAAKA,EACZwrB,EAAOjtB,EAAI,KACXitB,EAAOztB,EAAIytB,EAAO9tB,EAAI8tB,EAAO9tB,EAAEK,EAAI,EAAI,GAE3C,IAAIygC,GAAkBhT,EAAOjtB,EACzB8/B,IAAS7S,EAAO9tB,GAChB+gC,GAAYjT,EAAQ6S,GAEpBC,GAASE,GACTC,GAAYjT,EAAQ8S,IAG5B,SAASG,GAAYjT,EAAQkT,GASpBV,GAAaU,IARlB,SAASC,EAAanT,EAAQ/P,GAC1B,IAAIxb,EAAOwb,EAAGxb,KAAMD,EAAKyb,EAAGzb,GAAItC,EAAI+d,EAAG/d,EAAGa,EAAIkd,EAAGld,EACjD6/B,GAAS5S,EAAQvrB,EAAMD,GACnBtC,GACAihC,EAAanT,EAAQ9tB,GACrBa,GACAogC,EAAanT,EAAQjtB,GAGzBogC,CAAanT,EAAQkT,GAoB7B,SAASE,GAAoBX,GACzB,IAAI3F,EAAQ0F,GAAaC,GAAQ,KAAO,CAAEz+B,EAAG,EAAGN,EAAG++B,GACnD,MAAO,CACH53B,KAAM,SAAUrH,GAEZ,IADA,IAAI6/B,EAAcl/B,UAAUC,OAAS,EAC9B04B,GACH,OAAQA,EAAM94B,GACV,KAAK,EAED,GADA84B,EAAM94B,EAAI,EACNq/B,EACA,KAAOvG,EAAMp5B,EAAExB,GAAK8d,GAAIxc,EAAKs5B,EAAMp5B,EAAEe,MAAQ,GACzCq4B,EAAQ,CAAEwG,GAAIxG,EAAOp5B,EAAGo5B,EAAMp5B,EAAExB,EAAG8B,EAAG,QAG1C,KAAO84B,EAAMp5B,EAAExB,GACX46B,EAAQ,CAAEwG,GAAIxG,EAAOp5B,EAAGo5B,EAAMp5B,EAAExB,EAAG8B,EAAG,GAElD,KAAK,EAED,GADA84B,EAAM94B,EAAI,GACLq/B,GAAerjB,GAAIxc,EAAKs5B,EAAMp5B,EAAEc,KAAO,EACxC,MAAO,CAAEtB,MAAO45B,EAAMp5B,EAAGoH,MAAM,GACvC,KAAK,EACD,GAAIgyB,EAAMp5B,EAAEX,EAAG,CACX+5B,EAAM94B,EAAI,EACV84B,EAAQ,CAAEwG,GAAIxG,EAAOp5B,EAAGo5B,EAAMp5B,EAAEX,EAAGiB,EAAG,GACtC,SAER,KAAK,EACD84B,EAAQA,EAAMwG,GAG1B,MAAO,CAAEx4B,MAAM,KAI3B,SAASi4B,GAAU/S,GACf,IAAI/P,EAAIsjB,EACJ1K,IAA6B,QAAnB5Y,EAAK+P,EAAOjtB,SAAsB,IAAPkd,OAAgB,EAASA,EAAG1d,IAAM,KAA2B,QAAnBghC,EAAKvT,EAAO9tB,SAAsB,IAAPqhC,OAAgB,EAASA,EAAGhhC,IAAM,GAC5IQ,EAAI81B,EAAO,EAAI,IAAMA,GAAQ,EAAI,IAAM,GAC3C,GAAI91B,EAAG,CACH,IAAIb,EAAU,MAANa,EAAY,IAAM,IACtBygC,EAAYv/B,EAAS,GAAI+rB,GACzByT,EAAezT,EAAOjtB,GAC1BitB,EAAOvrB,KAAOg/B,EAAah/B,KAC3BurB,EAAOxrB,GAAKi/B,EAAaj/B,GACzBwrB,EAAOjtB,GAAK0gC,EAAa1gC,GACzBygC,EAAUzgC,GAAK0gC,EAAavhC,GAC5B8tB,EAAO9tB,GAAKshC,EACZA,EAAUjhC,EAAImhC,GAAaF,GAE/BxT,EAAOztB,EAAImhC,GAAa1T,GAE5B,SAAS0T,GAAazjB,GAClB,IAAIld,EAAIkd,EAAGld,EAAGb,EAAI+d,EAAG/d,EACrB,OAAQa,EAAKb,EAAI4lB,KAAKqH,IAAIpsB,EAAER,EAAGL,EAAEK,GAAKQ,EAAER,EAAKL,EAAIA,EAAEK,EAAI,GAAK,EA/IhEyD,EAAM08B,GAAS7+B,YAAYoc,GAAK,CACxBwB,IAAK,SAAUkiB,GAEX,OADAV,GAAY3+B,KAAMq/B,GACXr/B,MAEXs/B,OAAQ,SAAUpgC,GAEd,OADAo/B,GAASt+B,KAAMd,EAAKA,GACbc,MAEXu/B,QAAS,SAAUz+B,GACf,IAAIoO,EAAQlP,KAEZ,OADAc,EAAKK,SAAQ,SAAUjC,GAAO,OAAOo/B,GAASpvB,EAAOhQ,EAAKA,MACnDc,QAGZgG,GAAkB,WACjB,OAAO84B,GAAoB9+B,OAE/B2b,KAgIJ,IAAI6jB,GAA0B,CAC1Bv4B,MAAO,SACPw0B,MAAO,EACPx8B,OAAQ,SAAUmb,GACd,IAAI2e,EAAS3e,EAAK3B,OAAOva,KACrBuhC,EAAa,IAAIrB,GAAShkB,EAAK4X,QAAS5X,EAAK6X,SACjD,OAAOtyB,EAASA,EAAS,GAAIya,GAAO,CAAE+I,MAAO,SAAU5K,GAC/C,IAAI4K,EAAQ/I,EAAK+I,MAAM5K,GACnBE,EAAS0K,EAAM1K,OACfiI,EAAajI,EAAOiI,WACpBmF,EAAanF,EAAWmF,WAAYD,EAAWlF,EAAWkF,SAC1D8Z,EAAa//B,EAASA,EAAS,GAAIwjB,GAAQ,CAAE7F,OAAQ,SAAU0S,GAC3D,IAAI3X,EAAQ2X,EAAI3X,MACZsnB,EAAetnB,EAAMsnB,eAAiBtnB,EAAMsnB,aAAe,IAC3DC,EAAc,SAAUhb,GACxB,IAAIib,EAAO,SAAW9G,EAAS,IAAMxgB,EAAY,IAAMqM,EACvD,OAAQ+a,EAAaE,KAChBF,EAAaE,GAAQ,IAAIzB,KAE9B0B,EAAaF,EAAY,IACzBG,EAAeH,EAAY,SAC3B/tB,EAAOme,EAAIne,KACX8J,EAAkB,gBAAbqU,EAAIne,KACP,CAACme,EAAIhS,OACQ,WAAbgS,EAAIne,KACA,CAACme,EAAIlvB,MACLkvB,EAAIjd,OAAOjT,OAAS,GAChB,CAAC,GAAIkwB,EAAIjd,QACT,GAAIjS,EAAO6a,EAAG,GAAIqkB,EAAUrkB,EAAG,GACzCskB,EAAWjQ,EAAI3X,MAAc,OACjC,OAAO8K,EAAM7F,OAAO0S,GAAKjlB,MAAK,SAAUT,GACpC,GAAIvJ,EAAQD,GAAO,CACF,WAAT+Q,IACA/Q,EAAOwJ,EAAIuJ,SACfisB,EAAWP,QAAQz+B,GACnB,IAAIo/B,EAAUnC,GAAwBj9B,EAAMm/B,GACvCC,GAAoB,QAATruB,GACZkuB,EAAaR,QAAQz+B,IAErBo/B,GAAWF,IA+G/C,SAA8BJ,EAAannB,EAAQynB,EAASF,GAoBxDvnB,EAAOoC,QAAQ1Z,SAnBf,SAA0B4Z,GACtB,IAAIskB,EAAWO,EAAY7kB,EAAG7c,MAAQ,IACtC,SAAS2nB,EAAW5kB,GAChB,OAAc,MAAPA,EAAc8Z,EAAG8K,WAAW5kB,GAAO,KAE9C,IAAIk/B,EAAe,SAAUjhC,GAAO,OAAO6b,EAAG6U,YAAc7uB,EAAQ7B,GAC9DA,EAAIiC,SAAQ,SAAUjC,GAAO,OAAOmgC,EAASC,OAAOpgC,MACpDmgC,EAASC,OAAOpgC,KACrBghC,GAAWF,GAAS7+B,SAAQ,SAAUuV,EAAG/Y,GACtC,IAAIyiC,EAASF,GAAWra,EAAWqa,EAAQviC,IACvC0iC,EAASL,GAAWna,EAAWma,EAAQriC,IACf,IAAxB+d,GAAI0kB,EAAQC,KACE,MAAVD,GACAD,EAAaC,GACH,MAAVC,GACAF,EAAaE,UA9HGC,CAAqBV,EAAannB,EAAQynB,EAASF,QAGtD,GAAIl/B,EAAM,CACX,IAAIkd,EAAQ,CAAE7d,KAAMW,EAAK+W,MAAO3X,GAAIY,EAAKiX,OACzCgoB,EAAa5iB,IAAIa,GACjB8hB,EAAW3iB,IAAIa,QAGf8hB,EAAW3iB,IAAIsiB,GACfM,EAAa5iB,IAAIsiB,GACjBhnB,EAAOoC,QAAQ1Z,SAAQ,SAAU6a,GAAO,OAAO4jB,EAAY5jB,EAAI9d,MAAMif,IAAIsiB,MAE7E,OAAOn1B,QAGfi2B,EAAW,SAAU5kB,GACrB,IAAIsjB,EAAIuB,EACJC,EAAK9kB,EAAGuF,MAAOpF,EAAQ2kB,EAAG3kB,MAAOkC,EAAQyiB,EAAGziB,MAChD,MAAO,CACHlC,EACA,IAAIsiB,GAAgC,QAAtBa,EAAKjhB,EAAMnG,aAA0B,IAAPonB,EAAgBA,EAAK7kB,EAAK4X,QAAgC,QAAtBwO,EAAKxiB,EAAMjG,aAA0B,IAAPyoB,EAAgBA,EAAKpmB,EAAK6X,WAG5IyO,EAAkB,CAClBliC,IAAK,SAAUwxB,GAAO,MAAO,CAACtP,EAAY,IAAI0d,GAASpO,EAAI9wB,OAC3Dgf,QAAS,SAAU8R,GAAO,MAAO,CAACtP,GAAY,IAAI0d,IAAWmB,QAAQvP,EAAIlvB,QACzEsb,MAAOmkB,EACPrf,MAAOqf,EACP1f,WAAY0f,GA6EhB,OA3EAz/B,EAAK4/B,GAAiBv/B,SAAQ,SAAUw/B,GACpCjB,EAAWiB,GAAU,SAAU3Q,GAC3B,IAAI4Q,EAAS/yB,GAAI+yB,OACjB,GAAIA,EAAQ,CACR,IAAIhB,EAAc,SAAUhb,GACxB,IAAIib,EAAO,SAAW9G,EAAS,IAAMxgB,EAAY,IAAMqM,EACvD,OAAQgc,EAAOf,KACVe,EAAOf,GAAQ,IAAIzB,KAExByC,EAAejB,EAAY,IAC3BkB,EAAiBlB,EAAY,SAC7BjkB,EAAK+kB,EAAgBC,GAAQ3Q,GAAM+Q,EAAeplB,EAAG,GAAIqlB,EAAgBrlB,EAAG,GAEhF,GADAikB,EAAYmB,EAAa7iC,MAAQ,IAAIif,IAAI6jB,IACpCD,EAAarR,aAAc,CAC5B,GAAe,UAAXiR,EAGC,CACD,IAAIM,EAA2B,UAAXN,GAChB/a,GACAoK,EAAIjd,QACJoQ,EAAMjC,MAAMvhB,EAASA,EAAS,GAAIqwB,GAAM,CAAEjd,QAAQ,KACtD,OAAOoQ,EAAMwd,GAAQ5gC,MAAMC,KAAMH,WAAWkL,MAAK,SAAUT,GACvD,GAAe,UAAXq2B,EAAoB,CACpB,GAAI/a,GAAYoK,EAAIjd,OAChB,OAAOkuB,EAAcl2B,MAAK,SAAU4Q,GAChC,IAAIulB,EAAgBvlB,EAAGhY,OAEvB,OADAk9B,EAAatB,QAAQ2B,GACd52B,KAGf,IAAI62B,EAAQnR,EAAIjd,OACVzI,EAAI3G,OAAOyB,IAAIygB,GACfvb,EAAI3G,OACNqsB,EAAIjd,OACJ8tB,EAAatB,QAAQ4B,GAGrBL,EAAevB,QAAQ4B,QAG1B,GAAe,eAAXR,EAAyB,CAC9B,IAAIS,EAAW92B,EACX+2B,EAAerR,EAAIjd,OACvB,OAAQquB,GACJ/iC,OAAOY,OAAOmiC,EAAU,CACpBliC,IAAK,CACDV,IAAK,WAED,OADAsiC,EAAexB,OAAO8B,EAAS1gB,YACxB0gB,EAASliC,MAGxBwhB,WAAY,CACRliB,IAAK,WACD,IAAI8iC,EAAOF,EAAS1gB,WAEpB,OADAogB,EAAexB,OAAOgC,GACfA,IAGf1iC,MAAO,CACHJ,IAAK,WAED,OADA6iC,GAAgBR,EAAavB,OAAO8B,EAAS1gB,YACtC0gB,EAASxiC,UAKpC,OAAO0L,KApDXw2B,EAAe3jB,IAAIsiB,IAyD/B,OAAOtc,EAAMwd,GAAQ5gC,MAAMC,KAAMH,eAGlC6/B,OA2BvB,IA6ZI6B,GA7ZA7J,GAAY,WACZ,SAAS8J,EAAMtjC,EAAM8D,GACjB,IAAIkN,EAAQlP,KACZA,KAAK2yB,aAAe,GACpB3yB,KAAKk5B,MAAQ,EACb,IAAIuI,EAAOD,EAAME,aACjB1hC,KAAK8Z,SAAW9X,EAAUrC,EAAS,CAC/Bg4B,OAAQ6J,EAAM7J,OAAQ5d,UAAU,EAChC0B,UAAWgmB,EAAKhmB,UAAW2W,YAAaqP,EAAKrP,aAAepwB,GAChEhC,KAAKwb,MAAQ,CACTC,UAAWzZ,EAAQyZ,UACnB2W,YAAapwB,EAAQowB,aAEzB,IAAIuF,EAAS31B,EAAQ21B,OACrB33B,KAAKqZ,UAAY,GACjBrZ,KAAKg0B,UAAY,GACjBh0B,KAAK0zB,YAAc,GACnB1zB,KAAKq3B,WAAa,GAClBr3B,KAAKgZ,MAAQ,KACbhZ,KAAK0yB,OAAS1yB,KACd,IA3yE6B4Y,EA2yEzB4f,EAAQ,CACR5e,YAAa,KACbC,eAAe,EACfugB,kBAAmB,KACnBnhB,cAAc,EACd2f,eAAgB/uB,GAChBmQ,eAAgB,KAChB2nB,WAAY93B,GACZ4uB,cAAe,KACfO,YAAY,EACZ1f,eAAgB,GAEpBkf,EAAMxe,eAAiB,IAAIhM,IAAa,SAAU5C,GAC9CotB,EAAMI,eAAiBxtB,KAE3BotB,EAAMC,cAAgB,IAAIzqB,IAAa,SAAU0I,EAAGpH,GAChDkpB,EAAMmJ,WAAaryB,KAEvBtP,KAAKyO,OAAS+pB,EACdx4B,KAAK9B,KAAOA,EACZ8B,KAAK8sB,GAAKhO,GAAO9e,KAAM,WAAY,UAAW,gBAAiB,QAAS,CAAEq6B,MAAO,CAACvvB,GAAiBjB,MACnG7J,KAAK8sB,GAAGuN,MAAMlb,UAAYtc,EAAS7C,KAAK8sB,GAAGuN,MAAMlb,WAAW,SAAUA,GAClE,OAAO,SAAUD,EAAY0iB,GACzBJ,EAAMxJ,KAAI,WACN,IAAIQ,EAAQtpB,EAAMT,OAClB,GAAI+pB,EAAMvf,aACDuf,EAAM5e,aACP5L,GAAa5C,UAAUL,KAAKmU,GAC5B0iB,GACAziB,EAAUD,QAEb,GAAIsZ,EAAM4B,kBACX5B,EAAM4B,kBAAkBl2B,KAAKgb,GACzB0iB,GACAziB,EAAUD,OAEb,CACDC,EAAUD,GACV,IAAI2iB,EAAO3yB,EACN0yB,GACDziB,GAAU,SAASnC,IACf6kB,EAAK/U,GAAGuN,MAAMrd,YAAYkC,GAC1B2iB,EAAK/U,GAAGuN,MAAMrd,YAAYA,cAMlDhd,KAAK0c,YA31EwB9D,EA21EiB5Y,KA11E3C2f,GAAqBjD,GAAWnd,WAAW,SAAoB8nB,EAAaya,GAC/E9hC,KAAK4Y,GAAKA,EACV,IAAImpB,EAAWnqB,GAAUsL,EAAQ,KACjC,GAAI4e,EACA,IACIC,EAAWD,IAEf,MAAO5xB,GACHgT,EAAQhT,EAEhB,IAAI8xB,EAAW3a,EAAYpE,KACvBE,EAAQ6e,EAAS7e,MACjB8e,EAAc9e,EAAM9I,KAAKC,QAAQC,KACrCva,KAAKijB,KAAO,CACRE,MAAOA,EACPrH,MAAOkmB,EAASlmB,MAChB2E,WAAauhB,EAASlmB,OAAUqH,EAAM1K,OAAOqC,QAAQ/W,SAAWi+B,EAASlmB,QAAUqH,EAAM1K,OAAOqC,QAAQ5c,KACxG8f,MAAO+jB,EACPhhB,UAAU,EACVC,IAAK,OACLC,OAAQ,GACRnB,UAAW,KACXxa,OAAQ,KACR2a,aAAc,KACdD,WAAW,EACX2E,QAAS,KACTrI,OAAQ,EACRlK,MAAOK,IACPyQ,MAAOA,EACPnD,GAAIiiB,EAASjiB,GACb8B,YAAaogB,IAAgBn4B,GAASm4B,EAAc,UA6zExDjiC,KAAKkY,MA54Fb,SAAgCU,GAC5B,OAAO+G,GAAqBzH,GAAM3Y,WAAW,SAAerB,EAAMgwB,EAAa7V,GAC3ErY,KAAK4Y,GAAKA,EACV5Y,KAAKsY,IAAMD,EACXrY,KAAK9B,KAAOA,EACZ8B,KAAKyY,OAASyV,EACdluB,KAAKqa,KAAOzB,EAAGye,WAAWn5B,GAAQ0a,EAAGye,WAAWn5B,GAAMmc,KAAOyE,GAAO,KAAM,CACtE,SAAY,CAACzU,GAAmBR,IAChC,QAAW,CAACE,GAAmBD,IAC/B,SAAY,CAACa,GAAmBd,IAChC,SAAY,CAACa,GAAmBb,SAk4FvBq4B,CAAuBliC,MACpCA,KAAK6rB,YAnxDb,SAAsCjT,GAClC,OAAO+G,GAAqBkM,GAAYtsB,WAAW,SAAqBT,EAAMia,EAAYia,EAAUtG,EAA6BjY,GAC7H,IAAIvF,EAAQlP,KACZA,KAAK4Y,GAAKA,EACV5Y,KAAKlB,KAAOA,EACZkB,KAAK+Y,WAAaA,EAClB/Y,KAAKyY,OAASua,EACdhzB,KAAK0sB,4BAA8BA,EACnC1sB,KAAK2Y,SAAW,KAChB3Y,KAAK8sB,GAAKhO,GAAO9e,KAAM,WAAY,QAAS,SAC5CA,KAAKyU,OAASA,GAAU,KACxBzU,KAAKusB,QAAS,EACdvsB,KAAK+rB,UAAY,EACjB/rB,KAAKksB,cAAgB,GACrBlsB,KAAKgtB,SAAW,KAChBhtB,KAAK4sB,QAAU,KACf5sB,KAAKwtB,YAAc,KACnBxtB,KAAKytB,cAAgB,KACrBztB,KAAK6tB,WAAa,EAClB7tB,KAAK2Z,YAAc,IAAI3L,IAAa,SAAU5C,EAASkE,GACnDJ,EAAM8d,SAAW5hB,EACjB8D,EAAM0d,QAAUtd,KAEpBtP,KAAK2Z,YAAY5O,MAAK,WAClBmE,EAAMqd,QAAS,EACfrd,EAAM4d,GAAGqV,SAAS5nB,UACnB,SAAUpT,GACT,IAAIi7B,EAAYlzB,EAAMqd,OAMtB,OALArd,EAAMqd,QAAS,EACfrd,EAAM4d,GAAG5J,MAAM3I,KAAKpT,GACpB+H,EAAMuF,OACFvF,EAAMuF,OAAOmY,QAAQzlB,GACrBi7B,GAAalzB,EAAMyJ,UAAYzJ,EAAMyJ,SAASoV,QAC3C9Y,GAAU9N,SAkvDFk7B,CAA6BriC,MAChDA,KAAK42B,QA7+Bb,SAAkChe,GAC9B,OAAO+G,GAAqBiX,GAAQr3B,WAAW,SAAiB+iC,GAC5DtiC,KAAK4Y,GAAKA,EACV5Y,KAAKozB,KAAO,CACRC,QAASiP,EACTnL,aAAc,KACdnE,SAAU,GACV1D,OAAQ,GACR4F,eAAgB,SAq+BLqN,CAAyBviC,MACxCA,KAAKya,YA39Db,SAAsC7B,GAClC,OAAO+G,GAAqBlF,GAAYlb,WAAW,SAAqB4jB,EAAOrH,EAAO0mB,GAClFxiC,KAAK4Y,GAAKA,EACV5Y,KAAKijB,KAAO,CACRE,MAAOA,EACPrH,MAAiB,QAAVA,EAAkB,KAAOA,EAChCiE,GAAIyiB,GAER,IAAI/mB,EAAY7C,EAAG4C,MAAMC,UACzB,IAAKA,EACD,MAAM,IAAIxS,GAAWhB,WACzBjI,KAAKspB,KAAOtpB,KAAKkqB,WAAazO,EAAUC,IAAIvc,KAAKsc,GACjDzb,KAAKmqB,YAAc,SAAUllB,EAAGhC,GAAK,OAAOwY,EAAUC,IAAIzY,EAAGgC,IAC7DjF,KAAK8qB,KAAO,SAAU7lB,EAAGhC,GAAK,OAAOwY,EAAUC,IAAIzW,EAAGhC,GAAK,EAAIgC,EAAIhC,GACnEjD,KAAK4qB,KAAO,SAAU3lB,EAAGhC,GAAK,OAAOwY,EAAUC,IAAIzW,EAAGhC,GAAK,EAAIgC,EAAIhC,GACnEjD,KAAKyiC,aAAe7pB,EAAG4C,MAAM4W,eA48DVsQ,CAA6B1iC,MAChDA,KAAK8sB,GAAG,iBAAiB,SAAUH,GAC3BA,EAAGgW,WAAa,EAChB/rB,QAAQC,KAAK,iDAAmD3H,EAAMhR,KAAO,4CAE7E0Y,QAAQC,KAAK,gDAAkD3H,EAAMhR,KAAO,mDAChFgR,EAAMqqB,WAEVv5B,KAAK8sB,GAAG,WAAW,SAAUH,IACpBA,EAAGgW,YAAchW,EAAGgW,WAAahW,EAAG4G,WACrC3c,QAAQC,KAAK,iBAAmB3H,EAAMhR,KAAO,kBAE7C0Y,QAAQC,KAAK,YAAc3H,EAAMhR,KAAO,iDAAmDyuB,EAAG4G,WAAa,OAEnHvzB,KAAKkb,QAAUsT,GAAUxsB,EAAQowB,aACjCpyB,KAAKoZ,mBAAqB,SAAUta,EAAMia,EAAYia,EAAUmI,GAAqB,OAAO,IAAIjsB,EAAM2c,YAAY/sB,EAAMia,EAAYia,EAAU9jB,EAAM4K,SAAS4S,4BAA6ByO,IAC1Ln7B,KAAKo5B,eAAiB,SAAUzM,GAC5Bzd,EAAM4d,GAAG,WAAWvS,KAAKoS,GACzBzV,GACK5R,QAAO,SAAUtH,GAAK,OAAOA,EAAEE,OAASgR,EAAMhR,MAAQF,IAAMkR,IAAUlR,EAAEyQ,OAAOwrB,WAC/E70B,KAAI,SAAUpH,GAAK,OAAOA,EAAE8uB,GAAG,iBAAiBvS,KAAKoS,OAE9D3sB,KAAK4iC,IAAIpH,IACTx7B,KAAK4iC,IAAIjG,IACT38B,KAAK4iC,IAAIpD,IACTx/B,KAAK4iC,IAAI5E,IACTh+B,KAAKg4B,IAAM35B,OAAOY,OAAOe,KAAM,CAAEmZ,KAAM,CAAEva,OAAO,KAChD+4B,EAAOx2B,SAAQ,SAAU0hC,GAAS,OAAOA,EAAM3zB,MA2MnD,OAzMAsyB,EAAMjiC,UAAU8zB,QAAU,SAAUiP,GAChC,GAAI19B,MAAM09B,IAAkBA,EAAgB,GACxC,MAAM,IAAIr5B,GAAWM,KAAK,0CAE9B,GADA+4B,EAAgB9e,KAAKyV,MAAsB,GAAhBqJ,GAAsB,GAC7CtiC,KAAKgZ,OAAShZ,KAAKyO,OAAOoL,cAC1B,MAAM,IAAI5Q,GAAW2X,OAAO,4CAChC5gB,KAAKk5B,MAAQ1V,KAAKqH,IAAI7qB,KAAKk5B,MAAOoJ,GAClC,IAAIvO,EAAW/zB,KAAKg0B,UAChB8O,EAAkB/O,EAASzuB,QAAO,SAAUiD,GAAK,OAAOA,EAAE6qB,KAAKC,UAAYiP,KAAkB,GACjG,OAAIQ,IAEJA,EAAkB,IAAI9iC,KAAK42B,QAAQ0L,GACnCvO,EAAS7vB,KAAK4+B,GACd/O,EAAS5P,KAAKgP,IACd2P,EAAgBhM,OAAO,IACvB92B,KAAKyO,OAAOuqB,YAAa,EAClB8J,IAEXtB,EAAMjiC,UAAUwjC,WAAa,SAAU3/B,GACnC,IAAI8L,EAAQlP,KACZ,OAAQA,KAAKgZ,QAAUhZ,KAAKyO,OAAOwK,cAAgBpL,GAAIqL,YAAclZ,KAAKmZ,MAAS/V,IAAO,IAAI4K,IAAa,SAAU5C,EAASkE,GAC1H,GAAIJ,EAAMT,OAAOwK,aACb,OAAO3J,EAAO,IAAIrG,GAAWnB,eAAeoH,EAAMT,OAAOmL,cAE7D,IAAK1K,EAAMT,OAAOoL,cAAe,CAC7B,IAAK3K,EAAM4K,SAASC,SAEhB,YADAzK,EAAO,IAAIrG,GAAWnB,gBAG1BoH,EAAMwK,OAAO9H,MAAM/H,IAEvBqF,EAAMT,OAAOuL,eAAejP,KAAKK,EAASkE,MAC3CvE,KAAK3H,IAEZo+B,EAAMjiC,UAAUqjC,IAAM,SAAUjnB,GAC5B,IAAI1U,EAAQ0U,EAAG1U,MAAOhI,EAAS0c,EAAG1c,OAAQw8B,EAAQ9f,EAAG8f,MAAOv9B,EAAOyd,EAAGzd,KAClEA,GACA8B,KAAKgjC,MAAM,CAAE/7B,MAAOA,EAAO/I,KAAMA,IACrC,IAAIi0B,EAAcnyB,KAAK2yB,aAAa1rB,KAAWjH,KAAK2yB,aAAa1rB,GAAS,IAG1E,OAFAkrB,EAAYjuB,KAAK,CAAE+C,MAAOA,EAAOhI,OAAQA,EAAQw8B,MAAgB,MAATA,EAAgB,GAAKA,EAAOv9B,KAAMA,IAC1Fi0B,EAAYhO,MAAK,SAAUlf,EAAGhC,GAAK,OAAOgC,EAAEw2B,MAAQx4B,EAAEw4B,SAC/Cz7B,MAEXwhC,EAAMjiC,UAAUyjC,MAAQ,SAAUrnB,GAC9B,IAAI1U,EAAQ0U,EAAG1U,MAAO/I,EAAOyd,EAAGzd,KAAMe,EAAS0c,EAAG1c,OAQlD,OAPIgI,GAASjH,KAAK2yB,aAAa1rB,KAC3BjH,KAAK2yB,aAAa1rB,GAASjH,KAAK2yB,aAAa1rB,GAAO3B,QAAO,SAAU29B,GACjE,OAAOhkC,EAASgkC,EAAGhkC,SAAWA,IAC1Bf,GAAO+kC,EAAG/kC,OAASA,MAIxB8B,MAEXwhC,EAAMjiC,UAAUma,KAAO,WACnB,OAAO6e,GAAUv4B,OAErBwhC,EAAMjiC,UAAUka,OAAS,WACrB,IAAI+e,EAAQx4B,KAAKyO,OACbuN,EAAM9E,GAAY9S,QAAQpE,MAG9B,GAFIgc,GAAO,GACP9E,GAAYpS,OAAOkX,EAAK,GACxBhc,KAAKgZ,MAAO,CACZ,IACIhZ,KAAKgZ,MAAMugB,QAEf,MAAOpyB,IACPnH,KAAK0yB,OAAO1Z,MAAQ,KAExBwf,EAAMxe,eAAiB,IAAIhM,IAAa,SAAU5C,GAC9CotB,EAAMI,eAAiBxtB,KAE3BotB,EAAMC,cAAgB,IAAIzqB,IAAa,SAAU0I,EAAGpH,GAChDkpB,EAAMmJ,WAAaryB,MAG3BkyB,EAAMjiC,UAAUg6B,MAAQ,WACpBv5B,KAAKyZ,SACL,IAAI+e,EAAQx4B,KAAKyO,OACjBzO,KAAK8Z,SAASC,UAAW,EACzBye,EAAM5e,YAAc,IAAI3Q,GAAWnB,eAC/B0wB,EAAM3e,eACN2e,EAAMmJ,WAAWnJ,EAAM5e,cAE/B4nB,EAAMjiC,UAAUue,OAAS,WACrB,IAAI5O,EAAQlP,KACRkjC,EAAerjC,UAAUC,OAAS,EAClC04B,EAAQx4B,KAAKyO,OACjB,OAAO,IAAIT,IAAa,SAAU5C,EAASkE,GACvC,IAAI6zB,EAAW,WACXj0B,EAAMqqB,QACN,IAAIvJ,EAAM9gB,EAAMsM,MAAMC,UAAUge,eAAevqB,EAAMhR,MACrD8xB,EAAIzlB,UAAYiH,IAAK,WACjBumB,GAAmB7oB,EAAMsM,MAAOtM,EAAMhR,MACtCkN,OAEJ4kB,EAAIxlB,QAAUghB,GAAmBlc,GACjC0gB,EAAImJ,UAAYjqB,EAAMkqB,gBAE1B,GAAI8J,EACA,MAAM,IAAIj6B,GAAW2U,gBAAgB,wCACrC4a,EAAM3e,cACN2e,EAAMxe,eAAejP,KAAKo4B,GAG1BA,QAIZ3B,EAAMjiC,UAAU6jC,UAAY,WACxB,OAAOpjC,KAAKgZ,OAEhBwoB,EAAMjiC,UAAUia,OAAS,WACrB,OAAsB,OAAfxZ,KAAKgZ,OAEhBwoB,EAAMjiC,UAAU8jC,cAAgB,WAC5B,IAAIzpB,EAAc5Z,KAAKyO,OAAOmL,YAC9B,OAAOA,GAAqC,mBAArBA,EAAY1b,MAEvCsjC,EAAMjiC,UAAU+jC,UAAY,WACxB,OAAmC,OAA5BtjC,KAAKyO,OAAOmL,aAEvB4nB,EAAMjiC,UAAUgkC,kBAAoB,WAChC,OAAOvjC,KAAKyO,OAAOuqB,YAEvB36B,OAAOC,eAAekjC,EAAMjiC,UAAW,SAAU,CAC7Cf,IAAK,WACD,IAAI0Q,EAAQlP,KACZ,OAAOc,EAAKd,KAAKq3B,YAAYjyB,KAAI,SAAUlH,GAAQ,OAAOgR,EAAMmoB,WAAWn5B,OAE/EK,YAAY,EACZ2D,cAAc,IAElBs/B,EAAMjiC,UAAUitB,YAAc,WAC1B,IAAI9pB,EAAOq4B,GAAuBh7B,MAAMC,KAAMH,WAC9C,OAAOG,KAAKwjC,aAAazjC,MAAMC,KAAM0C,IAEzC8+B,EAAMjiC,UAAUikC,aAAe,SAAU1kC,EAAMwwB,EAAQ2L,GACnD,IAAI/rB,EAAQlP,KACRm7B,EAAoBttB,GAAIwK,MACvB8iB,GAAqBA,EAAkBviB,KAAO5Y,OAA+B,IAAvBlB,EAAKsF,QAAQ,OACpE+2B,EAAoB,MACxB,IAEIsI,EAAS1qB,EAFT2qB,GAA0C,IAAvB5kC,EAAKsF,QAAQ,KACpCtF,EAAOA,EAAKo4B,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAE1C,IAOI,GANAne,EAAauW,EAAOlqB,KAAI,SAAU+d,GAC9B,IAAIqS,EAAYrS,aAAiBjU,EAAMgJ,MAAQiL,EAAMjlB,KAAOilB,EAC5D,GAAyB,iBAAdqS,EACP,MAAM,IAAIhsB,UAAU,mFACxB,OAAOgsB,KAEC,KAAR12B,GA58GD,aA48GgBA,EACf2kC,EA78GD,eA88GE,IAAY,MAAR3kC,GA78GL,aA68GqBA,EAGrB,MAAM,IAAImK,GAAW2U,gBAAgB,6BAA+B9e,GAFpE2kC,EA98GA,YAi9GJ,GAAItI,EAAmB,CACnB,GAn9GD,aAm9GKA,EAAkBr8B,MAl9GtB,cAk9G2C2kC,EAAuB,CAC9D,IAAIC,EAIA,MAAM,IAAIz6B,GAAW06B,eAAe,0FAHpCxI,EAAoB,KAKxBA,GACApiB,EAAW5X,SAAQ,SAAUq0B,GACzB,GAAI2F,IAA0E,IAArDA,EAAkBpiB,WAAW3U,QAAQoxB,GAAmB,CAC7E,IAAIkO,EAIA,MAAM,IAAIz6B,GAAW06B,eAAe,SAAWnO,EAC3C,wCAJJ2F,EAAoB,SAQhCuI,GAAoBvI,IAAsBA,EAAkB5O,SAC5D4O,EAAoB,OAIhC,MAAOh0B,GACH,OAAOg0B,EACHA,EAAkB/qB,SAAS,MAAM,SAAUsG,EAAGpH,GAAUA,EAAOnI,MAC/D8N,GAAU9N,GAElB,IAAIy8B,EAAmB1I,GAAsB/7B,KAAK,KAAMa,KAAMyjC,EAAS1qB,EAAYoiB,EAAmBF,GACtG,OAAQE,EACJA,EAAkB/qB,SAASqzB,EAASG,EAAkB,QACtD/1B,GAAIwK,MACAhF,GAAOxF,GAAIgL,WAAW,WAAc,OAAO3J,EAAM6zB,WAAWa,MAC5D5jC,KAAK+iC,WAAWa,IAE5BpC,EAAMjiC,UAAU4jB,MAAQ,SAAU5K,GAC9B,IAAK/W,EAAOxB,KAAKq3B,WAAY9e,GACzB,MAAM,IAAItP,GAAW46B,aAAa,SAAWtrB,EAAY,mBAE7D,OAAOvY,KAAKq3B,WAAW9e,IAEpBipB,EA9SI,GAiTXsC,GAAqC,oBAAXplC,QAA0B,eAAgBA,OAClEA,OAAOqlC,WACP,eACFC,GAAe,WACf,SAASA,EAAW7kB,GAChBnf,KAAKikC,WAAa9kB,EAQtB,OANA6kB,EAAWzkC,UAAU4f,UAAY,SAAU3Z,EAAG0d,EAAOif,GACjD,OAAOniC,KAAKikC,WAAYz+B,GAAkB,mBAANA,EAAmEA,EAAhD,CAAEe,KAAMf,EAAG0d,MAAOA,EAAOif,SAAUA,KAE9F6B,EAAWzkC,UAAUukC,IAAoB,WACrC,OAAO9jC,MAEJgkC,EAVO,GAalB,SAASE,GAAuBxY,EAAQkT,GAKpC,OAJA99B,EAAK89B,GAAQz9B,SAAQ,SAAU0+B,GAE3BlB,GADejT,EAAOmU,KAAUnU,EAAOmU,GAAQ,IAAIzB,IAC7BQ,EAAOiB,OAE1BnU,EAwFX,IACI6V,GAAU,CACN9lB,UAAWhb,EAAQgb,WAAahb,EAAQ0jC,cAAgB1jC,EAAQ2jC,iBAAmB3jC,EAAQ4jC,YAC3FjS,YAAa3xB,EAAQ2xB,aAAe3xB,EAAQ6jC,mBAGpD,MAAOn9B,GACHo6B,GAAU,CAAE9lB,UAAW,KAAM2W,YAAa,MAG9C,IAAIoP,GAAQ9J,GA4GZ,SAAS6M,GAAiBC,GACtB,IAAIC,EAAQC,GACZ,IACIA,IAAqB,EACrB9Y,GAAaqB,eAAe1S,KAAKiqB,GAErC,QACIE,GAAqBD,GAlH7B/iC,EAAM8/B,GAAO7hC,EAASA,EAAS,GAAIiK,IAAqB,CACpDkU,OAAQ,SAAU6mB,GAEd,OADS,IAAInD,GAAMmD,EAAc,CAAEhN,OAAQ,KACjC7Z,UAEd8mB,OAAQ,SAAU1mC,GACd,OAAO,IAAIsjC,GAAMtjC,EAAM,CAAEy5B,OAAQ,KAAMje,OAAO3O,MAAK,SAAU6N,GAEzD,OADAA,EAAG2gB,SACI,KACR3nB,MAAM,uBAAuB,WAAc,OAAO,MAEzDizB,iBAAkB,SAAUh0B,GACxB,IACI,OAh0CZ,SAA0B8K,GACtB,IAAIF,EAAYE,EAAGF,UAAW2W,EAAczW,EAAGyW,YAC/C,OAAOyF,GAAmBpc,GACpBra,QAAQgK,QAAQqQ,EAAUqc,aAAa/sB,MAAK,SAAU+5B,GACpD,OAAOA,EACF1/B,KAAI,SAAU2/B,GAAQ,OAAOA,EAAK7mC,QAClCoH,QAAO,SAAUpH,GAAQ,MA50EzB,cA40EgCA,QAEvCs5B,GAAgB/b,EAAW2W,GAAalW,eAAegJ,cAwzC9C2f,CAAiBrD,GAAME,cAAc32B,KAAK8F,GAErD,MAAO8K,GACH,OAAO1G,GAAU,IAAIhM,GAAWhB,cAGxCgV,YAAa,WAIT,OAHA,SAAeC,GACXlc,EAAOhB,KAAMkd,KAGlB8nB,kBAAmB,SAAU/J,GAC5B,OAAOptB,GAAIwK,MACPhF,GAAOxF,GAAIgL,UAAWoiB,GACtBA,KACLjD,IAAKA,GAAKiN,MAAO,SAAUC,GAC1B,OAAO,WACH,IACI,IAAIlhC,EAAKw2B,GAAc0K,EAAYnlC,MAAMC,KAAMH,YAC/C,OAAKmE,GAAyB,mBAAZA,EAAG+G,KAEd/G,EADIgK,GAAa5C,QAAQpH,GAGpC,MAAOmD,GACH,OAAO8N,GAAU9N,MAG1Bg+B,MAAO,SAAUD,EAAaxiC,EAAMsI,GACnC,IACI,IAAIhH,EAAKw2B,GAAc0K,EAAYnlC,MAAMiL,EAAMtI,GAAQ,KACvD,OAAKsB,GAAyB,mBAAZA,EAAG+G,KAEd/G,EADIgK,GAAa5C,QAAQpH,GAGpC,MAAOmD,GACH,OAAO8N,GAAU9N,KAGzBi+B,mBAAoB,CAChB5mC,IAAK,WAAc,OAAOqP,GAAIwK,OAAS,OACxCgV,QAAS,SAAUgY,EAAmBC,GACrC,IAAI11B,EAAU5B,GAAa5C,QAAqC,mBAAtBi6B,EACtC7D,GAAMwD,kBAAkBK,GACxBA,GACC9yB,QAAQ+yB,GAAmB,KAChC,OAAOz3B,GAAIwK,MACPxK,GAAIwK,MAAMgV,QAAQzd,GAClBA,GAERxO,QAAS4M,GACTtH,MAAO,CACHlI,IAAK,WAAc,OAAOkI,GAC1BzE,IAAK,SAAUrD,GACXkI,EAASlI,EAAiB,UAAVA,EAAoB,WAAc,OAAO,GAAU4Y,MAG3EpV,OAAQA,EAAQpB,OAAQA,EAAQU,MAAOA,EAAOmB,SAAUA,EACxDic,OAAQA,GAAQgO,GAAIlB,GAAc2Z,UAtKtC,SAAmBC,GACf,IAAIC,GAAW,EACXC,OAAenhC,EACfw/B,EAAa,IAAIC,IAAW,SAAU2B,GACtC,IAAItK,EAAmB50B,EAAgB++B,GAevC,IAAII,GAAS,EACTC,EAAY,GACZC,EAAa,GACbC,EAAe,CACf,aACI,OAAOH,GAEX5oB,YAAa,WACT4oB,GAAS,EACTha,GAAaqB,eAAejQ,YAAYgpB,KAGhDL,EAAShjC,OAASgjC,EAAShjC,MAAMojC,GACjC,IAAIE,GAAW,EAAOC,GAAmB,EACzC,SAASC,IACL,OAAOrlC,EAAKglC,GAAYv1B,MAAK,SAAUrR,GACnC,OAAO2mC,EAAU3mC,IApmBjC,SAAuBknC,EAAWC,GAC9B,IAAIC,EAAKxH,GAAoBuH,GACzBE,EAAcD,EAAG//B,OACrB,GAAIggC,EAAY//B,KACZ,OAAO,EAKX,IAJA,IAAIvB,EAAIshC,EAAY3nC,MAChB4nC,EAAK1H,GAAoBsH,GACzBK,EAAcD,EAAGjgC,KAAKtB,EAAE9E,MACxB8C,EAAIwjC,EAAY7nC,OACZ2nC,EAAY//B,OAASigC,EAAYjgC,MAAM,CAC3C,GAAIkV,GAAIzY,EAAE9C,KAAM8E,EAAE/E,KAAO,GAAKwb,GAAIzY,EAAE/C,GAAI+E,EAAE9E,OAAS,EAC/C,OAAO,EACXub,GAAIzW,EAAE9E,KAAM8C,EAAE9C,MAAQ,EACf8E,GAAKshC,EAAcD,EAAG//B,KAAKtD,EAAE9C,OAAOvB,MACpCqE,GAAKwjC,EAAcD,EAAGjgC,KAAKtB,EAAE9E,OAAOvB,MAE/C,OAAO,EAolB8B8nC,CAAcb,EAAU3mC,GAAM4mC,EAAW5mC,OAG1E,IAAI8mC,EAAmB,SAAUriB,GAC7BugB,GAAuB2B,EAAWliB,GAC9BwiB,KACAQ,KAGJA,EAAU,WACV,IAAIV,IAAYL,EAAhB,CAEAC,EAAY,GACZ,IAAIjF,EAAS,GACT7vB,EA5CR,SAAiB6vB,GACTvF,GACAtmB,KAEJ,IAAI6xB,EAAO,WAAc,OAAOxzB,GAASoyB,EAAS,CAAE5E,OAAQA,EAAQvoB,MAAO,QACvErU,EAAK6J,GAAIwK,MAELhF,GAAOxF,GAAIgL,UAAW+tB,GACxBA,IAIN,OAHIvL,GACAr3B,EAAG+G,KAAKsE,GAAyBA,IAE9BrL,EAgCG6iC,CAAQjG,GACbsF,IACDta,GA9uEuB,iBA8uEwBoa,GAC/CE,GAAmB,GAEvBD,GAAW,EACX7kC,QAAQgK,QAAQ2F,GAAKhG,MAAK,SAAUpH,GAChC8hC,GAAW,EACXC,EAAe/hC,EACfsiC,GAAW,EACPL,IAEAO,IACAQ,KAGAd,EAAY,GACZC,EAAalF,EACb+E,EAASp/B,MAAQo/B,EAASp/B,KAAK5C,QAEpC,SAAUoO,GACTk0B,GAAW,EACXR,GAAW,EACXE,EAASziB,OAASyiB,EAASziB,MAAMnR,GACjCg0B,EAAa/oB,mBAIrB,OADA2pB,IACOZ,KAIX,OAFAhC,EAAW0B,SAAW,WAAc,OAAOA,GAC3C1B,EAAW+C,SAAW,WAAc,OAAOpB,GACpC3B,GAqFiDG,uBAAwBA,GAChFpgC,aAAcA,EAAcU,aAAcA,EAAcuiC,aA1wJ5D,SAAsB9lC,EAAK8C,GACA,iBAAZA,EACPS,EAAavD,EAAK8C,OAASQ,GACtB,WAAYR,GACjB,GAAGqB,IAAItH,KAAKiG,GAAS,SAAUoX,GAC3B3W,EAAavD,EAAKka,OAAI5W,OAqwJsDQ,aAAcA,EAAcW,UAAWA,EAAW42B,cAAeA,GAAe5gB,IAAKA,GAAKhP,KAAMvJ,EACpL6jC,QAzsHS,IA0sHTrP,OAAQ,GACRzgB,YAAaA,GACbnO,SAAUA,EACV24B,aAAcH,GACd0F,OAhtHgB,QAgtHO5T,QAhtHP,QAgtH8BluB,MAAM,KAC/CC,KAAI,SAAUhG,GAAK,OAAOyF,SAASzF,MACnCsE,QAAO,SAAUjE,EAAGzB,EAAGL,GAAK,OAAO8B,EAAKzB,EAAIwlB,KAAKoW,IAAI,GAAQ,EAAJj8B,SAClE6jC,GAAM0F,OAAS1Y,GAAUgT,GAAME,aAAatP,aAEf,oBAAlB7b,eAA6D,oBAArB4wB,mBAC/Cvb,GA92EmC,kBA82EY,SAAUwb,GAEjD,IAAIC,EADH3C,KAEGvtB,IACAkwB,EAAUh7B,SAAS8J,YAAY,gBACvBmxB,gBAl3Ea,sBAk3EmC,GAAM,EAAMF,GAGpEC,EAAU,IAAIhxB,YAr3EO,qBAq3EqC,CACtDC,OAAQ8wB,IAGhB1C,IAAqB,EACrBnuB,cAAc8wB,GACd3C,IAAqB,MAG7ByC,iBA93EiC,sBA83EgB,SAAUxrB,GACvD,IAAIrF,EAASqF,EAAGrF,OACXouB,IACDH,GAAiBjuB,OAc7B,IAAIouB,IAAqB,EAEzB,GAAgC,oBAArB6C,iBAAkC,CACzC,IAAIC,GAAO,IAAID,iBAl5EkB,sBAm5EP,mBAAfC,GAAKC,OACZD,GAAKC,QAET7b,GAv5EmC,kBAu5EY,SAAU8b,GAChDhD,IACD8C,GAAKG,YAAYD,MAGzBF,GAAKI,UAAY,SAAUjb,GACnBA,EAAGkb,MACHtD,GAAiB5X,EAAGkb,YAG3B,GAAoB,oBAATlnC,MAA6C,oBAAdyW,UAA2B,CACtEwU,GAl6EmC,kBAk6EY,SAAU8b,GACrD,IACShD,KAC2B,oBAAjBoD,cACPA,aAAaC,QAr6EI,qBAq6EoC3sB,KAAKC,UAAU,CAChE2sB,KAAMxkB,KAAKykB,SACXP,aAAcA,KAGS,iBAApB/mC,KAAc,SACrBV,EAAc,GAAIU,KAAc,QAAEunC,SAAS,CAAEC,qBAAqB,KAAS,GAAMhnC,SAAQ,SAAUinC,GAC/F,OAAOA,EAAOT,YAAY,CACtB91B,KA76ES,qBA86ET61B,aAAcA,QAMlC,MAAO/rB,QAEqB,oBAArBwrB,kBACPA,iBAAiB,WAAW,SAAUxa,GAClC,GAx7EyB,uBAw7ErBA,EAAGztB,IAAwC,CAC3C,IAAI2oC,EAAOzsB,KAAKitB,MAAM1b,EAAG2b,UACrBT,GACAtD,GAAiBsD,EAAKH,kBAItC,IAAIa,GAAc5nC,KAAK0L,UAAY+K,UAAUoxB,cACzCD,IACAA,GAAYpB,iBAAiB,WAGrC,SAAiCxrB,GAC7B,IAAIksB,EAAOlsB,EAAGksB,KACVA,GAt8E6B,uBAs8ErBA,EAAKh2B,MACb0yB,GAAiBsD,EAAKH,iBAI9B15B,GAAad,gBA7nJb,SAAkBu7B,EAAUlhC,GACxB,IAAKkhC,GAAYA,aAAoBvgC,GAAcugC,aAAoBj/B,WAAai/B,aAAoBn/B,cAAgBm/B,EAASvqC,OAASyL,GAAa8+B,EAASvqC,MAC5J,OAAOuqC,EACX,IAAIzkC,EAAK,IAAI2F,GAAa8+B,EAASvqC,MAAMqJ,GAAWkhC,EAASlhC,QAASkhC,GAMtE,MALI,UAAWA,GACX3mC,EAAQkC,EAAI,QAAS,CAAExF,IAAK,WACpB,OAAOwB,KAAKoJ,MAAMnC,SAGvBjD,GAqnJX8C,EAASJ,EAAO8Q,ICjjKhB,MAAMkxB,GAAc,CAClBC,QAAS,CACPtV,QAAS,EACTyD,OAAQ,CAAE6R,QAAS,qBAErBC,YAAa,CACXvV,QAAS,EACTyD,OAAQ,CAAE8R,YAAa,yBAEzBC,cAAe,CACbxV,QAAS,EACTyD,OAAQ,CAAE+R,cAAe,uCAE3BC,eAAgB,CACdzV,QAAS,EACTyD,OAAQ,CAAEgS,eAAgB,wCAW9B,SAASC,GAASC,EAAQ3V,EAAU,EAAGyD,EAAS,CAC9C6R,QAAS,uBAGT,IACE,MAAM/vB,EAAK,IAAI,GAAMowB,GAErB,OADApwB,EAAGya,QAAQA,GAASyD,OAAOA,GACpBle,EACP,MAAOsK,GACPtM,QAAQsM,MAAMA,IA4BlB+hB,eAAegE,GAAiBD,EAAQE,EAAUC,GAChD,MACMhmB,EADK4lB,GAASC,EAAQN,GAAYQ,GAAU7V,QAASqV,GAAYQ,GAAUpS,QAChEoS,GACjB,IACE,MAAMvlC,QAAewf,EAAMxG,QAAQ,eAAevK,MAAM+2B,GAAU1sB,UAG5D2sB,EAAMC,KAAKD,MACXE,EAAY,OACZC,EAAY,GAOlB,OANA5lC,EAAOxC,QAAQyC,IACTwlC,EAAMxlC,EAAK4lC,YAAcF,GAC3BC,EAAUrlC,KAAKN,EAAK6lC,iBAGlBtmB,EAAMvE,WAAW2qB,GAChB5lC,EACP,MAAOuf,GACPtM,QAAQsM,MAAMA,IA0BlB+hB,eAAeyE,GAAKD,EAASE,EAAKC,EAAQC,GACxC,MAAMb,EAAS,gBACfroC,KAAKgnC,YAAY,CACf8B,UACA53B,KAAM,WACNi4B,SAAU,KAEZ,MAAMZ,EAAW,gBACjB,IACE,MAAMa,QApEV9E,eAAuB+D,EAAQE,EAAUhqC,EAAKN,GAC5C,MACMukB,EADK4lB,GAASC,EAAQN,GAAYQ,GAAU7V,QAASqV,GAAYQ,GAAUpS,QAChEoS,GACjB,IACE,MAAMvlC,QAAewf,EAAMjJ,MAAMhb,GAAKyb,OAAO/b,GAAO6d,UACpD,GAAG9Y,EAAO7D,OAAS,EAAG,CACpB,MAAMkqC,EAAcrmC,EAAO,GAAGqmC,YAC9B7mB,EAAM1F,OAAO9Z,EAAO,GAAG8lC,QAAS,CAAED,YAAaH,KAAKD,MAAOY,YAAaA,EAAc,IAExF,OAAOrmC,EACP,MAAOuf,GACPtM,QAAQsM,MAAMA,IAyDQ+mB,CAAQjB,EAAQE,EAAU,UAAWO,GAC3D,GAAIM,GAAWA,EAAQjqC,OAAS,EAAG,CACjC,MAAM+nC,EAAOkC,EAAQ,GAErB,GADsBlC,EAAKgC,aACNA,EAAY,CAC/B,MAAMK,EAAOH,EAAQ,GAAGG,KACxBvpC,KAAKgnC,YAAY,CACf8B,UACA53B,KAAM,WACNi4B,SAAU,KAGZ,MAAMK,QAAkBD,EAAKE,OAAOr/B,KAAMq/B,GAC5BhvB,KAAKitB,MAAM+B,IAkBzB,OAdAzpC,KAAKgnC,YAAY,CACf8B,UACA53B,KAAM,WACNi4B,SAAU,KAKZnpC,KAAKgnC,YAAY,CACf8B,UACAG,SACA/B,KAAMsC,SAERlB,GAAiBD,EAAQE,EAAU,KAEhC,CAGH,MAAMmB,EAAc,GACpBA,EAAYnmC,KAAK6lC,EAAQ,GAAGN,eAnEpCxE,eAAyB+D,EAAQE,EAAUoB,GACzC,MACMnnB,EADK4lB,GAASC,EAAQN,GAAYQ,GAAU7V,QAASqV,GAAYQ,GAAUpS,QAChEoS,GACjB,UACQ/lB,EAAMvE,WAAW0rB,GACvB,MAAOpnB,GACPtM,QAAQsM,MAAMA,IA8DJqmB,CAAUP,EAAQE,EAAUmB,KAGtC,MAAOnnB,GACPtM,QAAQsM,MAAMA,IAtHlB,SAA2BymB,GACzB,OAAOY,MAAMZ,GAAK5+B,KAAMT,GAEfA,EAAIkgC,QACVz/B,KAAM88B,GACAA,IAmHT4C,CAAkBd,GAAK5+B,KAAKk6B,UAG1B,IAAIyF,EAAa7C,GAAQA,EAAKziC,IAAIxB,IACf,IACZA,EACH+mC,MAAO/mC,EAAKgnC,IACZC,IAAKjnC,EAAKinC,IACV7pB,IAAKpd,EAAKod,IACVlF,MAAOlY,EAAKoY,OAIZ6rB,IACF6C,EAAa,IAEf/pC,KAAKgnC,YAAY,CACf8B,UACAG,SACA/B,KAAM6C,IAKR,MAAMR,EAAO,IAAIY,KAAK,CAAC1vB,KAAKC,UAAUqvB,IAAc,CAAE74B,KAAM,qBAG5DlR,KAAKgnC,YAAY,CACf8B,UACA53B,KAAM,aAGRo3B,GAAiBD,EAAQE,EAAU,KAEnC,SAlGajE,OAAO4C,EAAMmB,EAAQE,KACpC,MAGM/lB,EAHK4lB,GAASC,EAAQN,GAAYQ,GAAU7V,QAASqV,GAAYQ,GAAUpS,QAGhEoS,GACjB,UACQ/lB,EAAMhG,IAAI0qB,GAChB,MAAO3kB,GACPtM,QAAQsM,MAAM,UAAWA,KA2FjB6nB,CAAS,CACbtB,UACAS,OACAr4B,KAAM,mBACNm5B,WAAY3B,KAAKD,MACjBI,YAAaH,KAAKD,MAClBY,YAAa,EACbH,cACCb,EAAQE,GACX,MAAOhmB,GACPtM,QAAQsM,MAAMA,MAEftR,MAAMsR,IAEPtM,QAAQsM,MAAMA,GAEdviB,KAAKgnC,YAAY,CACf8B,UACAG,SACA/B,KAAM,OAMZlnC,KAAKwmC,iBAAiB,UAAWlC,MAAOhvB,IAGtC,MAAM,KAAEpE,GAASoE,EAAM4xB,KACvB,GAAa,SAATh2B,EAAiB,CACnB,MAAM,QAAE43B,EAAO,IAAEE,EAAG,OAAEC,EAAM,WAAEC,GAAe5zB,EAAM4xB,KACnD6B,GAAKD,EAASE,EAAKC,EAAQC", "file": "motorAssets/Worker/indexdb.worker.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "/*\n * Dexie.js - a minimalistic wrapper for IndexedDB\n * ===============================================\n *\n * By <PERSON>, <EMAIL>\n *\n * Version 3.2.4, Tue May 30 2023\n *\n * https://dexie.org\n *\n * Apache License Version 2.0, January 2004, http://www.apache.org/licenses/\n */\n \n/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\nvar __assign = function() {\n    __assign = Object.assign || function __assign(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nfunction __spreadArray(to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nvar _global = typeof globalThis !== 'undefined' ? globalThis :\n    typeof self !== 'undefined' ? self :\n        typeof window !== 'undefined' ? window :\n            global;\n\nvar keys = Object.keys;\nvar isArray = Array.isArray;\nif (typeof Promise !== 'undefined' && !_global.Promise) {\n    _global.Promise = Promise;\n}\nfunction extend(obj, extension) {\n    if (typeof extension !== 'object')\n        return obj;\n    keys(extension).forEach(function (key) {\n        obj[key] = extension[key];\n    });\n    return obj;\n}\nvar getProto = Object.getPrototypeOf;\nvar _hasOwn = {}.hasOwnProperty;\nfunction hasOwn(obj, prop) {\n    return _hasOwn.call(obj, prop);\n}\nfunction props(proto, extension) {\n    if (typeof extension === 'function')\n        extension = extension(getProto(proto));\n    (typeof Reflect === \"undefined\" ? keys : Reflect.ownKeys)(extension).forEach(function (key) {\n        setProp(proto, key, extension[key]);\n    });\n}\nvar defineProperty = Object.defineProperty;\nfunction setProp(obj, prop, functionOrGetSet, options) {\n    defineProperty(obj, prop, extend(functionOrGetSet && hasOwn(functionOrGetSet, \"get\") && typeof functionOrGetSet.get === 'function' ?\n        { get: functionOrGetSet.get, set: functionOrGetSet.set, configurable: true } :\n        { value: functionOrGetSet, configurable: true, writable: true }, options));\n}\nfunction derive(Child) {\n    return {\n        from: function (Parent) {\n            Child.prototype = Object.create(Parent.prototype);\n            setProp(Child.prototype, \"constructor\", Child);\n            return {\n                extend: props.bind(null, Child.prototype)\n            };\n        }\n    };\n}\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nfunction getPropertyDescriptor(obj, prop) {\n    var pd = getOwnPropertyDescriptor(obj, prop);\n    var proto;\n    return pd || (proto = getProto(obj)) && getPropertyDescriptor(proto, prop);\n}\nvar _slice = [].slice;\nfunction slice(args, start, end) {\n    return _slice.call(args, start, end);\n}\nfunction override(origFunc, overridedFactory) {\n    return overridedFactory(origFunc);\n}\nfunction assert(b) {\n    if (!b)\n        throw new Error(\"Assertion Failed\");\n}\nfunction asap$1(fn) {\n    if (_global.setImmediate)\n        setImmediate(fn);\n    else\n        setTimeout(fn, 0);\n}\nfunction arrayToObject(array, extractor) {\n    return array.reduce(function (result, item, i) {\n        var nameAndValue = extractor(item, i);\n        if (nameAndValue)\n            result[nameAndValue[0]] = nameAndValue[1];\n        return result;\n    }, {});\n}\nfunction tryCatch(fn, onerror, args) {\n    try {\n        fn.apply(null, args);\n    }\n    catch (ex) {\n        onerror && onerror(ex);\n    }\n}\nfunction getByKeyPath(obj, keyPath) {\n    if (hasOwn(obj, keyPath))\n        return obj[keyPath];\n    if (!keyPath)\n        return obj;\n    if (typeof keyPath !== 'string') {\n        var rv = [];\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            var val = getByKeyPath(obj, keyPath[i]);\n            rv.push(val);\n        }\n        return rv;\n    }\n    var period = keyPath.indexOf('.');\n    if (period !== -1) {\n        var innerObj = obj[keyPath.substr(0, period)];\n        return innerObj === undefined ? undefined : getByKeyPath(innerObj, keyPath.substr(period + 1));\n    }\n    return undefined;\n}\nfunction setByKeyPath(obj, keyPath, value) {\n    if (!obj || keyPath === undefined)\n        return;\n    if ('isFrozen' in Object && Object.isFrozen(obj))\n        return;\n    if (typeof keyPath !== 'string' && 'length' in keyPath) {\n        assert(typeof value !== 'string' && 'length' in value);\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            setByKeyPath(obj, keyPath[i], value[i]);\n        }\n    }\n    else {\n        var period = keyPath.indexOf('.');\n        if (period !== -1) {\n            var currentKeyPath = keyPath.substr(0, period);\n            var remainingKeyPath = keyPath.substr(period + 1);\n            if (remainingKeyPath === \"\")\n                if (value === undefined) {\n                    if (isArray(obj) && !isNaN(parseInt(currentKeyPath)))\n                        obj.splice(currentKeyPath, 1);\n                    else\n                        delete obj[currentKeyPath];\n                }\n                else\n                    obj[currentKeyPath] = value;\n            else {\n                var innerObj = obj[currentKeyPath];\n                if (!innerObj || !hasOwn(obj, currentKeyPath))\n                    innerObj = (obj[currentKeyPath] = {});\n                setByKeyPath(innerObj, remainingKeyPath, value);\n            }\n        }\n        else {\n            if (value === undefined) {\n                if (isArray(obj) && !isNaN(parseInt(keyPath)))\n                    obj.splice(keyPath, 1);\n                else\n                    delete obj[keyPath];\n            }\n            else\n                obj[keyPath] = value;\n        }\n    }\n}\nfunction delByKeyPath(obj, keyPath) {\n    if (typeof keyPath === 'string')\n        setByKeyPath(obj, keyPath, undefined);\n    else if ('length' in keyPath)\n        [].map.call(keyPath, function (kp) {\n            setByKeyPath(obj, kp, undefined);\n        });\n}\nfunction shallowClone(obj) {\n    var rv = {};\n    for (var m in obj) {\n        if (hasOwn(obj, m))\n            rv[m] = obj[m];\n    }\n    return rv;\n}\nvar concat = [].concat;\nfunction flatten(a) {\n    return concat.apply([], a);\n}\nvar intrinsicTypeNames = \"Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey\"\n    .split(',').concat(flatten([8, 16, 32, 64].map(function (num) { return [\"Int\", \"Uint\", \"Float\"].map(function (t) { return t + num + \"Array\"; }); }))).filter(function (t) { return _global[t]; });\nvar intrinsicTypes = intrinsicTypeNames.map(function (t) { return _global[t]; });\narrayToObject(intrinsicTypeNames, function (x) { return [x, true]; });\nvar circularRefs = null;\nfunction deepClone(any) {\n    circularRefs = typeof WeakMap !== 'undefined' && new WeakMap();\n    var rv = innerDeepClone(any);\n    circularRefs = null;\n    return rv;\n}\nfunction innerDeepClone(any) {\n    if (!any || typeof any !== 'object')\n        return any;\n    var rv = circularRefs && circularRefs.get(any);\n    if (rv)\n        return rv;\n    if (isArray(any)) {\n        rv = [];\n        circularRefs && circularRefs.set(any, rv);\n        for (var i = 0, l = any.length; i < l; ++i) {\n            rv.push(innerDeepClone(any[i]));\n        }\n    }\n    else if (intrinsicTypes.indexOf(any.constructor) >= 0) {\n        rv = any;\n    }\n    else {\n        var proto = getProto(any);\n        rv = proto === Object.prototype ? {} : Object.create(proto);\n        circularRefs && circularRefs.set(any, rv);\n        for (var prop in any) {\n            if (hasOwn(any, prop)) {\n                rv[prop] = innerDeepClone(any[prop]);\n            }\n        }\n    }\n    return rv;\n}\nvar toString = {}.toString;\nfunction toStringTag(o) {\n    return toString.call(o).slice(8, -1);\n}\nvar iteratorSymbol = typeof Symbol !== 'undefined' ?\n    Symbol.iterator :\n    '@@iterator';\nvar getIteratorOf = typeof iteratorSymbol === \"symbol\" ? function (x) {\n    var i;\n    return x != null && (i = x[iteratorSymbol]) && i.apply(x);\n} : function () { return null; };\nvar NO_CHAR_ARRAY = {};\nfunction getArrayOf(arrayLike) {\n    var i, a, x, it;\n    if (arguments.length === 1) {\n        if (isArray(arrayLike))\n            return arrayLike.slice();\n        if (this === NO_CHAR_ARRAY && typeof arrayLike === 'string')\n            return [arrayLike];\n        if ((it = getIteratorOf(arrayLike))) {\n            a = [];\n            while ((x = it.next()), !x.done)\n                a.push(x.value);\n            return a;\n        }\n        if (arrayLike == null)\n            return [arrayLike];\n        i = arrayLike.length;\n        if (typeof i === 'number') {\n            a = new Array(i);\n            while (i--)\n                a[i] = arrayLike[i];\n            return a;\n        }\n        return [arrayLike];\n    }\n    i = arguments.length;\n    a = new Array(i);\n    while (i--)\n        a[i] = arguments[i];\n    return a;\n}\nvar isAsyncFunction = typeof Symbol !== 'undefined'\n    ? function (fn) { return fn[Symbol.toStringTag] === 'AsyncFunction'; }\n    : function () { return false; };\n\nvar debug = typeof location !== 'undefined' &&\n    /^(http|https):\\/\\/(localhost|127\\.0\\.0\\.1)/.test(location.href);\nfunction setDebug(value, filter) {\n    debug = value;\n    libraryFilter = filter;\n}\nvar libraryFilter = function () { return true; };\nvar NEEDS_THROW_FOR_STACK = !new Error(\"\").stack;\nfunction getErrorWithStack() {\n    if (NEEDS_THROW_FOR_STACK)\n        try {\n            getErrorWithStack.arguments;\n            throw new Error();\n        }\n        catch (e) {\n            return e;\n        }\n    return new Error();\n}\nfunction prettyStack(exception, numIgnoredFrames) {\n    var stack = exception.stack;\n    if (!stack)\n        return \"\";\n    numIgnoredFrames = (numIgnoredFrames || 0);\n    if (stack.indexOf(exception.name) === 0)\n        numIgnoredFrames += (exception.name + exception.message).split('\\n').length;\n    return stack.split('\\n')\n        .slice(numIgnoredFrames)\n        .filter(libraryFilter)\n        .map(function (frame) { return \"\\n\" + frame; })\n        .join('');\n}\n\nvar dexieErrorNames = [\n    'Modify',\n    'Bulk',\n    'OpenFailed',\n    'VersionChange',\n    'Schema',\n    'Upgrade',\n    'InvalidTable',\n    'MissingAPI',\n    'NoSuchDatabase',\n    'InvalidArgument',\n    'SubTransaction',\n    'Unsupported',\n    'Internal',\n    'DatabaseClosed',\n    'PrematureCommit',\n    'ForeignAwait'\n];\nvar idbDomErrorNames = [\n    'Unknown',\n    'Constraint',\n    'Data',\n    'TransactionInactive',\n    'ReadOnly',\n    'Version',\n    'NotFound',\n    'InvalidState',\n    'InvalidAccess',\n    'Abort',\n    'Timeout',\n    'QuotaExceeded',\n    'Syntax',\n    'DataClone'\n];\nvar errorList = dexieErrorNames.concat(idbDomErrorNames);\nvar defaultTexts = {\n    VersionChanged: \"Database version changed by other database connection\",\n    DatabaseClosed: \"Database has been closed\",\n    Abort: \"Transaction aborted\",\n    TransactionInactive: \"Transaction has already completed or failed\",\n    MissingAPI: \"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb\"\n};\nfunction DexieError(name, msg) {\n    this._e = getErrorWithStack();\n    this.name = name;\n    this.message = msg;\n}\nderive(DexieError).from(Error).extend({\n    stack: {\n        get: function () {\n            return this._stack ||\n                (this._stack = this.name + \": \" + this.message + prettyStack(this._e, 2));\n        }\n    },\n    toString: function () { return this.name + \": \" + this.message; }\n});\nfunction getMultiErrorMessage(msg, failures) {\n    return msg + \". Errors: \" + Object.keys(failures)\n        .map(function (key) { return failures[key].toString(); })\n        .filter(function (v, i, s) { return s.indexOf(v) === i; })\n        .join('\\n');\n}\nfunction ModifyError(msg, failures, successCount, failedKeys) {\n    this._e = getErrorWithStack();\n    this.failures = failures;\n    this.failedKeys = failedKeys;\n    this.successCount = successCount;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(ModifyError).from(DexieError);\nfunction BulkError(msg, failures) {\n    this._e = getErrorWithStack();\n    this.name = \"BulkError\";\n    this.failures = Object.keys(failures).map(function (pos) { return failures[pos]; });\n    this.failuresByPos = failures;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(BulkError).from(DexieError);\nvar errnames = errorList.reduce(function (obj, name) { return (obj[name] = name + \"Error\", obj); }, {});\nvar BaseException = DexieError;\nvar exceptions = errorList.reduce(function (obj, name) {\n    var fullName = name + \"Error\";\n    function DexieError(msgOrInner, inner) {\n        this._e = getErrorWithStack();\n        this.name = fullName;\n        if (!msgOrInner) {\n            this.message = defaultTexts[name] || fullName;\n            this.inner = null;\n        }\n        else if (typeof msgOrInner === 'string') {\n            this.message = \"\" + msgOrInner + (!inner ? '' : '\\n ' + inner);\n            this.inner = inner || null;\n        }\n        else if (typeof msgOrInner === 'object') {\n            this.message = msgOrInner.name + \" \" + msgOrInner.message;\n            this.inner = msgOrInner;\n        }\n    }\n    derive(DexieError).from(BaseException);\n    obj[name] = DexieError;\n    return obj;\n}, {});\nexceptions.Syntax = SyntaxError;\nexceptions.Type = TypeError;\nexceptions.Range = RangeError;\nvar exceptionMap = idbDomErrorNames.reduce(function (obj, name) {\n    obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\nfunction mapError(domError, message) {\n    if (!domError || domError instanceof DexieError || domError instanceof TypeError || domError instanceof SyntaxError || !domError.name || !exceptionMap[domError.name])\n        return domError;\n    var rv = new exceptionMap[domError.name](message || domError.message, domError);\n    if (\"stack\" in domError) {\n        setProp(rv, \"stack\", { get: function () {\n                return this.inner.stack;\n            } });\n    }\n    return rv;\n}\nvar fullNameExceptions = errorList.reduce(function (obj, name) {\n    if ([\"Syntax\", \"Type\", \"Range\"].indexOf(name) === -1)\n        obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\nfullNameExceptions.ModifyError = ModifyError;\nfullNameExceptions.DexieError = DexieError;\nfullNameExceptions.BulkError = BulkError;\n\nfunction nop() { }\nfunction mirror(val) { return val; }\nfunction pureFunctionChain(f1, f2) {\n    if (f1 == null || f1 === mirror)\n        return f2;\n    return function (val) {\n        return f2(f1(val));\n    };\n}\nfunction callBoth(on1, on2) {\n    return function () {\n        on1.apply(this, arguments);\n        on2.apply(this, arguments);\n    };\n}\nfunction hookCreatingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res !== undefined)\n            arguments[0] = res;\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res2 !== undefined ? res2 : res;\n    };\n}\nfunction hookDeletingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        f1.apply(this, arguments);\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = this.onerror = null;\n        f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n    };\n}\nfunction hookUpdatingChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function (modifications) {\n        var res = f1.apply(this, arguments);\n        extend(modifications, res);\n        var onsuccess = this.onsuccess,\n        onerror = this.onerror;\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess)\n            this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror)\n            this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res === undefined ?\n            (res2 === undefined ? undefined : res2) :\n            (extend(res, res2));\n    };\n}\nfunction reverseStoppableEventChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        if (f2.apply(this, arguments) === false)\n            return false;\n        return f1.apply(this, arguments);\n    };\n}\nfunction promisableChain(f1, f2) {\n    if (f1 === nop)\n        return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res && typeof res.then === 'function') {\n            var thiz = this, i = arguments.length, args = new Array(i);\n            while (i--)\n                args[i] = arguments[i];\n            return res.then(function () {\n                return f2.apply(thiz, args);\n            });\n        }\n        return f2.apply(this, arguments);\n    };\n}\n\nvar INTERNAL = {};\nvar LONG_STACKS_CLIP_LIMIT = 100,\nMAX_LONG_STACKS = 20, ZONE_ECHO_LIMIT = 100, _a$1 = typeof Promise === 'undefined' ?\n    [] :\n    (function () {\n        var globalP = Promise.resolve();\n        if (typeof crypto === 'undefined' || !crypto.subtle)\n            return [globalP, getProto(globalP), globalP];\n        var nativeP = crypto.subtle.digest(\"SHA-512\", new Uint8Array([0]));\n        return [\n            nativeP,\n            getProto(nativeP),\n            globalP\n        ];\n    })(), resolvedNativePromise = _a$1[0], nativePromiseProto = _a$1[1], resolvedGlobalPromise = _a$1[2], nativePromiseThen = nativePromiseProto && nativePromiseProto.then;\nvar NativePromise = resolvedNativePromise && resolvedNativePromise.constructor;\nvar patchGlobalPromise = !!resolvedGlobalPromise;\nvar stack_being_generated = false;\nvar schedulePhysicalTick = resolvedGlobalPromise ?\n    function () { resolvedGlobalPromise.then(physicalTick); }\n    :\n        _global.setImmediate ?\n            setImmediate.bind(null, physicalTick) :\n            _global.MutationObserver ?\n                function () {\n                    var hiddenDiv = document.createElement(\"div\");\n                    (new MutationObserver(function () {\n                        physicalTick();\n                        hiddenDiv = null;\n                    })).observe(hiddenDiv, { attributes: true });\n                    hiddenDiv.setAttribute('i', '1');\n                } :\n                function () { setTimeout(physicalTick, 0); };\nvar asap = function (callback, args) {\n    microtickQueue.push([callback, args]);\n    if (needsNewPhysicalTick) {\n        schedulePhysicalTick();\n        needsNewPhysicalTick = false;\n    }\n};\nvar isOutsideMicroTick = true,\nneedsNewPhysicalTick = true,\nunhandledErrors = [],\nrejectingErrors = [],\ncurrentFulfiller = null, rejectionMapper = mirror;\nvar globalPSD = {\n    id: 'global',\n    global: true,\n    ref: 0,\n    unhandleds: [],\n    onunhandled: globalError,\n    pgp: false,\n    env: {},\n    finalize: function () {\n        this.unhandleds.forEach(function (uh) {\n            try {\n                globalError(uh[0], uh[1]);\n            }\n            catch (e) { }\n        });\n    }\n};\nvar PSD = globalPSD;\nvar microtickQueue = [];\nvar numScheduledCalls = 0;\nvar tickFinalizers = [];\nfunction DexiePromise(fn) {\n    if (typeof this !== 'object')\n        throw new TypeError('Promises must be constructed via new');\n    this._listeners = [];\n    this.onuncatched = nop;\n    this._lib = false;\n    var psd = (this._PSD = PSD);\n    if (debug) {\n        this._stackHolder = getErrorWithStack();\n        this._prev = null;\n        this._numPrev = 0;\n    }\n    if (typeof fn !== 'function') {\n        if (fn !== INTERNAL)\n            throw new TypeError('Not a function');\n        this._state = arguments[1];\n        this._value = arguments[2];\n        if (this._state === false)\n            handleRejection(this, this._value);\n        return;\n    }\n    this._state = null;\n    this._value = null;\n    ++psd.ref;\n    executePromiseTask(this, fn);\n}\nvar thenProp = {\n    get: function () {\n        var psd = PSD, microTaskId = totalEchoes;\n        function then(onFulfilled, onRejected) {\n            var _this = this;\n            var possibleAwait = !psd.global && (psd !== PSD || microTaskId !== totalEchoes);\n            var cleanup = possibleAwait && !decrementExpectedAwaits();\n            var rv = new DexiePromise(function (resolve, reject) {\n                propagateToListener(_this, new Listener(nativeAwaitCompatibleWrap(onFulfilled, psd, possibleAwait, cleanup), nativeAwaitCompatibleWrap(onRejected, psd, possibleAwait, cleanup), resolve, reject, psd));\n            });\n            debug && linkToPreviousPromise(rv, this);\n            return rv;\n        }\n        then.prototype = INTERNAL;\n        return then;\n    },\n    set: function (value) {\n        setProp(this, 'then', value && value.prototype === INTERNAL ?\n            thenProp :\n            {\n                get: function () {\n                    return value;\n                },\n                set: thenProp.set\n            });\n    }\n};\nprops(DexiePromise.prototype, {\n    then: thenProp,\n    _then: function (onFulfilled, onRejected) {\n        propagateToListener(this, new Listener(null, null, onFulfilled, onRejected, PSD));\n    },\n    catch: function (onRejected) {\n        if (arguments.length === 1)\n            return this.then(null, onRejected);\n        var type = arguments[0], handler = arguments[1];\n        return typeof type === 'function' ? this.then(null, function (err) {\n            return err instanceof type ? handler(err) : PromiseReject(err);\n        })\n            : this.then(null, function (err) {\n                return err && err.name === type ? handler(err) : PromiseReject(err);\n            });\n    },\n    finally: function (onFinally) {\n        return this.then(function (value) {\n            onFinally();\n            return value;\n        }, function (err) {\n            onFinally();\n            return PromiseReject(err);\n        });\n    },\n    stack: {\n        get: function () {\n            if (this._stack)\n                return this._stack;\n            try {\n                stack_being_generated = true;\n                var stacks = getStack(this, [], MAX_LONG_STACKS);\n                var stack = stacks.join(\"\\nFrom previous: \");\n                if (this._state !== null)\n                    this._stack = stack;\n                return stack;\n            }\n            finally {\n                stack_being_generated = false;\n            }\n        }\n    },\n    timeout: function (ms, msg) {\n        var _this = this;\n        return ms < Infinity ?\n            new DexiePromise(function (resolve, reject) {\n                var handle = setTimeout(function () { return reject(new exceptions.Timeout(msg)); }, ms);\n                _this.then(resolve, reject).finally(clearTimeout.bind(null, handle));\n            }) : this;\n    }\n});\nif (typeof Symbol !== 'undefined' && Symbol.toStringTag)\n    setProp(DexiePromise.prototype, Symbol.toStringTag, 'Dexie.Promise');\nglobalPSD.env = snapShot();\nfunction Listener(onFulfilled, onRejected, resolve, reject, zone) {\n    this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n    this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n    this.resolve = resolve;\n    this.reject = reject;\n    this.psd = zone;\n}\nprops(DexiePromise, {\n    all: function () {\n        var values = getArrayOf.apply(null, arguments)\n            .map(onPossibleParallellAsync);\n        return new DexiePromise(function (resolve, reject) {\n            if (values.length === 0)\n                resolve([]);\n            var remaining = values.length;\n            values.forEach(function (a, i) { return DexiePromise.resolve(a).then(function (x) {\n                values[i] = x;\n                if (!--remaining)\n                    resolve(values);\n            }, reject); });\n        });\n    },\n    resolve: function (value) {\n        if (value instanceof DexiePromise)\n            return value;\n        if (value && typeof value.then === 'function')\n            return new DexiePromise(function (resolve, reject) {\n                value.then(resolve, reject);\n            });\n        var rv = new DexiePromise(INTERNAL, true, value);\n        linkToPreviousPromise(rv, currentFulfiller);\n        return rv;\n    },\n    reject: PromiseReject,\n    race: function () {\n        var values = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise(function (resolve, reject) {\n            values.map(function (value) { return DexiePromise.resolve(value).then(resolve, reject); });\n        });\n    },\n    PSD: {\n        get: function () { return PSD; },\n        set: function (value) { return PSD = value; }\n    },\n    totalEchoes: { get: function () { return totalEchoes; } },\n    newPSD: newScope,\n    usePSD: usePSD,\n    scheduler: {\n        get: function () { return asap; },\n        set: function (value) { asap = value; }\n    },\n    rejectionMapper: {\n        get: function () { return rejectionMapper; },\n        set: function (value) { rejectionMapper = value; }\n    },\n    follow: function (fn, zoneProps) {\n        return new DexiePromise(function (resolve, reject) {\n            return newScope(function (resolve, reject) {\n                var psd = PSD;\n                psd.unhandleds = [];\n                psd.onunhandled = reject;\n                psd.finalize = callBoth(function () {\n                    var _this = this;\n                    run_at_end_of_this_or_next_physical_tick(function () {\n                        _this.unhandleds.length === 0 ? resolve() : reject(_this.unhandleds[0]);\n                    });\n                }, psd.finalize);\n                fn();\n            }, zoneProps, resolve, reject);\n        });\n    }\n});\nif (NativePromise) {\n    if (NativePromise.allSettled)\n        setProp(DexiePromise, \"allSettled\", function () {\n            var possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n            return new DexiePromise(function (resolve) {\n                if (possiblePromises.length === 0)\n                    resolve([]);\n                var remaining = possiblePromises.length;\n                var results = new Array(remaining);\n                possiblePromises.forEach(function (p, i) { return DexiePromise.resolve(p).then(function (value) { return results[i] = { status: \"fulfilled\", value: value }; }, function (reason) { return results[i] = { status: \"rejected\", reason: reason }; })\n                    .then(function () { return --remaining || resolve(results); }); });\n            });\n        });\n    if (NativePromise.any && typeof AggregateError !== 'undefined')\n        setProp(DexiePromise, \"any\", function () {\n            var possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n            return new DexiePromise(function (resolve, reject) {\n                if (possiblePromises.length === 0)\n                    reject(new AggregateError([]));\n                var remaining = possiblePromises.length;\n                var failures = new Array(remaining);\n                possiblePromises.forEach(function (p, i) { return DexiePromise.resolve(p).then(function (value) { return resolve(value); }, function (failure) {\n                    failures[i] = failure;\n                    if (!--remaining)\n                        reject(new AggregateError(failures));\n                }); });\n            });\n        });\n}\nfunction executePromiseTask(promise, fn) {\n    try {\n        fn(function (value) {\n            if (promise._state !== null)\n                return;\n            if (value === promise)\n                throw new TypeError('A promise cannot be resolved with itself.');\n            var shouldExecuteTick = promise._lib && beginMicroTickScope();\n            if (value && typeof value.then === 'function') {\n                executePromiseTask(promise, function (resolve, reject) {\n                    value instanceof DexiePromise ?\n                        value._then(resolve, reject) :\n                        value.then(resolve, reject);\n                });\n            }\n            else {\n                promise._state = true;\n                promise._value = value;\n                propagateAllListeners(promise);\n            }\n            if (shouldExecuteTick)\n                endMicroTickScope();\n        }, handleRejection.bind(null, promise));\n    }\n    catch (ex) {\n        handleRejection(promise, ex);\n    }\n}\nfunction handleRejection(promise, reason) {\n    rejectingErrors.push(reason);\n    if (promise._state !== null)\n        return;\n    var shouldExecuteTick = promise._lib && beginMicroTickScope();\n    reason = rejectionMapper(reason);\n    promise._state = false;\n    promise._value = reason;\n    debug && reason !== null && typeof reason === 'object' && !reason._promise && tryCatch(function () {\n        var origProp = getPropertyDescriptor(reason, \"stack\");\n        reason._promise = promise;\n        setProp(reason, \"stack\", {\n            get: function () {\n                return stack_being_generated ?\n                    origProp && (origProp.get ?\n                        origProp.get.apply(reason) :\n                        origProp.value) :\n                    promise.stack;\n            }\n        });\n    });\n    addPossiblyUnhandledError(promise);\n    propagateAllListeners(promise);\n    if (shouldExecuteTick)\n        endMicroTickScope();\n}\nfunction propagateAllListeners(promise) {\n    var listeners = promise._listeners;\n    promise._listeners = [];\n    for (var i = 0, len = listeners.length; i < len; ++i) {\n        propagateToListener(promise, listeners[i]);\n    }\n    var psd = promise._PSD;\n    --psd.ref || psd.finalize();\n    if (numScheduledCalls === 0) {\n        ++numScheduledCalls;\n        asap(function () {\n            if (--numScheduledCalls === 0)\n                finalizePhysicalTick();\n        }, []);\n    }\n}\nfunction propagateToListener(promise, listener) {\n    if (promise._state === null) {\n        promise._listeners.push(listener);\n        return;\n    }\n    var cb = promise._state ? listener.onFulfilled : listener.onRejected;\n    if (cb === null) {\n        return (promise._state ? listener.resolve : listener.reject)(promise._value);\n    }\n    ++listener.psd.ref;\n    ++numScheduledCalls;\n    asap(callListener, [cb, promise, listener]);\n}\nfunction callListener(cb, promise, listener) {\n    try {\n        currentFulfiller = promise;\n        var ret, value = promise._value;\n        if (promise._state) {\n            ret = cb(value);\n        }\n        else {\n            if (rejectingErrors.length)\n                rejectingErrors = [];\n            ret = cb(value);\n            if (rejectingErrors.indexOf(value) === -1)\n                markErrorAsHandled(promise);\n        }\n        listener.resolve(ret);\n    }\n    catch (e) {\n        listener.reject(e);\n    }\n    finally {\n        currentFulfiller = null;\n        if (--numScheduledCalls === 0)\n            finalizePhysicalTick();\n        --listener.psd.ref || listener.psd.finalize();\n    }\n}\nfunction getStack(promise, stacks, limit) {\n    if (stacks.length === limit)\n        return stacks;\n    var stack = \"\";\n    if (promise._state === false) {\n        var failure = promise._value, errorName, message;\n        if (failure != null) {\n            errorName = failure.name || \"Error\";\n            message = failure.message || failure;\n            stack = prettyStack(failure, 0);\n        }\n        else {\n            errorName = failure;\n            message = \"\";\n        }\n        stacks.push(errorName + (message ? \": \" + message : \"\") + stack);\n    }\n    if (debug) {\n        stack = prettyStack(promise._stackHolder, 2);\n        if (stack && stacks.indexOf(stack) === -1)\n            stacks.push(stack);\n        if (promise._prev)\n            getStack(promise._prev, stacks, limit);\n    }\n    return stacks;\n}\nfunction linkToPreviousPromise(promise, prev) {\n    var numPrev = prev ? prev._numPrev + 1 : 0;\n    if (numPrev < LONG_STACKS_CLIP_LIMIT) {\n        promise._prev = prev;\n        promise._numPrev = numPrev;\n    }\n}\nfunction physicalTick() {\n    beginMicroTickScope() && endMicroTickScope();\n}\nfunction beginMicroTickScope() {\n    var wasRootExec = isOutsideMicroTick;\n    isOutsideMicroTick = false;\n    needsNewPhysicalTick = false;\n    return wasRootExec;\n}\nfunction endMicroTickScope() {\n    var callbacks, i, l;\n    do {\n        while (microtickQueue.length > 0) {\n            callbacks = microtickQueue;\n            microtickQueue = [];\n            l = callbacks.length;\n            for (i = 0; i < l; ++i) {\n                var item = callbacks[i];\n                item[0].apply(null, item[1]);\n            }\n        }\n    } while (microtickQueue.length > 0);\n    isOutsideMicroTick = true;\n    needsNewPhysicalTick = true;\n}\nfunction finalizePhysicalTick() {\n    var unhandledErrs = unhandledErrors;\n    unhandledErrors = [];\n    unhandledErrs.forEach(function (p) {\n        p._PSD.onunhandled.call(null, p._value, p);\n    });\n    var finalizers = tickFinalizers.slice(0);\n    var i = finalizers.length;\n    while (i)\n        finalizers[--i]();\n}\nfunction run_at_end_of_this_or_next_physical_tick(fn) {\n    function finalizer() {\n        fn();\n        tickFinalizers.splice(tickFinalizers.indexOf(finalizer), 1);\n    }\n    tickFinalizers.push(finalizer);\n    ++numScheduledCalls;\n    asap(function () {\n        if (--numScheduledCalls === 0)\n            finalizePhysicalTick();\n    }, []);\n}\nfunction addPossiblyUnhandledError(promise) {\n    if (!unhandledErrors.some(function (p) { return p._value === promise._value; }))\n        unhandledErrors.push(promise);\n}\nfunction markErrorAsHandled(promise) {\n    var i = unhandledErrors.length;\n    while (i)\n        if (unhandledErrors[--i]._value === promise._value) {\n            unhandledErrors.splice(i, 1);\n            return;\n        }\n}\nfunction PromiseReject(reason) {\n    return new DexiePromise(INTERNAL, false, reason);\n}\nfunction wrap(fn, errorCatcher) {\n    var psd = PSD;\n    return function () {\n        var wasRootExec = beginMicroTickScope(), outerScope = PSD;\n        try {\n            switchToZone(psd, true);\n            return fn.apply(this, arguments);\n        }\n        catch (e) {\n            errorCatcher && errorCatcher(e);\n        }\n        finally {\n            switchToZone(outerScope, false);\n            if (wasRootExec)\n                endMicroTickScope();\n        }\n    };\n}\nvar task = { awaits: 0, echoes: 0, id: 0 };\nvar taskCounter = 0;\nvar zoneStack = [];\nvar zoneEchoes = 0;\nvar totalEchoes = 0;\nvar zone_id_counter = 0;\nfunction newScope(fn, props, a1, a2) {\n    var parent = PSD, psd = Object.create(parent);\n    psd.parent = parent;\n    psd.ref = 0;\n    psd.global = false;\n    psd.id = ++zone_id_counter;\n    var globalEnv = globalPSD.env;\n    psd.env = patchGlobalPromise ? {\n        Promise: DexiePromise,\n        PromiseProp: { value: DexiePromise, configurable: true, writable: true },\n        all: DexiePromise.all,\n        race: DexiePromise.race,\n        allSettled: DexiePromise.allSettled,\n        any: DexiePromise.any,\n        resolve: DexiePromise.resolve,\n        reject: DexiePromise.reject,\n        nthen: getPatchedPromiseThen(globalEnv.nthen, psd),\n        gthen: getPatchedPromiseThen(globalEnv.gthen, psd)\n    } : {};\n    if (props)\n        extend(psd, props);\n    ++parent.ref;\n    psd.finalize = function () {\n        --this.parent.ref || this.parent.finalize();\n    };\n    var rv = usePSD(psd, fn, a1, a2);\n    if (psd.ref === 0)\n        psd.finalize();\n    return rv;\n}\nfunction incrementExpectedAwaits() {\n    if (!task.id)\n        task.id = ++taskCounter;\n    ++task.awaits;\n    task.echoes += ZONE_ECHO_LIMIT;\n    return task.id;\n}\nfunction decrementExpectedAwaits() {\n    if (!task.awaits)\n        return false;\n    if (--task.awaits === 0)\n        task.id = 0;\n    task.echoes = task.awaits * ZONE_ECHO_LIMIT;\n    return true;\n}\nif (('' + nativePromiseThen).indexOf('[native code]') === -1) {\n    incrementExpectedAwaits = decrementExpectedAwaits = nop;\n}\nfunction onPossibleParallellAsync(possiblePromise) {\n    if (task.echoes && possiblePromise && possiblePromise.constructor === NativePromise) {\n        incrementExpectedAwaits();\n        return possiblePromise.then(function (x) {\n            decrementExpectedAwaits();\n            return x;\n        }, function (e) {\n            decrementExpectedAwaits();\n            return rejection(e);\n        });\n    }\n    return possiblePromise;\n}\nfunction zoneEnterEcho(targetZone) {\n    ++totalEchoes;\n    if (!task.echoes || --task.echoes === 0) {\n        task.echoes = task.id = 0;\n    }\n    zoneStack.push(PSD);\n    switchToZone(targetZone, true);\n}\nfunction zoneLeaveEcho() {\n    var zone = zoneStack[zoneStack.length - 1];\n    zoneStack.pop();\n    switchToZone(zone, false);\n}\nfunction switchToZone(targetZone, bEnteringZone) {\n    var currentZone = PSD;\n    if (bEnteringZone ? task.echoes && (!zoneEchoes++ || targetZone !== PSD) : zoneEchoes && (!--zoneEchoes || targetZone !== PSD)) {\n        enqueueNativeMicroTask(bEnteringZone ? zoneEnterEcho.bind(null, targetZone) : zoneLeaveEcho);\n    }\n    if (targetZone === PSD)\n        return;\n    PSD = targetZone;\n    if (currentZone === globalPSD)\n        globalPSD.env = snapShot();\n    if (patchGlobalPromise) {\n        var GlobalPromise_1 = globalPSD.env.Promise;\n        var targetEnv = targetZone.env;\n        nativePromiseProto.then = targetEnv.nthen;\n        GlobalPromise_1.prototype.then = targetEnv.gthen;\n        if (currentZone.global || targetZone.global) {\n            Object.defineProperty(_global, 'Promise', targetEnv.PromiseProp);\n            GlobalPromise_1.all = targetEnv.all;\n            GlobalPromise_1.race = targetEnv.race;\n            GlobalPromise_1.resolve = targetEnv.resolve;\n            GlobalPromise_1.reject = targetEnv.reject;\n            if (targetEnv.allSettled)\n                GlobalPromise_1.allSettled = targetEnv.allSettled;\n            if (targetEnv.any)\n                GlobalPromise_1.any = targetEnv.any;\n        }\n    }\n}\nfunction snapShot() {\n    var GlobalPromise = _global.Promise;\n    return patchGlobalPromise ? {\n        Promise: GlobalPromise,\n        PromiseProp: Object.getOwnPropertyDescriptor(_global, \"Promise\"),\n        all: GlobalPromise.all,\n        race: GlobalPromise.race,\n        allSettled: GlobalPromise.allSettled,\n        any: GlobalPromise.any,\n        resolve: GlobalPromise.resolve,\n        reject: GlobalPromise.reject,\n        nthen: nativePromiseProto.then,\n        gthen: GlobalPromise.prototype.then\n    } : {};\n}\nfunction usePSD(psd, fn, a1, a2, a3) {\n    var outerScope = PSD;\n    try {\n        switchToZone(psd, true);\n        return fn(a1, a2, a3);\n    }\n    finally {\n        switchToZone(outerScope, false);\n    }\n}\nfunction enqueueNativeMicroTask(job) {\n    nativePromiseThen.call(resolvedNativePromise, job);\n}\nfunction nativeAwaitCompatibleWrap(fn, zone, possibleAwait, cleanup) {\n    return typeof fn !== 'function' ? fn : function () {\n        var outerZone = PSD;\n        if (possibleAwait)\n            incrementExpectedAwaits();\n        switchToZone(zone, true);\n        try {\n            return fn.apply(this, arguments);\n        }\n        finally {\n            switchToZone(outerZone, false);\n            if (cleanup)\n                enqueueNativeMicroTask(decrementExpectedAwaits);\n        }\n    };\n}\nfunction getPatchedPromiseThen(origThen, zone) {\n    return function (onResolved, onRejected) {\n        return origThen.call(this, nativeAwaitCompatibleWrap(onResolved, zone), nativeAwaitCompatibleWrap(onRejected, zone));\n    };\n}\nvar UNHANDLEDREJECTION = \"unhandledrejection\";\nfunction globalError(err, promise) {\n    var rv;\n    try {\n        rv = promise.onuncatched(err);\n    }\n    catch (e) { }\n    if (rv !== false)\n        try {\n            var event, eventData = { promise: promise, reason: err };\n            if (_global.document && document.createEvent) {\n                event = document.createEvent('Event');\n                event.initEvent(UNHANDLEDREJECTION, true, true);\n                extend(event, eventData);\n            }\n            else if (_global.CustomEvent) {\n                event = new CustomEvent(UNHANDLEDREJECTION, { detail: eventData });\n                extend(event, eventData);\n            }\n            if (event && _global.dispatchEvent) {\n                dispatchEvent(event);\n                if (!_global.PromiseRejectionEvent && _global.onunhandledrejection)\n                    try {\n                        _global.onunhandledrejection(event);\n                    }\n                    catch (_) { }\n            }\n            if (debug && event && !event.defaultPrevented) {\n                console.warn(\"Unhandled rejection: \" + (err.stack || err));\n            }\n        }\n        catch (e) { }\n}\nvar rejection = DexiePromise.reject;\n\nfunction tempTransaction(db, mode, storeNames, fn) {\n    if (!db.idbdb || (!db._state.openComplete && (!PSD.letThrough && !db._vip))) {\n        if (db._state.openComplete) {\n            return rejection(new exceptions.DatabaseClosed(db._state.dbOpenError));\n        }\n        if (!db._state.isBeingOpened) {\n            if (!db._options.autoOpen)\n                return rejection(new exceptions.DatabaseClosed());\n            db.open().catch(nop);\n        }\n        return db._state.dbReadyPromise.then(function () { return tempTransaction(db, mode, storeNames, fn); });\n    }\n    else {\n        var trans = db._createTransaction(mode, storeNames, db._dbSchema);\n        try {\n            trans.create();\n            db._state.PR1398_maxLoop = 3;\n        }\n        catch (ex) {\n            if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n                console.warn('Dexie: Need to reopen db');\n                db._close();\n                return db.open().then(function () { return tempTransaction(db, mode, storeNames, fn); });\n            }\n            return rejection(ex);\n        }\n        return trans._promise(mode, function (resolve, reject) {\n            return newScope(function () {\n                PSD.trans = trans;\n                return fn(resolve, reject, trans);\n            });\n        }).then(function (result) {\n            return trans._completion.then(function () { return result; });\n        });\n    }\n}\n\nvar DEXIE_VERSION = '3.2.4';\nvar maxString = String.fromCharCode(65535);\nvar minKey = -Infinity;\nvar INVALID_KEY_ARGUMENT = \"Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.\";\nvar STRING_EXPECTED = \"String expected.\";\nvar connections = [];\nvar isIEOrEdge = typeof navigator !== 'undefined' && /(MSIE|Trident|Edge)/.test(navigator.userAgent);\nvar hasIEDeleteObjectStoreBug = isIEOrEdge;\nvar hangsOnDeleteLargeKeyRange = isIEOrEdge;\nvar dexieStackFrameFilter = function (frame) { return !/(dexie\\.js|dexie\\.min\\.js)/.test(frame); };\nvar DBNAMES_DB = '__dbnames';\nvar READONLY = 'readonly';\nvar READWRITE = 'readwrite';\n\nfunction combine(filter1, filter2) {\n    return filter1 ?\n        filter2 ?\n            function () { return filter1.apply(this, arguments) && filter2.apply(this, arguments); } :\n            filter1 :\n        filter2;\n}\n\nvar AnyRange = {\n    type: 3 ,\n    lower: -Infinity,\n    lowerOpen: false,\n    upper: [[]],\n    upperOpen: false\n};\n\nfunction workaroundForUndefinedPrimKey(keyPath) {\n    return typeof keyPath === \"string\" && !/\\./.test(keyPath)\n        ? function (obj) {\n            if (obj[keyPath] === undefined && (keyPath in obj)) {\n                obj = deepClone(obj);\n                delete obj[keyPath];\n            }\n            return obj;\n        }\n        : function (obj) { return obj; };\n}\n\nvar Table =  (function () {\n    function Table() {\n    }\n    Table.prototype._trans = function (mode, fn, writeLocked) {\n        var trans = this._tx || PSD.trans;\n        var tableName = this.name;\n        function checkTableInTransaction(resolve, reject, trans) {\n            if (!trans.schema[tableName])\n                throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n            return fn(trans.idbtrans, trans);\n        }\n        var wasRootExec = beginMicroTickScope();\n        try {\n            return trans && trans.db === this.db ?\n                trans === PSD.trans ?\n                    trans._promise(mode, checkTableInTransaction, writeLocked) :\n                    newScope(function () { return trans._promise(mode, checkTableInTransaction, writeLocked); }, { trans: trans, transless: PSD.transless || PSD }) :\n                tempTransaction(this.db, mode, [this.name], checkTableInTransaction);\n        }\n        finally {\n            if (wasRootExec)\n                endMicroTickScope();\n        }\n    };\n    Table.prototype.get = function (keyOrCrit, cb) {\n        var _this = this;\n        if (keyOrCrit && keyOrCrit.constructor === Object)\n            return this.where(keyOrCrit).first(cb);\n        return this._trans('readonly', function (trans) {\n            return _this.core.get({ trans: trans, key: keyOrCrit })\n                .then(function (res) { return _this.hook.reading.fire(res); });\n        }).then(cb);\n    };\n    Table.prototype.where = function (indexOrCrit) {\n        if (typeof indexOrCrit === 'string')\n            return new this.db.WhereClause(this, indexOrCrit);\n        if (isArray(indexOrCrit))\n            return new this.db.WhereClause(this, \"[\" + indexOrCrit.join('+') + \"]\");\n        var keyPaths = keys(indexOrCrit);\n        if (keyPaths.length === 1)\n            return this\n                .where(keyPaths[0])\n                .equals(indexOrCrit[keyPaths[0]]);\n        var compoundIndex = this.schema.indexes.concat(this.schema.primKey).filter(function (ix) {\n            return ix.compound &&\n                keyPaths.every(function (keyPath) { return ix.keyPath.indexOf(keyPath) >= 0; }) &&\n                ix.keyPath.every(function (keyPath) { return keyPaths.indexOf(keyPath) >= 0; });\n        })[0];\n        if (compoundIndex && this.db._maxKey !== maxString)\n            return this\n                .where(compoundIndex.name)\n                .equals(compoundIndex.keyPath.map(function (kp) { return indexOrCrit[kp]; }));\n        if (!compoundIndex && debug)\n            console.warn(\"The query \" + JSON.stringify(indexOrCrit) + \" on \" + this.name + \" would benefit of a \" +\n                (\"compound index [\" + keyPaths.join('+') + \"]\"));\n        var idxByName = this.schema.idxByName;\n        var idb = this.db._deps.indexedDB;\n        function equals(a, b) {\n            try {\n                return idb.cmp(a, b) === 0;\n            }\n            catch (e) {\n                return false;\n            }\n        }\n        var _a = keyPaths.reduce(function (_a, keyPath) {\n            var prevIndex = _a[0], prevFilterFn = _a[1];\n            var index = idxByName[keyPath];\n            var value = indexOrCrit[keyPath];\n            return [\n                prevIndex || index,\n                prevIndex || !index ?\n                    combine(prevFilterFn, index && index.multi ?\n                        function (x) {\n                            var prop = getByKeyPath(x, keyPath);\n                            return isArray(prop) && prop.some(function (item) { return equals(value, item); });\n                        } : function (x) { return equals(value, getByKeyPath(x, keyPath)); })\n                    : prevFilterFn\n            ];\n        }, [null, null]), idx = _a[0], filterFunction = _a[1];\n        return idx ?\n            this.where(idx.name).equals(indexOrCrit[idx.keyPath])\n                .filter(filterFunction) :\n            compoundIndex ?\n                this.filter(filterFunction) :\n                this.where(keyPaths).equals('');\n    };\n    Table.prototype.filter = function (filterFunction) {\n        return this.toCollection().and(filterFunction);\n    };\n    Table.prototype.count = function (thenShortcut) {\n        return this.toCollection().count(thenShortcut);\n    };\n    Table.prototype.offset = function (offset) {\n        return this.toCollection().offset(offset);\n    };\n    Table.prototype.limit = function (numRows) {\n        return this.toCollection().limit(numRows);\n    };\n    Table.prototype.each = function (callback) {\n        return this.toCollection().each(callback);\n    };\n    Table.prototype.toArray = function (thenShortcut) {\n        return this.toCollection().toArray(thenShortcut);\n    };\n    Table.prototype.toCollection = function () {\n        return new this.db.Collection(new this.db.WhereClause(this));\n    };\n    Table.prototype.orderBy = function (index) {\n        return new this.db.Collection(new this.db.WhereClause(this, isArray(index) ?\n            \"[\" + index.join('+') + \"]\" :\n            index));\n    };\n    Table.prototype.reverse = function () {\n        return this.toCollection().reverse();\n    };\n    Table.prototype.mapToClass = function (constructor) {\n        this.schema.mappedClass = constructor;\n        var readHook = function (obj) {\n            if (!obj)\n                return obj;\n            var res = Object.create(constructor.prototype);\n            for (var m in obj)\n                if (hasOwn(obj, m))\n                    try {\n                        res[m] = obj[m];\n                    }\n                    catch (_) { }\n            return res;\n        };\n        if (this.schema.readHook) {\n            this.hook.reading.unsubscribe(this.schema.readHook);\n        }\n        this.schema.readHook = readHook;\n        this.hook(\"reading\", readHook);\n        return constructor;\n    };\n    Table.prototype.defineClass = function () {\n        function Class(content) {\n            extend(this, content);\n        }\n        return this.mapToClass(Class);\n    };\n    Table.prototype.add = function (obj, key) {\n        var _this = this;\n        var _a = this.schema.primKey, auto = _a.auto, keyPath = _a.keyPath;\n        var objToAdd = obj;\n        if (keyPath && auto) {\n            objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n        }\n        return this._trans('readwrite', function (trans) {\n            return _this.core.mutate({ trans: trans, type: 'add', keys: key != null ? [key] : null, values: [objToAdd] });\n        }).then(function (res) { return res.numFailures ? DexiePromise.reject(res.failures[0]) : res.lastResult; })\n            .then(function (lastResult) {\n            if (keyPath) {\n                try {\n                    setByKeyPath(obj, keyPath, lastResult);\n                }\n                catch (_) { }\n            }\n            return lastResult;\n        });\n    };\n    Table.prototype.update = function (keyOrObject, modifications) {\n        if (typeof keyOrObject === 'object' && !isArray(keyOrObject)) {\n            var key = getByKeyPath(keyOrObject, this.schema.primKey.keyPath);\n            if (key === undefined)\n                return rejection(new exceptions.InvalidArgument(\"Given object does not contain its primary key\"));\n            try {\n                if (typeof modifications !== \"function\") {\n                    keys(modifications).forEach(function (keyPath) {\n                        setByKeyPath(keyOrObject, keyPath, modifications[keyPath]);\n                    });\n                }\n                else {\n                    modifications(keyOrObject, { value: keyOrObject, primKey: key });\n                }\n            }\n            catch (_a) {\n            }\n            return this.where(\":id\").equals(key).modify(modifications);\n        }\n        else {\n            return this.where(\":id\").equals(keyOrObject).modify(modifications);\n        }\n    };\n    Table.prototype.put = function (obj, key) {\n        var _this = this;\n        var _a = this.schema.primKey, auto = _a.auto, keyPath = _a.keyPath;\n        var objToAdd = obj;\n        if (keyPath && auto) {\n            objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n        }\n        return this._trans('readwrite', function (trans) { return _this.core.mutate({ trans: trans, type: 'put', values: [objToAdd], keys: key != null ? [key] : null }); })\n            .then(function (res) { return res.numFailures ? DexiePromise.reject(res.failures[0]) : res.lastResult; })\n            .then(function (lastResult) {\n            if (keyPath) {\n                try {\n                    setByKeyPath(obj, keyPath, lastResult);\n                }\n                catch (_) { }\n            }\n            return lastResult;\n        });\n    };\n    Table.prototype.delete = function (key) {\n        var _this = this;\n        return this._trans('readwrite', function (trans) { return _this.core.mutate({ trans: trans, type: 'delete', keys: [key] }); })\n            .then(function (res) { return res.numFailures ? DexiePromise.reject(res.failures[0]) : undefined; });\n    };\n    Table.prototype.clear = function () {\n        var _this = this;\n        return this._trans('readwrite', function (trans) { return _this.core.mutate({ trans: trans, type: 'deleteRange', range: AnyRange }); })\n            .then(function (res) { return res.numFailures ? DexiePromise.reject(res.failures[0]) : undefined; });\n    };\n    Table.prototype.bulkGet = function (keys) {\n        var _this = this;\n        return this._trans('readonly', function (trans) {\n            return _this.core.getMany({\n                keys: keys,\n                trans: trans\n            }).then(function (result) { return result.map(function (res) { return _this.hook.reading.fire(res); }); });\n        });\n    };\n    Table.prototype.bulkAdd = function (objects, keysOrOptions, options) {\n        var _this = this;\n        var keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n        options = options || (keys ? undefined : keysOrOptions);\n        var wantResults = options ? options.allKeys : undefined;\n        return this._trans('readwrite', function (trans) {\n            var _a = _this.schema.primKey, auto = _a.auto, keyPath = _a.keyPath;\n            if (keyPath && keys)\n                throw new exceptions.InvalidArgument(\"bulkAdd(): keys argument invalid on tables with inbound keys\");\n            if (keys && keys.length !== objects.length)\n                throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n            var numObjects = objects.length;\n            var objectsToAdd = keyPath && auto ?\n                objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n                objects;\n            return _this.core.mutate({ trans: trans, type: 'add', keys: keys, values: objectsToAdd, wantResults: wantResults })\n                .then(function (_a) {\n                var numFailures = _a.numFailures, results = _a.results, lastResult = _a.lastResult, failures = _a.failures;\n                var result = wantResults ? results : lastResult;\n                if (numFailures === 0)\n                    return result;\n                throw new BulkError(_this.name + \".bulkAdd(): \" + numFailures + \" of \" + numObjects + \" operations failed\", failures);\n            });\n        });\n    };\n    Table.prototype.bulkPut = function (objects, keysOrOptions, options) {\n        var _this = this;\n        var keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n        options = options || (keys ? undefined : keysOrOptions);\n        var wantResults = options ? options.allKeys : undefined;\n        return this._trans('readwrite', function (trans) {\n            var _a = _this.schema.primKey, auto = _a.auto, keyPath = _a.keyPath;\n            if (keyPath && keys)\n                throw new exceptions.InvalidArgument(\"bulkPut(): keys argument invalid on tables with inbound keys\");\n            if (keys && keys.length !== objects.length)\n                throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n            var numObjects = objects.length;\n            var objectsToPut = keyPath && auto ?\n                objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n                objects;\n            return _this.core.mutate({ trans: trans, type: 'put', keys: keys, values: objectsToPut, wantResults: wantResults })\n                .then(function (_a) {\n                var numFailures = _a.numFailures, results = _a.results, lastResult = _a.lastResult, failures = _a.failures;\n                var result = wantResults ? results : lastResult;\n                if (numFailures === 0)\n                    return result;\n                throw new BulkError(_this.name + \".bulkPut(): \" + numFailures + \" of \" + numObjects + \" operations failed\", failures);\n            });\n        });\n    };\n    Table.prototype.bulkDelete = function (keys) {\n        var _this = this;\n        var numKeys = keys.length;\n        return this._trans('readwrite', function (trans) {\n            return _this.core.mutate({ trans: trans, type: 'delete', keys: keys });\n        }).then(function (_a) {\n            var numFailures = _a.numFailures, lastResult = _a.lastResult, failures = _a.failures;\n            if (numFailures === 0)\n                return lastResult;\n            throw new BulkError(_this.name + \".bulkDelete(): \" + numFailures + \" of \" + numKeys + \" operations failed\", failures);\n        });\n    };\n    return Table;\n}());\n\nfunction Events(ctx) {\n    var evs = {};\n    var rv = function (eventName, subscriber) {\n        if (subscriber) {\n            var i = arguments.length, args = new Array(i - 1);\n            while (--i)\n                args[i - 1] = arguments[i];\n            evs[eventName].subscribe.apply(null, args);\n            return ctx;\n        }\n        else if (typeof (eventName) === 'string') {\n            return evs[eventName];\n        }\n    };\n    rv.addEventType = add;\n    for (var i = 1, l = arguments.length; i < l; ++i) {\n        add(arguments[i]);\n    }\n    return rv;\n    function add(eventName, chainFunction, defaultFunction) {\n        if (typeof eventName === 'object')\n            return addConfiguredEvents(eventName);\n        if (!chainFunction)\n            chainFunction = reverseStoppableEventChain;\n        if (!defaultFunction)\n            defaultFunction = nop;\n        var context = {\n            subscribers: [],\n            fire: defaultFunction,\n            subscribe: function (cb) {\n                if (context.subscribers.indexOf(cb) === -1) {\n                    context.subscribers.push(cb);\n                    context.fire = chainFunction(context.fire, cb);\n                }\n            },\n            unsubscribe: function (cb) {\n                context.subscribers = context.subscribers.filter(function (fn) { return fn !== cb; });\n                context.fire = context.subscribers.reduce(chainFunction, defaultFunction);\n            }\n        };\n        evs[eventName] = rv[eventName] = context;\n        return context;\n    }\n    function addConfiguredEvents(cfg) {\n        keys(cfg).forEach(function (eventName) {\n            var args = cfg[eventName];\n            if (isArray(args)) {\n                add(eventName, cfg[eventName][0], cfg[eventName][1]);\n            }\n            else if (args === 'asap') {\n                var context = add(eventName, mirror, function fire() {\n                    var i = arguments.length, args = new Array(i);\n                    while (i--)\n                        args[i] = arguments[i];\n                    context.subscribers.forEach(function (fn) {\n                        asap$1(function fireEvent() {\n                            fn.apply(null, args);\n                        });\n                    });\n                });\n            }\n            else\n                throw new exceptions.InvalidArgument(\"Invalid event config\");\n        });\n    }\n}\n\nfunction makeClassConstructor(prototype, constructor) {\n    derive(constructor).from({ prototype: prototype });\n    return constructor;\n}\n\nfunction createTableConstructor(db) {\n    return makeClassConstructor(Table.prototype, function Table(name, tableSchema, trans) {\n        this.db = db;\n        this._tx = trans;\n        this.name = name;\n        this.schema = tableSchema;\n        this.hook = db._allTables[name] ? db._allTables[name].hook : Events(null, {\n            \"creating\": [hookCreatingChain, nop],\n            \"reading\": [pureFunctionChain, mirror],\n            \"updating\": [hookUpdatingChain, nop],\n            \"deleting\": [hookDeletingChain, nop]\n        });\n    });\n}\n\nfunction isPlainKeyRange(ctx, ignoreLimitFilter) {\n    return !(ctx.filter || ctx.algorithm || ctx.or) &&\n        (ignoreLimitFilter ? ctx.justLimit : !ctx.replayFilter);\n}\nfunction addFilter(ctx, fn) {\n    ctx.filter = combine(ctx.filter, fn);\n}\nfunction addReplayFilter(ctx, factory, isLimitFilter) {\n    var curr = ctx.replayFilter;\n    ctx.replayFilter = curr ? function () { return combine(curr(), factory()); } : factory;\n    ctx.justLimit = isLimitFilter && !curr;\n}\nfunction addMatchFilter(ctx, fn) {\n    ctx.isMatch = combine(ctx.isMatch, fn);\n}\nfunction getIndexOrStore(ctx, coreSchema) {\n    if (ctx.isPrimKey)\n        return coreSchema.primaryKey;\n    var index = coreSchema.getIndexByKeyPath(ctx.index);\n    if (!index)\n        throw new exceptions.Schema(\"KeyPath \" + ctx.index + \" on object store \" + coreSchema.name + \" is not indexed\");\n    return index;\n}\nfunction openCursor(ctx, coreTable, trans) {\n    var index = getIndexOrStore(ctx, coreTable.schema);\n    return coreTable.openCursor({\n        trans: trans,\n        values: !ctx.keysOnly,\n        reverse: ctx.dir === 'prev',\n        unique: !!ctx.unique,\n        query: {\n            index: index,\n            range: ctx.range\n        }\n    });\n}\nfunction iter(ctx, fn, coreTrans, coreTable) {\n    var filter = ctx.replayFilter ? combine(ctx.filter, ctx.replayFilter()) : ctx.filter;\n    if (!ctx.or) {\n        return iterate(openCursor(ctx, coreTable, coreTrans), combine(ctx.algorithm, filter), fn, !ctx.keysOnly && ctx.valueMapper);\n    }\n    else {\n        var set_1 = {};\n        var union = function (item, cursor, advance) {\n            if (!filter || filter(cursor, advance, function (result) { return cursor.stop(result); }, function (err) { return cursor.fail(err); })) {\n                var primaryKey = cursor.primaryKey;\n                var key = '' + primaryKey;\n                if (key === '[object ArrayBuffer]')\n                    key = '' + new Uint8Array(primaryKey);\n                if (!hasOwn(set_1, key)) {\n                    set_1[key] = true;\n                    fn(item, cursor, advance);\n                }\n            }\n        };\n        return Promise.all([\n            ctx.or._iterate(union, coreTrans),\n            iterate(openCursor(ctx, coreTable, coreTrans), ctx.algorithm, union, !ctx.keysOnly && ctx.valueMapper)\n        ]);\n    }\n}\nfunction iterate(cursorPromise, filter, fn, valueMapper) {\n    var mappedFn = valueMapper ? function (x, c, a) { return fn(valueMapper(x), c, a); } : fn;\n    var wrappedFn = wrap(mappedFn);\n    return cursorPromise.then(function (cursor) {\n        if (cursor) {\n            return cursor.start(function () {\n                var c = function () { return cursor.continue(); };\n                if (!filter || filter(cursor, function (advancer) { return c = advancer; }, function (val) { cursor.stop(val); c = nop; }, function (e) { cursor.fail(e); c = nop; }))\n                    wrappedFn(cursor.value, cursor, function (advancer) { return c = advancer; });\n                c();\n            });\n        }\n    });\n}\n\nfunction cmp(a, b) {\n    try {\n        var ta = type(a);\n        var tb = type(b);\n        if (ta !== tb) {\n            if (ta === 'Array')\n                return 1;\n            if (tb === 'Array')\n                return -1;\n            if (ta === 'binary')\n                return 1;\n            if (tb === 'binary')\n                return -1;\n            if (ta === 'string')\n                return 1;\n            if (tb === 'string')\n                return -1;\n            if (ta === 'Date')\n                return 1;\n            if (tb !== 'Date')\n                return NaN;\n            return -1;\n        }\n        switch (ta) {\n            case 'number':\n            case 'Date':\n            case 'string':\n                return a > b ? 1 : a < b ? -1 : 0;\n            case 'binary': {\n                return compareUint8Arrays(getUint8Array(a), getUint8Array(b));\n            }\n            case 'Array':\n                return compareArrays(a, b);\n        }\n    }\n    catch (_a) { }\n    return NaN;\n}\nfunction compareArrays(a, b) {\n    var al = a.length;\n    var bl = b.length;\n    var l = al < bl ? al : bl;\n    for (var i = 0; i < l; ++i) {\n        var res = cmp(a[i], b[i]);\n        if (res !== 0)\n            return res;\n    }\n    return al === bl ? 0 : al < bl ? -1 : 1;\n}\nfunction compareUint8Arrays(a, b) {\n    var al = a.length;\n    var bl = b.length;\n    var l = al < bl ? al : bl;\n    for (var i = 0; i < l; ++i) {\n        if (a[i] !== b[i])\n            return a[i] < b[i] ? -1 : 1;\n    }\n    return al === bl ? 0 : al < bl ? -1 : 1;\n}\nfunction type(x) {\n    var t = typeof x;\n    if (t !== 'object')\n        return t;\n    if (ArrayBuffer.isView(x))\n        return 'binary';\n    var tsTag = toStringTag(x);\n    return tsTag === 'ArrayBuffer' ? 'binary' : tsTag;\n}\nfunction getUint8Array(a) {\n    if (a instanceof Uint8Array)\n        return a;\n    if (ArrayBuffer.isView(a))\n        return new Uint8Array(a.buffer, a.byteOffset, a.byteLength);\n    return new Uint8Array(a);\n}\n\nvar Collection =  (function () {\n    function Collection() {\n    }\n    Collection.prototype._read = function (fn, cb) {\n        var ctx = this._ctx;\n        return ctx.error ?\n            ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n            ctx.table._trans('readonly', fn).then(cb);\n    };\n    Collection.prototype._write = function (fn) {\n        var ctx = this._ctx;\n        return ctx.error ?\n            ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n            ctx.table._trans('readwrite', fn, \"locked\");\n    };\n    Collection.prototype._addAlgorithm = function (fn) {\n        var ctx = this._ctx;\n        ctx.algorithm = combine(ctx.algorithm, fn);\n    };\n    Collection.prototype._iterate = function (fn, coreTrans) {\n        return iter(this._ctx, fn, coreTrans, this._ctx.table.core);\n    };\n    Collection.prototype.clone = function (props) {\n        var rv = Object.create(this.constructor.prototype), ctx = Object.create(this._ctx);\n        if (props)\n            extend(ctx, props);\n        rv._ctx = ctx;\n        return rv;\n    };\n    Collection.prototype.raw = function () {\n        this._ctx.valueMapper = null;\n        return this;\n    };\n    Collection.prototype.each = function (fn) {\n        var ctx = this._ctx;\n        return this._read(function (trans) { return iter(ctx, fn, trans, ctx.table.core); });\n    };\n    Collection.prototype.count = function (cb) {\n        var _this = this;\n        return this._read(function (trans) {\n            var ctx = _this._ctx;\n            var coreTable = ctx.table.core;\n            if (isPlainKeyRange(ctx, true)) {\n                return coreTable.count({\n                    trans: trans,\n                    query: {\n                        index: getIndexOrStore(ctx, coreTable.schema),\n                        range: ctx.range\n                    }\n                }).then(function (count) { return Math.min(count, ctx.limit); });\n            }\n            else {\n                var count = 0;\n                return iter(ctx, function () { ++count; return false; }, trans, coreTable)\n                    .then(function () { return count; });\n            }\n        }).then(cb);\n    };\n    Collection.prototype.sortBy = function (keyPath, cb) {\n        var parts = keyPath.split('.').reverse(), lastPart = parts[0], lastIndex = parts.length - 1;\n        function getval(obj, i) {\n            if (i)\n                return getval(obj[parts[i]], i - 1);\n            return obj[lastPart];\n        }\n        var order = this._ctx.dir === \"next\" ? 1 : -1;\n        function sorter(a, b) {\n            var aVal = getval(a, lastIndex), bVal = getval(b, lastIndex);\n            return aVal < bVal ? -order : aVal > bVal ? order : 0;\n        }\n        return this.toArray(function (a) {\n            return a.sort(sorter);\n        }).then(cb);\n    };\n    Collection.prototype.toArray = function (cb) {\n        var _this = this;\n        return this._read(function (trans) {\n            var ctx = _this._ctx;\n            if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n                var valueMapper_1 = ctx.valueMapper;\n                var index = getIndexOrStore(ctx, ctx.table.core.schema);\n                return ctx.table.core.query({\n                    trans: trans,\n                    limit: ctx.limit,\n                    values: true,\n                    query: {\n                        index: index,\n                        range: ctx.range\n                    }\n                }).then(function (_a) {\n                    var result = _a.result;\n                    return valueMapper_1 ? result.map(valueMapper_1) : result;\n                });\n            }\n            else {\n                var a_1 = [];\n                return iter(ctx, function (item) { return a_1.push(item); }, trans, ctx.table.core).then(function () { return a_1; });\n            }\n        }, cb);\n    };\n    Collection.prototype.offset = function (offset) {\n        var ctx = this._ctx;\n        if (offset <= 0)\n            return this;\n        ctx.offset += offset;\n        if (isPlainKeyRange(ctx)) {\n            addReplayFilter(ctx, function () {\n                var offsetLeft = offset;\n                return function (cursor, advance) {\n                    if (offsetLeft === 0)\n                        return true;\n                    if (offsetLeft === 1) {\n                        --offsetLeft;\n                        return false;\n                    }\n                    advance(function () {\n                        cursor.advance(offsetLeft);\n                        offsetLeft = 0;\n                    });\n                    return false;\n                };\n            });\n        }\n        else {\n            addReplayFilter(ctx, function () {\n                var offsetLeft = offset;\n                return function () { return (--offsetLeft < 0); };\n            });\n        }\n        return this;\n    };\n    Collection.prototype.limit = function (numRows) {\n        this._ctx.limit = Math.min(this._ctx.limit, numRows);\n        addReplayFilter(this._ctx, function () {\n            var rowsLeft = numRows;\n            return function (cursor, advance, resolve) {\n                if (--rowsLeft <= 0)\n                    advance(resolve);\n                return rowsLeft >= 0;\n            };\n        }, true);\n        return this;\n    };\n    Collection.prototype.until = function (filterFunction, bIncludeStopEntry) {\n        addFilter(this._ctx, function (cursor, advance, resolve) {\n            if (filterFunction(cursor.value)) {\n                advance(resolve);\n                return bIncludeStopEntry;\n            }\n            else {\n                return true;\n            }\n        });\n        return this;\n    };\n    Collection.prototype.first = function (cb) {\n        return this.limit(1).toArray(function (a) { return a[0]; }).then(cb);\n    };\n    Collection.prototype.last = function (cb) {\n        return this.reverse().first(cb);\n    };\n    Collection.prototype.filter = function (filterFunction) {\n        addFilter(this._ctx, function (cursor) {\n            return filterFunction(cursor.value);\n        });\n        addMatchFilter(this._ctx, filterFunction);\n        return this;\n    };\n    Collection.prototype.and = function (filter) {\n        return this.filter(filter);\n    };\n    Collection.prototype.or = function (indexName) {\n        return new this.db.WhereClause(this._ctx.table, indexName, this);\n    };\n    Collection.prototype.reverse = function () {\n        this._ctx.dir = (this._ctx.dir === \"prev\" ? \"next\" : \"prev\");\n        if (this._ondirectionchange)\n            this._ondirectionchange(this._ctx.dir);\n        return this;\n    };\n    Collection.prototype.desc = function () {\n        return this.reverse();\n    };\n    Collection.prototype.eachKey = function (cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        return this.each(function (val, cursor) { cb(cursor.key, cursor); });\n    };\n    Collection.prototype.eachUniqueKey = function (cb) {\n        this._ctx.unique = \"unique\";\n        return this.eachKey(cb);\n    };\n    Collection.prototype.eachPrimaryKey = function (cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        return this.each(function (val, cursor) { cb(cursor.primaryKey, cursor); });\n    };\n    Collection.prototype.keys = function (cb) {\n        var ctx = this._ctx;\n        ctx.keysOnly = !ctx.isMatch;\n        var a = [];\n        return this.each(function (item, cursor) {\n            a.push(cursor.key);\n        }).then(function () {\n            return a;\n        }).then(cb);\n    };\n    Collection.prototype.primaryKeys = function (cb) {\n        var ctx = this._ctx;\n        if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n            return this._read(function (trans) {\n                var index = getIndexOrStore(ctx, ctx.table.core.schema);\n                return ctx.table.core.query({\n                    trans: trans,\n                    values: false,\n                    limit: ctx.limit,\n                    query: {\n                        index: index,\n                        range: ctx.range\n                    }\n                });\n            }).then(function (_a) {\n                var result = _a.result;\n                return result;\n            }).then(cb);\n        }\n        ctx.keysOnly = !ctx.isMatch;\n        var a = [];\n        return this.each(function (item, cursor) {\n            a.push(cursor.primaryKey);\n        }).then(function () {\n            return a;\n        }).then(cb);\n    };\n    Collection.prototype.uniqueKeys = function (cb) {\n        this._ctx.unique = \"unique\";\n        return this.keys(cb);\n    };\n    Collection.prototype.firstKey = function (cb) {\n        return this.limit(1).keys(function (a) { return a[0]; }).then(cb);\n    };\n    Collection.prototype.lastKey = function (cb) {\n        return this.reverse().firstKey(cb);\n    };\n    Collection.prototype.distinct = function () {\n        var ctx = this._ctx, idx = ctx.index && ctx.table.schema.idxByName[ctx.index];\n        if (!idx || !idx.multi)\n            return this;\n        var set = {};\n        addFilter(this._ctx, function (cursor) {\n            var strKey = cursor.primaryKey.toString();\n            var found = hasOwn(set, strKey);\n            set[strKey] = true;\n            return !found;\n        });\n        return this;\n    };\n    Collection.prototype.modify = function (changes) {\n        var _this = this;\n        var ctx = this._ctx;\n        return this._write(function (trans) {\n            var modifyer;\n            if (typeof changes === 'function') {\n                modifyer = changes;\n            }\n            else {\n                var keyPaths = keys(changes);\n                var numKeys = keyPaths.length;\n                modifyer = function (item) {\n                    var anythingModified = false;\n                    for (var i = 0; i < numKeys; ++i) {\n                        var keyPath = keyPaths[i], val = changes[keyPath];\n                        if (getByKeyPath(item, keyPath) !== val) {\n                            setByKeyPath(item, keyPath, val);\n                            anythingModified = true;\n                        }\n                    }\n                    return anythingModified;\n                };\n            }\n            var coreTable = ctx.table.core;\n            var _a = coreTable.schema.primaryKey, outbound = _a.outbound, extractKey = _a.extractKey;\n            var limit = _this.db._options.modifyChunkSize || 200;\n            var totalFailures = [];\n            var successCount = 0;\n            var failedKeys = [];\n            var applyMutateResult = function (expectedCount, res) {\n                var failures = res.failures, numFailures = res.numFailures;\n                successCount += expectedCount - numFailures;\n                for (var _i = 0, _a = keys(failures); _i < _a.length; _i++) {\n                    var pos = _a[_i];\n                    totalFailures.push(failures[pos]);\n                }\n            };\n            return _this.clone().primaryKeys().then(function (keys) {\n                var nextChunk = function (offset) {\n                    var count = Math.min(limit, keys.length - offset);\n                    return coreTable.getMany({\n                        trans: trans,\n                        keys: keys.slice(offset, offset + count),\n                        cache: \"immutable\"\n                    }).then(function (values) {\n                        var addValues = [];\n                        var putValues = [];\n                        var putKeys = outbound ? [] : null;\n                        var deleteKeys = [];\n                        for (var i = 0; i < count; ++i) {\n                            var origValue = values[i];\n                            var ctx_1 = {\n                                value: deepClone(origValue),\n                                primKey: keys[offset + i]\n                            };\n                            if (modifyer.call(ctx_1, ctx_1.value, ctx_1) !== false) {\n                                if (ctx_1.value == null) {\n                                    deleteKeys.push(keys[offset + i]);\n                                }\n                                else if (!outbound && cmp(extractKey(origValue), extractKey(ctx_1.value)) !== 0) {\n                                    deleteKeys.push(keys[offset + i]);\n                                    addValues.push(ctx_1.value);\n                                }\n                                else {\n                                    putValues.push(ctx_1.value);\n                                    if (outbound)\n                                        putKeys.push(keys[offset + i]);\n                                }\n                            }\n                        }\n                        var criteria = isPlainKeyRange(ctx) &&\n                            ctx.limit === Infinity &&\n                            (typeof changes !== 'function' || changes === deleteCallback) && {\n                            index: ctx.index,\n                            range: ctx.range\n                        };\n                        return Promise.resolve(addValues.length > 0 &&\n                            coreTable.mutate({ trans: trans, type: 'add', values: addValues })\n                                .then(function (res) {\n                                for (var pos in res.failures) {\n                                    deleteKeys.splice(parseInt(pos), 1);\n                                }\n                                applyMutateResult(addValues.length, res);\n                            })).then(function () { return (putValues.length > 0 || (criteria && typeof changes === 'object')) &&\n                            coreTable.mutate({\n                                trans: trans,\n                                type: 'put',\n                                keys: putKeys,\n                                values: putValues,\n                                criteria: criteria,\n                                changeSpec: typeof changes !== 'function'\n                                    && changes\n                            }).then(function (res) { return applyMutateResult(putValues.length, res); }); }).then(function () { return (deleteKeys.length > 0 || (criteria && changes === deleteCallback)) &&\n                            coreTable.mutate({\n                                trans: trans,\n                                type: 'delete',\n                                keys: deleteKeys,\n                                criteria: criteria\n                            }).then(function (res) { return applyMutateResult(deleteKeys.length, res); }); }).then(function () {\n                            return keys.length > offset + count && nextChunk(offset + limit);\n                        });\n                    });\n                };\n                return nextChunk(0).then(function () {\n                    if (totalFailures.length > 0)\n                        throw new ModifyError(\"Error modifying one or more objects\", totalFailures, successCount, failedKeys);\n                    return keys.length;\n                });\n            });\n        });\n    };\n    Collection.prototype.delete = function () {\n        var ctx = this._ctx, range = ctx.range;\n        if (isPlainKeyRange(ctx) &&\n            ((ctx.isPrimKey && !hangsOnDeleteLargeKeyRange) || range.type === 3 ))\n         {\n            return this._write(function (trans) {\n                var primaryKey = ctx.table.core.schema.primaryKey;\n                var coreRange = range;\n                return ctx.table.core.count({ trans: trans, query: { index: primaryKey, range: coreRange } }).then(function (count) {\n                    return ctx.table.core.mutate({ trans: trans, type: 'deleteRange', range: coreRange })\n                        .then(function (_a) {\n                        var failures = _a.failures; _a.lastResult; _a.results; var numFailures = _a.numFailures;\n                        if (numFailures)\n                            throw new ModifyError(\"Could not delete some values\", Object.keys(failures).map(function (pos) { return failures[pos]; }), count - numFailures);\n                        return count - numFailures;\n                    });\n                });\n            });\n        }\n        return this.modify(deleteCallback);\n    };\n    return Collection;\n}());\nvar deleteCallback = function (value, ctx) { return ctx.value = null; };\n\nfunction createCollectionConstructor(db) {\n    return makeClassConstructor(Collection.prototype, function Collection(whereClause, keyRangeGenerator) {\n        this.db = db;\n        var keyRange = AnyRange, error = null;\n        if (keyRangeGenerator)\n            try {\n                keyRange = keyRangeGenerator();\n            }\n            catch (ex) {\n                error = ex;\n            }\n        var whereCtx = whereClause._ctx;\n        var table = whereCtx.table;\n        var readingHook = table.hook.reading.fire;\n        this._ctx = {\n            table: table,\n            index: whereCtx.index,\n            isPrimKey: (!whereCtx.index || (table.schema.primKey.keyPath && whereCtx.index === table.schema.primKey.name)),\n            range: keyRange,\n            keysOnly: false,\n            dir: \"next\",\n            unique: \"\",\n            algorithm: null,\n            filter: null,\n            replayFilter: null,\n            justLimit: true,\n            isMatch: null,\n            offset: 0,\n            limit: Infinity,\n            error: error,\n            or: whereCtx.or,\n            valueMapper: readingHook !== mirror ? readingHook : null\n        };\n    });\n}\n\nfunction simpleCompare(a, b) {\n    return a < b ? -1 : a === b ? 0 : 1;\n}\nfunction simpleCompareReverse(a, b) {\n    return a > b ? -1 : a === b ? 0 : 1;\n}\n\nfunction fail(collectionOrWhereClause, err, T) {\n    var collection = collectionOrWhereClause instanceof WhereClause ?\n        new collectionOrWhereClause.Collection(collectionOrWhereClause) :\n        collectionOrWhereClause;\n    collection._ctx.error = T ? new T(err) : new TypeError(err);\n    return collection;\n}\nfunction emptyCollection(whereClause) {\n    return new whereClause.Collection(whereClause, function () { return rangeEqual(\"\"); }).limit(0);\n}\nfunction upperFactory(dir) {\n    return dir === \"next\" ?\n        function (s) { return s.toUpperCase(); } :\n        function (s) { return s.toLowerCase(); };\n}\nfunction lowerFactory(dir) {\n    return dir === \"next\" ?\n        function (s) { return s.toLowerCase(); } :\n        function (s) { return s.toUpperCase(); };\n}\nfunction nextCasing(key, lowerKey, upperNeedle, lowerNeedle, cmp, dir) {\n    var length = Math.min(key.length, lowerNeedle.length);\n    var llp = -1;\n    for (var i = 0; i < length; ++i) {\n        var lwrKeyChar = lowerKey[i];\n        if (lwrKeyChar !== lowerNeedle[i]) {\n            if (cmp(key[i], upperNeedle[i]) < 0)\n                return key.substr(0, i) + upperNeedle[i] + upperNeedle.substr(i + 1);\n            if (cmp(key[i], lowerNeedle[i]) < 0)\n                return key.substr(0, i) + lowerNeedle[i] + upperNeedle.substr(i + 1);\n            if (llp >= 0)\n                return key.substr(0, llp) + lowerKey[llp] + upperNeedle.substr(llp + 1);\n            return null;\n        }\n        if (cmp(key[i], lwrKeyChar) < 0)\n            llp = i;\n    }\n    if (length < lowerNeedle.length && dir === \"next\")\n        return key + upperNeedle.substr(key.length);\n    if (length < key.length && dir === \"prev\")\n        return key.substr(0, upperNeedle.length);\n    return (llp < 0 ? null : key.substr(0, llp) + lowerNeedle[llp] + upperNeedle.substr(llp + 1));\n}\nfunction addIgnoreCaseAlgorithm(whereClause, match, needles, suffix) {\n    var upper, lower, compare, upperNeedles, lowerNeedles, direction, nextKeySuffix, needlesLen = needles.length;\n    if (!needles.every(function (s) { return typeof s === 'string'; })) {\n        return fail(whereClause, STRING_EXPECTED);\n    }\n    function initDirection(dir) {\n        upper = upperFactory(dir);\n        lower = lowerFactory(dir);\n        compare = (dir === \"next\" ? simpleCompare : simpleCompareReverse);\n        var needleBounds = needles.map(function (needle) {\n            return { lower: lower(needle), upper: upper(needle) };\n        }).sort(function (a, b) {\n            return compare(a.lower, b.lower);\n        });\n        upperNeedles = needleBounds.map(function (nb) { return nb.upper; });\n        lowerNeedles = needleBounds.map(function (nb) { return nb.lower; });\n        direction = dir;\n        nextKeySuffix = (dir === \"next\" ? \"\" : suffix);\n    }\n    initDirection(\"next\");\n    var c = new whereClause.Collection(whereClause, function () { return createRange(upperNeedles[0], lowerNeedles[needlesLen - 1] + suffix); });\n    c._ondirectionchange = function (direction) {\n        initDirection(direction);\n    };\n    var firstPossibleNeedle = 0;\n    c._addAlgorithm(function (cursor, advance, resolve) {\n        var key = cursor.key;\n        if (typeof key !== 'string')\n            return false;\n        var lowerKey = lower(key);\n        if (match(lowerKey, lowerNeedles, firstPossibleNeedle)) {\n            return true;\n        }\n        else {\n            var lowestPossibleCasing = null;\n            for (var i = firstPossibleNeedle; i < needlesLen; ++i) {\n                var casing = nextCasing(key, lowerKey, upperNeedles[i], lowerNeedles[i], compare, direction);\n                if (casing === null && lowestPossibleCasing === null)\n                    firstPossibleNeedle = i + 1;\n                else if (lowestPossibleCasing === null || compare(lowestPossibleCasing, casing) > 0) {\n                    lowestPossibleCasing = casing;\n                }\n            }\n            if (lowestPossibleCasing !== null) {\n                advance(function () { cursor.continue(lowestPossibleCasing + nextKeySuffix); });\n            }\n            else {\n                advance(resolve);\n            }\n            return false;\n        }\n    });\n    return c;\n}\nfunction createRange(lower, upper, lowerOpen, upperOpen) {\n    return {\n        type: 2 ,\n        lower: lower,\n        upper: upper,\n        lowerOpen: lowerOpen,\n        upperOpen: upperOpen\n    };\n}\nfunction rangeEqual(value) {\n    return {\n        type: 1 ,\n        lower: value,\n        upper: value\n    };\n}\n\nvar WhereClause =  (function () {\n    function WhereClause() {\n    }\n    Object.defineProperty(WhereClause.prototype, \"Collection\", {\n        get: function () {\n            return this._ctx.table.db.Collection;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WhereClause.prototype.between = function (lower, upper, includeLower, includeUpper) {\n        includeLower = includeLower !== false;\n        includeUpper = includeUpper === true;\n        try {\n            if ((this._cmp(lower, upper) > 0) ||\n                (this._cmp(lower, upper) === 0 && (includeLower || includeUpper) && !(includeLower && includeUpper)))\n                return emptyCollection(this);\n            return new this.Collection(this, function () { return createRange(lower, upper, !includeLower, !includeUpper); });\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n    };\n    WhereClause.prototype.equals = function (value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, function () { return rangeEqual(value); });\n    };\n    WhereClause.prototype.above = function (value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, function () { return createRange(value, undefined, true); });\n    };\n    WhereClause.prototype.aboveOrEqual = function (value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, function () { return createRange(value, undefined, false); });\n    };\n    WhereClause.prototype.below = function (value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, function () { return createRange(undefined, value, false, true); });\n    };\n    WhereClause.prototype.belowOrEqual = function (value) {\n        if (value == null)\n            return fail(this, INVALID_KEY_ARGUMENT);\n        return new this.Collection(this, function () { return createRange(undefined, value); });\n    };\n    WhereClause.prototype.startsWith = function (str) {\n        if (typeof str !== 'string')\n            return fail(this, STRING_EXPECTED);\n        return this.between(str, str + maxString, true, true);\n    };\n    WhereClause.prototype.startsWithIgnoreCase = function (str) {\n        if (str === \"\")\n            return this.startsWith(str);\n        return addIgnoreCaseAlgorithm(this, function (x, a) { return x.indexOf(a[0]) === 0; }, [str], maxString);\n    };\n    WhereClause.prototype.equalsIgnoreCase = function (str) {\n        return addIgnoreCaseAlgorithm(this, function (x, a) { return x === a[0]; }, [str], \"\");\n    };\n    WhereClause.prototype.anyOfIgnoreCase = function () {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return emptyCollection(this);\n        return addIgnoreCaseAlgorithm(this, function (x, a) { return a.indexOf(x) !== -1; }, set, \"\");\n    };\n    WhereClause.prototype.startsWithAnyOfIgnoreCase = function () {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return emptyCollection(this);\n        return addIgnoreCaseAlgorithm(this, function (x, a) { return a.some(function (n) { return x.indexOf(n) === 0; }); }, set, maxString);\n    };\n    WhereClause.prototype.anyOf = function () {\n        var _this = this;\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        var compare = this._cmp;\n        try {\n            set.sort(compare);\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        if (set.length === 0)\n            return emptyCollection(this);\n        var c = new this.Collection(this, function () { return createRange(set[0], set[set.length - 1]); });\n        c._ondirectionchange = function (direction) {\n            compare = (direction === \"next\" ?\n                _this._ascending :\n                _this._descending);\n            set.sort(compare);\n        };\n        var i = 0;\n        c._addAlgorithm(function (cursor, advance, resolve) {\n            var key = cursor.key;\n            while (compare(key, set[i]) > 0) {\n                ++i;\n                if (i === set.length) {\n                    advance(resolve);\n                    return false;\n                }\n            }\n            if (compare(key, set[i]) === 0) {\n                return true;\n            }\n            else {\n                advance(function () { cursor.continue(set[i]); });\n                return false;\n            }\n        });\n        return c;\n    };\n    WhereClause.prototype.notEqual = function (value) {\n        return this.inAnyRange([[minKey, value], [value, this.db._maxKey]], { includeLowers: false, includeUppers: false });\n    };\n    WhereClause.prototype.noneOf = function () {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (set.length === 0)\n            return new this.Collection(this);\n        try {\n            set.sort(this._ascending);\n        }\n        catch (e) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        var ranges = set.reduce(function (res, val) { return res ?\n            res.concat([[res[res.length - 1][1], val]]) :\n            [[minKey, val]]; }, null);\n        ranges.push([set[set.length - 1], this.db._maxKey]);\n        return this.inAnyRange(ranges, { includeLowers: false, includeUppers: false });\n    };\n    WhereClause.prototype.inAnyRange = function (ranges, options) {\n        var _this = this;\n        var cmp = this._cmp, ascending = this._ascending, descending = this._descending, min = this._min, max = this._max;\n        if (ranges.length === 0)\n            return emptyCollection(this);\n        if (!ranges.every(function (range) {\n            return range[0] !== undefined &&\n                range[1] !== undefined &&\n                ascending(range[0], range[1]) <= 0;\n        })) {\n            return fail(this, \"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower\", exceptions.InvalidArgument);\n        }\n        var includeLowers = !options || options.includeLowers !== false;\n        var includeUppers = options && options.includeUppers === true;\n        function addRange(ranges, newRange) {\n            var i = 0, l = ranges.length;\n            for (; i < l; ++i) {\n                var range = ranges[i];\n                if (cmp(newRange[0], range[1]) < 0 && cmp(newRange[1], range[0]) > 0) {\n                    range[0] = min(range[0], newRange[0]);\n                    range[1] = max(range[1], newRange[1]);\n                    break;\n                }\n            }\n            if (i === l)\n                ranges.push(newRange);\n            return ranges;\n        }\n        var sortDirection = ascending;\n        function rangeSorter(a, b) { return sortDirection(a[0], b[0]); }\n        var set;\n        try {\n            set = ranges.reduce(addRange, []);\n            set.sort(rangeSorter);\n        }\n        catch (ex) {\n            return fail(this, INVALID_KEY_ARGUMENT);\n        }\n        var rangePos = 0;\n        var keyIsBeyondCurrentEntry = includeUppers ?\n            function (key) { return ascending(key, set[rangePos][1]) > 0; } :\n            function (key) { return ascending(key, set[rangePos][1]) >= 0; };\n        var keyIsBeforeCurrentEntry = includeLowers ?\n            function (key) { return descending(key, set[rangePos][0]) > 0; } :\n            function (key) { return descending(key, set[rangePos][0]) >= 0; };\n        function keyWithinCurrentRange(key) {\n            return !keyIsBeyondCurrentEntry(key) && !keyIsBeforeCurrentEntry(key);\n        }\n        var checkKey = keyIsBeyondCurrentEntry;\n        var c = new this.Collection(this, function () { return createRange(set[0][0], set[set.length - 1][1], !includeLowers, !includeUppers); });\n        c._ondirectionchange = function (direction) {\n            if (direction === \"next\") {\n                checkKey = keyIsBeyondCurrentEntry;\n                sortDirection = ascending;\n            }\n            else {\n                checkKey = keyIsBeforeCurrentEntry;\n                sortDirection = descending;\n            }\n            set.sort(rangeSorter);\n        };\n        c._addAlgorithm(function (cursor, advance, resolve) {\n            var key = cursor.key;\n            while (checkKey(key)) {\n                ++rangePos;\n                if (rangePos === set.length) {\n                    advance(resolve);\n                    return false;\n                }\n            }\n            if (keyWithinCurrentRange(key)) {\n                return true;\n            }\n            else if (_this._cmp(key, set[rangePos][1]) === 0 || _this._cmp(key, set[rangePos][0]) === 0) {\n                return false;\n            }\n            else {\n                advance(function () {\n                    if (sortDirection === ascending)\n                        cursor.continue(set[rangePos][0]);\n                    else\n                        cursor.continue(set[rangePos][1]);\n                });\n                return false;\n            }\n        });\n        return c;\n    };\n    WhereClause.prototype.startsWithAnyOf = function () {\n        var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n        if (!set.every(function (s) { return typeof s === 'string'; })) {\n            return fail(this, \"startsWithAnyOf() only works with strings\");\n        }\n        if (set.length === 0)\n            return emptyCollection(this);\n        return this.inAnyRange(set.map(function (str) { return [str, str + maxString]; }));\n    };\n    return WhereClause;\n}());\n\nfunction createWhereClauseConstructor(db) {\n    return makeClassConstructor(WhereClause.prototype, function WhereClause(table, index, orCollection) {\n        this.db = db;\n        this._ctx = {\n            table: table,\n            index: index === \":id\" ? null : index,\n            or: orCollection\n        };\n        var indexedDB = db._deps.indexedDB;\n        if (!indexedDB)\n            throw new exceptions.MissingAPI();\n        this._cmp = this._ascending = indexedDB.cmp.bind(indexedDB);\n        this._descending = function (a, b) { return indexedDB.cmp(b, a); };\n        this._max = function (a, b) { return indexedDB.cmp(a, b) > 0 ? a : b; };\n        this._min = function (a, b) { return indexedDB.cmp(a, b) < 0 ? a : b; };\n        this._IDBKeyRange = db._deps.IDBKeyRange;\n    });\n}\n\nfunction eventRejectHandler(reject) {\n    return wrap(function (event) {\n        preventDefault(event);\n        reject(event.target.error);\n        return false;\n    });\n}\nfunction preventDefault(event) {\n    if (event.stopPropagation)\n        event.stopPropagation();\n    if (event.preventDefault)\n        event.preventDefault();\n}\n\nvar DEXIE_STORAGE_MUTATED_EVENT_NAME = 'storagemutated';\nvar STORAGE_MUTATED_DOM_EVENT_NAME = 'x-storagemutated-1';\nvar globalEvents = Events(null, DEXIE_STORAGE_MUTATED_EVENT_NAME);\n\nvar Transaction =  (function () {\n    function Transaction() {\n    }\n    Transaction.prototype._lock = function () {\n        assert(!PSD.global);\n        ++this._reculock;\n        if (this._reculock === 1 && !PSD.global)\n            PSD.lockOwnerFor = this;\n        return this;\n    };\n    Transaction.prototype._unlock = function () {\n        assert(!PSD.global);\n        if (--this._reculock === 0) {\n            if (!PSD.global)\n                PSD.lockOwnerFor = null;\n            while (this._blockedFuncs.length > 0 && !this._locked()) {\n                var fnAndPSD = this._blockedFuncs.shift();\n                try {\n                    usePSD(fnAndPSD[1], fnAndPSD[0]);\n                }\n                catch (e) { }\n            }\n        }\n        return this;\n    };\n    Transaction.prototype._locked = function () {\n        return this._reculock && PSD.lockOwnerFor !== this;\n    };\n    Transaction.prototype.create = function (idbtrans) {\n        var _this = this;\n        if (!this.mode)\n            return this;\n        var idbdb = this.db.idbdb;\n        var dbOpenError = this.db._state.dbOpenError;\n        assert(!this.idbtrans);\n        if (!idbtrans && !idbdb) {\n            switch (dbOpenError && dbOpenError.name) {\n                case \"DatabaseClosedError\":\n                    throw new exceptions.DatabaseClosed(dbOpenError);\n                case \"MissingAPIError\":\n                    throw new exceptions.MissingAPI(dbOpenError.message, dbOpenError);\n                default:\n                    throw new exceptions.OpenFailed(dbOpenError);\n            }\n        }\n        if (!this.active)\n            throw new exceptions.TransactionInactive();\n        assert(this._completion._state === null);\n        idbtrans = this.idbtrans = idbtrans ||\n            (this.db.core\n                ? this.db.core.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability })\n                : idbdb.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability }));\n        idbtrans.onerror = wrap(function (ev) {\n            preventDefault(ev);\n            _this._reject(idbtrans.error);\n        });\n        idbtrans.onabort = wrap(function (ev) {\n            preventDefault(ev);\n            _this.active && _this._reject(new exceptions.Abort(idbtrans.error));\n            _this.active = false;\n            _this.on(\"abort\").fire(ev);\n        });\n        idbtrans.oncomplete = wrap(function () {\n            _this.active = false;\n            _this._resolve();\n            if ('mutatedParts' in idbtrans) {\n                globalEvents.storagemutated.fire(idbtrans[\"mutatedParts\"]);\n            }\n        });\n        return this;\n    };\n    Transaction.prototype._promise = function (mode, fn, bWriteLock) {\n        var _this = this;\n        if (mode === 'readwrite' && this.mode !== 'readwrite')\n            return rejection(new exceptions.ReadOnly(\"Transaction is readonly\"));\n        if (!this.active)\n            return rejection(new exceptions.TransactionInactive());\n        if (this._locked()) {\n            return new DexiePromise(function (resolve, reject) {\n                _this._blockedFuncs.push([function () {\n                        _this._promise(mode, fn, bWriteLock).then(resolve, reject);\n                    }, PSD]);\n            });\n        }\n        else if (bWriteLock) {\n            return newScope(function () {\n                var p = new DexiePromise(function (resolve, reject) {\n                    _this._lock();\n                    var rv = fn(resolve, reject, _this);\n                    if (rv && rv.then)\n                        rv.then(resolve, reject);\n                });\n                p.finally(function () { return _this._unlock(); });\n                p._lib = true;\n                return p;\n            });\n        }\n        else {\n            var p = new DexiePromise(function (resolve, reject) {\n                var rv = fn(resolve, reject, _this);\n                if (rv && rv.then)\n                    rv.then(resolve, reject);\n            });\n            p._lib = true;\n            return p;\n        }\n    };\n    Transaction.prototype._root = function () {\n        return this.parent ? this.parent._root() : this;\n    };\n    Transaction.prototype.waitFor = function (promiseLike) {\n        var root = this._root();\n        var promise = DexiePromise.resolve(promiseLike);\n        if (root._waitingFor) {\n            root._waitingFor = root._waitingFor.then(function () { return promise; });\n        }\n        else {\n            root._waitingFor = promise;\n            root._waitingQueue = [];\n            var store = root.idbtrans.objectStore(root.storeNames[0]);\n            (function spin() {\n                ++root._spinCount;\n                while (root._waitingQueue.length)\n                    (root._waitingQueue.shift())();\n                if (root._waitingFor)\n                    store.get(-Infinity).onsuccess = spin;\n            }());\n        }\n        var currentWaitPromise = root._waitingFor;\n        return new DexiePromise(function (resolve, reject) {\n            promise.then(function (res) { return root._waitingQueue.push(wrap(resolve.bind(null, res))); }, function (err) { return root._waitingQueue.push(wrap(reject.bind(null, err))); }).finally(function () {\n                if (root._waitingFor === currentWaitPromise) {\n                    root._waitingFor = null;\n                }\n            });\n        });\n    };\n    Transaction.prototype.abort = function () {\n        if (this.active) {\n            this.active = false;\n            if (this.idbtrans)\n                this.idbtrans.abort();\n            this._reject(new exceptions.Abort());\n        }\n    };\n    Transaction.prototype.table = function (tableName) {\n        var memoizedTables = (this._memoizedTables || (this._memoizedTables = {}));\n        if (hasOwn(memoizedTables, tableName))\n            return memoizedTables[tableName];\n        var tableSchema = this.schema[tableName];\n        if (!tableSchema) {\n            throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n        }\n        var transactionBoundTable = new this.db.Table(tableName, tableSchema, this);\n        transactionBoundTable.core = this.db.core.table(tableName);\n        memoizedTables[tableName] = transactionBoundTable;\n        return transactionBoundTable;\n    };\n    return Transaction;\n}());\n\nfunction createTransactionConstructor(db) {\n    return makeClassConstructor(Transaction.prototype, function Transaction(mode, storeNames, dbschema, chromeTransactionDurability, parent) {\n        var _this = this;\n        this.db = db;\n        this.mode = mode;\n        this.storeNames = storeNames;\n        this.schema = dbschema;\n        this.chromeTransactionDurability = chromeTransactionDurability;\n        this.idbtrans = null;\n        this.on = Events(this, \"complete\", \"error\", \"abort\");\n        this.parent = parent || null;\n        this.active = true;\n        this._reculock = 0;\n        this._blockedFuncs = [];\n        this._resolve = null;\n        this._reject = null;\n        this._waitingFor = null;\n        this._waitingQueue = null;\n        this._spinCount = 0;\n        this._completion = new DexiePromise(function (resolve, reject) {\n            _this._resolve = resolve;\n            _this._reject = reject;\n        });\n        this._completion.then(function () {\n            _this.active = false;\n            _this.on.complete.fire();\n        }, function (e) {\n            var wasActive = _this.active;\n            _this.active = false;\n            _this.on.error.fire(e);\n            _this.parent ?\n                _this.parent._reject(e) :\n                wasActive && _this.idbtrans && _this.idbtrans.abort();\n            return rejection(e);\n        });\n    });\n}\n\nfunction createIndexSpec(name, keyPath, unique, multi, auto, compound, isPrimKey) {\n    return {\n        name: name,\n        keyPath: keyPath,\n        unique: unique,\n        multi: multi,\n        auto: auto,\n        compound: compound,\n        src: (unique && !isPrimKey ? '&' : '') + (multi ? '*' : '') + (auto ? \"++\" : \"\") + nameFromKeyPath(keyPath)\n    };\n}\nfunction nameFromKeyPath(keyPath) {\n    return typeof keyPath === 'string' ?\n        keyPath :\n        keyPath ? ('[' + [].join.call(keyPath, '+') + ']') : \"\";\n}\n\nfunction createTableSchema(name, primKey, indexes) {\n    return {\n        name: name,\n        primKey: primKey,\n        indexes: indexes,\n        mappedClass: null,\n        idxByName: arrayToObject(indexes, function (index) { return [index.name, index]; })\n    };\n}\n\nfunction safariMultiStoreFix(storeNames) {\n    return storeNames.length === 1 ? storeNames[0] : storeNames;\n}\nvar getMaxKey = function (IdbKeyRange) {\n    try {\n        IdbKeyRange.only([[]]);\n        getMaxKey = function () { return [[]]; };\n        return [[]];\n    }\n    catch (e) {\n        getMaxKey = function () { return maxString; };\n        return maxString;\n    }\n};\n\nfunction getKeyExtractor(keyPath) {\n    if (keyPath == null) {\n        return function () { return undefined; };\n    }\n    else if (typeof keyPath === 'string') {\n        return getSinglePathKeyExtractor(keyPath);\n    }\n    else {\n        return function (obj) { return getByKeyPath(obj, keyPath); };\n    }\n}\nfunction getSinglePathKeyExtractor(keyPath) {\n    var split = keyPath.split('.');\n    if (split.length === 1) {\n        return function (obj) { return obj[keyPath]; };\n    }\n    else {\n        return function (obj) { return getByKeyPath(obj, keyPath); };\n    }\n}\n\nfunction arrayify(arrayLike) {\n    return [].slice.call(arrayLike);\n}\nvar _id_counter = 0;\nfunction getKeyPathAlias(keyPath) {\n    return keyPath == null ?\n        \":id\" :\n        typeof keyPath === 'string' ?\n            keyPath :\n            \"[\" + keyPath.join('+') + \"]\";\n}\nfunction createDBCore(db, IdbKeyRange, tmpTrans) {\n    function extractSchema(db, trans) {\n        var tables = arrayify(db.objectStoreNames);\n        return {\n            schema: {\n                name: db.name,\n                tables: tables.map(function (table) { return trans.objectStore(table); }).map(function (store) {\n                    var keyPath = store.keyPath, autoIncrement = store.autoIncrement;\n                    var compound = isArray(keyPath);\n                    var outbound = keyPath == null;\n                    var indexByKeyPath = {};\n                    var result = {\n                        name: store.name,\n                        primaryKey: {\n                            name: null,\n                            isPrimaryKey: true,\n                            outbound: outbound,\n                            compound: compound,\n                            keyPath: keyPath,\n                            autoIncrement: autoIncrement,\n                            unique: true,\n                            extractKey: getKeyExtractor(keyPath)\n                        },\n                        indexes: arrayify(store.indexNames).map(function (indexName) { return store.index(indexName); })\n                            .map(function (index) {\n                            var name = index.name, unique = index.unique, multiEntry = index.multiEntry, keyPath = index.keyPath;\n                            var compound = isArray(keyPath);\n                            var result = {\n                                name: name,\n                                compound: compound,\n                                keyPath: keyPath,\n                                unique: unique,\n                                multiEntry: multiEntry,\n                                extractKey: getKeyExtractor(keyPath)\n                            };\n                            indexByKeyPath[getKeyPathAlias(keyPath)] = result;\n                            return result;\n                        }),\n                        getIndexByKeyPath: function (keyPath) { return indexByKeyPath[getKeyPathAlias(keyPath)]; }\n                    };\n                    indexByKeyPath[\":id\"] = result.primaryKey;\n                    if (keyPath != null) {\n                        indexByKeyPath[getKeyPathAlias(keyPath)] = result.primaryKey;\n                    }\n                    return result;\n                })\n            },\n            hasGetAll: tables.length > 0 && ('getAll' in trans.objectStore(tables[0])) &&\n                !(typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n                    !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n                    [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604)\n        };\n    }\n    function makeIDBKeyRange(range) {\n        if (range.type === 3 )\n            return null;\n        if (range.type === 4 )\n            throw new Error(\"Cannot convert never type to IDBKeyRange\");\n        var lower = range.lower, upper = range.upper, lowerOpen = range.lowerOpen, upperOpen = range.upperOpen;\n        var idbRange = lower === undefined ?\n            upper === undefined ?\n                null :\n                IdbKeyRange.upperBound(upper, !!upperOpen) :\n            upper === undefined ?\n                IdbKeyRange.lowerBound(lower, !!lowerOpen) :\n                IdbKeyRange.bound(lower, upper, !!lowerOpen, !!upperOpen);\n        return idbRange;\n    }\n    function createDbCoreTable(tableSchema) {\n        var tableName = tableSchema.name;\n        function mutate(_a) {\n            var trans = _a.trans, type = _a.type, keys = _a.keys, values = _a.values, range = _a.range;\n            return new Promise(function (resolve, reject) {\n                resolve = wrap(resolve);\n                var store = trans.objectStore(tableName);\n                var outbound = store.keyPath == null;\n                var isAddOrPut = type === \"put\" || type === \"add\";\n                if (!isAddOrPut && type !== 'delete' && type !== 'deleteRange')\n                    throw new Error(\"Invalid operation type: \" + type);\n                var length = (keys || values || { length: 1 }).length;\n                if (keys && values && keys.length !== values.length) {\n                    throw new Error(\"Given keys array must have same length as given values array.\");\n                }\n                if (length === 0)\n                    return resolve({ numFailures: 0, failures: {}, results: [], lastResult: undefined });\n                var req;\n                var reqs = [];\n                var failures = [];\n                var numFailures = 0;\n                var errorHandler = function (event) {\n                    ++numFailures;\n                    preventDefault(event);\n                };\n                if (type === 'deleteRange') {\n                    if (range.type === 4 )\n                        return resolve({ numFailures: numFailures, failures: failures, results: [], lastResult: undefined });\n                    if (range.type === 3 )\n                        reqs.push(req = store.clear());\n                    else\n                        reqs.push(req = store.delete(makeIDBKeyRange(range)));\n                }\n                else {\n                    var _a = isAddOrPut ?\n                        outbound ?\n                            [values, keys] :\n                            [values, null] :\n                        [keys, null], args1 = _a[0], args2 = _a[1];\n                    if (isAddOrPut) {\n                        for (var i = 0; i < length; ++i) {\n                            reqs.push(req = (args2 && args2[i] !== undefined ?\n                                store[type](args1[i], args2[i]) :\n                                store[type](args1[i])));\n                            req.onerror = errorHandler;\n                        }\n                    }\n                    else {\n                        for (var i = 0; i < length; ++i) {\n                            reqs.push(req = store[type](args1[i]));\n                            req.onerror = errorHandler;\n                        }\n                    }\n                }\n                var done = function (event) {\n                    var lastResult = event.target.result;\n                    reqs.forEach(function (req, i) { return req.error != null && (failures[i] = req.error); });\n                    resolve({\n                        numFailures: numFailures,\n                        failures: failures,\n                        results: type === \"delete\" ? keys : reqs.map(function (req) { return req.result; }),\n                        lastResult: lastResult\n                    });\n                };\n                req.onerror = function (event) {\n                    errorHandler(event);\n                    done(event);\n                };\n                req.onsuccess = done;\n            });\n        }\n        function openCursor(_a) {\n            var trans = _a.trans, values = _a.values, query = _a.query, reverse = _a.reverse, unique = _a.unique;\n            return new Promise(function (resolve, reject) {\n                resolve = wrap(resolve);\n                var index = query.index, range = query.range;\n                var store = trans.objectStore(tableName);\n                var source = index.isPrimaryKey ?\n                    store :\n                    store.index(index.name);\n                var direction = reverse ?\n                    unique ?\n                        \"prevunique\" :\n                        \"prev\" :\n                    unique ?\n                        \"nextunique\" :\n                        \"next\";\n                var req = values || !('openKeyCursor' in source) ?\n                    source.openCursor(makeIDBKeyRange(range), direction) :\n                    source.openKeyCursor(makeIDBKeyRange(range), direction);\n                req.onerror = eventRejectHandler(reject);\n                req.onsuccess = wrap(function (ev) {\n                    var cursor = req.result;\n                    if (!cursor) {\n                        resolve(null);\n                        return;\n                    }\n                    cursor.___id = ++_id_counter;\n                    cursor.done = false;\n                    var _cursorContinue = cursor.continue.bind(cursor);\n                    var _cursorContinuePrimaryKey = cursor.continuePrimaryKey;\n                    if (_cursorContinuePrimaryKey)\n                        _cursorContinuePrimaryKey = _cursorContinuePrimaryKey.bind(cursor);\n                    var _cursorAdvance = cursor.advance.bind(cursor);\n                    var doThrowCursorIsNotStarted = function () { throw new Error(\"Cursor not started\"); };\n                    var doThrowCursorIsStopped = function () { throw new Error(\"Cursor not stopped\"); };\n                    cursor.trans = trans;\n                    cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsNotStarted;\n                    cursor.fail = wrap(reject);\n                    cursor.next = function () {\n                        var _this = this;\n                        var gotOne = 1;\n                        return this.start(function () { return gotOne-- ? _this.continue() : _this.stop(); }).then(function () { return _this; });\n                    };\n                    cursor.start = function (callback) {\n                        var iterationPromise = new Promise(function (resolveIteration, rejectIteration) {\n                            resolveIteration = wrap(resolveIteration);\n                            req.onerror = eventRejectHandler(rejectIteration);\n                            cursor.fail = rejectIteration;\n                            cursor.stop = function (value) {\n                                cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsStopped;\n                                resolveIteration(value);\n                            };\n                        });\n                        var guardedCallback = function () {\n                            if (req.result) {\n                                try {\n                                    callback();\n                                }\n                                catch (err) {\n                                    cursor.fail(err);\n                                }\n                            }\n                            else {\n                                cursor.done = true;\n                                cursor.start = function () { throw new Error(\"Cursor behind last entry\"); };\n                                cursor.stop();\n                            }\n                        };\n                        req.onsuccess = wrap(function (ev) {\n                            req.onsuccess = guardedCallback;\n                            guardedCallback();\n                        });\n                        cursor.continue = _cursorContinue;\n                        cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n                        cursor.advance = _cursorAdvance;\n                        guardedCallback();\n                        return iterationPromise;\n                    };\n                    resolve(cursor);\n                }, reject);\n            });\n        }\n        function query(hasGetAll) {\n            return function (request) {\n                return new Promise(function (resolve, reject) {\n                    resolve = wrap(resolve);\n                    var trans = request.trans, values = request.values, limit = request.limit, query = request.query;\n                    var nonInfinitLimit = limit === Infinity ? undefined : limit;\n                    var index = query.index, range = query.range;\n                    var store = trans.objectStore(tableName);\n                    var source = index.isPrimaryKey ? store : store.index(index.name);\n                    var idbKeyRange = makeIDBKeyRange(range);\n                    if (limit === 0)\n                        return resolve({ result: [] });\n                    if (hasGetAll) {\n                        var req = values ?\n                            source.getAll(idbKeyRange, nonInfinitLimit) :\n                            source.getAllKeys(idbKeyRange, nonInfinitLimit);\n                        req.onsuccess = function (event) { return resolve({ result: event.target.result }); };\n                        req.onerror = eventRejectHandler(reject);\n                    }\n                    else {\n                        var count_1 = 0;\n                        var req_1 = values || !('openKeyCursor' in source) ?\n                            source.openCursor(idbKeyRange) :\n                            source.openKeyCursor(idbKeyRange);\n                        var result_1 = [];\n                        req_1.onsuccess = function (event) {\n                            var cursor = req_1.result;\n                            if (!cursor)\n                                return resolve({ result: result_1 });\n                            result_1.push(values ? cursor.value : cursor.primaryKey);\n                            if (++count_1 === limit)\n                                return resolve({ result: result_1 });\n                            cursor.continue();\n                        };\n                        req_1.onerror = eventRejectHandler(reject);\n                    }\n                });\n            };\n        }\n        return {\n            name: tableName,\n            schema: tableSchema,\n            mutate: mutate,\n            getMany: function (_a) {\n                var trans = _a.trans, keys = _a.keys;\n                return new Promise(function (resolve, reject) {\n                    resolve = wrap(resolve);\n                    var store = trans.objectStore(tableName);\n                    var length = keys.length;\n                    var result = new Array(length);\n                    var keyCount = 0;\n                    var callbackCount = 0;\n                    var req;\n                    var successHandler = function (event) {\n                        var req = event.target;\n                        if ((result[req._pos] = req.result) != null)\n                            ;\n                        if (++callbackCount === keyCount)\n                            resolve(result);\n                    };\n                    var errorHandler = eventRejectHandler(reject);\n                    for (var i = 0; i < length; ++i) {\n                        var key = keys[i];\n                        if (key != null) {\n                            req = store.get(keys[i]);\n                            req._pos = i;\n                            req.onsuccess = successHandler;\n                            req.onerror = errorHandler;\n                            ++keyCount;\n                        }\n                    }\n                    if (keyCount === 0)\n                        resolve(result);\n                });\n            },\n            get: function (_a) {\n                var trans = _a.trans, key = _a.key;\n                return new Promise(function (resolve, reject) {\n                    resolve = wrap(resolve);\n                    var store = trans.objectStore(tableName);\n                    var req = store.get(key);\n                    req.onsuccess = function (event) { return resolve(event.target.result); };\n                    req.onerror = eventRejectHandler(reject);\n                });\n            },\n            query: query(hasGetAll),\n            openCursor: openCursor,\n            count: function (_a) {\n                var query = _a.query, trans = _a.trans;\n                var index = query.index, range = query.range;\n                return new Promise(function (resolve, reject) {\n                    var store = trans.objectStore(tableName);\n                    var source = index.isPrimaryKey ? store : store.index(index.name);\n                    var idbKeyRange = makeIDBKeyRange(range);\n                    var req = idbKeyRange ? source.count(idbKeyRange) : source.count();\n                    req.onsuccess = wrap(function (ev) { return resolve(ev.target.result); });\n                    req.onerror = eventRejectHandler(reject);\n                });\n            }\n        };\n    }\n    var _a = extractSchema(db, tmpTrans), schema = _a.schema, hasGetAll = _a.hasGetAll;\n    var tables = schema.tables.map(function (tableSchema) { return createDbCoreTable(tableSchema); });\n    var tableMap = {};\n    tables.forEach(function (table) { return tableMap[table.name] = table; });\n    return {\n        stack: \"dbcore\",\n        transaction: db.transaction.bind(db),\n        table: function (name) {\n            var result = tableMap[name];\n            if (!result)\n                throw new Error(\"Table '\" + name + \"' not found\");\n            return tableMap[name];\n        },\n        MIN_KEY: -Infinity,\n        MAX_KEY: getMaxKey(IdbKeyRange),\n        schema: schema\n    };\n}\n\nfunction createMiddlewareStack(stackImpl, middlewares) {\n    return middlewares.reduce(function (down, _a) {\n        var create = _a.create;\n        return (__assign(__assign({}, down), create(down)));\n    }, stackImpl);\n}\nfunction createMiddlewareStacks(middlewares, idbdb, _a, tmpTrans) {\n    var IDBKeyRange = _a.IDBKeyRange; _a.indexedDB;\n    var dbcore = createMiddlewareStack(createDBCore(idbdb, IDBKeyRange, tmpTrans), middlewares.dbcore);\n    return {\n        dbcore: dbcore\n    };\n}\nfunction generateMiddlewareStacks(_a, tmpTrans) {\n    var db = _a._novip;\n    var idbdb = tmpTrans.db;\n    var stacks = createMiddlewareStacks(db._middlewares, idbdb, db._deps, tmpTrans);\n    db.core = stacks.dbcore;\n    db.tables.forEach(function (table) {\n        var tableName = table.name;\n        if (db.core.schema.tables.some(function (tbl) { return tbl.name === tableName; })) {\n            table.core = db.core.table(tableName);\n            if (db[tableName] instanceof db.Table) {\n                db[tableName].core = table.core;\n            }\n        }\n    });\n}\n\nfunction setApiOnPlace(_a, objs, tableNames, dbschema) {\n    var db = _a._novip;\n    tableNames.forEach(function (tableName) {\n        var schema = dbschema[tableName];\n        objs.forEach(function (obj) {\n            var propDesc = getPropertyDescriptor(obj, tableName);\n            if (!propDesc || (\"value\" in propDesc && propDesc.value === undefined)) {\n                if (obj === db.Transaction.prototype || obj instanceof db.Transaction) {\n                    setProp(obj, tableName, {\n                        get: function () { return this.table(tableName); },\n                        set: function (value) {\n                            defineProperty(this, tableName, { value: value, writable: true, configurable: true, enumerable: true });\n                        }\n                    });\n                }\n                else {\n                    obj[tableName] = new db.Table(tableName, schema);\n                }\n            }\n        });\n    });\n}\nfunction removeTablesApi(_a, objs) {\n    var db = _a._novip;\n    objs.forEach(function (obj) {\n        for (var key in obj) {\n            if (obj[key] instanceof db.Table)\n                delete obj[key];\n        }\n    });\n}\nfunction lowerVersionFirst(a, b) {\n    return a._cfg.version - b._cfg.version;\n}\nfunction runUpgraders(db, oldVersion, idbUpgradeTrans, reject) {\n    var globalSchema = db._dbSchema;\n    var trans = db._createTransaction('readwrite', db._storeNames, globalSchema);\n    trans.create(idbUpgradeTrans);\n    trans._completion.catch(reject);\n    var rejectTransaction = trans._reject.bind(trans);\n    var transless = PSD.transless || PSD;\n    newScope(function () {\n        PSD.trans = trans;\n        PSD.transless = transless;\n        if (oldVersion === 0) {\n            keys(globalSchema).forEach(function (tableName) {\n                createTable(idbUpgradeTrans, tableName, globalSchema[tableName].primKey, globalSchema[tableName].indexes);\n            });\n            generateMiddlewareStacks(db, idbUpgradeTrans);\n            DexiePromise.follow(function () { return db.on.populate.fire(trans); }).catch(rejectTransaction);\n        }\n        else\n            updateTablesAndIndexes(db, oldVersion, trans, idbUpgradeTrans).catch(rejectTransaction);\n    });\n}\nfunction updateTablesAndIndexes(_a, oldVersion, trans, idbUpgradeTrans) {\n    var db = _a._novip;\n    var queue = [];\n    var versions = db._versions;\n    var globalSchema = db._dbSchema = buildGlobalSchema(db, db.idbdb, idbUpgradeTrans);\n    var anyContentUpgraderHasRun = false;\n    var versToRun = versions.filter(function (v) { return v._cfg.version >= oldVersion; });\n    versToRun.forEach(function (version) {\n        queue.push(function () {\n            var oldSchema = globalSchema;\n            var newSchema = version._cfg.dbschema;\n            adjustToExistingIndexNames(db, oldSchema, idbUpgradeTrans);\n            adjustToExistingIndexNames(db, newSchema, idbUpgradeTrans);\n            globalSchema = db._dbSchema = newSchema;\n            var diff = getSchemaDiff(oldSchema, newSchema);\n            diff.add.forEach(function (tuple) {\n                createTable(idbUpgradeTrans, tuple[0], tuple[1].primKey, tuple[1].indexes);\n            });\n            diff.change.forEach(function (change) {\n                if (change.recreate) {\n                    throw new exceptions.Upgrade(\"Not yet support for changing primary key\");\n                }\n                else {\n                    var store_1 = idbUpgradeTrans.objectStore(change.name);\n                    change.add.forEach(function (idx) { return addIndex(store_1, idx); });\n                    change.change.forEach(function (idx) {\n                        store_1.deleteIndex(idx.name);\n                        addIndex(store_1, idx);\n                    });\n                    change.del.forEach(function (idxName) { return store_1.deleteIndex(idxName); });\n                }\n            });\n            var contentUpgrade = version._cfg.contentUpgrade;\n            if (contentUpgrade && version._cfg.version > oldVersion) {\n                generateMiddlewareStacks(db, idbUpgradeTrans);\n                trans._memoizedTables = {};\n                anyContentUpgraderHasRun = true;\n                var upgradeSchema_1 = shallowClone(newSchema);\n                diff.del.forEach(function (table) {\n                    upgradeSchema_1[table] = oldSchema[table];\n                });\n                removeTablesApi(db, [db.Transaction.prototype]);\n                setApiOnPlace(db, [db.Transaction.prototype], keys(upgradeSchema_1), upgradeSchema_1);\n                trans.schema = upgradeSchema_1;\n                var contentUpgradeIsAsync_1 = isAsyncFunction(contentUpgrade);\n                if (contentUpgradeIsAsync_1) {\n                    incrementExpectedAwaits();\n                }\n                var returnValue_1;\n                var promiseFollowed = DexiePromise.follow(function () {\n                    returnValue_1 = contentUpgrade(trans);\n                    if (returnValue_1) {\n                        if (contentUpgradeIsAsync_1) {\n                            var decrementor = decrementExpectedAwaits.bind(null, null);\n                            returnValue_1.then(decrementor, decrementor);\n                        }\n                    }\n                });\n                return (returnValue_1 && typeof returnValue_1.then === 'function' ?\n                    DexiePromise.resolve(returnValue_1) : promiseFollowed.then(function () { return returnValue_1; }));\n            }\n        });\n        queue.push(function (idbtrans) {\n            if (!anyContentUpgraderHasRun || !hasIEDeleteObjectStoreBug) {\n                var newSchema = version._cfg.dbschema;\n                deleteRemovedTables(newSchema, idbtrans);\n            }\n            removeTablesApi(db, [db.Transaction.prototype]);\n            setApiOnPlace(db, [db.Transaction.prototype], db._storeNames, db._dbSchema);\n            trans.schema = db._dbSchema;\n        });\n    });\n    function runQueue() {\n        return queue.length ? DexiePromise.resolve(queue.shift()(trans.idbtrans)).then(runQueue) :\n            DexiePromise.resolve();\n    }\n    return runQueue().then(function () {\n        createMissingTables(globalSchema, idbUpgradeTrans);\n    });\n}\nfunction getSchemaDiff(oldSchema, newSchema) {\n    var diff = {\n        del: [],\n        add: [],\n        change: []\n    };\n    var table;\n    for (table in oldSchema) {\n        if (!newSchema[table])\n            diff.del.push(table);\n    }\n    for (table in newSchema) {\n        var oldDef = oldSchema[table], newDef = newSchema[table];\n        if (!oldDef) {\n            diff.add.push([table, newDef]);\n        }\n        else {\n            var change = {\n                name: table,\n                def: newDef,\n                recreate: false,\n                del: [],\n                add: [],\n                change: []\n            };\n            if ((\n            '' + (oldDef.primKey.keyPath || '')) !== ('' + (newDef.primKey.keyPath || '')) ||\n                (oldDef.primKey.auto !== newDef.primKey.auto && !isIEOrEdge))\n             {\n                change.recreate = true;\n                diff.change.push(change);\n            }\n            else {\n                var oldIndexes = oldDef.idxByName;\n                var newIndexes = newDef.idxByName;\n                var idxName = void 0;\n                for (idxName in oldIndexes) {\n                    if (!newIndexes[idxName])\n                        change.del.push(idxName);\n                }\n                for (idxName in newIndexes) {\n                    var oldIdx = oldIndexes[idxName], newIdx = newIndexes[idxName];\n                    if (!oldIdx)\n                        change.add.push(newIdx);\n                    else if (oldIdx.src !== newIdx.src)\n                        change.change.push(newIdx);\n                }\n                if (change.del.length > 0 || change.add.length > 0 || change.change.length > 0) {\n                    diff.change.push(change);\n                }\n            }\n        }\n    }\n    return diff;\n}\nfunction createTable(idbtrans, tableName, primKey, indexes) {\n    var store = idbtrans.db.createObjectStore(tableName, primKey.keyPath ?\n        { keyPath: primKey.keyPath, autoIncrement: primKey.auto } :\n        { autoIncrement: primKey.auto });\n    indexes.forEach(function (idx) { return addIndex(store, idx); });\n    return store;\n}\nfunction createMissingTables(newSchema, idbtrans) {\n    keys(newSchema).forEach(function (tableName) {\n        if (!idbtrans.db.objectStoreNames.contains(tableName)) {\n            createTable(idbtrans, tableName, newSchema[tableName].primKey, newSchema[tableName].indexes);\n        }\n    });\n}\nfunction deleteRemovedTables(newSchema, idbtrans) {\n    [].slice.call(idbtrans.db.objectStoreNames).forEach(function (storeName) {\n        return newSchema[storeName] == null && idbtrans.db.deleteObjectStore(storeName);\n    });\n}\nfunction addIndex(store, idx) {\n    store.createIndex(idx.name, idx.keyPath, { unique: idx.unique, multiEntry: idx.multi });\n}\nfunction buildGlobalSchema(db, idbdb, tmpTrans) {\n    var globalSchema = {};\n    var dbStoreNames = slice(idbdb.objectStoreNames, 0);\n    dbStoreNames.forEach(function (storeName) {\n        var store = tmpTrans.objectStore(storeName);\n        var keyPath = store.keyPath;\n        var primKey = createIndexSpec(nameFromKeyPath(keyPath), keyPath || \"\", false, false, !!store.autoIncrement, keyPath && typeof keyPath !== \"string\", true);\n        var indexes = [];\n        for (var j = 0; j < store.indexNames.length; ++j) {\n            var idbindex = store.index(store.indexNames[j]);\n            keyPath = idbindex.keyPath;\n            var index = createIndexSpec(idbindex.name, keyPath, !!idbindex.unique, !!idbindex.multiEntry, false, keyPath && typeof keyPath !== \"string\", false);\n            indexes.push(index);\n        }\n        globalSchema[storeName] = createTableSchema(storeName, primKey, indexes);\n    });\n    return globalSchema;\n}\nfunction readGlobalSchema(_a, idbdb, tmpTrans) {\n    var db = _a._novip;\n    db.verno = idbdb.version / 10;\n    var globalSchema = db._dbSchema = buildGlobalSchema(db, idbdb, tmpTrans);\n    db._storeNames = slice(idbdb.objectStoreNames, 0);\n    setApiOnPlace(db, [db._allTables], keys(globalSchema), globalSchema);\n}\nfunction verifyInstalledSchema(db, tmpTrans) {\n    var installedSchema = buildGlobalSchema(db, db.idbdb, tmpTrans);\n    var diff = getSchemaDiff(installedSchema, db._dbSchema);\n    return !(diff.add.length || diff.change.some(function (ch) { return ch.add.length || ch.change.length; }));\n}\nfunction adjustToExistingIndexNames(_a, schema, idbtrans) {\n    var db = _a._novip;\n    var storeNames = idbtrans.db.objectStoreNames;\n    for (var i = 0; i < storeNames.length; ++i) {\n        var storeName = storeNames[i];\n        var store = idbtrans.objectStore(storeName);\n        db._hasGetAll = 'getAll' in store;\n        for (var j = 0; j < store.indexNames.length; ++j) {\n            var indexName = store.indexNames[j];\n            var keyPath = store.index(indexName).keyPath;\n            var dexieName = typeof keyPath === 'string' ? keyPath : \"[\" + slice(keyPath).join('+') + \"]\";\n            if (schema[storeName]) {\n                var indexSpec = schema[storeName].idxByName[dexieName];\n                if (indexSpec) {\n                    indexSpec.name = indexName;\n                    delete schema[storeName].idxByName[dexieName];\n                    schema[storeName].idxByName[indexName] = indexSpec;\n                }\n            }\n        }\n    }\n    if (typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n        !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n        _global.WorkerGlobalScope && _global instanceof _global.WorkerGlobalScope &&\n        [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604) {\n        db._hasGetAll = false;\n    }\n}\nfunction parseIndexSyntax(primKeyAndIndexes) {\n    return primKeyAndIndexes.split(',').map(function (index, indexNum) {\n        index = index.trim();\n        var name = index.replace(/([&*]|\\+\\+)/g, \"\");\n        var keyPath = /^\\[/.test(name) ? name.match(/^\\[(.*)\\]$/)[1].split('+') : name;\n        return createIndexSpec(name, keyPath || null, /\\&/.test(index), /\\*/.test(index), /\\+\\+/.test(index), isArray(keyPath), indexNum === 0);\n    });\n}\n\nvar Version =  (function () {\n    function Version() {\n    }\n    Version.prototype._parseStoresSpec = function (stores, outSchema) {\n        keys(stores).forEach(function (tableName) {\n            if (stores[tableName] !== null) {\n                var indexes = parseIndexSyntax(stores[tableName]);\n                var primKey = indexes.shift();\n                if (primKey.multi)\n                    throw new exceptions.Schema(\"Primary key cannot be multi-valued\");\n                indexes.forEach(function (idx) {\n                    if (idx.auto)\n                        throw new exceptions.Schema(\"Only primary key can be marked as autoIncrement (++)\");\n                    if (!idx.keyPath)\n                        throw new exceptions.Schema(\"Index must have a name and cannot be an empty string\");\n                });\n                outSchema[tableName] = createTableSchema(tableName, primKey, indexes);\n            }\n        });\n    };\n    Version.prototype.stores = function (stores) {\n        var db = this.db;\n        this._cfg.storesSource = this._cfg.storesSource ?\n            extend(this._cfg.storesSource, stores) :\n            stores;\n        var versions = db._versions;\n        var storesSpec = {};\n        var dbschema = {};\n        versions.forEach(function (version) {\n            extend(storesSpec, version._cfg.storesSource);\n            dbschema = (version._cfg.dbschema = {});\n            version._parseStoresSpec(storesSpec, dbschema);\n        });\n        db._dbSchema = dbschema;\n        removeTablesApi(db, [db._allTables, db, db.Transaction.prototype]);\n        setApiOnPlace(db, [db._allTables, db, db.Transaction.prototype, this._cfg.tables], keys(dbschema), dbschema);\n        db._storeNames = keys(dbschema);\n        return this;\n    };\n    Version.prototype.upgrade = function (upgradeFunction) {\n        this._cfg.contentUpgrade = promisableChain(this._cfg.contentUpgrade || nop, upgradeFunction);\n        return this;\n    };\n    return Version;\n}());\n\nfunction createVersionConstructor(db) {\n    return makeClassConstructor(Version.prototype, function Version(versionNumber) {\n        this.db = db;\n        this._cfg = {\n            version: versionNumber,\n            storesSource: null,\n            dbschema: {},\n            tables: {},\n            contentUpgrade: null\n        };\n    });\n}\n\nfunction getDbNamesTable(indexedDB, IDBKeyRange) {\n    var dbNamesDB = indexedDB[\"_dbNamesDB\"];\n    if (!dbNamesDB) {\n        dbNamesDB = indexedDB[\"_dbNamesDB\"] = new Dexie$1(DBNAMES_DB, {\n            addons: [],\n            indexedDB: indexedDB,\n            IDBKeyRange: IDBKeyRange,\n        });\n        dbNamesDB.version(1).stores({ dbnames: \"name\" });\n    }\n    return dbNamesDB.table(\"dbnames\");\n}\nfunction hasDatabasesNative(indexedDB) {\n    return indexedDB && typeof indexedDB.databases === \"function\";\n}\nfunction getDatabaseNames(_a) {\n    var indexedDB = _a.indexedDB, IDBKeyRange = _a.IDBKeyRange;\n    return hasDatabasesNative(indexedDB)\n        ? Promise.resolve(indexedDB.databases()).then(function (infos) {\n            return infos\n                .map(function (info) { return info.name; })\n                .filter(function (name) { return name !== DBNAMES_DB; });\n        })\n        : getDbNamesTable(indexedDB, IDBKeyRange).toCollection().primaryKeys();\n}\nfunction _onDatabaseCreated(_a, name) {\n    var indexedDB = _a.indexedDB, IDBKeyRange = _a.IDBKeyRange;\n    !hasDatabasesNative(indexedDB) &&\n        name !== DBNAMES_DB &&\n        getDbNamesTable(indexedDB, IDBKeyRange).put({ name: name }).catch(nop);\n}\nfunction _onDatabaseDeleted(_a, name) {\n    var indexedDB = _a.indexedDB, IDBKeyRange = _a.IDBKeyRange;\n    !hasDatabasesNative(indexedDB) &&\n        name !== DBNAMES_DB &&\n        getDbNamesTable(indexedDB, IDBKeyRange).delete(name).catch(nop);\n}\n\nfunction vip(fn) {\n    return newScope(function () {\n        PSD.letThrough = true;\n        return fn();\n    });\n}\n\nfunction idbReady() {\n    var isSafari = !navigator.userAgentData &&\n        /Safari\\//.test(navigator.userAgent) &&\n        !/Chrom(e|ium)\\//.test(navigator.userAgent);\n    if (!isSafari || !indexedDB.databases)\n        return Promise.resolve();\n    var intervalId;\n    return new Promise(function (resolve) {\n        var tryIdb = function () { return indexedDB.databases().finally(resolve); };\n        intervalId = setInterval(tryIdb, 100);\n        tryIdb();\n    }).finally(function () { return clearInterval(intervalId); });\n}\n\nfunction dexieOpen(db) {\n    var state = db._state;\n    var indexedDB = db._deps.indexedDB;\n    if (state.isBeingOpened || db.idbdb)\n        return state.dbReadyPromise.then(function () { return state.dbOpenError ?\n            rejection(state.dbOpenError) :\n            db; });\n    debug && (state.openCanceller._stackHolder = getErrorWithStack());\n    state.isBeingOpened = true;\n    state.dbOpenError = null;\n    state.openComplete = false;\n    var openCanceller = state.openCanceller;\n    function throwIfCancelled() {\n        if (state.openCanceller !== openCanceller)\n            throw new exceptions.DatabaseClosed('db.open() was cancelled');\n    }\n    var resolveDbReady = state.dbReadyResolve,\n    upgradeTransaction = null, wasCreated = false;\n    return DexiePromise.race([openCanceller, (typeof navigator === 'undefined' ? DexiePromise.resolve() : idbReady()).then(function () { return new DexiePromise(function (resolve, reject) {\n            throwIfCancelled();\n            if (!indexedDB)\n                throw new exceptions.MissingAPI();\n            var dbName = db.name;\n            var req = state.autoSchema ?\n                indexedDB.open(dbName) :\n                indexedDB.open(dbName, Math.round(db.verno * 10));\n            if (!req)\n                throw new exceptions.MissingAPI();\n            req.onerror = eventRejectHandler(reject);\n            req.onblocked = wrap(db._fireOnBlocked);\n            req.onupgradeneeded = wrap(function (e) {\n                upgradeTransaction = req.transaction;\n                if (state.autoSchema && !db._options.allowEmptyDB) {\n                    req.onerror = preventDefault;\n                    upgradeTransaction.abort();\n                    req.result.close();\n                    var delreq = indexedDB.deleteDatabase(dbName);\n                    delreq.onsuccess = delreq.onerror = wrap(function () {\n                        reject(new exceptions.NoSuchDatabase(\"Database \" + dbName + \" doesnt exist\"));\n                    });\n                }\n                else {\n                    upgradeTransaction.onerror = eventRejectHandler(reject);\n                    var oldVer = e.oldVersion > Math.pow(2, 62) ? 0 : e.oldVersion;\n                    wasCreated = oldVer < 1;\n                    db._novip.idbdb = req.result;\n                    runUpgraders(db, oldVer / 10, upgradeTransaction, reject);\n                }\n            }, reject);\n            req.onsuccess = wrap(function () {\n                upgradeTransaction = null;\n                var idbdb = db._novip.idbdb = req.result;\n                var objectStoreNames = slice(idbdb.objectStoreNames);\n                if (objectStoreNames.length > 0)\n                    try {\n                        var tmpTrans = idbdb.transaction(safariMultiStoreFix(objectStoreNames), 'readonly');\n                        if (state.autoSchema)\n                            readGlobalSchema(db, idbdb, tmpTrans);\n                        else {\n                            adjustToExistingIndexNames(db, db._dbSchema, tmpTrans);\n                            if (!verifyInstalledSchema(db, tmpTrans)) {\n                                console.warn(\"Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Some queries may fail.\");\n                            }\n                        }\n                        generateMiddlewareStacks(db, tmpTrans);\n                    }\n                    catch (e) {\n                    }\n                connections.push(db);\n                idbdb.onversionchange = wrap(function (ev) {\n                    state.vcFired = true;\n                    db.on(\"versionchange\").fire(ev);\n                });\n                idbdb.onclose = wrap(function (ev) {\n                    db.on(\"close\").fire(ev);\n                });\n                if (wasCreated)\n                    _onDatabaseCreated(db._deps, dbName);\n                resolve();\n            }, reject);\n        }); })]).then(function () {\n        throwIfCancelled();\n        state.onReadyBeingFired = [];\n        return DexiePromise.resolve(vip(function () { return db.on.ready.fire(db.vip); })).then(function fireRemainders() {\n            if (state.onReadyBeingFired.length > 0) {\n                var remainders_1 = state.onReadyBeingFired.reduce(promisableChain, nop);\n                state.onReadyBeingFired = [];\n                return DexiePromise.resolve(vip(function () { return remainders_1(db.vip); })).then(fireRemainders);\n            }\n        });\n    }).finally(function () {\n        state.onReadyBeingFired = null;\n        state.isBeingOpened = false;\n    }).then(function () {\n        return db;\n    }).catch(function (err) {\n        state.dbOpenError = err;\n        try {\n            upgradeTransaction && upgradeTransaction.abort();\n        }\n        catch (_a) { }\n        if (openCanceller === state.openCanceller) {\n            db._close();\n        }\n        return rejection(err);\n    }).finally(function () {\n        state.openComplete = true;\n        resolveDbReady();\n    });\n}\n\nfunction awaitIterator(iterator) {\n    var callNext = function (result) { return iterator.next(result); }, doThrow = function (error) { return iterator.throw(error); }, onSuccess = step(callNext), onError = step(doThrow);\n    function step(getNext) {\n        return function (val) {\n            var next = getNext(val), value = next.value;\n            return next.done ? value :\n                (!value || typeof value.then !== 'function' ?\n                    isArray(value) ? Promise.all(value).then(onSuccess, onError) : onSuccess(value) :\n                    value.then(onSuccess, onError));\n        };\n    }\n    return step(callNext)();\n}\n\nfunction extractTransactionArgs(mode, _tableArgs_, scopeFunc) {\n    var i = arguments.length;\n    if (i < 2)\n        throw new exceptions.InvalidArgument(\"Too few arguments\");\n    var args = new Array(i - 1);\n    while (--i)\n        args[i - 1] = arguments[i];\n    scopeFunc = args.pop();\n    var tables = flatten(args);\n    return [mode, tables, scopeFunc];\n}\nfunction enterTransactionScope(db, mode, storeNames, parentTransaction, scopeFunc) {\n    return DexiePromise.resolve().then(function () {\n        var transless = PSD.transless || PSD;\n        var trans = db._createTransaction(mode, storeNames, db._dbSchema, parentTransaction);\n        var zoneProps = {\n            trans: trans,\n            transless: transless\n        };\n        if (parentTransaction) {\n            trans.idbtrans = parentTransaction.idbtrans;\n        }\n        else {\n            try {\n                trans.create();\n                db._state.PR1398_maxLoop = 3;\n            }\n            catch (ex) {\n                if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n                    console.warn('Dexie: Need to reopen db');\n                    db._close();\n                    return db.open().then(function () { return enterTransactionScope(db, mode, storeNames, null, scopeFunc); });\n                }\n                return rejection(ex);\n            }\n        }\n        var scopeFuncIsAsync = isAsyncFunction(scopeFunc);\n        if (scopeFuncIsAsync) {\n            incrementExpectedAwaits();\n        }\n        var returnValue;\n        var promiseFollowed = DexiePromise.follow(function () {\n            returnValue = scopeFunc.call(trans, trans);\n            if (returnValue) {\n                if (scopeFuncIsAsync) {\n                    var decrementor = decrementExpectedAwaits.bind(null, null);\n                    returnValue.then(decrementor, decrementor);\n                }\n                else if (typeof returnValue.next === 'function' && typeof returnValue.throw === 'function') {\n                    returnValue = awaitIterator(returnValue);\n                }\n            }\n        }, zoneProps);\n        return (returnValue && typeof returnValue.then === 'function' ?\n            DexiePromise.resolve(returnValue).then(function (x) { return trans.active ?\n                x\n                : rejection(new exceptions.PrematureCommit(\"Transaction committed too early. See http://bit.ly/2kdckMn\")); })\n            : promiseFollowed.then(function () { return returnValue; })).then(function (x) {\n            if (parentTransaction)\n                trans._resolve();\n            return trans._completion.then(function () { return x; });\n        }).catch(function (e) {\n            trans._reject(e);\n            return rejection(e);\n        });\n    });\n}\n\nfunction pad(a, value, count) {\n    var result = isArray(a) ? a.slice() : [a];\n    for (var i = 0; i < count; ++i)\n        result.push(value);\n    return result;\n}\nfunction createVirtualIndexMiddleware(down) {\n    return __assign(__assign({}, down), { table: function (tableName) {\n            var table = down.table(tableName);\n            var schema = table.schema;\n            var indexLookup = {};\n            var allVirtualIndexes = [];\n            function addVirtualIndexes(keyPath, keyTail, lowLevelIndex) {\n                var keyPathAlias = getKeyPathAlias(keyPath);\n                var indexList = (indexLookup[keyPathAlias] = indexLookup[keyPathAlias] || []);\n                var keyLength = keyPath == null ? 0 : typeof keyPath === 'string' ? 1 : keyPath.length;\n                var isVirtual = keyTail > 0;\n                var virtualIndex = __assign(__assign({}, lowLevelIndex), { isVirtual: isVirtual, keyTail: keyTail, keyLength: keyLength, extractKey: getKeyExtractor(keyPath), unique: !isVirtual && lowLevelIndex.unique });\n                indexList.push(virtualIndex);\n                if (!virtualIndex.isPrimaryKey) {\n                    allVirtualIndexes.push(virtualIndex);\n                }\n                if (keyLength > 1) {\n                    var virtualKeyPath = keyLength === 2 ?\n                        keyPath[0] :\n                        keyPath.slice(0, keyLength - 1);\n                    addVirtualIndexes(virtualKeyPath, keyTail + 1, lowLevelIndex);\n                }\n                indexList.sort(function (a, b) { return a.keyTail - b.keyTail; });\n                return virtualIndex;\n            }\n            var primaryKey = addVirtualIndexes(schema.primaryKey.keyPath, 0, schema.primaryKey);\n            indexLookup[\":id\"] = [primaryKey];\n            for (var _i = 0, _a = schema.indexes; _i < _a.length; _i++) {\n                var index = _a[_i];\n                addVirtualIndexes(index.keyPath, 0, index);\n            }\n            function findBestIndex(keyPath) {\n                var result = indexLookup[getKeyPathAlias(keyPath)];\n                return result && result[0];\n            }\n            function translateRange(range, keyTail) {\n                return {\n                    type: range.type === 1  ?\n                        2  :\n                        range.type,\n                    lower: pad(range.lower, range.lowerOpen ? down.MAX_KEY : down.MIN_KEY, keyTail),\n                    lowerOpen: true,\n                    upper: pad(range.upper, range.upperOpen ? down.MIN_KEY : down.MAX_KEY, keyTail),\n                    upperOpen: true\n                };\n            }\n            function translateRequest(req) {\n                var index = req.query.index;\n                return index.isVirtual ? __assign(__assign({}, req), { query: {\n                        index: index,\n                        range: translateRange(req.query.range, index.keyTail)\n                    } }) : req;\n            }\n            var result = __assign(__assign({}, table), { schema: __assign(__assign({}, schema), { primaryKey: primaryKey, indexes: allVirtualIndexes, getIndexByKeyPath: findBestIndex }), count: function (req) {\n                    return table.count(translateRequest(req));\n                }, query: function (req) {\n                    return table.query(translateRequest(req));\n                }, openCursor: function (req) {\n                    var _a = req.query.index, keyTail = _a.keyTail, isVirtual = _a.isVirtual, keyLength = _a.keyLength;\n                    if (!isVirtual)\n                        return table.openCursor(req);\n                    function createVirtualCursor(cursor) {\n                        function _continue(key) {\n                            key != null ?\n                                cursor.continue(pad(key, req.reverse ? down.MAX_KEY : down.MIN_KEY, keyTail)) :\n                                req.unique ?\n                                    cursor.continue(cursor.key.slice(0, keyLength)\n                                        .concat(req.reverse\n                                        ? down.MIN_KEY\n                                        : down.MAX_KEY, keyTail)) :\n                                    cursor.continue();\n                        }\n                        var virtualCursor = Object.create(cursor, {\n                            continue: { value: _continue },\n                            continuePrimaryKey: {\n                                value: function (key, primaryKey) {\n                                    cursor.continuePrimaryKey(pad(key, down.MAX_KEY, keyTail), primaryKey);\n                                }\n                            },\n                            primaryKey: {\n                                get: function () {\n                                    return cursor.primaryKey;\n                                }\n                            },\n                            key: {\n                                get: function () {\n                                    var key = cursor.key;\n                                    return keyLength === 1 ?\n                                        key[0] :\n                                        key.slice(0, keyLength);\n                                }\n                            },\n                            value: {\n                                get: function () {\n                                    return cursor.value;\n                                }\n                            }\n                        });\n                        return virtualCursor;\n                    }\n                    return table.openCursor(translateRequest(req))\n                        .then(function (cursor) { return cursor && createVirtualCursor(cursor); });\n                } });\n            return result;\n        } });\n}\nvar virtualIndexMiddleware = {\n    stack: \"dbcore\",\n    name: \"VirtualIndexMiddleware\",\n    level: 1,\n    create: createVirtualIndexMiddleware\n};\n\nfunction getObjectDiff(a, b, rv, prfx) {\n    rv = rv || {};\n    prfx = prfx || '';\n    keys(a).forEach(function (prop) {\n        if (!hasOwn(b, prop)) {\n            rv[prfx + prop] = undefined;\n        }\n        else {\n            var ap = a[prop], bp = b[prop];\n            if (typeof ap === 'object' && typeof bp === 'object' && ap && bp) {\n                var apTypeName = toStringTag(ap);\n                var bpTypeName = toStringTag(bp);\n                if (apTypeName !== bpTypeName) {\n                    rv[prfx + prop] = b[prop];\n                }\n                else if (apTypeName === 'Object') {\n                    getObjectDiff(ap, bp, rv, prfx + prop + '.');\n                }\n                else if (ap !== bp) {\n                    rv[prfx + prop] = b[prop];\n                }\n            }\n            else if (ap !== bp)\n                rv[prfx + prop] = b[prop];\n        }\n    });\n    keys(b).forEach(function (prop) {\n        if (!hasOwn(a, prop)) {\n            rv[prfx + prop] = b[prop];\n        }\n    });\n    return rv;\n}\n\nfunction getEffectiveKeys(primaryKey, req) {\n    if (req.type === 'delete')\n        return req.keys;\n    return req.keys || req.values.map(primaryKey.extractKey);\n}\n\nvar hooksMiddleware = {\n    stack: \"dbcore\",\n    name: \"HooksMiddleware\",\n    level: 2,\n    create: function (downCore) { return (__assign(__assign({}, downCore), { table: function (tableName) {\n            var downTable = downCore.table(tableName);\n            var primaryKey = downTable.schema.primaryKey;\n            var tableMiddleware = __assign(__assign({}, downTable), { mutate: function (req) {\n                    var dxTrans = PSD.trans;\n                    var _a = dxTrans.table(tableName).hook, deleting = _a.deleting, creating = _a.creating, updating = _a.updating;\n                    switch (req.type) {\n                        case 'add':\n                            if (creating.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', function () { return addPutOrDelete(req); }, true);\n                        case 'put':\n                            if (creating.fire === nop && updating.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', function () { return addPutOrDelete(req); }, true);\n                        case 'delete':\n                            if (deleting.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', function () { return addPutOrDelete(req); }, true);\n                        case 'deleteRange':\n                            if (deleting.fire === nop)\n                                break;\n                            return dxTrans._promise('readwrite', function () { return deleteRange(req); }, true);\n                    }\n                    return downTable.mutate(req);\n                    function addPutOrDelete(req) {\n                        var dxTrans = PSD.trans;\n                        var keys = req.keys || getEffectiveKeys(primaryKey, req);\n                        if (!keys)\n                            throw new Error(\"Keys missing\");\n                        req = req.type === 'add' || req.type === 'put' ? __assign(__assign({}, req), { keys: keys }) : __assign({}, req);\n                        if (req.type !== 'delete')\n                            req.values = __spreadArray([], req.values, true);\n                        if (req.keys)\n                            req.keys = __spreadArray([], req.keys, true);\n                        return getExistingValues(downTable, req, keys).then(function (existingValues) {\n                            var contexts = keys.map(function (key, i) {\n                                var existingValue = existingValues[i];\n                                var ctx = { onerror: null, onsuccess: null };\n                                if (req.type === 'delete') {\n                                    deleting.fire.call(ctx, key, existingValue, dxTrans);\n                                }\n                                else if (req.type === 'add' || existingValue === undefined) {\n                                    var generatedPrimaryKey = creating.fire.call(ctx, key, req.values[i], dxTrans);\n                                    if (key == null && generatedPrimaryKey != null) {\n                                        key = generatedPrimaryKey;\n                                        req.keys[i] = key;\n                                        if (!primaryKey.outbound) {\n                                            setByKeyPath(req.values[i], primaryKey.keyPath, key);\n                                        }\n                                    }\n                                }\n                                else {\n                                    var objectDiff = getObjectDiff(existingValue, req.values[i]);\n                                    var additionalChanges_1 = updating.fire.call(ctx, objectDiff, key, existingValue, dxTrans);\n                                    if (additionalChanges_1) {\n                                        var requestedValue_1 = req.values[i];\n                                        Object.keys(additionalChanges_1).forEach(function (keyPath) {\n                                            if (hasOwn(requestedValue_1, keyPath)) {\n                                                requestedValue_1[keyPath] = additionalChanges_1[keyPath];\n                                            }\n                                            else {\n                                                setByKeyPath(requestedValue_1, keyPath, additionalChanges_1[keyPath]);\n                                            }\n                                        });\n                                    }\n                                }\n                                return ctx;\n                            });\n                            return downTable.mutate(req).then(function (_a) {\n                                var failures = _a.failures, results = _a.results, numFailures = _a.numFailures, lastResult = _a.lastResult;\n                                for (var i = 0; i < keys.length; ++i) {\n                                    var primKey = results ? results[i] : keys[i];\n                                    var ctx = contexts[i];\n                                    if (primKey == null) {\n                                        ctx.onerror && ctx.onerror(failures[i]);\n                                    }\n                                    else {\n                                        ctx.onsuccess && ctx.onsuccess(req.type === 'put' && existingValues[i] ?\n                                            req.values[i] :\n                                            primKey\n                                        );\n                                    }\n                                }\n                                return { failures: failures, results: results, numFailures: numFailures, lastResult: lastResult };\n                            }).catch(function (error) {\n                                contexts.forEach(function (ctx) { return ctx.onerror && ctx.onerror(error); });\n                                return Promise.reject(error);\n                            });\n                        });\n                    }\n                    function deleteRange(req) {\n                        return deleteNextChunk(req.trans, req.range, 10000);\n                    }\n                    function deleteNextChunk(trans, range, limit) {\n                        return downTable.query({ trans: trans, values: false, query: { index: primaryKey, range: range }, limit: limit })\n                            .then(function (_a) {\n                            var result = _a.result;\n                            return addPutOrDelete({ type: 'delete', keys: result, trans: trans }).then(function (res) {\n                                if (res.numFailures > 0)\n                                    return Promise.reject(res.failures[0]);\n                                if (result.length < limit) {\n                                    return { failures: [], numFailures: 0, lastResult: undefined };\n                                }\n                                else {\n                                    return deleteNextChunk(trans, __assign(__assign({}, range), { lower: result[result.length - 1], lowerOpen: true }), limit);\n                                }\n                            });\n                        });\n                    }\n                } });\n            return tableMiddleware;\n        } })); }\n};\nfunction getExistingValues(table, req, effectiveKeys) {\n    return req.type === \"add\"\n        ? Promise.resolve([])\n        : table.getMany({ trans: req.trans, keys: effectiveKeys, cache: \"immutable\" });\n}\n\nfunction getFromTransactionCache(keys, cache, clone) {\n    try {\n        if (!cache)\n            return null;\n        if (cache.keys.length < keys.length)\n            return null;\n        var result = [];\n        for (var i = 0, j = 0; i < cache.keys.length && j < keys.length; ++i) {\n            if (cmp(cache.keys[i], keys[j]) !== 0)\n                continue;\n            result.push(clone ? deepClone(cache.values[i]) : cache.values[i]);\n            ++j;\n        }\n        return result.length === keys.length ? result : null;\n    }\n    catch (_a) {\n        return null;\n    }\n}\nvar cacheExistingValuesMiddleware = {\n    stack: \"dbcore\",\n    level: -1,\n    create: function (core) {\n        return {\n            table: function (tableName) {\n                var table = core.table(tableName);\n                return __assign(__assign({}, table), { getMany: function (req) {\n                        if (!req.cache) {\n                            return table.getMany(req);\n                        }\n                        var cachedResult = getFromTransactionCache(req.keys, req.trans[\"_cache\"], req.cache === \"clone\");\n                        if (cachedResult) {\n                            return DexiePromise.resolve(cachedResult);\n                        }\n                        return table.getMany(req).then(function (res) {\n                            req.trans[\"_cache\"] = {\n                                keys: req.keys,\n                                values: req.cache === \"clone\" ? deepClone(res) : res,\n                            };\n                            return res;\n                        });\n                    }, mutate: function (req) {\n                        if (req.type !== \"add\")\n                            req.trans[\"_cache\"] = null;\n                        return table.mutate(req);\n                    } });\n            },\n        };\n    },\n};\n\nvar _a;\nfunction isEmptyRange(node) {\n    return !(\"from\" in node);\n}\nvar RangeSet = function (fromOrTree, to) {\n    if (this) {\n        extend(this, arguments.length ? { d: 1, from: fromOrTree, to: arguments.length > 1 ? to : fromOrTree } : { d: 0 });\n    }\n    else {\n        var rv = new RangeSet();\n        if (fromOrTree && (\"d\" in fromOrTree)) {\n            extend(rv, fromOrTree);\n        }\n        return rv;\n    }\n};\nprops(RangeSet.prototype, (_a = {\n        add: function (rangeSet) {\n            mergeRanges(this, rangeSet);\n            return this;\n        },\n        addKey: function (key) {\n            addRange(this, key, key);\n            return this;\n        },\n        addKeys: function (keys) {\n            var _this = this;\n            keys.forEach(function (key) { return addRange(_this, key, key); });\n            return this;\n        }\n    },\n    _a[iteratorSymbol] = function () {\n        return getRangeSetIterator(this);\n    },\n    _a));\nfunction addRange(target, from, to) {\n    var diff = cmp(from, to);\n    if (isNaN(diff))\n        return;\n    if (diff > 0)\n        throw RangeError();\n    if (isEmptyRange(target))\n        return extend(target, { from: from, to: to, d: 1 });\n    var left = target.l;\n    var right = target.r;\n    if (cmp(to, target.from) < 0) {\n        left\n            ? addRange(left, from, to)\n            : (target.l = { from: from, to: to, d: 1, l: null, r: null });\n        return rebalance(target);\n    }\n    if (cmp(from, target.to) > 0) {\n        right\n            ? addRange(right, from, to)\n            : (target.r = { from: from, to: to, d: 1, l: null, r: null });\n        return rebalance(target);\n    }\n    if (cmp(from, target.from) < 0) {\n        target.from = from;\n        target.l = null;\n        target.d = right ? right.d + 1 : 1;\n    }\n    if (cmp(to, target.to) > 0) {\n        target.to = to;\n        target.r = null;\n        target.d = target.l ? target.l.d + 1 : 1;\n    }\n    var rightWasCutOff = !target.r;\n    if (left && !target.l) {\n        mergeRanges(target, left);\n    }\n    if (right && rightWasCutOff) {\n        mergeRanges(target, right);\n    }\n}\nfunction mergeRanges(target, newSet) {\n    function _addRangeSet(target, _a) {\n        var from = _a.from, to = _a.to, l = _a.l, r = _a.r;\n        addRange(target, from, to);\n        if (l)\n            _addRangeSet(target, l);\n        if (r)\n            _addRangeSet(target, r);\n    }\n    if (!isEmptyRange(newSet))\n        _addRangeSet(target, newSet);\n}\nfunction rangesOverlap(rangeSet1, rangeSet2) {\n    var i1 = getRangeSetIterator(rangeSet2);\n    var nextResult1 = i1.next();\n    if (nextResult1.done)\n        return false;\n    var a = nextResult1.value;\n    var i2 = getRangeSetIterator(rangeSet1);\n    var nextResult2 = i2.next(a.from);\n    var b = nextResult2.value;\n    while (!nextResult1.done && !nextResult2.done) {\n        if (cmp(b.from, a.to) <= 0 && cmp(b.to, a.from) >= 0)\n            return true;\n        cmp(a.from, b.from) < 0\n            ? (a = (nextResult1 = i1.next(b.from)).value)\n            : (b = (nextResult2 = i2.next(a.from)).value);\n    }\n    return false;\n}\nfunction getRangeSetIterator(node) {\n    var state = isEmptyRange(node) ? null : { s: 0, n: node };\n    return {\n        next: function (key) {\n            var keyProvided = arguments.length > 0;\n            while (state) {\n                switch (state.s) {\n                    case 0:\n                        state.s = 1;\n                        if (keyProvided) {\n                            while (state.n.l && cmp(key, state.n.from) < 0)\n                                state = { up: state, n: state.n.l, s: 1 };\n                        }\n                        else {\n                            while (state.n.l)\n                                state = { up: state, n: state.n.l, s: 1 };\n                        }\n                    case 1:\n                        state.s = 2;\n                        if (!keyProvided || cmp(key, state.n.to) <= 0)\n                            return { value: state.n, done: false };\n                    case 2:\n                        if (state.n.r) {\n                            state.s = 3;\n                            state = { up: state, n: state.n.r, s: 0 };\n                            continue;\n                        }\n                    case 3:\n                        state = state.up;\n                }\n            }\n            return { done: true };\n        },\n    };\n}\nfunction rebalance(target) {\n    var _a, _b;\n    var diff = (((_a = target.r) === null || _a === void 0 ? void 0 : _a.d) || 0) - (((_b = target.l) === null || _b === void 0 ? void 0 : _b.d) || 0);\n    var r = diff > 1 ? \"r\" : diff < -1 ? \"l\" : \"\";\n    if (r) {\n        var l = r === \"r\" ? \"l\" : \"r\";\n        var rootClone = __assign({}, target);\n        var oldRootRight = target[r];\n        target.from = oldRootRight.from;\n        target.to = oldRootRight.to;\n        target[r] = oldRootRight[r];\n        rootClone[r] = oldRootRight[l];\n        target[l] = rootClone;\n        rootClone.d = computeDepth(rootClone);\n    }\n    target.d = computeDepth(target);\n}\nfunction computeDepth(_a) {\n    var r = _a.r, l = _a.l;\n    return (r ? (l ? Math.max(r.d, l.d) : r.d) : l ? l.d : 0) + 1;\n}\n\nvar observabilityMiddleware = {\n    stack: \"dbcore\",\n    level: 0,\n    create: function (core) {\n        var dbName = core.schema.name;\n        var FULL_RANGE = new RangeSet(core.MIN_KEY, core.MAX_KEY);\n        return __assign(__assign({}, core), { table: function (tableName) {\n                var table = core.table(tableName);\n                var schema = table.schema;\n                var primaryKey = schema.primaryKey;\n                var extractKey = primaryKey.extractKey, outbound = primaryKey.outbound;\n                var tableClone = __assign(__assign({}, table), { mutate: function (req) {\n                        var trans = req.trans;\n                        var mutatedParts = trans.mutatedParts || (trans.mutatedParts = {});\n                        var getRangeSet = function (indexName) {\n                            var part = \"idb://\" + dbName + \"/\" + tableName + \"/\" + indexName;\n                            return (mutatedParts[part] ||\n                                (mutatedParts[part] = new RangeSet()));\n                        };\n                        var pkRangeSet = getRangeSet(\"\");\n                        var delsRangeSet = getRangeSet(\":dels\");\n                        var type = req.type;\n                        var _a = req.type === \"deleteRange\"\n                            ? [req.range]\n                            : req.type === \"delete\"\n                                ? [req.keys]\n                                : req.values.length < 50\n                                    ? [[], req.values]\n                                    : [], keys = _a[0], newObjs = _a[1];\n                        var oldCache = req.trans[\"_cache\"];\n                        return table.mutate(req).then(function (res) {\n                            if (isArray(keys)) {\n                                if (type !== \"delete\")\n                                    keys = res.results;\n                                pkRangeSet.addKeys(keys);\n                                var oldObjs = getFromTransactionCache(keys, oldCache);\n                                if (!oldObjs && type !== \"add\") {\n                                    delsRangeSet.addKeys(keys);\n                                }\n                                if (oldObjs || newObjs) {\n                                    trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs);\n                                }\n                            }\n                            else if (keys) {\n                                var range = { from: keys.lower, to: keys.upper };\n                                delsRangeSet.add(range);\n                                pkRangeSet.add(range);\n                            }\n                            else {\n                                pkRangeSet.add(FULL_RANGE);\n                                delsRangeSet.add(FULL_RANGE);\n                                schema.indexes.forEach(function (idx) { return getRangeSet(idx.name).add(FULL_RANGE); });\n                            }\n                            return res;\n                        });\n                    } });\n                var getRange = function (_a) {\n                    var _b, _c;\n                    var _d = _a.query, index = _d.index, range = _d.range;\n                    return [\n                        index,\n                        new RangeSet((_b = range.lower) !== null && _b !== void 0 ? _b : core.MIN_KEY, (_c = range.upper) !== null && _c !== void 0 ? _c : core.MAX_KEY),\n                    ];\n                };\n                var readSubscribers = {\n                    get: function (req) { return [primaryKey, new RangeSet(req.key)]; },\n                    getMany: function (req) { return [primaryKey, new RangeSet().addKeys(req.keys)]; },\n                    count: getRange,\n                    query: getRange,\n                    openCursor: getRange,\n                };\n                keys(readSubscribers).forEach(function (method) {\n                    tableClone[method] = function (req) {\n                        var subscr = PSD.subscr;\n                        if (subscr) {\n                            var getRangeSet = function (indexName) {\n                                var part = \"idb://\" + dbName + \"/\" + tableName + \"/\" + indexName;\n                                return (subscr[part] ||\n                                    (subscr[part] = new RangeSet()));\n                            };\n                            var pkRangeSet_1 = getRangeSet(\"\");\n                            var delsRangeSet_1 = getRangeSet(\":dels\");\n                            var _a = readSubscribers[method](req), queriedIndex = _a[0], queriedRanges = _a[1];\n                            getRangeSet(queriedIndex.name || \"\").add(queriedRanges);\n                            if (!queriedIndex.isPrimaryKey) {\n                                if (method === \"count\") {\n                                    delsRangeSet_1.add(FULL_RANGE);\n                                }\n                                else {\n                                    var keysPromise_1 = method === \"query\" &&\n                                        outbound &&\n                                        req.values &&\n                                        table.query(__assign(__assign({}, req), { values: false }));\n                                    return table[method].apply(this, arguments).then(function (res) {\n                                        if (method === \"query\") {\n                                            if (outbound && req.values) {\n                                                return keysPromise_1.then(function (_a) {\n                                                    var resultingKeys = _a.result;\n                                                    pkRangeSet_1.addKeys(resultingKeys);\n                                                    return res;\n                                                });\n                                            }\n                                            var pKeys = req.values\n                                                ? res.result.map(extractKey)\n                                                : res.result;\n                                            if (req.values) {\n                                                pkRangeSet_1.addKeys(pKeys);\n                                            }\n                                            else {\n                                                delsRangeSet_1.addKeys(pKeys);\n                                            }\n                                        }\n                                        else if (method === \"openCursor\") {\n                                            var cursor_1 = res;\n                                            var wantValues_1 = req.values;\n                                            return (cursor_1 &&\n                                                Object.create(cursor_1, {\n                                                    key: {\n                                                        get: function () {\n                                                            delsRangeSet_1.addKey(cursor_1.primaryKey);\n                                                            return cursor_1.key;\n                                                        },\n                                                    },\n                                                    primaryKey: {\n                                                        get: function () {\n                                                            var pkey = cursor_1.primaryKey;\n                                                            delsRangeSet_1.addKey(pkey);\n                                                            return pkey;\n                                                        },\n                                                    },\n                                                    value: {\n                                                        get: function () {\n                                                            wantValues_1 && pkRangeSet_1.addKey(cursor_1.primaryKey);\n                                                            return cursor_1.value;\n                                                        },\n                                                    },\n                                                }));\n                                        }\n                                        return res;\n                                    });\n                                }\n                            }\n                        }\n                        return table[method].apply(this, arguments);\n                    };\n                });\n                return tableClone;\n            } });\n    },\n};\nfunction trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs) {\n    function addAffectedIndex(ix) {\n        var rangeSet = getRangeSet(ix.name || \"\");\n        function extractKey(obj) {\n            return obj != null ? ix.extractKey(obj) : null;\n        }\n        var addKeyOrKeys = function (key) { return ix.multiEntry && isArray(key)\n            ? key.forEach(function (key) { return rangeSet.addKey(key); })\n            : rangeSet.addKey(key); };\n        (oldObjs || newObjs).forEach(function (_, i) {\n            var oldKey = oldObjs && extractKey(oldObjs[i]);\n            var newKey = newObjs && extractKey(newObjs[i]);\n            if (cmp(oldKey, newKey) !== 0) {\n                if (oldKey != null)\n                    addKeyOrKeys(oldKey);\n                if (newKey != null)\n                    addKeyOrKeys(newKey);\n            }\n        });\n    }\n    schema.indexes.forEach(addAffectedIndex);\n}\n\nvar Dexie$1 =  (function () {\n    function Dexie(name, options) {\n        var _this = this;\n        this._middlewares = {};\n        this.verno = 0;\n        var deps = Dexie.dependencies;\n        this._options = options = __assign({\n            addons: Dexie.addons, autoOpen: true,\n            indexedDB: deps.indexedDB, IDBKeyRange: deps.IDBKeyRange }, options);\n        this._deps = {\n            indexedDB: options.indexedDB,\n            IDBKeyRange: options.IDBKeyRange\n        };\n        var addons = options.addons;\n        this._dbSchema = {};\n        this._versions = [];\n        this._storeNames = [];\n        this._allTables = {};\n        this.idbdb = null;\n        this._novip = this;\n        var state = {\n            dbOpenError: null,\n            isBeingOpened: false,\n            onReadyBeingFired: null,\n            openComplete: false,\n            dbReadyResolve: nop,\n            dbReadyPromise: null,\n            cancelOpen: nop,\n            openCanceller: null,\n            autoSchema: true,\n            PR1398_maxLoop: 3\n        };\n        state.dbReadyPromise = new DexiePromise(function (resolve) {\n            state.dbReadyResolve = resolve;\n        });\n        state.openCanceller = new DexiePromise(function (_, reject) {\n            state.cancelOpen = reject;\n        });\n        this._state = state;\n        this.name = name;\n        this.on = Events(this, \"populate\", \"blocked\", \"versionchange\", \"close\", { ready: [promisableChain, nop] });\n        this.on.ready.subscribe = override(this.on.ready.subscribe, function (subscribe) {\n            return function (subscriber, bSticky) {\n                Dexie.vip(function () {\n                    var state = _this._state;\n                    if (state.openComplete) {\n                        if (!state.dbOpenError)\n                            DexiePromise.resolve().then(subscriber);\n                        if (bSticky)\n                            subscribe(subscriber);\n                    }\n                    else if (state.onReadyBeingFired) {\n                        state.onReadyBeingFired.push(subscriber);\n                        if (bSticky)\n                            subscribe(subscriber);\n                    }\n                    else {\n                        subscribe(subscriber);\n                        var db_1 = _this;\n                        if (!bSticky)\n                            subscribe(function unsubscribe() {\n                                db_1.on.ready.unsubscribe(subscriber);\n                                db_1.on.ready.unsubscribe(unsubscribe);\n                            });\n                    }\n                });\n            };\n        });\n        this.Collection = createCollectionConstructor(this);\n        this.Table = createTableConstructor(this);\n        this.Transaction = createTransactionConstructor(this);\n        this.Version = createVersionConstructor(this);\n        this.WhereClause = createWhereClauseConstructor(this);\n        this.on(\"versionchange\", function (ev) {\n            if (ev.newVersion > 0)\n                console.warn(\"Another connection wants to upgrade database '\" + _this.name + \"'. Closing db now to resume the upgrade.\");\n            else\n                console.warn(\"Another connection wants to delete database '\" + _this.name + \"'. Closing db now to resume the delete request.\");\n            _this.close();\n        });\n        this.on(\"blocked\", function (ev) {\n            if (!ev.newVersion || ev.newVersion < ev.oldVersion)\n                console.warn(\"Dexie.delete('\" + _this.name + \"') was blocked\");\n            else\n                console.warn(\"Upgrade '\" + _this.name + \"' blocked by other connection holding version \" + ev.oldVersion / 10);\n        });\n        this._maxKey = getMaxKey(options.IDBKeyRange);\n        this._createTransaction = function (mode, storeNames, dbschema, parentTransaction) { return new _this.Transaction(mode, storeNames, dbschema, _this._options.chromeTransactionDurability, parentTransaction); };\n        this._fireOnBlocked = function (ev) {\n            _this.on(\"blocked\").fire(ev);\n            connections\n                .filter(function (c) { return c.name === _this.name && c !== _this && !c._state.vcFired; })\n                .map(function (c) { return c.on(\"versionchange\").fire(ev); });\n        };\n        this.use(virtualIndexMiddleware);\n        this.use(hooksMiddleware);\n        this.use(observabilityMiddleware);\n        this.use(cacheExistingValuesMiddleware);\n        this.vip = Object.create(this, { _vip: { value: true } });\n        addons.forEach(function (addon) { return addon(_this); });\n    }\n    Dexie.prototype.version = function (versionNumber) {\n        if (isNaN(versionNumber) || versionNumber < 0.1)\n            throw new exceptions.Type(\"Given version is not a positive number\");\n        versionNumber = Math.round(versionNumber * 10) / 10;\n        if (this.idbdb || this._state.isBeingOpened)\n            throw new exceptions.Schema(\"Cannot add version when database is open\");\n        this.verno = Math.max(this.verno, versionNumber);\n        var versions = this._versions;\n        var versionInstance = versions.filter(function (v) { return v._cfg.version === versionNumber; })[0];\n        if (versionInstance)\n            return versionInstance;\n        versionInstance = new this.Version(versionNumber);\n        versions.push(versionInstance);\n        versions.sort(lowerVersionFirst);\n        versionInstance.stores({});\n        this._state.autoSchema = false;\n        return versionInstance;\n    };\n    Dexie.prototype._whenReady = function (fn) {\n        var _this = this;\n        return (this.idbdb && (this._state.openComplete || PSD.letThrough || this._vip)) ? fn() : new DexiePromise(function (resolve, reject) {\n            if (_this._state.openComplete) {\n                return reject(new exceptions.DatabaseClosed(_this._state.dbOpenError));\n            }\n            if (!_this._state.isBeingOpened) {\n                if (!_this._options.autoOpen) {\n                    reject(new exceptions.DatabaseClosed());\n                    return;\n                }\n                _this.open().catch(nop);\n            }\n            _this._state.dbReadyPromise.then(resolve, reject);\n        }).then(fn);\n    };\n    Dexie.prototype.use = function (_a) {\n        var stack = _a.stack, create = _a.create, level = _a.level, name = _a.name;\n        if (name)\n            this.unuse({ stack: stack, name: name });\n        var middlewares = this._middlewares[stack] || (this._middlewares[stack] = []);\n        middlewares.push({ stack: stack, create: create, level: level == null ? 10 : level, name: name });\n        middlewares.sort(function (a, b) { return a.level - b.level; });\n        return this;\n    };\n    Dexie.prototype.unuse = function (_a) {\n        var stack = _a.stack, name = _a.name, create = _a.create;\n        if (stack && this._middlewares[stack]) {\n            this._middlewares[stack] = this._middlewares[stack].filter(function (mw) {\n                return create ? mw.create !== create :\n                    name ? mw.name !== name :\n                        false;\n            });\n        }\n        return this;\n    };\n    Dexie.prototype.open = function () {\n        return dexieOpen(this);\n    };\n    Dexie.prototype._close = function () {\n        var state = this._state;\n        var idx = connections.indexOf(this);\n        if (idx >= 0)\n            connections.splice(idx, 1);\n        if (this.idbdb) {\n            try {\n                this.idbdb.close();\n            }\n            catch (e) { }\n            this._novip.idbdb = null;\n        }\n        state.dbReadyPromise = new DexiePromise(function (resolve) {\n            state.dbReadyResolve = resolve;\n        });\n        state.openCanceller = new DexiePromise(function (_, reject) {\n            state.cancelOpen = reject;\n        });\n    };\n    Dexie.prototype.close = function () {\n        this._close();\n        var state = this._state;\n        this._options.autoOpen = false;\n        state.dbOpenError = new exceptions.DatabaseClosed();\n        if (state.isBeingOpened)\n            state.cancelOpen(state.dbOpenError);\n    };\n    Dexie.prototype.delete = function () {\n        var _this = this;\n        var hasArguments = arguments.length > 0;\n        var state = this._state;\n        return new DexiePromise(function (resolve, reject) {\n            var doDelete = function () {\n                _this.close();\n                var req = _this._deps.indexedDB.deleteDatabase(_this.name);\n                req.onsuccess = wrap(function () {\n                    _onDatabaseDeleted(_this._deps, _this.name);\n                    resolve();\n                });\n                req.onerror = eventRejectHandler(reject);\n                req.onblocked = _this._fireOnBlocked;\n            };\n            if (hasArguments)\n                throw new exceptions.InvalidArgument(\"Arguments not allowed in db.delete()\");\n            if (state.isBeingOpened) {\n                state.dbReadyPromise.then(doDelete);\n            }\n            else {\n                doDelete();\n            }\n        });\n    };\n    Dexie.prototype.backendDB = function () {\n        return this.idbdb;\n    };\n    Dexie.prototype.isOpen = function () {\n        return this.idbdb !== null;\n    };\n    Dexie.prototype.hasBeenClosed = function () {\n        var dbOpenError = this._state.dbOpenError;\n        return dbOpenError && (dbOpenError.name === 'DatabaseClosed');\n    };\n    Dexie.prototype.hasFailed = function () {\n        return this._state.dbOpenError !== null;\n    };\n    Dexie.prototype.dynamicallyOpened = function () {\n        return this._state.autoSchema;\n    };\n    Object.defineProperty(Dexie.prototype, \"tables\", {\n        get: function () {\n            var _this = this;\n            return keys(this._allTables).map(function (name) { return _this._allTables[name]; });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Dexie.prototype.transaction = function () {\n        var args = extractTransactionArgs.apply(this, arguments);\n        return this._transaction.apply(this, args);\n    };\n    Dexie.prototype._transaction = function (mode, tables, scopeFunc) {\n        var _this = this;\n        var parentTransaction = PSD.trans;\n        if (!parentTransaction || parentTransaction.db !== this || mode.indexOf('!') !== -1)\n            parentTransaction = null;\n        var onlyIfCompatible = mode.indexOf('?') !== -1;\n        mode = mode.replace('!', '').replace('?', '');\n        var idbMode, storeNames;\n        try {\n            storeNames = tables.map(function (table) {\n                var storeName = table instanceof _this.Table ? table.name : table;\n                if (typeof storeName !== 'string')\n                    throw new TypeError(\"Invalid table argument to Dexie.transaction(). Only Table or String are allowed\");\n                return storeName;\n            });\n            if (mode == \"r\" || mode === READONLY)\n                idbMode = READONLY;\n            else if (mode == \"rw\" || mode == READWRITE)\n                idbMode = READWRITE;\n            else\n                throw new exceptions.InvalidArgument(\"Invalid transaction mode: \" + mode);\n            if (parentTransaction) {\n                if (parentTransaction.mode === READONLY && idbMode === READWRITE) {\n                    if (onlyIfCompatible) {\n                        parentTransaction = null;\n                    }\n                    else\n                        throw new exceptions.SubTransaction(\"Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY\");\n                }\n                if (parentTransaction) {\n                    storeNames.forEach(function (storeName) {\n                        if (parentTransaction && parentTransaction.storeNames.indexOf(storeName) === -1) {\n                            if (onlyIfCompatible) {\n                                parentTransaction = null;\n                            }\n                            else\n                                throw new exceptions.SubTransaction(\"Table \" + storeName +\n                                    \" not included in parent transaction.\");\n                        }\n                    });\n                }\n                if (onlyIfCompatible && parentTransaction && !parentTransaction.active) {\n                    parentTransaction = null;\n                }\n            }\n        }\n        catch (e) {\n            return parentTransaction ?\n                parentTransaction._promise(null, function (_, reject) { reject(e); }) :\n                rejection(e);\n        }\n        var enterTransaction = enterTransactionScope.bind(null, this, idbMode, storeNames, parentTransaction, scopeFunc);\n        return (parentTransaction ?\n            parentTransaction._promise(idbMode, enterTransaction, \"lock\") :\n            PSD.trans ?\n                usePSD(PSD.transless, function () { return _this._whenReady(enterTransaction); }) :\n                this._whenReady(enterTransaction));\n    };\n    Dexie.prototype.table = function (tableName) {\n        if (!hasOwn(this._allTables, tableName)) {\n            throw new exceptions.InvalidTable(\"Table \" + tableName + \" does not exist\");\n        }\n        return this._allTables[tableName];\n    };\n    return Dexie;\n}());\n\nvar symbolObservable = typeof Symbol !== \"undefined\" && \"observable\" in Symbol\n    ? Symbol.observable\n    : \"@@observable\";\nvar Observable =  (function () {\n    function Observable(subscribe) {\n        this._subscribe = subscribe;\n    }\n    Observable.prototype.subscribe = function (x, error, complete) {\n        return this._subscribe(!x || typeof x === \"function\" ? { next: x, error: error, complete: complete } : x);\n    };\n    Observable.prototype[symbolObservable] = function () {\n        return this;\n    };\n    return Observable;\n}());\n\nfunction extendObservabilitySet(target, newSet) {\n    keys(newSet).forEach(function (part) {\n        var rangeSet = target[part] || (target[part] = new RangeSet());\n        mergeRanges(rangeSet, newSet[part]);\n    });\n    return target;\n}\n\nfunction liveQuery(querier) {\n    var hasValue = false;\n    var currentValue = undefined;\n    var observable = new Observable(function (observer) {\n        var scopeFuncIsAsync = isAsyncFunction(querier);\n        function execute(subscr) {\n            if (scopeFuncIsAsync) {\n                incrementExpectedAwaits();\n            }\n            var exec = function () { return newScope(querier, { subscr: subscr, trans: null }); };\n            var rv = PSD.trans\n                ?\n                    usePSD(PSD.transless, exec)\n                : exec();\n            if (scopeFuncIsAsync) {\n                rv.then(decrementExpectedAwaits, decrementExpectedAwaits);\n            }\n            return rv;\n        }\n        var closed = false;\n        var accumMuts = {};\n        var currentObs = {};\n        var subscription = {\n            get closed() {\n                return closed;\n            },\n            unsubscribe: function () {\n                closed = true;\n                globalEvents.storagemutated.unsubscribe(mutationListener);\n            },\n        };\n        observer.start && observer.start(subscription);\n        var querying = false, startedListening = false;\n        function shouldNotify() {\n            return keys(currentObs).some(function (key) {\n                return accumMuts[key] && rangesOverlap(accumMuts[key], currentObs[key]);\n            });\n        }\n        var mutationListener = function (parts) {\n            extendObservabilitySet(accumMuts, parts);\n            if (shouldNotify()) {\n                doQuery();\n            }\n        };\n        var doQuery = function () {\n            if (querying || closed)\n                return;\n            accumMuts = {};\n            var subscr = {};\n            var ret = execute(subscr);\n            if (!startedListening) {\n                globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, mutationListener);\n                startedListening = true;\n            }\n            querying = true;\n            Promise.resolve(ret).then(function (result) {\n                hasValue = true;\n                currentValue = result;\n                querying = false;\n                if (closed)\n                    return;\n                if (shouldNotify()) {\n                    doQuery();\n                }\n                else {\n                    accumMuts = {};\n                    currentObs = subscr;\n                    observer.next && observer.next(result);\n                }\n            }, function (err) {\n                querying = false;\n                hasValue = false;\n                observer.error && observer.error(err);\n                subscription.unsubscribe();\n            });\n        };\n        doQuery();\n        return subscription;\n    });\n    observable.hasValue = function () { return hasValue; };\n    observable.getValue = function () { return currentValue; };\n    return observable;\n}\n\nvar domDeps;\ntry {\n    domDeps = {\n        indexedDB: _global.indexedDB || _global.mozIndexedDB || _global.webkitIndexedDB || _global.msIndexedDB,\n        IDBKeyRange: _global.IDBKeyRange || _global.webkitIDBKeyRange\n    };\n}\ncatch (e) {\n    domDeps = { indexedDB: null, IDBKeyRange: null };\n}\n\nvar Dexie = Dexie$1;\nprops(Dexie, __assign(__assign({}, fullNameExceptions), {\n    delete: function (databaseName) {\n        var db = new Dexie(databaseName, { addons: [] });\n        return db.delete();\n    },\n    exists: function (name) {\n        return new Dexie(name, { addons: [] }).open().then(function (db) {\n            db.close();\n            return true;\n        }).catch('NoSuchDatabaseError', function () { return false; });\n    },\n    getDatabaseNames: function (cb) {\n        try {\n            return getDatabaseNames(Dexie.dependencies).then(cb);\n        }\n        catch (_a) {\n            return rejection(new exceptions.MissingAPI());\n        }\n    },\n    defineClass: function () {\n        function Class(content) {\n            extend(this, content);\n        }\n        return Class;\n    }, ignoreTransaction: function (scopeFunc) {\n        return PSD.trans ?\n            usePSD(PSD.transless, scopeFunc) :\n            scopeFunc();\n    }, vip: vip, async: function (generatorFn) {\n        return function () {\n            try {\n                var rv = awaitIterator(generatorFn.apply(this, arguments));\n                if (!rv || typeof rv.then !== 'function')\n                    return DexiePromise.resolve(rv);\n                return rv;\n            }\n            catch (e) {\n                return rejection(e);\n            }\n        };\n    }, spawn: function (generatorFn, args, thiz) {\n        try {\n            var rv = awaitIterator(generatorFn.apply(thiz, args || []));\n            if (!rv || typeof rv.then !== 'function')\n                return DexiePromise.resolve(rv);\n            return rv;\n        }\n        catch (e) {\n            return rejection(e);\n        }\n    },\n    currentTransaction: {\n        get: function () { return PSD.trans || null; }\n    }, waitFor: function (promiseOrFunction, optionalTimeout) {\n        var promise = DexiePromise.resolve(typeof promiseOrFunction === 'function' ?\n            Dexie.ignoreTransaction(promiseOrFunction) :\n            promiseOrFunction)\n            .timeout(optionalTimeout || 60000);\n        return PSD.trans ?\n            PSD.trans.waitFor(promise) :\n            promise;\n    },\n    Promise: DexiePromise,\n    debug: {\n        get: function () { return debug; },\n        set: function (value) {\n            setDebug(value, value === 'dexie' ? function () { return true; } : dexieStackFrameFilter);\n        }\n    },\n    derive: derive, extend: extend, props: props, override: override,\n    Events: Events, on: globalEvents, liveQuery: liveQuery, extendObservabilitySet: extendObservabilitySet,\n    getByKeyPath: getByKeyPath, setByKeyPath: setByKeyPath, delByKeyPath: delByKeyPath, shallowClone: shallowClone, deepClone: deepClone, getObjectDiff: getObjectDiff, cmp: cmp, asap: asap$1,\n    minKey: minKey,\n    addons: [],\n    connections: connections,\n    errnames: errnames,\n    dependencies: domDeps,\n    semVer: DEXIE_VERSION, version: DEXIE_VERSION.split('.')\n        .map(function (n) { return parseInt(n); })\n        .reduce(function (p, c, i) { return p + (c / Math.pow(10, i * 2)); }) }));\nDexie.maxKey = getMaxKey(Dexie.dependencies.IDBKeyRange);\n\nif (typeof dispatchEvent !== 'undefined' && typeof addEventListener !== 'undefined') {\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, function (updatedParts) {\n        if (!propagatingLocally) {\n            var event_1;\n            if (isIEOrEdge) {\n                event_1 = document.createEvent('CustomEvent');\n                event_1.initCustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, true, true, updatedParts);\n            }\n            else {\n                event_1 = new CustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, {\n                    detail: updatedParts\n                });\n            }\n            propagatingLocally = true;\n            dispatchEvent(event_1);\n            propagatingLocally = false;\n        }\n    });\n    addEventListener(STORAGE_MUTATED_DOM_EVENT_NAME, function (_a) {\n        var detail = _a.detail;\n        if (!propagatingLocally) {\n            propagateLocally(detail);\n        }\n    });\n}\nfunction propagateLocally(updateParts) {\n    var wasMe = propagatingLocally;\n    try {\n        propagatingLocally = true;\n        globalEvents.storagemutated.fire(updateParts);\n    }\n    finally {\n        propagatingLocally = wasMe;\n    }\n}\nvar propagatingLocally = false;\n\nif (typeof BroadcastChannel !== 'undefined') {\n    var bc_1 = new BroadcastChannel(STORAGE_MUTATED_DOM_EVENT_NAME);\n    if (typeof bc_1.unref === 'function') {\n        bc_1.unref();\n    }\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, function (changedParts) {\n        if (!propagatingLocally) {\n            bc_1.postMessage(changedParts);\n        }\n    });\n    bc_1.onmessage = function (ev) {\n        if (ev.data)\n            propagateLocally(ev.data);\n    };\n}\nelse if (typeof self !== 'undefined' && typeof navigator !== 'undefined') {\n    globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, function (changedParts) {\n        try {\n            if (!propagatingLocally) {\n                if (typeof localStorage !== 'undefined') {\n                    localStorage.setItem(STORAGE_MUTATED_DOM_EVENT_NAME, JSON.stringify({\n                        trig: Math.random(),\n                        changedParts: changedParts,\n                    }));\n                }\n                if (typeof self['clients'] === 'object') {\n                    __spreadArray([], self['clients'].matchAll({ includeUncontrolled: true }), true).forEach(function (client) {\n                        return client.postMessage({\n                            type: STORAGE_MUTATED_DOM_EVENT_NAME,\n                            changedParts: changedParts,\n                        });\n                    });\n                }\n            }\n        }\n        catch (_a) { }\n    });\n    if (typeof addEventListener !== 'undefined') {\n        addEventListener('storage', function (ev) {\n            if (ev.key === STORAGE_MUTATED_DOM_EVENT_NAME) {\n                var data = JSON.parse(ev.newValue);\n                if (data)\n                    propagateLocally(data.changedParts);\n            }\n        });\n    }\n    var swContainer = self.document && navigator.serviceWorker;\n    if (swContainer) {\n        swContainer.addEventListener('message', propagateMessageLocally);\n    }\n}\nfunction propagateMessageLocally(_a) {\n    var data = _a.data;\n    if (data && data.type === STORAGE_MUTATED_DOM_EVENT_NAME) {\n        propagateLocally(data.changedParts);\n    }\n}\n\nDexiePromise.rejectionMapper = mapError;\nsetDebug(debug, dexieStackFrameFilter);\n\nexport { Dexie$1 as Dexie, RangeSet, Dexie$1 as default, liveQuery, mergeRanges, rangesOverlap };\n//# sourceMappingURL=dexie.mjs.map\n", "import Dexie from 'dexie';\n\n// TODO: 边界条件 比如在某些设备 不想使用这个功能\n// 1. indexDB 不可使用的问题 已处理 try catch 掉\n// 2. indexDB 到达存储上限的问题\n// 3. indexDB 需要清理的问题\n\nconst tableConfig = {\n  bimInfo: {\n    version: 1,\n    stores: { bimInfo: 'bimId,dir,&index' } // 建立索引快速查询会使用  & 表示唯一索引 * 表示多值索引\n  },\n  modelRecord: {\n    version: 1,\n    stores: { modelRecord: 'modelId, lastUseTime' }\n  },\n  modelIdToBlob: {\n    version: 1,\n    stores: { modelIdToBlob: '&modelId, accessCount, lastUseTime' }\n  },\n  modelIdToJSAry: {\n    version: 1,\n    stores: { modelIdToJSAry: '&modelId, accessCount, lastUseTime' }\n  }\n}\n\nfunction isSupportIndexDB() {\n  return typeof self.indexedDB !== 'undefined'\n}\n// const isSptIdxDB = isSupportIndexDB()\n// console.log(' - isSptIdxDB - ', isSptIdxDB);\n\n// dbname should be unique and use model's associated_id\nfunction createDB(dbname, version = 1, stores = {\n  bimInfo: '&bimId, dir, index'\n}) {\n  // 初始化 Dexie 数据库\n  try {\n    const db = new Dexie(dbname);\n    db.version(version).stores(stores);\n    return db;\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction getDataUsingFetch(url) {\n  return fetch(url).then((res) => {\n    // return res.arrayBuffer();\n    return res.json();\n  }).then((data) => {\n    return data;\n  })\n}\n\nasync function queryDB(dbname, dbStruct, key, value) {\n  const db = createDB(dbname, tableConfig[dbStruct].version, tableConfig[dbStruct].stores)\n  const table = db[dbStruct]\n  try {\n    const result = await table.where(key).equals(value).toArray()\n    if(result.length > 0) {\n      const accessCount = result[0].accessCount\n      table.update(result[0].modelId, { lastUseTime: Date.now(), accessCount: accessCount + 1 })\n    }\n    return result\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nasync function clearOldDataInDB(dbname, dbStruct, maxCount) {\n  const db = createDB(dbname, tableConfig[dbStruct].version, tableConfig[dbStruct].stores)\n  const table = db[dbStruct]\n  try {\n    const result = await table.orderBy('lastUseTime').limit(maxCount).toArray()\n    // console.log(' result ', result);\n    // if lastUseTime is 3 days ago, delete it\n    const now = Date.now()\n    const threeDays = 3 * 24 * 60 * 60 * 1000\n    const deleteIds = []\n    result.forEach(item => {\n      if (now - item.lastUseTime > threeDays) {\n        deleteIds.push(item.modelId)\n      }\n    })\n    await table.bulkDelete(deleteIds)\n    return result\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nasync function deleteIds(dbname, dbStruct, modelIdAry) {\n  const db = createDB(dbname, tableConfig[dbStruct].version, tableConfig[dbStruct].stores)\n  const table = db[dbStruct]\n  try {\n    await table.bulkDelete(modelIdAry)\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nconst saveToDB = async (data, dbname, dbStruct) => {\n  const db = createDB(dbname, tableConfig[dbStruct].version, tableConfig[dbStruct].stores)\n  // console.log(db, data, tableConfig.bimIdToBlob);\n  /** @type {Dexie.Table} */\n  const table = db[dbStruct]\n  try {\n    await table.add(data)\n  } catch (error) {\n    console.error(' error ', error);\n  }\n}\n\nasync function init(modelId, url, taskId, updateDate) {\n  const dbname = 'modelCompData'\n  self.postMessage({\n    modelId,\n    type: 'progress',\n    progress: 10\n  });\n  const dbStruct = 'modelIdToBlob'\n  try {\n    const isExist = await queryDB(dbname, dbStruct, 'modelId', modelId)\n    if (isExist && isExist.length > 0) {\n      const data = isExist[0]\n      const oldUpdateDate = data.updateDate\n      if(oldUpdateDate === updateDate) {\n        const blob = isExist[0].blob\n        self.postMessage({\n          modelId,\n          type: 'progress',\n          progress: 30\n        });\n        // console.time('parse');\n        const blobToObj = await blob.text().then((text) => {\n          const obj = JSON.parse(text);\n          // console.log(obj);\n          return obj;\n        });\n        self.postMessage({\n          modelId,\n          type: 'progress',\n          progress: 90\n        });\n        // const jsonObj = JSON.parse(data)\n        // const formateData = JSON.parse(data)\n        // console.timeEnd('parse');\n        self.postMessage({\n          modelId,\n          taskId,\n          data: blobToObj\n        });\n        clearOldDataInDB(dbname, dbStruct, 100)\n        return\n      }else{\n        // updateDate is different, need to update\n        // delete old one\n        const deleteIdAry = []\n        deleteIdAry.push(isExist[0].modelId)\n        await deleteIds(dbname, dbStruct, deleteIdAry)\n      }\n    }\n  } catch (error) {\n    console.error(error);\n  }\n  getDataUsingFetch(url).then(async data => {\n    // console.time('format');\n    // console.log(' data --- ', data);\n    let formatData = data && data.map(item => {\n      const itemData = {\n        ...item,\n        bimId: item.bid,\n        box: item.box,\n        dir: item.dir,\n        index: item.idx,\n      }\n      return itemData\n    })\n    if(!data){\n      formatData = []\n    }\n    self.postMessage({\n      modelId,\n      taskId,\n      data: formatData\n    });\n    // console.timeEnd('format');\n    // formatData to blob\n    // console.time('formatToBlob');\n    const blob = new Blob([JSON.stringify(formatData)], { type: 'application/json' })\n    // console.timeEnd('formatToBlob');\n\n    self.postMessage({\n      modelId,\n      type: 'progress',\n    });\n\n    clearOldDataInDB(dbname, dbStruct, 100)\n    // save to db\n    try {\n      await saveToDB({\n        modelId,\n        blob,\n        type: 'application/json',\n        createDate: Date.now(),\n        lastUseTime: Date.now(),\n        accessCount: 1,\n        updateDate,\n      }, dbname, dbStruct)\n    } catch (error) {\n      console.error(error);\n    }\n  }).catch(error => {\n    // console.log(' error ', error);\n    console.error(error);\n\n    self.postMessage({\n      modelId,\n      taskId,\n      data: []\n    });\n  });\n}\n\n// 监听主线程的消息\nself.addEventListener('message', async (event) => {\n  // 你可以在这里处理来自主线程的数据并使用 Dexie\n\n  const { type } = event.data;\n  if (type === 'init') {\n    const { modelId, url, taskId, updateDate } = event.data\n    init(modelId, url, taskId, updateDate)\n  }\n});\n"], "sourceRoot": ""}