define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./Transforms-3ef1852a","./Matrix4-a50b021f","./PrimitiveType-ec02f806"],(function(e,t,r,n,a,i,u,o){"use strict";var s={NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3},c=Object.freeze(s);function f(e,r,n,a){this[0]=t.defaultValue(e,0),this[1]=t.defaultValue(n,0),this[2]=t.defaultValue(r,0),this[3]=t.defaultValue(a,0)}f.packedLength=4,f.pack=function(e,r,n){return n=t.defaultValue(n,0),r[n++]=e[0],r[n++]=e[1],r[n++]=e[2],r[n++]=e[3],r},f.unpack=function(e,r,n){return r=t.defaultValue(r,0),t.defined(n)||(n=new f),n[0]=e[r++],n[1]=e[r++],n[2]=e[r++],n[3]=e[r++],n},f.clone=function(e,r){if(t.defined(e))return t.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r):new f(e[0],e[2],e[1],e[3])},f.fromArray=function(e,r,n){return r=t.defaultValue(r,0),t.defined(n)||(n=new f),n[0]=e[r],n[1]=e[r+1],n[2]=e[r+2],n[3]=e[r+3],n},f.fromColumnMajorArray=function(e,t){return f.clone(e,t)},f.fromRowMajorArray=function(e,r){return t.defined(r)?(r[0]=e[0],r[1]=e[2],r[2]=e[1],r[3]=e[3],r):new f(e[0],e[1],e[2],e[3])},f.fromScale=function(e,r){return t.defined(r)?(r[0]=e.x,r[1]=0,r[2]=0,r[3]=e.y,r):new f(e.x,0,0,e.y)},f.fromUniformScale=function(e,r){return t.defined(r)?(r[0]=e,r[1]=0,r[2]=0,r[3]=e,r):new f(e,0,0,e)},f.fromRotation=function(e,r){var n=Math.cos(e),a=Math.sin(e);return t.defined(r)?(r[0]=n,r[1]=a,r[2]=-a,r[3]=n,r):new f(n,-a,a,n)},f.toArray=function(e,r){return t.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r):[e[0],e[1],e[2],e[3]]},f.getElementIndex=function(e,t){return r.Check.typeOf.number.greaterThanOrEquals("row",t,0),r.Check.typeOf.number.lessThanOrEquals("row",t,1),r.Check.typeOf.number.greaterThanOrEquals("column",e,0),r.Check.typeOf.number.lessThanOrEquals("column",e,1),2*e+t},f.getColumn=function(e,t,n){r.Check.typeOf.object("matrix",e),r.Check.typeOf.number.greaterThanOrEquals("index",t,0),r.Check.typeOf.number.lessThanOrEquals("index",t,1),r.Check.typeOf.object("result",n);var a=2*t,i=e[a],u=e[a+1];return n.x=i,n.y=u,n},f.setColumn=function(e,t,n,a){r.Check.typeOf.object("matrix",e),r.Check.typeOf.number.greaterThanOrEquals("index",t,0),r.Check.typeOf.number.lessThanOrEquals("index",t,1),r.Check.typeOf.object("cartesian",n),r.Check.typeOf.object("result",a),a=f.clone(e,a);var i=2*t;return a[i]=n.x,a[i+1]=n.y,a},f.getRow=function(e,t,n){r.Check.typeOf.object("matrix",e),r.Check.typeOf.number.greaterThanOrEquals("index",t,0),r.Check.typeOf.number.lessThanOrEquals("index",t,1),r.Check.typeOf.object("result",n);var a=e[t],i=e[t+2];return n.x=a,n.y=i,n},f.setRow=function(e,t,n,a){return r.Check.typeOf.object("matrix",e),r.Check.typeOf.number.greaterThanOrEquals("index",t,0),r.Check.typeOf.number.lessThanOrEquals("index",t,1),r.Check.typeOf.object("cartesian",n),r.Check.typeOf.object("result",a),a=f.clone(e,a),a[t]=n.x,a[t+2]=n.y,a};var l=new n.Cartesian2;f.getScale=function(e,t){return t.x=n.Cartesian2.magnitude(n.Cartesian2.fromElements(e[0],e[1],l)),t.y=n.Cartesian2.magnitude(n.Cartesian2.fromElements(e[2],e[3],l)),t};var p=new n.Cartesian2;function h(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),r.Check.typeOf.object("options.attributes",e.attributes),this.attributes=e.attributes,this.indices=e.indices,this.primitiveType=t.defaultValue(e.primitiveType,o.PrimitiveType.TRIANGLES),this.boundingSphere=e.boundingSphere,this.geometryType=t.defaultValue(e.geometryType,c.NONE),this.boundingSphereCV=e.boundingSphereCV,this.offsetAttribute=e.offsetAttribute}f.getMaximumScale=function(e){return f.getScale(e,p),n.Cartesian2.maximumComponent(p)},f.multiply=function(e,t,r){var n=e[0]*t[0]+e[2]*t[1],a=e[0]*t[2]+e[2]*t[3],i=e[1]*t[0]+e[3]*t[1],u=e[1]*t[2]+e[3]*t[3];return r[0]=n,r[1]=i,r[2]=a,r[3]=u,r},f.add=function(e,t,r){return r[0]=e[0]+t[0],r[1]=e[1]+t[1],r[2]=e[2]+t[2],r[3]=e[3]+t[3],r},f.subtract=function(e,t,r){return r[0]=e[0]-t[0],r[1]=e[1]-t[1],r[2]=e[2]-t[2],r[3]=e[3]-t[3],r},f.multiplyByVector=function(e,t,r){var n=e[0]*t.x+e[2]*t.y,a=e[1]*t.x+e[3]*t.y;return r.x=n,r.y=a,r},f.multiplyByScalar=function(e,t,r){return r[0]=e[0]*t,r[1]=e[1]*t,r[2]=e[2]*t,r[3]=e[3]*t,r},f.multiplyByScale=function(e,t,r){return r[0]=e[0]*t.x,r[1]=e[1]*t.x,r[2]=e[2]*t.y,r[3]=e[3]*t.y,r},f.negate=function(e,t){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},f.transpose=function(e,t){var r=e[0],n=e[2],a=e[1],i=e[3];return t[0]=r,t[1]=n,t[2]=a,t[3]=i,t},f.abs=function(e,t){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t},f.equals=function(e,r){return e===r||t.defined(e)&&t.defined(r)&&e[0]===r[0]&&e[1]===r[1]&&e[2]===r[2]&&e[3]===r[3]},f.equalsArray=function(e,t,r){return e[0]===t[r]&&e[1]===t[r+1]&&e[2]===t[r+2]&&e[3]===t[r+3]},f.equalsEpsilon=function(e,r,n){return n=t.defaultValue(n,0),e===r||t.defined(e)&&t.defined(r)&&Math.abs(e[0]-r[0])<=n&&Math.abs(e[1]-r[1])<=n&&Math.abs(e[2]-r[2])<=n&&Math.abs(e[3]-r[3])<=n},f.IDENTITY=Object.freeze(new f(1,0,0,1)),f.ZERO=Object.freeze(new f(0,0,0,0)),f.COLUMN0ROW0=0,f.COLUMN0ROW1=1,f.COLUMN1ROW0=2,f.COLUMN1ROW1=3,Object.defineProperties(f.prototype,{length:{get:function(){return f.packedLength}}}),f.prototype.clone=function(e){return f.clone(this,e)},f.prototype.equals=function(e){return f.equals(this,e)},f.prototype.equalsEpsilon=function(e,t){return f.equalsEpsilon(this,e,t)},f.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"},h.computeNumberOfVertices=function(e){r.Check.typeOf.object("geometry",e);var n=-1;for(var a in e.attributes)if(e.attributes.hasOwnProperty(a)&&t.defined(e.attributes[a])&&t.defined(e.attributes[a].values)){var i=e.attributes[a],u=i.values.length/i.componentsPerAttribute;if(n!==u&&-1!==n)throw new r.DeveloperError("All attribute lists must have the same number of attributes.");n=u}return n};var m=new a.Cartographic,y=new n.Cartesian3,d=new u.Matrix4,b=[new a.Cartographic,new a.Cartographic,new a.Cartographic],C=[new n.Cartesian2,new n.Cartesian2,new n.Cartesian2],O=[new n.Cartesian2,new n.Cartesian2,new n.Cartesian2],x=new n.Cartesian3,w=new i.Quaternion,g=new u.Matrix4,E=new f;function T(e){if(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),!t.defined(e.componentDatatype))throw new r.DeveloperError("options.componentDatatype is required.");if(!t.defined(e.componentsPerAttribute))throw new r.DeveloperError("options.componentsPerAttribute is required.");if(e.componentsPerAttribute<1||e.componentsPerAttribute>4)throw new r.DeveloperError("options.componentsPerAttribute must be between 1 and 4.");if(!t.defined(e.values))throw new r.DeveloperError("options.values is required.");this.componentDatatype=e.componentDatatype,this.componentsPerAttribute=e.componentsPerAttribute,this.normalize=t.defaultValue(e.normalize,!1),this.values=e.values}h._textureCoordinateRotationPoints=function(e,t,r,o){var s,c=a.Rectangle.center(o,m),l=a.Cartographic.toCartesian(c,r,y),p=i.Transforms.eastNorthUpToFixedFrame(l,r,d),h=u.Matrix4.inverse(p,d),T=C,v=b;v[0].longitude=o.west,v[0].latitude=o.south,v[1].longitude=o.west,v[1].latitude=o.north,v[2].longitude=o.east,v[2].latitude=o.south;var k=x;for(s=0;s<3;s++)a.Cartographic.toCartesian(v[s],r,k),k=u.Matrix4.multiplyByPointAsVector(h,k,k),T[s].x=k.x,T[s].y=k.y;var M=i.Quaternion.fromAxisAngle(n.Cartesian3.UNIT_Z,-t,w),N=u.Matrix3.fromQuaternion(M,g),V=e.length,A=Number.POSITIVE_INFINITY,I=Number.POSITIVE_INFINITY,q=Number.NEGATIVE_INFINITY,P=Number.NEGATIVE_INFINITY;for(s=0;s<V;s++)k=u.Matrix4.multiplyByPointAsVector(h,e[s],k),k=u.Matrix3.multiplyByVector(N,k,k),A=Math.min(A,k.x),I=Math.min(I,k.y),q=Math.max(q,k.x),P=Math.max(P,k.y);var j=f.fromRotation(t,E),S=O;S[0].x=A,S[0].y=I,S[1].x=A,S[1].y=P,S[2].x=q,S[2].y=I;var R=T[0],L=T[2].x-R.x,D=T[1].y-R.y;for(s=0;s<3;s++){var B=S[s];f.multiplyByVector(j,B,B),B.x=(B.x-R.x)/L,B.y=(B.y-R.y)/D}var Y=S[0],_=S[1],G=S[2],U=new Array(6);return n.Cartesian2.pack(Y,U),n.Cartesian2.pack(_,U,2),n.Cartesian2.pack(G,U,4),U},e.Geometry=h,e.GeometryAttribute=T,e.GeometryType=c,e.Matrix2=f}));