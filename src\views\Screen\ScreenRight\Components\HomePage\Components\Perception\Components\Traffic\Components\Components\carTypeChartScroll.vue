<!-- 滚动整个柱条的动画效果 -->
<template>
  <div class="carTypeChart">
    <div class="title">
      <span>{{ itemData.name }}</span>
      <span>{{ itemData.value }}%</span>
    </div>
    <div class="content">
      <!-- 为图表容器添加动画样式 -->
      <div class="chart-container" :style="{ transform: `rotateX(${animationRotateY}deg)` }">
        <Mychart :option="options" width="300px" height="79px" style="margin-top: 20px"></Mychart>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed, onUnmounted, watch } from 'vue'
  import Mychart from '@Common/components/MyChart/index.vue'
  
  const props = defineProps({
    itemData: {
      type: Object,
      default: () => ({})
    },
    carTotalNumber: {
      type: Number,
      default: 0
    }
  })

  // 动画相关的响应式数据
  const animationRotateY = ref(0) // X轴旋转角度（绕水平中线旋转）
  const isAnimating = ref(false)

  // 监听数据变化，触发拨片切换效果
  watch(() => [props.itemData.value, props.itemData.data], (newVal, oldVal) => {
    // 只有在数据真正发生变化时才触发动画
    if (newVal && oldVal && (newVal[0] !== oldVal[0] || newVal[1] !== oldVal[1])) {
      triggerFlipAnimation()
    }
  }, { deep: true })

  // 触发拨片切换动画 - 最小角度变化，消除闪烁
  const triggerFlipAnimation = () => {
    if (isAnimating.value) return
    
    isAnimating.value = true
    
    // 阶段1：微小前倾（3度，保持约99.9%可见高度）
    animationRotateY.value = 3
    
    setTimeout(() => {
      // 阶段2：微小后倾（177度，保持约99.9%可见高度）
      animationRotateY.value = 177
      
      setTimeout(() => {
        // 阶段3：微小深度后倾（183度，保持约99.9%可见高度）
        animationRotateY.value = 183
        
        setTimeout(() => {
          // 阶段4：回到微小前倾（357度，保持约99.9%可见高度）
          animationRotateY.value = 357
          
          setTimeout(() => {
            // 阶段5：回到正面，显示新数据
            animationRotateY.value = 360
            
            setTimeout(() => {
              // 重置角度
              animationRotateY.value = 0
              isAnimating.value = false
            }, 50) // 减少重置延迟
          }, 200) // 缩短时间，减少视觉停顿
        }, 200) // 缩短时间
      }, 300) // 在177度停留300ms
    }, 200) // 缩短时间
  }

  const options = computed(() => {
    // 计算 symbolOffset 的逻辑，根据 value 动态计算偏移量
    const m = (-126 - -25) / (51.75 - 13.41)
    const b = -25 - m * 13.41
    const value = parseFloat(props.itemData.value)
    const baseOffsetX = Math.round(m * value + b)
    
    return {
      // 添加全局动画配置（保持ECharts内部动画）
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
      animationDurationUpdate: 800,
      animationEasingUpdate: 'cubicOut',
      
      grid: {
        top: '-52%',
        left: '8',
        right: '10%',
        bottom: 0
      },
      xAxis: [
        {
          type: 'value',
          show: false,
          gridIndex: 0,
          max: 100
        }
      ],
      yAxis: {
        type: 'category',
        show: false,
        axisLine: { show: false },
        axisTick: { show: false },
        splitArea: { show: false },
        splitLine: { show: false },
        axisLabel: {
          fontFamily: 'Alibaba PuHuiTi',
          fontSize: 20,
          color: '#ffffff'
        },
        inverse: true
      },
      series: [
        {
          zlevel: 1,
          type: 'pictorialBar',
          symbolPosition: 'end',
          barGap: '-100%',
          data: [props.itemData.value == '0.0' ? null : props.itemData.value],
          symbol: 'diamond',
          symbolRotate: 90,
          symbolOffset: [baseOffsetX, 0],
          symbolSize: [20, 10],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return props.itemData.color
              }
            }
          }
        },
        {
          data: [props.itemData.value],
          type: 'bar',
          barWidth: 20,
          zlevel: 1,
          label: {
            show: true,
            position: 'right',
            fontSize: 14,
            fontFamily: 'PangMenZhengDao',
            color: ' #AEF5FF',
            fontWeight: 'bold',
            formatter: (params) => {
              return parseInt(props.itemData.data)
            }
          },
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  x: 0,
                  x2: 0,
                  y: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: lightenColor(props.itemData.color, 0.3)
                    },
                    {
                      offset: 0.5,
                      color: lightenColor(props.itemData.color, 0.1)
                    },
                    {
                      offset: 0.5,
                      color: darkenColor(props.itemData.color, 0.1)
                    },
                    {
                      offset: 1,
                      color: darkenColor(props.itemData.color, 0.3)
                    }
                  ]
                }
              }
            }
          }
        },
        {
          zlevel: 2, // 确保 pictorialBar 在柱状图之上
          type: 'pictorialBar',
          symbolPosition: 'end',
          // 关联对应的柱状图系列索引
          barGap: '-100%',
          data: [props.itemData.value == '0.0' ? null : props.itemData.value],
          symbol: 'diamond', // 菱形
          symbolRotate: 90,
          symbolOffset: [10, 0],
          symbolSize: [20, 10],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return props.itemData.color
              }
            }
          }
        }
      ]
    }
  })

  // 组件卸载时清理（如果需要）
  onUnmounted(() => {
    isAnimating.value = false
  })

  // 辅助函数：变亮颜色
  function lightenColor(hex, percent) {
    hex = hex.replace(/^#/, '')
    let r = parseInt(hex.substr(0, 2), 16)
    let g = parseInt(hex.substr(2, 2), 16)
    let b = parseInt(hex.substr(4, 2), 16)

    r = Math.min(255, Math.round(r * (1 + percent)))
    g = Math.min(255, Math.round(g * (1 + percent)))
    b = Math.min(255, Math.round(b * (1 + percent)))

    return `#${[r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('')}`
  }

  // 辅助函数：变暗颜色
  function darkenColor(hex, percent) {
    hex = hex.replace(/^#/, '')
    let r = parseInt(hex.substr(0, 2), 16)
    let g = parseInt(hex.substr(2, 2), 16)
    let b = parseInt(hex.substr(4, 2), 16)

    r = Math.max(0, Math.round(r * (1 - percent)))
    g = Math.max(0, Math.round(g * (1 - percent)))
    b = Math.max(0, Math.round(b * (1 - percent)))

    return `#${[r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('')}`
  }
</script>
<style lang="scss" scoped>
  .carTypeChart {
    position: relative;
    width: 300px;
    height: 50px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .content {
      position: absolute;
      top: -2px;
      width: 300px;
      height: 79px;
      background: url('@/assets/ScreenRight/Perception/Traffic/chartBg.png') no-repeat center;
      background-size: cover;
      overflow: hidden; // 防止动画时超出容器
      perspective: 800px; // 增强透视效果，使圆柱旋转更立体
      
      .chart-container {
        transition: transform 0.3s ease-in-out; // 最平滑的过渡，减少闪烁
        will-change: transform; // 优化动画性能
        transform-style: preserve-3d; // 保持3D变换
        transform-origin: center 25px; // 精确设置旋转中心在柱状图的中心位置
        backface-visibility: hidden; // 隐藏背面，避免闪烁
      }
    }
  }
</style>
