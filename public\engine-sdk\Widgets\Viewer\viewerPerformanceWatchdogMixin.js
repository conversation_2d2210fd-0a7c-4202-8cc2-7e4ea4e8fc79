import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import DeveloperError from"../../Core/DeveloperError.js";import PerformanceWatchdog from"../PerformanceWatchdog/PerformanceWatchdog.js";function viewerPerformanceWatchdogMixin(e,r){if(!defined(e))throw new DeveloperError("viewer is required.");r=defaultValue(r,defaultValue.EMPTY_OBJECT);var o=new PerformanceWatchdog({scene:e.scene,container:e.bottomContainer,lowFrameRateMessage:r.lowFrameRateMessage});Object.defineProperties(e,{performanceWatchdog:{get:function(){return o}}})}export default viewerPerformanceWatchdogMixin;