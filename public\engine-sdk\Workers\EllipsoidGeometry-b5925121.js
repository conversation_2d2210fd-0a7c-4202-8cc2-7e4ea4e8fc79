define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410"],(function(e,t,a,r,i,n,o,m,s,u,l,f,c){"use strict";var d=new r.Cartesian3,p=new r.Cartesian3,C=new r.Cartesian3,y=new r.Cartesian3,h=new r.Cartesian3,v=new r.Cartesian3(1,1,1),_=Math.cos,A=Math.sin;function x(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var i=t.defaultValue(e.radii,v),n=t.defaultValue(e.innerRadii,i),o=t.defaultValue(e.minimumClock,0),m=t.defaultValue(e.maximumClock,r.CesiumMath.TWO_PI),s=t.defaultValue(e.minimumCone,0),u=t.defaultValue(e.maximumCone,r.CesiumMath.PI),l=Math.round(t.defaultValue(e.stackPartitions,64)),f=Math.round(t.defaultValue(e.slicePartitions,64)),d=t.defaultValue(e.vertexFormat,c.VertexFormat.DEFAULT);if(f<3)throw new a.DeveloperError("options.slicePartitions cannot be less than three.");if(l<3)throw new a.DeveloperError("options.stackPartitions cannot be less than three.");this._radii=r.Cartesian3.clone(i),this._innerRadii=r.Cartesian3.clone(n),this._minimumClock=o,this._maximumClock=m,this._minimumCone=s,this._maximumCone=u,this._stackPartitions=l,this._slicePartitions=f,this._vertexFormat=c.VertexFormat.clone(d),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipsoidGeometry"}x.packedLength=2*r.Cartesian3.packedLength+c.VertexFormat.packedLength+7,x.pack=function(e,i,n){if(!t.defined(e))throw new a.DeveloperError("value is required");if(!t.defined(i))throw new a.DeveloperError("array is required");return n=t.defaultValue(n,0),r.Cartesian3.pack(e._radii,i,n),n+=r.Cartesian3.packedLength,r.Cartesian3.pack(e._innerRadii,i,n),n+=r.Cartesian3.packedLength,c.VertexFormat.pack(e._vertexFormat,i,n),n+=c.VertexFormat.packedLength,i[n++]=e._minimumClock,i[n++]=e._maximumClock,i[n++]=e._minimumCone,i[n++]=e._maximumCone,i[n++]=e._stackPartitions,i[n++]=e._slicePartitions,i[n]=t.defaultValue(e._offsetAttribute,-1),i};var b,w=new r.Cartesian3,k=new r.Cartesian3,P=new c.VertexFormat,g={radii:w,innerRadii:k,vertexFormat:P,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};x.unpack=function(e,i,n){if(!t.defined(e))throw new a.DeveloperError("array is required");i=t.defaultValue(i,0);var o=r.Cartesian3.unpack(e,i,w);i+=r.Cartesian3.packedLength;var m=r.Cartesian3.unpack(e,i,k);i+=r.Cartesian3.packedLength;var s=c.VertexFormat.unpack(e,i,P);i+=c.VertexFormat.packedLength;var u=e[i++],l=e[i++],f=e[i++],d=e[i++],p=e[i++],C=e[i++],y=e[i];return t.defined(n)?(n._radii=r.Cartesian3.clone(o,n._radii),n._innerRadii=r.Cartesian3.clone(m,n._innerRadii),n._vertexFormat=c.VertexFormat.clone(s,n._vertexFormat),n._minimumClock=u,n._maximumClock=l,n._minimumCone=f,n._maximumCone=d,n._stackPartitions=p,n._slicePartitions=C,n._offsetAttribute=-1===y?void 0:y,n):(g.minimumClock=u,g.maximumClock=l,g.minimumCone=f,g.maximumCone=d,g.stackPartitions=p,g.slicePartitions=C,g.offsetAttribute=-1===y?void 0:y,new x(g))},x.createGeometry=function(e){var a=e._radii;if(!(a.x<=0||a.y<=0||a.z<=0)){var c=e._innerRadii;if(!(c.x<=0||c.y<=0||c.z<=0)){var v,x,b=e._minimumClock,w=e._maximumClock,k=e._minimumCone,P=e._maximumCone,g=e._vertexFormat,F=e._slicePartitions+1,V=e._stackPartitions+1;F=Math.round(F*Math.abs(w-b)/r.CesiumMath.TWO_PI),V=Math.round(V*Math.abs(P-k)/r.CesiumMath.PI),F<2&&(F=2),V<2&&(V=2);var D=0,T=[k],E=[b];for(v=0;v<V;v++)T.push(k+v*(P-k)/(V-1));for(T.push(P),x=0;x<F;x++)E.push(b+x*(w-b)/(F-1));E.push(w);var M=T.length,G=E.length,L=0,O=1,I=c.x!==a.x||c.y!==a.y||c.z!==a.z,z=!1,N=!1,R=!1;I&&(O=2,k>0&&(z=!0,L+=F-1),P<Math.PI&&(N=!0,L+=F-1),(w-b)%r.CesiumMath.TWO_PI?(R=!0,L+=2*(V-1)+1):L+=1);var S=G*M*O,U=new Float64Array(3*S),B=f.arrayFill(new Array(S),!1),W=f.arrayFill(new Array(S),!1),q=F*V*O,Y=6*(q+L+1-(F+V)*O),J=l.IndexDatatype.createTypedArray(q,Y),X=g.normal?new Float32Array(3*S):void 0,Z=g.tangent?new Float32Array(3*S):void 0,j=g.bitangent?new Float32Array(3*S):void 0,H=g.st?new Float32Array(2*S):void 0,K=new Array(M),Q=new Array(M);for(v=0;v<M;v++)K[v]=A(T[v]),Q[v]=_(T[v]);var $=new Array(G),ee=new Array(G);for(x=0;x<G;x++)ee[x]=_(E[x]),$[x]=A(E[x]);for(v=0;v<M;v++)for(x=0;x<G;x++)U[D++]=a.x*K[v]*ee[x],U[D++]=a.y*K[v]*$[x],U[D++]=a.z*Q[v];var te,ae,re,ie,ne=S/2;if(I)for(v=0;v<M;v++)for(x=0;x<G;x++)U[D++]=c.x*K[v]*ee[x],U[D++]=c.y*K[v]*$[x],U[D++]=c.z*Q[v],B[ne]=!0,v>0&&v!==M-1&&0!==x&&x!==G-1&&(W[ne]=!0),ne++;for(D=0,v=1;v<M-2;v++)for(te=v*G,ae=(v+1)*G,x=1;x<G-2;x++)J[D++]=ae+x,J[D++]=ae+x+1,J[D++]=te+x+1,J[D++]=ae+x,J[D++]=te+x+1,J[D++]=te+x;if(I){var oe=M*G;for(v=1;v<M-2;v++)for(te=oe+v*G,ae=oe+(v+1)*G,x=1;x<G-2;x++)J[D++]=ae+x,J[D++]=te+x,J[D++]=te+x+1,J[D++]=ae+x,J[D++]=te+x+1,J[D++]=ae+x+1}if(I){if(z)for(ie=M*G,v=1;v<G-2;v++)J[D++]=v,J[D++]=v+1,J[D++]=ie+v+1,J[D++]=v,J[D++]=ie+v+1,J[D++]=ie+v;if(N)for(re=M*G-G,ie=M*G*O-G,v=1;v<G-2;v++)J[D++]=re+v+1,J[D++]=re+v,J[D++]=ie+v,J[D++]=re+v+1,J[D++]=ie+v,J[D++]=ie+v+1}if(R){for(v=1;v<M-2;v++)ie=G*M+G*v,re=G*v,J[D++]=ie,J[D++]=re+G,J[D++]=re,J[D++]=ie,J[D++]=ie+G,J[D++]=re+G;for(v=1;v<M-2;v++)ie=G*M+G*(v+1)-1,re=G*(v+1)-1,J[D++]=re+G,J[D++]=ie,J[D++]=re,J[D++]=re+G,J[D++]=ie+G,J[D++]=ie}var me=new u.GeometryAttributes;g.position&&(me.position=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:U}));var se,ue=0,le=0,fe=0,ce=0,de=S/2,pe=i.Ellipsoid.fromCartesian3(a),Ce=i.Ellipsoid.fromCartesian3(c);if(g.st||g.normal||g.tangent||g.bitangent){for(v=0;v<S;v++){se=B[v]?Ce:pe;var ye=r.Cartesian3.fromArray(U,3*v,d),he=se.geodeticSurfaceNormal(ye,p);if(W[v]&&r.Cartesian3.negate(he,he),g.st){var ve=r.Cartesian2.negate(he,h);H[ue++]=Math.atan2(ve.y,ve.x)/r.CesiumMath.TWO_PI+.5,H[ue++]=Math.asin(he.z)/Math.PI+.5}if(g.normal&&(X[le++]=he.x,X[le++]=he.y,X[le++]=he.z),g.tangent||g.bitangent){var _e,Ae=C,xe=0;if(B[v]&&(xe=de),_e=!z&&v>=xe&&v<xe+2*G?r.Cartesian3.UNIT_X:r.Cartesian3.UNIT_Z,r.Cartesian3.cross(_e,he,Ae),r.Cartesian3.normalize(Ae,Ae),g.tangent&&(Z[fe++]=Ae.x,Z[fe++]=Ae.y,Z[fe++]=Ae.z),g.bitangent){var be=r.Cartesian3.cross(he,Ae,y);r.Cartesian3.normalize(be,be),j[ce++]=be.x,j[ce++]=be.y,j[ce++]=be.z}}}g.st&&(me.st=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:H})),g.normal&&(me.normal=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:X})),g.tangent&&(me.tangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Z})),g.bitangent&&(me.bitangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j}))}if(t.defined(e._offsetAttribute)){var we=U.length,ke=new Uint8Array(we/3),Pe=e._offsetAttribute===f.GeometryOffsetAttribute.NONE?0:1;f.arrayFill(ke,Pe),me.applyOffset=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:ke})}return new m.Geometry({attributes:me,indices:J,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:n.BoundingSphere.fromEllipsoid(pe),offsetAttribute:e._offsetAttribute})}}},x.getUnitEllipsoid=function(){return t.defined(b)||(b=x.createGeometry(new x({radii:new r.Cartesian3(1,1,1),vertexFormat:c.VertexFormat.POSITION_ONLY}))),b},e.EllipsoidGeometry=x}));