/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Cartesian3-bb0e6278","./defined-3b3eb2ba","./EllipseOutlineGeometry-6beac65c","./Rectangle-9bffefe4","./Math-b5f4d889","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./EllipseGeometryLibrary-07d021fe","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b"],(function(e,t,n,r,i,o,a,l,b,d,s,c,f,u,m,p){"use strict";return function(i,o){return t.defined(o)&&(i=n.EllipseOutlineGeometry.unpack(i,o)),i._center=e.Cartesian3.clone(i._center),i._ellipsoid=r.Ellipsoid.clone(i._ellipsoid),n.EllipseOutlineGeometry.createGeometry(i)}}));
