import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import knockout from"../../ThirdParty/knockout.js";import getElement from"../getElement.js";import PerformanceWatchdogViewModel from"./PerformanceWatchdogViewModel.js";function PerformanceWatchdog(e){if(!defined(e)||!defined(e.container))throw new DeveloperError("options.container is required.");if(!defined(e.scene))throw new DeveloperError("options.scene is required.");var t=getElement(e.container),r=new PerformanceWatchdogViewModel(e),o=document.createElement("div");o.className="cesium-performance-watchdog-message-area",o.setAttribute("data-bind","visible: showingLowFrameRateMessage");var n=document.createElement("button");n.setAttribute("type","button"),n.className="cesium-performance-watchdog-message-dismiss",n.innerHTML="&times;",n.setAttribute("data-bind","click: dismissMessage"),o.appendChild(n);var i=document.createElement("div");i.className="cesium-performance-watchdog-message",i.setAttribute("data-bind","html: lowFrameRateMessage"),o.appendChild(i),t.appendChild(o),knockout.applyBindings(r,o),this._container=t,this._viewModel=r,this._element=o}Object.defineProperties(PerformanceWatchdog.prototype,{container:{get:function(){return this._container}},viewModel:{get:function(){return this._viewModel}}}),PerformanceWatchdog.prototype.isDestroyed=function(){return!1},PerformanceWatchdog.prototype.destroy=function(){return this._viewModel.destroy(),knockout.cleanNode(this._element),this._container.removeChild(this._element),destroyObject(this)};export default PerformanceWatchdog;