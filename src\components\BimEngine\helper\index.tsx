import BuildingInfo from '@/views/Screen/MapPopupWindow/Components/BuildingInfo/index.vue'
import RadarVideo from '@/views/Screen/MapPopupWindow/Components/RadarVideo/index.vue'

import lib from '@/utils/lib'
import { closePopWindowByTag, usePopWindow } from 'znyg-frontend-common'

export const useBimEngine = () => {
  const { showPopWindow } = usePopWindow()
  const addRadarPoint = async () => {
    const radarIcon = await lib.utils.convertImageToBase64('images/radarIcon.png')

    const radarList = [
      [120.51858462160423, 30.371092287750482, 9.520678731128836, 'http://172.22.51.1:10000/hls/test17-2.m3u8'],
      [120.51865886478416, 30.37109972005454, 9.518993520935645, 'http://172.22.51.1:10000/hls/test16-2.m3u8'],
      [120.5161833546921, 30.41112158933913, 8.847670517191611, 'http://172.22.51.1:10000/hls/test15-2.m3u8'],
      [120.51609528621309, 30.411103060195153, 8.81284922089891, 'http://172.22.51.1:10000/hls/test14-2.m3u8']
    ]
    radarList.map((_, _index) => {
      const czml = {
        id: lib.utils.getRandomString(10),
        name: lib.utils.getRandomString(5),
        billboard: {
          // 图片
          image: radarIcon,
          scale: 1,
          disableDepthTestDistance: 999999,
          horizontalOrigin: 'CENTER',
          verticalOrigin: 'BOTTOM'
        },
        position: {
          cartesian: [0, 0, 0]
        },
        onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
          console.log('点击了雷达' + _index, position, tagInstance)
          closePopWindowByTag('RadarVideo')

          showPopWindow(
            {
              left: clickClinetX + 10,
              top: clickClinetY + 10,
              tag: 'RadarVideo',
              appendParent: 'player',
              draggable: true,
              zIndex: 99,
              width: '731px',
              height: '503px',
              closeFunc: () => {}
            },
            RadarVideo,
            {
              data: { name: '江北1', url: _[3] },
              url: _[3],
              handleClose: () => {
                closePopWindowByTag('RadarVideo')
              }
            }
          )
        }
      }
      lib._engineController.addCzml(_[0], _[1], _[2], czml, 'radarPoint', false, 0)
    })
  }

  const addBuildingPoint = async () => {
    const buildingIcon = await lib.utils.convertImageToBase64('images/buildingIcon.png')

    const czml = {
      id: lib.utils.getRandomString(10),
      name: lib.utils.getRandomString(5),
      billboard: {
        // 图片
        image: buildingIcon,
        scale: 3,
        disableDepthTestDistance: 999999,
        horizontalOrigin: 'CENTER',
        verticalOrigin: 'BOTTOM'
      },
      position: {
        cartesian: [0, 0, 0]
      },
      onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
        closePopWindowByTag('BuildingInfo')

        showPopWindow(
          {
            left: clickClinetX + 10,
            top: clickClinetY + 10,
            tag: 'BuildingInfo',
            appendParent: 'player',
            draggable: true,
            zIndex: 99,
            width: '381px',
            height: '353px',
            closeFunc: () => {}
          },
          BuildingInfo,
          {
            handleClose: () => {
              closePopWindowByTag('BuildingInfo')
            }
          }
        )
      }
    }
    lib._engineController.addCzml(120.51746676358644, 30.374742225402777, 24.46671076184446, czml, 'buildingPoint', false, 0)
  }
  return {
    addRadarPoint,
    addBuildingPoint
  }
}
