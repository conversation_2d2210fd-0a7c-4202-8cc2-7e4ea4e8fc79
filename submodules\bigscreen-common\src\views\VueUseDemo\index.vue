<template>
  <div>
    <onClickOutside></onClickOutside>
    <el-divider></el-divider>
    <el-button @click="showInterval = !showInterval">打开/关闭定时器</el-button>
    <useInterval v-if="showInterval"></useInterval>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  import onClickOutside from './Components/onClickOutside.vue'
  import useInterval from './Components/useInterval.vue'

  import { useTitle } from '@vueuse/core'
  const title = useTitle()
  title.value = '公共库-vueUse Demo'

  const showInterval = ref(false)
</script>

<style lang="scss" scoped></style>
