/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./AttributeCompression-d661357e","./Cartesian3-bb0e6278","./defined-3b3eb2ba","./Math-b5f4d889","./Transforms-42ed7720","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./EncodedCartesian3-6d30a00c","./Geometry-a94d02e6","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,n,i,r,a,s,o,u,c,l,p,d){"use strict";const f=new n.Cartesian3,y=new n.Cartesian3,m=new n.Cartesian3;const C={calculateACMR:function(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).indices;let n=e.maximumIndex;const r=i.defaultValue(e.cacheSize,24),a=t.length;if(!i.defined(n)){n=0;let e=0,i=t[e];for(;e<a;)i>n&&(n=i),++e,i=t[e]}const s=[];for(let e=0;e<n+1;e++)s[e]=0;let o=r+1;for(let e=0;e<a;++e)o-s[t[e]]>r&&(s[t[e]]=o,++o);return(o-r+1)/(a/3)}};C.tipsify=function(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).indices,n=e.maximumIndex,r=i.defaultValue(e.cacheSize,24);let a;function s(e,t,n,i,r,s,o){let u,c=-1,l=-1,p=0;for(;p<n.length;){const e=n[p];i[e].numLiveTriangles&&(u=0,r-i[e].timeStamp+2*i[e].numLiveTriangles<=t&&(u=r-i[e].timeStamp),(u>l||-1===l)&&(l=u,c=e)),++p}return-1===c?function(e,t,n,i){for(;t.length>=1;){const n=t[t.length-1];if(t.splice(t.length-1,1),e[n].numLiveTriangles>0)return n}for(;a<i;){if(e[a].numLiveTriangles>0)return++a,a-1;++a}return-1}(i,s,0,o):c}const o=t.length;let u=0,c=0,l=t[c];const p=o;if(i.defined(n))u=n+1;else{for(;c<p;)l>u&&(u=l),++c,l=t[c];if(-1===u)return 0;++u}const d=[];let f;for(f=0;f<u;f++)d[f]={numLiveTriangles:0,timeStamp:0,vertexTriangles:[]};c=0;let y=0;for(;c<p;)d[t[c]].vertexTriangles.push(y),++d[t[c]].numLiveTriangles,d[t[c+1]].vertexTriangles.push(y),++d[t[c+1]].numLiveTriangles,d[t[c+2]].vertexTriangles.push(y),++d[t[c+2]].numLiveTriangles,++y,c+=3;let m=0,C=r+1;a=1;let h=[];const v=[];let b,g,A=0;const T=[],x=o/3,P=[];for(f=0;f<x;f++)P[f]=!1;let w,S;for(;-1!==m;){h=[],g=d[m],S=g.vertexTriangles.length;for(let e=0;e<S;++e)if(y=g.vertexTriangles[e],!P[y]){P[y]=!0,c=y+y+y;for(let e=0;e<3;++e)w=t[c],h.push(w),v.push(w),T[A]=w,++A,b=d[w],--b.numLiveTriangles,C-b.timeStamp>r&&(b.timeStamp=C,++C),++c}m=s(0,r,h,d,C,v,u)}return T};var h=C;const v={};function b(e,t,n,i,r){e[t++]=n,e[t++]=i,e[t++]=i,e[t++]=r,e[t++]=r,e[t]=n}function g(e){const t={};for(const n in e)if(e.hasOwnProperty(n)&&i.defined(e[n])&&i.defined(e[n].values)){const i=e[n];t[n]=new c.GeometryAttribute({componentDatatype:i.componentDatatype,componentsPerAttribute:i.componentsPerAttribute,normalize:i.normalize,values:[]})}return t}function A(e,t,n){for(const r in t)if(t.hasOwnProperty(r)&&i.defined(t[r])&&i.defined(t[r].values)){const i=t[r];for(let t=0;t<i.componentsPerAttribute;++t)e[r].values.push(i.values[n*i.componentsPerAttribute+t])}}v.toWireframe=function(e){const t=e.indices;if(i.defined(t)){switch(e.primitiveType){case c.PrimitiveType.TRIANGLES:e.indices=function(e){const t=e.length,n=t/3*6,i=l.IndexDatatype.createTypedArray(t,n);let r=0;for(let n=0;n<t;n+=3,r+=6)b(i,r,e[n],e[n+1],e[n+2]);return i}(t);break;case c.PrimitiveType.TRIANGLE_STRIP:e.indices=function(e){const t=e.length;if(t>=3){const n=6*(t-2),i=l.IndexDatatype.createTypedArray(t,n);b(i,0,e[0],e[1],e[2]);let r=6;for(let n=3;n<t;++n,r+=6)b(i,r,e[n-1],e[n],e[n-2]);return i}return new Uint16Array}(t);break;case c.PrimitiveType.TRIANGLE_FAN:e.indices=function(e){if(e.length>0){const t=e.length-1,n=6*(t-1),i=l.IndexDatatype.createTypedArray(t,n),r=e[0];let a=0;for(let n=1;n<t;++n,a+=6)b(i,a,r,e[n],e[n+1]);return i}return new Uint16Array}(t)}e.primitiveType=c.PrimitiveType.LINES}return e},v.createLineSegmentsForVectors=function(e,t,n){t=i.defaultValue(t,"normal"),n=i.defaultValue(n,1e4);const r=e.attributes.position.values,s=e.attributes[t].values,u=r.length,l=new Float64Array(2*u);let p,d=0;for(let e=0;e<u;e+=3)l[d++]=r[e],l[d++]=r[e+1],l[d++]=r[e+2],l[d++]=r[e]+s[e]*n,l[d++]=r[e+1]+s[e+1]*n,l[d++]=r[e+2]+s[e+2]*n;const f=e.boundingSphere;return i.defined(f)&&(p=new a.BoundingSphere(f.center,f.radius+n)),new c.Geometry({attributes:{position:new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l})},primitiveType:c.PrimitiveType.LINES,boundingSphere:p})},v.createAttributeLocations=function(e){const t=["position","positionHigh","positionLow","position3DHigh","position3DLow","position2DHigh","position2DLow","pickColor","normal","st","tangent","bitangent","extrudeDirection","compressedAttributes"],n=e.attributes,r={};let a,s=0;const o=t.length;for(a=0;a<o;++a){const e=t[a];i.defined(n[e])&&(r[e]=s++)}for(const e in n)n.hasOwnProperty(e)&&!i.defined(r[e])&&(r[e]=s++);return r},v.reorderForPreVertexCache=function(e){const t=c.Geometry.computeNumberOfVertices(e),n=e.indices;if(i.defined(n)){const r=new Int32Array(t);for(let e=0;e<t;e++)r[e]=-1;const a=n,s=a.length,u=l.IndexDatatype.createTypedArray(t,s);let c,p=0,d=0,f=0;for(;p<s;)c=r[a[p]],-1!==c?u[d]=c:(c=a[p],r[c]=f,u[d]=f,++f),++p,++d;e.indices=u;const y=e.attributes;for(const e in y)if(y.hasOwnProperty(e)&&i.defined(y[e])&&i.defined(y[e].values)){const n=y[e],i=n.values;let a=0;const s=n.componentsPerAttribute,u=o.ComponentDatatype.createTypedArray(n.componentDatatype,f*s);for(;a<t;){const e=r[a];if(-1!==e)for(let t=0;t<s;t++)u[s*e+t]=i[s*a+t];++a}n.values=u}}return e},v.reorderForPostVertexCache=function(e,t){const n=e.indices;if(e.primitiveType===c.PrimitiveType.TRIANGLES&&i.defined(n)){const i=n.length;let r=0;for(let e=0;e<i;e++)n[e]>r&&(r=n[e]);e.indices=h.tipsify({indices:n,maximumIndex:r,cacheSize:t})}return e},v.fitToUnsignedShortIndices=function(e){const t=[],n=c.Geometry.computeNumberOfVertices(e);if(i.defined(e.indices)&&n>=r.CesiumMath.SIXTY_FOUR_KILOBYTES){let n=[],a=[],s=0,o=g(e.attributes);const u=e.indices,l=u.length;let p;e.primitiveType===c.PrimitiveType.TRIANGLES?p=3:e.primitiveType===c.PrimitiveType.LINES?p=2:e.primitiveType===c.PrimitiveType.POINTS&&(p=1);for(let d=0;d<l;d+=p){for(let t=0;t<p;++t){const r=u[d+t];let c=n[r];i.defined(c)||(c=s++,n[r]=c,A(o,e.attributes,r)),a.push(c)}s+p>=r.CesiumMath.SIXTY_FOUR_KILOBYTES&&(t.push(new c.Geometry({attributes:o,indices:a,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV})),n=[],a=[],s=0,o=g(e.attributes))}0!==a.length&&t.push(new c.Geometry({attributes:o,indices:a,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV}))}else t.push(e);return t};const T=new n.Cartesian3,x=new s.Cartographic;v.projectTo2D=function(e,t,r,s,u){const l=e.attributes[t],p=(u=i.defined(u)?u:new a.GeographicProjection).ellipsoid,d=l.values,f=new Float64Array(d.length);let y=0;for(let e=0;e<d.length;e+=3){const t=n.Cartesian3.fromArray(d,e,T),i=p.cartesianToCartographic(t,x),r=u.project(i,T);f[y++]=r.x,f[y++]=r.y,f[y++]=r.z}return e.attributes[r]=l,e.attributes[s]=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f}),delete e.attributes[t],e};const P={high:0,low:0};v.encodeAttribute=function(e,t,n,i){const r=e.attributes[t],a=r.values,s=a.length,l=new Float32Array(s),p=new Float32Array(s);for(let e=0;e<s;++e)u.EncodedCartesian3.encode(a[e],P),l[e]=P.high,p[e]=P.low;const d=r.componentsPerAttribute;return e.attributes[n]=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:d,values:l}),e.attributes[i]=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:d,values:p}),delete e.attributes[t],e};let w=new n.Cartesian3;function S(e,t){if(i.defined(t)){const i=t.values,r=i.length;for(let t=0;t<r;t+=3)n.Cartesian3.unpack(i,t,w),s.Matrix4.multiplyByPoint(e,w,w),n.Cartesian3.pack(w,i,t)}}function I(e,t){if(i.defined(t)){const i=t.values,r=i.length;for(let t=0;t<r;t+=3)n.Cartesian3.unpack(i,t,w),s.Matrix3.multiplyByVector(e,w,w),w=n.Cartesian3.normalize(w,w),n.Cartesian3.pack(w,i,t)}}const O=new s.Matrix4,E=new s.Matrix3;v.transformToWorldCoordinates=function(e){const t=e.modelMatrix;if(s.Matrix4.equals(t,s.Matrix4.IDENTITY))return e;const n=e.geometry.attributes;S(t,n.position),S(t,n.prevPosition),S(t,n.nextPosition),(i.defined(n.normal)||i.defined(n.tangent)||i.defined(n.bitangent))&&(s.Matrix4.inverse(t,O),s.Matrix4.transpose(O,O),s.Matrix4.getMatrix3(O,E),I(E,n.normal),I(E,n.tangent),I(E,n.bitangent));const r=e.geometry.boundingSphere;return i.defined(r)&&(e.geometry.boundingSphere=a.BoundingSphere.transform(r,t,r)),e.modelMatrix=s.Matrix4.clone(s.Matrix4.IDENTITY),e};const N=new n.Cartesian3;function L(e,t){const r=e.length;let s,u,p,d;e[0].modelMatrix;const f=i.defined(e[0][t].indices),y=e[0][t].primitiveType,m=function(e,t){const n=e.length,r={},a=e[0][t].attributes;let s;for(s in a)if(a.hasOwnProperty(s)&&i.defined(a[s])&&i.defined(a[s].values)){const u=a[s];let l=u.values.length,p=!0;for(let r=1;r<n;++r){const n=e[r][t].attributes[s];if(!i.defined(n)||u.componentDatatype!==n.componentDatatype||u.componentsPerAttribute!==n.componentsPerAttribute||u.normalize!==n.normalize){p=!1;break}l+=n.values.length}p&&(r[s]=new c.GeometryAttribute({componentDatatype:u.componentDatatype,componentsPerAttribute:u.componentsPerAttribute,normalize:u.normalize,values:o.ComponentDatatype.createTypedArray(u.componentDatatype,l)}))}return r}(e,t);let C,h,v,b;for(s in m)if(m.hasOwnProperty(s))for(C=m[s].values,d=0,u=0;u<r;++u)for(h=e[u][t].attributes[s].values,v=h.length,p=0;p<v;++p)C[d++]=h[p];if(f){let n=0;for(u=0;u<r;++u)n+=e[u][t].indices.length;const i=c.Geometry.computeNumberOfVertices(new c.Geometry({attributes:m,primitiveType:c.PrimitiveType.POINTS})),a=l.IndexDatatype.createTypedArray(i,n);let s=0,o=0;for(u=0;u<r;++u){const n=e[u][t].indices,i=n.length;for(d=0;d<i;++d)a[s++]=o+n[d];o+=c.Geometry.computeNumberOfVertices(e[u][t])}b=a}let g,A=new n.Cartesian3,T=0;for(u=0;u<r;++u){if(g=e[u][t].boundingSphere,!i.defined(g)){A=void 0;break}n.Cartesian3.add(g.center,A,A)}if(i.defined(A))for(n.Cartesian3.divideByScalar(A,r,A),u=0;u<r;++u){g=e[u][t].boundingSphere;const i=n.Cartesian3.magnitude(n.Cartesian3.subtract(g.center,A,N))+g.radius;i>T&&(T=i)}return new c.Geometry({attributes:m,indices:b,primitiveType:y,boundingSphere:i.defined(A)?new a.BoundingSphere(A,T):void 0})}v.combineInstances=function(e){const t=[],n=[],r=e.length;for(let a=0;a<r;++a){const r=e[a];i.defined(r.geometry)?t.push(r):i.defined(r.westHemisphereGeometry)&&i.defined(r.eastHemisphereGeometry)&&n.push(r)}const a=[];return t.length>0&&a.push(L(t,"geometry")),n.length>0&&(a.push(L(n,"westHemisphereGeometry")),a.push(L(n,"eastHemisphereGeometry"))),a};const z=new n.Cartesian3,D=new n.Cartesian3,M=new n.Cartesian3,G=new n.Cartesian3;v.computeNormal=function(e){const t=e.indices,i=e.attributes,a=i.position.values,s=i.position.values.length/3,u=t.length,l=new Array(s),p=new Array(u/3),d=new Array(u);let f;for(f=0;f<s;f++)l[f]={indexOffset:0,count:0,currentCount:0};let y=0;for(f=0;f<u;f+=3){const e=t[f],i=t[f+1],r=t[f+2],s=3*e,o=3*i,u=3*r;D.x=a[s],D.y=a[s+1],D.z=a[s+2],M.x=a[o],M.y=a[o+1],M.z=a[o+2],G.x=a[u],G.y=a[u+1],G.z=a[u+2],l[e].count++,l[i].count++,l[r].count++,n.Cartesian3.subtract(M,D,M),n.Cartesian3.subtract(G,D,G),p[y]=n.Cartesian3.cross(M,G,new n.Cartesian3),y++}let m,C=0;for(f=0;f<s;f++)l[f].indexOffset+=C,C+=l[f].count;for(y=0,f=0;f<u;f+=3){m=l[t[f]];let e=m.indexOffset+m.currentCount;d[e]=y,m.currentCount++,m=l[t[f+1]],e=m.indexOffset+m.currentCount,d[e]=y,m.currentCount++,m=l[t[f+2]],e=m.indexOffset+m.currentCount,d[e]=y,m.currentCount++,y++}const h=new Float32Array(3*s);for(f=0;f<s;f++){const e=3*f;if(m=l[f],n.Cartesian3.clone(n.Cartesian3.ZERO,z),m.count>0){for(y=0;y<m.count;y++)n.Cartesian3.add(z,p[d[m.indexOffset+y]],z);n.Cartesian3.equalsEpsilon(n.Cartesian3.ZERO,z,r.CesiumMath.EPSILON10)&&n.Cartesian3.clone(p[d[m.indexOffset]],z)}n.Cartesian3.equalsEpsilon(n.Cartesian3.ZERO,z,r.CesiumMath.EPSILON10)&&(z.z=1),n.Cartesian3.normalize(z,z),h[e]=z.x,h[e+1]=z.y,h[e+2]=z.z}return e.attributes.normal=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:h}),e};const R=new n.Cartesian3,V=new n.Cartesian3,F=new n.Cartesian3;v.computeTangentAndBitangent=function(e){e.attributes;const t=e.indices,i=e.attributes.position.values,r=e.attributes.normal.values,a=e.attributes.st.values,s=e.attributes.position.values.length/3,u=t.length,l=new Array(3*s);let p,d,f,y;for(p=0;p<l.length;p++)l[p]=0;for(p=0;p<u;p+=3){const e=t[p],n=t[p+1],r=t[p+2];d=3*e,f=3*n,y=3*r;const s=2*e,o=2*n,u=2*r,c=i[d],m=i[d+1],C=i[d+2],h=a[s],v=a[s+1],b=a[o+1]-v,g=a[u+1]-v,A=1/((a[o]-h)*g-(a[u]-h)*b),T=(g*(i[f]-c)-b*(i[y]-c))*A,x=(g*(i[f+1]-m)-b*(i[y+1]-m))*A,P=(g*(i[f+2]-C)-b*(i[y+2]-C))*A;l[d]+=T,l[d+1]+=x,l[d+2]+=P,l[f]+=T,l[f+1]+=x,l[f+2]+=P,l[y]+=T,l[y+1]+=x,l[y+2]+=P}const m=new Float32Array(3*s),C=new Float32Array(3*s);for(p=0;p<s;p++){d=3*p,f=d+1,y=d+2;const e=n.Cartesian3.fromArray(r,d,R),t=n.Cartesian3.fromArray(l,d,F),i=n.Cartesian3.dot(e,t);n.Cartesian3.multiplyByScalar(e,i,V),n.Cartesian3.normalize(n.Cartesian3.subtract(t,V,t),t),m[d]=t.x,m[f]=t.y,m[y]=t.z,n.Cartesian3.normalize(n.Cartesian3.cross(e,t,t),t),C[d]=t.x,C[f]=t.y,C[y]=t.z}return e.attributes.tangent=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:m}),e.attributes.bitangent=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:C}),e};const B=new n.Cartesian2,_=new n.Cartesian3,k=new n.Cartesian3,q=new n.Cartesian3;let U=new n.Cartesian2;function Y(e){switch(e.primitiveType){case c.PrimitiveType.TRIANGLE_FAN:return function(e){const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,3*(t-2));n[0]=1,n[1]=0,n[2]=2;let i=3;for(let e=3;e<t;++e)n[i++]=e-1,n[i++]=0,n[i++]=e;return e.indices=n,e.primitiveType=c.PrimitiveType.TRIANGLES,e}(e);case c.PrimitiveType.TRIANGLE_STRIP:return function(e){const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,3*(t-2));n[0]=0,n[1]=1,n[2]=2,t>3&&(n[3]=0,n[4]=2,n[5]=3);let i=6;for(let e=3;e<t-1;e+=2)n[i++]=e,n[i++]=e-1,n[i++]=e+1,e+2<t&&(n[i++]=e,n[i++]=e+1,n[i++]=e+2);return e.indices=n,e.primitiveType=c.PrimitiveType.TRIANGLES,e}(e);case c.PrimitiveType.TRIANGLES:return function(e){if(i.defined(e.indices))return e;const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,t);for(let e=0;e<t;++e)n[e]=e;return e.indices=n,e}(e);case c.PrimitiveType.LINE_STRIP:return function(e){const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,2*(t-1));n[0]=0,n[1]=1;let i=2;for(let e=2;e<t;++e)n[i++]=e-1,n[i++]=e;return e.indices=n,e.primitiveType=c.PrimitiveType.LINES,e}(e);case c.PrimitiveType.LINE_LOOP:return function(e){const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,2*t);n[0]=0,n[1]=1;let i=2;for(let e=2;e<t;++e)n[i++]=e-1,n[i++]=e;return n[i++]=t-1,n[i]=0,e.indices=n,e.primitiveType=c.PrimitiveType.LINES,e}(e);case c.PrimitiveType.LINES:return function(e){if(i.defined(e.indices))return e;const t=c.Geometry.computeNumberOfVertices(e),n=l.IndexDatatype.createTypedArray(t,t);for(let e=0;e<t;++e)n[e]=e;return e.indices=n,e}(e)}return e}function Z(e,t){Math.abs(e.y)<r.CesiumMath.EPSILON6&&(e.y=t?-r.CesiumMath.EPSILON6:r.CesiumMath.EPSILON6)}v.compressVertices=function(e){const r=e.attributes.extrudeDirection;let a,s;if(i.defined(r)){const i=r.values;s=i.length/3;const u=new Float32Array(2*s);let l=0;for(a=0;a<s;++a)n.Cartesian3.fromArray(i,3*a,_),n.Cartesian3.equals(_,n.Cartesian3.ZERO)?l+=2:(U=t.AttributeCompression.octEncodeInRange(_,65535,U),u[l++]=U.x,u[l++]=U.y);return e.attributes.compressedAttributes=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:u}),delete e.attributes.extrudeDirection,e}const u=e.attributes.normal,l=e.attributes.st,p=i.defined(u),d=i.defined(l);if(!p&&!d)return e;const f=e.attributes.tangent,y=e.attributes.bitangent,m=i.defined(f),C=i.defined(y);let h,v,b,g;p&&(h=u.values),d&&(v=l.values),m&&(b=f.values),C&&(g=y.values);s=(p?h.length:v.length)/(p?3:2);let A=s,T=d&&p?2:1;T+=m||C?1:0,A*=T;const x=new Float32Array(A);let P=0;for(a=0;a<s;++a){d&&(n.Cartesian2.fromArray(v,2*a,B),x[P++]=t.AttributeCompression.compressTextureCoordinates(B));const e=3*a;p&&i.defined(b)&&i.defined(g)?(n.Cartesian3.fromArray(h,e,_),n.Cartesian3.fromArray(b,e,k),n.Cartesian3.fromArray(g,e,q),t.AttributeCompression.octPack(_,k,q,B),x[P++]=B.x,x[P++]=B.y):(p&&(n.Cartesian3.fromArray(h,e,_),x[P++]=t.AttributeCompression.octEncodeFloat(_)),m&&(n.Cartesian3.fromArray(b,e,_),x[P++]=t.AttributeCompression.octEncodeFloat(_)),C&&(n.Cartesian3.fromArray(g,e,_),x[P++]=t.AttributeCompression.octEncodeFloat(_)))}return e.attributes.compressedAttributes=new c.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:T,values:x}),p&&delete e.attributes.normal,d&&delete e.attributes.st,C&&delete e.attributes.bitangent,m&&delete e.attributes.tangent,e};const H=new n.Cartesian3;function W(e,t,i,r){n.Cartesian3.add(e,n.Cartesian3.multiplyByScalar(n.Cartesian3.subtract(t,e,H),e.y/(e.y-t.y),H),i),n.Cartesian3.clone(i,r),Z(i,!0),Z(r,!1)}const X=new n.Cartesian3,j=new n.Cartesian3,J=new n.Cartesian3,K=new n.Cartesian3,Q={positions:new Array(7),indices:new Array(9)};function $(e,t,n){if(e.x>=0||t.x>=0||n.x>=0)return;!function(e,t,n){if(0!==e.y&&0!==t.y&&0!==n.y)return Z(e,e.y<0),Z(t,t.y<0),void Z(n,n.y<0);const i=Math.abs(e.y),a=Math.abs(t.y),s=Math.abs(n.y);let o;o=i>a?i>s?r.CesiumMath.sign(e.y):r.CesiumMath.sign(n.y):a>s?r.CesiumMath.sign(t.y):r.CesiumMath.sign(n.y);const u=o<0;Z(e,u),Z(t,u),Z(n,u)}(e,t,n);const i=e.y<0,a=t.y<0,s=n.y<0;let o=0;o+=i?1:0,o+=a?1:0,o+=s?1:0;const u=Q.indices;1===o?(u[1]=3,u[2]=4,u[5]=6,u[7]=6,u[8]=5,i?(W(e,t,X,J),W(e,n,j,K),u[0]=0,u[3]=1,u[4]=2,u[6]=1):a?(W(t,n,X,J),W(t,e,j,K),u[0]=1,u[3]=2,u[4]=0,u[6]=2):s&&(W(n,e,X,J),W(n,t,j,K),u[0]=2,u[3]=0,u[4]=1,u[6]=0)):2===o&&(u[2]=4,u[4]=4,u[5]=3,u[7]=5,u[8]=6,i?a?s||(W(n,e,X,J),W(n,t,j,K),u[0]=0,u[1]=1,u[3]=0,u[6]=2):(W(t,n,X,J),W(t,e,j,K),u[0]=2,u[1]=0,u[3]=2,u[6]=1):(W(e,t,X,J),W(e,n,j,K),u[0]=1,u[1]=2,u[3]=1,u[6]=0));const c=Q.positions;return c[0]=e,c[1]=t,c[2]=n,c.length=3,1!==o&&2!==o||(c[3]=X,c[4]=j,c[5]=J,c[6]=K,c.length=7),Q}function ee(e,t){const n=e.attributes;if(0===n.position.values.length)return;for(const e in n)if(n.hasOwnProperty(e)&&i.defined(n[e])&&i.defined(n[e].values)){const t=n[e];t.values=o.ComponentDatatype.createTypedArray(t.componentDatatype,t.values)}const r=c.Geometry.computeNumberOfVertices(e);return e.indices=l.IndexDatatype.createTypedArray(r,e.indices),t&&(e.boundingSphere=a.BoundingSphere.fromVertices(n.position.values)),e}function te(e){const t=e.attributes,n={};for(const e in t)if(t.hasOwnProperty(e)&&i.defined(t[e])&&i.defined(t[e].values)){const i=t[e];n[e]=new c.GeometryAttribute({componentDatatype:i.componentDatatype,componentsPerAttribute:i.componentsPerAttribute,normalize:i.normalize,values:[]})}return new c.Geometry({attributes:n,indices:[],primitiveType:e.primitiveType})}function ne(e,t,n){const r=i.defined(e.geometry.boundingSphere);t=ee(t,r),n=ee(n,r),i.defined(n)&&!i.defined(t)?e.geometry=n:!i.defined(n)&&i.defined(t)?e.geometry=t:(e.westHemisphereGeometry=t,e.eastHemisphereGeometry=n,e.geometry=void 0)}function ie(e,t){const n=new e,i=new e,r=new e;return function(a,s,o,u,c,l,p,d){const f=e.fromArray(c,a*t,n),y=e.fromArray(c,s*t,i),m=e.fromArray(c,o*t,r);e.multiplyByScalar(f,u.x,f),e.multiplyByScalar(y,u.y,y),e.multiplyByScalar(m,u.z,m);const C=e.add(f,y,f);e.add(C,m,C),d&&e.normalize(C,C),e.pack(C,l,p*t)}}const re=ie(s.Cartesian4,4),ae=ie(n.Cartesian3,3),se=ie(n.Cartesian2,2),oe=function(e,t,n,i,a,s,o){const u=a[e]*i.x,c=a[t]*i.y,l=a[n]*i.z;s[o]=u+c+l>r.CesiumMath.EPSILON6?1:0},ue=new n.Cartesian3,ce=new n.Cartesian3,le=new n.Cartesian3,pe=new n.Cartesian3;function de(e,t,a,s,o,u,c,l,p,d,C,h,v,b,g,A){if(!(i.defined(u)||i.defined(c)||i.defined(l)||i.defined(p)||i.defined(d)||0!==b))return;const T=function(e,t,a,s,o){let u,c,l,p,d,C,h,v;if(i.defined(o)||(o=new n.Cartesian3),i.defined(t.z)){if(n.Cartesian3.equalsEpsilon(e,t,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_X,o);if(n.Cartesian3.equalsEpsilon(e,a,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_Y,o);if(n.Cartesian3.equalsEpsilon(e,s,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_Z,o);u=n.Cartesian3.subtract(a,t,f),c=n.Cartesian3.subtract(s,t,y),l=n.Cartesian3.subtract(e,t,m),p=n.Cartesian3.dot(u,u),d=n.Cartesian3.dot(u,c),C=n.Cartesian3.dot(u,l),h=n.Cartesian3.dot(c,c),v=n.Cartesian3.dot(c,l)}else{if(n.Cartesian2.equalsEpsilon(e,t,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_X,o);if(n.Cartesian2.equalsEpsilon(e,a,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_Y,o);if(n.Cartesian2.equalsEpsilon(e,s,r.CesiumMath.EPSILON14))return n.Cartesian3.clone(n.Cartesian3.UNIT_Z,o);u=n.Cartesian2.subtract(a,t,f),c=n.Cartesian2.subtract(s,t,y),l=n.Cartesian2.subtract(e,t,m),p=n.Cartesian2.dot(u,u),d=n.Cartesian2.dot(u,c),C=n.Cartesian2.dot(u,l),h=n.Cartesian2.dot(c,c),v=n.Cartesian2.dot(c,l)}o.y=h*C-d*v,o.z=p*v-d*C;const b=p*h-d*d;if(0!==b)return o.y/=b,o.z/=b,o.x=1-o.y-o.z,o}(s,n.Cartesian3.fromArray(o,3*e,ue),n.Cartesian3.fromArray(o,3*t,ce),n.Cartesian3.fromArray(o,3*a,le),pe);if(i.defined(T)){if(i.defined(u)&&ae(e,t,a,T,u,h.normal.values,A,!0),i.defined(d)){const i=n.Cartesian3.fromArray(d,3*e,ue),r=n.Cartesian3.fromArray(d,3*t,ce),s=n.Cartesian3.fromArray(d,3*a,le);let o;n.Cartesian3.multiplyByScalar(i,T.x,i),n.Cartesian3.multiplyByScalar(r,T.y,r),n.Cartesian3.multiplyByScalar(s,T.z,s),n.Cartesian3.equals(i,n.Cartesian3.ZERO)&&n.Cartesian3.equals(r,n.Cartesian3.ZERO)&&n.Cartesian3.equals(s,n.Cartesian3.ZERO)?(o=ue,o.x=0,o.y=0,o.z=0):(o=n.Cartesian3.add(i,r,i),n.Cartesian3.add(o,s,o),n.Cartesian3.normalize(o,o)),n.Cartesian3.pack(o,h.extrudeDirection.values,3*A)}if(i.defined(C)&&oe(e,t,a,T,C,h.applyOffset.values,A),i.defined(c)&&ae(e,t,a,T,c,h.tangent.values,A,!0),i.defined(l)&&ae(e,t,a,T,l,h.bitangent.values,A,!0),i.defined(p)&&se(e,t,a,T,p,h.st.values,A),b>0)for(let n=0;n<b;n++){const i=v[n];fe(e,t,a,T,A,g[i],h[i])}}}function fe(e,t,n,i,r,a,s){const o=a.componentsPerAttribute,u=a.values,c=s.values;switch(o){case 4:re(e,t,n,i,u,c,r,!1);break;case 3:ae(e,t,n,i,u,c,r,!1);break;case 2:se(e,t,n,i,u,c,r,!1);break;default:c[r]=u[e]*i.x+u[t]*i.y+u[n]*i.z}}function ye(e,t,n,i,r,a){const s=e.position.values.length/3;if(-1!==r){const o=i[r],u=n[o];return-1===u?(n[o]=s,e.position.values.push(a.x,a.y,a.z),t.push(s),s):(t.push(u),u)}return e.position.values.push(a.x,a.y,a.z),t.push(s),s}const me={position:!0,normal:!0,bitangent:!0,tangent:!0,st:!0,extrudeDirection:!0,applyOffset:!0};function Ce(e){const t=e.geometry,r=t.attributes,a=r.position.values,s=i.defined(r.normal)?r.normal.values:void 0,o=i.defined(r.bitangent)?r.bitangent.values:void 0,u=i.defined(r.tangent)?r.tangent.values:void 0,c=i.defined(r.st)?r.st.values:void 0,l=i.defined(r.extrudeDirection)?r.extrudeDirection.values:void 0,p=i.defined(r.applyOffset)?r.applyOffset.values:void 0,d=t.indices,f=[];for(const e in r)r.hasOwnProperty(e)&&!me[e]&&i.defined(r[e])&&f.push(e);const y=f.length,m=te(t),C=te(t);let h,v,b,g,A;const T=[];T.length=a.length/3;const x=[];for(x.length=a.length/3,A=0;A<T.length;++A)T[A]=-1,x[A]=-1;const P=d.length;for(A=0;A<P;A+=3){const e=d[A],t=d[A+1],P=d[A+2];let w=n.Cartesian3.fromArray(a,3*e),S=n.Cartesian3.fromArray(a,3*t),I=n.Cartesian3.fromArray(a,3*P);const O=$(w,S,I);if(i.defined(O)&&O.positions.length>3){const n=O.positions,i=O.indices,w=i.length;for(let S=0;S<w;++S){const w=i[S],I=n[w];I.y<0?(h=C.attributes,v=C.indices,b=T):(h=m.attributes,v=m.indices,b=x),g=ye(h,v,b,d,w<3?A+w:-1,I),de(e,t,P,I,a,s,u,o,c,l,p,h,f,y,r,g)}}else i.defined(O)&&(w=O.positions[0],S=O.positions[1],I=O.positions[2]),w.y<0?(h=C.attributes,v=C.indices,b=T):(h=m.attributes,v=m.indices,b=x),g=ye(h,v,b,d,A,w),de(e,t,P,w,a,s,u,o,c,l,p,h,f,y,r,g),g=ye(h,v,b,d,A+1,S),de(e,t,P,S,a,s,u,o,c,l,p,h,f,y,r,g),g=ye(h,v,b,d,A+2,I),de(e,t,P,I,a,s,u,o,c,l,p,h,f,y,r,g)}ne(e,C,m)}const he=d.Plane.fromPointNormal(n.Cartesian3.ZERO,n.Cartesian3.UNIT_Y),ve=new n.Cartesian3,be=new n.Cartesian3;function ge(e,t,a,s,o,u,c){if(!i.defined(c))return;const l=n.Cartesian3.fromArray(s,3*e,ue);n.Cartesian3.equalsEpsilon(l,a,r.CesiumMath.EPSILON10)?u.applyOffset.values[o]=c[e]:u.applyOffset.values[o]=c[t]}function Ae(e){const t=e.geometry,a=t.attributes,s=a.position.values,o=i.defined(a.applyOffset)?a.applyOffset.values:void 0,u=t.indices,c=te(t),l=te(t);let d;const f=u.length,y=[];y.length=s.length/3;const m=[];for(m.length=s.length/3,d=0;d<y.length;++d)y[d]=-1,m[d]=-1;for(d=0;d<f;d+=2){const e=u[d],t=u[d+1],a=n.Cartesian3.fromArray(s,3*e,ue),f=n.Cartesian3.fromArray(s,3*t,ce);let C;Math.abs(a.y)<r.CesiumMath.EPSILON6&&(a.y<0?a.y=-r.CesiumMath.EPSILON6:a.y=r.CesiumMath.EPSILON6),Math.abs(f.y)<r.CesiumMath.EPSILON6&&(f.y<0?f.y=-r.CesiumMath.EPSILON6:f.y=r.CesiumMath.EPSILON6);let h=c.attributes,v=c.indices,b=m,g=l.attributes,A=l.indices,T=y;const x=p.IntersectionTests.lineSegmentPlane(a,f,he,le);if(i.defined(x)){const i=n.Cartesian3.multiplyByScalar(n.Cartesian3.UNIT_Y,5*r.CesiumMath.EPSILON9,ve);a.y<0&&(n.Cartesian3.negate(i,i),h=l.attributes,v=l.indices,b=y,g=c.attributes,A=c.indices,T=m);const p=n.Cartesian3.add(x,i,be);C=ye(h,v,b,u,d,a),ge(e,t,a,s,C,h,o),C=ye(h,v,b,u,-1,p),ge(e,t,p,s,C,h,o),n.Cartesian3.negate(i,i),n.Cartesian3.add(x,i,p),C=ye(g,A,T,u,-1,p),ge(e,t,p,s,C,g,o),C=ye(g,A,T,u,d+1,f),ge(e,t,f,s,C,g,o)}else{let n,i,r;a.y<0?(n=l.attributes,i=l.indices,r=y):(n=c.attributes,i=c.indices,r=m),C=ye(n,i,r,u,d,a),ge(e,t,a,s,C,n,o),C=ye(n,i,r,u,d+1,f),ge(e,t,f,s,C,n,o)}}ne(e,l,c)}const Te=new n.Cartesian2,xe=new n.Cartesian2,Pe=new n.Cartesian3,we=new n.Cartesian3,Se=new n.Cartesian3,Ie=new n.Cartesian3,Oe=new n.Cartesian3,Ee=new n.Cartesian3,Ne=new s.Cartesian4;function Le(e){const t=e.attributes,i=t.position.values,r=t.prevPosition.values,a=t.nextPosition.values,s=i.length;for(let e=0;e<s;e+=3){const t=n.Cartesian3.unpack(i,e,Pe);if(t.x>0)continue;const o=n.Cartesian3.unpack(r,e,we);(t.y<0&&o.y>0||t.y>0&&o.y<0)&&(e-3>0?(r[e]=i[e-3],r[e+1]=i[e-2],r[e+2]=i[e-1]):n.Cartesian3.pack(t,r,e));const u=n.Cartesian3.unpack(a,e,Se);(t.y<0&&u.y>0||t.y>0&&u.y<0)&&(e+3<s?(a[e]=i[e+3],a[e+1]=i[e+4],a[e+2]=i[e+5]):n.Cartesian3.pack(t,a,e))}}const ze=5*r.CesiumMath.EPSILON9,De=r.CesiumMath.EPSILON6;v.splitLongitude=function(e){const t=e.geometry,o=t.boundingSphere;if(i.defined(o)){if(o.center.x-o.radius>0||a.BoundingSphere.intersectPlane(o,d.Plane.ORIGIN_ZX_PLANE)!==a.Intersect.INTERSECTING)return e}if(t.geometryType!==c.GeometryType.NONE)switch(t.geometryType){case c.GeometryType.POLYLINES:!function(e){const t=e.geometry,a=t.attributes,o=a.position.values,u=a.prevPosition.values,c=a.nextPosition.values,l=a.expandAndWidth.values,d=i.defined(a.st)?a.st.values:void 0,f=i.defined(a.color)?a.color.values:void 0,y=te(t),m=te(t);let C,h,v,b=!1;const g=o.length/3;for(C=0;C<g;C+=4){const e=C,t=C+2,a=n.Cartesian3.fromArray(o,3*e,Pe),g=n.Cartesian3.fromArray(o,3*t,we);if(Math.abs(a.y)<De)for(a.y=De*(g.y<0?-1:1),o[3*C+1]=a.y,o[3*(C+1)+1]=a.y,h=3*e;h<3*e+12;h+=3)u[h]=o[3*C],u[h+1]=o[3*C+1],u[h+2]=o[3*C+2];if(Math.abs(g.y)<De)for(g.y=De*(a.y<0?-1:1),o[3*(C+2)+1]=g.y,o[3*(C+3)+1]=g.y,h=3*e;h<3*e+12;h+=3)c[h]=o[3*(C+2)],c[h+1]=o[3*(C+2)+1],c[h+2]=o[3*(C+2)+2];let A=y.attributes,T=y.indices,x=m.attributes,P=m.indices;const w=p.IntersectionTests.lineSegmentPlane(a,g,he,Ie);if(i.defined(w)){b=!0;const o=n.Cartesian3.multiplyByScalar(n.Cartesian3.UNIT_Y,ze,Oe);a.y<0&&(n.Cartesian3.negate(o,o),A=m.attributes,T=m.indices,x=y.attributes,P=y.indices);const p=n.Cartesian3.add(w,o,Ee);A.position.values.push(a.x,a.y,a.z,a.x,a.y,a.z),A.position.values.push(p.x,p.y,p.z),A.position.values.push(p.x,p.y,p.z),A.prevPosition.values.push(u[3*e],u[3*e+1],u[3*e+2]),A.prevPosition.values.push(u[3*e+3],u[3*e+4],u[3*e+5]),A.prevPosition.values.push(a.x,a.y,a.z,a.x,a.y,a.z),A.nextPosition.values.push(p.x,p.y,p.z),A.nextPosition.values.push(p.x,p.y,p.z),A.nextPosition.values.push(p.x,p.y,p.z),A.nextPosition.values.push(p.x,p.y,p.z),n.Cartesian3.negate(o,o),n.Cartesian3.add(w,o,p),x.position.values.push(p.x,p.y,p.z),x.position.values.push(p.x,p.y,p.z),x.position.values.push(g.x,g.y,g.z,g.x,g.y,g.z),x.prevPosition.values.push(p.x,p.y,p.z),x.prevPosition.values.push(p.x,p.y,p.z),x.prevPosition.values.push(p.x,p.y,p.z),x.prevPosition.values.push(p.x,p.y,p.z),x.nextPosition.values.push(g.x,g.y,g.z,g.x,g.y,g.z),x.nextPosition.values.push(c[3*t],c[3*t+1],c[3*t+2]),x.nextPosition.values.push(c[3*t+3],c[3*t+4],c[3*t+5]);const S=n.Cartesian2.fromArray(l,2*e,Te),I=Math.abs(S.y);A.expandAndWidth.values.push(-1,I,1,I),A.expandAndWidth.values.push(-1,-I,1,-I),x.expandAndWidth.values.push(-1,I,1,I),x.expandAndWidth.values.push(-1,-I,1,-I);let O=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(w,a,Se));if(O/=n.Cartesian3.magnitudeSquared(n.Cartesian3.subtract(g,a,Se)),i.defined(f)){const n=s.Cartesian4.fromArray(f,4*e,Ne),i=s.Cartesian4.fromArray(f,4*t,Ne),a=r.CesiumMath.lerp(n.x,i.x,O),o=r.CesiumMath.lerp(n.y,i.y,O),u=r.CesiumMath.lerp(n.z,i.z,O),c=r.CesiumMath.lerp(n.w,i.w,O);for(h=4*e;h<4*e+8;++h)A.color.values.push(f[h]);for(A.color.values.push(a,o,u,c),A.color.values.push(a,o,u,c),x.color.values.push(a,o,u,c),x.color.values.push(a,o,u,c),h=4*t;h<4*t+8;++h)x.color.values.push(f[h])}if(i.defined(d)){const i=n.Cartesian2.fromArray(d,2*e,Te),a=n.Cartesian2.fromArray(d,2*(C+3),xe),s=r.CesiumMath.lerp(i.x,a.x,O);for(h=2*e;h<2*e+4;++h)A.st.values.push(d[h]);for(A.st.values.push(s,i.y),A.st.values.push(s,a.y),x.st.values.push(s,i.y),x.st.values.push(s,a.y),h=2*t;h<2*t+4;++h)x.st.values.push(d[h])}v=A.position.values.length/3-4,T.push(v,v+2,v+1),T.push(v+1,v+2,v+3),v=x.position.values.length/3-4,P.push(v,v+2,v+1),P.push(v+1,v+2,v+3)}else{let e,t;for(a.y<0?(e=m.attributes,t=m.indices):(e=y.attributes,t=y.indices),e.position.values.push(a.x,a.y,a.z),e.position.values.push(a.x,a.y,a.z),e.position.values.push(g.x,g.y,g.z),e.position.values.push(g.x,g.y,g.z),h=3*C;h<3*C+12;++h)e.prevPosition.values.push(u[h]),e.nextPosition.values.push(c[h]);for(h=2*C;h<2*C+8;++h)e.expandAndWidth.values.push(l[h]),i.defined(d)&&e.st.values.push(d[h]);if(i.defined(f))for(h=4*C;h<4*C+16;++h)e.color.values.push(f[h]);v=e.position.values.length/3-4,t.push(v,v+2,v+1),t.push(v+1,v+2,v+3)}}b&&(Le(m),Le(y)),ne(e,m,y)}(e);break;case c.GeometryType.TRIANGLES:Ce(e);break;case c.GeometryType.LINES:Ae(e)}else Y(t),t.primitiveType===c.PrimitiveType.TRIANGLES?Ce(e):t.primitiveType===c.PrimitiveType.LINES&&Ae(e);return e};var Me=v;e.GeometryPipeline=Me}));
