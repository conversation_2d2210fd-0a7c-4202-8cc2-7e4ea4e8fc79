<template>
  <div class="inspect-route-container">
    <div class="close-btn" @click="handleClose"></div>
    <img src="@/assets/StructureDiagram/robot/routeContent.png" class="position-absolute w1550 h633 top-82 left-56" />
  </div>
</template>

<script setup>
  import lib from '@/utils/lib.ts'
  const handleClose = lib.provideTools.handleClose.inject()
</script>

<style lang="scss" scoped>
  .inspect-route-container {
    position: absolute;
    z-index: 999;
    width: 1689px;
    height: 784px;
    background-image: url('@/assets/StructureDiagram/robot/routeBg.png');
    background-size: cover;
    .close-btn {
      position: absolute;
      top: 5px;
      right: 10px;
      width: 50px;
      height: 50px;
      cursor: pointer;
    }
  }
</style>
