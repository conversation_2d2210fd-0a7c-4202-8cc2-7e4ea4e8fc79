/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

/*! ../../../../node_modules/.pnpm/css-loader@5.2.7_webpack@5.75.0/node_modules/css-loader/dist/runtime/api.js */

/*! ../../../../node_modules/.pnpm/css-loader@5.2.7_webpack@5.75.0/node_modules/css-loader/dist/runtime/getUrl.js */

/*! ../controller/level-helper */

/*! ../crypt/decrypter */

/*! ../demux/aacdemuxer */

/*! ../demux/chunk-cache */

/*! ../demux/id3 */

/*! ../demux/mp3demuxer */

/*! ../demux/mp4demuxer */

/*! ../demux/transmuxer */

/*! ../demux/transmuxer-interface */

/*! ../demux/transmuxer-worker.ts */

/*! ../demux/tsdemuxer */

/*! ../errors */

/*! ../events */

/*! ../is-supported */

/*! ../loader/fragment */

/*! ../loader/fragment-loader */

/*! ../loader/load-stats */

/*! ../remux/mp4-remuxer */

/*! ../remux/passthrough-remuxer */

/*! ../task-loop */

/*! ../types/level */

/*! ../types/loader */

/*! ../types/transmuxer */

/*! ../utils/attr-list */

/*! ../utils/binary-search */

/*! ../utils/buffer-helper */

/*! ../utils/cea-608-parser */

/*! ../utils/codecs */

/*! ../utils/discontinuities */

/*! ../utils/ewma */

/*! ../utils/ewma-bandwidth-estimator */

/*! ../utils/imsc1-ttml-parser */

/*! ../utils/logger */

/*! ../utils/mediakeys-helper */

/*! ../utils/mediasource-helper */

/*! ../utils/mp4-tools */

/*! ../utils/output-filter */

/*! ../utils/texttrack-utils */

/*! ../utils/time-ranges */

/*! ../utils/timescale-conversion */

/*! ../utils/typed-array */

/*! ../utils/webvtt-parser */

/*! ./Images/TimelineIcons.png */

/*! ./_DataView */

/*! ./_Hash */

/*! ./_ListCache */

/*! ./_Map */

/*! ./_MapCache */

/*! ./_Promise */

/*! ./_Set */

/*! ./_Stack */

/*! ./_Symbol */

/*! ./_Uint8Array */

/*! ./_WeakMap */

/*! ./_arrayEach */

/*! ./_arrayFilter */

/*! ./_arrayLikeKeys */

/*! ./_arrayPush */

/*! ./_assignValue */

/*! ./_assocIndexOf */

/*! ./_baseAssign */

/*! ./_baseAssignIn */

/*! ./_baseAssignValue */

/*! ./_baseClone */

/*! ./_baseCreate */

/*! ./_baseGetAllKeys */

/*! ./_baseGetTag */

/*! ./_baseIsArguments */

/*! ./_baseIsMap */

/*! ./_baseIsNative */

/*! ./_baseIsSet */

/*! ./_baseIsTypedArray */

/*! ./_baseKeys */

/*! ./_baseKeysIn */

/*! ./_baseTimes */

/*! ./_baseUnary */

/*! ./_cloneArrayBuffer */

/*! ./_cloneBuffer */

/*! ./_cloneDataView */

/*! ./_cloneRegExp */

/*! ./_cloneSymbol */

/*! ./_cloneTypedArray */

/*! ./_copyArray */

/*! ./_copyObject */

/*! ./_copySymbols */

/*! ./_copySymbolsIn */

/*! ./_coreJsData */

/*! ./_defineProperty */

/*! ./_freeGlobal */

/*! ./_getAllKeys */

/*! ./_getAllKeysIn */

/*! ./_getMapData */

/*! ./_getNative */

/*! ./_getPrototype */

/*! ./_getRawTag */

/*! ./_getSymbols */

/*! ./_getSymbolsIn */

/*! ./_getTag */

/*! ./_getValue */

/*! ./_hashClear */

/*! ./_hashDelete */

/*! ./_hashGet */

/*! ./_hashHas */

/*! ./_hashSet */

/*! ./_initCloneArray */

/*! ./_initCloneByTag */

/*! ./_initCloneObject */

/*! ./_isIndex */

/*! ./_isKeyable */

/*! ./_isMasked */

/*! ./_isPrototype */

/*! ./_listCacheClear */

/*! ./_listCacheDelete */

/*! ./_listCacheGet */

/*! ./_listCacheHas */

/*! ./_listCacheSet */

/*! ./_mapCacheClear */

/*! ./_mapCacheDelete */

/*! ./_mapCacheGet */

/*! ./_mapCacheHas */

/*! ./_mapCacheSet */

/*! ./_nativeCreate */

/*! ./_nativeKeys */

/*! ./_nativeKeysIn */

/*! ./_nodeUtil */

/*! ./_objectToString */

/*! ./_overArg */

/*! ./_root */

/*! ./_stackClear */

/*! ./_stackDelete */

/*! ./_stackGet */

/*! ./_stackHas */

/*! ./_stackSet */

/*! ./_toSource */

/*! ./aac-helper */

/*! ./adts */

/*! ./aes-crypto */

/*! ./aes-decryptor */

/*! ./base-audio-demuxer */

/*! ./base-playlist-controller */

/*! ./base-stream-controller */

/*! ./buffer-operation-queue */

/*! ./chunk-cache */

/*! ./config */

/*! ./controller/abr-controller */

/*! ./controller/audio-stream-controller */

/*! ./controller/audio-track-controller */

/*! ./controller/buffer-controller */

/*! ./controller/cap-level-controller */

/*! ./controller/eme-controller */

/*! ./controller/fps-controller */

/*! ./controller/fragment-tracker */

/*! ./controller/id3-track-controller */

/*! ./controller/latency-controller */

/*! ./controller/level-controller */

/*! ./controller/stream-controller */

/*! ./controller/subtitle-stream-controller */

/*! ./controller/subtitle-track-controller */

/*! ./controller/timeline-controller */

/*! ./dummy-demuxed-track */

/*! ./eq */

/*! ./errors */

/*! ./events */

/*! ./exp-golomb */

/*! ./fast-aes-key */

/*! ./fragment */

/*! ./fragment-finders */

/*! ./fragment-tracker */

/*! ./gap-controller */

/*! ./id3 */

/*! ./is-supported */

/*! ./isArguments */

/*! ./isArray */

/*! ./isArrayLike */

/*! ./isBuffer */

/*! ./isFunction */

/*! ./isLength */

/*! ./isMap */

/*! ./isObject */

/*! ./isObjectLike */

/*! ./isSet */

/*! ./isTypedArray */

/*! ./keys */

/*! ./keysIn */

/*! ./level-details */

/*! ./level-helper */

/*! ./level-key */

/*! ./load-stats */

/*! ./loader/key-loader */

/*! ./loader/playlist-loader */

/*! ./logger */

/*! ./m3u8-parser */

/*! ./mp4-generator */

/*! ./mp4-tools */

/*! ./mpegaudio */

/*! ./sample-aes */

/*! ./src/polyfills/number */

/*! ./stubArray */

/*! ./stubFalse */

/*! ./texttrack-utils */

/*! ./timescale-conversion */

/*! ./tsdemuxer */

/*! ./typed-array */

/*! ./utils/cues */

/*! ./utils/fetch-loader */

/*! ./utils/logger */

/*! ./utils/mediakeys-helper */

/*! ./utils/mediasource-helper */

/*! ./utils/xhr-loader */

/*! ./vttcue */

/*! ./vttparser */

/*! ./webvtt-parser */

/*! NoSleep.js v0.9.0 - git.io/vfn01 - Rich Tibbett - MIT license */

/*! eventemitter3 */

/*! exports provided: AttrList */

/*! exports provided: BufferHelper */

/*! exports provided: ChunkMetadata */

/*! exports provided: ElementaryStreamTypes, BaseSegment, Fragment, Part */

/*! exports provided: ErrorTypes, ErrorDetails */

/*! exports provided: Events */

/*! exports provided: FragmentState, FragmentTracker */

/*! exports provided: HlsSkip, getSkipValue, HlsUrlParameters, Level */

/*! exports provided: IMSC1_CODEC, parseIMSC1 */

/*! exports provided: KeySystems, requestMediaKeySystemAccess */

/*! exports provided: LevelDetails */

/*! exports provided: LevelKey */

/*! exports provided: LoadStats */

/*! exports provided: PlaylistContextType, PlaylistLevelType */

/*! exports provided: Row, CaptionScreen, default */

/*! exports provided: STALL_MINIMUM_DURATION_MS, MAX_START_GAP_JUMP, SKIP_BUFFER_HOLE_STEP_SECONDS, SKIP_BUFFER_RANGE_START, default */

/*! exports provided: State, default */

/*! exports provided: SubtitleStreamController */

/*! exports provided: TimelineController */

/*! exports provided: addGroupId, assignTrackIdsByGroup, updatePTS, updateFragPTSDTS, mergeDetails, mapPartIntersection, mapFragmentIntersection, adjustSliding, addSliding, computeReloadInterval, getFragmentWithSN, getPartWith */

/*! exports provided: appendFrame, parseHeader, isHeaderPattern, isHeader, canParse, probe */

/*! exports provided: bin2str, readUint16, readUint32, writeUint32, findBox, parseSegmentIndex, parseInitSegment, getStartDTS, getDuration, computeRawDurationFromSamples, offsetStartDTS, segmentValidRange, appendUint8Array */

/*! exports provided: default */

/*! exports provided: default, LoadError */

/*! exports provided: default, isPromise, TransmuxConfig, TransmuxState */

/*! exports provided: default, normalizePts */

/*! exports provided: discardEPB, default */

/*! exports provided: dummyTrack */

/*! exports provided: enableLogs, logger */

/*! exports provided: fetchSupported, default */

/*! exports provided: findFirstFragWithCC, shouldAlignOnDiscontinuities, findDiscontinuousReferenceFrag, adjustSlidingStart, alignStream, alignPDT */

/*! exports provided: findFragmentByPDT, findFragmentByPTS, fragmentWithinToleranceTest, pdtWithinToleranceTest, findFragWithCC */

/*! exports provided: generateCueId, parseWebVTT */

/*! exports provided: getAudioConfig, isHeaderPattern, getHeaderLength, getFullFrameLength, canGetFrameLength, isHeader, canParse, probe, initTrackConfig, getFrameDuration, parseFrameHeader, appendFrame */

/*! exports provided: getMediaSource */

/*! exports provided: hlsDefaultConfig, mergeConfig, enableStreamingMode */

/*! exports provided: initPTSFn, default */

/*! exports provided: isCodecType, isCodecSupportedInMp4 */

/*! exports provided: isFiniteNumber, MAX_SAFE_INTEGER */

/*! exports provided: isHeader, isFooter, getID3Data, canParse, getTimeStamp, isTimeStampFrame, getID3Frames, decodeFrame, utf8ArrayToStr, testables */

/*! exports provided: isSupported, changeTypeSupported */

/*! exports provided: parseTimeStamp, fixLineBreaks, VTTParser */

/*! exports provided: removePadding, default */

/*! exports provided: sendAddTrackEvent, addCueToTrack, clearCurrentCues, removeCuesInRange, getCuesInRange */

/*! exports provided: sliceUint8 */

/*! exports provided: toTimescaleFromBase, toTimescaleFromScale, toMsFromMpegTsClock, toMpegTsClockFromTimescale */

/*! https://mths.be/punycode v1.4.1 by @mathias */

/*! no static exports found */

/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */

/*! url-toolkit */

/*! webworkify-webpack */

/*!********************!*\
  !*** ./src/hls.ts ***!
  \********************/

/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/

/*!***********************!*\
  !*** ./src/errors.ts ***!
  \***********************/

/*!***********************!*\
  !*** ./src/events.ts ***!
  \***********************/

/*!**************************!*\
  !*** ./src/demux/id3.ts ***!
  \**************************/

/*!**************************!*\
  !*** ./src/task-loop.ts ***!
  \**************************/

/*!***************************!*\
  !*** ./src/demux/adts.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/utils/cues.ts ***!
  \***************************/

/*!***************************!*\
  !*** ./src/utils/ewma.ts ***!
  \***************************/

/*!****************************!*\
  !*** ./src/types/level.ts ***!
  \****************************/

/*!*****************************!*\
  !*** ./src/is-supported.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/types/loader.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/utils/codecs.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/utils/vttcue.ts ***!
  \*****************************/

/*!********************************!*\
  !*** ./src/crypt/decrypter.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/demux/mpegaudio.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/demux/tsdemuxer.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/loader/fragment.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/utils/attr-list.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/utils/mp4-tools.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/utils/vttparser.ts ***!
  \********************************/

/*!*********************************!*\
  !*** ./src/crypt/aes-crypto.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/aacdemuxer.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/exp-golomb.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/mp3demuxer.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/mp4demuxer.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/sample-aes.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/demux/transmuxer.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/loader/level-key.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/polyfills/number.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/remux/aac-helper.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/types/transmuxer.ts ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/utils/xhr-loader.ts ***!
  \*********************************/

/*!**********************************!*\
  !*** ./src/demux/chunk-cache.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/loader/key-loader.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/loader/load-stats.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/remux/mp4-remuxer.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/utils/time-ranges.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/utils/typed-array.ts ***!
  \**********************************/

/*!***********************************!*\
  !*** ./src/crypt/fast-aes-key.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/loader/m3u8-parser.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/utils/fetch-loader.ts ***!
  \***********************************/

/*!************************************!*\
  !*** ../motor-ts/library/motor.js ***!
  \************************************/

/*!************************************!*\
  !*** ./src/crypt/aes-decryptor.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/remux/mp4-generator.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/utils/binary-search.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/utils/buffer-helper.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/utils/output-filter.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/utils/webvtt-parser.ts ***!
  \************************************/

/*!*************************************!*\
  !*** ./src/loader/level-details.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/utils/cea-608-parser.ts ***!
  \*************************************/

/*!**************************************!*\
  !*** ./src/utils/discontinuities.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/utils/texttrack-utils.ts ***!
  \**************************************/

/*!***************************************!*\
  !*** ./src/loader/fragment-loader.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/loader/playlist-loader.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/utils/mediakeys-helper.ts ***!
  \***************************************/

/*!****************************************!*\
  !*** ./src/controller/level-helper.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/demux/transmuxer-worker.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/utils/imsc1-ttml-parser.ts ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./src/demux/base-audio-demuxer.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/utils/mediasource-helper.ts ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./src/controller/abr-controller.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/controller/eme-controller.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/controller/fps-controller.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/controller/gap-controller.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/demux/dummy-demuxed-track.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/remux/passthrough-remuxer.ts ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./src/demux/transmuxer-interface.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/utils/timescale-conversion.ts ***!
  \*******************************************/

/*!********************************************!*\
  !*** ./src/controller/fragment-finders.ts ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/controller/fragment-tracker.ts ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/controller/level-controller.ts ***!
  \********************************************/

/*!*********************************************!*\
  !*** ./node_modules/eventemitter3/index.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/controller/buffer-controller.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/controller/stream-controller.ts ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/motor-sdk/index.ts + 39 modules ***!
  \*********************************************/

/*!**********************************************!*\
  !*** ./src/controller/latency-controller.ts ***!
  \**********************************************/

/*!***********************************************!*\
  !*** ./src/controller/timeline-controller.ts ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/utils/ewma-bandwidth-estimator.ts ***!
  \***********************************************/

/*!************************************************!*\
  !*** ./src/controller/cap-level-controller.ts ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/controller/id3-track-controller.ts ***!
  \************************************************/

/*!**************************************************!*\
  !*** ./node_modules/webworkify-webpack/index.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/controller/audio-track-controller.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/controller/base-stream-controller.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/controller/buffer-operation-queue.ts ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./src/controller/audio-stream-controller.ts ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./src/controller/base-playlist-controller.ts ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/url-toolkit/src/url-toolkit.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/controller/subtitle-track-controller.ts ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./src/controller/subtitle-stream-controller.ts ***!
  \******************************************************/

/*!************************************************************!*\
  !*** ../motor-ts/library/Widgets/Images/TimelineIcons.png ***!
  \************************************************************/

/*!*************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/eq.js ***!
  \*************************************************************************/

/*!***************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Map.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Set.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keys.js ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Hash.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_root.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isMap.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isSet.js ***!
  \****************************************************************************/

/*!*****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Stack.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/keysIn.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/lodash.js ***!
  \*****************************************************************************/

/*!******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Symbol.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getTag.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isArray.js ***!
  \******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Promise.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_WeakMap.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_hashGet.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_hashHas.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_hashSet.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isIndex.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_overArg.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isBuffer.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isLength.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isObject.js ***!
  \*******************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_DataView.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_MapCache.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseKeys.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getValue.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isMasked.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nodeUtil.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_stackGet.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_stackHas.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_stackSet.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_toSource.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/cloneDeep.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/stubArray.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/stubFalse.js ***!
  \********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_ListCache.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_arrayEach.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_arrayPush.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseClone.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsMap.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsSet.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseTimes.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseUnary.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_copyArray.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getNative.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getRawTag.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_hashClear.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isKeyable.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isFunction.js ***!
  \*********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_Uint8Array.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseAssign.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseCreate.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseGetTag.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseKeysIn.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_copyObject.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_coreJsData.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_freeGlobal.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getAllKeys.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getMapData.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getSymbols.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_hashDelete.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nativeKeys.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_stackClear.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isArguments.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isArrayLike.js ***!
  \**********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_arrayFilter.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_assignValue.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneBuffer.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneRegExp.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneSymbol.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_copySymbols.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_isPrototype.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_mapCacheGet.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_mapCacheHas.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_mapCacheSet.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_stackDelete.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isObjectLike.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isTypedArray.js ***!
  \***********************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_assocIndexOf.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseAssignIn.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsNative.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getAllKeysIn.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getPrototype.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_getSymbolsIn.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_listCacheGet.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_listCacheHas.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_listCacheSet.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nativeCreate.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_nativeKeysIn.js ***!
  \************************************************************************************/

/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_arrayLikeKeys.js ***!
  \*************************************************************************************/

/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneDataView.js ***!
  \*************************************************************************************/

/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_copySymbolsIn.js ***!
  \*************************************************************************************/

/*!*************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_mapCacheClear.js ***!
  \*************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseGetAllKeys.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_defineProperty.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_initCloneArray.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_initCloneByTag.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_listCacheClear.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_mapCacheDelete.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_objectToString.js ***!
  \**************************************************************************************/

/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseAssignValue.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsArguments.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneTypedArray.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_initCloneObject.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_listCacheDelete.js ***!
  \***************************************************************************************/

/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js ***!
  \****************************************************************************************/

/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_baseIsTypedArray.js ***!
  \****************************************************************************************/

/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_cloneArrayBuffer.js ***!
  \****************************************************************************************/

/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/css-loader@5.2.7_webpack@5.75.0/node_modules/css-loader/dist/runtime/api.js ***!
  \************************************************************************************************************/

/*!***************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/css-loader@5.2.7_webpack@5.75.0/node_modules/css-loader/dist/runtime/getUrl.js ***!
  \***************************************************************************************************************/

/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/style-loader@2.0.0_webpack@5.75.0/node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js ***!
  \*************************************************************************************************************************************/

/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/css-loader@5.2.7_webpack@5.75.0/node_modules/css-loader/dist/cjs.js!../motor-ts/library/Widgets/widgets.css ***!
  \********************************************************************************************************************************************/

/**
  @license
  fontmetrics.js - https://github.com/Pomax/fontmetrics.js

  Copyright (C) 2011 by Mike "Pomax" Kamermans

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to deal
  in the Software without restriction, including without limitation the rights
  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
  THE SOFTWARE.
**/

/**
  @license
  when.js - https://github.com/cujojs/when

  MIT License (c) copyright B Cavalier & J Hann

 * A lightweight CommonJS Promises/A and when() implementation
 * when is part of the cujo.js family of libraries (http://cujojs.com/)
 *
 * Licensed under the MIT License at:
 * http://www.opensource.org/licenses/mit-license.php
 *
 * @version 1.7.1
 */

/**
 * @license
 *
 * Grauw URI utilities
 *
 * See: http://hg.grauw.nl/grauw-lib/file/tip/src/uri.js
 *
 * <AUTHOR> Holst (http://www.grauw.nl/)
 *
 *   Copyright 2012 Laurens Holst
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 */

/**
 * @license
 * Copyright (c) 2000-2005, Sean O'Neil (<EMAIL>)
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * * Redistributions of source code must retain the above copyright notice,
 *   this list of conditions and the following disclaimer.
 * * Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 * * Neither the name of the project nor the names of its contributors may be
 *   used to endorse or promote products derived from this software without
 *   specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * Modifications made by Analytical Graphics, Inc.
 */

/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */

/**
@license
mersenne-twister.js - https://gist.github.com/banksean/300494

   Copyright (C) 1997 - 2002, Makoto Matsumoto and Takuji Nishimura,
   All rights reserved.

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:

     1. Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.

     2. Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.

     3. The names of its contributors may not be used to endorse or promote
        products derived from this software without specific prior written
        permission.

   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
   A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
   CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/**
@license
sprintf.js from the php.js project - https://github.com/kvz/phpjs
Directly from https://github.com/kvz/phpjs/blob/master/functions/strings/sprintf.js

php.js is copyright 2012 Kevin van Zonneveld.

Portions copyright Brett Zamir (http://brett-zamir.me), Kevin van Zonneveld
(http://kevin.vanzonneveld.net), Onno Marsman, Theriault, Michael White
(http://getsprink.com), Waldo Malqui Silva, Paulo Freitas, Jack, Jonas
Raoni Soares Silva (http://www.jsfromhell.com), Philip Peterson, Legaev
Andrey, Ates Goral (http://magnetiq.com), Alex, Ratheous, Martijn Wieringa,
Rafa? Kukawski (http://blog.kukawski.pl), lmeyrick
(https://sourceforge.net/projects/bcmath-js/), Nate, Philippe Baumann,
Enrique Gonzalez, Webtoolkit.info (http://www.webtoolkit.info/), Carlos R.
L. Rodrigues (http://www.jsfromhell.com), Ash Searle
(http://hexmen.com/blog/), Jani Hartikainen, travc, Ole Vrijenhoek,
Erkekjetter, Michael Grier, Rafa? Kukawski (http://kukawski.pl), Johnny
Mast (http://www.phpvrouwen.nl), T.Wild, d3x,
http://stackoverflow.com/questions/57803/how-to-convert-decimal-to-hex-in-javascript,
Rafa? Kukawski (http://blog.kukawski.pl/), stag019, pilus, WebDevHobo
(http://webdevhobo.blogspot.com/), marrtins, GeekFG
(http://geekfg.blogspot.com), Andrea Giammarchi
(http://webreflection.blogspot.com), Arpad Ray (mailto:<EMAIL>),
gorthaur, Paul Smith, Tim de Koning (http://www.kingsquare.nl), Joris, Oleg
Eremeev, Steve Hilder, majak, gettimeofday, KELAN, Josh Fraser
(http://onlineaspect.com/2007/06/08/auto-detect-a-time-zone-with-javascript/),
Marc Palau, Martin
(http://www.erlenwiese.de/), Breaking Par Consulting Inc
(http://www.breakingpar.com/bkp/home.nsf/0/87256B280015193F87256CFB006C45F7),
Chris, Mirek Slugen, saulius, Alfonso Jimenez
(http://www.alfonsojimenez.com), Diplom@t (http://difane.com/), felix,
Mailfaker (http://www.weedem.fr/), Tyler Akins (http://rumkin.com), Caio
Ariede (http://caioariede.com), Robin, Kankrelune
(http://www.webfaktory.info/), Karol Kowalski, Imgen Tata
(http://www.myipdf.com/), mdsjack (http://www.mdsjack.bo.it), Dreamer,
Felix Geisendoerfer (http://www.debuggable.com/felix), Lars Fischer, AJ,
David, Aman Gupta, Michael White, Public Domain
(http://www.json.org/json2.js), Steven Levithan
(http://blog.stevenlevithan.com), Sakimori, Pellentesque Malesuada,
Thunder.m, Dj (http://phpjs.org/functions/htmlentities:425#comment_134018),
Steve Clay, David James, Francois, class_exists, nobbler, T. Wild, Itsacon
(http://www.itsacon.net/), date, Ole Vrijenhoek (http://www.nervous.nl/),
Fox, Raphael (Ao RUDLER), Marco, noname, Mateusz "loonquawl" Zalega, Frank
Forte, Arno, ger, mktime, john (http://www.jd-tech.net), Nick Kolosov
(http://sammy.ru), marc andreu, Scott Cariss, Douglas Crockford
(http://javascript.crockford.com), madipta, Slawomir Kaniecki,
ReverseSyntax, Nathan, Alex Wilson, kenneth, Bayron Guevara, Adam Wallner
(http://web2.bitbaro.hu/), paulo kuong, jmweb, Lincoln Ramsay, djmix,
Pyerre, Jon Hohle, Thiago Mata (http://thiagomata.blog.com), lmeyrick
(https://sourceforge.net/projects/bcmath-js/this.), Linuxworld, duncan,
Gilbert, Sanjoy Roy, Shingo, sankai, Oskar Larsson H?gfeldt
(http://oskar-lh.name/), Denny Wardhana, 0m3r, Everlasto, Subhasis Deb,
josh, jd, Pier Paolo Ramon (http://www.mastersoup.com/), P, merabi, Soren
Hansen, Eugene Bulkin (http://doubleaw.com/), Der Simon
(http://innerdom.sourceforge.net/), echo is bad, Ozh, XoraX
(http://www.xorax.info), EdorFaus, JB, J A R, Marc Jansen, Francesco, LH,
Stoyan Kyosev (http://www.svest.org/), nord_ua, omid
(http://phpjs.org/functions/380:380#comment_137122), Brad Touesnard, MeEtc
(http://yass.meetcweb.com), Peter-Paul Koch
(http://www.quirksmode.org/js/beat.html), Olivier Louvignes
(http://mg-crea.com/), T0bsn, Tim Wiel, Bryan Elliott, Jalal Berrami,
Martin, JT, David Randall, Thomas Beaucourt (http://www.webapp.fr), taith,
vlado houba, Pierre-Luc Paour, Kristof Coomans (SCK-CEN Belgian Nucleair
Research Centre), Martin Pool, Kirk Strobeck, Rick Waldron, Brant Messenger
(http://www.brantmessenger.com/), Devan Penner-Woelk, Saulo Vallory, Wagner
B. Soares, Artur Tchernychev, Valentina De Rosa, Jason Wong
(http://carrot.org/), Christoph, Daniel Esteban, strftime, Mick@el, rezna,
Simon Willison (http://simonwillison.net), Anton Ongson, Gabriel Paderni,
Marco van Oort, penutbutterjelly, Philipp Lenssen, Bjorn Roesbeke
(http://www.bjornroesbeke.be/), Bug?, Eric Nagel, Tomasz Wesolowski,
Evertjan Garretsen, Bobby Drake, Blues (http://tech.bluesmoon.info/), Luke
Godfrey, Pul, uestla, Alan C, Ulrich, Rafal Kukawski, Yves Sucaet,
sowberry, Norman "zEh" Fuchs, hitwork, Zahlii, johnrembo, Nick Callen,
Steven Levithan (stevenlevithan.com), ejsanders, Scott Baker, Brian Tafoya
(http://www.premasolutions.com/), Philippe Jausions
(http://pear.php.net/user/jausions), Aidan Lister
(http://aidanlister.com/), Rob, e-mike, HKM, ChaosNo1, metjay, strcasecmp,
strcmp, Taras Bogach, jpfle, Alexander Ermolaev
(http://snippets.dzone.com/user/AlexanderErmolaev), DxGx, kilops, Orlando,
dptr1988, Le Torbi, James (http://www.james-bell.co.uk/), Pedro Tainha
(http://www.pedrotainha.com), James, Arnout Kazemier
(http://www.3rd-Eden.com), Chris McMacken, gabriel paderni, Yannoo,
FGFEmperor, baris ozdil, Tod Gentille, Greg Frazier, jakes, 3D-GRAF, Allan
Jensen (http://www.winternet.no), Howard Yeend, Benjamin Lupton, davook,
daniel airton wermann (http://wermann.com.br), Atli T¨®r, Maximusya, Ryan
W Tenney (http://ryan.10e.us), Alexander M Beedie, fearphage
(http://http/my.opera.com/fearphage/), Nathan Sepulveda, Victor, Matteo,
Billy, stensi, Cord, Manish, T.J. Leahy, Riddler
(http://www.frontierwebdev.com/), Rafa? Kukawski, FremyCompany, Matt
Bradley, Tim de Koning, Luis Salazar (http://www.freaky-media.com/), Diogo
Resende, Rival, Andrej Pavlovic, Garagoth, Le Torbi
(http://www.letorbi.de/), Dino, Josep Sanz (http://www.ws3.es/), rem,
Russell Walker (http://www.nbill.co.uk/), Jamie Beck
(http://www.terabit.ca/), setcookie, Michael, YUI Library:
http://developer.yahoo.com/yui/docs/YAHOO.util.DateLocale.html, Blues at
http://hacks.bluesmoon.info/strftime/strftime.js, Ben
(http://benblume.co.uk/), DtTvB
(http://dt.in.th/2008-09-16.string-length-in-bytes.html), Andreas, William,
meo, incidence, Cagri Ekin, Amirouche, Amir Habibi
(http://www.residence-mixte.com/), Luke Smith (http://lucassmith.name),
Kheang Hok Chin (http://www.distantia.ca/), Jay Klehr, Lorenzo Pisani,
Tony, Yen-Wei Liu, Greenseed, mk.keck, Leslie Hoare, dude, booeyOH, Ben
Bryan

Licensed under the MIT (MIT-LICENSE.txt) license.

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be included
in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL KEVIN VAN ZONNEVELD BE LIABLE FOR ANY CLAIM, DAMAGES
OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.
*/

/**
@license
topojson - https://github.com/topojson/topojson

Copyright (c) 2012-2016, Michael Bostock
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

* Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright notice,
  this list of conditions and the following disclaimer in the documentation
  and/or other materials provided with the distribution.

* The name Michael Bostock may not be used to endorse or promote products
  derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL MICHAEL BOSTOCK BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
**/

/**
@license
tween.js - https://github.com/sole/tween.js

Copyright (c) 2010-2012 Tween.js authors.

Easing equations Copyright (c) 2001 Robert Penner http://robertpenner.com/easing/

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

//!reset,
