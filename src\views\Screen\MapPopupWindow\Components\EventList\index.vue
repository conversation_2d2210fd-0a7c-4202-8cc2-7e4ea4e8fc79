<!--
 * @Author: wangchen <EMAIL>
 * @Date: 2024-05-13 09:42:02
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-23 14:37:04
 * @FilePath: \bigscreen-qj-web\src\views\Screen\MapPopupWindow\Components\WorkList\index.vue
 * @Description:
 *
-->
<template>
  <div class="event-list-container">
    <PopupBg title="事件列表" width="800px" height="490px" @close="handlerClose">
      <tableList list-width="780px" list-data-height="360px" :head-list="eventHeadList" :list="data" @click-item="tableClick($event)"></tableList>
    </PopupBg>
  </div>
</template>

<script setup>
  import { onUnmounted, ref } from 'vue'
  import PopupBg from '@/components/PopupBg/index.vue'

  import tableList from '@/components/TableList/index.vue'
  import { closePopWindowByTag } from 'znyg-frontend-common'

  import { toUe5 } from '@/hooks/useUE/tools.js'
  const { appContext } = getCurrentInstance()

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })
  const handlerClose = () => {
    // 关闭弹窗
    closePopWindowByTag(lib.enumMap.PopWindowTag.事件列表)
  }
  const emit = defineEmits(['close'])
  const eventHeadList = ref([
    { label: '发现时间', prop: 'findTime', formatDate: 'YYYY-MM-DD' },
    { label: '发现位置', prop: 'sdetailPosition' },
    { label: '事件类型', prop: 'type' },
    { label: '事件等级', prop: 'level' },
    { label: '处置时间', prop: 'handlingTime', unit: '分钟' },
    { label: '事件状态', prop: 'status' }
  ])
  const tableClick = async (item) => {
    lib._engineController.clearSelect()
    item.isOn = !item.isOn
    // toUe5('customPOIList', {
    //   isOn: false, // 是否开启标签
    //   typeId: typeId // 设施设备类型ID（为0时指全部类型）
    // })
    lib._engineController.clearCzml()
    lib.popWindow.removeDialog('eventsWindow')
    lib.popWindow.createPopWindow(
      './Components/EventsView/index.vue',
      {
        left: 1700,
        top: 700,
        tag: 'eventsWindow',
        appContext,
        appendParent: 'player',
        zIndex: 999,
        // followPoint: { typeId: obj.typeId, id },
        closeFunc: () => {}
      },
      item
    )
    console.log('item----', item)
    // 没有里程号默认撒在路面上
    if (!item.edetailPosition) {
      item.edetailPosition = 'EK53+825'
      // item.structureType = 4
    }
    lib.api.getPointCode
      .getModeCodeFromMileage({
        mileage: item.edetailPosition,
        structureType: 4 // 4代表路面，默认查4
      })
      .then(async (res) => {
        if (res.success) {
          item.modelCode = res.result.code
          const bimInfo = await lib._engineController.getBimInfoByCode(item.modelCode)
          if (bimInfo) {
            const icon = await lib.utils.convertImageToBase64('images/eventIcon.png')
            const cartesian = bimInfo.position

            const czml = {
              id: lib.utils.getRandomString(10),
              name: lib.utils.getRandomString(5),
              billboard: {
                // 图片
                image: icon,
                scale: 0.5,
                disableDepthTestDistance: 999999,
                horizontalOrigin: 'CENTER',
                verticalOrigin: 'BOTTOM'
              },
              position: {
                cartesian: [0, 0, 0]
              },
              onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
                lib.popWindow.removeDialog('eventsWindow')
                lib.popWindow.createPopWindow(
                  './Components/EventsView/index.vue',
                  {
                    left: clickClinetX,
                    top: clickClinetY,
                    tag: 'eventsWindow',
                    appContext,
                    appendParent: 'player',
                    zIndex: 999,
                    // followPoint: { typeId: obj.typeId, id },
                    closeFunc: () => {}
                  },
                  item
                )
                lib._engineController.flyToBimId(bimInfo.bimId, -15)
              }
            }
            lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
            lib._engineController.flyToBimId(bimInfo.bimId, -15)
          }
        }
      })
    // setTimeout(() => {
    //   const obj = {
    //     isOn: item.isOn, // 是否开启标签
    //     typeId: typeId,
    //     POIList: [
    //       {
    //         code: item.modelCode, // 构件编码，,坐标定位时可以不传
    //         type: 'eventPoint', // 设施设备类型名称【※】
    //         typeId: typeId, // 设施设备类型ID【※】
    //         id: parseInt(item.id), // ID【※】
    //         poseType: 0, // 定位类型（0：构件定位，1：坐标定位） 【※】
    //         alwaysShowLabel: false, // 是否永远显示title, true:显示title(默认), false:不显示title
    //         showLabelRange: [0, 2000], // POI点显示label的范围(单位:米, 范围最小、最大距离; 在此范围内显示, 超出范围隐藏; 注意:alwaysShowLabel属性优先于此属性)
    //         animationType: '', // 动画类型((bounce:弹出式; stretch:伸缩式; wipe:展开式)，默认bounce
    //         durationTime: 0.5, // 规定完成动画所花费的时间(单位:秒)
    //         marker: {
    //           size: [64, 75], // marker大小(宽,高 单位:像素)
    //           images: {
    //             normalURL: import.meta.env.VITE_IMG_URL + `images/eventIcon.png`, // normal 图片地址
    //             // normalURL: 'https://tech.suitbim.com/bigscreen-pddd-view/static/carPopUpBg-ebb8ff43.png', // normal 图片地址
    //             activateURL: '' // hover, active 图片地址，可为空
    //           }
    //         },
    //         window: {
    //           jsonParameter: JSON.stringify(item)
    //         }
    //       }
    //     ]
    //   }
    //   toUe5('customPOIList', obj)
    //   // 视角转换到点附近
    //   toUe5('focusCustomPOI', { id: item.id, distanceOff: 0.1 })
    // }, 500)
  }
</script>

<style lang="scss" scoped>
  .event-list-container {
  }
</style>
