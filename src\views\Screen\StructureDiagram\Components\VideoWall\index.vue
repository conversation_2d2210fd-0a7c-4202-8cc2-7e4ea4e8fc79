<template>
  <div class="camera-list c-white" :style="{ width: props.width + 'px', height: props.height + 'px' }">
    <div class="camera-item" :class="{ active: activeZone.value === item.zoneId }" :data-zone-id="item.zoneId" v-for="item in list" :key="item.id">
      <VideoPlayer :code="item.code" :height="props.height / 2 - 10" :name="item.name" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import VideoPlayer from './components/videoPlayer.vue'
  const props = withDefaults(
    defineProps<{
      width: number
      height: number
      activeZone: Ref<string>
      dropData?: Ref<any>
    }>(),
    {
      width: 100,
      height: 100
    }
  )
  const list = ref([
    { id: 1, code: '', zoneId: 'camera-item-1', name: '' },
    { id: 2, code: '', zoneId: 'camera-item-2', name: '' },
    { id: 3, code: '', zoneId: 'camera-item-3', name: '' },
    { id: 4, code: '', zoneId: 'camera-item-4', name: '' }
  ])

  watch(
    () => props.dropData?.value,
    (obj) => {
      console.log('接收到的信息', obj, props.activeZone)
      if (obj?.object?.name?.includes('|')) {
        const deviceCode = obj.object.name.split('|')[0]
        list.value.forEach((item) => {
          if (item.code === deviceCode) {
            item.code = ''
            item.name = ''
          }
        })
        const item = list.value.find((item) => item.zoneId === obj?.zoneId)
        if (item) {
          item.code = deviceCode
          item.name = obj.object.name.split('|')[1]
        }
      }
    }
  )

</script>

<style scoped lang="scss">
  .camera-list {
    position: absolute;
    display: grid;
    grid-template-rows: repeat(2, 1fr);
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    .camera-item {
      @apply border-2 border-gray border-solid;
      &.active {
        @apply border-blue-500;
      }
    }
  }
</style>
