<template>
  <div class="structure-monitor-container">
    <!-- <Tabs style="margin-top: 20px" v-if="type === '结构监测'"></Tabs>
    <TabsDevice style="margin-top: 20px" v-if="type === '机电监测'"></TabsDevice>
    <RingChart :type="type"></RingChart> -->
    <div style="margin-top: 20px"></div>
    <Analysis></Analysis>
    <div style="margin-top: 20px"></div>
    <AnalysisDevice></AnalysisDevice>
  </div>
</template>

<script setup>
  import Analysis from './Components/analysis.vue'
  import AnalysisDevice from './Components/analysisDevice.vue'
  import RingChart from './Components/ringChart.vue'
  import Tabs from './Components/tabs.vue'
  import TabsDevice from './Components/tabsDevice.vue'

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    type: {
      type: String,
      default: '结构监测'
    }
  })

  onMounted(() => {
    lib.api.bigscreenApi.deviceMonitor({ type: 'device' }).then((res) => {
      console.log('结构监测')
    })
  })
</script>

<style lang="scss" scoped>
  .structure-monitor-container {
    .content {
      width: 100%;
      height: 708px;
      padding-top: 16px;
    }
  }
</style>
