define(["./when-1807bd8d","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./createTaskProcessorWorker","./PixelFormat-a042b7a2"],(function(when,RuntimeError,WebGLConstants,createTaskProcessorWorker,PixelFormat){"use strict";function CompressedTextureBuffer(e,r,i,n,t){this._format=e,this._width=r,this._height=i,this._buffer=n,this._datatype=t}var Module;Object.defineProperties(CompressedTextureBuffer.prototype,{internalFormat:{get:function(){return this._format}},width:{get:function(){return this._width}},height:{get:function(){return this._height}},bufferView:{get:function(){return this._buffer}}}),CompressedTextureBuffer.clone=function(e){if(when.defined(e))return new CompressedTextureBuffer(e._format,e._width,e._height,e._buffer)},CompressedTextureBuffer.prototype.clone=function(){return CompressedTextureBuffer.clone(this)},Module||(Module=("undefined"!==typeof Module?Module:null)||{});var moduleOverrides={};for(var key in Module)Module.hasOwnProperty(key)&&(moduleOverrides[key]=Module[key]);var ENVIRONMENT_IS_WEB=!1,ENVIRONMENT_IS_WORKER=!1,ENVIRONMENT_IS_NODE=!1,ENVIRONMENT_IS_SHELL=!1,nodeFS,nodePath;if(Module["ENVIRONMENT"])if("WEB"===Module["ENVIRONMENT"])ENVIRONMENT_IS_WEB=!0;else if("WORKER"===Module["ENVIRONMENT"])ENVIRONMENT_IS_WORKER=!0;else if("NODE"===Module["ENVIRONMENT"])ENVIRONMENT_IS_NODE=!0;else{if("SHELL"!==Module["ENVIRONMENT"])throw new Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");ENVIRONMENT_IS_SHELL=!0}else ENVIRONMENT_IS_WEB="object"===typeof window,ENVIRONMENT_IS_WORKER="function"===typeof importScripts,ENVIRONMENT_IS_NODE="object"===typeof process&&"function"===typeof require&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_NODE)Module["print"]||(Module["print"]=console.log),Module["printErr"]||(Module["printErr"]=console.warn),Module["read"]=function(e,r){nodeFS||(nodeFS=require("fs")),nodePath||(nodePath=require("path")),e=nodePath["normalize"](e);var i=nodeFS["readFileSync"](e);return r?i:i.toString()},Module["readBinary"]=function(e){var r=Module["read"](e,!0);return r.buffer||(r=new Uint8Array(r)),assert(r.buffer),r},Module["load"]=function(e){globalEval(read(e))},Module["thisProgram"]||(process["argv"].length>1?Module["thisProgram"]=process["argv"][1].replace(/\\/g,"/"):Module["thisProgram"]="unknown-program"),Module["arguments"]=process["argv"].slice(2),"undefined"!==typeof module&&(module["exports"]=Module),process["on"]("uncaughtException",(function(e){if(!(e instanceof ExitStatus))throw e})),Module["inspect"]=function(){return"[Emscripten Module object]"};else if(ENVIRONMENT_IS_SHELL)Module["print"]||(Module["print"]=print),"undefined"!=typeof printErr&&(Module["printErr"]=printErr),"undefined"!=typeof read?Module["read"]=read:Module["read"]=function(){throw"no read() available"},Module["readBinary"]=function(e){if("function"===typeof readbuffer)return new Uint8Array(readbuffer(e));var r=read(e,"binary");return assert("object"===typeof r),r},"undefined"!=typeof scriptArgs?Module["arguments"]=scriptArgs:"undefined"!=typeof arguments&&(Module["arguments"]=arguments),"function"===typeof quit&&(Module["quit"]=function(e,r){quit(e)});else{if(!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER)throw"Unknown runtime environment. Where are we?";if(Module["read"]=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},ENVIRONMENT_IS_WORKER&&(Module["readBinary"]=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),Module["readAsync"]=function(e,r,i){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):i()},n.onerror=i,n.send(null)},"undefined"!=typeof arguments&&(Module["arguments"]=arguments),"undefined"!==typeof console)Module["print"]||(Module["print"]=function(e){console.log(e)}),Module["printErr"]||(Module["printErr"]=function(e){console.warn(e)});else{var TRY_USE_DUMP=!1;Module["print"]||(Module["print"]=TRY_USE_DUMP&&"undefined"!==typeof dump?function(e){dump(e)}:function(e){})}ENVIRONMENT_IS_WORKER&&(Module["load"]=importScripts),"undefined"===typeof Module["setWindowTitle"]&&(Module["setWindowTitle"]=function(e){document.title=e})}function globalEval(e){eval.call(null,e)}for(var key in!Module["load"]&&Module["read"]&&(Module["load"]=function(e){globalEval(Module["read"](e))}),Module["print"]||(Module["print"]=function(){}),Module["printErr"]||(Module["printErr"]=Module["print"]),Module["arguments"]||(Module["arguments"]=[]),Module["thisProgram"]||(Module["thisProgram"]="./this.program"),Module["quit"]||(Module["quit"]=function(e,r){throw r}),Module.print=Module["print"],Module.printErr=Module["printErr"],Module["preRun"]=[],Module["postRun"]=[],moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(Module[key]=moduleOverrides[key]);moduleOverrides=void 0;var Runtime={setTempRet0:function(e){return tempRet0=e,e},getTempRet0:function(){return tempRet0},stackSave:function(){return STACKTOP},stackRestore:function(e){STACKTOP=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:if("*"===e[e.length-1])return Runtime.QUANTUM_SIZE;if("i"===e[0]){var r=parseInt(e.substr(1));return assert(r%8===0),r/8}return 0}},getNativeFieldSize:function(e){return Math.max(Runtime.getNativeTypeSize(e),Runtime.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return"double"===r||"i64"===r?7&e&&(assert(4===(7&e)),e+=4):assert(0===(3&e)),e},getAlignSize:function(e,r,i){return i||"i64"!=e&&"double"!=e?e?Math.min(r||(e?Runtime.getNativeFieldSize(e):0),Runtime.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,i){return i&&i.length?Module["dynCall_"+e].apply(null,[r].concat(i)):Module["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<Runtime.functionPointers.length;r++)if(!Runtime.functionPointers[r])return Runtime.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){Runtime.functionPointers[(e-2)/2]=null},warnOnce:function(e){Runtime.warnOnce.shown||(Runtime.warnOnce.shown={}),Runtime.warnOnce.shown[e]||(Runtime.warnOnce.shown[e]=1,Module.printErr(e))},funcWrappers:{},getFuncWrapper:function(e,r){assert(r),Runtime.funcWrappers[r]||(Runtime.funcWrappers[r]={});var i=Runtime.funcWrappers[r];return i[e]||(1===r.length?i[e]=function(){return Runtime.dynCall(r,e)}:2===r.length?i[e]=function(i){return Runtime.dynCall(r,e,[i])}:i[e]=function(){return Runtime.dynCall(r,e,Array.prototype.slice.call(arguments))}),i[e]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=STACKTOP;return STACKTOP=STACKTOP+e|0,STACKTOP=STACKTOP+15&-16,r},staticAlloc:function(e){var r=STATICTOP;return STATICTOP=STATICTOP+e|0,STATICTOP=STATICTOP+15&-16,r},dynamicAlloc:function(e){var r=HEAP32[DYNAMICTOP_PTR>>2],i=-16&(r+e+15|0);if(HEAP32[DYNAMICTOP_PTR>>2]=i,i>=TOTAL_MEMORY){var n=enlargeMemory();if(!n)return HEAP32[DYNAMICTOP_PTR>>2]=r,0}return r},alignMemory:function(e,r){var i=e=Math.ceil(e/(r||16))*(r||16);return i},makeBigInt:function(e,r,i){var n=i?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r);return n},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};Module["Runtime"]=Runtime;var ABORT=0,cwrap,ccall;function assert(e,r){e||abort("Assertion failed: "+r)}function getCFunc(ident){var func=Module["_"+ident];if(!func)try{func=eval("_"+ident)}catch(e){}return assert(func,"Cannot call unknown function "+ident+" (perhaps LLVM optimizations or closure removed it?)"),func}function setValue(e,r,i,n){switch(i=i||"i8","*"===i.charAt(i.length-1)&&(i="i32"),i){case"i1":HEAP8[e>>0]=r;break;case"i8":HEAP8[e>>0]=r;break;case"i16":HEAP16[e>>1]=r;break;case"i32":HEAP32[e>>2]=r;break;case"i64":tempI64=[r>>>0,(tempDouble=r,+Math_abs(tempDouble)>=1?tempDouble>0?(0|Math_min(+Math_floor(tempDouble/4294967296),4294967295))>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[e>>2]=tempI64[0],HEAP32[e+4>>2]=tempI64[1];break;case"float":HEAPF32[e>>2]=r;break;case"double":HEAPF64[e>>3]=r;break;default:abort("invalid type for setValue: "+i)}}function getValue(e,r,i){switch(r=r||"i8","*"===r.charAt(r.length-1)&&(r="i32"),r){case"i1":return HEAP8[e>>0];case"i8":return HEAP8[e>>0];case"i16":return HEAP16[e>>1];case"i32":return HEAP32[e>>2];case"i64":return HEAP32[e>>2];case"float":return HEAPF32[e>>2];case"double":return HEAPF64[e>>3];default:abort("invalid type for setValue: "+r)}return null}(function(){var JSfuncs={stackSave:function(){Runtime.stackSave()},stackRestore:function(){Runtime.stackRestore()},arrayToC:function(e){var r=Runtime.stackAlloc(e.length);return writeArrayToMemory(e,r),r},stringToC:function(e){var r=0;if(null!==e&&void 0!==e&&0!==e){var i=1+(e.length<<2);r=Runtime.stackAlloc(i),stringToUTF8(e,r,i)}return r}},toC={string:JSfuncs["stringToC"],array:JSfuncs["arrayToC"]};ccall=function(e,r,i,n,t){var a=getCFunc(e),o=[],u=0;if(n)for(var l=0;l<n.length;l++){var f=toC[i[l]];f?(0===u&&(u=Runtime.stackSave()),o[l]=f(n[l])):o[l]=n[l]}var c=a.apply(null,o);if("string"===r&&(c=Pointer_stringify(c)),0!==u){if(t&&t.async)return void EmterpreterAsync.asyncFinalizers.push((function(){Runtime.stackRestore(u)}));Runtime.stackRestore(u)}return c};var sourceRegex=/^function\s*[a-zA-Z$_0-9]*\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function parseJSFunc(e){var r=e.toString().match(sourceRegex).slice(1);return{arguments:r[0],body:r[1],returnValue:r[2]}}var JSsource=null;function ensureJSsource(){if(!JSsource)for(var e in JSsource={},JSfuncs)JSfuncs.hasOwnProperty(e)&&(JSsource[e]=parseJSFunc(JSfuncs[e]))}cwrap=function cwrap(ident,returnType,argTypes){argTypes=argTypes||[];var cfunc=getCFunc(ident),numericArgs=argTypes.every((function(e){return"number"===e})),numericRet="string"!==returnType;if(numericRet&&numericArgs)return cfunc;var argNames=argTypes.map((function(e,r){return"$"+r})),funcstr="(function("+argNames.join(",")+") {",nargs=argTypes.length;if(!numericArgs){ensureJSsource(),funcstr+="var stack = "+JSsource["stackSave"].body+";";for(var i=0;i<nargs;i++){var arg=argNames[i],type=argTypes[i];if("number"!==type){var convertCode=JSsource[type+"ToC"];funcstr+="var "+convertCode.arguments+" = "+arg+";",funcstr+=convertCode.body+";",funcstr+=arg+"=("+convertCode.returnValue+");"}}}var cfuncname=parseJSFunc((function(){return cfunc})).returnValue;if(funcstr+="var ret = "+cfuncname+"("+argNames.join(",")+");",!numericRet){var strgfy=parseJSFunc((function(){return Pointer_stringify})).returnValue;funcstr+="ret = "+strgfy+"(ret);"}return numericArgs||(ensureJSsource(),funcstr+=JSsource["stackRestore"].body.replace("()","(stack)")+";"),funcstr+="return ret})",eval(funcstr)}})(),Module["ccall"]=ccall,Module["cwrap"]=cwrap,Module["setValue"]=setValue,Module["getValue"]=getValue;var ALLOC_NORMAL=0,ALLOC_STACK=1,ALLOC_STATIC=2,ALLOC_DYNAMIC=3,ALLOC_NONE=4;function allocate(e,r,i,n){var t,a;"number"===typeof e?(t=!0,a=e):(t=!1,a=e.length);var o,u="string"===typeof r?r:null;if(o=i==ALLOC_NONE?n:["function"===typeof _malloc?_malloc:Runtime.staticAlloc,Runtime.stackAlloc,Runtime.staticAlloc,Runtime.dynamicAlloc][void 0===i?ALLOC_STATIC:i](Math.max(a,u?1:r.length)),t){var l;n=o;for(assert(0==(3&o)),l=o+(-4&a);n<l;n+=4)HEAP32[n>>2]=0;l=o+a;while(n<l)HEAP8[n++>>0]=0;return o}if("i8"===u)return e.subarray||e.slice?HEAPU8.set(e,o):HEAPU8.set(new Uint8Array(e),o),o;var f,c,s,_=0;while(_<a){var d=e[_];"function"===typeof d&&(d=Runtime.getFunctionIndex(d)),f=u||r[_],0!==f?("i64"==f&&(f="i32"),setValue(o+_,d,f),s!==f&&(c=Runtime.getNativeTypeSize(f),s=f),_+=c):_++}return o}function getMemory(e){return staticSealed?runtimeInitialized?_malloc(e):Runtime.dynamicAlloc(e):Runtime.staticAlloc(e)}function Pointer_stringify(e,r){if(0===r||!e)return"";var i,n=0,t=0;while(1){if(i=HEAPU8[e+t>>0],n|=i,0==i&&!r)break;if(t++,r&&t==r)break}r||(r=t);var a="";if(n<128){var o,u=1024;while(r>0)o=String.fromCharCode.apply(String,HEAPU8.subarray(e,e+Math.min(r,u))),a=a?a+o:o,e+=u,r-=u;return a}return Module["UTF8ToString"](e)}function AsciiToString(e){var r="";while(1){var i=HEAP8[e++>>0];if(!i)return r;r+=String.fromCharCode(i)}}function stringToAscii(e,r){return writeAsciiToMemory(e,r,!1)}Module["ALLOC_NORMAL"]=ALLOC_NORMAL,Module["ALLOC_STACK"]=ALLOC_STACK,Module["ALLOC_STATIC"]=ALLOC_STATIC,Module["ALLOC_DYNAMIC"]=ALLOC_DYNAMIC,Module["ALLOC_NONE"]=ALLOC_NONE,Module["allocate"]=allocate,Module["getMemory"]=getMemory,Module["Pointer_stringify"]=Pointer_stringify,Module["AsciiToString"]=AsciiToString,Module["stringToAscii"]=stringToAscii;var UTF8Decoder="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;function UTF8ArrayToString(e,r){var i=r;while(e[i])++i;if(i-r>16&&e.subarray&&UTF8Decoder)return UTF8Decoder.decode(e.subarray(r,i));var n,t,a,o,u,l,f="";while(1){if(n=e[r++],!n)return f;if(128&n)if(t=63&e[r++],192!=(224&n))if(a=63&e[r++],224==(240&n)?n=(15&n)<<12|t<<6|a:(o=63&e[r++],240==(248&n)?n=(7&n)<<18|t<<12|a<<6|o:(u=63&e[r++],248==(252&n)?n=(3&n)<<24|t<<18|a<<12|o<<6|u:(l=63&e[r++],n=(1&n)<<30|t<<24|a<<18|o<<12|u<<6|l))),n<65536)f+=String.fromCharCode(n);else{var c=n-65536;f+=String.fromCharCode(55296|c>>10,56320|1023&c)}else f+=String.fromCharCode((31&n)<<6|t);else f+=String.fromCharCode(n)}}function UTF8ToString(e){return UTF8ArrayToString(HEAPU8,e)}function stringToUTF8Array(e,r,i,n){if(!(n>0))return 0;for(var t=i,a=i+n-1,o=0;o<e.length;++o){var u=e.charCodeAt(o);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++o)),u<=127){if(i>=a)break;r[i++]=u}else if(u<=2047){if(i+1>=a)break;r[i++]=192|u>>6,r[i++]=128|63&u}else if(u<=65535){if(i+2>=a)break;r[i++]=224|u>>12,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=2097151){if(i+3>=a)break;r[i++]=240|u>>18,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else if(u<=67108863){if(i+4>=a)break;r[i++]=248|u>>24,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}else{if(i+5>=a)break;r[i++]=252|u>>30,r[i++]=128|u>>24&63,r[i++]=128|u>>18&63,r[i++]=128|u>>12&63,r[i++]=128|u>>6&63,r[i++]=128|63&u}}return r[i]=0,i-t}function stringToUTF8(e,r,i){return stringToUTF8Array(e,HEAPU8,r,i)}function lengthBytesUTF8(e){for(var r=0,i=0;i<e.length;++i){var n=e.charCodeAt(i);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++i)),n<=127?++r:r+=n<=2047?2:n<=65535?3:n<=2097151?4:n<=67108863?5:6}return r}Module["UTF8ArrayToString"]=UTF8ArrayToString,Module["UTF8ToString"]=UTF8ToString,Module["stringToUTF8Array"]=stringToUTF8Array,Module["stringToUTF8"]=stringToUTF8,Module["lengthBytesUTF8"]=lengthBytesUTF8;var UTF16Decoder="undefined"!==typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function demangle(e){var r=Module["___cxa_demangle"]||Module["__cxa_demangle"];if(r){try{var i=e.substr(1),n=lengthBytesUTF8(i)+1,t=_malloc(n);stringToUTF8(i,t,n);var a=_malloc(4),o=r(t,0,0,a);if(0===getValue(a,"i32")&&o)return Pointer_stringify(o)}catch(u){}finally{t&&_free(t),a&&_free(a),o&&_free(o)}return e}return Runtime.warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function demangleAll(e){var r=/__Z[\w\d_]+/g;return e.replace(r,(function(e){var r=demangle(e);return e===r?e:e+" ["+r+"]"}))}function jsStackTrace(){var e=new Error;if(!e.stack){try{throw new Error(0)}catch(r){e=r}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}function stackTrace(){var e=jsStackTrace();return Module["extraStackTrace"]&&(e+="\n"+Module["extraStackTrace"]()),demangleAll(e)}Module["stackTrace"]=stackTrace;var WASM_PAGE_SIZE=65536,ASMJS_PAGE_SIZE=16777216,MIN_TOTAL_MEMORY=16777216,HEAP,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64,STATIC_BASE,STATICTOP,staticSealed,STACK_BASE,STACKTOP,STACK_MAX,DYNAMIC_BASE,DYNAMICTOP_PTR,byteLength;function alignUp(e,r){return e%r>0&&(e+=r-e%r),e}function updateGlobalBuffer(e){Module["buffer"]=buffer=e}function updateGlobalBufferViews(){Module["HEAP8"]=HEAP8=new Int8Array(buffer),Module["HEAP16"]=HEAP16=new Int16Array(buffer),Module["HEAP32"]=HEAP32=new Int32Array(buffer),Module["HEAPU8"]=HEAPU8=new Uint8Array(buffer),Module["HEAPU16"]=HEAPU16=new Uint16Array(buffer),Module["HEAPU32"]=HEAPU32=new Uint32Array(buffer),Module["HEAPF32"]=HEAPF32=new Float32Array(buffer),Module["HEAPF64"]=HEAPF64=new Float64Array(buffer)}function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or (4) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function enlargeMemory(){var e=Module["usingWasm"]?WASM_PAGE_SIZE:ASMJS_PAGE_SIZE,r=2147483648-e;if(HEAP32[DYNAMICTOP_PTR>>2]>r)return!1;var i=TOTAL_MEMORY;TOTAL_MEMORY=Math.max(TOTAL_MEMORY,MIN_TOTAL_MEMORY);while(TOTAL_MEMORY<HEAP32[DYNAMICTOP_PTR>>2])TOTAL_MEMORY=TOTAL_MEMORY<=536870912?alignUp(2*TOTAL_MEMORY,e):Math.min(alignUp((3*TOTAL_MEMORY+2147483648)/4,e),r);var n=Module["reallocBuffer"](TOTAL_MEMORY);return n&&n.byteLength==TOTAL_MEMORY?(updateGlobalBuffer(n),updateGlobalBufferViews(),!0):(TOTAL_MEMORY=i,!1)}STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0,staticSealed=!1,Module["reallocBuffer"]||(Module["reallocBuffer"]=function(e){var r;try{if(ArrayBuffer.transfer)r=ArrayBuffer.transfer(buffer,e);else{var i=HEAP8;r=new ArrayBuffer(e);var n=new Int8Array(r);n.set(i)}}catch(a){return!1}var t=_emscripten_replace_memory(r);return!!t&&r});try{byteLength=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get),byteLength(new ArrayBuffer(4))}catch(e){byteLength=function(e){return e.byteLength}}var TOTAL_STACK=Module["TOTAL_STACK"]||5242880,TOTAL_MEMORY=Module["TOTAL_MEMORY"]||16777216;function getTotalMemory(){return TOTAL_MEMORY}if(TOTAL_MEMORY<TOTAL_STACK&&Module.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")"),buffer=Module["buffer"]?Module["buffer"]:new ArrayBuffer(TOTAL_MEMORY),updateGlobalBufferViews(),HEAP32[0]=1668509029,HEAP16[1]=25459,115!==HEAPU8[2]||99!==HEAPU8[3])throw"Runtime error: expected the system to be little-endian!";function callRuntimeCallbacks(e){while(e.length>0){var r=e.shift();if("function"!=typeof r){var i=r.func;"number"===typeof i?void 0===r.arg?Module["dynCall_v"](i):Module["dynCall_vi"](i,r.arg):i(void 0===r.arg?null:r.arg)}else r()}}Module["HEAP"]=HEAP,Module["buffer"]=buffer,Module["HEAP8"]=HEAP8,Module["HEAP16"]=HEAP16,Module["HEAP32"]=HEAP32,Module["HEAPU8"]=HEAPU8,Module["HEAPU16"]=HEAPU16,Module["HEAPU32"]=HEAPU32,Module["HEAPF32"]=HEAPF32,Module["HEAPF64"]=HEAPF64;var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1;function preRun(){if(Module["preRun"]){"function"==typeof Module["preRun"]&&(Module["preRun"]=[Module["preRun"]]);while(Module["preRun"].length)addOnPreRun(Module["preRun"].shift())}callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){runtimeInitialized||(runtimeInitialized=!0,callRuntimeCallbacks(__ATINIT__))}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__)}function postRun(){if(Module["postRun"]){"function"==typeof Module["postRun"]&&(Module["postRun"]=[Module["postRun"]]);while(Module["postRun"].length)addOnPostRun(Module["postRun"].shift())}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnPreMain(e){__ATMAIN__.unshift(e)}function addOnExit(e){__ATEXIT__.unshift(e)}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}function intArrayFromString(e,r,i){var n=i>0?i:lengthBytesUTF8(e)+1,t=new Array(n),a=stringToUTF8Array(e,t,0,t.length);return r&&(t.length=a),t}function intArrayToString(e){for(var r=[],i=0;i<e.length;i++){var n=e[i];n>255&&(n&=255),r.push(String.fromCharCode(n))}return r.join("")}function writeStringToMemory(e,r,i){var n,t;Runtime.warnOnce("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!"),i&&(t=r+lengthBytesUTF8(e),n=HEAP8[t]),stringToUTF8(e,r,1/0),i&&(HEAP8[t]=n)}function writeArrayToMemory(e,r){HEAP8.set(e,r)}function writeAsciiToMemory(e,r,i){for(var n=0;n<e.length;++n)HEAP8[r++>>0]=e.charCodeAt(n);i||(HEAP8[r>>0]=0)}Module["addOnPreRun"]=addOnPreRun,Module["addOnInit"]=addOnInit,Module["addOnPreMain"]=addOnPreMain,Module["addOnExit"]=addOnExit,Module["addOnPostRun"]=addOnPostRun,Module["intArrayFromString"]=intArrayFromString,Module["intArrayToString"]=intArrayToString,Module["writeStringToMemory"]=writeStringToMemory,Module["writeArrayToMemory"]=writeArrayToMemory,Module["writeAsciiToMemory"]=writeAsciiToMemory,Math["imul"]&&-5===Math["imul"](4294967295,5)||(Math["imul"]=function(e,r){var i=e>>>16,n=65535&e,t=r>>>16,a=65535&r;return n*a+(i*a+n*t<<16)|0}),Math.imul=Math["imul"],Math["clz32"]||(Math["clz32"]=function(e){e>>>=0;for(var r=0;r<32;r++)if(e&1<<31-r)return r;return 32}),Math.clz32=Math["clz32"],Math["trunc"]||(Math["trunc"]=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Math.trunc=Math["trunc"];var Math_abs=Math.abs,Math_ceil=Math.ceil,Math_floor=Math.floor,Math_min=Math.min,runDependencies=0,dependenciesFulfilled=null;function addRunDependency(e){runDependencies++,Module["monitorRunDependencies"]&&Module["monitorRunDependencies"](runDependencies)}function removeRunDependency(e){if(runDependencies--,Module["monitorRunDependencies"]&&Module["monitorRunDependencies"](runDependencies),0==runDependencies&&dependenciesFulfilled){var r=dependenciesFulfilled;dependenciesFulfilled=null,r()}}Module["addRunDependency"]=addRunDependency,Module["removeRunDependency"]=removeRunDependency,Module["preloadedImages"]={},Module["preloadedAudios"]={},STATIC_BASE=Runtime.GLOBAL_BASE,STATICTOP=STATIC_BASE+6192,__ATINIT__.push(),allocate([228,2,0,0,81,16,0,0,12,3,0,0,177,16,0,0,32,0,0,0,0,0,0,0,12,3,0,0,94,16,0,0,48,0,0,0,0,0,0,0,228,2,0,0,127,16,0,0,12,3,0,0,140,16,0,0,16,0,0,0,0,0,0,0,12,3,0,0,183,17,0,0,32,0,0,0,0,0,0,0,12,3,0,0,147,17,0,0,72,0,0,0,0,0,0,0,108,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,32,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,248,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,224,1,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,40,20,0,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,10,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,16,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,56,0,0,0,1,0,0,0,5,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,2,0,0,0,2,0,0,0,2,0,0,0,37,115,40,37,117,41,58,32,65,115,115,101,114,116,105,111,110,32,102,97,105,108,117,114,101,58,32,34,37,115,34,10,0,109,95,115,105,122,101,32,60,61,32,109,95,99,97,112,97,99,105,116,121,0,46,47,105,110,99,92,99,114,110,95,100,101,99,111,109,112,46,104,0,109,105,110,95,110,101,119,95,99,97,112,97,99,105,116,121,32,60,32,40,48,120,55,70,70,70,48,48,48,48,85,32,47,32,101,108,101,109,101,110,116,95,115,105,122,101,41,0,110,101,119,95,99,97,112,97,99,105,116,121,32,38,38,32,40,110,101,119,95,99,97,112,97,99,105,116,121,32,62,32,109,95,99,97,112,97,99,105,116,121,41,0,110,117,109,95,99,111,100,101,115,91,99,93,0,115,111,114,116,101,100,95,112,111,115,32,60,32,116,111,116,97,108,95,117,115,101,100,95,115,121,109,115,0,112,67,111,100,101,115,105,122,101,115,91,115,121,109,95,105,110,100,101,120,93,32,61,61,32,99,111,100,101,115,105,122,101,0,116,32,60,32,40,49,85,32,60,60,32,116,97,98,108,101,95,98,105,116,115,41,0,109,95,108,111,111,107,117,112,91,116,93,32,61,61,32,99,85,73,78,84,51,50,95,77,65,88,0,99,114,110,100,95,109,97,108,108,111,99,58,32,115,105,122,101,32,116,111,111,32,98,105,103,0,99,114,110,100,95,109,97,108,108,111,99,58,32,111,117,116,32,111,102,32,109,101,109,111,114,121,0,40,40,117,105,110,116,51,50,41,112,95,110,101,119,32,38,32,40,67,82,78,68,95,77,73,78,95,65,76,76,79,67,95,65,76,73,71,78,77,69,78,84,32,45,32,49,41,41,32,61,61,32,48,0,99,114,110,100,95,114,101,97,108,108,111,99,58,32,98,97,100,32,112,116,114,0,99,114,110,100,95,102,114,101,101,58,32,98,97,100,32,112,116,114,0,102,97,108,115,101,0,40,116,111,116,97,108,95,115,121,109,115,32,62,61,32,49,41,32,38,38,32,40,116,111,116,97,108,95,115,121,109,115,32,60,61,32,112,114,101,102,105,120,95,99,111,100,105,110,103,58,58,99,77,97,120,83,117,112,112,111,114,116,101,100,83,121,109,115,41,0,17,18,19,20,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15,16,48,0,110,117,109,95,98,105,116,115,32,60,61,32,51,50,85,0,109,95,98,105,116,95,99,111,117,110,116,32,60,61,32,99,66,105,116,66,117,102,83,105,122,101,0,116,32,33,61,32,99,85,73,78,84,51,50,95,77,65,88,0,109,111,100,101,108,46,109,95,99,111,100,101,95,115,105,122,101,115,91,115,121,109,93,32,61,61,32,108,101,110,0,0,2,3,1,0,2,3,4,5,6,7,1,40,108,101,110,32,62,61,32,49,41,32,38,38,32,40,108,101,110,32,60,61,32,99,77,97,120,69,120,112,101,99,116,101,100,67,111,100,101,83,105,122,101,41,0,105,32,60,32,109,95,115,105,122,101,0,110,101,120,116,95,108,101,118,101,108,95,111,102,115,32,62,32,99,117,114,95,108,101,118,101,108,95,111,102,115,0,1,2,2,3,3,3,3,4,0,0,0,0,0,0,1,1,0,1,0,1,0,0,1,2,1,2,0,0,0,1,0,2,1,0,2,0,0,1,2,3,110,117,109,32,38,38,32,40,110,117,109,32,61,61,32,126,110,117,109,95,99,104,101,99,107,41,0,17,0,10,0,17,17,17,0,0,0,0,5,0,0,0,0,0,0,9,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,15,10,17,17,17,3,10,7,0,1,19,9,11,11,0,0,9,6,11,0,0,11,0,6,17,0,0,0,17,17,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,10,10,17,17,17,0,10,0,0,2,0,9,11,0,0,0,9,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,13,0,0,0,0,9,14,0,0,0,0,0,14,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,0,15,0,0,0,0,9,16,0,0,0,0,0,16,0,0,16,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,10,0,0,0,0,9,11,0,0,0,0,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,45,43,32,32,32,48,88,48,120,0,40,110,117,108,108,41,0,45,48,88,43,48,88,32,48,88,45,48,120,43,48,120,32,48,120,0,105,110,102,0,73,78,70,0,110,97,110,0,78,65,78,0,48,49,50,51,52,53,54,55,56,57,65,66,67,68,69,70,46,0,84,33,34,25,13,1,2,3,17,75,28,12,16,4,11,29,18,30,39,104,110,111,112,113,98,32,5,6,15,19,20,21,26,8,22,7,40,36,23,24,9,10,14,27,31,37,35,131,130,125,38,42,43,60,61,62,63,67,71,74,77,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,105,106,107,108,114,115,116,121,122,123,124,0,73,108,108,101,103,97,108,32,98,121,116,101,32,115,101,113,117,101,110,99,101,0,68,111,109,97,105,110,32,101,114,114,111,114,0,82,101,115,117,108,116,32,110,111,116,32,114,101,112,114,101,115,101,110,116,97,98,108,101,0,78,111,116,32,97,32,116,116,121,0,80,101,114,109,105,115,115,105,111,110,32,100,101,110,105,101,100,0,79,112,101,114,97,116,105,111,110,32,110,111,116,32,112,101,114,109,105,116,116,101,100,0,78,111,32,115,117,99,104,32,102,105,108,101,32,111,114,32,100,105,114,101,99,116,111,114,121,0,78,111,32,115,117,99,104,32,112,114,111,99,101,115,115,0,70,105,108,101,32,101,120,105,115,116,115,0,86,97,108,117,101,32,116,111,111,32,108,97,114,103,101,32,102,111,114,32,100,97,116,97,32,116,121,112,101,0,78,111,32,115,112,97,99,101,32,108,101,102,116,32,111,110,32,100,101,118,105,99,101,0,79,117,116,32,111,102,32,109,101,109,111,114,121,0,82,101,115,111,117,114,99,101,32,98,117,115,121,0,73,110,116,101,114,114,117,112,116,101,100,32,115,121,115,116,101,109,32,99,97,108,108,0,82,101,115,111,117,114,99,101,32,116,101,109,112,111,114,97,114,105,108,121,32,117,110,97,118,97,105,108,97,98,108,101,0,73,110,118,97,108,105,100,32,115,101,101,107,0,67,114,111,115,115,45,100,101,118,105,99,101,32,108,105,110,107,0,82,101,97,100,45,111,110,108,121,32,102,105,108,101,32,115,121,115,116,101,109,0,68,105,114,101,99,116,111,114,121,32,110,111,116,32,101,109,112,116,121,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,112,101,101,114,0,79,112,101,114,97,116,105,111,110,32,116,105,109,101,100,32,111,117,116,0,67,111,110,110,101,99,116,105,111,110,32,114,101,102,117,115,101,100,0,72,111,115,116,32,105,115,32,100,111,119,110,0,72,111,115,116,32,105,115,32,117,110,114,101,97,99,104,97,98,108,101,0,65,100,100,114,101,115,115,32,105,110,32,117,115,101,0,66,114,111,107,101,110,32,112,105,112,101,0,73,47,79,32,101,114,114,111,114,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,32,111,114,32,97,100,100,114,101,115,115,0,66,108,111,99,107,32,100,101,118,105,99,101,32,114,101,113,117,105,114,101,100,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,0,78,111,116,32,97,32,100,105,114,101,99,116,111,114,121,0,73,115,32,97,32,100,105,114,101,99,116,111,114,121,0,84,101,120,116,32,102,105,108,101,32,98,117,115,121,0,69,120,101,99,32,102,111,114,109,97,116,32,101,114,114,111,114,0,73,110,118,97,108,105,100,32,97,114,103,117,109,101,110,116,0,65,114,103,117,109,101,110,116,32,108,105,115,116,32,116,111,111,32,108,111,110,103,0,83,121,109,98,111,108,105,99,32,108,105,110,107,32,108,111,111,112,0,70,105,108,101,110,97,109,101,32,116,111,111,32,108,111,110,103,0,84,111,111,32,109,97,110,121,32,111,112,101,110,32,102,105,108,101,115,32,105,110,32,115,121,115,116,101,109,0,78,111,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,115,32,97,118,97,105,108,97,98,108,101,0,66,97,100,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,0,78,111,32,99,104,105,108,100,32,112,114,111,99,101,115,115,0,66,97,100,32,97,100,100,114,101,115,115,0,70,105,108,101,32,116,111,111,32,108,97,114,103,101,0,84,111,111,32,109,97,110,121,32,108,105,110,107,115,0,78,111,32,108,111,99,107,115,32,97,118,97,105,108,97,98,108,101,0,82,101,115,111,117,114,99,101,32,100,101,97,100,108,111,99,107,32,119,111,117,108,100,32,111,99,99,117,114,0,83,116,97,116,101,32,110,111,116,32,114,101,99,111,118,101,114,97,98,108,101,0,80,114,101,118,105,111,117,115,32,111,119,110,101,114,32,100,105,101,100,0,79,112,101,114,97,116,105,111,110,32,99,97,110,99,101,108,101,100,0,70,117,110,99,116,105,111,110,32,110,111,116,32,105,109,112,108,101,109,101,110,116,101,100,0,78,111,32,109,101,115,115,97,103,101,32,111,102,32,100,101,115,105,114,101,100,32,116,121,112,101,0,73,100,101,110,116,105,102,105,101,114,32,114,101,109,111,118,101,100,0,68,101,118,105,99,101,32,110,111,116,32,97,32,115,116,114,101,97,109,0,78,111,32,100,97,116,97,32,97,118,97,105,108,97,98,108,101,0,68,101,118,105,99,101,32,116,105,109,101,111,117,116,0,79,117,116,32,111,102,32,115,116,114,101,97,109,115,32,114,101,115,111,117,114,99,101,115,0,76,105,110,107,32,104,97,115,32,98,101,101,110,32,115,101,118,101,114,101,100,0,80,114,111,116,111,99,111,108,32,101,114,114,111,114,0,66,97,100,32,109,101,115,115,97,103,101,0,70,105,108,101,32,100,101,115,99,114,105,112,116,111,114,32,105,110,32,98,97,100,32,115,116,97,116,101,0,78,111,116,32,97,32,115,111,99,107,101,116,0,68,101,115,116,105,110,97,116,105,111,110,32,97,100,100,114,101,115,115,32,114,101,113,117,105,114,101,100,0,77,101,115,115,97,103,101,32,116,111,111,32,108,97,114,103,101,0,80,114,111,116,111,99,111,108,32,119,114,111,110,103,32,116,121,112,101,32,102,111,114,32,115,111,99,107,101,116,0,80,114,111,116,111,99,111,108,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,80,114,111,116,111,99,111,108,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,83,111,99,107,101,116,32,116,121,112,101,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,78,111,116,32,115,117,112,112,111,114,116,101,100,0,80,114,111,116,111,99,111,108,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,65,100,100,114,101,115,115,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,32,98,121,32,112,114,111,116,111,99,111,108,0,65,100,100,114,101,115,115,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,78,101,116,119,111,114,107,32,105,115,32,100,111,119,110,0,78,101,116,119,111,114,107,32,117,110,114,101,97,99,104,97,98,108,101,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,110,101,116,119,111,114,107,0,67,111,110,110,101,99,116,105,111,110,32,97,98,111,114,116,101,100,0,78,111,32,98,117,102,102,101,114,32,115,112,97,99,101,32,97,118,97,105,108,97,98,108,101,0,83,111,99,107,101,116,32,105,115,32,99,111,110,110,101,99,116,101,100,0,83,111,99,107,101,116,32,110,111,116,32,99,111,110,110,101,99,116,101,100,0,67,97,110,110,111,116,32,115,101,110,100,32,97,102,116,101,114,32,115,111,99,107,101,116,32,115,104,117,116,100,111,119,110,0,79,112,101,114,97,116,105,111,110,32,97,108,114,101,97,100,121,32,105,110,32,112,114,111,103,114,101,115,115,0,79,112,101,114,97,116,105,111,110,32,105,110,32,112,114,111,103,114,101,115,115,0,83,116,97,108,101,32,102,105,108,101,32,104,97,110,100,108,101,0,82,101,109,111,116,101,32,73,47,79,32,101,114,114,111,114,0,81,117,111,116,97,32,101,120,99,101,101,100,101,100,0,78,111,32,109,101,100,105,117,109,32,102,111,117,110,100,0,87,114,111,110,103,32,109,101,100,105,117,109,32,116,121,112,101,0,78,111,32,101,114,114,111,114,32,105,110,102,111,114,109,97,116,105,111,110,0,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,58,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,101,120,99,101,112,116,105,111,110,32,111,102,32,116,121,112,101,32,37,115,0,116,101,114,109,105,110,97,116,105,110,103,32,119,105,116,104,32,37,115,32,102,111,114,101,105,103,110,32,101,120,99,101,112,116,105,111,110,0,116,101,114,109,105,110,97,116,105,110,103,0,117,110,99,97,117,103,104,116,0,83,116,57,101,120,99,101,112,116,105,111,110,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,54,95,95,115,104,105,109,95,116,121,112,101,95,105,110,102,111,69,0,83,116,57,116,121,112,101,95,105,110,102,111,0,78,49,48,95,95,99,120,120,97,98,105,118,49,50,48,95,95,115,105,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,99,108,97,115,115,95,116,121,112,101,95,105,110,102,111,69,0,112,116,104,114,101,97,100,95,111,110,99,101,32,102,97,105,108,117,114,101,32,105,110,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,95,102,97,115,116,40,41,0,99,97,110,110,111,116,32,99,114,101,97,116,101,32,112,116,104,114,101,97,100,32,107,101,121,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,99,97,110,110,111,116,32,122,101,114,111,32,111,117,116,32,116,104,114,101,97,100,32,118,97,108,117,101,32,102,111,114,32,95,95,99,120,97,95,103,101,116,95,103,108,111,98,97,108,115,40,41,0,116,101,114,109,105,110,97,116,101,95,104,97,110,100,108,101,114,32,117,110,101,120,112,101,99,116,101,100,108,121,32,114,101,116,117,114,110,101,100,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,57,95,95,112,111,105,110,116,101,114,95,116,121,112,101,95,105,110,102,111,69,0,78,49,48,95,95,99,120,120,97,98,105,118,49,49,55,95,95,112,98,97,115,101,95,116,121,112,101,95,105,110,102,111,69,0],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE);var tempDoublePtr=STATICTOP;function _abort(){Module["abort"]()}function __ZSt18uncaught_exceptionv(){return!!__ZSt18uncaught_exceptionv.uncaught_exception}STATICTOP+=16;var EXCEPTIONS={last:0,caught:[],infos:{},deAdjust:function(e){if(!e||EXCEPTIONS.infos[e])return e;for(var r in EXCEPTIONS.infos){var i=EXCEPTIONS.infos[r];if(i.adjusted===e)return r}return e},addRef:function(e){if(e){var r=EXCEPTIONS.infos[e];r.refcount++}},decRef:function(e){if(e){var r=EXCEPTIONS.infos[e];assert(r.refcount>0),r.refcount--,0!==r.refcount||r.rethrown||(r.destructor&&Module["dynCall_vi"](r.destructor,e),delete EXCEPTIONS.infos[e],___cxa_free_exception(e))}},clearRef:function(e){if(e){var r=EXCEPTIONS.infos[e];r.refcount=0}}};function ___cxa_begin_catch(e){var r=EXCEPTIONS.infos[e];return r&&!r.caught&&(r.caught=!0,__ZSt18uncaught_exceptionv.uncaught_exception--),r&&(r.rethrown=!1),EXCEPTIONS.caught.push(e),EXCEPTIONS.addRef(EXCEPTIONS.deAdjust(e)),e}function _pthread_once(e,r){_pthread_once.seen||(_pthread_once.seen={}),e in _pthread_once.seen||(Module["dynCall_v"](r),_pthread_once.seen[e]=1)}function _emscripten_memcpy_big(e,r,i){return HEAPU8.set(HEAPU8.subarray(r,r+i),e),e}var SYSCALLS={varargs:0,get:function(e){SYSCALLS.varargs+=4;var r=HEAP32[SYSCALLS.varargs-4>>2];return r},getStr:function(){var e=Pointer_stringify(SYSCALLS.get());return e},get64:function(){var e=SYSCALLS.get(),r=SYSCALLS.get();return assert(e>=0?0===r:-1===r),e},getZero:function(){assert(0===SYSCALLS.get())}};function ___syscall6(r,i){SYSCALLS.varargs=i;try{var n=SYSCALLS.getStreamFromFD();return FS.close(n),0}catch(e){return"undefined"!==typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}var cttz_i8=allocate([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0],"i8",ALLOC_STATIC),PTHREAD_SPECIFIC={};function _pthread_getspecific(e){return PTHREAD_SPECIFIC[e]||0}function ___setErrNo(e){return Module["___errno_location"]&&(HEAP32[Module["___errno_location"]()>>2]=e),e}var PTHREAD_SPECIFIC_NEXT_KEY=1,ERRNO_CODES={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function _pthread_key_create(e,r){return 0==e?ERRNO_CODES.EINVAL:(HEAP32[e>>2]=PTHREAD_SPECIFIC_NEXT_KEY,PTHREAD_SPECIFIC[PTHREAD_SPECIFIC_NEXT_KEY]=0,PTHREAD_SPECIFIC_NEXT_KEY++,0)}function ___resumeException(e){throw EXCEPTIONS.last||(EXCEPTIONS.last=e),e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."}function ___cxa_find_matching_catch(){var e=EXCEPTIONS.last;if(!e)return 0|(Runtime.setTempRet0(0),0);var r=EXCEPTIONS.infos[e],i=r.type;if(!i)return 0|(Runtime.setTempRet0(0),e);var n=Array.prototype.slice.call(arguments);Module["___cxa_is_pointer_type"](i);___cxa_find_matching_catch.buffer||(___cxa_find_matching_catch.buffer=_malloc(4)),HEAP32[___cxa_find_matching_catch.buffer>>2]=e,e=___cxa_find_matching_catch.buffer;for(var t=0;t<n.length;t++)if(n[t]&&Module["___cxa_can_catch"](n[t],i,e))return e=HEAP32[e>>2],r.adjusted=e,0|(Runtime.setTempRet0(n[t]),e);return e=HEAP32[e>>2],0|(Runtime.setTempRet0(i),e)}function ___gxx_personality_v0(){}function _pthread_setspecific(e,r){return e in PTHREAD_SPECIFIC?(PTHREAD_SPECIFIC[e]=r,0):ERRNO_CODES.EINVAL}function ___syscall140(r,i){SYSCALLS.varargs=i;try{var n=SYSCALLS.getStreamFromFD(),t=(SYSCALLS.get(),SYSCALLS.get()),a=SYSCALLS.get(),o=SYSCALLS.get(),u=t;return FS.llseek(n,u,o),HEAP32[a>>2]=n.position,n.getdents&&0===u&&0===o&&(n.getdents=null),0}catch(e){return"undefined"!==typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall146(r,i){SYSCALLS.varargs=i;try{var n=SYSCALLS.get(),t=SYSCALLS.get(),a=SYSCALLS.get(),o=0;___syscall146.buffer||(___syscall146.buffers=[null,[],[]],___syscall146.printChar=function(e,r){var i=___syscall146.buffers[e];assert(i),0===r||10===r?((1===e?Module["print"]:Module["printErr"])(UTF8ArrayToString(i,0)),i.length=0):i.push(r)});for(var u=0;u<a;u++){for(var l=HEAP32[t+8*u>>2],f=HEAP32[t+(8*u+4)>>2],c=0;c<f;c++)___syscall146.printChar(n,HEAPU8[l+c]);o+=f}return o}catch(e){return"undefined"!==typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function ___syscall54(r,i){SYSCALLS.varargs=i;try{return 0}catch(e){return"undefined"!==typeof FS&&e instanceof FS.ErrnoError||abort(e),-e.errno}}function invoke_iiii(r,i,n,t){try{return Module["dynCall_iiii"](r,i,n,t)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_viiiii(r,i,n,t,a,o){try{Module["dynCall_viiiii"](r,i,n,t,a,o)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_vi(r,i){try{Module["dynCall_vi"](r,i)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_ii(r,i){try{return Module["dynCall_ii"](r,i)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_viii(r,i,n,t){try{Module["dynCall_viii"](r,i,n,t)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_v(r){try{Module["dynCall_v"](r)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_viiiiii(r,i,n,t,a,o,u){try{Module["dynCall_viiiiii"](r,i,n,t,a,o,u)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}function invoke_viiii(r,i,n,t,a){try{Module["dynCall_viiii"](r,i,n,t,a)}catch(e){if("number"!==typeof e&&"longjmp"!==e)throw e;Module["setThrew"](1,0)}}__ATEXIT__.push((function(){var e=Module["_fflush"];e&&e(0);var r=___syscall146.printChar;if(r){var i=___syscall146.buffers;i[1].length&&r(1,10),i[2].length&&r(2,10)}})),DYNAMICTOP_PTR=allocate(1,"i32",ALLOC_STATIC),STACK_BASE=STACKTOP=Runtime.alignMemory(STATICTOP),STACK_MAX=STACK_BASE+TOTAL_STACK,DYNAMIC_BASE=Runtime.alignMemory(STACK_MAX),HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE,staticSealed=!0,Module.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0,byteLength:byteLength},Module.asmLibraryArg={abort:abort,assert:assert,enlargeMemory:enlargeMemory,getTotalMemory:getTotalMemory,abortOnCannotGrowMemory:abortOnCannotGrowMemory,invoke_iiii:invoke_iiii,invoke_viiiii:invoke_viiiii,invoke_vi:invoke_vi,invoke_ii:invoke_ii,invoke_viii:invoke_viii,invoke_v:invoke_v,invoke_viiiiii:invoke_viiiiii,invoke_viiii:invoke_viiii,_pthread_getspecific:_pthread_getspecific,___syscall54:___syscall54,_pthread_setspecific:_pthread_setspecific,___gxx_personality_v0:___gxx_personality_v0,___syscall6:___syscall6,___setErrNo:___setErrNo,_abort:_abort,___cxa_begin_catch:___cxa_begin_catch,_pthread_once:_pthread_once,_emscripten_memcpy_big:_emscripten_memcpy_big,_pthread_key_create:_pthread_key_create,___syscall140:___syscall140,___resumeException:___resumeException,___cxa_find_matching_catch:___cxa_find_matching_catch,___syscall146:___syscall146,__ZSt18uncaught_exceptionv:__ZSt18uncaught_exceptionv,DYNAMICTOP_PTR:DYNAMICTOP_PTR,tempDoublePtr:tempDoublePtr,ABORT:ABORT,STACKTOP:STACKTOP,STACK_MAX:STACK_MAX,cttz_i8:cttz_i8};var asm=function(e,r,i){var n=e.Int8Array,t=new n(i),a=e.Int16Array,o=new a(i),u=e.Int32Array,l=new u(i),f=e.Uint8Array,c=new f(i),s=e.Uint16Array,_=new s(i),d=e.Uint32Array,h=(new d(i),e.Float32Array),E=(new h(i),e.Float64Array),b=new E(i),T=e.byteLength,M=0|r.DYNAMICTOP_PTR,A=0|r.tempDoublePtr,m=(r.ABORT,0|r.STACKTOP),k=(r.STACK_MAX,0|r.cttz_i8),p=(e.NaN,e.Infinity,0),v=(e.Math.floor,e.Math.abs,e.Math.sqrt,e.Math.pow,e.Math.cos,e.Math.sin,e.Math.tan,e.Math.acos,e.Math.asin,e.Math.atan,e.Math.atan2,e.Math.exp,e.Math.log,e.Math.ceil,e.Math.imul),w=(e.Math.min,e.Math.max,e.Math.clz32),S=r.abort,y=(r.assert,r.enlargeMemory),R=r.getTotalMemory,g=r.abortOnCannotGrowMemory,O=(r.invoke_iiii,r.invoke_viiiii,r.invoke_vi,r.invoke_ii,r.invoke_viii,r.invoke_v,r.invoke_viiiiii,r.invoke_viiii,r._pthread_getspecific),C=r.___syscall54,N=r._pthread_setspecific,P=(r.___gxx_personality_v0,r.___syscall6),I=r.___setErrNo,L=r._abort,D=(r.___cxa_begin_catch,r._pthread_once),F=r._emscripten_memcpy_big,H=r._pthread_key_create,U=r.___syscall140,x=(r.___resumeException,r.___cxa_find_matching_catch,r.___syscall146);r.__ZSt18uncaught_exceptionv;function B(e){return!(16777215&T(e)||T(e)<=16777215||T(e)>2147483648)&&(t=new n(e),o=new a(e),l=new u(e),c=new f(e),_=new s(e),new d(e),new h(e),b=new E(e),i=e,!0)}function Y(e){e|=0;var r=0,i=0,n=0,t=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0;v=m,m=m+16|0,d=v;do{if(e>>>0<245){if(c=e>>>0<11?16:e+11&-8,e=c>>>3,_=0|l[1144],i=_>>>e,3&i|0)return r=(1&i^1)+e|0,e=4616+(r<<1<<2)|0,i=e+8|0,n=0|l[i>>2],t=n+8|0,a=0|l[t>>2],(0|e)==(0|a)?l[1144]=_&~(1<<r):(l[a+12>>2]=e,l[i>>2]=a),p=r<<3,l[n+4>>2]=3|p,p=n+p+4|0,l[p>>2]=1|l[p>>2],p=t,m=v,0|p;if(s=0|l[1146],c>>>0>s>>>0){if(0|i)return r=2<<e,r=i<<e&(r|0-r),r=(r&0-r)-1|0,o=r>>>12&16,r>>>=o,i=r>>>5&8,r>>>=i,t=r>>>2&4,r>>>=t,e=r>>>1&2,r>>>=e,n=r>>>1&1,n=(i|o|t|e|n)+(r>>>n)|0,r=4616+(n<<1<<2)|0,e=r+8|0,t=0|l[e>>2],o=t+8|0,i=0|l[o>>2],(0|r)==(0|i)?(e=_&~(1<<n),l[1144]=e):(l[i+12>>2]=r,l[e>>2]=i,e=_),a=(n<<3)-c|0,l[t+4>>2]=3|c,n=t+c|0,l[n+4>>2]=1|a,l[n+a>>2]=a,0|s&&(t=0|l[1149],r=s>>>3,i=4616+(r<<1<<2)|0,r=1<<r,e&r?(e=i+8|0,r=0|l[e>>2]):(l[1144]=e|r,r=i,e=i+8|0),l[e>>2]=t,l[r+12>>2]=t,l[t+8>>2]=r,l[t+12>>2]=i),l[1146]=a,l[1149]=n,p=o,m=v,0|p;if(u=0|l[1145],u){if(i=(u&0-u)-1|0,o=i>>>12&16,i>>>=o,a=i>>>5&8,i>>>=a,f=i>>>2&4,i>>>=f,n=i>>>1&2,i>>>=n,e=i>>>1&1,e=0|l[4880+((a|o|f|n|e)+(i>>>e)<<2)>>2],i=(-8&l[e+4>>2])-c|0,n=0|l[e+16+((0==(0|l[e+16>>2])&1)<<2)>>2],n){do{o=(-8&l[n+4>>2])-c|0,f=o>>>0<i>>>0,i=f?o:i,e=f?n:e,n=0|l[n+16+((0==(0|l[n+16>>2])&1)<<2)>>2]}while(0!=(0|n));f=e,a=i}else f=e,a=i;if(o=f+c|0,f>>>0<o>>>0){t=0|l[f+24>>2],r=0|l[f+12>>2];do{if((0|r)==(0|f)){if(e=f+20|0,r=0|l[e>>2],!r&&(e=f+16|0,r=0|l[e>>2],!r)){i=0;break}while(1)if(i=r+20|0,n=0|l[i>>2],0|n)r=n,e=i;else{if(i=r+16|0,n=0|l[i>>2],!n)break;r=n,e=i}l[e>>2]=0,i=r}else i=0|l[f+8>>2],l[i+12>>2]=r,l[r+8>>2]=i,i=r}while(0);do{if(0|t){if(r=0|l[f+28>>2],e=4880+(r<<2)|0,(0|f)==(0|l[e>>2])){if(l[e>>2]=i,!i){l[1145]=u&~(1<<r);break}}else if(l[t+16+(((0|l[t+16>>2])!=(0|f)&1)<<2)>>2]=i,!i)break;l[i+24>>2]=t,r=0|l[f+16>>2],0|r&&(l[i+16>>2]=r,l[r+24>>2]=i),r=0|l[f+20>>2],0|r&&(l[i+20>>2]=r,l[r+24>>2]=i)}}while(0);return a>>>0<16?(p=a+c|0,l[f+4>>2]=3|p,p=f+p+4|0,l[p>>2]=1|l[p>>2]):(l[f+4>>2]=3|c,l[o+4>>2]=1|a,l[o+a>>2]=a,0|s&&(n=0|l[1149],r=s>>>3,i=4616+(r<<1<<2)|0,r=1<<r,_&r?(e=i+8|0,r=0|l[e>>2]):(l[1144]=_|r,r=i,e=i+8|0),l[e>>2]=n,l[r+12>>2]=n,l[n+8>>2]=r,l[n+12>>2]=i),l[1146]=a,l[1149]=o),p=f+8|0,m=v,0|p}_=c}else _=c}else _=c}else if(e>>>0<=4294967231)if(e=e+11|0,c=-8&e,f=0|l[1145],f){n=0-c|0,e>>>=8,e?c>>>0>16777215?u=31:(_=(e+1048320|0)>>>16&8,k=e<<_,s=(k+520192|0)>>>16&4,k<<=s,u=(k+245760|0)>>>16&2,u=14-(s|_|u)+(k<<u>>>15)|0,u=c>>>(u+7|0)&1|u<<1):u=0,i=0|l[4880+(u<<2)>>2];e:do{if(i){e=0,o=c<<(31==(0|u)?0:25-(u>>>1)|0),a=0;while(1){if(t=(-8&l[i+4>>2])-c|0,t>>>0<n>>>0){if(!t){e=i,n=0,t=i,k=61;break e}e=i,n=t}if(t=0|l[i+20>>2],i=0|l[i+16+(o>>>31<<2)>>2],a=0==(0|t)|(0|t)==(0|i)?a:t,t=0==(0|i),t){i=a,k=57;break}o<<=1&(1^t)}}else i=0,e=0,k=57}while(0);if(57==(0|k)){if(0==(0|i)&0==(0|e)){if(e=2<<u,e=f&(e|0-e),!e){_=c;break}_=(e&0-e)-1|0,o=_>>>12&16,_>>>=o,a=_>>>5&8,_>>>=a,u=_>>>2&4,_>>>=u,s=_>>>1&2,_>>>=s,i=_>>>1&1,e=0,i=0|l[4880+((a|o|u|s|i)+(_>>>i)<<2)>>2]}i?(t=i,k=61):(u=e,o=n)}if(61==(0|k))while(1){if(k=0,i=(-8&l[t+4>>2])-c|0,_=i>>>0<n>>>0,i=_?i:n,e=_?t:e,t=0|l[t+16+((0==(0|l[t+16>>2])&1)<<2)>>2],!t){u=e,o=i;break}n=i,k=61}if(0!=(0|u)&&o>>>0<((0|l[1146])-c|0)>>>0){if(a=u+c|0,u>>>0>=a>>>0)return p=0,m=v,0|p;t=0|l[u+24>>2],r=0|l[u+12>>2];do{if((0|r)==(0|u)){if(e=u+20|0,r=0|l[e>>2],!r&&(e=u+16|0,r=0|l[e>>2],!r)){r=0;break}while(1)if(i=r+20|0,n=0|l[i>>2],0|n)r=n,e=i;else{if(i=r+16|0,n=0|l[i>>2],!n)break;r=n,e=i}l[e>>2]=0}else p=0|l[u+8>>2],l[p+12>>2]=r,l[r+8>>2]=p}while(0);do{if(t){if(e=0|l[u+28>>2],i=4880+(e<<2)|0,(0|u)==(0|l[i>>2])){if(l[i>>2]=r,!r){n=f&~(1<<e),l[1145]=n;break}}else if(l[t+16+(((0|l[t+16>>2])!=(0|u)&1)<<2)>>2]=r,!r){n=f;break}l[r+24>>2]=t,e=0|l[u+16>>2],0|e&&(l[r+16>>2]=e,l[e+24>>2]=r),e=0|l[u+20>>2],e?(l[r+20>>2]=e,l[e+24>>2]=r,n=f):n=f}else n=f}while(0);do{if(o>>>0>=16){if(l[u+4>>2]=3|c,l[a+4>>2]=1|o,l[a+o>>2]=o,r=o>>>3,o>>>0<256){i=4616+(r<<1<<2)|0,e=0|l[1144],r=1<<r,e&r?(e=i+8|0,r=0|l[e>>2]):(l[1144]=e|r,r=i,e=i+8|0),l[e>>2]=a,l[r+12>>2]=a,l[a+8>>2]=r,l[a+12>>2]=i;break}if(r=o>>>8,r?o>>>0>16777215?r=31:(k=(r+1048320|0)>>>16&8,p=r<<k,A=(p+520192|0)>>>16&4,p<<=A,r=(p+245760|0)>>>16&2,r=14-(A|k|r)+(p<<r>>>15)|0,r=o>>>(r+7|0)&1|r<<1):r=0,i=4880+(r<<2)|0,l[a+28>>2]=r,e=a+16|0,l[e+4>>2]=0,l[e>>2]=0,e=1<<r,!(n&e)){l[1145]=n|e,l[i>>2]=a,l[a+24>>2]=i,l[a+12>>2]=a,l[a+8>>2]=a;break}e=o<<(31==(0|r)?0:25-(r>>>1)|0),i=0|l[i>>2];while(1){if((-8&l[i+4>>2]|0)==(0|o)){k=97;break}if(n=i+16+(e>>>31<<2)|0,r=0|l[n>>2],!r){k=96;break}e<<=1,i=r}if(96==(0|k)){l[n>>2]=a,l[a+24>>2]=i,l[a+12>>2]=a,l[a+8>>2]=a;break}if(97==(0|k)){k=i+8|0,p=0|l[k>>2],l[p+12>>2]=a,l[k>>2]=a,l[a+8>>2]=p,l[a+12>>2]=i,l[a+24>>2]=0;break}}else p=o+c|0,l[u+4>>2]=3|p,p=u+p+4|0,l[p>>2]=1|l[p>>2]}while(0);return p=u+8|0,m=v,0|p}_=c}else _=c;else _=-1}while(0);if(i=0|l[1146],i>>>0>=_>>>0)return r=i-_|0,e=0|l[1149],r>>>0>15?(p=e+_|0,l[1149]=p,l[1146]=r,l[p+4>>2]=1|r,l[p+r>>2]=r,l[e+4>>2]=3|_):(l[1146]=0,l[1149]=0,l[e+4>>2]=3|i,p=e+i+4|0,l[p>>2]=1|l[p>>2]),p=e+8|0,m=v,0|p;if(o=0|l[1147],o>>>0>_>>>0)return A=o-_|0,l[1147]=A,p=0|l[1150],k=p+_|0,l[1150]=k,l[k+4>>2]=1|A,l[p+4>>2]=3|_,p=p+8|0,m=v,0|p;if(0|l[1262]?e=0|l[1264]:(l[1264]=4096,l[1263]=4096,l[1265]=-1,l[1266]=-1,l[1267]=0,l[1255]=0,e=-16&d^1431655768,l[d>>2]=e,l[1262]=e,e=4096),u=_+48|0,f=_+47|0,a=e+f|0,t=0-e|0,c=a&t,c>>>0<=_>>>0)return p=0,m=v,0|p;if(e=0|l[1254],0|e&&(s=0|l[1252],d=s+c|0,d>>>0<=s>>>0|d>>>0>e>>>0))return p=0,m=v,0|p;e:do{if(4&l[1255])r=0,k=133;else{i=0|l[1150];r:do{if(i){n=5024;while(1){if(e=0|l[n>>2],e>>>0<=i>>>0&&(b=n+4|0,(e+(0|l[b>>2])|0)>>>0>i>>>0))break;if(e=0|l[n+8>>2],!e){k=118;break r}n=e}if(r=a-o&t,r>>>0<2147483647)if(e=0|nr(0|r),(0|e)==((0|l[n>>2])+(0|l[b>>2])|0)){if(-1!=(0|e)){o=r,a=e,k=135;break e}}else n=e,k=126;else r=0}else k=118}while(0);do{if(118==(0|k))if(i=0|nr(0),-1!=(0|i)&&(r=i,h=0|l[1263],E=h+-1|0,r=(0==(E&r|0)?0:(E+r&0-h)-r|0)+c|0,h=0|l[1252],E=r+h|0,r>>>0>_>>>0&r>>>0<2147483647)){if(b=0|l[1254],0|b&&E>>>0<=h>>>0|E>>>0>b>>>0){r=0;break}if(e=0|nr(0|r),(0|e)==(0|i)){o=r,a=i,k=135;break e}n=e,k=126}else r=0}while(0);do{if(126==(0|k)){if(i=0-r|0,!(u>>>0>r>>>0&r>>>0<2147483647&-1!=(0|n))){if(-1==(0|n)){r=0;break}o=r,a=n,k=135;break e}if(e=0|l[1264],e=f-r+e&0-e,e>>>0>=2147483647){o=r,a=n,k=135;break e}if(-1==(0|nr(0|e))){nr(0|i),r=0;break}o=e+r|0,a=n,k=135;break e}}while(0);l[1255]=4|l[1255],k=133}}while(0);if(133==(0|k)&&c>>>0<2147483647&&(A=0|nr(0|c),b=0|nr(0),T=b-A|0,M=T>>>0>(_+40|0)>>>0,!(-1==(0|A)|1^M|A>>>0<b>>>0&-1!=(0|A)&-1!=(0|b)^1))&&(o=M?T:r,a=A,k=135),135==(0|k)){r=(0|l[1252])+o|0,l[1252]=r,r>>>0>(0|l[1253])>>>0&&(l[1253]=r),f=0|l[1150];do{if(f){r=5024;while(1){if(e=0|l[r>>2],i=r+4|0,n=0|l[i>>2],(0|a)==(e+n|0)){k=145;break}if(t=0|l[r+8>>2],!t)break;r=t}if(145==(0|k)&&0==(8&l[r+12>>2]|0)&&f>>>0<a>>>0&f>>>0>=e>>>0){l[i>>2]=n+o,p=f+8|0,p=0==(7&p|0)?0:0-p&7,k=f+p|0,p=(0|l[1147])+(o-p)|0,l[1150]=k,l[1147]=p,l[k+4>>2]=1|p,l[k+p+4>>2]=40,l[1151]=l[1266];break}a>>>0<(0|l[1148])>>>0&&(l[1148]=a),i=a+o|0,r=5024;while(1){if((0|l[r>>2])==(0|i)){k=153;break}if(e=0|l[r+8>>2],!e)break;r=e}if(153==(0|k)&&0==(8&l[r+12>>2]|0)){l[r>>2]=a,s=r+4|0,l[s>>2]=(0|l[s>>2])+o,s=a+8|0,s=a+(0==(7&s|0)?0:0-s&7)|0,r=i+8|0,r=i+(0==(7&r|0)?0:0-r&7)|0,c=s+_|0,u=r-s-_|0,l[s+4>>2]=3|_;do{if((0|r)!=(0|f)){if((0|r)==(0|l[1149])){p=(0|l[1146])+u|0,l[1146]=p,l[1149]=c,l[c+4>>2]=1|p,l[c+p>>2]=p;break}if(e=0|l[r+4>>2],1==(3&e|0)){o=-8&e,n=e>>>3;e:do{if(e>>>0<256){if(e=0|l[r+8>>2],i=0|l[r+12>>2],(0|i)==(0|e)){l[1144]=l[1144]&~(1<<n);break}l[e+12>>2]=i,l[i+8>>2]=e;break}a=0|l[r+24>>2],e=0|l[r+12>>2];do{if((0|e)==(0|r)){if(n=r+16|0,i=n+4|0,e=0|l[i>>2],!e){if(e=0|l[n>>2],!e){e=0;break}i=n}while(1)if(n=e+20|0,t=0|l[n>>2],0|t)e=t,i=n;else{if(n=e+16|0,t=0|l[n>>2],!t)break;e=t,i=n}l[i>>2]=0}else p=0|l[r+8>>2],l[p+12>>2]=e,l[e+8>>2]=p}while(0);if(!a)break;i=0|l[r+28>>2],n=4880+(i<<2)|0;do{if((0|r)==(0|l[n>>2])){if(l[n>>2]=e,0|e)break;l[1145]=l[1145]&~(1<<i);break e}if(l[a+16+(((0|l[a+16>>2])!=(0|r)&1)<<2)>>2]=e,!e)break e}while(0);if(l[e+24>>2]=a,i=r+16|0,n=0|l[i>>2],0|n&&(l[e+16>>2]=n,l[n+24>>2]=e),i=0|l[i+4>>2],!i)break;l[e+20>>2]=i,l[i+24>>2]=e}while(0);r=r+o|0,t=o+u|0}else t=u;if(r=r+4|0,l[r>>2]=-2&l[r>>2],l[c+4>>2]=1|t,l[c+t>>2]=t,r=t>>>3,t>>>0<256){i=4616+(r<<1<<2)|0,e=0|l[1144],r=1<<r,e&r?(e=i+8|0,r=0|l[e>>2]):(l[1144]=e|r,r=i,e=i+8|0),l[e>>2]=c,l[r+12>>2]=c,l[c+8>>2]=r,l[c+12>>2]=i;break}r=t>>>8;do{if(r){if(t>>>0>16777215){r=31;break}k=(r+1048320|0)>>>16&8,p=r<<k,A=(p+520192|0)>>>16&4,p<<=A,r=(p+245760|0)>>>16&2,r=14-(A|k|r)+(p<<r>>>15)|0,r=t>>>(r+7|0)&1|r<<1}else r=0}while(0);if(n=4880+(r<<2)|0,l[c+28>>2]=r,e=c+16|0,l[e+4>>2]=0,l[e>>2]=0,e=0|l[1145],i=1<<r,!(e&i)){l[1145]=e|i,l[n>>2]=c,l[c+24>>2]=n,l[c+12>>2]=c,l[c+8>>2]=c;break}e=t<<(31==(0|r)?0:25-(r>>>1)|0),i=0|l[n>>2];while(1){if((-8&l[i+4>>2]|0)==(0|t)){k=194;break}if(n=i+16+(e>>>31<<2)|0,r=0|l[n>>2],!r){k=193;break}e<<=1,i=r}if(193==(0|k)){l[n>>2]=c,l[c+24>>2]=i,l[c+12>>2]=c,l[c+8>>2]=c;break}if(194==(0|k)){k=i+8|0,p=0|l[k>>2],l[p+12>>2]=c,l[k>>2]=c,l[c+8>>2]=p,l[c+12>>2]=i,l[c+24>>2]=0;break}}else p=(0|l[1147])+u|0,l[1147]=p,l[1150]=c,l[c+4>>2]=1|p}while(0);return p=s+8|0,m=v,0|p}r=5024;while(1){if(e=0|l[r>>2],e>>>0<=f>>>0&&(p=e+(0|l[r+4>>2])|0,p>>>0>f>>>0))break;r=0|l[r+8>>2]}t=p+-47|0,e=t+8|0,e=t+(0==(7&e|0)?0:0-e&7)|0,t=f+16|0,e=e>>>0<t>>>0?f:e,r=e+8|0,i=a+8|0,i=0==(7&i|0)?0:0-i&7,k=a+i|0,i=o+-40-i|0,l[1150]=k,l[1147]=i,l[k+4>>2]=1|i,l[k+i+4>>2]=40,l[1151]=l[1266],i=e+4|0,l[i>>2]=27,l[r>>2]=l[1256],l[r+4>>2]=l[1257],l[r+8>>2]=l[1258],l[r+12>>2]=l[1259],l[1256]=a,l[1257]=o,l[1259]=0,l[1258]=r,r=e+24|0;do{k=r,r=r+4|0,l[r>>2]=7}while((k+8|0)>>>0<p>>>0);if((0|e)!=(0|f)){if(a=e-f|0,l[i>>2]=-2&l[i>>2],l[f+4>>2]=1|a,l[e>>2]=a,r=a>>>3,a>>>0<256){i=4616+(r<<1<<2)|0,e=0|l[1144],r=1<<r,e&r?(e=i+8|0,r=0|l[e>>2]):(l[1144]=e|r,r=i,e=i+8|0),l[e>>2]=f,l[r+12>>2]=f,l[f+8>>2]=r,l[f+12>>2]=i;break}if(r=a>>>8,r?a>>>0>16777215?i=31:(k=(r+1048320|0)>>>16&8,p=r<<k,A=(p+520192|0)>>>16&4,p<<=A,i=(p+245760|0)>>>16&2,i=14-(A|k|i)+(p<<i>>>15)|0,i=a>>>(i+7|0)&1|i<<1):i=0,n=4880+(i<<2)|0,l[f+28>>2]=i,l[f+20>>2]=0,l[t>>2]=0,r=0|l[1145],e=1<<i,!(r&e)){l[1145]=r|e,l[n>>2]=f,l[f+24>>2]=n,l[f+12>>2]=f,l[f+8>>2]=f;break}e=a<<(31==(0|i)?0:25-(i>>>1)|0),i=0|l[n>>2];while(1){if((-8&l[i+4>>2]|0)==(0|a)){k=216;break}if(n=i+16+(e>>>31<<2)|0,r=0|l[n>>2],!r){k=215;break}e<<=1,i=r}if(215==(0|k)){l[n>>2]=f,l[f+24>>2]=i,l[f+12>>2]=f,l[f+8>>2]=f;break}if(216==(0|k)){k=i+8|0,p=0|l[k>>2],l[p+12>>2]=f,l[k>>2]=f,l[f+8>>2]=p,l[f+12>>2]=i,l[f+24>>2]=0;break}}}else{p=0|l[1148],0==(0|p)|a>>>0<p>>>0&&(l[1148]=a),l[1256]=a,l[1257]=o,l[1259]=0,l[1153]=l[1262],l[1152]=-1,r=0;do{p=4616+(r<<1<<2)|0,l[p+12>>2]=p,l[p+8>>2]=p,r=r+1|0}while(32!=(0|r));p=a+8|0,p=0==(7&p|0)?0:0-p&7,k=a+p|0,p=o+-40-p|0,l[1150]=k,l[1147]=p,l[k+4>>2]=1|p,l[k+p+4>>2]=40,l[1151]=l[1266]}}while(0);if(r=0|l[1147],r>>>0>_>>>0)return A=r-_|0,l[1147]=A,p=0|l[1150],k=p+_|0,l[1150]=k,l[k+4>>2]=1|A,l[p+4>>2]=3|_,p=p+8|0,m=v,0|p}return p=0|li(),l[p>>2]=12,p=0,m=v,0|p}function X(e,r,i,n,a,o){e|=0,r=+r,i|=0,n|=0,a|=0,o|=0;var u=0,f=0,s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0;L=m,m=m+560|0,s=L+8|0,A=L,I=L+524|0,P=I,_=L+512|0,l[A>>2]=0,N=_+12|0,Ir(r),(0|p)<0?(r=-r,O=1,g=2087):(O=0!=(2049&a|0)&1,g=0==(2048&a|0)?0==(1&a|0)?2088:2093:2090),Ir(r),C=2146435072&p;do{if(C>>>0<2146435072|2146435072==(0|C)&!1){if(b=2*+oi(r,A),u=0!=b,u&&(l[A>>2]=(0|l[A>>2])-1),w=32|o,97==(0|w)){T=32&o,E=0==(0|T)?g:g+9|0,h=2|O,u=12-n|0;do{if(!(n>>>0>11|0==(0|u))){r=8;do{u=u+-1|0,r*=16}while(0!=(0|u));if(45==(0|t[E>>0])){r=-(r+(-b-r));break}r=b+r-r;break}r=b}while(0);f=0|l[A>>2],u=(0|f)<0?0-f|0:f,u=0|We(u,((0|u)<0)<<31>>31,N),(0|u)==(0|N)&&(u=_+11|0,t[u>>0]=48),t[u+-1>>0]=43+(f>>31&2),d=u+-2|0,t[d>>0]=o+15,_=(0|n)<1,s=0==(8&a|0),u=I;do{C=~~r,f=u+1|0,t[u>>0]=c[2122+C>>0]|T,r=16*(r-+(0|C)),1!=(f-P|0)||s&_&0==r?u=f:(t[f>>0]=46,u=u+2|0)}while(0!=r);C=u-P|0,P=N-d|0,N=0!=(0|n)&(C+-2|0)<(0|n)?n+2|0:C,u=P+h+N|0,qe(e,32,i,u,a),Br(e,E,h),qe(e,48,i,u,65536^a),Br(e,I,C),qe(e,48,N-C|0,0,0),Br(e,d,P),qe(e,32,i,u,8192^a);break}f=(0|n)<0?6:n,u?(u=(0|l[A>>2])-28|0,l[A>>2]=u,r=268435456*b):(r=b,u=0|l[A>>2]),C=(0|u)<0?s:s+288|0,s=C;do{y=~~r>>>0,l[s>>2]=y,s=s+4|0,r=1e9*(r-+(y>>>0))}while(0!=r);if((0|u)>0){_=C,h=s;while(1){if(d=(0|u)<29?u:29,u=h+-4|0,u>>>0>=_>>>0){s=0;do{S=0|yr(0|l[u>>2],0,0|d),S=0|Lr(0|S,0|p,0|s,0),y=p,k=0|hr(0|S,0|y,1e9,0),l[u>>2]=k,s=0|zr(0|S,0|y,1e9,0),u=u+-4|0}while(u>>>0>=_>>>0);s&&(_=_+-4|0,l[_>>2]=s)}s=h;while(1){if(s>>>0<=_>>>0)break;if(u=s+-4|0,0|l[u>>2])break;s=u}if(u=(0|l[A>>2])-d|0,l[A>>2]=u,!((0|u)>0))break;h=s}}else _=C;if((0|u)<0){n=1+((f+25|0)/9|0)|0,M=102==(0|w);do{if(T=0-u|0,T=(0|T)<9?T:9,_>>>0<s>>>0){d=(1<<T)-1|0,h=1e9>>>T,E=0,u=_;do{y=0|l[u>>2],l[u>>2]=(y>>>T)+E,E=0|v(y&d,h),u=u+4|0}while(u>>>0<s>>>0);u=0==(0|l[_>>2])?_+4|0:_,E?(l[s>>2]=E,_=u,u=s+4|0):(_=u,u=s)}else _=0==(0|l[_>>2])?_+4|0:_,u=s;s=M?C:_,s=(u-s>>2|0)>(0|n)?s+(n<<2)|0:u,u=(0|l[A>>2])+T|0,l[A>>2]=u}while((0|u)<0);u=_,n=s}else u=_,n=s;if(y=C,u>>>0<n>>>0){if(s=9*(y-u>>2)|0,d=0|l[u>>2],d>>>0>=10){_=10;do{_=10*_|0,s=s+1|0}while(d>>>0>=_>>>0)}}else s=0;if(M=103==(0|w),k=0!=(0|f),_=f-(102!=(0|w)?s:0)+((k&M)<<31>>31)|0,(0|_)<((9*(n-y>>2)|0)-9|0)){if(_=_+9216|0,T=C+4+(((0|_)/9|0)-1024<<2)|0,_=1+((0|_)%9|0)|0,(0|_)<9){d=10;do{d=10*d|0,_=_+1|0}while(9!=(0|_))}else d=10;if(h=0|l[T>>2],E=(h>>>0)%(d>>>0)|0,_=(T+4|0)==(0|n),_&0==(0|E))_=T;else if(b=0==(1&((h>>>0)/(d>>>0)|0)|0)?9007199254740992:9007199254740994,S=(0|d)/2|0,r=E>>>0<S>>>0?.5:_&(0|E)==(0|S)?1:1.5,O&&(S=45==(0|t[g>>0]),r=S?-r:r,b=S?-b:b),_=h-E|0,l[T>>2]=_,b+r!=b){if(S=_+d|0,l[T>>2]=S,S>>>0>999999999){s=T;while(1){if(_=s+-4|0,l[s>>2]=0,_>>>0<u>>>0&&(u=u+-4|0,l[u>>2]=0),S=1+(0|l[_>>2])|0,l[_>>2]=S,!(S>>>0>999999999))break;s=_}}else _=T;if(s=9*(y-u>>2)|0,h=0|l[u>>2],h>>>0>=10){d=10;do{d=10*d|0,s=s+1|0}while(h>>>0>=d>>>0)}}else _=T;_=_+4|0,_=n>>>0>_>>>0?_:n,S=u}else _=n,S=u;w=_;while(1){if(w>>>0<=S>>>0){A=0;break}if(u=w+-4|0,0|l[u>>2]){A=1;break}w=u}n=0-s|0;do{if(M){if(u=(1&(1^k))+f|0,(0|u)>(0|s)&(0|s)>-5?(d=o+-1|0,f=u+-1-s|0):(d=o+-2|0,f=u+-1|0),u=8&a,!u){if(A&&(R=0|l[w+-4>>2],0!=(0|R)))if((R>>>0)%10|0)_=0;else{_=0,u=10;do{u=10*u|0,_=_+1|0}while(!(0|(R>>>0)%(u>>>0)))}else _=9;if(u=(9*(w-y>>2)|0)-9|0,102==(32|d)){T=u-_|0,T=(0|T)>0?T:0,f=(0|f)<(0|T)?f:T,T=0;break}T=u+s-_|0,T=(0|T)>0?T:0,f=(0|f)<(0|T)?f:T,T=0;break}T=u}else d=o,T=8&a}while(0);if(M=f|T,h=0!=(0|M)&1,E=102==(32|d),E)k=0,u=(0|s)>0?s:0;else{if(u=(0|s)<0?n:s,u=0|We(u,((0|u)<0)<<31>>31,N),_=N,(_-u|0)<2)do{u=u+-1|0,t[u>>0]=48}while((_-u|0)<2);t[u+-1>>0]=43+(s>>31&2),u=u+-2|0,t[u>>0]=d,k=u,u=_-u|0}if(u=O+1+f+h+u|0,qe(e,32,i,u,a),Br(e,g,O),qe(e,48,i,u,65536^a),E){d=S>>>0>C>>>0?C:S,T=I+9|0,h=T,E=I+8|0,_=d;do{if(s=0|We(0|l[_>>2],0,T),(0|_)==(0|d))(0|s)==(0|T)&&(t[E>>0]=48,s=E);else if(s>>>0>I>>>0){He(0|I,48,s-P|0);do{s=s+-1|0}while(s>>>0>I>>>0)}Br(e,s,h-s|0),_=_+4|0}while(_>>>0<=C>>>0);if(0|M&&Br(e,2138,1),_>>>0<w>>>0&(0|f)>0)while(1){if(s=0|We(0|l[_>>2],0,T),s>>>0>I>>>0){He(0|I,48,s-P|0);do{s=s+-1|0}while(s>>>0>I>>>0)}if(Br(e,s,(0|f)<9?f:9),_=_+4|0,s=f+-9|0,!(_>>>0<w>>>0&(0|f)>9)){f=s;break}f=s}qe(e,48,f+9|0,9,0)}else{if(M=A?w:S+4|0,(0|f)>-1){A=I+9|0,T=0==(0|T),n=A,h=0-P|0,E=I+8|0,d=S;do{s=0|We(0|l[d>>2],0,A),(0|s)==(0|A)&&(t[E>>0]=48,s=E);do{if((0|d)==(0|S)){if(_=s+1|0,Br(e,s,1),T&(0|f)<1){s=_;break}Br(e,2138,1),s=_}else{if(s>>>0<=I>>>0)break;He(0|I,48,s+h|0);do{s=s+-1|0}while(s>>>0>I>>>0)}}while(0);P=n-s|0,Br(e,s,(0|f)>(0|P)?P:f),f=f-P|0,d=d+4|0}while(d>>>0<M>>>0&(0|f)>-1)}qe(e,48,f+18|0,18,0),Br(e,k,N-k|0)}qe(e,32,i,u,8192^a)}else I=0!=(32&o|0),u=O+3|0,qe(e,32,i,u,-65537&a),Br(e,g,O),Br(e,r!=r|!1?I?2114:2118:I?2106:2110,3),qe(e,32,i,u,8192^a)}while(0);return m=L,0|((0|u)<(0|i)?i:u)}function K(e,r,i,n,a){e|=0,r|=0,i|=0,n|=0,a|=0;var u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0,T=0,M=0,A=0,k=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0;I=m,m=m+64|0,O=I+16|0,C=I,R=I+24|0,N=I+8|0,P=I+20|0,l[O>>2]=r,w=0!=(0|e),S=R+40|0,y=S,R=R+39|0,g=N+4|0,f=0,u=0,d=0;e:while(1){do{if((0|u)>-1){if((0|f)>(2147483647-u|0)){u=0|li(),l[u>>2]=75,u=-1;break}u=f+u|0;break}}while(0);if(f=0|t[r>>0],!(f<<24>>24)){v=87;break}c=r;r:while(1){switch(f<<24>>24){case 37:f=c,v=9;break r;case 0:f=c;break r}k=c+1|0,l[O>>2]=k,f=0|t[k>>0],c=k}r:do{if(9==(0|v))while(1){if(v=0,37!=(0|t[c+1>>0]))break r;if(f=f+1|0,c=c+2|0,l[O>>2]=c,37!=(0|t[c>>0]))break;v=9}}while(0);if(f=f-r|0,w&&Br(e,r,f),0|f)r=c;else{s=c+1|0,f=(0|t[s>>0])-48|0,f>>>0<10?(k=36==(0|t[c+2>>0]),A=k?f:-1,d=k?1:d,s=k?c+3|0:s):A=-1,l[O>>2]=s,f=0|t[s>>0],c=(f<<24>>24)-32|0;r:do{if(c>>>0<32){_=0,h=f;while(1){if(f=1<<c,!(75913&f)){f=h;break r}if(_|=f,s=s+1|0,l[O>>2]=s,f=0|t[s>>0],c=(f<<24>>24)-32|0,c>>>0>=32)break;h=f}}else _=0}while(0);if(f<<24>>24==42){if(c=s+1|0,f=(0|t[c>>0])-48|0,f>>>0<10&&36==(0|t[s+2>>0]))l[a+(f<<2)>>2]=10,f=0|l[n+((0|t[c>>0])-48<<3)>>2],d=1,s=s+3|0;else{if(0|d){u=-1;break}w?(d=3+(0|l[i>>2])&-4,f=0|l[d>>2],l[i>>2]=d+4,d=0,s=c):(f=0,d=0,s=c)}l[O>>2]=s,k=(0|f)<0,f=k?0-f|0:f,_=k?8192|_:_}else{if(f=0|ur(O),(0|f)<0){u=-1;break}s=0|l[O>>2]}do{if(46==(0|t[s>>0])){if(42!=(0|t[s+1>>0])){l[O>>2]=s+1,c=0|ur(O),s=0|l[O>>2];break}if(h=s+2|0,c=(0|t[h>>0])-48|0,c>>>0<10&&36==(0|t[s+3>>0])){l[a+(c<<2)>>2]=10,c=0|l[n+((0|t[h>>0])-48<<3)>>2],s=s+4|0,l[O>>2]=s;break}if(0|d){u=-1;break e}w?(k=3+(0|l[i>>2])&-4,c=0|l[k>>2],l[i>>2]=k+4):c=0,l[O>>2]=h,s=h}else c=-1}while(0);M=0;while(1){if(((0|t[s>>0])-65|0)>>>0>57){u=-1;break e}if(k=s+1|0,l[O>>2]=k,h=0|t[(0|t[s>>0])-65+(1606+(58*M|0))>>0],E=255&h,!((E+-1|0)>>>0<8))break;M=E,s=k}if(!(h<<24>>24)){u=-1;break}T=(0|A)>-1;do{if(h<<24>>24==19){if(T){u=-1;break e}v=49}else{if(T){l[a+(A<<2)>>2]=E,T=n+(A<<3)|0,A=0|l[T+4>>2],v=C,l[v>>2]=l[T>>2],l[v+4>>2]=A,v=49;break}if(!w){u=0;break e}oe(C,E,i)}}while(0);if(49!=(0|v)||(v=0,w)){s=0|t[s>>0],s=0!=(0|M)&3==(15&s|0)?-33&s:s,T=-65537&_,A=0==(8192&_|0)?_:T;r:do{switch(0|s){case 110:switch((255&M)<<24>>24){case 0:l[l[C>>2]>>2]=u,f=0,r=k;continue e;case 1:l[l[C>>2]>>2]=u,f=0,r=k;continue e;case 2:f=0|l[C>>2],l[f>>2]=u,l[f+4>>2]=((0|u)<0)<<31>>31,f=0,r=k;continue e;case 3:o[l[C>>2]>>1]=u,f=0,r=k;continue e;case 4:t[l[C>>2]>>0]=u,f=0,r=k;continue e;case 6:l[l[C>>2]>>2]=u,f=0,r=k;continue e;case 7:f=0|l[C>>2],l[f>>2]=u,l[f+4>>2]=((0|u)<0)<<31>>31,f=0,r=k;continue e;default:f=0,r=k;continue e}case 112:s=120,c=c>>>0>8?c:8,r=8|A,v=61;break;case 88:case 120:r=A,v=61;break;case 111:s=C,r=0|l[s>>2],s=0|l[s+4>>2],E=0|_r(r,s,S),T=y-E|0,_=0,h=2070,c=0==(8&A|0)|(0|c)>(0|T)?c:T+1|0,T=A,v=67;break;case 105:case 100:if(s=C,r=0|l[s>>2],s=0|l[s+4>>2],(0|s)<0){r=0|Cr(0,0,0|r,0|s),s=p,_=C,l[_>>2]=r,l[_+4>>2]=s,_=1,h=2070,v=66;break r}_=0!=(2049&A|0)&1,h=0==(2048&A|0)?0==(1&A|0)?2070:2072:2071,v=66;break r;case 117:s=C,_=0,h=2070,r=0|l[s>>2],s=0|l[s+4>>2],v=66;break;case 99:t[R>>0]=l[C>>2],r=R,_=0,h=2070,E=S,s=1,c=T;break;case 109:s=0|li(),s=0|Kr(0|l[s>>2]),v=71;break;case 115:s=0|l[C>>2],s=0|s?s:2080,v=71;break;case 67:l[N>>2]=l[C>>2],l[g>>2]=0,l[C>>2]=N,E=-1,s=N,v=75;break;case 83:r=0|l[C>>2],c?(E=c,s=r,v=75):(qe(e,32,f,0,A),r=0,v=84);break;case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:f=0|X(e,+b[C>>3],f,c,A,s),r=k;continue e;default:_=0,h=2070,E=S,s=c,c=A}}while(0);r:do{if(61==(0|v))A=C,M=0|l[A>>2],A=0|l[A+4>>2],E=0|lr(M,A,S,32&s),h=0==(8&r|0)|0==(0|M)&0==(0|A),_=h?0:2,h=h?2070:2070+(s>>4)|0,T=r,r=M,s=A,v=67;else if(66==(0|v))E=0|We(r,s,S),T=A,v=67;else if(71==(0|v))v=0,A=0|we(s,0,c),M=0==(0|A),r=s,_=0,h=2070,E=M?s+c|0:A,s=M?c:A-s|0,c=T;else if(75==(0|v)){v=0,h=s,r=0,c=0;while(1){if(_=0|l[h>>2],!_)break;if(c=0|Vr(P,_),(0|c)<0|c>>>0>(E-r|0)>>>0)break;if(r=c+r|0,!(E>>>0>r>>>0))break;h=h+4|0}if((0|c)<0){u=-1;break e}if(qe(e,32,f,r,A),r){_=0;while(1){if(c=0|l[s>>2],!c){v=84;break r}if(c=0|Vr(P,c),_=c+_|0,(0|_)>(0|r)){v=84;break r}if(Br(e,P,c),_>>>0>=r>>>0){v=84;break}s=s+4|0}}else r=0,v=84}}while(0);if(67==(0|v))v=0,s=0!=(0|r)|0!=(0|s),A=0!=(0|c)|s,s=y-E+(1&(1^s))|0,r=A?E:S,E=S,s=A?(0|c)>(0|s)?c:s:c,c=(0|c)>-1?-65537&T:T;else if(84==(0|v)){v=0,qe(e,32,f,r,8192^A),f=(0|f)>(0|r)?f:r,r=k;continue}M=E-r|0,T=(0|s)<(0|M)?M:s,A=T+_|0,f=(0|f)<(0|A)?A:f,qe(e,32,f,A,c),Br(e,h,_),qe(e,48,f,A,65536^c),qe(e,48,T,M,0),Br(e,r,M),qe(e,32,f,A,8192^c),r=k}else f=0,r=k}}e:do{if(87==(0|v)&&!e)if(d){u=1;while(1){if(r=0|l[a+(u<<2)>>2],!r)break;if(oe(n+(u<<3)|0,r,i),u=u+1|0,(0|u)>=10){u=1;break e}}while(1){if(0|l[a+(u<<2)>>2]){u=-1;break e}if(u=u+1|0,(0|u)>=10){u=1;break}}}else u=0}while(0);return m=I,0|u}function V(e,r){e|=0,r|=0;var i=0,n=0,a=0,o=0,u=0,f=0,s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0;if(C=m,m=m+704|0,y=C+144|0,S=C+128|0,w=C+112|0,v=C+96|0,p=C+80|0,k=C+64|0,A=C+48|0,R=C+32|0,d=C+16|0,f=C,E=C+184|0,O=C+160|0,b=0|Re(e,14),!b)return Ie(r),O=1,m=C,0|O;if(T=r+4|0,M=r+8|0,i=0|l[M>>2],(0|i)!=(0|b)){if(i>>>0<=b>>>0){do{if((0|l[r+12>>2])>>>0<b>>>0){if(0|le(T,b,(i+1|0)==(0|b),1,0)){i=0|l[M>>2];break}return t[r+16>>0]=1,O=0,m=C,0|O}}while(0);He((0|l[T>>2])+i|0,0,b-i|0)}l[M>>2]=b}if(He(0|l[T>>2],0,0|b),h=e+20|0,i=0|l[h>>2],(0|i)<5){o=e+4|0,u=e+8|0,a=e+16|0;do{n=0|l[o>>2],(0|n)==(0|l[u>>2])?n=0:(l[o>>2]=n+1,n=0|c[n>>0]),i=i+8|0,l[h>>2]=i,(0|i)>=33&&(l[f>>2]=866,l[f+4>>2]=3208,l[f+8>>2]=1366,vr(E,812,f),Je(E),i=0|l[h>>2]),n=n<<32-i|l[a>>2],l[a>>2]=n}while((0|i)<5)}else n=e+16|0,a=n,n=0|l[n>>2];if(_=n>>>27,l[a>>2]=n<<5,l[h>>2]=i+-5,(_+-1|0)>>>0>20)return O=0,m=C,0|O;l[O+20>>2]=0,l[O>>2]=0,l[O+4>>2]=0,l[O+8>>2]=0,l[O+12>>2]=0,t[O+16>>0]=0,i=O+4|0,n=O+8|0;e:do{if(0|le(i,21,0,1,0)){o=0|l[n>>2],s=0|l[i>>2],He(s+o|0,0,21-o|0),l[n>>2]=21,o=e+4|0,u=e+8|0,f=e+16|0,a=0;do{if(i=0|l[h>>2],(0|i)<3)do{n=0|l[o>>2],(0|n)==(0|l[u>>2])?n=0:(l[o>>2]=n+1,n=0|c[n>>0]),i=i+8|0,l[h>>2]=i,(0|i)>=33&&(l[d>>2]=866,l[d+4>>2]=3208,l[d+8>>2]=1366,vr(E,812,d),Je(E),i=0|l[h>>2]),n=n<<32-i|l[f>>2],l[f>>2]=n}while((0|i)<3);else n=0|l[f>>2];l[f>>2]=n<<3,l[h>>2]=i+-3,t[s+(0|c[1327+a>>0])>>0]=n>>>29,a=a+1|0}while((0|a)!=(0|_));if(0|Ae(O)){f=e+4|0,s=e+8|0,_=e+16|0,i=0;r:do{u=b-i|0,a=0|te(e,O);i:do{if(a>>>0<17)(0|l[M>>2])>>>0<=i>>>0&&(l[R>>2]=866,l[R+4>>2]=910,l[R+8>>2]=1497,vr(E,812,R),Je(E)),t[(0|l[T>>2])+i>>0]=a,i=i+1|0;else switch(0|a){case 17:if(n=0|l[h>>2],(0|n)<3)do{a=0|l[f>>2],(0|a)==(0|l[s>>2])?a=0:(l[f>>2]=a+1,a=0|c[a>>0]),n=n+8|0,l[h>>2]=n,(0|n)>=33&&(l[A>>2]=866,l[A+4>>2]=3208,l[A+8>>2]=1366,vr(E,812,A),Je(E),n=0|l[h>>2]),a=a<<32-n|l[_>>2],l[_>>2]=a}while((0|n)<3);else a=0|l[_>>2];if(l[_>>2]=a<<3,l[h>>2]=n+-3,a=3+(a>>>29)|0,n=a>>>0>u>>>0,n){i=0;break e}i=(n?0:a)+i|0;break i;case 18:if(n=0|l[h>>2],(0|n)<7)do{a=0|l[f>>2],(0|a)==(0|l[s>>2])?a=0:(l[f>>2]=a+1,a=0|c[a>>0]),n=n+8|0,l[h>>2]=n,(0|n)>=33&&(l[k>>2]=866,l[k+4>>2]=3208,l[k+8>>2]=1366,vr(E,812,k),Je(E),n=0|l[h>>2]),a=a<<32-n|l[_>>2],l[_>>2]=a}while((0|n)<7);else a=0|l[_>>2];if(l[_>>2]=a<<7,l[h>>2]=n+-7,a=11+(a>>>25)|0,n=a>>>0>u>>>0,n){i=0;break e}i=(n?0:a)+i|0;break i;default:if((a+-19|0)>>>0>=2){g=81;break r}if(n=0|l[h>>2],19==(0|a)){if((0|n)<2){a=n;while(1){if(n=0|l[f>>2],(0|n)==(0|l[s>>2])?o=0:(l[f>>2]=n+1,o=0|c[n>>0]),n=a+8|0,l[h>>2]=n,(0|n)>=33&&(l[p>>2]=866,l[p+4>>2]=3208,l[p+8>>2]=1366,vr(E,812,p),Je(E),n=0|l[h>>2]),a=o<<32-n|l[_>>2],l[_>>2]=a,!((0|n)<2))break;a=n}}else a=0|l[_>>2];l[_>>2]=a<<2,a>>>=30,o=3,n=n+-2|0}else{if((0|n)<6)do{a=0|l[f>>2],(0|a)==(0|l[s>>2])?a=0:(l[f>>2]=a+1,a=0|c[a>>0]),n=n+8|0,l[h>>2]=n,(0|n)>=33&&(l[v>>2]=866,l[v+4>>2]=3208,l[v+8>>2]=1366,vr(E,812,v),Je(E),n=0|l[h>>2]),a=a<<32-n|l[_>>2],l[_>>2]=a}while((0|n)<6);else a=0|l[_>>2];l[_>>2]=a<<6,a>>>=26,o=7,n=n+-6|0}if(l[h>>2]=n,a=a+o|0,0==(0|i)|a>>>0>u>>>0){i=0;break e}if(n=i+-1|0,(0|l[M>>2])>>>0<=n>>>0&&(l[w>>2]=866,l[w+4>>2]=910,l[w+8>>2]=1497,vr(E,812,w),Je(E)),o=0|t[(0|l[T>>2])+n>>0],!(o<<24>>24)){i=0;break e}if(n=a+i|0,i>>>0>=n>>>0)break i;do{(0|l[M>>2])>>>0<=i>>>0&&(l[S>>2]=866,l[S+4>>2]=910,l[S+8>>2]=1497,vr(E,812,S),Je(E)),t[(0|l[T>>2])+i>>0]=o,i=i+1|0}while((0|i)!=(0|n));i=n}}while(0)}while(b>>>0>i>>>0);if(81==(0|g)){l[y>>2]=866,l[y+4>>2]=3149,l[y+8>>2]=1348,vr(E,812,y),Je(E),i=0;break}i=(0|b)==(0|i)?0|Ae(r):0}else i=0}else t[O+16>>0]=1,i=0}while(0);return Ne(O),O=i,m=C,0|O}function G(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var a=0,u=0,f=0,s=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0;if(H=m,m=m+880|0,D=H+144|0,L=H+128|0,I=H+112|0,P=H+96|0,g=H+80|0,v=H+64|0,k=H+48|0,p=H+32|0,b=H+16|0,E=H,C=H+360|0,N=H+296|0,F=H+224|0,A=H+156|0,0==(0|r)|n>>>0>11)return F=0,m=H,0|F;l[e>>2]=r,a=F,u=a+68|0;do{l[a>>2]=0,a=a+4|0}while((0|a)<(0|u));a=0;do{O=0|t[i+a>>0],u=F+((255&O)<<2)|0,O<<24>>24&&(l[u>>2]=1+(0|l[u>>2])),a=a+1|0}while((0|a)!=(0|r));u=0,f=0,s=0,d=-1,h=1;while(1){if(a=0|l[F+(h<<2)>>2],a?(T=h+-1|0,l[N+(T<<2)>>2]=u,u=a+u|0,O=16-h|0,l[e+28+(T<<2)>>2]=1+(u+-1<<O|(1<<O)-1),l[e+96+(T<<2)>>2]=f,l[A+(h<<2)>>2]=f,T=a+f|0,s=s>>>0>h>>>0?s:h,d=d>>>0<h>>>0?d:h):(l[e+28+(h+-1<<2)>>2]=0,T=f),h=h+1|0,17==(0|h))break;u<<=1,f=T}l[e+4>>2]=T,u=e+172|0;do{if(T>>>0>(0|l[u>>2])>>>0){a=T+-1|0,a&T?(a|=a>>>16,a|=a>>>8,a|=a>>>4,a|=a>>>2,a=1+(a>>>1|a)|0,a=a>>>0>r>>>0?r:a):a=T,l[u>>2]=a,f=e+176|0,a=0|l[f>>2];do{if(0|a){if(O=0|l[a+-4>>2],a=a+-8|0,0!=(0|O)&&(0|O)==(0|~l[a>>2])||(l[E>>2]=866,l[E+4>>2]=651,l[E+8>>2]=1579,vr(C,812,E),Je(C)),7&a){l[b>>2]=866,l[b+4>>2]=2506,l[b+8>>2]=1232,vr(C,812,b),Je(C);break}Xe(a,0,0,1,0);break}}while(0);if(a=0|l[u>>2],a=0|a?a:1,u=0|Pe(8+(a<<1)|0,0),u){l[u+4>>2]=a,l[u>>2]=~a,l[f>>2]=u+8,M=24;break}l[f>>2]=0,n=0;break}M=24}while(0);e:do{if(24==(0|M)){O=e+24|0,t[O>>0]=d,t[e+25>>0]=s,f=e+176|0,u=0;do{R=0|t[i+u>>0],a=255&R,R<<24>>24&&(0|l[F+(a<<2)>>2]||(l[p>>2]=866,l[p+4>>2]=2276,l[p+8>>2]=977,vr(C,812,p),Je(C)),R=A+(a<<2)|0,a=0|l[R>>2],l[R>>2]=a+1,a>>>0>=T>>>0&&(l[k>>2]=866,l[k+4>>2]=2280,l[k+8>>2]=990,vr(C,812,k),Je(C)),o[(0|l[f>>2])+(a<<1)>>1]=u),u=u+1|0}while((0|u)!=(0|r));if(y=(0|c[O>>0])>>>0<n>>>0?n:0,R=e+8|0,l[R>>2]=y,S=0!=(0|y),S){w=1<<y,a=e+164|0;do{if(w>>>0>(0|l[a>>2])>>>0){l[a>>2]=w,f=e+168|0,a=0|l[f>>2];do{if(0|a){if(p=0|l[a+-4>>2],a=a+-8|0,0!=(0|p)&&(0|p)==(0|~l[a>>2])||(l[v>>2]=866,l[v+4>>2]=651,l[v+8>>2]=1579,vr(C,812,v),Je(C)),7&a){l[g>>2]=866,l[g+4>>2]=2506,l[g+8>>2]=1232,vr(C,812,g),Je(C);break}Xe(a,0,0,1,0);break}}while(0);if(a=w<<2,u=0|Pe(a+8|0,0),u){g=u+8|0,l[u+4>>2]=w,l[u>>2]=~w,l[f>>2]=g,u=g;break}l[f>>2]=0,n=0;break e}u=e+168|0,a=w<<2,f=u,u=0|l[u>>2]}while(0);He(0|u,-1,0|a),k=e+176|0,A=1;do{if(0|l[F+(A<<2)>>2]&&(p=y-A|0,v=1<<p,a=A+-1|0,u=0|l[N+(a<<2)>>2],a>>>0>=16&&(l[P>>2]=866,l[P+4>>2]=1960,l[P+8>>2]=1453,vr(C,812,P),Je(C)),r=0|l[e+28+(a<<2)>>2],r=0==(0|r)?-1:(r+-1|0)>>>(16-A|0),u>>>0<=r>>>0)){T=(0|l[e+96+(a<<2)>>2])-u|0,M=A<<16;do{a=0|_[(0|l[k>>2])+(T+u<<1)>>1],(0|c[i+a>>0])!=(0|A)&&(l[I>>2]=866,l[I+4>>2]=2322,l[I+8>>2]=1019,vr(C,812,I),Je(C)),b=u<<p,h=a|M,d=0;do{E=d+b|0,E>>>0>=w>>>0&&(l[L>>2]=866,l[L+4>>2]=2328,l[L+8>>2]=1053,vr(C,812,L),Je(C)),a=0|l[f>>2],-1!=(0|l[a+(E<<2)>>2])&&(l[D>>2]=866,l[D+4>>2]=2330,l[D+8>>2]=1076,vr(C,812,D),Je(C),a=0|l[f>>2]),l[a+(E<<2)>>2]=h,d=d+1|0}while(d>>>0<v>>>0);u=u+1|0}while(u>>>0<=r>>>0)}A=A+1|0}while(y>>>0>=A>>>0)}a=e+96|0,l[a>>2]=(0|l[a>>2])-(0|l[N>>2]),a=e+100|0,l[a>>2]=(0|l[a>>2])-(0|l[N+4>>2]),a=e+104|0,l[a>>2]=(0|l[a>>2])-(0|l[N+8>>2]),a=e+108|0,l[a>>2]=(0|l[a>>2])-(0|l[N+12>>2]),a=e+112|0,l[a>>2]=(0|l[a>>2])-(0|l[N+16>>2]),a=e+116|0,l[a>>2]=(0|l[a>>2])-(0|l[N+20>>2]),a=e+120|0,l[a>>2]=(0|l[a>>2])-(0|l[N+24>>2]),a=e+124|0,l[a>>2]=(0|l[a>>2])-(0|l[N+28>>2]),a=e+128|0,l[a>>2]=(0|l[a>>2])-(0|l[N+32>>2]),a=e+132|0,l[a>>2]=(0|l[a>>2])-(0|l[N+36>>2]),a=e+136|0,l[a>>2]=(0|l[a>>2])-(0|l[N+40>>2]),a=e+140|0,l[a>>2]=(0|l[a>>2])-(0|l[N+44>>2]),a=e+144|0,l[a>>2]=(0|l[a>>2])-(0|l[N+48>>2]),a=e+148|0,l[a>>2]=(0|l[a>>2])-(0|l[N+52>>2]),a=e+152|0,l[a>>2]=(0|l[a>>2])-(0|l[N+56>>2]),a=e+156|0,l[a>>2]=(0|l[a>>2])-(0|l[N+60>>2]),a=e+16|0,l[a>>2]=0,u=e+20|0,l[u>>2]=c[O>>0];r:do{if(S){do{if(!n)break r;D=n,n=n+-1|0}while(!(0|l[F+(D<<2)>>2]));if(l[a>>2]=l[e+28+(n<<2)>>2],n=y+1|0,l[u>>2]=n,n>>>0<=s>>>0){while(1){if(0|l[F+(n<<2)>>2])break;if(n=n+1|0,n>>>0>s>>>0)break r}l[u>>2]=n}}}while(0);l[e+92>>2]=-1,l[e+160>>2]=1048575,l[e+12>>2]=32-(0|l[R>>2]),n=1}}while(0);return F=n,m=H,0|F}function W(e,r,i,n,a,o,u,f){e|=0,r|=0,i|=0,n|=0,a|=0,o|=0,u|=0,f|=0;var s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0,ae=0,oe=0,ue=0,le=0,fe=0;if(ue=m,m=m+656|0,ae=ue+112|0,ie=ue+96|0,re=ue+80|0,ee=ue+64|0,$=ue+48|0,oe=ue+32|0,ne=ue+16|0,Q=ue,Z=ue+144|0,q=ue+128|0,B=e+240|0,Y=0|l[B>>2],X=e+256|0,K=0|l[X>>2],J=0|t[17+(0|l[e+88>>2])>>0],V=255&J,G=n>>>2,!(J<<24>>24))return m=ue,1;W=0==(0|f),z=u+-1|0,j=z<<4,J=f+-1|0,L=0!=(1&o|0),D=n<<1,F=e+92|0,H=e+116|0,U=e+140|0,x=e+236|0,I=0!=(1&a|0),P=e+188|0,g=e+252|0,O=G+1|0,C=G+2|0,N=G+3|0,R=0,o=0,i=0,a=1;do{if(!W){S=0|l[r+(R<<2)>>2],y=0;while(1){if(p=1&y,_=0==(0|p),k=(p<<5^32)-16|0,p=(p<<1^2)-1|0,A=_?u:-1,s=_?0:z,e=(0|y)==(0|J),w=L&e,(0|s)!=(0|A)){M=L&e^1,T=_?S:S+j|0;while(1){1==(0|a)&&(a=512|te(F,H)),b=7&a,a>>>=3,_=0|c[1539+b>>0],e=0;do{d=(0|te(F,U))+i|0,h=d-Y|0,E=h>>31,i=E&d|h&~E,(0|l[B>>2])>>>0<=i>>>0&&(l[Q>>2]=866,l[Q+4>>2]=910,l[Q+8>>2]=1497,vr(Z,812,Q),Je(Z)),l[q+(e<<2)>>2]=l[(0|l[x>>2])+(i<<2)>>2],e=e+1|0}while(e>>>0<_>>>0);if(E=I&(0|s)==(0|z),w|E){h=0;do{e=T+(0|v(h,n))|0,d=0==(0|h)|M,_=h<<1,fe=(0|te(F,P))+o|0,le=fe-K|0,o=le>>31,o=o&fe|le&~o;do{if(E){if(!d){le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o;break}l[e>>2]=l[q+((0|c[1547+(b<<2)+_>>0])<<2)>>2],(0|l[X>>2])>>>0<=o>>>0&&(l[ie>>2]=866,l[ie+4>>2]=910,l[ie+8>>2]=1497,vr(Z,812,ie),Je(Z)),l[e+4>>2]=l[(0|l[g>>2])+(o<<2)>>2],le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o}else d&&(l[e>>2]=l[q+((0|c[1547+(b<<2)+_>>0])<<2)>>2],(0|l[X>>2])>>>0<=o>>>0&&(l[re>>2]=866,l[re+4>>2]=910,l[re+8>>2]=1497,vr(Z,812,re),Je(Z)),l[e+4>>2]=l[(0|l[g>>2])+(o<<2)>>2]),e=e+8|0,le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o,d&&(l[e>>2]=l[q+((0|c[1547+(b<<2)+(1|_)>>0])<<2)>>2],(0|l[X>>2])>>>0<=o>>>0&&(l[ae>>2]=866,l[ae+4>>2]=910,l[ae+8>>2]=1497,vr(Z,812,ae),Je(Z)),l[e+4>>2]=l[(0|l[g>>2])+(o<<2)>>2])}while(0);h=h+1|0}while(2!=(0|h))}else l[T>>2]=l[q+((0|c[1547+(b<<2)>>0])<<2)>>2],le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o,(0|l[X>>2])>>>0<=o>>>0&&(l[ne>>2]=866,l[ne+4>>2]=910,l[ne+8>>2]=1497,vr(Z,812,ne),Je(Z)),l[T+4>>2]=l[(0|l[g>>2])+(o<<2)>>2],l[T+8>>2]=l[q+((0|c[1547+(b<<2)+1>>0])<<2)>>2],le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o,(0|l[X>>2])>>>0<=o>>>0&&(l[oe>>2]=866,l[oe+4>>2]=910,l[oe+8>>2]=1497,vr(Z,812,oe),Je(Z)),l[T+12>>2]=l[(0|l[g>>2])+(o<<2)>>2],l[T+(G<<2)>>2]=l[q+((0|c[1547+(b<<2)+2>>0])<<2)>>2],le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o,(0|l[X>>2])>>>0<=o>>>0&&(l[$>>2]=866,l[$+4>>2]=910,l[$+8>>2]=1497,vr(Z,812,$),Je(Z)),l[T+(O<<2)>>2]=l[(0|l[g>>2])+(o<<2)>>2],l[T+(C<<2)>>2]=l[q+((0|c[1547+(b<<2)+3>>0])<<2)>>2],le=(0|te(F,P))+o|0,fe=le-K|0,o=fe>>31,o=o&le|fe&~o,(0|l[X>>2])>>>0<=o>>>0&&(l[ee>>2]=866,l[ee+4>>2]=910,l[ee+8>>2]=1497,vr(Z,812,ee),Je(Z)),l[T+(N<<2)>>2]=l[(0|l[g>>2])+(o<<2)>>2];if(s=p+s|0,(0|s)==(0|A))break;T=T+k|0}}if(y=y+1|0,(0|y)==(0|f))break;S=S+D|0}}R=R+1|0}while((0|R)!=(0|V));return m=ue,1}function z(e,r,i,n,a,o,u,f){e|=0,r|=0,i|=0,n|=0,a|=0,o|=0,u|=0,f|=0;var s=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0,ae=0,oe=0,ue=0,le=0,fe=0,ce=0,se=0;if(se=m,m=m+640|0,le=se+80|0,ue=se+64|0,oe=se+48|0,ce=se+32|0,fe=se+16|0,ae=se,ie=se+128|0,ne=se+112|0,Y=se+96|0,X=e+272|0,K=0|l[X>>2],re=0|l[e+88>>2],V=(0|c[re+63>>0])<<8|0|c[re+64>>0],re=0|t[re+17>>0],G=255&re,!(re<<24>>24))return m=se,1;W=0==(0|f),z=u+-1|0,j=z<<5,J=f+-1|0,Z=n<<1,q=e+92|0,Q=e+116|0,$=e+164|0,ee=e+268|0,re=e+212|0,B=0==(1&a|0),x=0==(1&o|0),U=e+288|0,H=e+284|0,F=0,e=0,o=0,a=0,i=0,s=1;do{if(!W){L=0|l[r+(F<<2)>>2],D=0;while(1){if(I=1&D,h=0==(0|I),P=(I<<6^64)-32|0,I=(I<<1^2)-1|0,C=h?u:-1,d=h?0:z,(0|d)!=(0|C)){N=x|(0|D)!=(0|J),O=h?L:L+j|0;while(1){1==(0|s)&&(s=512|te(q,Q)),g=7&s,s>>>=3,E=0|c[1539+g>>0],h=0;do{S=(0|te(q,$))+i|0,y=S-K|0,R=y>>31,i=R&S|y&~R,(0|l[X>>2])>>>0<=i>>>0&&(l[ae>>2]=866,l[ae+4>>2]=910,l[ae+8>>2]=1497,vr(ie,812,ae),Je(ie)),l[ne+(h<<2)>>2]=_[(0|l[ee>>2])+(i<<1)>>1],h=h+1|0}while(h>>>0<E>>>0);h=0;do{S=(0|te(q,$))+o|0,y=S-K|0,R=y>>31,o=R&S|y&~R,(0|l[X>>2])>>>0<=o>>>0&&(l[fe>>2]=866,l[fe+4>>2]=910,l[fe+8>>2]=1497,vr(ie,812,fe),Je(ie)),l[Y+(h<<2)>>2]=_[(0|l[ee>>2])+(o<<1)>>1],h=h+1|0}while(h>>>0<E>>>0);R=B|(0|d)!=(0|z),S=0,y=O;while(1){if(p=N|0==(0|S),v=S<<1,R){A=0,k=y;while(1){if(w=(0|te(q,re))+a|0,M=w-V|0,a=M>>31,a=a&w|M&~a,M=(0|te(q,re))+e|0,w=M-V|0,e=w>>31,e=e&M|w&~e,p&&(M=0|c[A+v+(1547+(g<<2))>>0],E=3*a|0,h=0|l[U>>2],h>>>0<=E>>>0&&(l[ce>>2]=866,l[ce+4>>2]=910,l[ce+8>>2]=1497,vr(ie,812,ce),Je(ie),h=0|l[U>>2]),b=0|l[H>>2],E=b+(E<<1)|0,T=3*e|0,h>>>0>T>>>0?h=b:(l[oe>>2]=866,l[oe+4>>2]=910,l[oe+8>>2]=1497,vr(ie,812,oe),Je(ie),h=0|l[H>>2]),w=h+(T<<1)|0,l[k>>2]=(0|_[E>>1])<<16|l[ne+(M<<2)>>2],l[k+4>>2]=(0|_[E+4>>1])<<16|0|_[E+2>>1],l[k+8>>2]=(0|_[w>>1])<<16|l[Y+(M<<2)>>2],l[k+12>>2]=(0|_[w+4>>1])<<16|0|_[w+2>>1]),A=A+1|0,2==(0|A))break;k=k+16|0}}else{w=1^p,p=1547+(g<<2)+v|0,A=0,k=y;while(1){if(v=(0|te(q,re))+a|0,M=v-V|0,a=M>>31,a=a&v|M&~a,M=(0|te(q,re))+e|0,v=M-V|0,e=v>>31,e=e&M|v&~e,0!=(0|A)|w||(M=0|c[p>>0],E=3*a|0,h=0|l[U>>2],h>>>0<=E>>>0&&(l[ue>>2]=866,l[ue+4>>2]=910,l[ue+8>>2]=1497,vr(ie,812,ue),Je(ie),h=0|l[U>>2]),b=0|l[H>>2],E=b+(E<<1)|0,T=3*e|0,h>>>0>T>>>0?h=b:(l[le>>2]=866,l[le+4>>2]=910,l[le+8>>2]=1497,vr(ie,812,le),Je(ie),h=0|l[H>>2]),v=h+(T<<1)|0,l[k>>2]=(0|_[E>>1])<<16|l[ne+(M<<2)>>2],l[k+4>>2]=(0|_[E+4>>1])<<16|0|_[E+2>>1],l[k+8>>2]=(0|_[v>>1])<<16|l[Y+(M<<2)>>2],l[k+12>>2]=(0|_[v+4>>1])<<16|0|_[v+2>>1]),A=A+1|0,2==(0|A))break;k=k+16|0}}if(S=S+1|0,2==(0|S))break;y=y+n|0}if(d=I+d|0,(0|d)==(0|C))break;O=O+P|0}}if(D=D+1|0,(0|D)==(0|f))break;L=L+Z|0}}F=F+1|0}while((0|F)!=(0|G));return m=se,1}function j(e){e|=0;var r=0,i=0,n=0,t=0,a=0,o=0,u=0,f=0;if(e){i=e+-8|0,t=0|l[1148],e=0|l[e+-4>>2],r=-8&e,f=i+r|0;do{if(1&e)u=i,o=i;else{if(n=0|l[i>>2],!(3&e))return;if(o=i+(0-n)|0,a=n+r|0,o>>>0<t>>>0)return;if((0|o)==(0|l[1149])){if(e=f+4|0,r=0|l[e>>2],3!=(3&r|0)){u=o,r=a;break}return l[1146]=a,l[e>>2]=-2&r,l[o+4>>2]=1|a,void(l[o+a>>2]=a)}if(i=n>>>3,n>>>0<256){if(e=0|l[o+8>>2],r=0|l[o+12>>2],(0|r)==(0|e)){l[1144]=l[1144]&~(1<<i),u=o,r=a;break}l[e+12>>2]=r,l[r+8>>2]=e,u=o,r=a;break}t=0|l[o+24>>2],e=0|l[o+12>>2];do{if((0|e)==(0|o)){if(i=o+16|0,r=i+4|0,e=0|l[r>>2],!e){if(e=0|l[i>>2],!e){e=0;break}r=i}while(1)if(i=e+20|0,n=0|l[i>>2],0|n)e=n,r=i;else{if(i=e+16|0,n=0|l[i>>2],!n)break;e=n,r=i}l[r>>2]=0}else u=0|l[o+8>>2],l[u+12>>2]=e,l[e+8>>2]=u}while(0);if(t){if(r=0|l[o+28>>2],i=4880+(r<<2)|0,(0|o)==(0|l[i>>2])){if(l[i>>2]=e,!e){l[1145]=l[1145]&~(1<<r),u=o,r=a;break}}else if(l[t+16+(((0|l[t+16>>2])!=(0|o)&1)<<2)>>2]=e,!e){u=o,r=a;break}l[e+24>>2]=t,r=o+16|0,i=0|l[r>>2],0|i&&(l[e+16>>2]=i,l[i+24>>2]=e),r=0|l[r+4>>2],r?(l[e+20>>2]=r,l[r+24>>2]=e,u=o,r=a):(u=o,r=a)}else u=o,r=a}}while(0);if(!(o>>>0>=f>>>0)&&(e=f+4|0,n=0|l[e>>2],1&n)){if(2&n)l[e>>2]=-2&n,l[u+4>>2]=1|r,l[o+r>>2]=r,t=r;else{if(e=0|l[1149],(0|f)==(0|l[1150])){if(f=(0|l[1147])+r|0,l[1147]=f,l[1150]=u,l[u+4>>2]=1|f,(0|u)!=(0|e))return;return l[1149]=0,void(l[1146]=0)}if((0|f)==(0|e))return f=(0|l[1146])+r|0,l[1146]=f,l[1149]=o,l[u+4>>2]=1|f,void(l[o+f>>2]=f);t=(-8&n)+r|0,i=n>>>3;do{if(n>>>0<256){if(r=0|l[f+8>>2],e=0|l[f+12>>2],(0|e)==(0|r)){l[1144]=l[1144]&~(1<<i);break}l[r+12>>2]=e,l[e+8>>2]=r;break}a=0|l[f+24>>2],e=0|l[f+12>>2];do{if((0|e)==(0|f)){if(i=f+16|0,r=i+4|0,e=0|l[r>>2],!e){if(e=0|l[i>>2],!e){i=0;break}r=i}while(1)if(i=e+20|0,n=0|l[i>>2],0|n)e=n,r=i;else{if(i=e+16|0,n=0|l[i>>2],!n)break;e=n,r=i}l[r>>2]=0,i=e}else i=0|l[f+8>>2],l[i+12>>2]=e,l[e+8>>2]=i,i=e}while(0);if(0|a){if(e=0|l[f+28>>2],r=4880+(e<<2)|0,(0|f)==(0|l[r>>2])){if(l[r>>2]=i,!i){l[1145]=l[1145]&~(1<<e);break}}else if(l[a+16+(((0|l[a+16>>2])!=(0|f)&1)<<2)>>2]=i,!i)break;l[i+24>>2]=a,e=f+16|0,r=0|l[e>>2],0|r&&(l[i+16>>2]=r,l[r+24>>2]=i),e=0|l[e+4>>2],0|e&&(l[i+20>>2]=e,l[e+24>>2]=i)}}while(0);if(l[u+4>>2]=1|t,l[o+t>>2]=t,(0|u)==(0|l[1149]))return void(l[1146]=t)}if(e=t>>>3,t>>>0<256)return i=4616+(e<<1<<2)|0,r=0|l[1144],e=1<<e,r&e?(r=i+8|0,e=0|l[r>>2]):(l[1144]=r|e,e=i,r=i+8|0),l[r>>2]=u,l[e+12>>2]=u,l[u+8>>2]=e,void(l[u+12>>2]=i);e=t>>>8,e?t>>>0>16777215?e=31:(o=(e+1048320|0)>>>16&8,f=e<<o,a=(f+520192|0)>>>16&4,f<<=a,e=(f+245760|0)>>>16&2,e=14-(a|o|e)+(f<<e>>>15)|0,e=t>>>(e+7|0)&1|e<<1):e=0,n=4880+(e<<2)|0,l[u+28>>2]=e,l[u+20>>2]=0,l[u+16>>2]=0,r=0|l[1145],i=1<<e;do{if(r&i){r=t<<(31==(0|e)?0:25-(e>>>1)|0),i=0|l[n>>2];while(1){if((-8&l[i+4>>2]|0)==(0|t)){e=73;break}if(n=i+16+(r>>>31<<2)|0,e=0|l[n>>2],!e){e=72;break}r<<=1,i=e}if(72==(0|e)){l[n>>2]=u,l[u+24>>2]=i,l[u+12>>2]=u,l[u+8>>2]=u;break}if(73==(0|e)){o=i+8|0,f=0|l[o>>2],l[f+12>>2]=u,l[o>>2]=u,l[u+8>>2]=f,l[u+12>>2]=i,l[u+24>>2]=0;break}}else l[1145]=r|i,l[n>>2]=u,l[u+24>>2]=n,l[u+12>>2]=u,l[u+8>>2]=u}while(0);if(f=(0|l[1152])-1|0,l[1152]=f,!f){e=5032;while(1){if(e=0|l[e>>2],!e)break;e=e+8|0}l[1152]=-1}}}}function J(e,r,i,n,a,o,u,f){e|=0,r|=0,i|=0,n|=0,a|=0,o|=0,u|=0,f|=0;var s=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0,re=0,ie=0,ne=0,ae=0,oe=0,ue=0,le=0,fe=0,ce=0,se=0,_e=0,de=0,he=0,Ee=0,be=0;if(be=m,m=m+640|0,de=be+80|0,_e=be+64|0,se=be+48|0,Ee=be+32|0,he=be+16|0,ce=be,le=be+128|0,fe=be+112|0,x=be+96|0,B=e+240|0,Y=0|l[B>>2],X=e+256|0,K=0|l[X>>2],V=e+272|0,G=0|l[V>>2],ue=0|l[e+88>>2],W=(0|c[ue+63>>0])<<8|0|c[ue+64>>0],ue=0|t[ue+17>>0],z=255&ue,!(ue<<24>>24))return m=be,1;j=0==(0|f),J=u+-1|0,Z=J<<5,q=f+-1|0,Q=n<<1,$=e+92|0,ee=e+116|0,re=e+164|0,ie=e+268|0,ne=e+140|0,ae=e+236|0,oe=e+212|0,ue=e+188|0,U=0==(1&a|0),H=0==(1&o|0),D=e+288|0,F=e+284|0,L=e+252|0,I=0,e=0,o=0,a=0,i=0,s=1;do{if(!j){N=0|l[r+(I<<2)>>2],P=0;while(1){if(C=1&P,h=0==(0|C),O=(C<<6^64)-32|0,C=(C<<1^2)-1|0,R=h?u:-1,d=h?0:J,(0|d)!=(0|R)){g=H|(0|P)!=(0|q),y=h?N:N+Z|0;while(1){1==(0|s)&&(s=512|te($,ee)),S=7&s,s>>>=3,E=0|c[1539+S>>0],h=0;do{p=(0|te($,re))+o|0,v=p-G|0,w=v>>31,o=w&p|v&~w,(0|l[V>>2])>>>0<=o>>>0&&(l[ce>>2]=866,l[ce+4>>2]=910,l[ce+8>>2]=1497,vr(le,812,ce),Je(le)),l[x+(h<<2)>>2]=_[(0|l[ie>>2])+(o<<1)>>1],h=h+1|0}while(h>>>0<E>>>0);h=0;do{p=(0|te($,ne))+i|0,v=p-Y|0,w=v>>31,i=w&p|v&~w,(0|l[B>>2])>>>0<=i>>>0&&(l[he>>2]=866,l[he+4>>2]=910,l[he+8>>2]=1497,vr(le,812,he),Je(le)),l[fe+(h<<2)>>2]=l[(0|l[ae>>2])+(i<<2)>>2],h=h+1|0}while(h>>>0<E>>>0);w=U|(0|d)!=(0|J),p=0,v=y;while(1){if(M=g|0==(0|p),A=p<<1,w){b=0,T=v;while(1){if(k=(0|te($,oe))+e|0,E=k-W|0,e=E>>31,e=e&k|E&~e,E=(0|te($,ue))+a|0,k=E-K|0,a=k>>31,a=a&E|k&~a,M&&(h=0|c[b+A+(1547+(S<<2))>>0],E=3*e|0,(0|l[D>>2])>>>0<=E>>>0&&(l[Ee>>2]=866,l[Ee+4>>2]=910,l[Ee+8>>2]=1497,vr(le,812,Ee),Je(le)),k=(0|l[F>>2])+(E<<1)|0,l[T>>2]=(0|_[k>>1])<<16|l[x+(h<<2)>>2],l[T+4>>2]=(0|_[k+4>>1])<<16|0|_[k+2>>1],l[T+8>>2]=l[fe+(h<<2)>>2],(0|l[X>>2])>>>0<=a>>>0&&(l[se>>2]=866,l[se+4>>2]=910,l[se+8>>2]=1497,vr(le,812,se),Je(le)),l[T+12>>2]=l[(0|l[L>>2])+(a<<2)>>2]),b=b+1|0,2==(0|b))break;T=T+16|0}}else{k=1^M,M=1547+(S<<2)+A|0,b=0,T=v;while(1){if(A=(0|te($,oe))+e|0,E=A-W|0,e=E>>31,e=e&A|E&~e,E=(0|te($,ue))+a|0,A=E-K|0,a=A>>31,a=a&E|A&~a,0!=(0|b)|k||(h=0|c[M>>0],E=3*e|0,(0|l[D>>2])>>>0<=E>>>0&&(l[_e>>2]=866,l[_e+4>>2]=910,l[_e+8>>2]=1497,vr(le,812,_e),Je(le)),A=(0|l[F>>2])+(E<<1)|0,l[T>>2]=(0|_[A>>1])<<16|l[x+(h<<2)>>2],l[T+4>>2]=(0|_[A+4>>1])<<16|0|_[A+2>>1],l[T+8>>2]=l[fe+(h<<2)>>2],(0|l[X>>2])>>>0<=a>>>0&&(l[de>>2]=866,l[de+4>>2]=910,l[de+8>>2]=1497,vr(le,812,de),Je(le)),l[T+12>>2]=l[(0|l[L>>2])+(a<<2)>>2]),b=b+1|0,2==(0|b))break;T=T+16|0}}if(p=p+1|0,2==(0|p))break;v=v+n|0}if(d=C+d|0,(0|d)==(0|R))break;y=y+O|0}}if(P=P+1|0,(0|P)==(0|f))break;N=N+Q|0}}I=I+1|0}while((0|I)!=(0|z));return m=be,1}function Z(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0;f=e+r|0,i=0|l[e+4>>2];do{if(1&i)u=e,i=r;else{if(n=0|l[e>>2],!(3&i))return;if(a=e+(0-n)|0,o=n+r|0,(0|a)==(0|l[1149])){if(e=f+4|0,i=0|l[e>>2],3!=(3&i|0)){u=a,i=o;break}return l[1146]=o,l[e>>2]=-2&i,l[a+4>>2]=1|o,void(l[a+o>>2]=o)}if(r=n>>>3,n>>>0<256){if(e=0|l[a+8>>2],i=0|l[a+12>>2],(0|i)==(0|e)){l[1144]=l[1144]&~(1<<r),u=a,i=o;break}l[e+12>>2]=i,l[i+8>>2]=e,u=a,i=o;break}t=0|l[a+24>>2],e=0|l[a+12>>2];do{if((0|e)==(0|a)){if(r=a+16|0,i=r+4|0,e=0|l[i>>2],!e){if(e=0|l[r>>2],!e){e=0;break}i=r}while(1)if(r=e+20|0,n=0|l[r>>2],0|n)e=n,i=r;else{if(r=e+16|0,n=0|l[r>>2],!n)break;e=n,i=r}l[i>>2]=0}else u=0|l[a+8>>2],l[u+12>>2]=e,l[e+8>>2]=u}while(0);if(t){if(i=0|l[a+28>>2],r=4880+(i<<2)|0,(0|a)==(0|l[r>>2])){if(l[r>>2]=e,!e){l[1145]=l[1145]&~(1<<i),u=a,i=o;break}}else if(l[t+16+(((0|l[t+16>>2])!=(0|a)&1)<<2)>>2]=e,!e){u=a,i=o;break}l[e+24>>2]=t,i=a+16|0,r=0|l[i>>2],0|r&&(l[e+16>>2]=r,l[r+24>>2]=e),i=0|l[i+4>>2],i?(l[e+20>>2]=i,l[i+24>>2]=e,u=a,i=o):(u=a,i=o)}else u=a,i=o}}while(0);if(e=f+4|0,n=0|l[e>>2],2&n)l[e>>2]=-2&n,l[u+4>>2]=1|i,l[u+i>>2]=i;else{if(e=0|l[1149],(0|f)==(0|l[1150])){if(f=(0|l[1147])+i|0,l[1147]=f,l[1150]=u,l[u+4>>2]=1|f,(0|u)!=(0|e))return;return l[1149]=0,void(l[1146]=0)}if((0|f)==(0|e))return f=(0|l[1146])+i|0,l[1146]=f,l[1149]=u,l[u+4>>2]=1|f,void(l[u+f>>2]=f);a=(-8&n)+i|0,r=n>>>3;do{if(n>>>0<256){if(i=0|l[f+8>>2],e=0|l[f+12>>2],(0|e)==(0|i)){l[1144]=l[1144]&~(1<<r);break}l[i+12>>2]=e,l[e+8>>2]=i;break}t=0|l[f+24>>2],e=0|l[f+12>>2];do{if((0|e)==(0|f)){if(r=f+16|0,i=r+4|0,e=0|l[i>>2],!e){if(e=0|l[r>>2],!e){r=0;break}i=r}while(1)if(r=e+20|0,n=0|l[r>>2],0|n)e=n,i=r;else{if(r=e+16|0,n=0|l[r>>2],!n)break;e=n,i=r}l[i>>2]=0,r=e}else r=0|l[f+8>>2],l[r+12>>2]=e,l[e+8>>2]=r,r=e}while(0);if(0|t){if(e=0|l[f+28>>2],i=4880+(e<<2)|0,(0|f)==(0|l[i>>2])){if(l[i>>2]=r,!r){l[1145]=l[1145]&~(1<<e);break}}else if(l[t+16+(((0|l[t+16>>2])!=(0|f)&1)<<2)>>2]=r,!r)break;l[r+24>>2]=t,e=f+16|0,i=0|l[e>>2],0|i&&(l[r+16>>2]=i,l[i+24>>2]=r),e=0|l[e+4>>2],0|e&&(l[r+20>>2]=e,l[e+24>>2]=r)}}while(0);if(l[u+4>>2]=1|a,l[u+a>>2]=a,(0|u)==(0|l[1149]))return void(l[1146]=a);i=a}if(e=i>>>3,i>>>0<256)return r=4616+(e<<1<<2)|0,i=0|l[1144],e=1<<e,i&e?(i=r+8|0,e=0|l[i>>2]):(l[1144]=i|e,e=r,i=r+8|0),l[i>>2]=u,l[e+12>>2]=u,l[u+8>>2]=e,void(l[u+12>>2]=r);if(e=i>>>8,e?i>>>0>16777215?e=31:(o=(e+1048320|0)>>>16&8,f=e<<o,a=(f+520192|0)>>>16&4,f<<=a,e=(f+245760|0)>>>16&2,e=14-(a|o|e)+(f<<e>>>15)|0,e=i>>>(e+7|0)&1|e<<1):e=0,t=4880+(e<<2)|0,l[u+28>>2]=e,l[u+20>>2]=0,l[u+16>>2]=0,r=0|l[1145],n=1<<e,!(r&n))return l[1145]=r|n,l[t>>2]=u,l[u+24>>2]=t,l[u+12>>2]=u,void(l[u+8>>2]=u);r=i<<(31==(0|e)?0:25-(e>>>1)|0),n=0|l[t>>2];while(1){if((-8&l[n+4>>2]|0)==(0|i)){e=69;break}if(t=n+16+(r>>>31<<2)|0,e=0|l[t>>2],!e){e=68;break}r<<=1,n=e}return 68==(0|e)?(l[t>>2]=u,l[u+24>>2]=n,l[u+12>>2]=u,void(l[u+8>>2]=u)):69==(0|e)?(o=n+8|0,f=0|l[o>>2],l[f+12>>2]=u,l[o>>2]=u,l[u+8>>2]=f,l[u+12>>2]=n,void(l[u+24>>2]=0)):void 0}function q(e){e|=0;var r=0,i=0,n=0,a=0,u=0,f=0,s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0,G=0,W=0,z=0;if(G=m,m=m+2416|0,f=G,u=G+1904|0,K=G+1880|0,B=G+980|0,Y=G+80|0,X=G+16|0,i=0|l[e+88>>2],U=(0|c[i+63>>0])<<8|0|c[i+64>>0],x=e+92|0,r=(0|l[e+4>>2])+((0|c[i+58>>0])<<8|(0|c[i+57>>0])<<16|0|c[i+59>>0])|0,i=(0|c[i+61>>0])<<8|(0|c[i+60>>0])<<16|0|c[i+62>>0],!i)return K=0,m=G,0|K;if(l[x>>2]=r,l[e+96>>2]=r,l[e+104>>2]=i,l[e+100>>2]=r+i,l[e+108>>2]=0,l[e+112>>2]=0,l[K+20>>2]=0,l[K>>2]=0,l[K+4>>2]=0,l[K+8>>2]=0,l[K+12>>2]=0,t[K+16>>0]=0,0|V(x,K)){r=0,i=-7,n=-7;while(1){if(l[B+(r<<2)>>2]=n,l[Y+(r<<2)>>2]=i,a=(0|n)>6,r=r+1|0,225==(0|r))break;i=(1&a)+i|0,n=a?-7:n+1|0}r=X,i=r+64|0;do{l[r>>2]=0,r=r+4|0}while((0|r)<(0|i));a=e+284|0,i=3*U|0,n=e+288|0,r=0|l[n>>2];e:do{if((0|r)==(0|i))s=13;else{if(r>>>0<=i>>>0){do{if((0|l[e+292>>2])>>>0<i>>>0){if(0|le(a,i,(r+1|0)==(0|i),2,0)){r=0|l[n>>2];break}t[e+296>>0]=1,r=0;break e}}while(0);He((0|l[a>>2])+(r<<1)|0,0,i-r<<1|0)}l[n>>2]=i,s=13}}while(0);do{if(13==(0|s)){if(!U){l[f>>2]=866,l[f+4>>2]=910,l[f+8>>2]=1497,vr(u,812,f),Je(u),r=1;break}v=X+4|0,w=X+8|0,S=X+12|0,y=X+16|0,R=X+20|0,g=X+24|0,O=X+28|0,C=X+32|0,N=X+36|0,P=X+40|0,I=X+44|0,L=X+48|0,D=X+52|0,F=X+56|0,H=X+60|0,p=0,r=0|l[a>>2],i=0|l[X>>2],n=0|l[v>>2],a=0|l[w>>2],e=0|l[S>>2],u=0|l[y>>2],f=0|l[R>>2],s=0|l[g>>2],_=0|l[O>>2],d=0|l[C>>2],h=0|l[N>>2],E=0|l[P>>2],b=0|l[I>>2],T=0,M=0,A=0,k=0;while(1){if(z=0|te(x,K),i=i+(0|l[B+(z<<2)>>2])&7,n=n+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),a=a+(0|l[B+(z<<2)>>2])&7,e=e+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),u=u+(0|l[B+(z<<2)>>2])&7,f=f+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),s=s+(0|l[B+(z<<2)>>2])&7,_=_+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),d=d+(0|l[B+(z<<2)>>2])&7,h=h+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),E=E+(0|l[B+(z<<2)>>2])&7,b=b+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),T=T+(0|l[B+(z<<2)>>2])&7,M=M+(0|l[Y+(z<<2)>>2])&7,z=0|te(x,K),A=A+(0|l[B+(z<<2)>>2])&7,k=k+(0|l[Y+(z<<2)>>2])&7,z=0|c[1445+f>>0],o[r>>1]=(0|c[1445+n>>0])<<3|0|c[1445+i>>0]|(0|c[1445+a>>0])<<6|(0|c[1445+e>>0])<<9|(0|c[1445+u>>0])<<12|z<<15,W=0|c[1445+E>>0],o[r+2>>1]=(0|c[1445+s>>0])<<2|z>>>1|(0|c[1445+_>>0])<<5|(0|c[1445+d>>0])<<8|(0|c[1445+h>>0])<<11|W<<14,o[r+4>>1]=(0|c[1445+b>>0])<<1|W>>>2|(0|c[1445+T>>0])<<4|(0|c[1445+M>>0])<<7|(0|c[1445+A>>0])<<10|(0|c[1445+k>>0])<<13,p=p+1|0,p>>>0>=U>>>0)break;r=r+6|0}l[X>>2]=i,l[v>>2]=n,l[w>>2]=a,l[S>>2]=e,l[y>>2]=u,l[R>>2]=f,l[g>>2]=s,l[O>>2]=_,l[C>>2]=d,l[N>>2]=h,l[P>>2]=E,l[I>>2]=b,l[L>>2]=T,l[D>>2]=M,l[F>>2]=A,l[H>>2]=k,r=1}}while(0)}else r=0;return Ne(K),z=r,m=G,0|z}function Q(e){e|=0;var r=0,i=0,n=0,a=0,o=0,u=0,f=0,s=0,_=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0;if(O=m,m=m+1008|0,u=O,o=O+496|0,g=O+472|0,S=O+276|0,y=O+80|0,R=O+16|0,i=0|l[e+88>>2],v=(0|c[i+47>>0])<<8|0|c[i+48>>0],w=e+92|0,r=(0|l[e+4>>2])+((0|c[i+42>>0])<<8|(0|c[i+41>>0])<<16|0|c[i+43>>0])|0,i=(0|c[i+45>>0])<<8|(0|c[i+44>>0])<<16|0|c[i+46>>0],!i)return g=0,m=O,0|g;if(l[w>>2]=r,l[e+96>>2]=r,l[e+104>>2]=i,l[e+100>>2]=r+i,l[e+108>>2]=0,l[e+112>>2]=0,l[g+20>>2]=0,l[g>>2]=0,l[g+4>>2]=0,l[g+8>>2]=0,l[g+12>>2]=0,t[g+16>>0]=0,0|V(w,g)){r=0,i=-3,n=-3;while(1){if(l[S+(r<<2)>>2]=n,l[y+(r<<2)>>2]=i,a=(0|n)>2,r=r+1|0,49==(0|r))break;i=(1&a)+i|0,n=a?-3:n+1|0}r=R,i=r+64|0;do{l[r>>2]=0,r=r+4|0}while((0|r)<(0|i));n=e+252|0,i=e+256|0,r=0|l[i>>2];e:do{if((0|r)==(0|v))f=13;else{if(r>>>0<=v>>>0){do{if((0|l[e+260>>2])>>>0<v>>>0){if(0|le(n,v,(r+1|0)==(0|v),4,0)){r=0|l[i>>2];break}t[e+264>>0]=1,r=0;break e}}while(0);He((0|l[n>>2])+(r<<2)|0,0,v-r<<2|0)}l[i>>2]=v,f=13}}while(0);do{if(13==(0|f)){if(!v){l[u>>2]=866,l[u+4>>2]=910,l[u+8>>2]=1497,vr(o,812,u),Je(o),r=1;break}e=R+4|0,o=R+8|0,u=R+12|0,f=R+16|0,s=R+20|0,_=R+24|0,d=R+28|0,h=R+32|0,E=R+36|0,b=R+40|0,T=R+44|0,M=R+48|0,A=R+52|0,k=R+56|0,p=R+60|0,a=0,r=0|l[n>>2],i=0|l[e>>2],n=0|l[R>>2];while(1){if(X=0|te(w,g),n=n+(0|l[S+(X<<2)>>2])&3,i=i+(0|l[y+(X<<2)>>2])&3,X=0|te(w,g),K=(0|l[o>>2])+(0|l[S+(X<<2)>>2])&3,l[o>>2]=K,X=(0|l[u>>2])+(0|l[y+(X<<2)>>2])&3,l[u>>2]=X,B=0|te(w,g),Y=(0|l[f>>2])+(0|l[S+(B<<2)>>2])&3,l[f>>2]=Y,B=(0|l[s>>2])+(0|l[y+(B<<2)>>2])&3,l[s>>2]=B,U=0|te(w,g),x=(0|l[_>>2])+(0|l[S+(U<<2)>>2])&3,l[_>>2]=x,U=(0|l[d>>2])+(0|l[y+(U<<2)>>2])&3,l[d>>2]=U,F=0|te(w,g),H=(0|l[h>>2])+(0|l[S+(F<<2)>>2])&3,l[h>>2]=H,F=(0|l[E>>2])+(0|l[y+(F<<2)>>2])&3,l[E>>2]=F,L=0|te(w,g),D=(0|l[b>>2])+(0|l[S+(L<<2)>>2])&3,l[b>>2]=D,L=(0|l[T>>2])+(0|l[y+(L<<2)>>2])&3,l[T>>2]=L,P=0|te(w,g),I=(0|l[M>>2])+(0|l[S+(P<<2)>>2])&3,l[M>>2]=I,P=(0|l[A>>2])+(0|l[y+(P<<2)>>2])&3,l[A>>2]=P,C=0|te(w,g),N=(0|l[k>>2])+(0|l[S+(C<<2)>>2])&3,l[k>>2]=N,C=(0|l[p>>2])+(0|l[y+(C<<2)>>2])&3,l[p>>2]=C,l[r>>2]=(0|c[1441+i>>0])<<2|0|c[1441+n>>0]|(0|c[1441+K>>0])<<4|(0|c[1441+X>>0])<<6|(0|c[1441+Y>>0])<<8|(0|c[1441+B>>0])<<10|(0|c[1441+x>>0])<<12|(0|c[1441+U>>0])<<14|(0|c[1441+H>>0])<<16|(0|c[1441+F>>0])<<18|(0|c[1441+D>>0])<<20|(0|c[1441+L>>0])<<22|(0|c[1441+I>>0])<<24|(0|c[1441+P>>0])<<26|(0|c[1441+N>>0])<<28|(0|c[1441+C>>0])<<30,a=a+1|0,a>>>0>=v>>>0)break;r=r+4|0}l[R>>2]=n,l[e>>2]=i,r=1}}while(0)}else r=0;return Ne(g),K=r,m=O,0|K}function $(e,r,i,n,a,o,u,f){e|=0,r|=0,i|=0,n|=0,a|=0,o|=0,u|=0,f|=0;var s=0,d=0,h=0,E=0,b=0,T=0,M=0,A=0,k=0,p=0,v=0,w=0,S=0,y=0,R=0,g=0,O=0,C=0,N=0,P=0,I=0,L=0,D=0,F=0,H=0,U=0,x=0,B=0,Y=0,X=0,K=0,V=0,G=0,W=0,z=0,j=0,J=0,Z=0,q=0,Q=0,$=0,ee=0;if(ee=m,m=m+592|0,q=ee+48|0,$=ee+32|0,Q=ee+16|0,Z=ee,j=ee+80|0,J=ee+64|0,L=e+272|0,D=0|l[L>>2],z=0|l[e+88>>2],F=(0|c[z+63>>0])<<8|0|c[z+64>>0],z=0|t[z+17>>0],H=255&z,!(z<<24>>24))return m=ee,1;U=0==(0|f),x=u+-1|0,B=x<<4,Y=f+-1|0,X=n<<1,K=e+92|0,V=e+116|0,G=e+164|0,W=e+268|0,z=e+212|0,I=0==(1&a|0),P=0==(1&o|0),N=e+288|0,C=e+284|0,O=0,a=0,i=0,o=1;do{if(!U){R=0|l[r+(O<<2)>>2],g=0;while(1){if(y=1&g,s=0==(0|y),S=(y<<5^32)-16|0,y=(y<<1^2)-1|0,v=s?u:-1,e=s?0:x,(0|e)!=(0|v)){w=P|(0|g)!=(0|Y),p=s?R:R+B|0;while(1){1==(0|o)&&(o=512|te(K,V)),k=7&o,o>>>=3,d=0|c[1539+k>>0],s=0;do{T=(0|te(K,G))+i|0,M=T-D|0,A=M>>31,i=A&T|M&~A,(0|l[L>>2])>>>0<=i>>>0&&(l[Z>>2]=866,l[Z+4>>2]=910,l[Z+8>>2]=1497,vr(j,812,Z),Je(j)),l[J+(s<<2)>>2]=_[(0|l[W>>2])+(i<<1)>>1],s=s+1|0}while(s>>>0<d>>>0);A=I|(0|e)!=(0|x),T=0,M=p;while(1){if(b=w|0==(0|T),d=T<<1,s=(0|te(K,z))+a|0,h=s-F|0,E=h>>31,E=E&s|h&~E,A?(b&&(a=0|c[1547+(k<<2)+d>>0],s=3*E|0,(0|l[N>>2])>>>0<=s>>>0&&(l[Q>>2]=866,l[Q+4>>2]=910,l[Q+8>>2]=1497,vr(j,812,Q),Je(j)),h=(0|l[C>>2])+(s<<1)|0,l[M>>2]=(0|_[h>>1])<<16|l[J+(a<<2)>>2],l[M+4>>2]=(0|_[h+4>>1])<<16|0|_[h+2>>1]),h=M+8|0,s=(0|te(K,z))+E|0,E=s-F|0,a=E>>31,a=a&s|E&~a,b&&(s=0|c[1547+(k<<2)+(1|d)>>0],d=3*a|0,(0|l[N>>2])>>>0<=d>>>0&&(l[q>>2]=866,l[q+4>>2]=910,l[q+8>>2]=1497,vr(j,812,q),Je(j)),b=(0|l[C>>2])+(d<<1)|0,l[h>>2]=(0|_[b>>1])<<16|l[J+(s<<2)>>2],l[M+12>>2]=(0|_[b+4>>1])<<16|0|_[b+2>>1])):(b&&(a=0|c[1547+(k<<2)+d>>0],s=3*E|0,(0|l[N>>2])>>>0<=s>>>0&&(l[$>>2]=866,l[$+4>>2]=910,l[$+8>>2]=1497,vr(j,812,$),Je(j)),b=(0|l[C>>2])+(s<<1)|0,l[M>>2]=(0|_[b>>1])<<16|l[J+(a<<2)>>2],l[M+4>>2]=(0|_[b+4>>1])<<16|0|_[b+2>>1]),E=(0|te(K,z))+E|0,b=E-F|0,a=b>>31,a=a&E|b&~a),T=T+1|0,2==(0|T))break;M=M+n|0}if(e=y+e|0,(0|e)==(0|v))break;p=p+S|0}}if(g=g+1|0,(0|g)==(0|f))break;R=R+X|0}}O=O+1|0}while((0|O)!=(0|H));return m=ee,1}function ee(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0;if(s=e,f=r,c=f,o=i,d=n,u=d,!c)return a=0!=(0|t),u?a?(l[t>>2]=0|e,l[t+4>>2]=0&r,d=0,t=0,0|(p=d,t)):(d=0,t=0,0|(p=d,t)):(a&&(l[t>>2]=(s>>>0)%(o>>>0),l[t+4>>2]=0),d=0,t=(s>>>0)/(o>>>0)>>>0,0|(p=d,t));a=0==(0|u);do{if(o){if(!a){if(a=(0|w(0|u))-(0|w(0|c))|0,a>>>0<=31){_=a+1|0,u=31-a|0,r=a-31>>31,o=_,e=s>>>(_>>>0)&r|c<<u,r&=c>>>(_>>>0),a=0,u=s<<u;break}return t?(l[t>>2]=0|e,l[t+4>>2]=f|0&r,d=0,t=0,0|(p=d,t)):(d=0,t=0,0|(p=d,t))}if(a=o-1|0,a&o|0){u=33+(0|w(0|o))-(0|w(0|c))|0,E=64-u|0,_=32-u|0,f=_>>31,h=u-32|0,r=h>>31,o=u,e=_-1>>31&c>>>(h>>>0)|(c<<_|s>>>(u>>>0))&r,r&=c>>>(u>>>0),a=s<<E&f,u=(c<<E|s>>>(h>>>0))&f|s<<_&u-33>>31;break}return 0|t&&(l[t>>2]=a&s,l[t+4>>2]=0),1==(0|o)?(h=f|0&r,E=0|e,0|(p=h,E)):(E=0|cr(0|o),h=c>>>(E>>>0)|0,E=c<<32-E|s>>>(E>>>0)|0,0|(p=h,E))}if(a)return 0|t&&(l[t>>2]=(c>>>0)%(o>>>0),l[t+4>>2]=0),h=0,E=(c>>>0)/(o>>>0)>>>0,0|(p=h,E);if(!s)return 0|t&&(l[t>>2]=0,l[t+4>>2]=(c>>>0)%(u>>>0)),h=0,E=(c>>>0)/(u>>>0)>>>0,0|(p=h,E);if(a=u-1|0,!(a&u))return 0|t&&(l[t>>2]=0|e,l[t+4>>2]=a&c|0&r),h=0,E=c>>>((0|cr(0|u))>>>0),0|(p=h,E);if(a=(0|w(0|u))-(0|w(0|c))|0,a>>>0<=30){r=a+1|0,u=31-a|0,o=r,e=c<<u|s>>>(r>>>0),r=c>>>(r>>>0),a=0,u=s<<u;break}return t?(l[t>>2]=0|e,l[t+4>>2]=f|0&r,h=0,E=0,0|(p=h,E)):(h=0,E=0,0|(p=h,E))}while(0);if(o){_=0|i,s=d|0&n,c=0|Lr(0|_,0|s,-1,-1),i=p,f=u,u=0;do{n=f,f=a>>>31|f<<1,a=u|a<<1,n=e<<1|n>>>31|0,d=e>>>31|r<<1|0,Cr(0|c,0|i,0|n,0|d),E=p,h=E>>31|((0|E)<0?-1:0)<<1,u=1&h,e=0|Cr(0|n,0|d,h&_|0,(((0|E)<0?-1:0)>>31|((0|E)<0?-1:0)<<1)&s|0),r=p,o=o-1|0}while(0!=(0|o));c=f,f=0}else c=u,f=0,u=0;return o=0,0|t&&(l[t>>2]=e,l[t+4>>2]=r),h=(0|a)>>>31|(c|o)<<1|0&(o<<1|a>>>31)|f,E=-2&(a<<1|0)|u,0|(p=h,E)}function re(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0;if(_=e+4|0,s=0|l[_>>2],i=-8&s,u=e+i|0,!(3&s))return r>>>0<256?(e=0,0|e):(i>>>0>=(r+4|0)>>>0&&(i-r|0)>>>0<=l[1264]<<1>>>0||(e=0),0|e);if(i>>>0>=r>>>0)return i=i-r|0,i>>>0<=15||(c=e+r|0,l[_>>2]=1&s|r|2,l[c+4>>2]=3|i,_=c+i+4|0,l[_>>2]=1|l[_>>2],Z(c,i)),0|e;if((0|u)==(0|l[1150]))return c=(0|l[1147])+i|0,i=c-r|0,n=e+r|0,c>>>0<=r>>>0?(e=0,0|e):(l[_>>2]=1&s|r|2,l[n+4>>2]=1|i,l[1150]=n,l[1147]=i,0|e);if((0|u)==(0|l[1149]))return t=(0|l[1146])+i|0,t>>>0<r>>>0?(e=0,0|e):(i=t-r|0,n=1&s,i>>>0>15?(s=e+r|0,c=s+i|0,l[_>>2]=n|r|2,l[s+4>>2]=1|i,l[c>>2]=i,n=c+4|0,l[n>>2]=-2&l[n>>2],n=s):(l[_>>2]=n|t|2,n=e+t+4|0,l[n>>2]=1|l[n>>2],n=0,i=0),l[1146]=i,l[1149]=n,0|e);if(n=0|l[u+4>>2],2&n|0)return e=0,0|e;if(f=(-8&n)+i|0,f>>>0<r>>>0)return e=0,0|e;c=f-r|0,t=n>>>3;do{if(n>>>0<256){if(n=0|l[u+8>>2],i=0|l[u+12>>2],(0|i)==(0|n)){l[1144]=l[1144]&~(1<<t);break}l[n+12>>2]=i,l[i+8>>2]=n;break}o=0|l[u+24>>2],i=0|l[u+12>>2];do{if((0|i)==(0|u)){if(t=u+16|0,n=t+4|0,i=0|l[n>>2],i)a=n;else{if(i=0|l[t>>2],!i){t=0;break}a=t}while(1)if(t=i+20|0,n=0|l[t>>2],0|n)i=n,a=t;else{if(n=i+16|0,t=0|l[n>>2],!t)break;i=t,a=n}l[a>>2]=0,t=i}else t=0|l[u+8>>2],l[t+12>>2]=i,l[i+8>>2]=t,t=i}while(0);if(0|o){if(i=0|l[u+28>>2],n=4880+(i<<2)|0,(0|u)==(0|l[n>>2])){if(l[n>>2]=t,!t){l[1145]=l[1145]&~(1<<i);break}}else if(l[o+16+(((0|l[o+16>>2])!=(0|u)&1)<<2)>>2]=t,!t)break;l[t+24>>2]=o,i=u+16|0,n=0|l[i>>2],0|n&&(l[t+16>>2]=n,l[n+24>>2]=t),i=0|l[i+4>>2],0|i&&(l[t+20>>2]=i,l[i+24>>2]=t)}}while(0);return i=1&s,c>>>0<16?(l[_>>2]=f|i|2,_=e+f+4|0,l[_>>2]=1|l[_>>2],0|e):(s=e+r|0,l[_>>2]=i|r|2,l[s+4>>2]=3|c,_=s+c+4|0,l[_>>2]=1|l[_>>2],Z(s,c),0|e)}function ie(e,r,i,n,t,a){e|=0,r|=0,i|=0,n|=0,t|=0,a|=0;var o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0;E=m,m=m+592|0,h=E+56|0,f=E+40|0,_=E+72|0,s=E,d=E+68|0,l[s>>2]=40,pe(e,r,s),o=(0|l[s+4>>2])>>>t,u=(0|l[s+8>>2])>>>t,s=s+32|0,n=0|l[s+4>>2];do{switch(0|l[s>>2]){case 0:n?c=14:s=8;break;case 1:c=n?14:13;break;case 2:c=n?14:13;break;case 3:c=n?14:13;break;case 4:c=n?14:13;break;case 5:c=n?14:13;break;case 6:c=n?14:13;break;case 7:c=n?14:13;break;case 8:c=n?14:13;break;case 9:n?c=14:s=8;break;case 10:n?c=14:s=8;break;default:c=14}}while(0);13==(0|c)?s=16:14==(0|c)&&(l[f>>2]=866,l[f+4>>2]=2672,l[f+8>>2]=1251,vr(_,812,f),Je(_),s=0),l[d>>2]=i,c=0|fe(e,r),r=a+t|0;do{if(r>>>0>t>>>0){if(!c){n=i;while(1){if(n=n+(0|v(0|v((o+3|0)>>>2,s),(u+3|0)>>>2))|0,t=t+1|0,(0|t)==(0|r))break;u>>>=1,o>>>=1}l[d>>2]=n;break}e=u,n=i;while(1){if(u=0|v((o+3|0)>>>2,s),f=0|v(u,(e+3|0)>>>2),t>>>0>15|f>>>0<8||519686845!=(0|l[c>>2])||(Se(c,d,f,u,t),n=0|l[d>>2]),n=n+f|0,l[d>>2]=n,t=t+1|0,(0|t)==(0|r))break;e>>>=1,o>>>=1}}}while(0);if(c){if(519686845==(0|l[c>>2]))return ae(c),7&c?(l[h>>2]=866,l[h+4>>2]=2506,l[h+8>>2]=1232,vr(_,812,h),Je(_),void(m=E)):(Xe(c,0,0,1,0),void(m=E));m=E}else m=E}function ne(e){e|=0;var r=0,i=0,n=0,a=0,o=0,u=0,f=0,s=0,_=0,d=0,h=0,E=0,b=0;if(b=m,m=m+576|0,u=b,a=b+64|0,E=b+16|0,n=e+88|0,r=0|l[n>>2],h=(0|c[r+39>>0])<<8|0|c[r+40>>0],_=e+236|0,o=e+240|0,i=0|l[o>>2],(0|i)!=(0|h)){if(i>>>0<=h>>>0){do{if((0|l[e+244>>2])>>>0<h>>>0){if(0|le(_,h,(i+1|0)==(0|h),4,0)){r=0|l[o>>2];break}return t[e+248>>0]=1,E=0,m=b,0|E}r=i}while(0);He((0|l[_>>2])+(r<<2)|0,0,h-r<<2|0),r=0|l[n>>2]}l[o>>2]=h}if(d=e+92|0,i=(0|l[e+4>>2])+((0|c[r+34>>0])<<8|(0|c[r+33>>0])<<16|0|c[r+35>>0])|0,r=(0|c[r+37>>0])<<8|(0|c[r+36>>0])<<16|0|c[r+38>>0],!r)return E=0,m=b,0|E;if(l[d>>2]=i,l[e+96>>2]=i,l[e+104>>2]=r,l[e+100>>2]=i+r,l[e+108>>2]=0,l[e+112>>2]=0,f=E+20|0,l[E>>2]=0,l[E+4>>2]=0,l[E+8>>2]=0,l[E+12>>2]=0,t[E+16>>0]=0,s=E+24|0,l[E+44>>2]=0,l[f>>2]=0,l[f+4>>2]=0,l[f+8>>2]=0,l[f+12>>2]=0,l[f+16>>2]=0,t[f+20>>0]=0,0|V(d,E)&&0|V(d,s))if(0|l[o>>2]||(l[u>>2]=866,l[u+4>>2]=910,l[u+8>>2]=1497,vr(a,812,u),Je(a)),h){u=0,f=0,i=0|l[_>>2],n=0,e=0,r=0,a=0,o=0;while(1){if(u=(0|te(d,E))+u&31,o=(0|te(d,s))+o&63,a=(0|te(d,E))+a&31,r=(0|te(d,E))+r|0,e=(0|te(d,s))+e&63,n=(0|te(d,E))+n&31,l[i>>2]=o<<5|u<<11|a|r<<27|e<<21|n<<16,f=f+1|0,f>>>0>=h>>>0){r=1;break}i=i+4|0,r&=31}}else r=1;else r=0;return Ne(E+24|0),Ne(E),E=r,m=b,0|E}function te(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0,s=0,d=0,h=0,E=0,b=0,T=0,M=0;M=m,m=m+576|0,s=M+48|0,h=M+32|0,d=M+16|0,f=M,b=M+64|0,E=0|l[r+20>>2],T=e+20|0,u=0|l[T>>2],(0|u)<24?(o=e+4|0,i=0|l[o>>2],t=0|l[e+8>>2],n=i>>>0<t>>>0,(0|u)<16?(n?(a=(0|c[i>>0])<<8,i=i+1|0):a=0,i>>>0<t>>>0?(t=0|c[i>>0],i=i+1|0):t=0,l[o>>2]=i,l[T>>2]=u+16,n=16,i=t|a):(n?(l[o>>2]=i+1,i=0|c[i>>0]):i=0,l[T>>2]=u+8,n=24),o=e+16|0,t=l[o>>2]|i<<n-u,l[o>>2]=t):(t=e+16|0,o=t,t=0|l[t>>2]),a=1+(t>>>16)|0;do{if(!(a>>>0<=(0|l[E+16>>2])>>>0)){n=0|l[E+20>>2];while(1){if(i=n+-1|0,!(a>>>0>(0|l[E+28+(i<<2)>>2])>>>0))break;n=n+1|0}if(i=(t>>>(32-n|0))+(0|l[E+96+(i<<2)>>2])|0,i>>>0<(0|l[r>>2])>>>0){i=0|_[(0|l[E+176>>2])+(i<<1)>>1];break}return l[s>>2]=866,l[s+4>>2]=3275,l[s+8>>2]=1348,vr(b,812,s),Je(b),T=0,m=M,0|T}n=0|l[(0|l[E+168>>2])+(t>>>(32-(0|l[E+8>>2])|0)<<2)>>2],-1==(0|n)&&(l[f>>2]=866,l[f+4>>2]=3253,l[f+8>>2]=1393,vr(b,812,f),Je(b)),i=65535&n,n>>>=16,(0|l[r+8>>2])>>>0<=i>>>0&&(l[d>>2]=866,l[d+4>>2]=909,l[d+8>>2]=1497,vr(b,812,d),Je(b)),(0|c[(0|l[r+4>>2])+i>>0])!=(0|n)&&(l[h>>2]=866,l[h+4>>2]=3257,l[h+8>>2]=1410,vr(b,812,h),Je(b))}while(0);return l[o>>2]=l[o>>2]<<n,l[T>>2]=(0|l[T>>2])-n,T=i,m=M,0|T}function ae(e){e|=0;var r=0,i=0,n=0,a=0,o=0,u=0,f=0,c=0;if(c=m,m=m+576|0,f=c+48|0,o=c+32|0,a=c+16|0,n=c,u=c+64|0,l[e>>2]=0,r=e+284|0,i=0|l[r>>2],0|i&&(7&i?(l[n>>2]=866,l[n+4>>2]=2506,l[n+8>>2]=1232,vr(u,812,n),Je(u)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+288>>2]=0,l[e+292>>2]=0),t[e+296>>0]=0,r=e+268|0,i=0|l[r>>2],0|i&&(7&i?(l[a>>2]=866,l[a+4>>2]=2506,l[a+8>>2]=1232,vr(u,812,a),Je(u)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+272>>2]=0,l[e+276>>2]=0),t[e+280>>0]=0,r=e+252|0,i=0|l[r>>2],0|i&&(7&i?(l[o>>2]=866,l[o+4>>2]=2506,l[o+8>>2]=1232,vr(u,812,o),Je(u)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+256>>2]=0,l[e+260>>2]=0),t[e+264>>0]=0,r=e+236|0,i=0|l[r>>2],!i)return f=e+248|0,t[f>>0]=0,f=e+212|0,Ne(f),f=e+188|0,Ne(f),f=e+164|0,Ne(f),f=e+140|0,Ne(f),f=e+116|0,Ne(f),void(m=c);7&i?(l[f>>2]=866,l[f+4>>2]=2506,l[f+8>>2]=1232,vr(u,812,f),Je(u)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+240>>2]=0,l[e+244>>2]=0,f=e+248|0,t[f>>0]=0,f=e+212|0,Ne(f),f=e+188|0,Ne(f),f=e+164|0,Ne(f),f=e+140|0,Ne(f),f=e+116|0,Ne(f),m=c}function oe(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,a=0;e:do{if(r>>>0<=20)do{switch(0|r){case 9:n=3+(0|l[i>>2])&-4,r=0|l[n>>2],l[i>>2]=n+4,l[e>>2]=r;break e;case 10:n=3+(0|l[i>>2])&-4,r=0|l[n>>2],l[i>>2]=n+4,n=e,l[n>>2]=r,l[n+4>>2]=((0|r)<0)<<31>>31;break e;case 11:n=3+(0|l[i>>2])&-4,r=0|l[n>>2],l[i>>2]=n+4,n=e,l[n>>2]=r,l[n+4>>2]=0;break e;case 12:n=7+(0|l[i>>2])&-8,r=n,t=0|l[r>>2],r=0|l[r+4>>2],l[i>>2]=n+8,n=e,l[n>>2]=t,l[n+4>>2]=r;break e;case 13:t=3+(0|l[i>>2])&-4,n=0|l[t>>2],l[i>>2]=t+4,n=(65535&n)<<16>>16,t=e,l[t>>2]=n,l[t+4>>2]=((0|n)<0)<<31>>31;break e;case 14:t=3+(0|l[i>>2])&-4,n=0|l[t>>2],l[i>>2]=t+4,t=e,l[t>>2]=65535&n,l[t+4>>2]=0;break e;case 15:t=3+(0|l[i>>2])&-4,n=0|l[t>>2],l[i>>2]=t+4,n=(255&n)<<24>>24,t=e,l[t>>2]=n,l[t+4>>2]=((0|n)<0)<<31>>31;break e;case 16:t=3+(0|l[i>>2])&-4,n=0|l[t>>2],l[i>>2]=t+4,t=e,l[t>>2]=255&n,l[t+4>>2]=0;break e;case 17:t=7+(0|l[i>>2])&-8,a=+b[t>>3],l[i>>2]=t+8,b[e>>3]=a;break e;case 18:t=7+(0|l[i>>2])&-8,a=+b[t>>3],l[i>>2]=t+8,b[e>>3]=a;break e;default:break e}}while(0)}while(0)}function ue(e){e|=0;var r=0,i=0,n=0,a=0,u=0,f=0,s=0,_=0,d=0;if(d=m,m=m+560|0,a=d,n=d+40|0,_=d+16|0,i=0|l[e+88>>2],f=(0|c[i+55>>0])<<8|0|c[i+56>>0],s=e+92|0,r=(0|l[e+4>>2])+((0|c[i+50>>0])<<8|(0|c[i+49>>0])<<16|0|c[i+51>>0])|0,i=(0|c[i+53>>0])<<8|(0|c[i+52>>0])<<16|0|c[i+54>>0],!i)return _=0,m=d,0|_;l[s>>2]=r,l[e+96>>2]=r,l[e+104>>2]=i,l[e+100>>2]=r+i,l[e+108>>2]=0,l[e+112>>2]=0,l[_+20>>2]=0,l[_>>2]=0,l[_+4>>2]=0,l[_+8>>2]=0,l[_+12>>2]=0,t[_+16>>0]=0;e:do{if(0|V(s,_)){if(u=e+268|0,i=e+272|0,r=0|l[i>>2],(0|r)!=(0|f)){if(r>>>0<=f>>>0){do{if((0|l[e+276>>2])>>>0<f>>>0){if(0|le(u,f,(r+1|0)==(0|f),2,0)){r=0|l[i>>2];break}t[e+280>>0]=1,r=0;break e}}while(0);He((0|l[u>>2])+(r<<1)|0,0,f-r<<1|0)}l[i>>2]=f}if(!f){l[a>>2]=866,l[a+4>>2]=910,l[a+8>>2]=1497,vr(n,812,a),Je(n),r=1;break}i=0,e=0,n=0,r=0|l[u>>2];while(1){if(u=0|te(s,_),n=u+n&255,e=(0|te(s,_))+e&255,o[r>>1]=e<<8|n,i=i+1|0,i>>>0>=f>>>0){r=1;break}r=r+2|0}}else r=0}while(0);return Ne(_),_=r,m=d,0|_}function le(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0;if(E=m,m=m+576|0,_=E+48|0,f=E+32|0,o=E+16|0,a=E,s=E+64|0,d=E+60|0,c=e+4|0,h=e+8|0,(0|l[c>>2])>>>0>(0|l[h>>2])>>>0&&(l[a>>2]=866,l[a+4>>2]=2123,l[a+8>>2]=845,vr(s,812,a),Je(s)),(2147418112/(n>>>0)|0)>>>0<=r>>>0&&(l[o>>2]=866,l[o+4>>2]=2124,l[o+8>>2]=885,vr(s,812,o),Je(s)),a=0|l[h>>2],a>>>0>=r>>>0)return h=1,m=E,0|h;if(i&&(u=r+-1|0,0!=(u&r|0))?(r=u>>>16|u,r|=r>>>8,r|=r>>>4,r|=r>>>2,r=1+(r>>>1|r)|0,r?i=9:(r=0,i=10)):i=9,9==(0|i)&&r>>>0<=a>>>0&&(i=10),10==(0|i)&&(l[f>>2]=866,l[f+4>>2]=2133,l[f+8>>2]=933,vr(s,812,f),Je(s)),u=0|v(r,n),t)if(o=0|Pe(u,d),o){gi[0&t](o,0|l[e>>2],0|l[c>>2]),a=0|l[e>>2];do{if(0|a){if(7&a){l[_>>2]=866,l[_+4>>2]=2506,l[_+8>>2]=1232,vr(s,812,_),Je(s);break}Xe(a,0,0,1,0);break}}while(0);l[e>>2]=o,i=20}else r=0;else a=0|De(0|l[e>>2],u,d,1),a?(l[e>>2]=a,i=20):r=0;return 20==(0|i)&&(a=0|l[d>>2],a>>>0>u>>>0&&(r=(a>>>0)/(n>>>0)|0),l[h>>2]=r,r=1),h=r,m=E,0|h}function fe(e,r){e|=0,r|=0;var i=0,n=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0;if(h=m,m=m+528|0,_=h,f=h+16|0,0==(0|e)|r>>>0<62)return E=0,m=h,0|E;if(c=0|Pe(300,0),!c)return E=0,m=h,0|E;l[c>>2]=519686845,l[c+4>>2]=0,l[c+8>>2]=0,s=c+88|0,i=c+136|0,n=c+160|0,a=c+184|0,o=c+208|0,u=c+232|0,d=c+252|0,l[d>>2]=0,l[d+4>>2]=0,l[d+8>>2]=0,t[d+12>>0]=0,d=c+268|0,l[d>>2]=0,l[d+4>>2]=0,l[d+8>>2]=0,t[d+12>>0]=0,d=c+284|0,l[d>>2]=0,l[d+4>>2]=0,l[d+8>>2]=0,t[d+12>>0]=0,d=s,E=d+44|0;do{l[d>>2]=0,d=d+4|0}while((0|d)<(0|E));return t[s+44>>0]=0,l[i>>2]=0,l[i+4>>2]=0,l[i+8>>2]=0,l[i+12>>2]=0,l[i+16>>2]=0,t[i+20>>0]=0,l[n>>2]=0,l[n+4>>2]=0,l[n+8>>2]=0,l[n+12>>2]=0,l[n+16>>2]=0,t[n+20>>0]=0,l[a>>2]=0,l[a+4>>2]=0,l[a+8>>2]=0,l[a+12>>2]=0,l[a+16>>2]=0,t[a+20>>0]=0,l[o>>2]=0,l[o+4>>2]=0,l[o+8>>2]=0,l[o+12>>2]=0,l[o+16>>2]=0,t[o+20>>0]=0,l[u>>2]=0,l[u+4>>2]=0,l[u+8>>2]=0,l[u+12>>2]=0,t[u+16>>0]=0,0|ye(c,e,r)?(E=c,m=h,0|E):(ae(c),7&c?(l[_>>2]=866,l[_+4>>2]=2506,l[_+8>>2]=1232,vr(f,812,_),Je(f),E=0,m=h,0|E):(Xe(c,0,0,1,0),E=0,m=h,0|E))}function ce(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,a=0,o=0,u=0,f=0;u=m,m=m+576|0,a=u+40|0,t=u+56|0,f=u,l[f>>2]=40,pe(e,r,f),n=(3+((0|l[f+4>>2])>>>i)|0)>>>2,r=(3+((0|l[f+8>>2])>>>i)|0)>>>2,i=f+32|0,e=0|l[i+4>>2];do{switch(0|l[i>>2]){case 0:e?o=14:e=8;break;case 1:o=e?14:13;break;case 2:o=e?14:13;break;case 3:o=e?14:13;break;case 4:o=e?14:13;break;case 5:o=e?14:13;break;case 6:o=e?14:13;break;case 7:o=e?14:13;break;case 8:o=e?14:13;break;case 9:e?o=14:e=8;break;case 10:e?o=14:e=8;break;default:o=14}}while(0);return 13==(0|o)?e=16:14==(0|o)&&(l[a>>2]=866,l[a+4>>2]=2672,l[a+8>>2]=1251,vr(t,812,a),Je(t),e=0),f=0|v(0|v(r,n),e),m=u,0|f}function se(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0;t=m,m=m+576|0,n=t+40|0,i=t+56|0,a=t,l[a>>2]=40,pe(e,r,a),r=a+32|0,e=0|l[r+4>>2];do{switch(0|l[r>>2]){case 0:if(!e)return a=8,m=t,0|a;e=14;break;case 1:e=e?14:13;break;case 2:e=e?14:13;break;case 3:e=e?14:13;break;case 4:e=e?14:13;break;case 5:e=e?14:13;break;case 6:e=e?14:13;break;case 7:e=e?14:13;break;case 8:e=e?14:13;break;case 9:if(!e)return a=8,m=t,0|a;e=14;break;case 10:if(!e)return a=8,m=t,0|a;e=14;break;default:e=14}}while(0);return 13==(0|e)?(a=16,m=t,0|a):14==(0|e)?(l[n>>2]=866,l[n+4>>2]=2672,l[n+8>>2]=1251,vr(i,812,n),Je(i),a=0,m=t,0|a):0}function _e(e,r,i,n,a,o,u){e|=0,r|=0,i|=0,n|=0,a|=0,o|=0,u|=0;var f=0,s=0,_=0,d=0;if(d=0|l[e+88>>2],s=(c[d+12>>0]<<8|c[d+13>>0])>>>u,_=(c[d+14>>0]<<8|c[d+15>>0])>>>u,s=((s>>>0>1?s:1)+3|0)>>>2,_=((_>>>0>1?_:1)+3|0)>>>2,d=d+18|0,u=0|t[d>>0],u=0|v(s,u<<24>>24==0|u<<24>>24==9?8:16),o){if(!(0==(3&o|0)&u>>>0<=o>>>0))return a=0,0|a;u=o}if((0|v(u,_))>>>0>a>>>0)return a=0,0|a;if(o=(s+1|0)>>>1,f=(_+1|0)>>>1,!i)return a=0,0|a;switch(l[e+92>>2]=r,l[e+96>>2]=r,l[e+104>>2]=i,l[e+100>>2]=r+i,l[e+108>>2]=0,l[e+112>>2]=0,0|t[d>>0]){case 0:if(!(0|W(e,n,a,u,s,_,o,f)))return a=0,0|a;break;case 4:case 6:case 5:case 3:case 2:if(!(0|J(e,n,a,u,s,_,o,f)))return a=0,0|a;break;case 9:if(!(0|$(e,n,a,u,s,_,o,f)))return a=0,0|a;break;case 8:case 7:if(!(0|z(e,n,a,u,s,_,o,f)))return a=0,0|a;break;default:return a=0,0|a}return a=1,0|a}function de(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0;if((0|i)>=8192)return 0|F(0|e,0|r,0|i);if(o=0|e,a=e+i|0,(3&e)==(3&r)){while(3&e){if(!i)return 0|o;t[e>>0]=0|t[r>>0],e=e+1|0,r=r+1|0,i=i-1|0}i=-4&a|0,n=i-64|0;while((0|e)<=(0|n))l[e>>2]=l[r>>2],l[e+4>>2]=l[r+4>>2],l[e+8>>2]=l[r+8>>2],l[e+12>>2]=l[r+12>>2],l[e+16>>2]=l[r+16>>2],l[e+20>>2]=l[r+20>>2],l[e+24>>2]=l[r+24>>2],l[e+28>>2]=l[r+28>>2],l[e+32>>2]=l[r+32>>2],l[e+36>>2]=l[r+36>>2],l[e+40>>2]=l[r+40>>2],l[e+44>>2]=l[r+44>>2],l[e+48>>2]=l[r+48>>2],l[e+52>>2]=l[r+52>>2],l[e+56>>2]=l[r+56>>2],l[e+60>>2]=l[r+60>>2],e=e+64|0,r=r+64|0;while((0|e)<(0|i))l[e>>2]=l[r>>2],e=e+4|0,r=r+4|0}else{i=a-4|0;while((0|e)<(0|i))t[e>>0]=0|t[r>>0],t[e+1>>0]=0|t[r+1>>0],t[e+2>>0]=0|t[r+2>>0],t[e+3>>0]=0|t[r+3>>0],e=e+4|0,r=r+4|0}while((0|e)<(0|a))t[e>>0]=0|t[r>>0],e=e+1|0,r=r+1|0;return 0|o}function he(e){e|=0;var r=0,i=0,n=0,t=0;if(t=e+92|0,n=e+88|0,i=0|l[n>>2],r=(0|l[e+4>>2])+((0|c[i+68>>0])<<8|(0|c[i+67>>0])<<16|0|c[i+69>>0])|0,i=(0|c[i+65>>0])<<8|0|c[i+66>>0],!i)return t=0,0|t;if(l[t>>2]=r,l[e+96>>2]=r,l[e+104>>2]=i,l[e+100>>2]=r+i,l[e+108>>2]=0,l[e+112>>2]=0,!(0|V(t,e+116|0)))return t=0,0|t;r=0|l[n>>2];do{if((0|c[r+39>>0])<<8|0|c[r+40>>0]){if(!(0|V(t,e+140|0)))return t=0,0|t;if(0|V(t,e+188|0)){r=0|l[n>>2];break}return t=0,0|t}if(!((0|c[r+55>>0])<<8|0|c[r+56>>0]))return t=0,0|t}while(0);if((0|c[r+55>>0])<<8|0|c[r+56>>0]|0){if(!(0|V(t,e+164|0)))return t=0,0|t;if(!(0|V(t,e+212|0)))return t=0,0|t}return t=1,0|t}function Ee(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0;_=m,m=m+48|0,c=_+16|0,a=_,t=_+32|0,u=e+28|0,n=0|l[u>>2],l[t>>2]=n,f=e+20|0,n=(0|l[f>>2])-n|0,l[t+4>>2]=n,l[t+8>>2]=r,l[t+12>>2]=i,n=n+i|0,o=e+60|0,l[a>>2]=l[o>>2],l[a+4>>2]=t,l[a+8>>2]=2,a=0|Fr(0|x(146,0|a));e:do{if((0|n)!=(0|a)){r=2;while(1){if((0|a)<0)break;if(n=n-a|0,h=0|l[t+4>>2],d=a>>>0>h>>>0,t=d?t+8|0:t,r=(d<<31>>31)+r|0,h=a-(d?h:0)|0,l[t>>2]=(0|l[t>>2])+h,d=t+4|0,l[d>>2]=(0|l[d>>2])-h,l[c>>2]=l[o>>2],l[c+4>>2]=t,l[c+8>>2]=r,a=0|Fr(0|x(146,0|c)),(0|n)==(0|a)){s=3;break e}}l[e+16>>2]=0,l[u>>2]=0,l[f>>2]=0,l[e>>2]=32|l[e>>2],i=2==(0|r)?0:i-(0|l[t+4>>2])|0}else s=3}while(0);return 3==(0|s)&&(h=0|l[e+44>>2],l[e+16>>2]=h+(0|l[e+48>>2]),l[u>>2]=h,l[f>>2]=h),m=_,0|i}function be(e,r,i,n,a){e|=0,r|=0,i|=0,n|=0,a|=0;var o=0,u=0,f=0,c=0;do{if(0|Gr(e,0|l[r+8>>2]))sr(0,r,i,n);else{if(o=e+8|0,!(0|Gr(e,0|l[r>>2]))){f=0|l[o>>2],Si[3&l[24+(0|l[f>>2])>>2]](f,r,i,n,a);break}if(e=r+32|0,(0|l[r+16>>2])!=(0|i)&&(u=r+20|0,(0|l[u>>2])!=(0|i))){if(l[e>>2]=n,n=r+44|0,4==(0|l[n>>2]))break;e=r+52|0,t[e>>0]=0,c=r+53|0,t[c>>0]=0,o=0|l[o>>2],Ci[3&l[20+(0|l[o>>2])>>2]](o,r,i,i,1,a),0|t[c>>0]?0|t[e>>0]?e=3:(e=3,f=11):(e=4,f=11),11==(0|f)&&(l[u>>2]=i,c=r+40|0,l[c>>2]=1+(0|l[c>>2]),1==(0|l[r+36>>2])&&2==(0|l[r+24>>2])&&(t[r+54>>0]=1)),l[n>>2]=e;break}1==(0|n)&&(l[e>>2]=1)}}while(0)}function Te(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0,b=0;b=m,m=m+224|0,_=b+120|0,d=b+80|0,h=b,E=b+136|0,n=d,a=n+40|0;do{l[n>>2]=0,n=n+4|0}while((0|n)<(0|a));return l[_>>2]=l[i>>2],(0|K(0,r,_,h,d))<0?i=-1:(l[e+76>>2],i=0|l[e>>2],s=32&i,(0|t[e+74>>0])<1&&(l[e>>2]=-33&i),n=e+48|0,0|l[n>>2]?i=0|K(e,r,_,h,d):(a=e+44|0,o=0|l[a>>2],l[a>>2]=E,u=e+28|0,l[u>>2]=E,f=e+20|0,l[f>>2]=E,l[n>>2]=80,c=e+16|0,l[c>>2]=E+80,i=0|K(e,r,_,h,d),o&&(wi[7&l[e+36>>2]](e,0,0),i=0==(0|l[f>>2])?-1:i,l[a>>2]=o,l[n>>2]=0,l[c>>2]=0,l[u>>2]=0,l[f>>2]=0)),n=0|l[e>>2],l[e>>2]=n|s,i=0==(32&n|0)?i:-1),m=b,0|i}function Me(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var a=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0,E=0;E=m,m=m+64|0,d=E,_=0|l[e>>2],h=e+(0|l[_+-8>>2])|0,_=0|l[_+-4>>2],l[d>>2]=i,l[d+4>>2]=e,l[d+8>>2]=r,l[d+12>>2]=n,e=d+16|0,r=d+20|0,n=d+24|0,a=d+28|0,u=d+32|0,f=d+40|0,c=e,s=c+36|0;do{l[c>>2]=0,c=c+4|0}while((0|c)<(0|s));o[e+36>>1]=0,t[e+38>>0]=0;e:do{if(0|Gr(_,i))l[d+48>>2]=1,Ci[3&l[20+(0|l[_>>2])>>2]](_,d,h,h,1,0),e=1==(0|l[n>>2])?h:0;else{switch(Si[3&l[24+(0|l[_>>2])>>2]](_,d,h,1,0),0|l[d+36>>2]){case 0:e=1==(0|l[f>>2])&1==(0|l[a>>2])&1==(0|l[u>>2])?0|l[r>>2]:0;break e;case 1:break;default:e=0;break e}if(1!=(0|l[n>>2])&&!(0==(0|l[f>>2])&1==(0|l[a>>2])&1==(0|l[u>>2]))){e=0;break}e=0|l[e>>2]}}while(0);return m=E,0|e}function Ae(e){e|=0;var r=0,i=0,n=0,t=0,a=0,o=0,u=0,f=0;if(f=m,m=m+544|0,o=f+16|0,r=f,t=f+32|0,a=e+8|0,i=0|l[a>>2],(i+-1|0)>>>0>=8192&&(l[r>>2]=866,l[r+4>>2]=3006,l[r+8>>2]=1257,vr(t,812,r),Je(t)),l[e>>2]=i,n=e+20|0,r=0|l[n>>2],r?u=i:(r=0|Pe(180,0),r?(u=r+164|0,l[u>>2]=0,l[u+4>>2]=0,l[u+8>>2]=0,l[u+12>>2]=0):r=0,l[n>>2]=r,u=0|l[e>>2]),0|l[a>>2]?o=u:(l[o>>2]=866,l[o+4>>2]=910,l[o+8>>2]=1497,vr(t,812,o),Je(t),o=0|l[e>>2]),t=0|l[e+4>>2],!(o>>>0>16))return e=0,e=0|G(r,u,t,e),m=f,0|e;i=o,n=0;while(1){if(a=n+1|0,!(i>>>0>3))break;i>>>=1,n=a}return e=n+2+(32!=(0|a)&1<<a>>>0<o>>>0&1)|0,e=255&(e>>>0<11?e:11),e=0|G(r,u,t,e),m=f,0|e}function me(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0,u=0,f=0,c=0,s=0,_=0,d=0,h=0;h=1794895138+(0|l[e>>2])|0,o=0|Wr(0|l[e+8>>2],h),n=0|Wr(0|l[e+12>>2],h),a=0|Wr(0|l[e+16>>2],h);e:do{if(o>>>0<r>>>2>>>0&&(d=r-(o<<2)|0,n>>>0<d>>>0&a>>>0<d>>>0)&&0==(3&(a|n)|0)){d=n>>>2,_=a>>>2,s=0;while(1){if(f=o>>>1,c=s+f|0,u=c<<1,a=u+d|0,n=0|Wr(0|l[e+(a<<2)>>2],h),a=0|Wr(0|l[e+(a+1<<2)>>2],h),!(a>>>0<r>>>0&n>>>0<(r-a|0)>>>0)){n=0;break e}if(0|t[e+(a+n)>>0]){n=0;break e}if(n=0|rr(i,e+a|0),!n)break;if(n=(0|n)<0,1==(0|o)){n=0;break e}s=n?s:c,o=n?f:o-f|0}n=u+_|0,a=0|Wr(0|l[e+(n<<2)>>2],h),n=0|Wr(0|l[e+(n+1<<2)>>2],h),n=n>>>0<r>>>0&a>>>0<(r-n|0)>>>0&&0==(0|t[e+(n+a)>>0])?e+n|0:0}else n=0}while(0);return 0|n}function ke(e){e|=0;var r=0,i=0,n=0,t=0,a=0,o=0,u=0,f=0;u=m,m=m+576|0,a=u+48|0,o=u+32|0,n=u+16|0,i=u,t=u+64|0,r=0|l[e+168>>2];do{if(0|r){if(f=0|l[r+-4>>2],r=r+-8|0,0!=(0|f)&&(0|f)==(0|~l[r>>2])||(l[i>>2]=866,l[i+4>>2]=651,l[i+8>>2]=1579,vr(t,812,i),Je(t)),7&r){l[n>>2]=866,l[n+4>>2]=2506,l[n+8>>2]=1232,vr(t,812,n),Je(t);break}Xe(r,0,0,1,0);break}}while(0);if(r=0|l[e+176>>2],r)return f=0|l[r+-4>>2],r=r+-8|0,0!=(0|f)&&(0|f)==(0|~l[r>>2])||(l[o>>2]=866,l[o+4>>2]=651,l[o+8>>2]=1579,vr(t,812,o),Je(t)),7&r?(l[a>>2]=866,l[a+4>>2]=2506,l[a+8>>2]=1232,vr(t,812,a),Je(t),void(m=u)):(Xe(r,0,0,1,0),void(m=u));m=u}function pe(e,r,i){e|=0,r|=0,i|=0;var n=0;return 0!=(0|e)&r>>>0>73&0!=(0|i)?40!=(0|l[i>>2])||18552!=((0|c[e>>0])<<8|0|c[e+1>>0]|0)||((0|c[e+2>>0])<<8|0|c[e+3>>0])>>>0<74||((0|c[e+7>>0])<<16|(0|c[e+6>>0])<<24|(0|c[e+8>>0])<<8|0|c[e+9>>0])>>>0>r>>>0?(i=0,0|i):(l[i+4>>2]=(0|c[e+12>>0])<<8|0|c[e+13>>0],l[i+8>>2]=(0|c[e+14>>0])<<8|0|c[e+15>>0],l[i+12>>2]=c[e+16>>0],l[i+16>>2]=c[e+17>>0],r=e+18|0,n=i+32|0,l[n>>2]=c[r>>0],l[n+4>>2]=0,r=0|t[r>>0],l[i+20>>2]=r<<24>>24==0|r<<24>>24==9?8:16,l[i+24>>2]=(0|c[e+26>>0])<<16|(0|c[e+25>>0])<<24|(0|c[e+27>>0])<<8|0|c[e+28>>0],l[i+28>>2]=(0|c[e+30>>0])<<16|(0|c[e+29>>0])<<24|(0|c[e+31>>0])<<8|0|c[e+32>>0],i=1,0|i):(i=0,0|i)}function ve(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0,s=0;if(s=m,m=m+544|0,u=s+16|0,i=s,o=s+32|0,r>>>0>=33&&(l[i>>2]=866,l[i+4>>2]=3199,l[i+8>>2]=1350,vr(o,812,i),Je(o)),f=e+20|0,i=0|l[f>>2],(0|i)>=(0|r))return t=e+16|0,a=t,t=0|l[t>>2],o=i,u=32-r|0,u=t>>>u,t<<=r,l[a>>2]=t,r=o-r|0,l[f>>2]=r,m=s,0|u;t=e+4|0,a=e+8|0,n=e+16|0;do{e=0|l[t>>2],(0|e)==(0|l[a>>2])?e=0:(l[t>>2]=e+1,e=0|c[e>>0]),i=i+8|0,l[f>>2]=i,(0|i)>=33&&(l[u>>2]=866,l[u+4>>2]=3208,l[u+8>>2]=1366,vr(o,812,u),Je(o),i=0|l[f>>2]),e=e<<32-i|l[n>>2],l[n>>2]=e}while((0|i)<(0|r));return u=32-r|0,u=e>>>u,o=e<<r,l[n>>2]=o,r=i-r|0,l[f>>2]=r,m=s,0|u}function we(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0,u=0;o=255&r,n=0!=(0|i);e:do{if(n&0!=(3&e|0)){a=255&r;while(1){if((0|t[e>>0])==a<<24>>24){u=6;break e}if(e=e+1|0,i=i+-1|0,n=0!=(0|i),!(n&0!=(3&e|0))){u=5;break}}}else u=5}while(0);5==(0|u)&&(n?u=6:i=0);e:do{if(6==(0|u)&&(a=255&r,(0|t[e>>0])!=a<<24>>24)){n=0|v(o,16843009);r:do{if(i>>>0>3)while(1){if(o=l[e>>2]^n,(-2139062144&o^-2139062144)&o+-16843009|0)break;if(e=e+4|0,i=i+-4|0,i>>>0<=3){u=11;break r}}else u=11}while(0);if(11==(0|u)&&!i){i=0;break}while(1){if((0|t[e>>0])==a<<24>>24)break e;if(e=e+1|0,i=i+-1|0,!i){i=0;break}}}}while(0);return 0|(0|i?e:0)}function Se(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var a=0,o=0,u=0,f=0,s=0,_=0;return _=m,m=m+528|0,s=_,f=_+16|0,o=0|l[e+88>>2],u=(0|c[o+70+(t<<2)+1>>0])<<16|(0|c[o+70+(t<<2)>>0])<<24|(0|c[o+70+(t<<2)+2>>0])<<8|0|c[o+70+(t<<2)+3>>0],a=t+1|0,a=a>>>0<(0|c[o+16>>0])>>>0?(0|c[o+70+(a<<2)+1>>0])<<16|(0|c[o+70+(a<<2)>>0])<<24|(0|c[o+70+(a<<2)+2>>0])<<8|0|c[o+70+(a<<2)+3>>0]:0|l[e+8>>2],a>>>0>u>>>0?(f=e+4|0,f=0|l[f>>2],f=f+u|0,s=a-u|0,s=0|_e(e,f,s,r,i,n,t),m=_,0|s):(l[s>>2]=866,l[s+4>>2]=3694,l[s+8>>2]=1508,vr(f,812,s),Je(f),f=e+4|0,f=0|l[f>>2],f=f+u|0,s=a-u|0,s=0|_e(e,f,s,r,i,n,t),m=_,0|s)}function ye(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0;if(!(0==(0|r)|i>>>0<74||18552!=((0|c[r>>0])<<8|0|c[r+1>>0]|0))&&((0|c[r+2>>0])<<8|0|c[r+3>>0])>>>0>=74&&((0|c[r+7>>0])<<16|(0|c[r+6>>0])<<24|(0|c[r+8>>0])<<8|0|c[r+9>>0])>>>0<=i>>>0){if(n=e+88|0,l[n>>2]=r,l[e+4>>2]=r,l[e+8>>2]=i,!(0|he(e)))return t=0,0|t;if(r=0|l[n>>2],(0|c[r+39>>0])<<8|0|c[r+40>>0]?0|ne(e)&&0|Q(e)&&(r=0|l[n>>2],t=11):t=11,11==(0|t)){if(!((0|c[r+55>>0])<<8|0|c[r+56>>0]))return t=1,0|t;if(0|ue(e)&&0|q(e))return t=1,0|t}return t=0,0|t}return l[e+88>>2]=0,t=0,0|t}function Re(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0,s=0;if(s=m,m=m+528|0,o=s,a=s+16|0,!r)return f=0,m=s,0|f;if(r>>>0<=16)return f=0|ve(e,r),m=s,0|f;if(u=0|ve(e,r+-16|0),f=e+20|0,r=0|l[f>>2],(0|r)<16){n=e+4|0,t=e+8|0,i=e+16|0;do{e=0|l[n>>2],(0|e)==(0|l[t>>2])?e=0:(l[n>>2]=e+1,e=0|c[e>>0]),r=r+8|0,l[f>>2]=r,(0|r)>=33&&(l[o>>2]=866,l[o+4>>2]=3208,l[o+8>>2]=1366,vr(a,812,o),Je(a),r=0|l[f>>2]),e=e<<32-r|l[i>>2],l[i>>2]=e}while((0|r)<16)}else e=e+16|0,i=e,e=0|l[e>>2];return l[i>>2]=e<<16,l[f>>2]=r+-16,f=e>>>16|u<<16,m=s,0|f}function ge(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0,u=0,f=0;n=i+16|0,a=0|l[n>>2],a?o=5:0|er(i)?n=0:(a=0|l[n>>2],o=5);e:do{if(5==(0|o)){if(f=i+20|0,u=0|l[f>>2],n=u,(a-u|0)>>>0<r>>>0){n=0|wi[7&l[i+36>>2]](i,e,r);break}r:do{if((0|t[i+75>>0])>-1){u=r;while(1){if(!u){o=0,a=e;break r}if(a=u+-1|0,10==(0|t[e+a>>0]))break;u=a}if(n=0|wi[7&l[i+36>>2]](i,e,u),n>>>0<u>>>0)break e;o=u,a=e+u|0,r=r-u|0,n=0|l[f>>2]}else o=0,a=e}while(0);de(0|n,0|a,0|r),l[f>>2]=(0|l[f>>2])+r,n=o+r|0}}while(0);return 0|n}function Oe(e,r,i){e|=0,r|=0,i|=0;do{if(e){if(r>>>0<128){t[e>>0]=r,e=1;break}if(i=188+(0|ci())|0,!(0|l[l[i>>2]>>2])){if(57216==(-128&r|0)){t[e>>0]=r,e=1;break}e=0|li(),l[e>>2]=84,e=-1;break}if(r>>>0<2048){t[e>>0]=r>>>6|192,t[e+1>>0]=63&r|128,e=2;break}if(r>>>0<55296|57344==(-8192&r|0)){t[e>>0]=r>>>12|224,t[e+1>>0]=r>>>6&63|128,t[e+2>>0]=63&r|128,e=3;break}if((r+-65536|0)>>>0<1048576){t[e>>0]=r>>>18|240,t[e+1>>0]=r>>>12&63|128,t[e+2>>0]=r>>>6&63|128,t[e+3>>0]=63&r|128,e=4;break}e=0|li(),l[e>>2]=84,e=-1;break}e=1}while(0);return 0|e}function Ce(){var e=0,r=0,i=0,n=0,t=0,a=0,o=0,u=0;t=m,m=m+48|0,o=t+32|0,i=t+24|0,u=t+16|0,a=t,t=t+36|0,e=0|Ar(),0|e&&(n=0|l[e>>2],0|n)&&(e=n+48|0,r=0|l[e>>2],e=0|l[e+4>>2],1126902528==(-256&r|0)&1129074247==(0|e)||(l[i>>2]=4168,gr(4118,i)),e=1126902529==(0|r)&1129074247==(0|e)?0|l[n+44>>2]:n+80|0,l[t>>2]=e,n=0|l[n>>2],e=0|l[n+4>>2],0|wi[7&l[16+(0|l[2])>>2]](8,n,t)?(u=0|l[t>>2],u=0|Ri[1&l[8+(0|l[u>>2])>>2]](u),l[a>>2]=4168,l[a+4>>2]=e,l[a+8>>2]=u,gr(4032,a)):(l[u>>2]=4168,l[u+4>>2]=e,gr(4077,u))),gr(4156,o)}function Ne(e){e|=0;var r=0,i=0,n=0,a=0,o=0;o=m,m=m+544|0,a=o+16|0,i=o,n=o+32|0,r=0|l[e+20>>2];do{if(0|r){if(ke(r),7&r){l[i>>2]=866,l[i+4>>2]=2506,l[i+8>>2]=1232,vr(n,812,i),Je(n);break}Xe(r,0,0,1,0);break}}while(0);if(r=e+4|0,i=0|l[r>>2],!i)return a=e+16|0,t[a>>0]=0,void(m=o);7&i?(l[a>>2]=866,l[a+4>>2]=2506,l[a+8>>2]=1232,vr(n,812,a),Je(n)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+8>>2]=0,l[e+12>>2]=0,a=e+16|0,t[a>>0]=0,m=o}function Pe(e,r){e|=0,r|=0;var i=0,n=0,t=0,a=0,o=0,u=0,f=0;return f=m,m=m+560|0,u=f+32|0,o=f+16|0,i=f,a=f+48|0,t=f+44|0,n=e+3&-4,n=0|n?n:4,n>>>0>2147418112?(l[i>>2]=866,l[i+4>>2]=2506,l[i+8>>2]=1103,vr(a,812,i),Je(a),u=0,m=f,0|u):(l[t>>2]=n,e=0|Xe(0,n,t,1,0),i=0|l[t>>2],0|r&&(l[r>>2]=i),0==(0|e)|i>>>0<n>>>0?(l[o>>2]=866,l[o+4>>2]=2506,l[o+8>>2]=1129,vr(a,812,o),Je(a),e=0):7&e&&(l[u>>2]=866,l[u+4>>2]=2533,l[u+8>>2]=1156,vr(a,812,u),Je(a)),u=e,m=f,0|u)}function Ie(e){e|=0;var r=0,i=0,n=0,a=0,o=0,u=0;u=m,m=m+544|0,o=u+16|0,n=u,a=u+32|0,l[e>>2]=0,r=e+4|0,i=0|l[r>>2],0|i&&(7&i?(l[n>>2]=866,l[n+4>>2]=2506,l[n+8>>2]=1232,vr(a,812,n),Je(a)):Xe(i,0,0,1,0),l[r>>2]=0,l[e+8>>2]=0,l[e+12>>2]=0),t[e+16>>0]=0,e=e+20|0,r=0|l[e>>2],r?(ke(r),7&r?(l[o>>2]=866,l[o+4>>2]=2506,l[o+8>>2]=1232,vr(a,812,o),Je(a)):Xe(r,0,0,1,0),l[e>>2]=0,m=u):m=u}function Le(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var a=0,o=0,u=0,f=0,c=0,s=0,_=0;_=m,m=m+128|0,a=_+124|0,s=_,o=s,u=604,f=o+124|0;do{l[o>>2]=l[u>>2],o=o+4|0,u=u+4|0}while((0|o)<(0|f));return(r+-1|0)>>>0>2147483646?r?(r=0|li(),l[r>>2]=75,r=-1):(e=a,r=1,c=4):c=4,4==(0|c)&&(c=-2-e|0,c=r>>>0>c>>>0?c:r,l[s+48>>2]=c,a=s+20|0,l[a>>2]=e,l[s+44>>2]=e,r=e+c|0,e=s+16|0,l[e>>2]=r,l[s+28>>2]=r,r=0|Te(s,i,n),c&&(s=0|l[a>>2],t[s+(((0|s)==(0|l[e>>2]))<<31>>31)>>0]=0)),m=_,0|r}function De(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var t=0,a=0,o=0,u=0,f=0,c=0;return c=m,m=m+560|0,f=c+32|0,a=c+16|0,t=c,o=c+48|0,u=c+44|0,7&e|0?(l[t>>2]=866,l[t+4>>2]=2506,l[t+8>>2]=1210,vr(o,812,t),Je(o),f=0,m=c,0|f):r>>>0>2147418112?(l[a>>2]=866,l[a+4>>2]=2506,l[a+8>>2]=1103,vr(o,812,a),Je(o),f=0,m=c,0|f):(l[u>>2]=r,e=0|Xe(e,r,u,n,0),0|i&&(l[i>>2]=l[u>>2]),7&e|0&&(l[f>>2]=866,l[f+4>>2]=2558,l[f+8>>2]=1156,vr(o,812,f),Je(o)),f=e,m=c,0|f)}function Fe(e,r,i,n,a){e|=0,r|=0,i|=0,n|=0;var o=0;do{if(0|Gr(e,0|l[r+8>>2]))sr(0,r,i,n);else if(0|Gr(e,0|l[r>>2])){if(e=r+32|0,(0|l[r+16>>2])!=(0|i)&&(o=r+20|0,(0|l[o>>2])!=(0|i))){l[e>>2]=n,l[o>>2]=i,n=r+40|0,l[n>>2]=1+(0|l[n>>2]),1==(0|l[r+36>>2])&&2==(0|l[r+24>>2])&&(t[r+54>>0]=1),l[r+44>>2]=4;break}1==(0|n)&&(l[e>>2]=1)}}while(0)}function He(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0,o=0,u=0;if(o=e+i|0,r&=255,(0|i)>=67){while(3&e)t[e>>0]=r,e=e+1|0;n=-4&o|0,a=n-64|0,u=r|r<<8|r<<16|r<<24;while((0|e)<=(0|a))l[e>>2]=u,l[e+4>>2]=u,l[e+8>>2]=u,l[e+12>>2]=u,l[e+16>>2]=u,l[e+20>>2]=u,l[e+24>>2]=u,l[e+28>>2]=u,l[e+32>>2]=u,l[e+36>>2]=u,l[e+40>>2]=u,l[e+44>>2]=u,l[e+48>>2]=u,l[e+52>>2]=u,l[e+56>>2]=u,l[e+60>>2]=u,e=e+64|0;while((0|e)<(0|n))l[e>>2]=u,e=e+4|0}while((0|e)<(0|o))t[e>>0]=r,e=e+1|0;return o-i|0}function Ue(e,r,i,n,a){e|=0,r|=0,i|=0,n|=0,a|=0;var o=0,u=0,f=0,c=0;t[r+53>>0]=1;do{if((0|l[r+4>>2])==(0|n)){if(t[r+52>>0]=1,n=r+16|0,o=0|l[n>>2],f=r+54|0,c=r+48|0,u=r+24|0,e=r+36|0,!o){if(l[n>>2]=i,l[u>>2]=a,l[e>>2]=1,!(1==(0|l[c>>2])&1==(0|a)))break;t[f>>0]=1;break}if((0|o)!=(0|i)){l[e>>2]=1+(0|l[e>>2]),t[f>>0]=1;break}e=0|l[u>>2],2==(0|e)&&(l[u>>2]=a,e=a),1==(0|l[c>>2])&1==(0|e)&&(t[f>>0]=1)}}while(0)}function xe(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,a=0,o=0;if(o=m,m=m+64|0,t=o,0|Gr(e,r))r=1;else if(0!=(0|r)&&(a=0|Me(r,32,16,0),0!=(0|a))){r=t+4|0,n=r+52|0;do{l[r>>2]=0,r=r+4|0}while((0|r)<(0|n));l[t>>2]=a,l[t+8>>2]=e,l[t+12>>2]=-1,l[t+48>>2]=1,Ni[3&l[28+(0|l[a>>2])>>2]](a,t,0|l[i>>2],1),1==(0|l[t+24>>2])?(l[i>>2]=l[t+16>>2],r=1):r=0}else r=0;return m=o,0|r}function Be(e,r){e|=0,r|=0;var i=0,n=0,a=0,o=0,u=0,f=0,s=0;s=m,m=m+16|0,u=s,f=255&r,t[u>>0]=f,n=e+16|0,a=0|l[n>>2],a?o=4:0|er(e)?i=-1:(a=0|l[n>>2],o=4);do{if(4==(0|o)){if(o=e+20|0,n=0|l[o>>2],n>>>0<a>>>0&&(i=255&r,(0|i)!=(0|t[e+75>>0]))){l[o>>2]=n+1,t[n>>0]=f;break}i=1==(0|wi[7&l[e+36>>2]](e,u,1))?0|c[u>>0]:-1}}while(0);return m=s,0|i}function Ye(e,r){e|=0,r|=0;var i=0,n=0,a=0,o=0,u=0,f=0,c=0;f=255&e,i=255&e,(0|l[r+76>>2])>=0&&0!=(0|Mi())?(0|i)!=(0|t[r+75>>0])&&(o=r+20|0,u=0|l[o>>2],u>>>0<(0|l[r+16>>2])>>>0)?(l[o>>2]=u+1,t[u>>0]=f):i=0|Be(r,e):c=3;do{if(3==(0|c)){if((0|i)!=(0|t[r+75>>0])&&(n=r+20|0,a=0|l[n>>2],a>>>0<(0|l[r+16>>2])>>>0)){l[n>>2]=a+1,t[a>>0]=f;break}i=0|Be(r,e)}}while(0);return 0|i}function Xe(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;do{if(e){if(!r){if(j(e),!i){r=0;break}l[i>>2]=0,r=0;break}n?(r=0|ze(e,r),e=0==(0|r)?e:r):r=0,i&&(t=0|pr(e),l[i>>2]=t)}else r=0|Y(r),i&&(e=r?0|pr(r):0,l[i>>2]=e)}while(0);return 0|r}function Ke(e){e|=0;var r=0,i=0,n=0;n=e;e:do{if(3&n){r=n;while(1){if(!(0|t[e>>0])){e=r;break e}if(e=e+1|0,r=e,!(3&r)){i=4;break}}}else i=4}while(0);if(4==(0|i)){while(1){if(r=0|l[e>>2],(-2139062144&r^-2139062144)&r+-16843009)break;e=e+4|0}if((255&r)<<24>>24)do{e=e+1|0}while(0!=(0|t[e>>0]))}return e-n|0}function Ve(e,r){e=+e,r|=0;var i=0,n=0,t=0;switch(b[A>>3]=e,i=0|l[A>>2],n=0|l[A+4>>2],t=0|Rr(0|i,0|n,52),2047&t){case 0:0!=e?(e=+Ve(0x10000000000000000*e,r),i=(0|l[r>>2])-64|0):i=0,l[r>>2]=i;break;case 2047:break;default:l[r>>2]=(2047&t)-1022,l[A>>2]=i,l[A+4>>2]=-2146435073&n|1071644672,e=+b[A>>3]}return+e}function Ge(e,r){e|=0,r|=0;var i=0,n=0;n=0;while(1){if((0|c[2140+n>>0])==(0|e)){e=2;break}if(i=n+1|0,87==(0|i)){i=2228,n=87,e=5;break}n=i}if(2==(0|e)&&(n?(i=2228,e=5):i=2228),5==(0|e))while(1){do{e=i,i=i+1|0}while(0!=(0|t[e>>0]));if(n=n+-1|0,!n)break;e=5}return 0|ni(i,0|l[r+20>>2])}function We(e,r,i){e|=0,r|=0,i|=0;var n=0;if(r>>>0>0|0==(0|r)&e>>>0>4294967295){while(1){if(n=0|hr(0|e,0|r,10,0),i=i+-1|0,t[i>>0]=255&n|48,n=e,e=0|zr(0|e,0|r,10,0),!(r>>>0>9|9==(0|r)&n>>>0>4294967295))break;r=p}r=e}else r=e;if(r)while(1){if(i=i+-1|0,t[i>>0]=48|(r>>>0)%10,r>>>0<10)break;r=(r>>>0)/10|0}return 0|i}function ze(e,r){e|=0,r|=0;var i=0,n=0;return e?r>>>0>4294967231?(r=0|li(),l[r>>2]=12,r=0,0|r):(i=0|re(e+-8|0,r>>>0<11?16:r+11&-8),0|i?(r=i+8|0,0|r):(i=0|Y(r),i?(n=0|l[e+-4>>2],n=(-8&n)-(0==(3&n|0)?8:4)|0,de(0|i,0|e,0|(n>>>0<r>>>0?n:r)),j(e),r=i,0|r):(r=0,0|r))):(r=0|Y(r),0|r)}function je(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var a=0,o=0,u=0;e=r+16|0,a=0|l[e>>2],o=r+36|0,u=r+24|0;do{if(a){if((0|a)!=(0|i)){l[o>>2]=1+(0|l[o>>2]),l[u>>2]=2,t[r+54>>0]=1;break}2==(0|l[u>>2])&&(l[u>>2]=n)}else l[e>>2]=i,l[u>>2]=n,l[o>>2]=1}while(0)}function Je(e){e|=0;var r=0,i=0,n=0;n=0|l[119],l[n+76>>2];do{if((0|xr(e,n))<0)e=1;else{if(10!=(0|t[n+75>>0])&&(r=n+20|0,i=0|l[r>>2],i>>>0<(0|l[n+16>>2])>>>0)){l[r>>2]=i+1,t[i>>0]=10,e=0;break}e=(0|Be(n,10))<0}}while(0);return e<<31>>31|0}function Ze(e,r,i,n,t,a){e|=0,r|=0,i|=0,n|=0,t|=0,a|=0,0|Gr(e,0|l[r+8>>2])?Ue(0,r,i,n,t):(e=0|l[e+8>>2],Ci[3&l[20+(0|l[e>>2])>>2]](e,r,i,n,t,a))}function qe(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0;var a=0,o=0;if(o=m,m=m+256|0,a=o,(0|i)>(0|n)&0==(73728&t|0)){if(t=i-n|0,He(0|a,0|r,0|(t>>>0<256?t:256)),t>>>0>255){r=i-n|0;do{Br(e,a,256),t=t+-256|0}while(t>>>0>255);t=255&r}Br(e,a,t)}m=o}function Qe(e,r,i,n){e|=0,r|=0,i|=0,n|=0,0|Gr(e,0|l[r+8>>2])?je(0,r,i,n):(e=0|l[e+8>>2],Ni[3&l[28+(0|l[e>>2])>>2]](e,r,i,n))}function $e(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0,a=0;return t=m,m=m+32|0,a=t,n=t+20|0,l[a>>2]=l[e+60>>2],l[a+4>>2]=0,l[a+8>>2]=r,l[a+12>>2]=n,l[a+16>>2]=i,(0|Fr(0|U(140,0|a)))<0?(l[n>>2]=-1,e=-1):e=0|l[n>>2],m=t,0|e}function er(e){e|=0;var r=0,i=0;return r=e+74|0,i=0|t[r>>0],t[r>>0]=i+255|i,r=0|l[e>>2],8&r?(l[e>>2]=32|r,e=-1):(l[e+8>>2]=0,l[e+4>>2]=0,i=0|l[e+44>>2],l[e+28>>2]=i,l[e+20>>2]=i,l[e+16>>2]=i+(0|l[e+48>>2]),e=0),0|e}function rr(e,r){e|=0,r|=0;var i=0,n=0;if(i=0|t[e>>0],n=0|t[r>>0],i<<24>>24==0||i<<24>>24!=n<<24>>24)e=n;else{do{e=e+1|0,r=r+1|0,i=0|t[e>>0],n=0|t[r>>0]}while(i<<24>>24!=0&&i<<24>>24==n<<24>>24);e=n}return(255&i)-(255&e)|0}function ir(e,r,i){e|=0,r|=0,i|=0;var n=0,a=0;return a=m,m=m+32|0,n=a,l[e+36>>2]=1,0==(64&l[e>>2]|0)&&(l[n>>2]=l[e+60>>2],l[n+4>>2]=21523,l[n+8>>2]=a+16,0|C(54,0|n))&&(t[e+75>>0]=-1),n=0|Ee(e,r,i),m=a,0|n}function nr(e){e|=0;var r=0,i=0;return i=e+15&-16|0,r=0|l[M>>2],e=r+i|0,(0|i)>0&(0|e)<(0|r)|(0|e)<0?(g(),I(12),-1):(l[M>>2]=e,(0|e)>(0|R())&&0==(0|y())?(l[M>>2]=r,I(12),-1):0|r)}function tr(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var t=0;return t=0|v(i,r),i=0==(0|r)?0:i,e=(l[n+76>>2],0|ge(e,t,n)),(0|e)!=(0|t)&&(i=(e>>>0)/(r>>>0)|0),0|i}function ar(e,r,i,n,t,a){e|=0,r|=0,i|=0,n|=0,t|=0,0|Gr(e,0|l[r+8>>2])&&Ue(0,r,i,n,t)}function or(e,r,i,n){e|=0,r|=0,i|=0,n|=0,0|Gr(e,0|l[r+8>>2])&&je(0,r,i,n)}function ur(e){e|=0;var r=0,i=0,n=0;if(i=0|l[e>>2],n=(0|t[i>>0])-48|0,n>>>0<10){r=0;do{r=n+(10*r|0)|0,i=i+1|0,l[e>>2]=i,n=(0|t[i>>0])-48|0}while(n>>>0<10)}else r=0;return 0|r}function lr(e,r,i,n){if(e|=0,r|=0,i|=0,n|=0,!(0==(0|e)&0==(0|r)))do{i=i+-1|0,t[i>>0]=0|c[2122+(15&e)>>0]|n,e=0|Rr(0|e,0|r,4),r=p}while(!(0==(0|e)&0==(0|r)));return 0|i}function fr(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0;return t=m,m=m+16|0,n=t,l[n>>2]=l[i>>2],e=0|wi[7&l[16+(0|l[e>>2])>>2]](e,r,n),e&&(l[i>>2]=l[n>>2]),m=t,1&e|0}function cr(e){e|=0;var r=0;return r=0|t[k+(255&e)>>0],(0|r)<8?0|r:(r=0|t[k+(e>>8&255)>>0],(0|r)<8?r+8|0:(r=0|t[k+(e>>16&255)>>0],(0|r)<8?r+16|0:24+(0|t[k+(e>>>24)>>0])|0))}function sr(e,r,i,n){r|=0,i|=0,n|=0;var t=0;(0|l[r+4>>2])==(0|i)&&(t=r+28|0,1!=(0|l[t>>2]))&&(l[t>>2]=n)}function _r(e,r,i){if(e|=0,r|=0,i|=0,!(0==(0|e)&0==(0|r)))do{i=i+-1|0,t[i>>0]=7&e|48,e=0|Rr(0|e,0|r,3),r=p}while(!(0==(0|e)&0==(0|r)));return 0|i}function dr(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0;return n=e+20|0,t=0|l[n>>2],e=(0|l[e+16>>2])-t|0,e=e>>>0>i>>>0?i:e,de(0|t,0|r,0|e),l[n>>2]=(0|l[n>>2])+e,0|i}function hr(e,r,i,n){e|=0,r|=0,i|=0,n|=0;var t=0,a=0;return a=m,m=m+16|0,t=0|a,ee(e,r,i,n,t),m=a,0|(p=0|l[t+4>>2],0|l[t>>2])}function Er(e,r){e|=0,r|=0;var i=0,n=0;return n=m,m=m+48|0,i=n,l[i>>2]=40,pe(e,r,i),m=n,0|l[i+32>>2]}function br(e,r){e|=0,r|=0;var i=0,n=0;return n=m,m=m+48|0,i=n,l[i>>2]=40,pe(e,r,i),m=n,0|l[i+12>>2]}function Tr(e,r){e|=0,r|=0;var i=0,n=0;return n=m,m=m+48|0,i=n,l[i>>2]=40,pe(e,r,i),m=n,0|l[i+8>>2]}function Mr(e,r){e|=0,r|=0;var i=0,n=0;return n=m,m=m+48|0,i=n,l[i>>2]=40,pe(e,r,i),m=n,0|l[i+4>>2]}function Ar(){var e=0,r=0;return e=m,m=m+16|0,0|D(5136,2)?(gr(4307,e),0):(r=0|O(0|l[1285]),m=e,0|r)}function mr(e){e|=0;var r=0,i=0;return r=m,m=m+16|0,i=r,e=0|hi(0|l[e+60>>2]),l[i>>2]=e,e=0|Fr(0|P(6,0|i)),m=r,0|e}function kr(e){e|=0;var r=0;r=m,m=m+16|0,j(e),0|N(0|l[1285],0)?gr(4406,r):m=r}function pr(e){e|=0;var r=0;return e?(r=0|l[e+-4>>2],e=3&r,0|(1==(0|e)?0:(-8&r)-(0==(0|e)?8:4)|0)):0}function vr(e,r,i){e|=0,r|=0,i|=0;var n=0,t=0;return n=m,m=m+16|0,t=n,l[t>>2]=i,i=0|qr(e,r,t),m=n,0|i}function wr(e,r,i,n,t,a,o){e|=0,r|=0,i|=0,n|=0,t|=0,a|=0,o|=0,Ci[3&e](0|r,0|i,0|n,0|t,0|a,0|o)}function Sr(){var e=0;e=m,m=m+16|0,0|H(5140,6)?gr(4356,e):m=e}function yr(e,r,i){return e|=0,r|=0,i|=0,(0|i)<32?(p=r<<i|(e&(1<<i)-1<<32-i)>>>32-i,e<<i):(p=e<<i-32,0)}function Rr(e,r,i){return e|=0,r|=0,i|=0,(0|i)<32?(p=r>>>i,e>>>i|(r&(1<<i)-1)<<32-i):(p=0,r>>>i-32|0)}function gr(e,r){e|=0,r|=0;var i=0;i=m,m=m+16|0,l[i>>2]=r,r=0|l[26],Te(r,e,i),Ye(10,r),L()}function Or(){}function Cr(e,r,i,n){return e|=0,r|=0,i|=0,n|=0,n=r-n-(i>>>0>e>>>0|0)>>>0,0|(p=n,e-i>>>0|0)}function Nr(e,r){return e|=0,r|=0,r=r?0|me(0|l[r>>2],0|l[r+4>>2],e):0,0|(0|r?r:e)}function Pr(e,r,i,n,t,a){e|=0,r|=0,i|=0,n|=0,t|=0,a|=0,Si[3&e](0|r,0|i,0|n,0|t,0|a)}function Ir(e){e=+e;var r=0;return b[A>>3]=e,r=0|l[A>>2],p=0|l[A+4>>2],0|r}function Lr(e,r,i,n){return e|=0,r|=0,i|=0,n|=0,i=e+i>>>0,0|(p=r+n+(i>>>0<e>>>0|0)>>>0,0|i)}function Dr(e,r,i,n,t){e|=0,r|=0,i|=0,n|=0,t|=0,Ni[3&e](0|r,0|i,0|n,0|t)}function Fr(e){e|=0;var r=0;return e>>>0>4294963200&&(r=0|li(),l[r>>2]=0-e,e=-1),0|e}function Hr(e){return e|=0,e=e?0!=(0|Me(e,32,88,0)):0,1&e|0}function Ur(e,r,i,n){return e|=0,r|=0,i|=0,n|=0,0|wi[7&e](0|r,0|i,0|n)}function xr(e,r){e|=0,r|=0;var i=0;return i=0|Ke(e),((0|tr(e,1,i,r))!=(0|i))<<31>>31|0}function Br(e,r,i){e|=0,r|=0,i|=0,32&l[e>>2]||ge(r,i,e)}function Yr(e,r,i,n){e|=0,r|=0,i|=0,n|=0,gi[0&e](0|r,0|i,0|n)}function Xr(e){e|=0;var r=0;return r=m,m=m+e|0,m=m+15&-16,0|r}function Kr(e){e|=0;var r=0;return r=188+(0|ci())|0,0|Ge(e,0|l[r>>2])}function Vr(e,r){return e|=0,r|=0,e=e?0|Oe(e,r,0):0,0|e}function Gr(e,r,i){return e|=0,r|=0,(0|e)==(0|r)|0}function Wr(e,r){e|=0,r|=0;var i=0;return i=0|jr(0|e),0|(0==(0|r)?e:i)}function zr(e,r,i,n){return e|=0,r|=0,i|=0,n|=0,0|ee(e,r,i,n,0)}function jr(e){return e|=0,(255&e)<<24|(e>>8&255)<<16|(e>>16&255)<<8|e>>>24|0}function Jr(e,r,i,n,t,a){S(6)}function Zr(e,r){}function qr(e,r,i){return e|=0,r|=0,i|=0,0|Le(e,2147483647,r,i)}function Qr(e,r,i,n,t){S(1)}function $r(e){e|=0,_i(e)}function ei(e,r){return e|=0,r|=0,0|Ri[1&e](0|r)}function ri(e,r){e|=0,r|=0,m=e,r}function ii(e,r,i,n){S(7)}function ni(e,r){return e|=0,r|=0,0|Nr(e,r)}function ti(e,r){e|=0,r|=0,yi[7&e](0|r)}function ai(e,r,i){return S(0),0}function oi(e,r){return e=+e,r|=0,+ +Ve(e,r)}function ui(e,r,i){S(4)}function li(){return 64+(0|ci())|0}function fi(e){e|=0,Oi[3&e]()}function ci(){return 0|pi()}function si(e){e|=0,m=e}function _i(e){e|=0,j(e)}function di(e){e|=0,p=e}function hi(e){return e|=0,0|e}function Ei(){return 5072}function bi(e){return S(3),0}function Ti(e){}function Mi(e){return 0}function Ai(){return 0|p}function mi(){return 0|m}function ki(e){S(2)}function pi(){return 232}function vi(){S(5)}var wi=[ai,Ee,$e,ir,dr,xe,ai,ai],Si=[Qr,Fe,be,Qr],yi=[ki,Ti,$r,Ti,Ti,$r,kr,ki],Ri=[bi,mr],gi=[ui],Oi=[vi,Ce,Sr,vi],Ci=[Jr,ar,Ze,Jr],Ni=[ii,or,Qe,ii];return{stackSave:mi,_i64Subtract:Cr,_crn_get_bytes_per_block:se,setThrew:Zr,dynCall_viii:Yr,_bitshift64Lshr:Rr,_bitshift64Shl:yr,dynCall_viiii:Dr,setTempRet0:di,_crn_decompress:ie,_memset:He,_sbrk:nr,_memcpy:de,stackAlloc:Xr,_crn_get_height:Tr,dynCall_vi:ti,getTempRet0:Ai,_crn_get_levels:br,_crn_get_uncompressed_size:ce,_i64Add:Lr,dynCall_iiii:Ur,_emscripten_get_global_libc:Ei,dynCall_ii:ei,___udivdi3:zr,_llvm_bswap_i32:jr,dynCall_viiiii:Pr,___cxa_can_catch:fr,_free:j,runPostSets:Or,dynCall_viiiiii:wr,establishStackSpace:ri,___uremdi3:hr,___cxa_is_pointer_type:Hr,stackRestore:si,_malloc:Y,_emscripten_replace_memory:B,dynCall_v:fi,_crn_get_width:Mr,_crn_get_dxt_format:Er}}(Module.asmGlobalArg,Module.asmLibraryArg,buffer),stackSave=Module["stackSave"]=asm["stackSave"],getTempRet0=Module["getTempRet0"]=asm["getTempRet0"],_memset=Module["_memset"]=asm["_memset"],setThrew=Module["setThrew"]=asm["setThrew"],_bitshift64Lshr=Module["_bitshift64Lshr"]=asm["_bitshift64Lshr"],_bitshift64Shl=Module["_bitshift64Shl"]=asm["_bitshift64Shl"],setTempRet0=Module["setTempRet0"]=asm["setTempRet0"],_crn_decompress=Module["_crn_decompress"]=asm["_crn_decompress"],_crn_get_bytes_per_block=Module["_crn_get_bytes_per_block"]=asm["_crn_get_bytes_per_block"],_sbrk=Module["_sbrk"]=asm["_sbrk"],_memcpy=Module["_memcpy"]=asm["_memcpy"],stackAlloc=Module["stackAlloc"]=asm["stackAlloc"],_crn_get_height=Module["_crn_get_height"]=asm["_crn_get_height"],_i64Subtract=Module["_i64Subtract"]=asm["_i64Subtract"],_crn_get_levels=Module["_crn_get_levels"]=asm["_crn_get_levels"],_crn_get_uncompressed_size=Module["_crn_get_uncompressed_size"]=asm["_crn_get_uncompressed_size"],_i64Add=Module["_i64Add"]=asm["_i64Add"],_emscripten_get_global_libc=Module["_emscripten_get_global_libc"]=asm["_emscripten_get_global_libc"],___udivdi3=Module["___udivdi3"]=asm["___udivdi3"],_llvm_bswap_i32=Module["_llvm_bswap_i32"]=asm["_llvm_bswap_i32"],___cxa_can_catch=Module["___cxa_can_catch"]=asm["___cxa_can_catch"],_free=Module["_free"]=asm["_free"],runPostSets=Module["runPostSets"]=asm["runPostSets"],establishStackSpace=Module["establishStackSpace"]=asm["establishStackSpace"],___uremdi3=Module["___uremdi3"]=asm["___uremdi3"],___cxa_is_pointer_type=Module["___cxa_is_pointer_type"]=asm["___cxa_is_pointer_type"],stackRestore=Module["stackRestore"]=asm["stackRestore"],_malloc=Module["_malloc"]=asm["_malloc"],_emscripten_replace_memory=Module["_emscripten_replace_memory"]=asm["_emscripten_replace_memory"],_crn_get_width=Module["_crn_get_width"]=asm["_crn_get_width"],_crn_get_dxt_format=Module["_crn_get_dxt_format"]=asm["_crn_get_dxt_format"],dynCall_iiii=Module["dynCall_iiii"]=asm["dynCall_iiii"],dynCall_viiiii=Module["dynCall_viiiii"]=asm["dynCall_viiiii"],dynCall_vi=Module["dynCall_vi"]=asm["dynCall_vi"],dynCall_ii=Module["dynCall_ii"]=asm["dynCall_ii"],dynCall_viii=Module["dynCall_viii"]=asm["dynCall_viii"],dynCall_v=Module["dynCall_v"]=asm["dynCall_v"],dynCall_viiiiii=Module["dynCall_viiiiii"]=asm["dynCall_viiiiii"],dynCall_viiii=Module["dynCall_viiii"]=asm["dynCall_viiii"],initialStackTop;function ExitStatus(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function run(e){function r(){Module["calledRun"]||(Module["calledRun"]=!0,ABORT||(ensureInitRuntime(),preMain(),Module["onRuntimeInitialized"]&&Module["onRuntimeInitialized"](),Module["_main"]&&shouldRunNow&&Module["callMain"](e),postRun()))}e=e||Module["arguments"],runDependencies>0||(preRun(),runDependencies>0||Module["calledRun"]||(Module["setStatus"]?(Module["setStatus"]("Running..."),setTimeout((function(){setTimeout((function(){Module["setStatus"]("")}),1),r()}),1)):r()))}function exit(e,r){r&&Module["noExitRuntime"]||(Module["noExitRuntime"]||(ABORT=!0,STACKTOP=initialStackTop,exitRuntime(),Module["onExit"]&&Module["onExit"](e)),ENVIRONMENT_IS_NODE&&process["exit"](e),Module["quit"](e,new ExitStatus(e)))}Runtime.stackAlloc=Module["stackAlloc"],Runtime.stackSave=Module["stackSave"],Runtime.stackRestore=Module["stackRestore"],Runtime.establishStackSpace=Module["establishStackSpace"],Runtime.setTempRet0=Module["setTempRet0"],Runtime.getTempRet0=Module["getTempRet0"],Module["asm"]=asm,ExitStatus.prototype=new Error,ExitStatus.prototype.constructor=ExitStatus,dependenciesFulfilled=function e(){Module["calledRun"]||run(),Module["calledRun"]||(dependenciesFulfilled=e)},Module["callMain"]=Module.callMain=function(r){r=r||[],ensureInitRuntime();var i=r.length+1;function n(){for(var e=0;e<3;e++)t.push(0)}var t=[allocate(intArrayFromString(Module["thisProgram"]),"i8",ALLOC_NORMAL)];n();for(var a=0;a<i-1;a+=1)t.push(allocate(intArrayFromString(r[a]),"i8",ALLOC_NORMAL)),n();t.push(0),t=allocate(t,"i32",ALLOC_NORMAL);try{var o=Module["_main"](i,t,0);exit(o,!0)}catch(e){if(e instanceof ExitStatus)return;if("SimulateInfiniteLoop"==e)return void(Module["noExitRuntime"]=!0);var u=e;e&&"object"===typeof e&&e.stack&&(u=[e,e.stack]),Module.printErr("exception thrown: "+u),Module["quit"](1,e)}},Module["run"]=Module.run=run,Module["exit"]=Module.exit=exit;var abortDecorators=[];function abort(e){Module["onAbort"]&&Module["onAbort"](e),void 0!==e?(Module.print(e),Module.printErr(e),e=JSON.stringify(e)):e="",ABORT=!0;var r="\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.",i="abort("+e+") at "+stackTrace()+r;throw abortDecorators&&abortDecorators.forEach((function(r){i=r(i,e)})),i}if(Module["abort"]=Module.abort=abort,Module["preInit"]){"function"==typeof Module["preInit"]&&(Module["preInit"]=[Module["preInit"]]);while(Module["preInit"].length>0)Module["preInit"].pop()()}var shouldRunNow=!0;Module["noInitialRun"]&&(shouldRunNow=!1),Module["noExitRuntime"]=!0,run();var crunch=Module,CRN_FORMAT={cCRNFmtInvalid:-1,cCRNFmtDXT1:0,cCRNFmtDXT3:1,cCRNFmtDXT5:2},DXT_FORMAT_MAP={},dst,dxtData;DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT1]=PixelFormat.PixelFormat.RGB_DXT1,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT3]=PixelFormat.PixelFormat.RGBA_DXT3,DXT_FORMAT_MAP[CRN_FORMAT.cCRNFmtDXT5]=PixelFormat.PixelFormat.RGBA_DXT5;var cachedDstSize=0;function arrayBufferCopy(e,r,i,n){var t,a=i/4,o=n%4,u=new Uint32Array(e.buffer,0,(n-o)/4),l=new Uint32Array(r.buffer);for(t=0;t<u.length;t++)l[a+t]=u[t];for(t=n-o;t<n;t++)r[i+t]=e[t]}function transcodeCRNToDXT(e,r){var i=e.byteLength,n=new Uint8Array(e),t=crunch._malloc(i);arrayBufferCopy(n,crunch.HEAPU8,t,i);var a=crunch._crn_get_dxt_format(t,i),o=DXT_FORMAT_MAP[a];if(!when.defined(o))throw new RuntimeError.RuntimeError("Unsupported compressed format.");var u,l=crunch._crn_get_levels(t,i),f=crunch._crn_get_width(t,i),c=crunch._crn_get_height(t,i),s=0;for(u=0;u<l;++u)s+=PixelFormat.PixelFormat.compressedTextureSizeInBytes(o,f>>u,c>>u);cachedDstSize<s&&(when.defined(dst)&&crunch._free(dst),dst=crunch._malloc(s),dxtData=new Uint8Array(crunch.HEAPU8.buffer,dst,s),cachedDstSize=s),crunch._crn_decompress(t,i,dst,s,0,l),crunch._free(t);var _=PixelFormat.PixelFormat.compressedTextureSizeInBytes(o,f,c),d=dxtData.subarray(0,_),h=new Uint8Array(_);return h.set(d,0),r.push(h.buffer),new CompressedTextureBuffer(o,f,c,h)}var transcodeCRNToDXT$1=createTaskProcessorWorker(transcodeCRNToDXT);return transcodeCRNToDXT$1}));