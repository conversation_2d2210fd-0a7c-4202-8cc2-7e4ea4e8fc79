<template>
  <div class="device-info-container">
    <div class="title">
      <span>设备信息</span>
      <img style="padding-top: 10px; cursor: pointer" src="@/assets/CommonPopup/closeIcon.png" @click="handlerClose" />
    </div>
    <div class="sub-title">设备信息</div>
    <div class="info-box">
      <InfoCom :item="item" v-for="(item, index) in infoList" :key="index"></InfoCom>
    </div>
    <div class="sub-title">数据监测</div>
    <DatePicker
      v-model="dateRange"
      @change="handleDateChange"
      type="datetimerange"
      dateStyle="width:395px;height:24px"
      style="top: 186px; left: 12px"></DatePicker>
    <div class="tab-box">
      <div class="left"></div>
      <div class="tab">
        <div class="tab-item" v-for="(item, index) in tabList" :key="index" :class="{ active: activeTab === index }" @click="handleTabClick(index)">
          {{ item }}
        </div>
      </div>
      <div class="right"></div>
    </div>
  </div>
</template>

<script setup>
  import moment from 'moment'

  import DatePicker from '@/components/DatePicker/index.vue'
  import InfoCom from '@/components/InfoCom/index.vue'
  const dateRange = ref([moment().add(-1, 'W').format('YYYY-MM-DD HH:mm:ss'), moment().format('YYYY-MM-DD HH:mm:ss')])
  const infoList = ref([
    { name: '设备名称', value: '设备名称2222' },
    { name: '安装位置', value: '安装位置1111' }
  ])
  const tabList = ref(['运行状态', '故障状态', '遥控信号'])
  const activeTab = ref(0)
  const handleDateChange = (val) => {
    console.log(val)
  }
  const handleTabClick = (index) => {
    activeTab.value = index
  }
</script>

<style lang="scss" scoped>
  .device-info-container {
    width: 418px;
    height: 600px;
    background: url('@/assets/CommonPopup/popupBg4.png');
    background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7px 17px 0;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .sub-title {
      width: 395px;
      height: 24px;
      padding-left: 28px;
      margin: 29px 12px 12px;
      font-family: PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/CommonPopup/title.png');
      background-size: 395px 24px;
    }
    .info-box {
      margin-left: 14px;
    }
    .tab-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 50px 63px 20px;
      font-family: ' Alibaba PuHuiTi';
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .left {
        width: 12px;
        height: 16px;
        cursor: pointer;
        background: url('@/assets/CommonPopup/left.png');
        background-size: cover;
      }
      .right {
        width: 12px;
        height: 16px;
        cursor: pointer;
        background: url('@/assets/CommonPopup/right.png');
        background-size: cover;
      }
      .tab {
        display: flex;
      }
      .tab-item {
        width: 79px;
        height: 25px;
        text-align: center;
        cursor: pointer;
        &.active {
          background: url('@/assets/CommonPopup/tab.png');
          background-size: cover;
        }
      }
    }
  }
</style>
