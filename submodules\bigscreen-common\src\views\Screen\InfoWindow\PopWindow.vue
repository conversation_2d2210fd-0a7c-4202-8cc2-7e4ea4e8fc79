<!--
 * @Description: 弹窗组件
 * @Autor: qian
 * @Date: 2023-06-19 17:07:00
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2023-10-12 10:45:28
-->
<template>
  <div :id="id" class="pop-window-container" v-drag="{ draggableClass }">
    <component :is="showComponent" :data="data" :id="id"></component>
  </div>
</template>

<script setup>
  import vDrag from '@Common/directives/drag/index.js'
  const modules = import.meta.glob('./components/**/*.vue', { eager: true })
  import { onMounted, onUnmounted, reactive, shallowRef, toRefs } from 'vue'
  import { provide } from 'vue'

  // import useStore from '@/store'
  import { removeDialogById } from '@Common/views/Screen/InfoWindow/popWindow.js'
  // const { storeScreenPage } = useStore()

  const props = defineProps({
    componentName: {
      type: String,
      default: ''
    },
    left: {
      type: [String, Number],
      default: null
    },
    top: {
      type: [String, Number],
      default: null
    },
    right: {
      type: [String, Number],
      default: null
    },
    bottom: {
      type: [String, Number],
      default: null
    },
    id: {
      type: String,
      default: 'popwindow_' + Math.ceil(Math.random() * 1000000)
    },
    data: {
      type: Object,
      default: () => ({})
    },
    closeFunc: {
      type: Object,
      default: null
    },
    popPosition: {
      type: Array,
      default: () => []
    },
    offset: {
      type: Array,
      default: () => []
    },
    isFollow: {
      type: Boolean,
      default: false
    },
    draggableClass: {
      type: String,
      default: 'popup-title'
    },
  })
  const showComponent = shallowRef('')
  console.log('path-----', props)
  const componentPath = `./components/${props.componentName}${props.componentName.endsWith('.vue') ? '' : '/index.vue'}`
  const module = modules[componentPath]
  if (module) {
    showComponent.value = module.default
  } else {
    console.error(componentPath + '组件路径错误')
  }

  const state = reactive({
    popupLeft: '',
    popupTop: '',
    popupRight: '',
    popupBottom: '',
    draggableClass: 'popup-title',
    zIndex: 999
  })
  // 判断是否为数字类型
  const isNumber = (value) => {
    return typeof value === 'number'
  }
  
  state.popupLeft = props.left !== null && isNumber(props.left)? props.left + 'px' : props.left
  state.popupTop = props.top !== null && isNumber(props.top)? props.top + 'px' : props.top
  state.popupRight = props.right !== null && isNumber(props.right)? props.right + 'px' :  props.right
  state.popupBottom = props.bottom !== null && isNumber(props.bottom)? props.bottom + 'px' : props.bottom
  state.draggableClass = props.draggableClass

  const { popupLeft, popupTop, draggableClass, zIndex, popupRight, popupBottom } = toRefs(state)
  const handleClose = () => {
    if (props.closeFunc) {
      props.closeFunc()
    }
    removeDialogById(props.id)
  }
  provide('handleClose', handleClose)

  onMounted(() => {
    if (props.isFollow) {
      state.zIndex = 2
      state.draggableClass = 'none'
      state.popupLeft = __Cesium.getWindowPositionByWgs84(props.popPosition).x + 'px'
      state.popupTop = __Cesium.getWindowPositionByWgs84(props.popPosition).y + 'px'
      __Cesium.viewer.scene.postRender.addEventListener(updateWheel)
    }
  })
  onUnmounted(() => {
    props.isFollow ? __Cesium.viewer.scene.postRender.removeEventListener(updateWheel) : null
  })
  // 订阅位置信息
  const updateWheel = () => {
    const p = __Cesium.getWindowPositionByWgs84(props.popPosition)

    state.popupLeft = p.x + (props.offset[0] || 0) + 'px'
    state.popupTop = p.y + (props.offset[1] || 0) + 'px'
  }
</script>
<style lang="scss" scoped>
  .pop-window-container {
    position: absolute;
    top: v-bind('popupTop');
    left: v-bind('popupLeft');
    right: v-bind('popupRight');
    bottom: v-bind('popupBottom');
    z-index: v-bind('zIndex');
    overflow: hidden;

    // cursor: move;

    // background: #ccc;
  }
</style>
