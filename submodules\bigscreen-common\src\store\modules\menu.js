import { defineStore } from 'pinia'
const useMenuStore = defineStore('menu', {
  state: () => ({
    interface: [],
    switchButton: false,
    functionCall: {},
    clickFunctionCall: {},
    keyword: 'code',
    subs: 'subs'
  }),
  getters: {},
  actions: {
    // 不传入bool则取反
    setActive(key, bool) {
      const item = this.getItemByCode(key)
      if (item.active === undefined) {
        item.active = false
      }
      if (bool === undefined) {
        item.active = !item.active
      } else {
        item.active = bool
      }
    },
    setSubs(key) {
      this.subs = key
    },
    // 返回所有设备，返回promise
    getDevices() {
      const func = (resolve) => {
        if (this.interface.length == 0) {
          setTimeout(func, 500, resolve)
        } else {
          resolve(this.interface)
        }
      }
      const promises = [
        new Promise((resolve) => {
          func(resolve)
        }),
        new Promise((resolve, reject) => {
          setTimeout(reject, 10000, new Error('获取设备列表超时'))
        })
      ]
      return Promise.race(promises)
    },
    /**
     * 返回所有设备，包含子菜单项目
     * @param {Array} filter 需要排除的层级数组，如[0]表示排除最顶级的菜单项
     */
    getAllDevices(filter = []) {
      const func = (resolve) => {
        if (this.interface.length == 0) {
          setTimeout(func, 500, resolve)
        } else {
          const re = []
          const list = []
          this.interface.forEach((_) => list.push({ val: _, level: 0 }))
          let item = list.shift()
          // console.log(filter, 'filter')
          while (item) {
            if (!filter.includes(item.level)) {
              re.push(item.val)
            }
            if (item.val[this.subs]) {
              item.val[this.subs].forEach((_) => list.push({ val: _, level: item.level + 1 }))
              // list.push()
            }
            item = list.shift()
          }

          // console.log(re, 're')
          resolve(re)
        }
      }
      const promises = [
        new Promise((resolve) => {
          func(resolve)
        }),
        new Promise((resolve, reject) => {
          setTimeout(reject, 10000, new Error('获取设备列表超时'))
        })
      ]
      return Promise.race(promises)
    },
    // 通过code获取item
    getItemByCode(code) {
      const help = (val) => {
        // console.log('遍历子菜单')
        // console.log(val)
        for (const subItem of val) {
          if (subItem[this.keyword] == code) {
            return subItem
          }
          if (subItem[this.subs] != null) {
            const result = help(subItem[this.subs])
            if (result != null) {
              return result
            }
          }
        }
        return null
      }
      for (const item of this.interface) {
        if (item[this.keyword] == code) {
          return item
        }
        // console.log('遍历')
        // console.log(item)
        if (item[this.subs]) {
          const re = help(item[this.subs])
          if (re) {
            return re
          }
        }
      }
      return null
    },
    // 调用某个code对应的函数
    callFunc(code) {
      const item = this.getItemByCode(code)
      const func = (resolve) => {
        // console.log(code + '找到的item' + item)
        // console.log(this.functionCall[code])
        // console.log()
        if (!this.functionCall[code]) {
          setTimeout(() => {
            func(resolve)
          }, 500)
        } else {
          const temp = this.functionCall[code].find((_) => _.id == 0)
          if (!temp) {
            setTimeout(() => {
              func(resolve)
            }, 500)
          } else {
            resolve(temp)
          }
        }
      }
      const promises = [
        new Promise((resolve) => {
          func(resolve)
        }),
        new Promise((resolve) => {
          setTimeout(() => {
            resolve(null)
          }, 10000)
        })
      ]
      Promise.race(promises).then((temp) => {
        if (temp) {
          if (!temp.disabled) {
            if (temp.context) {
              temp.func.call(temp.context, item.active, item)
            } else {
              temp.func.call(this, item.active, item)
            }
          }
          for (const funcObj of this.functionCall[code]) {
            if (!funcObj.disabled && funcObj.id != 0) {
              if (funcObj.context) {
                funcObj.func.call(funcObj.context, item.active, item)
              } else {
                funcObj.func.call(this, item.active, item)
              }
            }
          }
          console.table([
            { 名称: '调用了函数', 内容: code },
            { 名称: '执行了的函数有', 内容: this.functionCall[code].filter((_) => _.disabled == false) },
            { 名称: '存在但禁止的函数有', 内容: this.functionCall[code].filter((_) => _.disabled == true) }
          ])
        } else {
          console.log('调用' + code + '函数超时')
        }
      })
      // const temp = this.functionCall[code].find((_) => _.id == 0)
      // console.log('调用了' + code + '函数')
      // console.log('执行了的函数有')
      // console.log(this.functionCall[code].filter((_) => _.disabled == false))
      // console.log('存在但禁止的函数有')
      // console.log(this.functionCall[code].filter((_) => _.disabled == true))
    },
    callClickFunc(code, e, pointState, obj) {
      // let obj = {}
      const item = this.getItemByCode(code)
      this.clickFunctionCall[code].filter((_) => _.disabled == false).forEach((funcObj) => funcObj.func(item, e, pointState, obj))
      // console.log('调用了' + code + '撒点点击函数')
      // console.log('执行了的函数有')
      // console.log(this.clickFunctionCall[code].filter((_) => _.disabled == false))
      // console.log('存在但禁止的函数有')
      // console.log(this.clickFunctionCall[code].filter((_) => _.disabled == true))
      console.table([
        { 名称: '调用了函数', 内容: code },
        { 名称: '执行了的函数有', 内容: this.functionCall[code].filter((_) => _.disabled == false) },
        { 名称: '存在但禁止的函数有', 内容: this.functionCall[code].filter((_) => _.disabled == true) }
      ])
    },
    // 设置设备列表
    setDevices(arr) {
      // const { storeCesium, bottomPop } = useStore()
      arr.forEach((_) => {
        _.active = false
        if (this.functionCall[_.code] === undefined) this.functionCall[_.code] = []
        if (this.clickFunctionCall[_.code] === undefined) this.clickFunctionCall[_.code] = []
      })

      this.interface.push(...arr)
    },
    findFunction(code, id) {
      // console.log(this.functionCall + '查找' + id)
      // if (this.functionCall[code] == undefined) {
      //   this.functionCall[code] = []
      // }
      if (this.functionCall[code] === undefined) {
        this.functionCall[code] = []
      }
      return this.functionCall[code].find((val) => val.id == id)
    },
    findClickFunc(code, id) {
      if (this.clickFunctionCall[code] == undefined) {
        this.clickFunctionCall[code] = []
      }
      return this.clickFunctionCall[code].find((val) => val.id == id)
    },

    /**
     * @typedef {Object} ActiveFuncObj
     * @property {ActiveCallback} func  要调用的函数
     * @property {Object} context 要留存的上下文
     * @property {boolean} disabled 是否禁用调用
     * @property {string} id 用于辨认函数对象，id=0为默认撒点行为,自己传入时尽量避免纯数字id
     *
     */
    /**
     * @callback ActiveCallback
     * @param {boolean} bool 对应code的bool值
     * @param {Object} item 对应code的item项
     */

    /**
     * @param {string} code -要调用的code
     * @param {ActiveFuncObj} funcObj 一个对象或者函数
     * @description funcObj是对象时
     * @property {ActiveCallback} func  要调用的函数
     * @property {Object} context 要留存的上下文
     * @property {boolean} disabled 是否禁用调用
     * @property {string} id 用于辨认函数对象，id=0为默认撒点行为,自己传入时尽量避免纯数字id
     * @description func函数
     * @param {boolean} bool 对应code的bool值
     * @param {Object} item 对应code的item项
     * @returns {String} 生成或传入的id
     */
    addFunction(code, funcObj) {
      if (this.functionCall[code] == undefined) this.functionCall[code] = []
      if (funcObj.id === undefined) {
        funcObj.id = Math.floor(Math.random() * 1000) + 1
        while (this.findFunction(code, funcObj.id) != undefined) {
          funcObj.id = Math.floor(Math.random() * 1000) + 1
        }
      } else {
        const func = this.findFunction(code, funcObj.id)
        if (func != undefined) {
          if (func.func == null) {
            func.func = funcObj.func
            func.context = funcObj.context
          } else {
            console.log('重复的id,在添加' + code + '函数时')
          }
          return
        }
      }
      this.functionCall[code].push(funcObj)
      return funcObj.id
    },
    deleteFunction(code, id) {
      this.functionCall[code].splice(
        this.functionCall[code].findIndex((val) => val.id == id),
        1
      )
    },
    /**
     * @description 禁用或解禁某个函数，id传入0为禁用默认行为，传入-1为禁止所有行为
     * @param {string} code 要禁用的函数所属code
     * @param {string} id 要更改状态的函数id,-1为更改所有函数
     * @param {boolean} disable 是否禁用，默认为true
     */
    disableFunction(code, id, disable = true) {
      if (id == '-1') {
        this.functionCall[code].forEach((funcObj) => {
          funcObj.disabled = disable
        })
      } else {
        console.log(111)
        const item = this.findFunction(code, id)
        console.log('item')
        console.log(item)
        if (item === undefined) {
          this.functionCall[code].push({ func: null, id: id, disabled: disable })
        } else {
          item.disabled = disable
        }
      }
    },
    /**
     * @callback ActiveClickCallback
     * @param {Object} item 对应设备种类的Item对象
     * @param {Object} e 点击时传入的事件对象
     * @param {Object} pointState 撒点状态
     * @param {Object} obj 对应的设备
     */

    /**
     * @typedef {Object} ActiveClickFuncObj
     * @property {ActiveClickCallback} func  要调用的函数
     * @property {Object} context 要留存的上下文,默认为当前store对象
     * @property {boolean} disabled 是否禁用调用,默认为否
     * @property {string} id 用于辨认函数对象，id=0为默认点击弹窗行为,自己传入时尽量避免纯数字id
     */
    /**
     * @description 添加code激活时的回调函数
     * @param {string} code -要调用的code
     * @param {ActiveClickFuncObj|ActiveClickCallback} funcObj 一个对象或者函数
     * @description ActiveClickFuncObj
     * @property {ActiveCallback} func  要调用的函数
     * @property {Object} context 要留存的上下文,默认为当前store对象
     * @property {boolean} disabled 是否禁用调用,默认为否
     * @property {string} id 用于辨认函数对象，id=0为默认点击弹窗行为,自己传入时尽量避免纯数字id
     * @description ActiveClickCallback
     * @param {Object|Null} obj 对应设备的基础信息和图表信息,在同步函数中为null
     * @param {Object} item 对应设备种类的Item对象
     * @param {Object} e 点击时传入的事件对象
     * @param {Object} movement 点击时传入的对象
     * @returns {ActiveClickFuncObj} 传入或生成的对象
     */
    addClickFunction(code, funcObj) {
      // console.log(typeof funcObj)
      if (this.clickFunctionCall[code] == undefined) this.clickFunctionCall[code] = []
      if (funcObj.id === undefined) {
        funcObj.id = Math.floor(Math.random() * 1000) + 1
        while (this.findClickFunc(code, funcObj.id) != undefined) {
          funcObj.id = Math.floor(Math.random() * 1000) + 1
        }
      } else {
        const func = this.findClickFunc(code, funcObj.id)
        if (func != undefined) {
          if (func.func == null) {
            func.func = funcObj.func
            func.context = funcObj.context
          } else {
            console.log('重复的id,在添加' + code + '点击函数时')
          }
          return
        }
      }
      this.clickFunctionCall[code].push(funcObj)
      return funcObj.id
    },
    deleteClickFunction(code, id) {
      this.clickFunctionCall[code].splice(
        this.clickFunctionCall[code].findIndex((val) => val.id == id),
        1
      )
    },
    /**
     * @description 禁用或解禁某个点击函数，id传入0为禁用默认行为，传入-1为禁止所有行为
     * @param {string} code 要禁用的函数所属code
     * @param {string} id 要更改状态的函数id,-1为更改所有函数
     * @param {boolean} disable 是否禁用，默认为true
     */
    disableClickFunction(code, id, disable = true) {
      if (id == '-1') {
        this.functionCall[code].forEach((funcObj) => {
          funcObj.disabled = disable
        })
      } else {
        const item = this.findClickFunc(code, id)
        if (item === undefined) {
          this.functionCall[code].push({ func: null, id: id, disabled: disable })
        } else {
          item.disabled = disable
        }
      }
    },
    // 获取某个对象的子菜单列表
    getSubs(code) {
      return this.getItemByCode(code)[this.subs]
    }
  }
})

export default useMenuStore
