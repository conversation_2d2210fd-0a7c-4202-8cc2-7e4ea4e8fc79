<template>
  <div class="echarts-demo-container" :class="{ dark: isDark }">
    <h1>Echarts Demo</h1>
    <el-switch v-model="isDark" class="switch-box"></el-switch>
    <el-text size="large">Bar</el-text>
    <el-row>
      <el-col :span="8">
        bar1:
        <MyChart ref="barRef1" width="100%" :option="bar1"></MyChart>
      </el-col>
      <el-col :span="8">
        bar2:
        <MyChart ref="barRef2" width="100%" :option="bar2"></MyChart>
      </el-col>
      <el-col :span="8">
        bar3:
        <MyChart ref="barRef3" width="100%" :option="bar3"></MyChart>
      </el-col>
      <el-col :span="8">
        bar4:
        <MyChart ref="barRef4" width="100%" :option="bar4"></MyChart>
      </el-col>
      <el-col :span="8">
        bar5:
        <MyChart ref="barRef5" width="100%" :option="bar5"></MyChart>
      </el-col>
      <el-col :span="8">
        bar6:
        <MyChart ref="barRef6" width="100%" :option="bar6"></MyChart>
      </el-col>
      <el-col :span="8">
        bar7:
        <MyChart ref="barRef7" width="100%" :option="bar7"></MyChart>
      </el-col>
      <el-col :span="8">
        bar8:
        <MyChart ref="barRef8" width="100%" :option="bar8"></MyChart>
      </el-col>
      <el-col :span="8">
        bar9:
        <MyChart ref="barRef9" width="100%" :option="bar9"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">Line</el-text>
    <el-row>
      <el-col :span="8">
        line1:
        <MyChart ref="lineRef1" width="100%" :option="lineOption1"></MyChart>
      </el-col>
      <el-col :span="8">
        line2:
        <MyChart ref="lineRef2" width="100%" :option="line2"></MyChart>
      </el-col>
      <el-col :span="8">
        line3:
        <MyChart ref="lineRef3" width="100%" :option="line3"></MyChart>
      </el-col>
      <el-col :span="8">
        line4:
        <MyChart ref="lineRef4" width="100%" :option="line4"></MyChart>
      </el-col>
      <el-col :span="8">
        line5:
        <MyChart ref="lineRef5" width="100%" :option="line5"></MyChart>
      </el-col>
      <el-col :span="8">
        line6:
        <MyChart ref="lineRef6" width="100%" :option="line6"></MyChart>
      </el-col>
      <el-col :span="8">
        line7:
        <MyChart ref="lineRef7" width="100%" :option="line7"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">Gauge&&Radar</el-text>
    <el-row>
      <el-col :span="6">
        gauge1:
        <MyChart ref="gaugeRef1" width="100%" :option="gauge1"></MyChart>
      </el-col>
      <el-col :span="6">
        radar1:
        <MyChart ref="radarRef1" width="100%" :option="radar1"></MyChart>
      </el-col>
      <el-col :span="6">
        radar2:
        <MyChart ref="radarRef2" width="100%" :option="radar2"></MyChart>
      </el-col>
      <el-col :span="6">
        radar3:
        <MyChart ref="radarRef3" width="100%" :option="radar3"></MyChart>
      </el-col>
      <el-col :span="6">
        radar4:
        <MyChart ref="radarRef4" width="100%" :option="radar4"></MyChart>
      </el-col>
      <el-col :span="6">
        radar5:
        <MyChart ref="radarRef5" width="100%" :option="radar5"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">Heat</el-text>
    <el-row>
      <el-col :span="8">
        heat1:
        <MyChart ref="heatRef1" width="800px" :option="heat1"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">Scatter</el-text>
    <el-row>
      <el-col :span="8">
        scatter1:
        <MyChart width="100%" :option="scatter1"></MyChart>
      </el-col>
      <el-col :span="8">
        scatter2:
        <MyChart width="100%" :option="scatter2"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">极坐标</el-text>
    <el-row>
      <el-col :span="8">
        polar1:
        <MyChart width="100%" :option="polar1"></MyChart>
      </el-col>
    </el-row>
    <el-text size="large">liquidfill</el-text>
    <el-row>
      <el-col :span="8">
        liquidfill1:
        <MyChart width="100%" :option="liquidfill1"></MyChart>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  import MyChart from '@Common/components/MyChart/index.vue'

  import {
    bar1,
    bar2,
    bar3,
    bar4,
    bar5,
    bar6,
    bar7,
    bar8,
    bar9,
    gauge1,
    heat1,
    line1,
    line2,
    line3,
    line4,
    line5,
    line6,
    line7,
    liquidfill1,
    polar1,
    radar1,
    radar2,
    radar3,
    radar4,
    radar5,
    scatter1,
    scatter2,
    setOptionData
  } from '@Common/components/MyChart/index.js'
  import { useTitle } from '@vueuse/core'
  const title = useTitle()
  title.value = '公共库-Echarts Demo'

  // const barrrr = deepClone(bar1)
  // barrrr.color = ['red', 'green']
  // const barRef1 = ref(null)
  // const barOption1 = setOptionData(barrrr)

  // const barRef2 = ref(null)
  // const barOption2 = setOptionData(bar1, { xData: ['周一', '周二', '周三'], seriesData: [[20, 30, 40]] })

  // const barRef3 = ref(null)
  // const barOption3 = setOptionData(bar1, { xData: ['1月', '2月', '3月'], seriesData: [[2, 6, 3]] })

  const lineRef1 = ref(null)
  const lineOption1 = setOptionData(line1, { xData: ['1月', '2月', '3月'], seriesData: [2, 6, 3] })

  const isDark = ref(true)
</script>

<style lang="scss" scoped>
  .echarts-demo-container {
    color: #0b3053;
    background: #ffffff;
    &.dark {
      color: #ffffff;
      background: #0b3053;
    }
    .switch-box {
      position: absolute;
      right: 20px;
    }
  }
</style>
