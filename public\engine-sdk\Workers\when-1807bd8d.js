define(["exports"],(function(n){"use strict";function r(n){return void 0!==n&&null!==n}function t(n,r){return void 0!==n&&null!==n?n:r}var e,u,o;function i(n,r,t,e){return c(n).then(r,t,e)}function c(n){var r,t;return n instanceof s?r=n:p(n)?(t=a(),n.then((function(n){t.resolve(n)}),(function(n){t.reject(n)}),(function(n){t.progress(n)})),r=t.promise):r=h(n),r}function f(n){return i(n,l)}function s(n){this.then=n}function h(n){var r=new s((function(r){try{return c(r?r(n):n)}catch(t){return l(t)}}));return r}function l(n){var r=new s((function(r,t){try{return t?c(t(n)):l(n)}catch(e){return l(e)}}));return r}function a(){var n,r,t,e,u,i,f;return r=new s(h),n={then:h,resolve:p,reject:v,progress:g,promise:r,resolver:{resolve:p,reject:v,progress:g}},t=[],e=[],u=function(n,r,u){var o,i;return o=a(),i="function"===typeof u?function(n){try{o.progress(u(n))}catch(r){o.progress(r)}}:function(n){o.progress(n)},t.push((function(t){t.then(n,r).then(o.resolve,o.reject,i)})),e.push(i),o.promise},i=function(n){return b(e,n),n},f=function(n){return n=c(n),u=n.then,f=c,i=M,b(t,n),e=t=o,n},n;function h(n,r,t){return u(n,r,t)}function p(n){return f(n)}function v(n){return f(l(n))}function g(n){return i(n)}}function p(n){return n&&"function"===typeof n.then}function v(n,r,t,e,u){return E(2,arguments),i(n,(function(n){var o,c,f,s,h,l,p,v,g,y;if(g=n.length>>>0,o=Math.max(0,Math.min(r,g)),f=[],c=g-o+1,s=[],h=a(),o)for(v=h.progress,p=function(n){s.push(n),--c||(l=p=M,h.reject(s))},l=function(n){f.push(n),--o||(l=p=M,h.resolve(f))},y=0;y<g;++y)y in n&&i(n[y],d,w,v);else h.resolve(f);return h.then(t,e,u);function w(n){p(n)}function d(n){l(n)}}))}function g(n,r,t,e){function u(n){return r?r(n[0]):n[0]}return v(n,1,u,t,e)}function y(n,r,t,e){return E(1,arguments),d(n,O).then(r,t,e)}function w(){return d(arguments,O)}function d(n,r){return i(n,(function(n){var t,e,u,o,c,f;if(u=e=n.length>>>0,t=[],f=a(),u)for(o=function(n,e){i(n,r).then((function(n){t[e]=n,--u||f.resolve(t)}),f.reject)},c=0;c<e;c++)c in n?o(n[c],c):--u;else f.resolve(t);return f.promise}))}function j(n,r){var t=u.call(arguments,1);return i(n,(function(n){var u;return u=n.length,t[0]=function(n,t,e){return i(n,(function(n){return i(t,(function(t){return r(n,t,e,u)}))}))},e.apply(n,t)}))}function m(n,r,t){var e=arguments.length>2;return i(n,(function(n){return n=e?t:n,r.resolve(n),n}),(function(n){return r.reject(n),l(n)}),r.progress)}function b(n,r){var t,e=0;while(t=n[e++])t(r)}function E(n,r){var t,e=r.length;while(e>n)if(t=r[--e],null!=t&&"function"!=typeof t)throw new Error("arg "+e+" must be a function")}function M(){}function O(n){return n}t.EMPTY_OBJECT=Object.freeze({}),i.defer=a,i.resolve=c,i.reject=f,i.join=w,i.all=y,i.map=d,i.reduce=j,i.any=g,i.some=v,i.chain=m,i.isPromise=p,s.prototype={always:function(n,r){return this.then(n,n,r)},otherwise:function(n){return this.then(o,n)},yield:function(n){return this.then((function(){return n}))},spread:function(n){return this.then((function(r){return y(r,(function(r){return n.apply(o,r)}))}))}},u=[].slice,e=[].reduce||function(n){var r,t,e,u,o;if(o=0,r=Object(this),u=r.length>>>0,t=arguments,t.length<=1)for(;;){if(o in r){e=r[o++];break}if(++o>=u)throw new TypeError}else e=t[1];for(;o<u;++o)o in r&&(e=n(e,r[o],o,r));return e},n.defaultValue=t,n.defined=r,n.when=i}));