import useStore from '@/store'

export const screenCompontent = {
  addCom:(param)=>{//增加更新页面元素
    const {storeScreenComWindowData} = useStore()
    let isTrue = false
    let isIndex 
    storeScreenComWindowData.screenComArr.map((item,index)=>{
      if(item.name==param.name){
        isTrue = true
        isIndex = index
      }
    })
    if(isTrue){//更新
      storeScreenComWindowData.screenComArr[isIndex] = Object.assign(storeScreenComWindowData.screenComArr[isIndex],param)
    }else{//增加
      storeScreenComWindowData.screenComArr.push(param)
    }
  },
  delCom:(name)=>{//删除页面元素
    const {storeScreenComWindowData} = useStore()
    let isTrue = false
    let isIndex 
    storeScreenComWindowData.screenComArr.map((item,index)=>{
      if(item.name==name){
        isTrue = true
        isIndex = index
      }
    })
    if(isTrue) storeScreenComWindowData.screenComArr.splice(isIndex,1)//删除页面元素
  },
  allDelCom:()=>{
    const {storeScreenComWindowData} = useStore()
    storeScreenComWindowData.screenComArr = []
  },



  addPop:(param)=>{//增加更新页面元素
    const {storeScreenComWindowData} = useStore()
    let isTrue = false
    let isIndex 
    storeScreenComWindowData.screenPopArr.map((item,index)=>{
      if(item.name==param.name){
        isTrue = true
        isIndex = index
      }
    })
    if(isTrue){//更新
      storeScreenComWindowData.screenPopArr[isIndex] = Object.assign(storeScreenComWindowData.screenPopArr[isIndex],param)
    }else{//增加
      storeScreenComWindowData.screenPopArr.push(param)
    }
  },
  delPop:(name)=>{//删除页面元素
    const {storeScreenComWindowData} = useStore()
    let isTrue = false
    let isIndex 
    storeScreenComWindowData.screenPopArr.map((item,index)=>{
      if(item.name==name){
        isTrue = true
        isIndex = index
      }
    })
    if(isTrue) storeScreenComWindowData.screenPopArr.splice(isIndex,1)//删除页面元素
  },
  allDelPop:()=>{
    const {storeScreenComWindowData} = useStore()
    storeScreenComWindowData.screenPopArr = []
  }
}