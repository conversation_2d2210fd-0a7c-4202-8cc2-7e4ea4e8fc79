import defined from"../../Core/defined.js";import defaultValue from"../../Core/defaultValue.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import Fullscreen from"../../Core/Fullscreen.js";import knockout from"../../ThirdParty/knockout.js";import createCommand from"../createCommand.js";import getElement from"../getElement.js";function FullscreenButtonViewModel(e,t){defined(t)||(t=document.body),t=getElement(t);var n=this,l=knockout.observable(Fullscreen.fullscreen),r=knockout.observable(Fullscreen.enabled),o=t.ownerDocument;this.isFullscreen=void 0,knockout.defineProperty(this,"isFullscreen",{get:function(){return l()}}),this.isFullscreenEnabled=void 0,knockout.defineProperty(this,"isFullscreenEnabled",{get:function(){return r()},set:function(e){r(e&&Fullscreen.enabled)}}),this.tooltip=void 0,knockout.defineProperty(this,"tooltip",(function(){return this.isFullscreenEnabled?l()?"Exit full screen":"Full screen":"Full screen unavailable"})),this._command=createCommand((function(){Fullscreen.fullscreen?Fullscreen.exitFullscreen():Fullscreen.requestFullscreen(n._fullscreenElement)}),knockout.getObservable(this,"isFullscreenEnabled")),this._fullscreenElement=defaultValue(getElement(e),o.body),this._callback=function(){l(Fullscreen.fullscreen)},o.addEventListener(Fullscreen.changeEventName,this._callback)}Object.defineProperties(FullscreenButtonViewModel.prototype,{fullscreenElement:{get:function(){return this._fullscreenElement},set:function(e){if(!(e instanceof Element))throw new DeveloperError("value must be a valid Element.");this._fullscreenElement=e}},command:{get:function(){return this._command}}}),FullscreenButtonViewModel.prototype.isDestroyed=function(){return!1},FullscreenButtonViewModel.prototype.destroy=function(){document.removeEventListener(Fullscreen.changeEventName,this._callback),destroyObject(this)};export default FullscreenButtonViewModel;