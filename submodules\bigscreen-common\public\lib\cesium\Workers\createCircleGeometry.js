/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Cartesian3-bb0e6278","./defined-3b3eb2ba","./EllipseGeometry-fee2055c","./Rectangle-9bffefe4","./VertexFormat-86c096b8","./Math-b5f4d889","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./EllipseGeometryLibrary-07d021fe","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryOffsetAttribute-5a4c2801","./GeometryPipeline-9dbd2054","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,i,r,o,n,s,a,l,d,m,c,u,p,y,_,G,h,x,f,g,b,E){"use strict";function w(e){const r=(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new i.EllipseGeometry(o),this._workerName="createCircleGeometry"}w.packedLength=i.EllipseGeometry.packedLength,w.pack=function(e,t,r){return i.EllipseGeometry.pack(e._ellipseGeometry,t,r)};const v=new i.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),A={center:new e.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new o.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return w.unpack=function(n,s,a){const l=i.EllipseGeometry.unpack(n,s,v);return A.center=e.Cartesian3.clone(l._center,A.center),A.ellipsoid=r.Ellipsoid.clone(l._ellipsoid,A.ellipsoid),A.height=l._height,A.extrudedHeight=l._extrudedHeight,A.granularity=l._granularity,A.vertexFormat=o.VertexFormat.clone(l._vertexFormat,A.vertexFormat),A.stRotation=l._stRotation,A.shadowVolume=l._shadowVolume,t.defined(a)?(A.semiMajorAxis=l._semiMajorAxis,A.semiMinorAxis=l._semiMinorAxis,a._ellipseGeometry=new i.EllipseGeometry(A),a):(A.radius=l._semiMajorAxis,new w(A))},w.createGeometry=function(e){return i.EllipseGeometry.createGeometry(e._ellipseGeometry)},w.createShadowVolume=function(e,t,i){const r=e._ellipseGeometry._granularity,n=e._ellipseGeometry._ellipsoid,s=t(r,n),a=i(r,n);return new w({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:n,stRotation:e._ellipseGeometry._stRotation,granularity:r,extrudedHeight:s,height:a,vertexFormat:o.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(w.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(i,o){return t.defined(o)&&(i=w.unpack(i,o)),i._ellipseGeometry._center=e.Cartesian3.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(i._ellipseGeometry._ellipsoid),w.createGeometry(i)}}));
