<!--
 * @Author: wangchen <EMAIL>
 * @Date: 2024-05-13 09:42:02
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-28 14:56:20
 * @FilePath: \bigscreen-qj-web\src\views\Screen\MapPopupWindow\Components\WorkList\index.vue
 * @Description:
 *
-->
<template>
  <div class="work-list-container">
    <PopupBg :title="props.data.title" width="800px" height="490px" @close="handlerClose">
      <tableList list-width="780px" list-data-height="360px" :head-list="tableHead" :list="tableData" @click-item="tableClick($event)"></tableList>
    </PopupBg>
  </div>
</template>

<script setup>
  import { onUnmounted, ref } from 'vue'
  import PopupBg from '@/components/PopupBg/index.vue'
  import tableList from '@/components/TableList/index.vue'

  import { toUe5 } from '@/hooks/useUE/tools.js'
  const { appContext } = getCurrentInstance()

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    data: {
      type: String,
      default: '作业列表'
    }
  })
  const emit = defineEmits(['close'])
  const handlerClose = () => {
    lib.popWindow.removeDialog('maintenanceWindow')
    lib.popWindow.removeDialog('WorkList')
    emit('close')
  }
  const tableHead = ref([])
  const tableData = ref([])
  const typeId = lib.typeIdMap.动态养护
  watch(
    () => props.data,
    () => {
      if (props.data.title === '作业列表') {
        tableHead.value = [
          { label: '作业名称', prop: 'name' },
          { label: '作业类型', prop: 'typeName' },
          { label: '作业时间', prop: 'time' },
          { label: '当前状态', prop: 'statusName' }
        ]
      } else {
        tableHead.value = [
          { label: '缺陷类型', prop: 'type' },
          { label: '缺陷等级', prop: 'level' },
          { label: '发现时间', prop: 'findTime' },
          { label: '处理状态', prop: 'status' }
        ]
      }
    },
    {
      immediate: true
    }
  )
  watch(
    () => props.data.list,
    () => {
      if (props.data.list.length) {
        props.data.list.forEach((data) => {
          if (props.data.title === '作业列表') {
            // data.typeName = lib.store().storeDictionary.dictMap.find((_) => _.code === data.type).name
            data.statusName = lib.store().storeDictionary.dictMap.find((_) => _.code === data.status).name
          }
        })
      }
      tableData.value = props.data.list
    },
    {
      deep: true,
      immediate: true
    }
  )
  const isSelectedItem = ref(false)
  // const clickModel = lib.provideTools.clickModel.inject()
  // watch(
  //   () => clickModel.data,
  //   (val) => {
  //     if (!isSelectedItem.value) return
  //     const obj = val.object
  //     if (obj && obj.typeId === typeId) {
  //       const id = obj.id
  //       toUe5('openCustomPOIWindow', {
  //         id: id,
  //         isOpen: true
  //       })
  //       const componentPaths = {
  //         作业列表: './Components/MaintenanceDetails/index.vue',
  //         缺陷列表: './Components/DefectDetails/index.vue'
  //       }
  //       const data = JSON.parse(obj.window?.jsonParameter)
  //       lib.popWindow.createPopWindow(
  //         componentPaths[props.data],
  //         {
  //           // left: 1420,
  //           // top: 580,
  //           tag: 'maintenanceWindow',
  //           appContext,
  //           appendParent: 'player',
  //           followPoint: { typeId: obj.typeId, id },
  //           closeFunc: () => {
  //             toUe5('openCustomPOIWindow', {
  //               id: id,
  //               isOpen: false
  //             })
  //           }
  //         },
  //         data
  //       )
  //     }
  //   },
  //   {
  //     immediate: true,
  //     deep: true
  //   }
  // )
  lib.bus.busTreeTableStructure.on((type) => {
    if (type === 'clear') {
      tableData.value.forEach((_) => {
        _.isOn = false
      })
    }
  })
  const tableClick = (item) => {
    console.log('列表点击-----', item)
    lib.popWindow.removeDialog('maintenanceWindow')
    item.isOn = !item.isOn
    isSelectedItem.value = true

    lib._engineController.clearCzml()
    if (props.data.title == '作业列表') {
      // 没有里程号默认撒在路面上
      if (!item.mileage) {
        item.mileage = 'EK53+825'
      }
      lib.popWindow.createPopWindow(
        './Components/MaintenanceDetails/index.vue',
        {
          left: 1700,
          top: 700,
          tag: 'maintenanceWindow',
          appContext,
          appendParent: 'player',
          zIndex: 999,
          // followPoint: { typeId: obj.typeId, id },
          closeFunc: () => {
            // toUe5('openCustomPOIWindow', {
            //   id: id,
            //   isOpen: false
            // })
          }
        },
        item
      )

      lib.api.getPointCode
        .getModeCodeFromMileage({
          mileage: item.mileage,
          structureType: 4 // 4代表路面，默认查4
        })
        .then(async (res) => {
          if (res.success) {
            item.modelCode = res.result.code

            const bimInfo = await lib._engineController.getBimInfoByCode(item.modelCode)
            if (bimInfo) {
              const icon = await lib.utils.convertImageToBase64(`images/${props.data.title == '作业列表' ? 'workIcon' : 'defectIcon'}.png`)
              const cartesian = bimInfo.position

              const czml = {
                id: lib.utils.getRandomString(10),
                name: lib.utils.getRandomString(5),
                billboard: {
                  // 图片
                  image: icon,
                  scale: 0.5,
                  disableDepthTestDistance: 999999,
                  horizontalOrigin: 'CENTER',
                  verticalOrigin: 'BOTTOM'
                },
                position: {
                  cartesian: [0, 0, 0]
                },
                onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
                  const componentPaths = {
                    作业列表: './Components/MaintenanceDetails/index.vue',
                    缺陷列表: './Components/DefectDetails/index.vue'
                  }
                  // const data = JSON.parse(obj.window?.jsonParameter)
                  lib.popWindow.createPopWindow(
                    componentPaths[props.data.title],
                    {
                      left: clickClinetX,
                      top: clickClinetY,
                      tag: 'maintenanceWindow',
                      appContext,
                      appendParent: 'player',
                      zIndex: 999,
                      // followPoint: { typeId: obj.typeId, id },
                      closeFunc: () => {
                        // toUe5('openCustomPOIWindow', {
                        //   id: id,
                        //   isOpen: false
                        // })
                      }
                    },
                    item
                  )
                }
              }
              lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
              lib._engineController.flyToBimId(bimInfo.bimId)
            }
          }
        })
    } else {
      lib.popWindow.createPopWindow(
        './Components/DefectDetails/index.vue',
        {
          left: 1700,
          top: 700,
          tag: 'maintenanceWindow',
          appContext,
          appendParent: 'player'
        },
        item
      )
      // 缺陷列表有里程号
      // if (!item.position) {
      //   item.mileage = 'EK53+825'
      //   lib._engineController.clearSelect()

      //   // ! 没有里程号，直接弹窗
      //   // lib.popWindow.createPopWindow(
      //   //   './Components/DefectDetails/index.vue',
      //   //   {
      //   //     left: 1700,
      //   //     top: 700,
      //   //     tag: 'maintenanceWindow',
      //   //     appContext,
      //   //     appendParent: 'player'
      //   //   },
      //   //   item
      //   // )
      //   return
      // } else {
      //   if (!item.mileage) {
      //     item.mileage = item.position
      //   }
      // }

      lib.api.bigscreenApi.geDefectOrderDetails({ id: item.id }).then(async (res) => {
        console.log('缺陷详情', res)
        if (res.success && (res.result?.deviceCode || res.result?.structureCode)) {
          let code = res.result.deviceCode || res.result.structureCode
          const bimInfo = await lib._engineController.getBimInfoByCode(code)
          if (bimInfo) {
            lib._engineController.setColorByList([{ bimId: bimInfo.bimId, color: 'rgb(0,176,80)' }])
            lib._engineController.flyToBimId(bimInfo.bimId, -15)

            const icon = await lib.utils.convertImageToBase64(`images/defectIcon.png`)
            const cartesian = bimInfo.position

            const czml = {
              id: lib.utils.getRandomString(10),
              name: lib.utils.getRandomString(5),
              billboard: {
                // 图片
                image: icon,
                scale: 0.5,
                disableDepthTestDistance: 999999,
                horizontalOrigin: 'CENTER',
                verticalOrigin: 'BOTTOM'
              },
              position: {
                cartesian: [0, 0, 0]
              },
              onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
                lib.popWindow.createPopWindow(
                  './Components/DefectDetails/index.vue',
                  {
                    left: clickClinetX,
                    top: clickClinetY,
                    tag: 'maintenanceWindow',
                    appContext,
                    appendParent: 'player',
                    zIndex: 999,
                    // followPoint: { typeId: obj.typeId, id },
                    closeFunc: () => {
                      // toUe5('openCustomPOIWindow', {
                      //   id: id,
                      //   isOpen: false
                      // })
                    }
                  },
                  item
                )
              }
            }
            lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
          }
          console.log('bimInfo-------------', bimInfo)
        }
      })
    }
  }
  onUnmounted(() => {
    isSelectedItem.value = false
    toUe5('customPOIList', {
      isOn: false, // 是否开启标签
      typeId: typeId // 设施设备类型ID（为0时指全部类型）
    })
    lib.popWindow.removeDialog('maintenanceWindow')
  })
</script>

<style lang="scss" scoped>
  .work-list-container {
    width: 435px;
    height: 280px;
    background: url('@/assets/CommonPopup/popupBg.png');
    background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 14px 0 45px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .content {
      width: 407px;
      height: 200px;
      margin: 13px 21px 0 17px;
    }
  }
</style>
