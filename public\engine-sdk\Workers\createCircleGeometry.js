define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./EllipseGeometryLibrary-98898859","./GeometryInstance-d4317835","./EllipseGeometry-e38c578b"],(function(e,t,i,r,o,n,a,l,s,d,m,c,u,p,y,_,h,f,G,x,g,v,E,b){"use strict";function w(i){i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT);var r=i.radius;t.Check.typeOf.number("radius",r);var o={center:i.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:i.ellipsoid,height:i.height,extrudedHeight:i.extrudedHeight,granularity:i.granularity,vertexFormat:i.vertexFormat,stRotation:i.stRotation,shadowVolume:i.shadowVolume};this._ellipseGeometry=new b.EllipseGeometry(o),this._workerName="createCircleGeometry"}w.packedLength=b.EllipseGeometry.packedLength,w.pack=function(e,i,r){return t.Check.typeOf.object("value",e),b.EllipseGeometry.pack(e._ellipseGeometry,i,r)};var A=new b.EllipseGeometry({center:new i.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),C={center:new i.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new g.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};function M(t,o){return e.defined(o)&&(t=w.unpack(t,o)),t._ellipseGeometry._center=i.Cartesian3.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(t._ellipseGeometry._ellipsoid),w.createGeometry(t)}return w.unpack=function(t,o,n){var a=b.EllipseGeometry.unpack(t,o,A);return C.center=i.Cartesian3.clone(a._center,C.center),C.ellipsoid=r.Ellipsoid.clone(a._ellipsoid,C.ellipsoid),C.height=a._height,C.extrudedHeight=a._extrudedHeight,C.granularity=a._granularity,C.vertexFormat=g.VertexFormat.clone(a._vertexFormat,C.vertexFormat),C.stRotation=a._stRotation,C.shadowVolume=a._shadowVolume,e.defined(n)?(C.semiMajorAxis=a._semiMajorAxis,C.semiMinorAxis=a._semiMinorAxis,n._ellipseGeometry=new b.EllipseGeometry(C),n):(C.radius=a._semiMajorAxis,new w(C))},w.createGeometry=function(e){return b.EllipseGeometry.createGeometry(e._ellipseGeometry)},w.createShadowVolume=function(e,t,i){var r=e._ellipseGeometry._granularity,o=e._ellipseGeometry._ellipsoid,n=t(r,o),a=i(r,o);return new w({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:o,stRotation:e._ellipseGeometry._stRotation,granularity:r,extrudedHeight:n,height:a,vertexFormat:g.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(w.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),M}));