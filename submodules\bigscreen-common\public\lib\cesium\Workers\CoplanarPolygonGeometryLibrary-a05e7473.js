/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./OrientedBoundingBox-e47c7a90"],(function(n,t,e,r){"use strict";const a={},i=new t.Cartesian3,o=new t.Cartesian3,u=new t.Cartesian3,s=new t.Cartesian3,c=new r.OrientedBoundingBox;function C(n,e,r,a,o){const u=t.Cartesian3.subtract(n,e,i),s=t.Cartesian3.dot(r,u),c=t.Cartesian3.dot(a,u);return t.Cartesian2.fromElements(s,c,o)}a.validOutline=function(n){const a=r.OrientedBoundingBox.fromPoints(n,c).halfAxes,i=e.Matrix3.getColumn(a,0,o),C=e.Matrix3.getColumn(a,1,u),m=e.Matrix3.getColumn(a,2,s),g=t.Cartesian3.magnitude(i),l=t.Cartesian3.magnitude(C),f=t.Cartesian3.magnitude(m);return!(0===g&&(0===l||0===f)||0===l&&0===f)},a.computeProjectTo2DArguments=function(n,a,i,C){const m=r.OrientedBoundingBox.fromPoints(n,c),g=m.halfAxes,l=e.Matrix3.getColumn(g,0,o),f=e.Matrix3.getColumn(g,1,u),d=e.Matrix3.getColumn(g,2,s),x=t.Cartesian3.magnitude(l),B=t.Cartesian3.magnitude(f),P=t.Cartesian3.magnitude(d),M=Math.min(x,B,P);if(0===x&&(0===B||0===P)||0===B&&0===P)return!1;let w,b;return M!==B&&M!==P||(w=l),M===x?w=f:M===P&&(b=f),M!==x&&M!==B||(b=d),t.Cartesian3.normalize(w,i),t.Cartesian3.normalize(b,C),t.Cartesian3.clone(m.center,a),!0},a.createProjectPointsTo2DFunction=function(n,t,e){return function(r){const a=new Array(r.length);for(let i=0;i<r.length;i++)a[i]=C(r[i],n,t,e);return a}},a.createProjectPointTo2DFunction=function(n,t,e){return function(r,a){return C(r,n,t,e,a)}};var m=a;n.CoplanarPolygonGeometryLibrary=m}));
