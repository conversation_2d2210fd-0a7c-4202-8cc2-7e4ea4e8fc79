define(["./when-1807bd8d","./Check-1951f41f","./RuntimeError-d0e509ca","./createTaskProcessorWorker"],(function(e,t,i,n){"use strict";var r=1953029805,a=2917034100;function s(e,n){if(s.passThroughDataForTesting)return n;t.Check.typeOf.object("key",e),t.Check.typeOf.object("data",n);var o=e.byteLength;if(0===o||o%4!==0)throw new i.RuntimeError("The length of key must be greater than 0 and a multiple of 4.");var l=new DataView(n),f=l.getUint32(0,!0);if(f===r||f===a)return n;var h,d=new DataView(e),u=0,c=n.byteLength,w=c-c%8,b=o,m=8;while(u<w){m=(m+8)%24,h=m;while(u<w&&h<b)l.setUint32(u,l.getUint32(u,!0)^d.getUint32(h,!0),!0),l.setUint32(u+4,l.getUint32(u+4,!0)^d.getUint32(h+4,!0),!0),u+=8,h+=24}if(u<c){h>=b&&(m=(m+8)%24,h=m);while(u<c)l.setUint8(u,l.getUint8(u)^d.getUint8(h)),u++,h++}}function o(e,t){return 0!==(e&t)}s.passThroughDataForTesting=!1;var l=[1,2,4,8],f=15,h=16,d=64,u=128;function c(e,t,i,n,r,a){this._bits=e,this.cnodeVersion=t,this.imageryVersion=i,this.terrainVersion=n,this.imageryProvider=r,this.terrainProvider=a,this.ancestorHasTerrain=!1,this.terrainState=void 0}c.clone=function(t,i){return e.defined(i)?(i._bits=t._bits,i.cnodeVersion=t.cnodeVersion,i.imageryVersion=t.imageryVersion,i.terrainVersion=t.terrainVersion,i.imageryProvider=t.imageryProvider,i.terrainProvider=t.terrainProvider):i=new c(t._bits,t.cnodeVersion,t.imageryVersion,t.terrainVersion,t.imageryProvider,t.terrainProvider),i.ancestorHasTerrain=t.ancestorHasTerrain,i.terrainState=t.terrainState,i},c.prototype.setParent=function(e){this.ancestorHasTerrain=e.ancestorHasTerrain||this.hasTerrain()},c.prototype.hasSubtree=function(){return o(this._bits,h)},c.prototype.hasImagery=function(){return o(this._bits,d)},c.prototype.hasTerrain=function(){return o(this._bits,u)},c.prototype.hasChildren=function(){return o(this._bits,f)},c.prototype.hasChild=function(e){return o(this._bits,l[e])},c.prototype.getChildBitmask=function(){return this._bits&f};var w={};(function(e){w=e()})((function(){return function e(t,i,n){function r(s,o){if(!i[s]){if(!t[s]){var l="function"==typeof require&&require;if(!o&&l)return l(s,!0);if(a)return a(s,!0);var f=new Error("Cannot find module '"+s+"'");throw f.code="MODULE_NOT_FOUND",f}var h=i[s]={exports:{}};t[s][0].call(h.exports,(function(e){var i=t[s][1][e];return r(i||e)}),h,h.exports,e,t,i,n)}return i[s].exports}for(var a="function"==typeof require&&require,s=0;s<n.length;s++)r(n[s]);return r}({1:[function(e,t,i){var n="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Int32Array;i.assign=function(e){var t=Array.prototype.slice.call(arguments,1);while(t.length){var i=t.shift();if(i){if("object"!==typeof i)throw new TypeError(i+"must be non-object");for(var n in i)i.hasOwnProperty(n)&&(e[n]=i[n])}}return e},i.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,i,n,r){if(t.subarray&&e.subarray)e.set(t.subarray(i,i+n),r);else for(var a=0;a<n;a++)e[r+a]=t[i+a]},flattenChunks:function(e){var t,i,n,r,a,s;for(n=0,t=0,i=e.length;t<i;t++)n+=e[t].length;for(s=new Uint8Array(n),r=0,t=0,i=e.length;t<i;t++)a=e[t],s.set(a,r),r+=a.length;return s}},a={arraySet:function(e,t,i,n,r){for(var a=0;a<n;a++)e[r+a]=t[i+a]},flattenChunks:function(e){return[].concat.apply([],e)}};i.setTyped=function(e){e?(i.Buf8=Uint8Array,i.Buf16=Uint16Array,i.Buf32=Int32Array,i.assign(i,r)):(i.Buf8=Array,i.Buf16=Array,i.Buf32=Array,i.assign(i,a))},i.setTyped(n)},{}],2:[function(e,t,i){var n=e("./common"),r=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch(f){r=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(f){a=!1}for(var s=new n.Buf8(256),o=0;o<256;o++)s[o]=o>=252?6:o>=248?5:o>=240?4:o>=224?3:o>=192?2:1;function l(e,t){if(t<65537&&(e.subarray&&a||!e.subarray&&r))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var i="",s=0;s<t;s++)i+=String.fromCharCode(e[s]);return i}s[254]=s[254]=1,i.string2buf=function(e){var t,i,r,a,s,o=e.length,l=0;for(a=0;a<o;a++)i=e.charCodeAt(a),55296===(64512&i)&&a+1<o&&(r=e.charCodeAt(a+1),56320===(64512&r)&&(i=65536+(i-55296<<10)+(r-56320),a++)),l+=i<128?1:i<2048?2:i<65536?3:4;for(t=new n.Buf8(l),s=0,a=0;s<l;a++)i=e.charCodeAt(a),55296===(64512&i)&&a+1<o&&(r=e.charCodeAt(a+1),56320===(64512&r)&&(i=65536+(i-55296<<10)+(r-56320),a++)),i<128?t[s++]=i:i<2048?(t[s++]=192|i>>>6,t[s++]=128|63&i):i<65536?(t[s++]=224|i>>>12,t[s++]=128|i>>>6&63,t[s++]=128|63&i):(t[s++]=240|i>>>18,t[s++]=128|i>>>12&63,t[s++]=128|i>>>6&63,t[s++]=128|63&i);return t},i.buf2binstring=function(e){return l(e,e.length)},i.binstring2buf=function(e){for(var t=new n.Buf8(e.length),i=0,r=t.length;i<r;i++)t[i]=e.charCodeAt(i);return t},i.buf2string=function(e,t){var i,n,r,a,o=t||e.length,f=new Array(2*o);for(n=0,i=0;i<o;)if(r=e[i++],r<128)f[n++]=r;else if(a=s[r],a>4)f[n++]=65533,i+=a-1;else{r&=2===a?31:3===a?15:7;while(a>1&&i<o)r=r<<6|63&e[i++],a--;a>1?f[n++]=65533:r<65536?f[n++]=r:(r-=65536,f[n++]=55296|r>>10&1023,f[n++]=56320|1023&r)}return l(f,n)},i.utf8border=function(e,t){var i;t=t||e.length,t>e.length&&(t=e.length),i=t-1;while(i>=0&&128===(192&e[i]))i--;return i<0||0===i?t:i+s[e[i]]>t?i:t}},{"./common":1}],3:[function(e,t,i){function n(e,t,i,n){var r=65535&e|0,a=e>>>16&65535|0,s=0;while(0!==i){s=i>2e3?2e3:i,i-=s;do{r=r+t[n++]|0,a=a+r|0}while(--s);r%=65521,a%=65521}return r|a<<16|0}t.exports=n},{}],4:[function(e,t,i){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],5:[function(e,t,i){function n(){for(var e,t=[],i=0;i<256;i++){e=i;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[i]=e}return t}var r=n();function a(e,t,i,n){var a=r,s=n+i;e^=-1;for(var o=n;o<s;o++)e=e>>>8^a[255&(e^t[o])];return-1^e}t.exports=a},{}],6:[function(e,t,i){function n(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}t.exports=n},{}],7:[function(e,t,i){var n=30,r=12;t.exports=function(e,t){var i,a,s,o,l,f,h,d,u,c,w,b,m,g,v,k,_,p,y,x,E,S,T,R,A;i=e.state,a=e.next_in,R=e.input,s=a+(e.avail_in-5),o=e.next_out,A=e.output,l=o-(t-e.avail_out),f=o+(e.avail_out-257),h=i.dmax,d=i.wsize,u=i.whave,c=i.wnext,w=i.window,b=i.hold,m=i.bits,g=i.lencode,v=i.distcode,k=(1<<i.lenbits)-1,_=(1<<i.distbits)-1;e:do{m<15&&(b+=R[a++]<<m,m+=8,b+=R[a++]<<m,m+=8),p=g[b&k];t:for(;;){if(y=p>>>24,b>>>=y,m-=y,y=p>>>16&255,0===y)A[o++]=65535&p;else{if(!(16&y)){if(0===(64&y)){p=g[(65535&p)+(b&(1<<y)-1)];continue t}if(32&y){i.mode=r;break e}e.msg="invalid literal/length code",i.mode=n;break e}x=65535&p,y&=15,y&&(m<y&&(b+=R[a++]<<m,m+=8),x+=b&(1<<y)-1,b>>>=y,m-=y),m<15&&(b+=R[a++]<<m,m+=8,b+=R[a++]<<m,m+=8),p=v[b&_];i:for(;;){if(y=p>>>24,b>>>=y,m-=y,y=p>>>16&255,!(16&y)){if(0===(64&y)){p=v[(65535&p)+(b&(1<<y)-1)];continue i}e.msg="invalid distance code",i.mode=n;break e}if(E=65535&p,y&=15,m<y&&(b+=R[a++]<<m,m+=8,m<y&&(b+=R[a++]<<m,m+=8)),E+=b&(1<<y)-1,E>h){e.msg="invalid distance too far back",i.mode=n;break e}if(b>>>=y,m-=y,y=o-l,E>y){if(y=E-y,y>u&&i.sane){e.msg="invalid distance too far back",i.mode=n;break e}if(S=0,T=w,0===c){if(S+=d-y,y<x){x-=y;do{A[o++]=w[S++]}while(--y);S=o-E,T=A}}else if(c<y){if(S+=d+c-y,y-=c,y<x){x-=y;do{A[o++]=w[S++]}while(--y);if(S=0,c<x){y=c,x-=y;do{A[o++]=w[S++]}while(--y);S=o-E,T=A}}}else if(S+=c-y,y<x){x-=y;do{A[o++]=w[S++]}while(--y);S=o-E,T=A}while(x>2)A[o++]=T[S++],A[o++]=T[S++],A[o++]=T[S++],x-=3;x&&(A[o++]=T[S++],x>1&&(A[o++]=T[S++]))}else{S=o-E;do{A[o++]=A[S++],A[o++]=A[S++],A[o++]=A[S++],x-=3}while(x>2);x&&(A[o++]=A[S++],x>1&&(A[o++]=A[S++]))}break}}break}}while(a<s&&o<f);x=m>>3,a-=x,m-=x<<3,b&=(1<<m)-1,e.next_in=a,e.next_out=o,e.avail_in=a<s?s-a+5:5-(a-s),e.avail_out=o<f?f-o+257:257-(o-f),i.hold=b,i.bits=m}},{}],8:[function(e,t,i){var n=e("../utils/common"),r=e("./adler32"),a=e("./crc32"),s=e("./inffast"),o=e("./inftrees"),l=0,f=1,h=2,d=4,u=5,c=6,w=0,b=1,m=2,g=-2,v=-3,k=-4,_=-5,p=8,y=1,x=2,E=3,S=4,T=5,R=6,A=7,B=8,U=9,Z=10,I=11,O=12,D=13,N=14,C=15,z=16,F=17,L=18,H=19,P=20,M=21,V=22,j=23,K=24,Y=25,q=26,G=27,Q=28,W=29,X=30,J=31,$=32,ee=852,te=592,ie=15,ne=ie;function re(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function ae(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function se(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=y,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(ee),t.distcode=t.distdyn=new n.Buf32(te),t.sane=1,t.back=-1,w):g}function oe(e){var t;return e&&e.state?(t=e.state,t.wsize=0,t.whave=0,t.wnext=0,se(e)):g}function le(e,t){var i,n;return e&&e.state?(n=e.state,t<0?(i=0,t=-t):(i=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?g:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=i,n.wbits=t,oe(e))):g}function fe(e,t){var i,n;return e?(n=new ae,e.state=n,n.window=null,i=le(e,t),i!==w&&(e.state=null),i):g}function he(e){return fe(e,ne)}var de,ue,ce=!0;function we(e){if(ce){var t;de=new n.Buf32(512),ue=new n.Buf32(32),t=0;while(t<144)e.lens[t++]=8;while(t<256)e.lens[t++]=9;while(t<280)e.lens[t++]=7;while(t<288)e.lens[t++]=8;o(f,e.lens,0,288,de,0,e.work,{bits:9}),t=0;while(t<32)e.lens[t++]=5;o(h,e.lens,0,32,ue,0,e.work,{bits:5}),ce=!1}e.lencode=de,e.lenbits=9,e.distcode=ue,e.distbits=5}function be(e,t,i,r){var a,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new n.Buf8(s.wsize)),r>=s.wsize?(n.arraySet(s.window,t,i-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):(a=s.wsize-s.wnext,a>r&&(a=r),n.arraySet(s.window,t,i-r,a,s.wnext),r-=a,r?(n.arraySet(s.window,t,i-r,r,0),s.wnext=r,s.whave=s.wsize):(s.wnext+=a,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=a))),0}function me(e,t){var i,ee,te,ie,ne,ae,se,oe,le,fe,he,de,ue,ce,me,ge,ve,ke,_e,pe,ye,xe,Ee,Se,Te=0,Re=new n.Buf8(4),Ae=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return g;i=e.state,i.mode===O&&(i.mode=D),ne=e.next_out,te=e.output,se=e.avail_out,ie=e.next_in,ee=e.input,ae=e.avail_in,oe=i.hold,le=i.bits,fe=ae,he=se,xe=w;e:for(;;)switch(i.mode){case y:if(0===i.wrap){i.mode=D;break}while(le<16){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(2&i.wrap&&35615===oe){i.check=0,Re[0]=255&oe,Re[1]=oe>>>8&255,i.check=a(i.check,Re,2,0),oe=0,le=0,i.mode=x;break}if(i.flags=0,i.head&&(i.head.done=!1),!(1&i.wrap)||(((255&oe)<<8)+(oe>>8))%31){e.msg="incorrect header check",i.mode=X;break}if((15&oe)!==p){e.msg="unknown compression method",i.mode=X;break}if(oe>>>=4,le-=4,ye=8+(15&oe),0===i.wbits)i.wbits=ye;else if(ye>i.wbits){e.msg="invalid window size",i.mode=X;break}i.dmax=1<<ye,e.adler=i.check=1,i.mode=512&oe?Z:O,oe=0,le=0;break;case x:while(le<16){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(i.flags=oe,(255&i.flags)!==p){e.msg="unknown compression method",i.mode=X;break}if(57344&i.flags){e.msg="unknown header flags set",i.mode=X;break}i.head&&(i.head.text=oe>>8&1),512&i.flags&&(Re[0]=255&oe,Re[1]=oe>>>8&255,i.check=a(i.check,Re,2,0)),oe=0,le=0,i.mode=E;case E:while(le<32){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.head&&(i.head.time=oe),512&i.flags&&(Re[0]=255&oe,Re[1]=oe>>>8&255,Re[2]=oe>>>16&255,Re[3]=oe>>>24&255,i.check=a(i.check,Re,4,0)),oe=0,le=0,i.mode=S;case S:while(le<16){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.head&&(i.head.xflags=255&oe,i.head.os=oe>>8),512&i.flags&&(Re[0]=255&oe,Re[1]=oe>>>8&255,i.check=a(i.check,Re,2,0)),oe=0,le=0,i.mode=T;case T:if(1024&i.flags){while(le<16){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.length=oe,i.head&&(i.head.extra_len=oe),512&i.flags&&(Re[0]=255&oe,Re[1]=oe>>>8&255,i.check=a(i.check,Re,2,0)),oe=0,le=0}else i.head&&(i.head.extra=null);i.mode=R;case R:if(1024&i.flags&&(de=i.length,de>ae&&(de=ae),de&&(i.head&&(ye=i.head.extra_len-i.length,i.head.extra||(i.head.extra=new Array(i.head.extra_len)),n.arraySet(i.head.extra,ee,ie,de,ye)),512&i.flags&&(i.check=a(i.check,ee,de,ie)),ae-=de,ie+=de,i.length-=de),i.length))break e;i.length=0,i.mode=A;case A:if(2048&i.flags){if(0===ae)break e;de=0;do{ye=ee[ie+de++],i.head&&ye&&i.length<65536&&(i.head.name+=String.fromCharCode(ye))}while(ye&&de<ae);if(512&i.flags&&(i.check=a(i.check,ee,de,ie)),ae-=de,ie+=de,ye)break e}else i.head&&(i.head.name=null);i.length=0,i.mode=B;case B:if(4096&i.flags){if(0===ae)break e;de=0;do{ye=ee[ie+de++],i.head&&ye&&i.length<65536&&(i.head.comment+=String.fromCharCode(ye))}while(ye&&de<ae);if(512&i.flags&&(i.check=a(i.check,ee,de,ie)),ae-=de,ie+=de,ye)break e}else i.head&&(i.head.comment=null);i.mode=U;case U:if(512&i.flags){while(le<16){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(oe!==(65535&i.check)){e.msg="header crc mismatch",i.mode=X;break}oe=0,le=0}i.head&&(i.head.hcrc=i.flags>>9&1,i.head.done=!0),e.adler=i.check=0,i.mode=O;break;case Z:while(le<32){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}e.adler=i.check=re(oe),oe=0,le=0,i.mode=I;case I:if(0===i.havedict)return e.next_out=ne,e.avail_out=se,e.next_in=ie,e.avail_in=ae,i.hold=oe,i.bits=le,m;e.adler=i.check=1,i.mode=O;case O:if(t===u||t===c)break e;case D:if(i.last){oe>>>=7&le,le-=7&le,i.mode=G;break}while(le<3){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}switch(i.last=1&oe,oe>>>=1,le-=1,3&oe){case 0:i.mode=N;break;case 1:if(we(i),i.mode=P,t===c){oe>>>=2,le-=2;break e}break;case 2:i.mode=F;break;case 3:e.msg="invalid block type",i.mode=X}oe>>>=2,le-=2;break;case N:oe>>>=7&le,le-=7&le;while(le<32){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if((65535&oe)!==(oe>>>16^65535)){e.msg="invalid stored block lengths",i.mode=X;break}if(i.length=65535&oe,oe=0,le=0,i.mode=C,t===c)break e;case C:i.mode=z;case z:if(de=i.length,de){if(de>ae&&(de=ae),de>se&&(de=se),0===de)break e;n.arraySet(te,ee,ie,de,ne),ae-=de,ie+=de,se-=de,ne+=de,i.length-=de;break}i.mode=O;break;case F:while(le<14){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(i.nlen=257+(31&oe),oe>>>=5,le-=5,i.ndist=1+(31&oe),oe>>>=5,le-=5,i.ncode=4+(15&oe),oe>>>=4,le-=4,i.nlen>286||i.ndist>30){e.msg="too many length or distance symbols",i.mode=X;break}i.have=0,i.mode=L;case L:while(i.have<i.ncode){while(le<3){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.lens[Ae[i.have++]]=7&oe,oe>>>=3,le-=3}while(i.have<19)i.lens[Ae[i.have++]]=0;if(i.lencode=i.lendyn,i.lenbits=7,Ee={bits:i.lenbits},xe=o(l,i.lens,0,19,i.lencode,0,i.work,Ee),i.lenbits=Ee.bits,xe){e.msg="invalid code lengths set",i.mode=X;break}i.have=0,i.mode=H;case H:while(i.have<i.nlen+i.ndist){for(;;){if(Te=i.lencode[oe&(1<<i.lenbits)-1],me=Te>>>24,ge=Te>>>16&255,ve=65535&Te,me<=le)break;if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(ve<16)oe>>>=me,le-=me,i.lens[i.have++]=ve;else{if(16===ve){Se=me+2;while(le<Se){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(oe>>>=me,le-=me,0===i.have){e.msg="invalid bit length repeat",i.mode=X;break}ye=i.lens[i.have-1],de=3+(3&oe),oe>>>=2,le-=2}else if(17===ve){Se=me+3;while(le<Se){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}oe>>>=me,le-=me,ye=0,de=3+(7&oe),oe>>>=3,le-=3}else{Se=me+7;while(le<Se){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}oe>>>=me,le-=me,ye=0,de=11+(127&oe),oe>>>=7,le-=7}if(i.have+de>i.nlen+i.ndist){e.msg="invalid bit length repeat",i.mode=X;break}while(de--)i.lens[i.have++]=ye}}if(i.mode===X)break;if(0===i.lens[256]){e.msg="invalid code -- missing end-of-block",i.mode=X;break}if(i.lenbits=9,Ee={bits:i.lenbits},xe=o(f,i.lens,0,i.nlen,i.lencode,0,i.work,Ee),i.lenbits=Ee.bits,xe){e.msg="invalid literal/lengths set",i.mode=X;break}if(i.distbits=6,i.distcode=i.distdyn,Ee={bits:i.distbits},xe=o(h,i.lens,i.nlen,i.ndist,i.distcode,0,i.work,Ee),i.distbits=Ee.bits,xe){e.msg="invalid distances set",i.mode=X;break}if(i.mode=P,t===c)break e;case P:i.mode=M;case M:if(ae>=6&&se>=258){e.next_out=ne,e.avail_out=se,e.next_in=ie,e.avail_in=ae,i.hold=oe,i.bits=le,s(e,he),ne=e.next_out,te=e.output,se=e.avail_out,ie=e.next_in,ee=e.input,ae=e.avail_in,oe=i.hold,le=i.bits,i.mode===O&&(i.back=-1);break}for(i.back=0;;){if(Te=i.lencode[oe&(1<<i.lenbits)-1],me=Te>>>24,ge=Te>>>16&255,ve=65535&Te,me<=le)break;if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(ge&&0===(240&ge)){for(ke=me,_e=ge,pe=ve;;){if(Te=i.lencode[pe+((oe&(1<<ke+_e)-1)>>ke)],me=Te>>>24,ge=Te>>>16&255,ve=65535&Te,ke+me<=le)break;if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}oe>>>=ke,le-=ke,i.back+=ke}if(oe>>>=me,le-=me,i.back+=me,i.length=ve,0===ge){i.mode=q;break}if(32&ge){i.back=-1,i.mode=O;break}if(64&ge){e.msg="invalid literal/length code",i.mode=X;break}i.extra=15&ge,i.mode=V;case V:if(i.extra){Se=i.extra;while(le<Se){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.length+=oe&(1<<i.extra)-1,oe>>>=i.extra,le-=i.extra,i.back+=i.extra}i.was=i.length,i.mode=j;case j:for(;;){if(Te=i.distcode[oe&(1<<i.distbits)-1],me=Te>>>24,ge=Te>>>16&255,ve=65535&Te,me<=le)break;if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(0===(240&ge)){for(ke=me,_e=ge,pe=ve;;){if(Te=i.distcode[pe+((oe&(1<<ke+_e)-1)>>ke)],me=Te>>>24,ge=Te>>>16&255,ve=65535&Te,ke+me<=le)break;if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}oe>>>=ke,le-=ke,i.back+=ke}if(oe>>>=me,le-=me,i.back+=me,64&ge){e.msg="invalid distance code",i.mode=X;break}i.offset=ve,i.extra=15&ge,i.mode=K;case K:if(i.extra){Se=i.extra;while(le<Se){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}i.offset+=oe&(1<<i.extra)-1,oe>>>=i.extra,le-=i.extra,i.back+=i.extra}if(i.offset>i.dmax){e.msg="invalid distance too far back",i.mode=X;break}i.mode=Y;case Y:if(0===se)break e;if(de=he-se,i.offset>de){if(de=i.offset-de,de>i.whave&&i.sane){e.msg="invalid distance too far back",i.mode=X;break}de>i.wnext?(de-=i.wnext,ue=i.wsize-de):ue=i.wnext-de,de>i.length&&(de=i.length),ce=i.window}else ce=te,ue=ne-i.offset,de=i.length;de>se&&(de=se),se-=de,i.length-=de;do{te[ne++]=ce[ue++]}while(--de);0===i.length&&(i.mode=M);break;case q:if(0===se)break e;te[ne++]=i.length,se--,i.mode=M;break;case G:if(i.wrap){while(le<32){if(0===ae)break e;ae--,oe|=ee[ie++]<<le,le+=8}if(he-=se,e.total_out+=he,i.total+=he,he&&(e.adler=i.check=i.flags?a(i.check,te,he,ne-he):r(i.check,te,he,ne-he)),he=se,(i.flags?oe:re(oe))!==i.check){e.msg="incorrect data check",i.mode=X;break}oe=0,le=0}i.mode=Q;case Q:if(i.wrap&&i.flags){while(le<32){if(0===ae)break e;ae--,oe+=ee[ie++]<<le,le+=8}if(oe!==(4294967295&i.total)){e.msg="incorrect length check",i.mode=X;break}oe=0,le=0}i.mode=W;case W:xe=b;break e;case X:xe=v;break e;case J:return k;case $:default:return g}return e.next_out=ne,e.avail_out=se,e.next_in=ie,e.avail_in=ae,i.hold=oe,i.bits=le,(i.wsize||he!==e.avail_out&&i.mode<X&&(i.mode<G||t!==d))&&be(e,e.output,e.next_out,he-e.avail_out),fe-=e.avail_in,he-=e.avail_out,e.total_in+=fe,e.total_out+=he,i.total+=he,i.wrap&&he&&(e.adler=i.check=i.flags?a(i.check,te,he,e.next_out-he):r(i.check,te,he,e.next_out-he)),e.data_type=i.bits+(i.last?64:0)+(i.mode===O?128:0)+(i.mode===P||i.mode===C?256:0),(0===fe&&0===he||t===d)&&xe===w&&(xe=_),xe}function ge(e){if(!e||!e.state)return g;var t=e.state;return t.window&&(t.window=null),e.state=null,w}function ve(e,t){var i;return e&&e.state?(i=e.state,0===(2&i.wrap)?g:(i.head=t,t.done=!1,w)):g}function ke(e,t){var i,n,a,s=t.length;return e&&e.state?(i=e.state,0!==i.wrap&&i.mode!==I?g:i.mode===I&&(n=1,n=r(n,t,s,0),n!==i.check)?v:(a=be(e,t,s,s),a?(i.mode=J,k):(i.havedict=1,w))):g}i.inflateReset=oe,i.inflateReset2=le,i.inflateResetKeep=se,i.inflateInit=he,i.inflateInit2=fe,i.inflate=me,i.inflateEnd=ge,i.inflateGetHeader=ve,i.inflateSetDictionary=ke,i.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":1,"./adler32":3,"./crc32":5,"./inffast":7,"./inftrees":9}],9:[function(e,t,i){var n=e("../utils/common"),r=15,a=852,s=592,o=0,l=1,f=2,h=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],d=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],u=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],c=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,i,w,b,m,g,v){var k,_,p,y,x,E,S,T,R,A=v.bits,B=0,U=0,Z=0,I=0,O=0,D=0,N=0,C=0,z=0,F=0,L=null,H=0,P=new n.Buf16(r+1),M=new n.Buf16(r+1),V=null,j=0;for(B=0;B<=r;B++)P[B]=0;for(U=0;U<w;U++)P[t[i+U]]++;for(O=A,I=r;I>=1;I--)if(0!==P[I])break;if(O>I&&(O=I),0===I)return b[m++]=20971520,b[m++]=20971520,v.bits=1,0;for(Z=1;Z<I;Z++)if(0!==P[Z])break;for(O<Z&&(O=Z),C=1,B=1;B<=r;B++)if(C<<=1,C-=P[B],C<0)return-1;if(C>0&&(e===o||1!==I))return-1;for(M[1]=0,B=1;B<r;B++)M[B+1]=M[B]+P[B];for(U=0;U<w;U++)0!==t[i+U]&&(g[M[t[i+U]]++]=U);if(e===o?(L=V=g,E=19):e===l?(L=h,H-=257,V=d,j-=257,E=256):(L=u,V=c,E=-1),F=0,U=0,B=Z,x=m,D=O,N=0,p=-1,z=1<<O,y=z-1,e===l&&z>a||e===f&&z>s)return 1;for(;;){S=B-N,g[U]<E?(T=0,R=g[U]):g[U]>E?(T=V[j+g[U]],R=L[H+g[U]]):(T=96,R=0),k=1<<B-N,_=1<<D,Z=_;do{_-=k,b[x+(F>>N)+_]=S<<24|T<<16|R|0}while(0!==_);k=1<<B-1;while(F&k)k>>=1;if(0!==k?(F&=k-1,F+=k):F=0,U++,0===--P[B]){if(B===I)break;B=t[i+g[U]]}if(B>O&&(F&y)!==p){0===N&&(N=O),x+=Z,D=B-N,C=1<<D;while(D+N<I){if(C-=P[D+N],C<=0)break;D++,C<<=1}if(z+=1<<D,e===l&&z>a||e===f&&z>s)return 1;p=F&y,b[p]=O<<24|D<<16|x-m|0}}return 0!==F&&(b[x+F]=B-N<<24|64<<16|0),v.bits=O,0}},{"../utils/common":1}],10:[function(e,t,i){t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],11:[function(e,t,i){function n(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}t.exports=n},{}],"/lib/inflate.js":[function(e,t,i){var n=e("./zlib/inflate"),r=e("./utils/common"),a=e("./utils/strings"),s=e("./zlib/constants"),o=e("./zlib/messages"),l=e("./zlib/zstream"),f=e("./zlib/gzheader"),h=Object.prototype.toString;function d(e){if(!(this instanceof d))return new d(e);this.options=r.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0===(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var i=n.inflateInit2(this.strm,t.windowBits);if(i!==s.Z_OK)throw new Error(o[i]);this.header=new f,n.inflateGetHeader(this.strm,this.header)}function u(e,t){var i=new d(t);if(i.push(e,!0),i.err)throw i.msg||o[i.err];return i.result}function c(e,t){return t=t||{},t.raw=!0,u(e,t)}d.prototype.push=function(e,t){var i,o,l,f,d,u,c=this.strm,w=this.options.chunkSize,b=this.options.dictionary,m=!1;if(this.ended)return!1;o=t===~~t?t:!0===t?s.Z_FINISH:s.Z_NO_FLUSH,"string"===typeof e?c.input=a.binstring2buf(e):"[object ArrayBuffer]"===h.call(e)?c.input=new Uint8Array(e):c.input=e,c.next_in=0,c.avail_in=c.input.length;do{if(0===c.avail_out&&(c.output=new r.Buf8(w),c.next_out=0,c.avail_out=w),i=n.inflate(c,s.Z_NO_FLUSH),i===s.Z_NEED_DICT&&b&&(u="string"===typeof b?a.string2buf(b):"[object ArrayBuffer]"===h.call(b)?new Uint8Array(b):b,i=n.inflateSetDictionary(this.strm,u)),i===s.Z_BUF_ERROR&&!0===m&&(i=s.Z_OK,m=!1),i!==s.Z_STREAM_END&&i!==s.Z_OK)return this.onEnd(i),this.ended=!0,!1;c.next_out&&(0!==c.avail_out&&i!==s.Z_STREAM_END&&(0!==c.avail_in||o!==s.Z_FINISH&&o!==s.Z_SYNC_FLUSH)||("string"===this.options.to?(l=a.utf8border(c.output,c.next_out),f=c.next_out-l,d=a.buf2string(c.output,l),c.next_out=f,c.avail_out=w-f,f&&r.arraySet(c.output,c.output,l,f,0),this.onData(d)):this.onData(r.shrinkBuf(c.output,c.next_out)))),0===c.avail_in&&0===c.avail_out&&(m=!0)}while((c.avail_in>0||0===c.avail_out)&&i!==s.Z_STREAM_END);return i===s.Z_STREAM_END&&(o=s.Z_FINISH),o===s.Z_FINISH?(i=n.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===s.Z_OK):o!==s.Z_SYNC_FLUSH||(this.onEnd(s.Z_OK),c.avail_out=0,!0)},d.prototype.onData=function(e){this.chunks.push(e)},d.prototype.onEnd=function(e){e===s.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=r.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},i.Inflate=d,i.inflate=u,i.inflateRaw=c,i.ungzip=u},{"./utils/common":1,"./utils/strings":2,"./zlib/constants":4,"./zlib/gzheader":6,"./zlib/inflate":8,"./zlib/messages":10,"./zlib/zstream":11}]},{},[])("/lib/inflate.js")}));var b=w,m=Uint16Array.BYTES_PER_ELEMENT,g=Int32Array.BYTES_PER_ELEMENT,v=Uint32Array.BYTES_PER_ELEMENT,k={METADATA:0,TERRAIN:1,DBROOT:2};function _(e,t){var i=k.fromString(e.type),n=e.buffer;s(e.key,n);var r=T(n);n=r.buffer;var a=r.length;switch(i){case k.METADATA:return y(n,a,e.quadKey);case k.TERRAIN:return x(n,a,t);case k.DBROOT:return t.push(n),{buffer:n}}}k.fromString=function(e){return"Metadata"===e?k.METADATA:"Terrain"===e?k.TERRAIN:"DbRoot"===e?k.DBROOT:void 0};var p=32301;function y(e,t,n){var r=new DataView(e),a=0,s=r.getUint32(a,!0);if(a+=v,s!==p)throw new i.RuntimeError("Invalid magic");var o=r.getUint32(a,!0);if(a+=v,1!==o)throw new i.RuntimeError("Invalid data type. Must be 1 for QuadTreePacket");var l=r.getUint32(a,!0);if(a+=v,2!==l)throw new i.RuntimeError("Invalid QuadTreePacket version. Only version 2 is supported.");var f=r.getInt32(a,!0);a+=g;var h=r.getInt32(a,!0);if(a+=g,32!==h)throw new i.RuntimeError("Invalid instance size.");var d=r.getInt32(a,!0);a+=g;var u=r.getInt32(a,!0);a+=g;var w=r.getInt32(a,!0);if(a+=g,d!==f*h+a)throw new i.RuntimeError("Invalid dataBufferOffset");if(d+u+w!==t)throw new i.RuntimeError("Invalid packet offsets");for(var b=[],k=0;k<f;++k){var _=r.getUint8(a);++a,++a;var y=r.getUint16(a,!0);a+=m;var x=r.getUint16(a,!0);a+=m;var E=r.getUint16(a,!0);a+=m,a+=m,a+=m,a+=g,a+=g,a+=8;var S=r.getUint8(a++),T=r.getUint8(a++);a+=m,b.push(new c(_,y,x,E,S,T))}var R=[],A=0;function B(e,t,i){var n=!1;if(4===i){if(t.hasSubtree())return;n=!0}for(var r=0;r<4;++r){var a=e+r.toString();if(n)R[a]=null;else if(i<4)if(t.hasChild(r)){if(A===f)return void console.log("Incorrect number of instances");var s=b[A++];R[a]=s,B(a,s,i+1)}else R[a]=null}}var U=0,Z=b[A++];return""===n?++U:R[n]=Z,B(n,Z,U),R}function x(e,t,i){var n=new DataView(e),r=0,a=[];while(r<t){for(var s=r,o=0;o<4;++o){var l=n.getUint32(r,!0);r+=v,r+=l}var f=e.slice(s,r);i.push(f),a.push(f)}return a}var E=1953029805,S=2917034100;function T(e){var t=new DataView(e),n=0,r=t.getUint32(n,!0);if(n+=v,r!==E&&r!==S)throw new i.RuntimeError("Invalid magic");var a=t.getUint32(n,r===E);n+=v;var s=new Uint8Array(e,n),o=b.inflate(s);if(o.length!==a)throw new i.RuntimeError("Size of packet doesn't match header");return o}var R=n(_);return R}));