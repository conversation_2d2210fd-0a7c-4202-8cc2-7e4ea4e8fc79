define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./EllipseGeometryLibrary-98898859","./GeometryInstance-d4317835","./EllipseGeometry-e38c578b"],(function(e,t,r,n,i,a,o,c,s,d,f,l,m,b,p,y,u,G,C,E,A,_,h,x){"use strict";function I(t,i){return e.defined(i)&&(t=x.EllipseGeometry.unpack(t,i)),t._center=r.Cartesian3.clone(t._center),t._ellipsoid=n.Ellipsoid.clone(t._ellipsoid),x.EllipseGeometry.createGeometry(t)}return I}));