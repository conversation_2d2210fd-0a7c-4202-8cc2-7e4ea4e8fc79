define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./GeometryOffsetAttribute-30ce4d84","./GeometryInstance-d4317835","./arrayRemoveDuplicates-7c710eac","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./ArcType-10662e8b","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./PolygonGeometryLibrary-4b76b18a"],(function(e,t,i,r,o,n,a,l,s,y,p,d,u,c,f,g,h,m,b,P,v,E,A,_,G,T,H,C){"use strict";var L=[],O=[];function D(e,t,r,o,n,a){var l,s;l=a?b.Plane.fromPointNormal(t[0],i.Cartesian3.UNIT_Z):_.EllipsoidTangentPlane.fromPoints(t,e),s=l.projectPointsOntoPlane(t,L);var c,f,g=H.PolygonPipeline.computeWindingOrder2D(s);g===H.WindingOrder.CLOCKWISE&&(s.reverse(),t=t.slice().reverse());var m=t.length,P=0;if(o||a)for(c=new Float64Array(2*m*3),f=0;f<m;f++){var E=t[f],A=t[(f+1)%m];c[P++]=E.x,c[P++]=E.y,c[P++]=o?E.z:0,c[P++]=A.x,c[P++]=A.y,c[P++]=o?E.z:0}else if(!o){var T=0;if(n===G.ArcType.GEODESIC)for(f=0;f<m;f++)T+=C.PolygonGeometryLibrary.subdivideLineCount(t[f],t[(f+1)%m],r);else if(n===G.ArcType.RHUMB)for(f=0;f<m;f++)T+=C.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[f],t[(f+1)%m],r);for(c=new Float64Array(3*T),f=0;f<m;f++){var D;n===G.ArcType.GEODESIC?D=C.PolygonGeometryLibrary.subdivideLine(t[f],t[(f+1)%m],r,O):n===G.ArcType.RHUMB&&(D=C.PolygonGeometryLibrary.subdivideRhumbLine(e,t[f],t[(f+1)%m],r,O));for(var I=D.length,x=0;x<I;++x)c[P++]=D[x]}}m=c.length/3;var w=2*m,k=h.IndexDatatype.createTypedArray(m,w);for(P=0,f=0;f<m-1;f++)k[P++]=f,k[P++]=f+1;return k[P++]=m-1,k[P++]=0,new v.GeometryInstance({geometry:new p.Geometry({attributes:new u.GeometryAttributes({position:new p.GeometryAttribute({componentDatatype:y.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:k,primitiveType:d.PrimitiveType.LINES})})}function I(e,t,r,o,n,a){var l,s;l=a?b.Plane.fromPointNormal(t[0],i.Cartesian3.UNIT_Z):_.EllipsoidTangentPlane.fromPoints(t,e),s=l.projectPointsOntoPlane(t,L);var c,f,g=H.PolygonPipeline.computeWindingOrder2D(s);g===H.WindingOrder.CLOCKWISE&&(s.reverse(),t=t.slice().reverse());var m=t.length,P=new Array(m),E=0;if(o||a)for(c=new Float64Array(2*m*3*2),f=0;f<m;++f){P[f]=E/3;var A=t[f],T=t[(f+1)%m];c[E++]=A.x,c[E++]=A.y,c[E++]=o?A.z:0,c[E++]=T.x,c[E++]=T.y,c[E++]=o?T.z:0}else if(!o){var D=0;if(n===G.ArcType.GEODESIC)for(f=0;f<m;f++)D+=C.PolygonGeometryLibrary.subdivideLineCount(t[f],t[(f+1)%m],r);else if(n===G.ArcType.RHUMB)for(f=0;f<m;f++)D+=C.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[f],t[(f+1)%m],r);for(c=new Float64Array(3*D*2),f=0;f<m;++f){var I;P[f]=E/3,n===G.ArcType.GEODESIC?I=C.PolygonGeometryLibrary.subdivideLine(t[f],t[(f+1)%m],r,O):n===G.ArcType.RHUMB&&(I=C.PolygonGeometryLibrary.subdivideRhumbLine(e,t[f],t[(f+1)%m],r,O));for(var x=I.length,w=0;w<x;++w)c[E++]=I[w]}}m=c.length/6;var k=P.length,S=2*(2*m+k),R=h.IndexDatatype.createTypedArray(m+k,S);for(E=0,f=0;f<m;++f)R[E++]=f,R[E++]=(f+1)%m,R[E++]=f+m,R[E++]=(f+1)%m+m;for(f=0;f<k;f++){var N=P[f];R[E++]=N,R[E++]=N+m}return new v.GeometryInstance({geometry:new p.Geometry({attributes:new u.GeometryAttributes({position:new p.GeometryAttribute({componentDatatype:y.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:R,primitiveType:d.PrimitiveType.LINES})})}function x(o){if(t.Check.typeOf.object("options",o),t.Check.typeOf.object("options.polygonHierarchy",o.polygonHierarchy),o.perPositionHeight&&e.defined(o.height))throw new t.DeveloperError("Cannot use both options.perPositionHeight and options.height");if(e.defined(o.arcType)&&o.arcType!==G.ArcType.GEODESIC&&o.arcType!==G.ArcType.RHUMB)throw new t.DeveloperError("Invalid arcType. Valid options are ArcType.GEODESIC and ArcType.RHUMB.");var n=o.polygonHierarchy,a=e.defaultValue(o.ellipsoid,r.Ellipsoid.WGS84),l=e.defaultValue(o.granularity,i.CesiumMath.RADIANS_PER_DEGREE),s=e.defaultValue(o.perPositionHeight,!1),y=s&&e.defined(o.extrudedHeight),p=e.defaultValue(o.arcType,G.ArcType.GEODESIC),d=e.defaultValue(o.height,0),u=e.defaultValue(o.extrudedHeight,d);if(!y){var c=Math.max(d,u);u=Math.min(d,u),d=c}this._ellipsoid=r.Ellipsoid.clone(a),this._granularity=l,this._height=d,this._extrudedHeight=u,this._arcType=p,this._polygonHierarchy=n,this._perPositionHeight=s,this._perPositionHeightExtrude=y,this._offsetAttribute=o.offsetAttribute,this._workerName="createPolygonOutlineGeometry",this.packedLength=C.PolygonGeometryLibrary.computeHierarchyPackedLength(n)+r.Ellipsoid.packedLength+8}x.pack=function(i,o,n){return t.Check.typeOf.object("value",i),t.Check.defined("array",o),n=e.defaultValue(n,0),n=C.PolygonGeometryLibrary.packPolygonHierarchy(i._polygonHierarchy,o,n),r.Ellipsoid.pack(i._ellipsoid,o,n),x.isPlaneMode?(o[n++]=0,o[n++]=0,o[n++]=1):n+=r.Ellipsoid.packedLength,o[n++]=i._height,o[n++]=i._extrudedHeight,o[n++]=i._granularity,o[n++]=i._perPositionHeightExtrude?1:0,o[n++]=i._perPositionHeight?1:0,o[n++]=i._arcType,o[n++]=e.defaultValue(i._offsetAttribute,-1),o[n]=i.packedLength,o};var w=r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),k={polygonHierarchy:{}};function S(t,i){return e.defined(i)&&(t=x.unpack(t,i)),t._ellipsoid=r.Ellipsoid.clone(t._ellipsoid),x.createGeometry(t)}return x.unpack=function(i,o,n){t.Check.defined("array",i),o=e.defaultValue(o,0);var a=C.PolygonGeometryLibrary.unpackPolygonHierarchy(i,o);o=a.startingIndex,delete a.startingIndex;let l=0===i[o+1];var s=r.Ellipsoid.unpack(i,o,w);o+=r.Ellipsoid.packedLength;var y=i[o++],p=i[o++],d=i[o++],u=1===i[o++],c=1===i[o++],f=i[o++],g=i[o++],h=i[o];return e.defined(n)||(n=new x(k)),n._polygonHierarchy=a,n._ellipsoid=r.Ellipsoid.clone(s,n._ellipsoid),n._height=y,n._extrudedHeight=p,n._granularity=d,n._perPositionHeight=c,n._perPositionHeightExtrude=u,n._arcType=f,n._isPlaneMode=l,n._offsetAttribute=-1===g?void 0:g,n.packedLength=h,n},x.fromPositions=function(i){i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT),t.Check.defined("options.positions",i.positions);var r={polygonHierarchy:{positions:i.positions},height:i.height,extrudedHeight:i.extrudedHeight,ellipsoid:i.ellipsoid,granularity:i.granularity,perPositionHeight:i.perPositionHeight,arcType:i.arcType,offsetAttribute:i.offsetAttribute};return new x(r)},x.createGeometry=function(t){var r=t._ellipsoid,n=t._granularity,a=t._polygonHierarchy,l=t._perPositionHeight,s=t._arcType;let d=t._isPlaneMode;var u=C.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(a,!l,r,d);if(0!==u.length){var c,g,h,m=[],b=i.CesiumMath.chordLength(n,r.maximumRadius),v=t._height,E=t._extrudedHeight,A=t._perPositionHeightExtrude||!i.CesiumMath.equalsEpsilon(v,E,0,i.CesiumMath.EPSILON2);if(A)for(h=0;h<u.length;h++){if(c=I(r,u[h],b,l,s,d),c.geometry=C.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(c.geometry,v,E,r,l,d),e.defined(t._offsetAttribute)){var _=c.geometry.attributes.position.values.length/3,G=new Uint8Array(_);t._offsetAttribute===P.GeometryOffsetAttribute.TOP?G=P.arrayFill(G,1,0,_/2):(g=t._offsetAttribute===P.GeometryOffsetAttribute.NONE?0:1,G=P.arrayFill(G,g)),c.geometry.attributes.applyOffset=new p.GeometryAttribute({componentDatatype:y.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:G})}m.push(c)}else for(h=0;h<u.length;h++){if(c=D(r,u[h],b,l,s,d),c.geometry.attributes.position.values=H.PolygonPipeline.scaleToGeodeticHeight(c.geometry.attributes.position.values,v,r,!l,d),e.defined(t._offsetAttribute)){var T=c.geometry.attributes.position.values.length,L=new Uint8Array(T/3);g=t._offsetAttribute===P.GeometryOffsetAttribute.NONE?0:1,P.arrayFill(L,g),c.geometry.attributes.applyOffset=new p.GeometryAttribute({componentDatatype:y.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:L})}m.push(c)}var O=f.GeometryPipeline.combineInstances(m)[0],x=o.BoundingSphere.fromVertices(O.attributes.position.values);return new p.Geometry({attributes:O.attributes,indices:O.indices,primitiveType:O.primitiveType,boundingSphere:x,offsetAttribute:t._offsetAttribute})}},S}));