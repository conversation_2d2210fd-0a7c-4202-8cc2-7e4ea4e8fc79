{"asset": {"generatetool": "<EMAIL>", "gltfUpAxis": "Z", "version": "1.0"}, "geometricError": 1227.03341571558, "root": {"boundingVolume": {"box": [5.82076609134674e-10, -1.28729100245982, 55.4219060610048, 3873.57358047075, 0, 0, 0, 6576.41368446639, 0, 0, 0, 60.0027316664346]}, "children": [{"boundingVolume": {"box": [1907.07962175787, 3265.38869148478, 27.7121574173965, 1964.11963143898, 0, 0, 0, 3309.67559511825, 0, 0, 0, 32.2874333632217]}, "content": {"uri": "tileset_1_1_1.json"}, "geometricError": 153.379176964448, "refine": "REPLACE"}, {"boundingVolume": {"box": [-1923.06241365935, 3229.57046161696, 57.7445127208429, 1948.17644799276, 0, 0, 0, 3282.85896060601, 0, 0, 0, 62.255214356323]}, "content": {"uri": "tileset_1_0_1.json"}, "geometricError": 153.379176964448, "refine": "REPLACE"}, {"boundingVolume": {"box": [-2146.24881746128, -3004.54118605814, 57.9677898328786, 1727.13398761422, 0, 0, 0, 3040.23690017269, 0, 0, 0, 62.0183550855099]}, "content": {"uri": "tileset_1_0_0.json"}, "geometricError": 153.379176964448, "refine": "REPLACE"}, {"boundingVolume": {"box": [2165.78026122492, -3282.7027236301, 24.7067629369951, 1687.44184129405, 0, 0, 0, 3295.61291571218, 0, 0, 0, 29.2753037891615]}, "content": {"uri": "tileset_1_1_0.json"}, "geometricError": 153.379176964448, "refine": "REPLACE"}], "content": {"uri": "0/0/0.b3dm"}, "geometricError": 306.758353928896, "refine": "REPLACE", "transform": [-0.970551653293778, -0.240893105523414, 0.0, 0.0, 0.122495172383308, -0.493529658388545, 0.8610594689298, 0.0, -0.207423289510841, 0.83570269115408, 0.508504268385557, 0.0, -1324120.68691549, 5334845.59076105, 3224389.87425388, 1.0]}}