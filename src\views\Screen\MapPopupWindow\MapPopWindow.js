/*
 * @Description:
 * @Autor: wangjialing
 * @Date: 2023-06-27 14:49:58
 * @LastEditors: wangjialing
 * @LastEditTime: 2024-01-18 11:18:17
 */
import { h, nextTick, render } from 'vue'

import MapPopWindow from './MapPopWindow.vue'

import useStore from '@/store'

/**
 * @description: 函数式创建弹出层
 * @param {string} componentName  - 组件路径，如:DemoWindow/index.vue，也可以使用DemoWindow则默认会取该目录下的index.vue组件
 * @param {Object} options  -配置参数
 * @param {string} [options.left] -离左侧距离
 * @param {string} [options.top] - 离顶部距离
 * @param {string} [options.width] - 宽度
 * @param {string} [options.height] - 高度
 * @param {string} [options.id] - divId默认会生成一串字符串
 * @param {string} [options.tag] - 给节点添加标签，用于统一清除
 * @param {string} [options.appendParent] - 弹出框组件的挂载父容器的Id，传入app，则挂载到#App   如果传入：body,则挂载到body中  默认为app
 * @param {Boolean} [options.followPoint]  -跟随UE点的信息 // * UE中撒点的信息{typeId:1,id:1}
 * @param {Array} [options.popPosition]  -弹窗位置信息（wgs84）- [wgs84.lng, wgs84.lat] // * isFollow为true，必填
 * @param {Array} [options.offset]  -弹窗偏移值 默认值为[0, 0] -- 弹窗左上方
 * @param {Array} [options.appContext]  - 上下文，如果需要继承main.js中app的上下文，  const { appContext } = getCurrentInstance()
 * @param {Array} [options.clickable]  - 是否可以点击
 * @param {Number} [options.scale]  - 缩放比例

 * @param {string} popComponent - 弹出的组件  默认为MapPopWindow
 *  */
export const createPopWindow = (componentName, options, data, popComponent = 'MapPopWindow') => {
  const popWindowMap = {
    MapPopWindow: MapPopWindow
    // MapPoint: MapPoint,
    // MapPointWaves: MapPointWaves
  }
  const PopWindow = popWindowMap[popComponent]
  const { storeScreenPopWindowData } = useStore()
  const opt = {
    left: null,
    top: null,
    right: null,
    bottom: null,
    position: 'absolute',
    tag: null,
    followPoint: null,
    isFollow: false,
    // appendParent: 'app',
    appendParent: 'popUpRoot',
    id: 'mappopwindow_' + Math.ceil(Math.random() * 1000000),
    closeFunc: null,
    isAllowAllDeletion: true,
    popPosition: [],
    offset: [],
    draggableClass: 'popup-title',
    zIndex: null,
    appContext: null,
    clickable: true,
    scale: 1
  }

  const option = {
    ...opt,
    ...options
  }
  if (option.tag == 'mapPoint') option.isFollow = true
  removeDialogById(option.id) // 先清除相同id的弹窗

  const {
    left,
    top,
    position,
    width,
    height,
    id,
    tag,
    appendParent,
    closeFunc,
    popPosition,
    offset,
    isAllowAllDeletion,
    right,
    bottom,
    followPoint,
    isFollow,
    draggableClass,
    zIndex,
    clickable,
    scale
  } = option

  const appendParentDiv = option.appendParent === 'body' ? document.body : document.getElementById(option.appendParent)
  const mountNode = document.createElement('div')
  mountNode.id = option.id + '_parent'
  appendParentDiv.appendChild(mountNode)

  const handleDestroy = () => {
    if (mountNode) {
      // document.getElementById('app').removeChild(mountNode)
      // appendParentDiv.removeChild(mountNode)
      // mountNode = null
      removeDialogById(option.id)
    }
  }

  // 使用 h 函数创建 vnode
  const vnode = h(PopWindow, {
    componentName,
    left,
    top,
    position,
    width,
    height,
    id,
    appendParent,
    data: data || {},
    closeFunc: closeFunc || null,
    popPosition,
    offset,
    isAllowAllDeletion,
    right,
    bottom,
    followPoint,
    draggableClass,
    tag,
    zIndex,
    clickable,
    scale
  })
  if (option.appContext) {
    vnode.appContext = { ...option.appContext }
  }

  // 使用 render 函数将 vnode 渲染为真实DOM并挂载到 body 上
  render(vnode, mountNode)

  nextTick(() => {
    const dialogNodeInfo = { id: option.id, tag: tag, node: mountNode, isAllowAllDeletion, closeFunc: closeFunc }
    if (tag === 'mapPoint') {
      // 撒点
      dialogNodeInfo.pointType = data.pointType
    }
    storeScreenPopWindowData.dialogNodes.push(dialogNodeInfo)
  })

  // document.getElementById('app').appendChild(mountNode)
  nextTick(() => {
    if (options?.appendParent === 'statisticsPopContainer') {
      // 如果加载到此，则滚动到最下面
      const div = document.getElementById('statisticsPopContainer')
      div.scrollTop = div.scrollHeight
    }
  })
  return { id: option.id, handleDestroy }
}

/**
 * @description: 清除弹框
 * @param {string} [tag] - 清除相同tag的弹窗，如果不传则清除全部
 */
export const removeDialog = (tag) => {
  const { storeScreenPopWindowData } = useStore()
  const list = []
  if (tag) {
    storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
      if (_.tag === tag) {
        if (_.closeFunc) {
          _.closeFunc()
        }
        render(null, _.node)
        _.node.remove()
      } else {
        list.push(_)
      }
    })
  } else {
    storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
      if (_.isAllowAllDeletion) {
        render(null, _.node)
        _.node.remove()
      } else {
        list.push(_)
      }
    })
  }
  storeScreenPopWindowData.dialogNodes = [...list]
}

/**
 * @description: 根据id清除弹框
 * @param {*} id 弹窗id
 * @author: qian
 */
export const removeDialogById = (id) => {
  const { storeScreenPopWindowData } = useStore()
  storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
    if (id == _.id) {
      storeScreenPopWindowData.dialogNodes.splice(index, 1)
      render(null, _.node)
      _.node.remove()
    }
  })
}
