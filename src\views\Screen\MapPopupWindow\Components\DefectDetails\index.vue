<template>
  <div class="defect-details-container">
    <PopupBg title="缺陷详情" width="450px" height="587px" @close="handlerClose">
      <PopupSubTitle title="缺陷信息"></PopupSubTitle>
      <!-- <div class="info-box">
        <div class="info-item" v-for="(item, index) in infoData" :key="index">
          <div style="width: 73px; color: #dbefff">{{ item.name }}</div>
          <div style="flex: 1; color: #5bb5ff" v-tooltip>{{ item.value }}</div>
        </div>
      </div> -->
      <div class="info-box gap-10">
        <div class="w-50% flex" v-for="i in 2" :key="i">
          <div class="name-block">
            <div v-for="(item, index) in i == 1 ? infoData.slice(0, 4) : infoData.slice(4)" :key="index" style="width: 73px; color: #dbefff">
              {{ item.name }}:
            </div>
          </div>
          <div class="value-block">
            <div v-for="(item, index) in i == 1 ? infoData.slice(0, 4) : infoData.slice(4)" :key="index">
              <div v-tooltip>{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
      <PopupSubTitle title="缺陷照片" class="mt12"></PopupSubTitle>
      <div class="img-box">
        <PropPicture :data="pictureInfo" width="300px"></PropPicture>
      </div>
      <PopupSubTitle title="历史缺陷列表" class="mt12"></PopupSubTitle>
      <div class="list-box">
        <div class="content" v-if="list.length > 0">
          <div class="content-item headline">
            <div>发生时间</div>
            <div>病害类型</div>
            <div>病害等级</div>
            <div>处理状态</div>
          </div>
          <el-scrollbar height="90px">
            <div class="content-item list-item" v-for="(item, index) in list" :key="index">
              <div v-tooltip>{{ item.time }}</div>
              <div v-tooltip>{{ item.type }}</div>
              <div v-tooltip>{{ item.level }}</div>
              <div :style="{ color: item.color }">{{ item.status }}</div>
            </div>
          </el-scrollbar>
        </div>
        <div class="no-data" v-else>暂无数据</div>
      </div>
    </PopupBg>
  </div>
</template>

<script setup>
  import PropPicture from '@/components/PropPicture/index.vue'
  import PopupBg from '@/components/PopupBg/index.vue'
  import PopupSubTitle from '@/components/PopupSubTitle/index.vue'
  import lib from '@/utils/lib'
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  })
  const pictureInfo = ref([])

  const infoData = ref([
    { name: '构件名称', value: '---' },
    { name: '病害类型', value: '---' },
    { name: '病害等级', value: '--' },
    { name: '病害位置', value: '---' },
    { name: '病害描述', value: '---' },
    { name: '发现人', value: '---' },
    { name: '发现时间', value: '---' },
    { name: '病害来源', value: '---' }
  ])
  const list = ref([])
  watch(
    () => props.data,
    (newVal) => {
      if (newVal) {
        lib.api.defectOrderDetailApi
          .geDefectOrderDetails({
            id: newVal.id
          })
          .then(async (res) => {
            if (res.success && res.result) {
              infoData.value[0].value = res.result.componentName
              infoData.value[1].value = res.result.type
              infoData.value[2].value = res.result.level
              infoData.value[3].value = res.result.mileage
              infoData.value[4].value = res.result.description
              infoData.value[5].value = res.result.finder
              infoData.value[6].value = res.result.findTime
              infoData.value[7].value = res.result.source
              pictureInfo.value = []
              const { picture } = res.result

              Object.keys(picture).map((key) => {
                pictureInfo.value = [
                  ...pictureInfo.value,
                  ...picture[key].map((_) => {
                    return { url: _ }
                  })
                ]
              })

              const picRes = await lib.api.docDiyDiyApi.getDocsFromUrl({ urlList: pictureInfo.value.map((_) => _.url) })
              if (picRes.success) {
                picRes.result.forEach((item, index) => {
                  pictureInfo.value[index].url = import.meta.env.VITE_DOC_URL + 'img/' + item.fileName
                })
              }
              const colorMap = {
                待处理: '#F56C22',
                已处理: '#55E68A'
              }
              list.value = res.result.history?.map((item) => {
                return {
                  ...item,
                  color: colorMap[item.status]
                }
              })
            }
          })
      }
    },
    { immediate: true, deep: true }
  )
  const handlerClose = () => {
    lib.popWindow.removeDialog('maintenanceWindow')
  }
</script>

<style lang="scss" scoped>
  .defect-details-container {
    width: 450px;
    height: 587px;

    // background: url('@/assets/CommonPopup/popupBg2.png');
    // background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 7px 17px 0;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .sub-title {
      width: 343px;
      height: 24px;
      padding-left: 28px;
      margin: 14px 21px 0 14px;
      font-family: PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/CommonPopup/title.png');
      background-size: 343px 24px;
    }
    .info-box {
      display: flex;

      // flex-wrap: wrap;
      margin: 11px 21px 0 0;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      .name-block,
      .value-block {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 96px;
        height: 150px;
        padding: 10px;
        margin-right: 10px;
        color: #5bb5ff;
        background: rgb(39 75 113 / 22%);
        border-radius: 13px;
      }
      .value-block {
        // flex: 2;
        margin-right: 0;
        color: #ffffff;
      }
    }
    .img-box {
      display: flex;
      margin: 9px 18px 25px 14px;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      color: #dbefff;
      :nth-child(2) {
        width: 282px;
        height: 68px;
        margin-left: 10px;
      }
    }
    .list-box {
      margin: 9px 21px 0 14px;
      .content {
        .content-item {
          display: flex;
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
          &.headline {
            width: 387px;
            height: 30px;
            margin-bottom: 5px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            color: #ffffff;
            border-bottom: 1px solid rgb(0 179 255 / 55%);
          }
          &.list-item {
            width: 387px;
            height: 24px;
            margin-bottom: 8px;
            background: linear-gradient(180deg, rgb(0 178 255 / 0%) 0%, rgb(0 178 255 / 14%) 100%);
            border-radius: 0;
          }
          div {
            flex: 1;
            overflow: hidden;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .no-data {
        width: 387px;
        height: 120px;
        line-height: 120px;
        color: #ffffff;
        text-align: center;
      }
    }
  }
</style>
