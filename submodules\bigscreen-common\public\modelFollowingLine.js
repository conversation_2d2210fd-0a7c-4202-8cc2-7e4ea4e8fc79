const ModelFollowingLine = function (viewer, options) {
  this.viewer = viewer;
  this.endTime = options.endTime;
  this.linePosition = options.linePosition;
  this.lineTime = options.lineTime;
  this.model = options.model;
  this.image = options.image;
  this._multiplier = options.multiplier || 0.1;

  this._modelState = false;
  this._isFinish = false;
  this.init(); 
}

ModelFollowingLine.prototype.init = function () {
  const startTime = this.viewer.clock.currentTime;
  this.movingProperty = computeMovingProperty(this.linePosition, this.lineTime, startTime, this.multiplier);
  // console.log(this.movingProperty);

  const distance = Cesium.Cartesian3.distance(this.linePosition[0], this.linePosition[1]);
  const duration = this.lineTime[1];
  this.speed = distance/duration;

  this.entity = this.viewer.entities.add({
    position: this.movingProperty,
    orientation: new Cesium.VelocityOrientationProperty(this.movingProperty),
    model: {
      uri: this.model,
      minimumPixelSize: 64,
    },  
    label: {
      // text: `speed: ${this.speed.toFixed(2)} m/s`,
      font: "14pt",
      showBackground: true,
      backgroundColor: new Cesium.Color(0, 0, 0, 0),
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(60, -215),
      // eyeOffset: new Cesium.Cartesian3(0, 0, -10),
    },
    billboard: {
      image: this.image,
      scale: 1,
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
      // eyeOffset: new Cesium.Cartesian3(0, 0, 10),
    },  
  });

  this.pause();
  // 监听实时速度和结束时间
  listenAnimation(this);
}

ModelFollowingLine.prototype.start = function () {
  if (this.modelState && !this.isFinish) {
    if (Cesium.defined(this.stopIndex) && this.stopIndex < this.linePosition.length) {
      let newMovingPosition = this.linePosition.slice(this.stopIndex);
      let newMovingTime = this.lineTime.slice(this.stopIndex);
      // console.log(newMovingPosition, newMovingTime);

      if (this.currentTime) {
        newMovingPosition.unshift(this.stopPosition);

        const subDuration = this.lineTime[this.stopIndex];       
        const stopBeforePosition = this.linePosition[this.stopIndex-1];
        const stopAfterPosition = this.linePosition[this.stopIndex];
        const subDistance = Cesium.Cartesian3.distance(stopBeforePosition, stopAfterPosition);
        const stopDistance = Cesium.Cartesian3.distance(this.stopPosition, stopAfterPosition);
        const stopDuration = (subDuration*stopDistance)/subDistance;

        newMovingTime.unshift(0);
        newMovingTime[1] = stopDuration;
      }
      
      this.movingProperty.removeSample(this.stopTime); 
      this.movingProperty.removeSample(this.endTime);

      this.currentTime = this.viewer.clock.currentTime;
      let deltaTime;
      for (let i = 0; i < newMovingPosition.length; i++) {
        const position = newMovingPosition[i];

        if (!deltaTime) {
          deltaTime = newMovingTime[i];
        } else {
          deltaTime += newMovingTime[i];
        }

        this.finishTime = Cesium.JulianDate.addSeconds(
          this.currentTime,
          // 到达每个点的时间
          deltaTime/this.multiplier,
          new Cesium.JulianDate()
        );
        
        this.movingProperty.addSample(this.finishTime, position);
      }

      this.entity.orientation = new Cesium.VelocityOrientationProperty(this.movingProperty);
      // this.entity.label.text = `speed: ${this.speed.toFixed(2)} m/s`;
    }
    this.modelState = false;
  }
}

ModelFollowingLine.prototype.pause = function () {
  if (!this.modelState && !this.isFinish) {
    this.stopTime = this.viewer.clock.currentTime;
    this.stopPosition = this.entity.position.getValue(this.stopTime);
    const orientation = this.entity.orientation.getValue(this.stopTime);

    // 去除上次暂停时增加的样例
    if (this.currentTime && this.stopIndex) {
      this.movingProperty.removeSample(this.currentTime);
    }

    // 去除暂停后的样例
    this.movingProperty.removeSamples(
      new Cesium.TimeInterval({start: this.stopTime, stop: this.endTime})
    );

    this.stopIndex = this.movingProperty._property._times.length;
    // console.log(this.stopIndex);

    if (this.stopPosition) {
      this.movingProperty.addSample(this.stopTime, this.stopPosition);
      this.movingProperty.addSample(this.endTime, this.stopPosition);
      this.entity.orientation = orientation;
    }
    this.modelState = true;
  }
}

function computeMovingProperty(movingPosition, movingTime, startTime, multiplier) {
  const property = new Cesium.SampledPositionProperty();

  let subTime;
  for (let i = 0; i < movingPosition.length; i++) {
    const position = movingPosition[i];
    if (!subTime) {
      subTime = movingTime[i];
    } else {
      subTime += movingTime[i];
    }
    
    const time = Cesium.JulianDate.addSeconds(
      startTime,
      // 到达每个点的时间
      subTime/multiplier,
      new Cesium.JulianDate()
    );
    // console.log(movingTime[i]);
    
    property.addSample(time, position);
  }

  return property
}

function listenAnimation (ModelFollowingLine) {
  let nextIndex = 0;
  const travelLength = ModelFollowingLine.linePosition.length;

  const watchAnimation = (scene, time) => {
    // 实时更改速度标签
    if (nextIndex < travelLength - 1 && !ModelFollowingLine.modelState) {
    const movingProperty = ModelFollowingLine.entity.position._property;
    const movingLength = movingProperty._times.length;
    // console.log('nextIndex', nextIndex);
    // console.log('movingLength', movingLength);
    const nextIndexTime = movingLength === travelLength ? movingProperty._times[nextIndex]:movingProperty._times[nextIndex+1];
    const isNextTravel = Cesium.JulianDate.greaterThanOrEquals(time, nextIndexTime);
    
      if (isNextTravel) {
        const initDistance = Cesium.Cartesian3.distance(ModelFollowingLine.linePosition[nextIndex], ModelFollowingLine.linePosition[nextIndex+1]);
        const initDuration = ModelFollowingLine.lineTime[nextIndex+1];
        const speed = initDistance/initDuration;

        // ModelFollowingLine.entity.label.text = `speed: ${speed.toFixed(2)} m/s`;
        nextIndex += 1;
      }
    }

    // 监听运动结束时间
    const finishTime = ModelFollowingLine.finishTime;

    if (finishTime && !ModelFollowingLine.modelState) {
      ModelFollowingLine.isFinish = Cesium.JulianDate.greaterThanOrEquals(time, finishTime);

      if (ModelFollowingLine.isFinish && finishHandler instanceof Function) {
        // 动画结束时小车不消失
        const orientation = ModelFollowingLine.entity.orientation.getValue(finishTime);
        // console.log(orientation);
        ModelFollowingLine.movingProperty.addSample(ModelFollowingLine.endTime, ModelFollowingLine.linePosition[ModelFollowingLine.linePosition.length-1]);
        ModelFollowingLine.entity.orientation = orientation;

        console.log('小车结束运动！');
        finishHandler();
      }
    }
  }

  const finishHandler = ModelFollowingLine.viewer.scene.preRender.addEventListener(watchAnimation);
}

Object.defineProperties(ModelFollowingLine.prototype, {
  entity: {
    get: function () {
      return this._entity;
    },
    set: function (value) {
      this._entity = value;
    }
  },
  speed: {
    get: function () {
      return this._speed;
    },
    set: function (value) {
      this._speed = value;
    }
  },
  multiplier: {
    get: function () {
      return this._multiplier;
    },
    set: function (value) {
      this._multiplier = value;
    }
  },
  modelState: {
    get: function () {
      return this._modelState;
    },
    set: function (value) {
      this._modelState = value;
    }
  },
  isFinish: {
    get: function () {
      return this._isFinish;
    },
    set: function (value) {
      this._isFinish = value;
    }
  },

});
