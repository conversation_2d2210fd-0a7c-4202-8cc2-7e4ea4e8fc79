---
description: 
globs: 
alwaysApply: false
---
# ZnTable 表格组件使用指南

## 重要说明

**在使用ZnTable组件时，必须先调用MCP服务中的getLocalDoc方法来查看最新的组件文档和属性：**

```javascript
// 调用MCP服务获取ZnTable最新文档
// URL: http://*************:10000/znyg-frontend-common-docs/components/ZnTable.html
// 此操作将获取ZnTable的所有最新属性、方法和使用说明
```

## 标准Mock数据格式

当生成Mock数据时，请使用以下标准格式：

```json
{
  "message": "角色查询成功！",
  "messages": [
    {
      "body": "角色查询成功！",
      "level": 1
    }
  ],
  "result": {
    "currPage": 1,
    "exp": {},
    "limit": 15,
    "list": [
      {
        "code": "xmbczry",
        "description": "项目部操作层：系统级项目管理列表，项目级设施/设备清单、人/物资/车辆等基础数据、业务模块基本权限",
        "id": 38,
        "name": "项目部操作人员",
        "recordCreateDate": "2020-03-09 11:31:07.000",
        "recordUpdateDate": "2025-04-17 16:42:37.000",
        "state": 36,
        "type": 100
      },
      {
        "code": "xmbglry",
        "description": "项目部管理层：系统级项目管理列表、基础信息维护；项目级基本所有权限",
        "id": 32,
        "name": "项目部管理人员",
        "recordCreateDate": "2020-03-09 11:31:07.000",
        "recordUpdateDate": "2025-04-17 16:42:40.000",
        "state": 39,
        "type": 100
      }
    ],
    "offset": 0,
    "start": 0,
    "total": 16,
    "totalPage": 2
  },
  "status": 1,
  "success": true
}
```

## 数据处理说明

ZnTable组件的`dataCallback`函数应该在helper文件中统一管理，不需要在组件中单独配置。数据处理逻辑已集成到hooks函数中。

## 项目结构说明

案件列表界面位于 [src/views/case/list](mdc:src/views/case/list) 文件夹下：

- [index.vue](mdc:src/views/case/list/index.vue) - 主入口文件，展示表格组件使用
- [helper/index.tsx](mdc:src/views/case/list/helper/index.tsx) - 统一管理ZnTable的配置和方法
- [components/](mdc:src/views/case/list/components) - 子组件文件夹

## ZnTable 组件核心属性

基于 ZnTable 文档，以下是常用的核心属性：

### 必传属性

```typescript
// 列配置，必传
columns: ColumnProps[]
```

### 重要可选属性

```typescript
// 请求API
requestApi?: (params: any) => Promise<any>
// 是否自动执行请求
requestAuto?: boolean = true
// 是否需要分页
pagination?: boolean = true
// 初始化参数
initParam?: any = {}
// 表格高度
height?: string | number
// 搜索表单是否显示
searchFormShow?: boolean = true
```

## 列配置 (ColumnProps)

### 基础列配置

```typescript
{
  prop: 'fieldName',        // 字段名
  label: '列标题',           // 列标题
  width: 150,               // 列宽度
  isShow: true,             // 是否显示
}
```

### 操作列配置（推荐使用）

使用 `type: 'operationBtns'` 来配置操作按钮列：

```typescript
{
  prop: 'operation',
  label: '操作',
  type: 'operationBtns',
  fixed: 'right',
  width: 200,
  props: {
    btns: [
      {
        name: 'view',
        label: '查看',
        show: true
      },
      {
        name: 'edit',
        label: '编辑',
        show: (row) => row.status !== 'readonly'
      },
      {
        name: 'delete',
        label: '删除',
        show: (row) => row.canDelete
      }
    ],
    maxCount: 3,              // 最大显示按钮数量，超过的会放入下拉菜单
    moreText: '更多'           // 更多按钮文本
  }
}
```

### 搜索配置

```typescript
{
  search: {
    el: 'input',            // 搜索框类型：input/select/date-picker等
    render: ({ searchParam }) => {
      // 自定义搜索组件渲染
      return <ProSelect v-model:id={searchParam.fieldName} />
    }
  }
}
```

### 内容渲染

```typescript
{
  render: (scope) => {
    // 自定义单元格内容，支持JSX语法
    return <span style={{color: 'red'}}>{scope.row.fieldName}</span>
  }
}
```

### 枚举字典

```typescript
{
  dictCode: 'dictionary_code',  // 字典编码，自动从字典获取枚举值
  // 或者
  enum: [
    { label: '显示名', value: 'value1' },
    { label: '显示名2', value: 'value2' }
  ]
}
```

**重要规范**：当通过MCP服务获取接口文档时，如果字段描述中包含类似"字典：area"、"字典：structure_tech_category"等字典说明，应该：

1. **优先使用dictCode配置**：直接使用字典编码，不需要手动配置enum属性
2. **字典编码提取**：从字段描述中提取字典编码（冒号后的部分）
3. **自动枚举加载**：ZnTable会自动从localStorage中的字典数据加载对应的枚举选项

```typescript
// ✅ 正确：根据接口文档字段描述"字典：area"配置
{
  prop: 'areaList',
  label: '所属区域',
  search: {
    el: 'select',
    props: {
      placeholder: '请选择所属区域',
      multiple: true
    }
  },
  dictCode: 'area',  // 直接使用字典编码
  render: (scope) => {
    return scope.row.area || '-'
  }
}

// ❌ 错误：手动配置enum属性
{
  prop: 'areaList',
  label: '所属区域',
  search: {
    el: 'select',
    props: {
      placeholder: '请选择所属区域',
      multiple: true
    }
  },
  enum: [  // 不需要手动配置
    { label: '成华区', value: 'chenghua' },
    { label: '锦江区', value: 'jinjiang' }
  ]
}
```

**常见字典编码示例**：
- `area` - 区域字典
- `structure_tech_category` - 技术规范分类字典
- `structure_maintain_level` - 养护等级字典
- `city_manage_category` - 城市管理分类字典
- `administer_range` - 管辖范围字典
```

## 标准使用模式

当创建表格或识别图片中的表格时，请按照以下模式生成代码：

### 1. 模板部分

**重要规范**：每个组件的最外层必须有一个具有语义化的容器class，该class应该作为CSS样式的最顶层class。

```vue
<template>
  <div class="module-name-container table-container">
    <Card title="表格标题">
      <ZnTable
        ref="refZnTable"
        :request-api="requestApi"
        :columns="columns"
        :data-callback="dataCallback"
        @handle-operate-btns-click="handleOperateBtnsClick">
        <!-- 表格头部插槽 -->
        <template #tableHeader="scope">
          <div class="flex items-center">
            <!-- 状态切换按钮组 -->
            <el-radio-group v-model="status" @change="handleStatusChange">
              <el-radio-button label="全部" value="" />
              <el-radio-button label="待处理" value="pending" />
              <el-radio-button label="已处理" value="processed" />
            </el-radio-group>
            <!-- 操作按钮 -->
            <el-button type="primary" class="ml-20" @click="handleAdd">新增</el-button>
          </div>
        </template>
      </ZnTable>
    </Card>
  </div>
</template>
```

**容器class命名规范**：
- 使用 `模块名-container` 的格式，如：`user-manage-container`、`road-manage-container`、`privilege-manage-container`
- 可以同时添加功能性class，如：`table-container`
- 在CSS中，该容器class必须作为最顶层的选择器

### 2. 脚本部分

```vue
<script setup lang="tsx">
import { ref } from "vue"
import { ZnTable, type ColumnProps } from "znyg-frontend-common"
import { useRoadManage } from "./helper"

// 状态管理
const status = ref("")
const refZnTable = ref()

// 使用helper中的配置，注意hooks命名方式：使用具体模块名称
const { columns, requestApi, dataCallback } = useRoadManage(refZnTable)

// 事件处理
const handleStatusChange = () => {
  refZnTable.value?.search()
}

// 操作按钮点击事件处理
const handleOperateBtnsClick = (btnName: string, row: any, btn: any) => {
  console.log("点击的按钮:", btnName)
  console.log("当前行数据:", row)

  switch (btnName) {
    case "view":
      handleView(row)
      break
    case "edit":
      handleEdit(row)
      break
    case "delete":
      handleDelete(row)
      break
  }
}

const handleAdd = () => {
  // 新增逻辑
}

const handleView = (row: any) => {
  // 查看逻辑
}

const handleEdit = (row: any) => {
  // 编辑逻辑
}

const handleDelete = (row: any) => {
  // 删除逻辑
}
</script>
```

### 3. Helper配置文件

在 `helper/index.tsx` 中统一管理配置：

```typescript
import { reactive } from "vue"
import { type ColumnProps } from "znyg-frontend-common"
import lib from "@/lib"

// Mock数据（开发阶段使用）
const mockData = {
  message: "道路管理查询成功！",
  messages: [
    {
      body: "道路管理查询成功！",
      level: 1,
    },
  ],
  result: {
    currPage: 1,
    exp: {},
    limit: 15,
    list: [
      {
        id: 1,
        roadName: "凤凰大道",
        startPoint: "凤凰山隧道口",
        endPoint: "凤凰广场",
        managementScope: "市政道路",
        area: "高新区",
        cityManagementLevel: "主干道",
        maintainLevel: "I等",
        roadLength: 3200,
        managementUnit: "市政管理局",
        facilityStatus: "正常",
        roadLevel: "一级",
        createTime: "2023-01-15 09:30:00",
        updateTime: "2024-12-15 14:20:00",
      },
      // ... 更多数据项
    ],
    offset: 0,
    start: 0,
    total: 10,
    totalPage: 1,
  },
  status: 1,
  success: true,
}

// 数据处理回调函数
const dataCallback = (data: any) => {
  // 从标准格式中提取实际的表格数据
  return data || []
}

export const useRoadManage = (refZnTable: any) => {
  const columns = reactive<ColumnProps[]>([
    { type: "index", width: 60, label: "序号" },
    {
      prop: "roadName",
      label: "道路名称",
      width: 150,
      search: { el: "input" },
    },
    {
      prop: "status",
      label: "状态",
      dictCode: "status_dict",
      search: { el: "select" },
    },
    {
      prop: "createTime",
      label: "创建时间",
      render: "dateFormat@YYYY-MM-DD HH:mm:ss",
    },
    {
      prop: "operation",
      label: "操作",
      type: "operationBtns" as any,
      width: 200,
      fixed: "right",
      props: {
        btns: [
          {
            name: "view",
            label: "查看",
            show: true,
          },
          {
            name: "edit",
            label: "编辑",
            type: "text",
            show: (row: any) => row.facilityStatus !== "停用",
          },
          {
            name: "delete",
            label: "删除",
            type: "text",
            show: (row: any) => row.canDelete,
          },
        ],
        maxCount: 3,
        moreText: "更多",
      },
    },
  ])

  const requestApi = async (params: any) => {
    try {
      // 参数转换：将搜索参数转换为接口期望的格式
      const apiParams = {
        ...params
      }

      // 使用lib.api调用真实接口 - 直接传递参数，不需要包装在params对象中
      const response = await lib.api.roadManageApi.list(apiParams)
      return response
    } catch (error) {
      console.error("API调用失败，使用Mock数据:", error)
      // 开发阶段API失败时返回Mock数据
      return mockData
    }
  }

  return {
    columns,
    requestApi,
    dataCallback,
    refZnTable,
  }
}
```

## 快速功能说明

### 日期格式化

```typescript
render: "dateFormat@YYYY-MM-DD HH:mm:ss"
```

### 字典枚举

```typescript
{
  dictCode: 'dictionary_code',  // 字典编码，自动从字典获取枚举值
  // 或者
  enum: [
    { label: '显示名', value: 'value1' },
    { label: '显示名2', value: 'value2' }
  ]
}
```


- `city_manage_category` - 城市管理分类字典
- `administer_range` - 管辖范围字典

**重要规范**：当通过MCP服务获取接口文档时，如果字段描述中包含类似"字典：area"、"字典：structure_tech_category"等字典说明，应该：

1. **优先使用dictCode配置**：直接使用字典编码，不需要手动配置enum属性
2. **字典编码提取**：从字段描述中提取字典编码（冒号后的部分）
3. **自动枚举加载**：ZnTable会自动从localStorage中的字典数据加载对应的枚举选项

```typescript
// ✅ 正确：根据接口文档字段描述"字典：area"配置
{
  prop: 'areaList',
  label: '所属区域',
  search: {
    el: 'select',
    props: {
      placeholder: '请选择所属区域',
      multiple: true
    }
  },
  dictCode: 'area',  // 直接使用字典编码
  render: (scope) => {
    return scope.row.area || '-'
  }
}

// ❌ 错误：手动配置enum属性
{
  prop: 'areaList',
  label: '所属区域',
  search: {
    el: 'select',
    props: {
      placeholder: '请选择所属区域',
      multiple: true
    }
  },
  enum: [  // 不需要手动配置
    { label: '成华区', value: 'chenghua' },
    { label: '锦江区', value: 'jinjiang' }
  ]
}
```

**常见字典编码示例**：
- `area` - 区域字典
- `structure_tech_category` - 技术规范分类字典
- `structure_maintain_level` - 养护等级字典
- `city_manage_category` - 城市管理分类字典
- `administer_range` - 管辖范围字典

### 搜索组件

- `el: 'input'` - 输入框
- `el: 'select'` - 下拉选择
- `el: 'date-picker'` - 日期选择器
- `render: ({ searchParam }) => JSX` - 自定义搜索组件

### 表格方法

- `refZnTable.value.search()` - 搜索
- `refZnTable.value.reset()` - 重置
- `refZnTable.value.refreshTable()` - 刷新表格
- `refZnTable.value.clearSelection()` - 清空选择

## 操作按钮配置详解

### 按钮属性说明

每个操作按钮支持以下属性：

```typescript
{
  name: string,                                    // 按钮名称（唯一标识），点击时传递给事件回调
  label: string,                                   // 按钮显示文本
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text',  // 按钮类型，默认 'text'
  size?: 'large' | 'default' | 'small',          // 按钮大小，默认 'small'
  disabled?: boolean,                              // 是否禁用，默认 false
  icon?: Component,                                // 按钮图标
  show?: boolean | ((row: any) => boolean)        // 是否显示，支持函数动态判断
}
```

### 容器属性说明

操作按钮容器支持以下配置：

```typescript
{
  btns: OperationBtnProps[],      // 按钮配置数组
  maxCount?: number,              // 最大显示按钮数量，超过此数量的按钮会显示在下拉菜单中，默认3
  moreText?: string,              // 更多按钮的文本，默认'更多'
  minWidth?: string               // 下拉菜单最小宽度，默认'120px'
}
```

### 完整操作列示例

```typescript
{
  prop: "operation",
  label: "操作",
  type: "operationBtns",
  width: 250,
  fixed: "right",
  props: {
    btns: [
      {
        name: 'view',
        label: '查看',
        type: 'text',
        show: true
      },
      {
        name: 'edit',
        label: '编辑',
        type: 'primary',
        show: (row) => row.status === 'active'
      },
      {
        name: 'copy',
        label: '复制',
        type: 'text',
        show: (row) => row.canCopy
      },
      {
        name: 'delete',
        label: '删除',
        type: 'danger',
        show: (row) => row.canDelete
      },
      {
        name: 'export',
        label: '导出',
        type: 'text',
        show: true
      }
    ],
    maxCount: 3,                // 前3个按钮直接显示，后面的在"更多"菜单中
    moreText: '更多操作',
    minWidth: '120px'
  }
}
```

### 事件处理最佳实践

```typescript
const handleOperateBtnsClick = (btnName: string, row: any, btn: any) => {
  console.log("点击的按钮:", btnName)
  console.log("当前行数据:", row)
  console.log("按钮配置:", btn)

  switch (btnName) {
    case "view":
      router.push(`/detail/${row.id}`)
      break
    case "edit":
      openEditDialog(row)
      break
    case "copy":
      handleCopy(row)
      break
    case "delete":
      handleDeleteConfirm(row)
      break
    case "export":
      handleExport(row)
      break
    default:
      console.warn(`未处理的操作类型: ${btnName}`)
  }
}
```

## API接口配置

### 在src/api/index.ts中添加接口

按照模块进行接口分组，每个模块都应在 [src/api/index.ts](mdc:src/api/index.ts) 中配置：

```typescript
// #region 道路管理
export const roadManageApi = {
  list: (params: any) => requestApi("rest/roadManage/list", params),
  save: (params: any) => requestApi("rest/roadManage/save", params),
  get: (params: any) => requestApi("rest/roadManage/get", params),
  delete: (params: any) => requestApi("rest/roadManage/delete", params),
}
// #endregion 道路管理
```

### Helper文件中的API调用

在helper文件中使用 `lib.api` 调用接口：

```typescript
import lib from "@/lib"

const requestApi = async (params: any) => {
  try {
    // 参数转换：将搜索参数转换为接口期望的格式
    const apiParams = {
      ...params
    }

    // 使用lib.api调用真实接口 - 直接传递参数，不需要包装在params对象中
    const response = await lib.api.roadManageApi.list(apiParams)
    return response
  } catch (error) {
    console.error("API调用失败，使用Mock数据:", error)
    // 开发阶段API失败时返回Mock数据
    return mockData
  }
}
```

### API调用规范

**重要**：根据最新的项目规范，API调用应该：

1. **直接传递参数**：不需要将参数包装在 `params` 对象中
2. **简化参数处理**：直接使用展开运算符传递所有搜索参数
3. **移除固定参数**：不要在helper中硬编码 `projectId` 等固定参数

```typescript
// ✅ 正确的API调用方式
const response = await lib.api.structureDiyApi.bridgePageList(apiParams)

// ❌ 错误的API调用方式
const response = await lib.api.structureDiyApi.bridgePageList({
  params: apiParams
})
```

## 命名规范

### Hooks函数命名

hooks函数应该使用具体的模块名称，而不是通用的 `useTableConfig`：

- ✅ 正确：`useRoadManage` （道路管理）
- ✅ 正确：`useUserManage` （用户管理）
- ✅ 正确：`useProjectManage` （项目管理）
- ❌ 错误：`useTableConfig` （通用配置）

### 表格Ref命名

统一使用 `refZnTable` 作为ZnTable组件的ref名称：

```vue
<template>
  <ZnTable ref="refZnTable" />
</template>

<script setup>
const refZnTable = ref()
const { columns, requestApi } = useRoadManage(refZnTable)
</script>
```

## 重要提醒

1. **必须使用operationBtns类型**：新版本ZnTable推荐使用 `type: 'operationBtns'` 配置操作列，不再推荐使用模板插槽方式。

2. **监听事件**：在ZnTable组件上添加 `@handle-operate-btns-click="handleOperateBtnsClick"` 事件监听。

3. **按钮显示控制**：通过 `show` 属性控制按钮的显示与隐藏，支持函数动态判断。

4. **超过maxCount的按钮会自动收纳到"更多"下拉菜单中**，保持界面整洁。

5. **Mock数据和dataCallback函数**：都应该在helper文件中统一管理，不需要在组件中单独配置。

6. **API调用**：统一使用 `lib.api.moduleName.methodName(params)` 的方式调用接口，直接传递参数，不需要包装。

7. **参数处理**：使用展开运算符 `...params` 直接传递所有搜索参数，避免硬编码固定参数。

请按照此标准模式创建表格相关代码。
