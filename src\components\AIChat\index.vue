<template>
  <div class="ai-chat-contaienr">
    <div class="absolute top-0 left-0 w-full h80 bg-transparent cursor-move drag-container"></div>
    <el-icon class="!absolute top-30 right-30 cursor-pointer" size="36px" @click="handleClose"><CloseBold /></el-icon>
    <div class="flex gap-78" :class="{ 'collapsed-layout': isHistoryCollapsed }">
      <div class="history-container" :class="{ collapsed: isHistoryCollapsed }">
        <!-- 历史对话框 -->
        <div class="history-dialog" v-if="!isHistoryCollapsed">
          <!-- 头部搜索区域 -->
          <div class="history-header">
            <div class="search-container">
              <el-input v-model="searchKeyword" placeholder="搜索历史对话" clearable @input="handleSearchHistory"></el-input>
              <!-- <el-button type="text" class="search-btn" @click="handleSearchHistory"> -->
              <!-- <el-icon class="search-icon" @click="handleSearchHistory"><Search /></el-icon> -->
              <img src="@/assets/CommonPopup/AIChat/search.png" class="w24 h24 cursor-pointer" @click="handleSearchHistory" />

              <!-- </el-button> -->
            </div>
            <!-- <el-button class="collapse-btn" @click="toggleHistoryCollapse"> -->
            <img src="@/assets/CommonPopup/AIChat/collapse.png" class="w24 h24 cursor-pointer" @click="toggleHistoryCollapse" />
            <!-- <el-icon class="collapse-btn" @click="toggleHistoryCollapse"><ArrowLeft /></el-icon> -->
            <!-- </el-button> -->
          </div>

          <!-- 最近标题和操作按钮 -->
          <div class="history-toolbar">
            <div class="toolbar-title">最近</div>
            <div class="toolbar-actions cursor-pointer">
              <el-icon @click="handleToolbarCommand('select')" class="cursor-pointer"><EditPen /></el-icon>
              <!-- <el-dropdown @command="handleToolbarCommand" placement="bottom-end">
                <div class="more-btn">
                  <el-icon><MoreFilled /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="select">
                      <el-icon><Select /></el-icon>
                      选择
                    </el-dropdown-item>
                    <el-dropdown-item command="clear" class="danger-item">
                      <el-icon><Delete /></el-icon>
                      清除所有对话
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown> -->
            </div>
          </div>

          <!-- 历史对话列表 -->
          <div class="history-content">
            <div class="history-list" v-if="filteredHistoryGroups.length > 0">
              <div v-for="group in filteredHistoryGroups" :key="group.title" class="history-group">
                <div class="group-title">{{ group.title }}</div>
                <div class="group-items">
                  <div v-for="item in group.items" :key="item.id" class="history-item" @click="!isSelectMode ? handleSelectHistory(item) : null">
                    <el-checkbox
                      v-if="isSelectMode"
                      :model-value="selectedHistoryItems.includes(item.id)"
                      @change="(checked) => handleHistoryItemSelect(checked, item)"
                      class="history-checkbox"
                      @click.stop />
                    <div class="item-title" :class="{ 'with-checkbox': isSelectMode }">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <div class="empty-icon">
                <el-icon size="48"><ChatDotRound /></el-icon>
              </div>
              <div class="empty-text">暂无历史对话</div>
            </div>
          </div>
        </div>

        <!-- 选择模式下的底部操作按钮 -->
        <div v-if="isSelectMode" class="history-actions">
          <div class="actions-content">
            <div class="select-info">
              <span>已选择</span>
              <span class="select-count">{{ selectedHistoryItems.length }}</span>
              <span>项</span>
            </div>
            <div class="actions-buttons">
              <el-button type="primary" @click="handleConfirmSelection" :disabled="selectedHistoryItems.length === 0">确定</el-button>
              <el-button @click="handleCancelSelection">取消</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1">
        <BubbleList :list="list" :max-height="maxHeight + 'px'">
          <template #header="{ item }">
            <Thinking
              v-if="item.thinkingStatus"
              :content="item.thinkingContent"
              :status="item.thinkingStatus"
              autoCollapse
              class="thinking-chain-warp"
              color="#333333"
              maxWidth="1700px"
              @change="handleThinkingChange" />
          </template>
          <template #content="{ item }">
            <XMarkdown :markdown="item.content" class="markdown-block" />
          </template>
          <!-- 自定义底部 -->
          <template #footer="{ item }">
            <div class="quote-list" v-if="item?.quoteList?.length > 0">
              <div 
                class="quote-item" 
                v-for="(mergedQuote, index) in getMergedQuoteList(item.quoteList)" 
                :key="index">
                <!-- 鼠标悬停弹窗 -->
                <el-popover
                  :visible="mergedQuote.showPopover"
                  placement="top"
                  :width="400"
                  trigger="manual"
                  popper-class="quote-detail-popover"
                  :popper-style="{ zIndex: 9999 }"
                  :show-arrow="true"
                  :hide-after="0"
                  :show-after="0">
                  <template #reference>
                    <div 
                      class="quote-item-wrapper"
                      @mouseenter="handleQuoteMouseEnter(mergedQuote, $event)"
                      @mouseleave="handleQuoteMouseLeave(mergedQuote)">
                      <el-checkbox
                        :model-value="selectedFiles.some((file) => file.id === mergedQuote.id?.toString())"
                        @change="(checked) => handleQuoteSelect(checked as boolean, mergedQuote)"
                        :disabled="!selectedFiles.some((file) => file.id === mergedQuote.id?.toString()) && selectedFiles.length >= 5"
                        class="quote-checkbox" />
                      <div class="quote-item-icon">
                        <el-icon><Document /></el-icon>
                      </div>
                      <div class="quote-item-content" @click="handleQuote(mergedQuote)">
                        <div class="quote-item-title">
                          {{ mergedQuote.fileName }}
                          <span v-if="mergedQuote.count > 1" class="quote-count">(#{{ mergedQuote.count }})</span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="quote-detail-content">
                    <div class="quote-detail-header">
                      <el-icon><Document /></el-icon>
                      <span class="quote-detail-title">{{ mergedQuote.fileName }}</span>
                      <span class="quote-detail-count">共 {{ mergedQuote.count }} 个片段</span>
                    </div>
                    <div class="quote-detail-list">
                      <div 
                        v-for="(quote, idx) in mergedQuote.originalQuotes" 
                        :key="idx"
                        class="quote-detail-item"
                        @click="handleQuote(quote)">
                        <div class="quote-detail-item-header">
                          <span class="quote-detail-item-index">#{{ idx + 1 }}</span>
                          <span class="quote-detail-item-score">相关度: {{ (quote.score * 100).toFixed(1) }}%</span>
                        </div>
                        <div class="quote-detail-item-content">
                          {{ quote.content?.substring(0, 100) }}{{ quote.content?.length > 100 ? '...' : '' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>
            <div v-if="item.echartsJSONData" class="echarts-container">
              <ZnChart :key="item.key" :option="item.echartsJSONData" width="800px" height="600px" />
            </div>
          </template>
          <!-- <template #footer="{ item }">
        <div class="footer-container" v-if="item.role === 'ai'">
          <el-icon @click="handleOper('复制', item)"><DocumentCopy /></el-icon>
          <el-icon @click="handleOper('刷新', item)"><Refresh /></el-icon>
          <el-icon @click="handleOper('删除', item)"><Delete /></el-icon>
        </div>
      </template> -->
        </BubbleList>
        <!-- 欢迎页 -->
        <WelcomePage mt30 v-if="showWelcomePage" @question="handleQuestion"></WelcomePage>
        <!-- 底部输入区域 -->
        <div class="chat-input-area" :class="{ collapsed: isHistoryCollapsed }">
          <!-- 输入框区域 -->
          <div class="sender-wrapper" ref="senderWrapperRef">
            <Sender
              ref="refSender"
              class="sender-style"
              v-model="inputValue"
              @submit="handleQuestion(null)"
              @keydown="handleSenderKeydown"
              variant="updown"
              allow-speech
              clearable
              :input-style="{ color: '#1D1D1D', fontSize: '25px' }"
              style="overflow: hidden"
              :loading="submitLoading">
              <template #prefix>
                <div class="tools-btns">
                  <div
                    class="tool-btn"
                    :class="{ selected: selectedTools === item }"
                    v-for="(item, index) in toolsBtns"
                    :key="index"
                    @click="handleSelectTools(item)">
                    {{ item }}
                  </div>
                  <!-- 当有文件时显示文件按钮，点击可切换头部显示状态 -->
                  <div
                    v-if="selectedTools === '知识库' && selectedFiles.length > 0"
                    class="tool-btn files-toggle-btn"
                    :class="{ active: showSenderHeader }"
                    @click="showSenderHeader ? closeSenderHeader() : openSenderHeader()">
                    <el-icon><Document /></el-icon>
                    <span>{{ selectedFiles.length }}个文件</span>
                  </div>
                </div>
              </template>
              <template #header>
                <div class="selected-files-header" v-if="selectedTools === '知识库' && selectedFiles.length > 0">
                  <SelectedFiles ref="selectedFilesRef" :selected-files="selectedFiles" @remove="removeFile" @clear-all="clearAllFiles" />
                </div>
              </template>
              <template #action-list>
                <div class="action-buttons">
                  <el-button round class="action-btn" v-if="selectedTools === '知识库'" @click="handleFiles">
                    <el-icon size="22px" color="#000000"><Files /></el-icon>
                  </el-button>
                  <el-button round class="action-btn" @click="handleClear">
                    <el-icon size="22px" color="#000000"><Delete /></el-icon>
                  </el-button>
                  <el-button round class="action-btn" @click="handleRecord">
                    <el-icon size="22px" color="#000000">
                      <Microphone v-if="!recording" />
                      <Loading v-else />
                    </el-icon>
                  </el-button>
                  <el-button
                    round
                    class="action-btn submit-btn"
                    :loading="submitLoading"
                    v-if="!submitLoading"
                    @click="handleQuestion(null)"
                    :disabled="inputValue.length === 0">
                    <el-icon size="22px" color="#000000"><Promotion /></el-icon>
                  </el-button>
                  <el-button v-else type="danger" circle class="cancel-btn" @click="handleCancel">
                    <el-icon class="is-loading">
                      <Loading />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </Sender>

            <!-- 建议列表悬浮框 -->
            <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-dropdown" ref="suggestionsRef">
              <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                class="suggestion-item"
                :class="{ active: activeSuggestionIndex === index }"
                @click="selectSuggestion(suggestion)"
                @mouseenter="activeSuggestionIndex = index">
                <div class="suggestion-text">{{ suggestion.question || suggestion.title || suggestion.content }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件选择器弹窗 -->
    <!-- 如果需要自定义字段映射，可以添加 :field-config 配置：
     :field-config="{
       id: 'fileId',
       parentId: 'folderId', 
       name: 'fileName',
       leaf: 'isFile',
       path: 'fullPath',
       size: 'fileSize'
     }"
-->
    <FileTreeSelector
      v-if="selectedTools === '知识库'"
      v-model="showFileSelector"
      :selected-file-ids="selectedFiles.map((file) => file.id)"
      :max-count="5"
      :leaf-only="true"
      :request-api="fileRequestApi"
      :init-params="fileRequestParams"
      :max-height="500"
      @confirm="handleFileConfirm"
      :field-config="{
        id: 'id',
        childId: 'knowledgeFileId',
        parentId: 'parentId',
        name: 'name',
        leaf: 'leaf',
        path: 'url',
        size: 'size'
      }" />

    <!-- 折叠状态的展开按钮 - 放在最外层 -->
    <div class="collapsed-expand-btn" v-if="isHistoryCollapsed" @click="toggleHistoryCollapse">
      <el-icon><ArrowRight /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    DocumentCopy,
    Refresh,
    Delete,
    Document,
    Close,
    Search,
    ArrowLeft,
    ArrowRight,
    ChatDotRound,
    MoreFilled,
    Select,
    EditPen
  } from '@element-plus/icons-vue'
  import WelcomePage from './components/WelcomePage/index.vue' // 引入WelcomePage组件 欢迎页
  import { ZnChart } from 'znyg-frontend-common'
  import FileTreeSelector from './components/FileTreeSelector/index.vue' // 引入文件选择器组件
  import SelectedFiles from './components/SelectedFiles/index.vue' // 引入已选择文件展示组件

  import lib from '@/utils/lib'
  import { useAiChat } from './helper'
  import { debounce } from 'lodash-es'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { XMarkdown } from 'vue-element-plus-x'
  import { useImageViewer, type ImageViewerProps } from 'znyg-frontend-common'

  const emits = defineEmits(['close'])

  const refSender = ref(null)
  const selectedFilesRef = ref(null)
  const showSenderHeader = ref(false)
  const senderWrapperRef = ref(null)
  const suggestionsRef = ref(null)

  const toolsBtns = ['业务库', '知识库', 'DeepSeek']
  const selectedTools = ref('业务库')

  // 建议列表相关状态
  const suggestions = ref([])
  const showSuggestions = ref(false)
  const activeSuggestionIndex = ref(-1)
  const isLoadingSuggestions = ref(false)

  const appKey = computed(() => {
    const appKeyMap = {
      业务库: 'chatdb',
      知识库: 'qjgfkf',
      DeepSeek: 'deepseekAndSearch',
      // 控制: 'BimEngineCode2'
      控制: 'BimEngineMCPAgent'
    }
    return appKeyMap[selectedTools.value]
  })
  const {
    inputValue,
    recording,
    submitLoading,
    list,
    currentConversationId,
    isCarryHistory,
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear,
    handleThinkingChange,
    handleFiles,
    // 文件选择相关
    showFileSelector,
    selectedFiles,
    handleFileConfirm,
    removeFile,
    clearAllFiles,
    fileRequestApi,
    fileRequestParams,
    handleQuoteSelect,
    isHistoryCollapsed,
    searchKeyword,
    isSelectMode,
    selectedHistoryItems,
    filteredHistoryGroups,
    getHistoryList,
    toggleHistoryCollapse,
    handleSearchHistory,
    handleSelectHistory,
    handleToolbarCommand,
    handleHistoryItemSelect,
    handleConfirmSelection,
    handleCancelSelection,
    showWelcomePage
  } = useAiChat(appKey)

  const maxHeight = computed(() => {
    return showSenderHeader.value ? 750 : 800
  })

  onMounted(async () => {
    try {
      await getHistoryList()
    } catch (error) {
      console.error('获取历史对话失败:', error)
    }
  })

  const scopeDict = {
    业务库: 'business_library',
    知识库: 'knowledge_library',
    操作库: 'operation_library',
    平台资料: 'platform_material'
  }
  // 防抖查询建议
  const currentKeyword = ref('')
  const debouncedSearchSuggestions = debounce(async (keyword: string) => {
    currentKeyword.value = keyword
    if (!keyword.trim()) {
      suggestions.value = []
      showSuggestions.value = false
      return
    }

    try {
      isLoadingSuggestions.value = true
      const response = await lib.api.questionCompletionApi.list({
        keyword,
        scope: scopeDict[selectedTools.value]
      })

      if (response && response?.result) {
        suggestions.value = Array.isArray(response.result) ? response.result : []
        showSuggestions.value = suggestions.value.length > 0
        activeSuggestionIndex.value = -1 // 重置选中状态
      } else {
        suggestions.value = []
        showSuggestions.value = false
      }
    } catch (error) {
      console.error('获取建议列表失败:', error)
      suggestions.value = []
      showSuggestions.value = false
    } finally {
      isLoadingSuggestions.value = false
    }
  }, 300)

  // 提取关键字的函数
  const extractKeyword = (text: string): string => {
    if (!text) return ''

    // 定义分隔符：空格、逗号、顿号、分号、各种引号
    const separators = [' ', '，', '、', '；', '"', '"', "'", '"', '\u2018', '\u2019']

    // 找到最后一个分隔符的位置
    let lastSeparatorIndex = -1
    for (const separator of separators) {
      const index = text.lastIndexOf(separator)
      if (index > lastSeparatorIndex) {
        lastSeparatorIndex = index
      }
    }

    // 如果找到分隔符，从分隔符后开始截取；否则使用整个文本
    const keyword = lastSeparatorIndex >= 0 ? text.substring(lastSeparatorIndex + 1).trim() : text.trim()

    return keyword
  }

  // 监听输入值变化
  watch(inputValue, (newValue) => {
    if (newValue && newValue.trim()) {
      const keyword = extractKeyword(newValue)
      console.log('keyword', keyword)
      // 只有关键字长度小于6且不为空时才调用接口
      if (keyword && keyword.length < 6) {
        debouncedSearchSuggestions(keyword)
      } else {
        suggestions.value = []
        showSuggestions.value = false
      }
    } else {
      suggestions.value = []
      showSuggestions.value = false
    }
  })

  // 选择建议项
  const selectSuggestion = (suggestion: any) => {
    const text = suggestion.question || ''
    inputValue.value = inputValue.value.substring(0, inputValue.value.length - currentKeyword.value.length)
    inputValue.value += text
    showSuggestions.value = false
    activeSuggestionIndex.value = -1

    nextTick(() => {
      refSender.value?.focus('end')
    })
  }

  // 键盘导航
  const handleKeydown = (event: KeyboardEvent) => {
    // 只在建议列表显示时处理特定按键
    if (!showSuggestions.value || suggestions.value.length === 0) return

    // 检查是否是我们需要处理的按键
    const isNavigationKey = ['ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)
    if (!isNavigationKey) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        event.stopPropagation()
        // 循环选择：到达末尾时回到第一个
        if (activeSuggestionIndex.value >= suggestions.value.length - 1) {
          activeSuggestionIndex.value = 0
        } else {
          activeSuggestionIndex.value += 1
        }
        scrollToActiveItem()
        break
      case 'ArrowUp':
        event.preventDefault()
        event.stopPropagation()
        // 循环选择：到达开头时跳到最后一个
        if (activeSuggestionIndex.value <= 0) {
          activeSuggestionIndex.value = suggestions.value.length - 1
        } else {
          activeSuggestionIndex.value -= 1
        }
        scrollToActiveItem()
        break
      case 'Enter':
        if (activeSuggestionIndex.value >= 0) {
          event.preventDefault()
          event.stopPropagation()
          selectSuggestion(suggestions.value[activeSuggestionIndex.value])
        }
        break
      case 'Escape':
        event.preventDefault()
        event.stopPropagation()
        showSuggestions.value = false
        activeSuggestionIndex.value = -1
        break
    }
  }

  // 滚动到当前选中的建议项
  const scrollToActiveItem = () => {
    nextTick(() => {
      if (suggestionsRef.value && activeSuggestionIndex.value >= 0) {
        const activeItem = suggestionsRef.value.children[activeSuggestionIndex.value]
        if (activeItem) {
          activeItem.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
          })
        }
      }
    })
  }

  // 处理Sender组件的键盘事件
  const handleSenderKeydown = (event: KeyboardEvent) => {
    // 只在建议列表显示时处理导航按键
    if (!showSuggestions.value || suggestions.value.length === 0) return

    const isNavigationKey = ['ArrowDown', 'ArrowUp', 'Enter', 'Escape'].includes(event.key)
    if (!isNavigationKey) return

    // 直接调用键盘导航处理函数
    handleKeydown(event)
  }

  // 点击外部关闭建议列表
  const handleClickOutside = (event: Event) => {
    if (showSuggestions.value && senderWrapperRef.value && !senderWrapperRef.value.contains(event.target as Node)) {
      showSuggestions.value = false
      activeSuggestionIndex.value = -1
    }
  }

  const handleSelectTools = (item) => {
    selectedTools.value = item
    currentConversationId.value = ''
    if (item !== '业务库') {
      //非业务科，则需要携带最后的五条历史记录
      isCarryHistory.value = true
    }
    getHistoryList()
  }

  const handleClose = () => {
    console.log('handleClose')
    emits('close')
  }

  const handleQuestion = (question) => {
    question && (inputValue.value = question)
    // 隐藏建议列表
    showSuggestions.value = false
    activeSuggestionIndex.value = -1
    handleSubmit()
  }

  // 控制Sender头部显示
  const toggleSenderHeader = () => {
    if (selectedTools.value === '知识库' && selectedFiles.value.length > 0) {
      if (!showSenderHeader.value) {
        openSenderHeader()
      }
    } else {
      if (showSenderHeader.value) {
        closeSenderHeader()
      }
    }
  }

  // 监听文件选择状态变化
  watch(
    () => selectedFiles.value.length,
    (newLength) => {
      nextTick(() => {
        toggleSenderHeader()
      })
    }
  )

  // 监听工具切换
  watch(
    () => selectedTools.value,
    () => {
      nextTick(() => {
        toggleSenderHeader()
      })
    }
  )

  // 手动关闭Sender头部
  const closeSenderHeader = () => {
    refSender.value?.closeHeader()
    showSenderHeader.value = false
  }

  // 手动打开Sender头部
  const openSenderHeader = () => {
    refSender.value?.openHeader()
    showSenderHeader.value = true
  }

  onMounted(() => {
    console.log('初始化')
    const divParent = document.getElementById('ai-chat-container')
    const div = divParent?.querySelector('.popWindow') as HTMLElement

    // 添加键盘事件监听（使用capture模式优先捕获）
    document.addEventListener('keydown', handleKeydown, true)
    // 添加点击外部事件监听
    document.addEventListener('click', handleClickOutside)

    // 添加图片点击事件监听
    document.addEventListener('click', handleImageClick)

    // 添加链接点击事件监听
    document.addEventListener('click', handleLinkClick, true)

    // document.addEventListener('mousemove', function (e) {
    //   const x = e.clientX - window.innerWidth / 2
    //   const y = e.clientY - window.innerHeight / 2
    //   const rotationX = (y / window.innerHeight) * -30
    //   const rotationY = (x / window.innerWidth) * 30
    //   div.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`
    // })
    div?.addEventListener('mousemove', (e: MouseEvent) => {
      const rect = div.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // 元素的宽度和高度
      const width = rect.width
      const height = rect.height

      // 确定鼠标在哪个象限/角落
      let rotateX = 0
      let rotateY = 0
      const maxRotation = 5 // 进一步减小最大旋转角度

      // 计算缓冲区域（距离边缘100px）
      const buffer = 100
      const effectiveX = Math.max(buffer, Math.min(x, width - buffer))
      const effectiveY = Math.max(buffer, Math.min(y, height - buffer))

      // 计算每个方向的旋转强度（0-1之间）
      const xStrength = Math.abs((effectiveX / width) * 2 - 1) // 水平方向强度
      const yStrength = Math.abs((effectiveY / height) * 2 - 1) // 垂直方向强度

      // 左上角
      if (effectiveX < width / 2 && effectiveY < height / 2) {
        rotateX = maxRotation * (1 - effectiveY / (height / 2)) // 上边抬起
        rotateY = -maxRotation * (1 - effectiveX / (width / 2)) // 左边抬起
      }
      // 右上角
      else if (effectiveX >= width / 2 && effectiveY < height / 2) {
        rotateX = maxRotation * (1 - effectiveY / (height / 2)) // 上边抬起
        rotateY = maxRotation * ((effectiveX - width / 2) / (width / 2)) // 右边抬起
      }
      // 左下角
      else if (effectiveX < width / 2 && effectiveY >= height / 2) {
        rotateX = maxRotation * ((effectiveY - height / 2) / (height / 2)) // 下边抬起
        rotateY = -maxRotation * (1 - effectiveX / (width / 2)) // 左边抬起
      }
      // 右下角
      else {
        rotateX = maxRotation * ((effectiveY - height / 2) / (height / 2)) // 下边抬起
        rotateY = maxRotation * ((effectiveX - width / 2) / (width / 2)) // 右边抬起
      }

      // 应用平滑过渡
      div.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`
    })
  })

  onUnmounted(() => {
    handleClear()

    // 清理事件监听
    document.removeEventListener('keydown', handleKeydown, true)
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('click', handleImageClick)
    document.removeEventListener('click', handleLinkClick)

    // 取消防抖函数
    debouncedSearchSuggestions.cancel()
    handleImageClick.cancel()
  })

  const handleOper = (type: '复制' | '刷新' | '删除', item: any) => {
    console.log(type, item)
  }

  // 打开弹出式窗口
  const openDialogWindow = (url: string) => {
    const windowFeatures = 'left=100,top=100,width=640,height=520'
    // 打开弹出式窗口
    window.open(url, 'newwindow', windowFeatures)
  }

  const handleQuote = (quote) => {
    console.log(quote)
    openDialogWindow(quote.url)
  }

  // 图片预览相关功能
  const handleImageClick = debounce((event) => {
    const clickedImg = event.target
    if (clickedImg.tagName === 'IMG') {
      // 检查是否在.el-bubble-content内
      const bubbleContent = clickedImg.closest('.el-bubble-content')
      if (!bubbleContent) return

      const imgSrc = clickedImg.src

      // 获取所有.el-bubble-content中的图片
      const bubbleContents = document.querySelectorAll('.el-bubble-content')
      const allImages = []
      let clickedIndex = -1

      bubbleContents.forEach((content) => {
        const images = content.querySelectorAll('img')
        images.forEach((img) => {
          // 只添加有效的图片URL
          if (
            img.src &&
            img.src !==
              'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0LjUgMTQuNUgxLjVWMS41SDE0LjVWMTQuNVoiIHN0cm9rZT0iI0Q5RDlEOSIgc3Ryb2tlLXdpZHRoPSIxIi8+CjxwYXRoIGQ9Ik02LjUgNC41QzcuMzI4NDMgNC41IDggNS4xNzE1NyA4IDZDOCA2LjgyODQzIDcuMzI4NDMgNy41IDYuNSA3LjVDNS42NzE1NyA3LjUgNSA2LjgyODQzIDUgNkM1IDUuMTcxNTcgNS42NzE1NyA0LjUgNi41IDQuNVoiIHN0cm9rZT0iI0Q5RDlEOSIgc3Ryb2tlLXdpZHRoPSIxIi8+CjxwYXRoIGQ9Ik0xMy41IDEyLjVMMTIuNSAxMS41QzEyLjE5NDQgMTEuMTk0NCAxMS43MDU2IDExLjE5NDQgMTEuNDEgMTEuNUw5LjUgMTMuNUw2LjUgMTAuNUM2LjE5NDQ0IDEwLjE5NDQgNS43MDU2IDEwLjE5NDQgNS40MSAxMC41TDIuNSAxMy41IiBzdHJva2U9IiNEOTlEOTkiIHN0cm9rZS13aWR0aD0iMSIvPgo8L3N2Zz4K' &&
            !img.src.includes('data:image/svg+xml')
          ) {
            allImages.push(img.src)
            if (img.src === imgSrc) {
              clickedIndex = allImages.length - 1
            }
          }
        })
      })

      if (allImages.length > 0 && clickedIndex >= 0) {
        console.log('图片预览:', {
          totalImages: allImages.length,
          clickedIndex,
          clickedImage: imgSrc
        })

        const imageViewerProps: ImageViewerProps = {
          urlList: allImages,
          initialIndex: clickedIndex,
          infinite: true,
          hideOnClickModal: true,
          teleported: true,
          zIndex: 2000
        }
        useImageViewer(imageViewerProps)
      } else {
        // 如果没有找到有效图片，显示提示
        ElMessage.warning('没有找到可预览的图片')
      }
    }
  }, 300)

  // 链接处理功能
  const handleLinkClick = (event) => {
    const clickedElement = event.target
    if (clickedElement.tagName === 'A') {
      // 检查是否在.el-bubble-content内
      const bubbleContent = clickedElement.closest('.el-bubble-content')
      if (!bubbleContent) return

      const href = clickedElement.href
      if (!href) return

      // 立即阻止默认行为和事件传播
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 在新窗口中打开链接
      openDialogWindow(href)

      console.log('链接已在新窗口打开:', href)
    }
  }

  // 合并引用文件列表，添加计数和弹窗状态
  const getMergedQuoteList = (quoteList: any[]) => {
    const mergedQuotes: any[] = []
    const quoteMap = new Map()

    quoteList.forEach(quote => {
      const fileName = quote.fileName || quote.document_name || '未知文件'
      const fileId = quote.id || quote.document_id || quote.docId
      
      if (!quoteMap.has(fileName)) {
        quoteMap.set(fileName, {
          id: fileId,
          fileName: fileName,
          url: quote.url,
          count: 1,
          showPopover: false,
          originalQuotes: [quote]
        })
      } else {
        const existing = quoteMap.get(fileName)
        existing.count++
        existing.originalQuotes.push(quote)
      }
    })

    // 按相关度排序（取最高分）
    quoteMap.forEach(mergedQuote => {
      // 按score降序排序原始引用
      mergedQuote.originalQuotes.sort((a, b) => (b.score || 0) - (a.score || 0))
      mergedQuotes.push(mergedQuote)
    })

    return mergedQuotes
  }

  // 鼠标进入引用项时显示弹窗
  const handleQuoteMouseEnter = (mergedQuote: any, event: MouseEvent) => {
    // 所有引用都显示弹窗，不再限制只有多个引用才显示
    mergedQuote.showPopover = true
  }

  // 鼠标离开引用项时隐藏弹窗
  const handleQuoteMouseLeave = (mergedQuote: any) => {
    // 所有引用都隐藏弹窗
    mergedQuote.showPopover = false
  }
</script>

<style scoped lang="scss">
  .ai-chat-contaienr {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 1938px;
    height: 1042px;
    padding: 80px 115px 47px 120px;
    overflow: hidden;

    /* background: rgb(255 255 255 / 70%); */

    /* backdrop-filter: blur(10px); */
    border-radius: 26px;
    box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);

    /* transition: transform 0.1s ease; */

    /* transform-style: preserve-3d; */
  }

  /* 历史对话框样式 */
  .collapsed-layout {
    gap: 0 !important;
  }
  .history-container {
    position: relative;
    width: 434px;
    height: 900px;
    margin-top: 20px;
    background: #f6faff;
    border-radius: 20px;
    transition: all 0.3s ease;
    &.collapsed {
      width: 0 !important;
      height: 0 !important;
      padding: 0 !important;
      margin: 0 !important;
      overflow: visible;
      background: transparent !important;
      border: none !important;
    }
    .history-dialog {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 20px;
      overflow: hidden;
      .history-header {
        display: flex;
        gap: 12px;
        align-items: center;
        margin-bottom: 20px;
      }
      .search-container {
        display: flex;
        flex: 1;
        gap: 8px;
        align-items: center;
      }
      .history-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 4px;
        margin-bottom: 16px;
        border-bottom: 1px solid rgb(0 0 0 / 8%);
      }
      .toolbar-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
      }
      .toolbar-actions {
        display: flex;
        align-items: center;
      }
      .more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        cursor: pointer;
        background: rgb(255 255 255 / 60%);
        border: 1px solid rgb(0 0 0 / 8%);
        border-radius: 6px;
        transition: all 0.3s ease;
        &:hover {
          background: rgb(255 255 255 / 90%);
          border-color: rgb(64 158 255 / 30%);
          transform: translateY(-1px);
        }
        .el-icon {
          font-size: 16px;
          color: #666666;
        }
      }
      :deep(.el-dropdown-menu) {
        .el-dropdown-menu__item {
          display: flex;
          gap: 8px;
          align-items: center;
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

    /* 选择模式下的底部操作按钮 */
    .history-actions {
      position: absolute;
      right: 20px;
      bottom: 20px;
      left: 20px;
      z-index: 10;
      animation: slide-up 0.3s ease-out;
    }
    .actions-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background: linear-gradient(135deg, rgb(255 255 255 / 95%) 0%, rgb(248 250 252 / 90%) 100%);
      backdrop-filter: blur(12px);
      border: 1px solid rgb(64 158 255 / 20%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgb(0 0 0 / 12%), 0 4px 16px rgb(64 158 255 / 8%);
    }
    .select-info {
      display: flex;
      gap: 8px;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
    .select-count {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 24px;
      height: 24px;
      padding: 0 8px;
      font-size: 14px;
      font-weight: 600;
      color: white;
      background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgb(64 158 255 / 30%);
    }
    .actions-buttons {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    @keyframes slide-up {
      0% {
        opacity: 0;
        transform: translateY(20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .history-content {
      flex: 1;
      padding-right: 8px;
      padding-bottom: 100px; /* 为底部操作按钮留出空间 */
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: rgb(0 0 0 / 5%);
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgb(0 0 0 / 20%);
        border-radius: 3px;
        &:hover {
          background: rgb(0 0 0 / 30%);
        }
      }
      .history-group {
        margin-bottom: 24px;
      }
      .group-title {
        padding-left: 4px;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }
      .group-items {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .history-item {
        display: flex;
        gap: 12px;
        align-items: flex-start;
        padding: 14px 16px;
        cursor: pointer;
        background: rgb(255 255 255 / 60%);
        border: 1px solid rgb(0 0 0 / 5%);
        border-radius: 10px;
        transition: all 0.3s ease;
        &:hover {
          background: rgb(255 255 255 / 90%);
          border-color: rgb(64 158 255 / 30%);
          box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
          transform: translateY(-1px);
        }
      }
      .history-checkbox {
        flex-shrink: 0;
        margin-top: 2px;
      }
      .item-title {
        display: -webkit-box;
        flex: 1;
        overflow: hidden;
        font-size: 22px;
        font-weight: 500;
        line-height: 1.5;
        color: #333333;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        &.with-checkbox {
          flex: 1;
          min-width: 0;
        }
      }
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #999999;
      }
      .empty-icon {
        margin-bottom: 16px;
        opacity: 0.6;
      }
      .empty-text {
        font-size: 16px;
      }
    }
  }

  /* 折叠状态的展开按钮 - 最外层 */
  .collapsed-expand-btn {
    position: fixed;
    top: 80px;
    left: 120px;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    cursor: pointer;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(64 158 255 / 20%);
    border-radius: 50%;
    box-shadow: 0 4px 16px rgb(0 0 0 / 12%), 0 2px 8px rgb(0 0 0 / 8%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    &:hover {
      background: rgb(255 255 255 / 100%);
      border-color: rgb(64 158 255 / 40%);
      box-shadow: 0 8px 24px rgb(0 0 0 / 18%), 0 4px 12px rgb(64 158 255 / 15%);
      transform: scale(1.15);
      .el-icon {
        color: #337ecc;
        transform: translateX(2px);
      }
    }
    .el-icon {
      font-size: 22px;
      color: #409eff;
      transition: all 0.3s ease;
    }
  }

  /* 底部输入区域 */
  .chat-input-area {
    position: absolute;
    right: 120px;
    bottom: 40px;
    left: 610px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow: visible;
    transition: all 0.3s ease;
    &.collapsed {
      left: 120px;
    }
  }

  /* 当有文件选择且为收起状态时，为指示器预留空间 */
  .chat-input-area:has(.collapsed-indicator) {
    right: 190px;
  }

  /* 已选择文件区域 */
  .selected-files-header {
    position: relative;
    padding: 8px 12px;
    margin-bottom: 0;
    animation: slide-down 0.3s ease-out;
  }

  /* 在Sender头部时的特殊样式 */
  .selected-files-header :deep(.files-expanded) {
    padding: 0;
    margin-bottom: 0;
    background: transparent;
    backdrop-filter: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
  .selected-files-header :deep(.files-count) {
    font-size: 15px;
    font-weight: 600;
    color: #409eff;
  }
  .selected-files-header :deep(.files-header) {
    padding: 8px 0;
    margin-bottom: 12px;
    border-bottom: 1px solid rgb(0 0 0 / 10%);
  }
  .selected-files-header :deep(.files-list) {
    max-height: 120px;
    padding: 0;
  }
  .selected-files-header :deep(.file-card) {
    max-width: 240px;
    padding: 10px 14px;
    background: rgb(255 255 255 / 95%);
    border: 1px solid rgb(0 0 0 / 10%);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
    transition: all 0.2s ease;
  }
  .selected-files-header :deep(.file-card:hover) {
    background: rgb(255 255 255 / 100%);
    border-color: rgb(64 158 255 / 30%);
    box-shadow: 0 4px 12px rgb(64 158 255 / 15%);
    transform: translateY(-1px);
  }
  .selected-files-header :deep(.file-name) {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .selected-files-header :deep(.clear-all-btn) {
    padding: 4px 10px;
    font-size: 13px;
    border-radius: 6px;
  }
  .selected-files-header :deep(.collapse-btn) {
    padding: 4px 10px;
    font-size: 13px;
    border-radius: 6px;
  }

  @keyframes slide-down {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 输入框包装器 */
  .sender-wrapper {
    position: relative;
  }

  /* 输入框样式调整 */
  .sender-style {
    position: relative;
  }

  /* 建议列表悬浮框 */
  .suggestions-dropdown {
    position: absolute;
    right: 0;
    bottom: 120px;
    left: 0;
    z-index: 1000;
    max-height: 300px;
    margin-top: 4px;
    overflow-x: hidden;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(226 232 240 / 60%);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgb(0 0 0 / 15%), 0 4px 10px rgb(0 0 0 / 10%);
    animation: fade-in-up 0.2s ease-out;
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid rgb(241 245 249 / 50%);
    transition: all 0.2s ease;
  }
  .suggestion-item:last-child {
    border-bottom: none;
  }
  .suggestion-item:hover,
  .suggestion-item.active {
    padding-left: 13px;
    background: rgb(59 130 246 / 12%);
    border-left: 3px solid #3b82f6;
    transform: translateX(2px);
  }
  .suggestion-text {
    overflow: hidden;
    font-size: 18px;
    line-height: 1.5;
    color: #374151;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 工具按钮样式 */
  .tools-btns {
    display: flex;
    gap: 10px;
    font-size: 20px;
    .tool-btn {
      padding: 8px 16px;
      font-size: 20px;
      color: #333333;
      white-space: nowrap;
      cursor: pointer;
      background: transparent;
      border: 1px solid #333333;
      border-radius: 8px;
      transition: all 0.3s ease;
      &:hover {
        background: #f5f5f5;
        transform: translateY(-1px);
      }
      &.selected {
        font-weight: 600;
        color: #0e1df0;
        background: rgb(14 29 240 / 10%);
        border-color: #0e1df0;
      }
      &.open-files-btn {
        display: flex;
        gap: 6px;
        align-items: center;
        padding: 10px 16px;
        font-size: 16px;
        font-weight: 500;
        color: #409eff;
        background: rgb(64 158 255 / 8%);
        border-color: rgb(64 158 255 / 30%);
        border-radius: 10px;
        box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
        transition: all 0.3s ease;
        &:hover {
          color: #409eff;
          background: rgb(64 158 255 / 15%);
          border-color: rgb(64 158 255 / 50%);
          box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
          transform: translateY(-1px);
        }
        .el-icon {
          font-size: 16px;
        }
        span {
          font-size: 15px;
        }
      }

      /* 文件切换按钮样式 */
      .files-toggle-btn {
        display: flex;
        gap: 6px;
        align-items: center;
        padding: 8px 12px;
        font-size: 14px;
        font-weight: 500;
        color: #409eff;
        cursor: pointer;
        background: rgb(64 158 255 / 10%);
        border: 1px solid rgb(64 158 255 / 30%);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgb(64 158 255 / 10%);
        transition: all 0.3s ease;
        &:hover {
          background: rgb(64 158 255 / 15%);
          border-color: rgb(64 158 255 / 50%);
          box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
          transform: translateY(-1px);
        }
        &.active {
          background: rgb(64 158 255 / 20%);
          border-color: #409eff;
          box-shadow: 0 2px 8px rgb(64 158 255 / 30%);
        }
        .el-icon {
          font-size: 16px;
        }
        span {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .active {
      color: #67c23a;
      background: rgb(103 194 58 / 15%);
      border: 1px solid rgb(103 194 58 / 40%);
      box-shadow: 0 2px 8px rgb(103 194 58 / 20%);
      &:hover {
        background: rgb(103 194 58 / 20%);
      }
    }
  }

  /* 操作按钮区域 */
  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    .action-btn {
      background: rgb(255 255 255 / 74%);
      border: 1px solid rgb(255 255 255 / 20%);
      transition: all 0.3s ease;
      &:hover {
        background: rgb(255 255 255 / 90%);
        box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
        transform: translateY(-1px);
      }
      &.submit-btn:not(:disabled) {
        background: rgb(14 29 240 / 10%);
        border-color: #0e1df0;
        &:hover {
          background: rgb(14 29 240 / 20%);
        }
      }
      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
        transform: none;
      }
    }
    .cancel-btn {
      background: #ff4757;
      border-color: #ff4757;
      &:hover {
        background: #ff3742;
      }
    }
  }

  /* 引用列表样式 */
  .quote-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 16px;
  }
  .quote-item {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: flex-start;
    padding: 10px 14px;
    overflow: hidden;
    background: linear-gradient(135deg, rgb(255 255 255 / 90%) 0%, rgb(248 250 252 / 80%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(226 232 240 / 60%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%), 0 1px 3px rgb(0 0 0 / 10%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .quote-item-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    width: 100%;
  }
  .quote-item::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 3px;
    content: '';
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .quote-item:hover {
    border-color: rgb(102 126 234 / 30%);
    box-shadow: 0 8px 25px rgb(0 0 0 / 12%), 0 4px 10px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .quote-item:hover::before {
    opacity: 1;
  }
  .quote-item-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgb(102 126 234 / 30%);
    transition: all 0.3s ease;
  }
  .quote-item:hover .quote-item-icon {
    box-shadow: 0 4px 12px rgb(102 126 234 / 40%);
    transform: scale(1.05);
  }
  .quote-checkbox {
    flex-shrink: 0;
    margin-top: 5px;
  }
  .quote-item-content {
    flex: 1;
    min-width: 0;
    cursor: pointer;
  }
  .quote-item-title {
    display: -webkit-box;
    overflow: hidden;
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: #1e293b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .quote-count {
    display: inline-block;
    padding: 2px 6px;
    margin-left: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    background: rgb(102 126 234 / 10%);
    border-radius: 4px;
  }
  .quote-item-text {
    display: -webkit-box;
    overflow: hidden;
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* 引用详情弹窗样式 */
  :deep(.quote-detail-popover) {
    .el-popover {
      padding: 0;
      background: rgb(255 255 255 / 95%);
      backdrop-filter: blur(12px);
      border: 1px solid rgb(226 232 240 / 60%);
      border-radius: 12px;
      box-shadow: 0 10px 25px rgb(0 0 0 / 15%), 0 4px 10px rgb(0 0 0 / 10%);
    }
  }
  .quote-detail-content {
    padding: 16px;
  }
  .quote-detail-header {
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid rgb(226 232 240 / 60%);
  }
  .quote-detail-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }
  .quote-detail-count {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #667eea;
    background: rgb(102 126 234 / 10%);
    border-radius: 6px;
  }
  .quote-detail-list {
    max-height: 300px;
    overflow-y: auto;
  }
  .quote-detail-item {
    padding: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    background: rgb(248 250 252 / 60%);
    border: 1px solid rgb(226 232 240 / 40%);
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  .quote-detail-item:hover {
    background: rgb(255 255 255 / 100%);
    border-color: rgb(102 126 234 / 30%);
    box-shadow: 0 2px 8px rgb(102 126 234 / 15%);
  }
  .quote-detail-item:last-child {
    margin-bottom: 0;
  }
  .quote-detail-item-header {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;
  }
  .quote-detail-item-index {
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
  }
  .quote-detail-item-score {
    font-size: 12px;
    color: #64748b;
  }
  .quote-detail-item-content {
    font-size: 14px;
    line-height: 1.5;
    color: #475569;
  }

  /* 响应式设计 */
  @media (width <= 768px) {
    .quote-list {
      grid-template-columns: 1fr;
    }
    .quote-item {
      gap: 10px;
      padding: 12px;
    }
    .quote-item-icon {
      width: 36px;
      height: 36px;
    }
    .quote-item-title {
      font-size: 16px;
    }
    .quote-item-text {
      font-size: 14px;
    }
  }
</style>
<style lang="scss">
  .ai-chat-contaienr {
    .echarts-container {
      background: #ffffff;
    }
    .el-bubble-content {
      font-size: 25px !important;
      line-height: 36px !important;
      color: #242424 !important;
    }
    .el-bubble-end {
      --el-fill-color: #9fc6ff;
    }
    .sender-style {
      background: rgb(255 255 255 / 40%);

      // border: 1px solid rgb(112 112 112 / 50%);
      border-radius: 20px;
      box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);
      .el-sender {
        border: none !important;
      }
      .el-textarea__inner::placeholder {
        color: #333333;
      }
    }
  }
  .thinking-chain-warp {
    margin-bottom: 20px !important;
    .content pre,
    .label {
      font-size: 18px;
    }
  }
</style>

<style lang="scss">
  /* 下拉菜单危险选项样式 - 全局样式 */
  .el-dropdown-menu .el-dropdown-menu__item.danger-item {
    color: #f56565 !important;
    &:hover {
      color: #e53e3e !important;
      background-color: #fed7d7 !important;
    }
    .el-icon {
      color: #f56565 !important;
    }
  }
  .el-bubble-content {
    img {
      position: relative;
      max-width: 1323px;
      cursor: pointer;
      border-radius: 8px;
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
        transform: scale(1.001);
        &::after {
          position: absolute;
          top: 8px;
          right: 8px;
          padding: 4px 6px;
          font-size: 12px;
          color: white;
          pointer-events: none;
          content: '🔍';
          background: rgb(0 0 0 / 60%);
          border-radius: 4px;
        }
      }
    }

    // 外部链接样式
    a {
      position: relative;
      color: #1890ff;
      text-decoration: none;
      transition: all 0.3s ease;
      &:hover {
        color: #40a9ff;
        text-decoration: underline;
        &::after {
          position: absolute;
          top: -2px;
          right: -16px;
          font-size: 12px;
          color: #40a9ff;
          content: '↗';
          opacity: 0.8;
        }
      }
    }
  }
</style>
