/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Cartesian3-bb0e6278","./defined-3b3eb2ba","./EllipseGeometry-fee2055c","./Rectangle-9bffefe4","./Math-b5f4d889","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./EllipseGeometryLibrary-07d021fe","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryOffsetAttribute-5a4c2801","./GeometryPipeline-9dbd2054","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11","./VertexFormat-86c096b8"],(function(e,t,r,n,a,i,o,d,s,b,c,f,l,m,p,y,u,G,E,C,_,A,I){"use strict";return function(a,i){return t.defined(i)&&(a=r.EllipseGeometry.unpack(a,i)),a._center=e.Cartesian3.clone(a._center),a._ellipsoid=n.Ellipsoid.clone(a._ellipsoid),r.EllipseGeometry.createGeometry(a)}}));
