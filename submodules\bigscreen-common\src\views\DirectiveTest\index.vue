<!--
 * @Author: ya<PERSON>hen <EMAIL>
 * @Date: 2023-09-26 11:29:01
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2024-03-12 11:00:25
 * @FilePath: \bigscreen-common\src\views\DirectiveTest\index.vue
 * @Description: 指令测试页面
 *
-->
<template>
  <div class="directive-test bg-amber">
    <!-- v-tooltip不传参的情况 -->
    <ul class="text-list bg-cyan">
      <li class="value" v-for="(item, index) in state.textList" :key="index" v-tooltip>{{ item }}</li>
    </ul>
    <!-- v-tooltip传参的情况（参数与el-tooltip的属性一致） -->
    <ul class="text-list bg-red">
      <li
        class="value"
        v-for="(item, index) in state.textList"
        :key="index"
        v-tooltip="{ content: index + item, effect: 'light', popperClass: 'test-max-width' }">
        {{ item }}
      </li>
    </ul>
    <!-- v-drag拖拽 -->
    <!--  <div class="drag-box" v-drag>拖拽</div> -->
    <!-- v-drag-fixed-range拖拽 -->
    <!--  <div class="range-drag-box" v-drag-fixed-range="{ parentClass: 'directive-test' }">只能在固定范围内拖拽</div> -->
    <ul class="custom-table" v-auto-scroll.mouse="1">
      <li v-for="(person, index) in state.people" :key="index">
        <span>{{ person.name }}</span>
        <span>{{ person.age }}</span>
        <span>{{ person.gender }}</span>
      </li>
    </ul>
    <!-- v-iscroll（当前示例为不需要传参的情况，默认横向拖动） -->
    <div class="relative h250 w400 flex overflow-auto" v-iscroll>
      <ul class="absolute flex bg-emerald">
        <li class="h-200 w-200 border-1 border-black border-solid" v-for="(item, index) in state.textList" :key="index">{{ item }}</li>
      </ul>
    </div>
    <!-- v-iscroll（当前示例为传参的情况） -->
    <div class="relative h250 w400 overflow-hidden" v-iscroll="{ option: { scrollX: true, scrollbars: true } }">
      <ul class="absolute display-flex bg-emerald">
        <li class="h-200 w-200 border-1 border-black border-solid" v-for="(item, index) in state.textList" :key="index">{{ item }}</li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DirectiveTest'
  }
</script>
<script setup>
  import { reactive } from 'vue'
  // import { getCurrentInstance } from 'vue'
  // import { setComponents } from '@Common/utils'
  // const modulesFiles = {
  //   ...import.meta.glob('./components/*/index.vue', { eager: true }),
  //   ...import.meta.glob('./components/*.vue', { eager: true })
  // }
  // setComponents(modulesFiles, getCurrentInstance())
  const state = reactive({
    textList: ['所属公司', '平台运行总负责', '平台负责人电话', '区域应急负责人', '区域负责人电话', '当日工作人数'],
    people: [
      { name: '1', age: 18, gender: '男' },
      { name: '2', age: 19, gender: '女' },
      { name: '3', age: 20, gender: '男' },
      { name: '4', age: 18, gender: '男' },
      { name: '5', age: 19, gender: '女' },
      { name: '6', age: 20, gender: '男' },
      { name: '7', age: 18, gender: '男' },
      { name: '8', age: 19, gender: '女' },
      { name: '9', age: 20, gender: '男' },
      { name: '10', age: 21, gender: '女' }
    ]
  })
  setTimeout(() => {
    state.textList = [
      '平台运行总负责',
      '所属公司',
      '所属公司',
      '所属公司',
      '所属公司',
      '区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话区域负责人电话'
    ]
    state.people.push(
      { name: '11', age: 18, gender: '男' },
      { name: '12', age: 19, gender: '女' },
      { name: '13', age: 20, gender: '男' },
      { name: '14', age: 18, gender: '男' },
      { name: '15', age: 19, gender: '女' },
      { name: '16', age: 20, gender: '男' },
      { name: '17', age: 18, gender: '男' },
      { name: '18', age: 19, gender: '女' },
      { name: '19', age: 20, gender: '男' },
      { name: '20', age: 21, gender: '女' }
    )
  }, 2000)
</script>
<style lang="scss" scoped>
  * {
    flex-shrink: 0;
  }
  .directive-test {
    // height: 1000px;
    position: relative;
    width: 1800px;
    user-select: none;
    .text-list {
      overflow: hidden;
      .value {
        width: 100px;
        margin: 20px;
      }
    }
    .drag-box {
      position: absolute;
      top: 550px;
      left: 100px;
      width: 100px;
      height: 100px;
      background-color: bisque;
    }
    .range-drag-box {
      position: absolute;
      top: 780px;
      left: 100px;
      width: 100px;
      height: 100px;
      background-color: burlywood;
    }
    .custom-table {
      width: 500px;
      height: 200px;
      margin-left: 500px;
      overflow: auto;
      li {
        display: flex;
        justify-content: space-between;
        height: 50px;
        border-top: 1px solid black;
      }
      & > li:last-child {
        border-bottom: 1px solid black;
      }
    }
  }
</style>
<style>
  .test-max-width {
    max-width: 200px;
  }
</style>
