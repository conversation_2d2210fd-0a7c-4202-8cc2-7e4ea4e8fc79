<template>
  <div>
    <Mychart :option="options" width="390px" height="280px"></Mychart>
  </div>
</template>

<script setup>
  import * as echarts from 'echarts'

  import Mychart from '@Common/components/MyChart/index.vue'

  const configList = ref([])
  const options = ref({})

  const initChart = (dataX, list, deviceExt, dataYList, type) => {
    const option = {
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed'
          }
        }
        // formatter: function(params) {
        //   console.log(params)
        //   return params[0]
        // }
      },
      grid: {
        left: '5%',
        top: '5%',
        right: '5%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dataX,
        axisLabel: {
          fontSize: 13,
          color: '#DBF4FF',
          formatter: function (value, index) {
            return value.replace(' ', '\n')
          }
        },
        axisTick: false,
        axisLine: {
          lineStyle: {
            color: 'rgba(51, 165, 246, 0.47)',
            width: 2
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 13,
          color: '#DBF4FF'
          // formatter: (val) => {
          //   for (let i = 0; i < dataYList.length; i += 1) {
          //     if (dataYList[i].value * 1 === val) {
          //       return dataYList[i].name;
          //     }
          //   }
          //   return val;
          // }
        },
        axisTick: false,
        axisLine: false,
        splitLine: {
          lineStyle: {
            color: 'rgba(159, 162, 166, 0.5)'
          }
        }
      },
      series: [
        {
          data: list,
          type: 'line',
          symbol: 'circle',
          itemStyle: {
            color: 'rgba(0, 209, 255, 0.60)',
            borderColor: '#00D1FF',
            borderWidth: 1
          },
          lineStyle: {
            color: 'rgba(0, 209, 255, 0.60)',
            width: 0
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 209, 255, 0.60)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(0, 209, 255, 0)' // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          }
        }
      ]
    }

    configList.value = dataYList || []
    if (configList.value.length > 0) {
      option.yAxis.axisLabel = {
        fontSize: 13,
        color: 'rgba(255, 255, 255, 0.61)',
        formatter: (value) => {
          let ret = ''
          const configItem = configList.value.find((_) => `${_.value}` === `${value}`)
          if (configItem) {
            ret = configItem.name
          }

          return ret
        }
      }
    }
    option.series[0].markLine = null
    if (
      deviceExt &&
      ((deviceExt.waterLevel1 !== undefined && deviceExt.waterLevel1 !== '') || (deviceExt.waterLevel2 !== undefined && deviceExt.waterLevel2 !== ''))
    ) {
      // 液位计  添加第一起泵点和第二起泵点
      const markLine = {
        lineStyle: {
          normal: {
            color: '#007cfa'
          }
        },
        symbolSize: [0, 0],
        data: []
      }
      if (deviceExt.waterLevel1 !== undefined && deviceExt.waterLevel1 !== '') {
        markLine.data.push({ yAxis: parseFloat(deviceExt.waterLevel1), label: { formatter: '第一起泵点', color: '#8b929a' } })
        option.grid.right = '80px'
      }
      if (deviceExt.waterLevel2 !== undefined && deviceExt.waterLevel2 !== '') {
        markLine.data.push({ yAxis: parseFloat(deviceExt.waterLevel2), label: { formatter: '第二起泵点', color: '#8b929a' } })
        option.grid.right = '80px'
      }
      option.series[0].markLine = markLine
    }
    option.series[0].step = type === 'status' ? 'end' : false
    console.log(dataYList, option, 'option ----------------------------------------------------------------')

    options.value = option
  }
  defineExpose({ initChart })
</script>

<style lang="scss" scoped></style>
