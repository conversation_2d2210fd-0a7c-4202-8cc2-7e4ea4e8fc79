# CanvasDragHandler 使用指南

`CanvasDragHandler` 是一个封装了 Canvas 元素拖拽到 DOM 区域功能的 TypeScript 类，可以方便地集成到现有的 Fabric.js 项目中。

## 特性

- ✅ **非侵入式设计**: 不影响现有 Canvas 的其他功能
- ✅ **高度可配置**: 支持自定义拖拽条件、样式、回调等
- ✅ **TypeScript 支持**: 完整的类型定义
- ✅ **事件驱动**: 丰富的生命周期回调
- ✅ **灵活的目标区域**: 支持 class 选择器和 data 属性
- ✅ **调试模式**: 可选的详细日志输出

## 快速开始

### 1. 基础用法

```typescript
import { CanvasDragHandler, injectDragStyles } from '@/utils/CanvasDragHandler'

// 创建拖拽处理器
const dragHandler = new CanvasDragHandler({
  canvas: fabricCanvasInstance,
  dropZoneSelector: '.drop-zone',
  onDrop: (data, zoneId, zoneElement) => {
    console.log('元素放置成功:', data, zoneId)
  }
})

// 注入必要的CSS样式
injectDragStyles()
```

### 2. 完整配置示例

```typescript
const dragHandler = new CanvasDragHandler({
  // 必需参数
  canvas: fabricCanvas,
  dropZoneSelector: '.drop-zone',

  // 可选配置
  canDrag: (obj: fabric.Object) => {
    // 自定义拖拽条件
    return !!(obj as any).isCamera
  },

  getCustomData: (obj: fabric.Object) => {
    // 自定义数据提取
    return (
      (obj as any).customData || {
        id: `object-${Date.now()}`,
        type: obj.type
      }
    )
  },

  onDrop: (data, zoneId, zoneElement) => {
    // 拖拽成功回调
    console.log('Drop successful:', { data, zoneId })
  },

  onDragStart: (obj, data) => {
    // 拖拽开始回调
    console.log('Drag started:', obj.type)
  },

  onDragEnd: (success, data, zoneId) => {
    // 拖拽结束回调
    console.log('Drag ended:', { success, zoneId })
  },

  onZoneChange: (zoneId) => {
    // 实时区域变化回调（用于更新UI状态）
    activeZone.value = zoneId || ''
  },

  previewStyle: {
    // 自定义预览样式
    borderRadius: '12px',
    border: '3px solid #007bff'
  },

  debug: true // 启用调试日志
})
```

## 配置选项

### DragOptions 接口

| 属性               | 类型                                | 必需 | 默认值               | 说明                  |
| ------------------ | ----------------------------------- | ---- | -------------------- | --------------------- |
| `canvas`           | `fabric.Canvas`                     | ✅   | -                    | Fabric.js Canvas 实例 |
| `dropZoneSelector` | `string`                            | ✅   | -                    | 目标区域 CSS 选择器   |
| `canDrag`          | `(obj: fabric.Object) => boolean`   | ❌   | 检查`isCamera`属性   | 拖拽条件检查函数      |
| `onDrop`           | `(data, zoneId, element) => void`   | ❌   | 控制台输出           | 拖拽成功回调          |
| `onDragStart`      | `(obj, data) => void`               | ❌   | 空函数               | 拖拽开始回调          |
| `onDragEnd`        | `(success, data?, zoneId?) => void` | ❌   | 空函数               | 拖拽结束回调          |
| `onZoneChange`     | `(zoneId: string \| null) => void`  | ❌   | 空函数               | 区域变化回调（实时）  |
| `getCustomData`    | `(obj: fabric.Object) => any`       | ❌   | 获取`customData`属性 | 自定义数据提取函数    |
| `previewStyle`     | `Partial<CSSStyleDeclaration>`      | ❌   | `{}`                 | 预览元素样式配置      |
| `debug`            | `boolean`                           | ❌   | `false`              | 是否启用调试日志      |

## 目标区域配置

### 方式 1: 使用 data-zone-id 属性（推荐）

```html
<div class="drop-zone" data-zone-id="project-zone">项目区域</div>

<div class="drop-zone" data-zone-id="workspace-zone">工作区域</div>
```

### 方式 2: 使用 CSS 类名

```html
<div class="drop-zone zone-1">区域1</div>

<div class="drop-zone zone-2">区域2</div>
```

## API 方法

### 公共方法

```typescript
// 设置拖拽条件函数
dragHandler.setCanDragFunction((obj: fabric.Object) => {
  return (obj as any).isActive
})

// 设置Drop处理器
dragHandler.setDropHandler((data, zoneId, element) => {
  console.log('New drop handler:', data)
})

// 启用/禁用拖拽功能
dragHandler.setEnabled(false) // 禁用
dragHandler.setEnabled(true) // 启用

// 销毁处理器（清理事件监听器）
dragHandler.destroy()
```

## 实时视觉反馈

### 目标区域高亮

使用 `onZoneChange` 回调实现拖拽时的实时区域高亮：

```typescript
// Vue 3 示例
const activeZone = ref('')

const dragHandler = new CanvasDragHandler({
  // ...其他配置
  onZoneChange: (zoneId: string | null) => {
    // 实时更新当前活动区域
    activeZone.value = zoneId || ''
  }
})
```

```html
<!-- 模板中使用 activeZone 来控制样式 -->
<div class="drop-zone" data-zone-id="zone-1" :class="{ active: activeZone === 'zone-1' }">目标区域 1</div>
```

```css
/* CSS样式 */
.drop-zone {
  border: 3px dashed #ccc;
  transition: all 0.3s ease;
}

.drop-zone.active {
  border-color: #007bff;
  background-color: #e3f2fd;
  transform: scale(1.02);
}
```

## 在现有项目中集成

### 1. 保护现有功能

```typescript
// 现有的Canvas配置和功能
const canvas = new fabric.Canvas('canvas', {
  selection: true,
  preserveObjectStacking: true
  // ... 其他现有配置
})

// 现有的事件监听器继续工作
canvas.on('selection:created', handleSelection)
canvas.on('object:modified', handleModification)

// 添加拖拽功能（不影响现有功能）
const dragHandler = new CanvasDragHandler({
  canvas,
  dropZoneSelector: '.my-drop-zones',
  onDrop: handleElementDrop
})
```

### 2. 条件启用拖拽

```typescript
// 根据用户权限或模式启用拖拽
const dragHandler = new CanvasDragHandler({
  canvas,
  dropZoneSelector: '.drop-zone',
  canDrag: (obj) => {
    // 只有在编辑模式下且用户有权限时才能拖拽
    return editMode && userHasPermission && (obj as any).isDraggable
  }
})
```

### 3. 与 Vue/React 集成

```typescript
// Vue 3 组合式API示例
import { onMounted, onBeforeUnmount } from 'vue'

export default {
  setup() {
    let dragHandler: CanvasDragHandler | null = null

    onMounted(() => {
      dragHandler = new CanvasDragHandler({
        // 配置选项
      })
      injectDragStyles()
    })

    onBeforeUnmount(() => {
      dragHandler?.destroy()
    })

    return {
      // 组件数据和方法
    }
  }
}
```

## 样式自定义

### 1. 预览元素样式

```typescript
const dragHandler = new CanvasDragHandler({
  // ...其他配置
  previewStyle: {
    backgroundColor: '#4CAF50',
    borderRadius: '16px',
    border: '3px solid white',
    fontSize: '16px',
    fontWeight: 'bold',
    transform: 'rotate(0deg) scale(1.1)'
  }
})
```

### 2. 全局样式覆盖

```css
/* 覆盖预览元素样式 */
.canvas-drag-preview {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

/* 自定义拖拽时的全局样式 */
body.canvas-dragging {
  background-color: #f0f8ff;
}
```

## 调试和故障排除

### 1. 启用调试模式

```typescript
const dragHandler = new CanvasDragHandler({
  // ...
  debug: true
})
```

### 2. 常见问题

**Q: 元素无法拖拽？**

- 检查`canDrag`函数是否返回`true`
- 确认元素已被选中（`canvas.getActiveObject()`）
- 查看调试日志确认拖拽条件

**Q: 目标区域无法识别？**

- 确认`dropZoneSelector`选择器正确
- 检查目标元素是否有正确的 class 或 data 属性
- 确认目标元素在 DOM 中存在

**Q: 拖拽预览元素不显示？**

- 确认已调用`injectDragStyles()`
- 检查 z-index 层级问题
- 查看控制台是否有 CSS 错误

## 性能优化

### 1. 减少事件频率

```typescript
// 对于大量元素的Canvas，考虑添加防抖
const dragHandler = new CanvasDragHandler({
  // ...
  onDragStart: debounce((obj, data) => {
    // 处理逻辑
  }, 100)
})
```

### 2. 清理资源

```typescript
// 组件销毁时务必调用destroy方法
onBeforeUnmount(() => {
  dragHandler?.destroy()
})
```

## 完整示例

参考 `src/examples/DragHandlerExample.vue` 文件查看完整的使用示例。

## 许可证

MIT License
