import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import EventHelper from"../../Core/EventHelper.js";import OrthographicFrustum from"../../Core/OrthographicFrustum.js";import SceneMode from"../../Scene/SceneMode.js";import knockout from"../../ThirdParty/knockout.js";import createCommand from"../createCommand.js";function ProjectionPickerViewModel(e){if(!defined(e))throw new DeveloperError("scene is required.");this._scene=e,this._orthographic=e.camera.frustum instanceof OrthographicFrustum,this._flightInProgress=!1,this.dropDownVisible=!1,this.tooltipPerspective="Perspective Projection",this.tooltipOrthographic="Orthographic Projection",this.selectedTooltip=void 0,this.sceneMode=e.mode,knockout.track(this,["_orthographic","_flightInProgress","sceneMode","dropDownVisible","tooltipPerspective","tooltipOrthographic"]);var o=this;knockout.defineProperty(this,"selectedTooltip",(function(){return o._orthographic?o.tooltipOrthographic:o.tooltipPerspective})),this._toggleDropDown=createCommand((function(){o.sceneMode===SceneMode.SCENE2D||o._flightInProgress||(o.dropDownVisible=!o.dropDownVisible)})),this._eventHelper=new EventHelper,this._eventHelper.add(e.morphComplete,(function(e,t,r,i){o.sceneMode=r,o._orthographic=r===SceneMode.SCENE2D||o._scene.camera.frustum instanceof OrthographicFrustum})),this._eventHelper.add(e.preRender,(function(){o._flightInProgress=defined(e.camera._currentFlight)})),this._switchToPerspective=createCommand((function(){o.sceneMode!==SceneMode.SCENE2D&&(o._scene.camera.switchToPerspectiveFrustum(),o._orthographic=!1,o.dropDownVisible=!1)})),this._switchToOrthographic=createCommand((function(){o.sceneMode!==SceneMode.SCENE2D&&(o._scene.camera.switchToOrthographicFrustum(),o._orthographic=!0,o.dropDownVisible=!1)})),this._sceneMode=SceneMode}Object.defineProperties(ProjectionPickerViewModel.prototype,{scene:{get:function(){return this._scene}},toggleDropDown:{get:function(){return this._toggleDropDown}},switchToPerspective:{get:function(){return this._switchToPerspective}},switchToOrthographic:{get:function(){return this._switchToOrthographic}},isOrthographicProjection:{get:function(){return this._orthographic}}}),ProjectionPickerViewModel.prototype.isDestroyed=function(){return!1},ProjectionPickerViewModel.prototype.destroy=function(){this._eventHelper.removeAll(),destroyObject(this)};export default ProjectionPickerViewModel;