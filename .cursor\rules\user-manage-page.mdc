---
description: 
globs: 
alwaysApply: false
---
# 用户管理页面开发规范

## 页面概述

用户管理页面是一个标准的增删改查(CRUD)列表页面，位于 [src/views/userManage](mdc:src/views/userManage) 目录。该页面遵循项目的标准架构模式，表格使用ZnTable组件，表单使用ZnForm组件。

## 目录结构规范

```
userManage/
├── index.vue                    # 主页面入口文件
├── helper/
│   └── index.tsx               # 表格配置和数据处理
└── components/
    └── UserForm/               # 用户表单组件目录
        ├── index.vue           # 表单组件入口
        └── helper/
            └── index.tsx       # 表单配置和验证规则
```

## 主页面规范 ([index.vue](mdc:src/views/userManage/index.vue))

### 1. 组件结构

主页面必须包含以下核心元素：

- **ZnTable组件**：用于数据展示和操作
- **表格头部插槽(tableHeader)**：包含添加按钮等操作
- **操作按钮事件处理**：统一的按钮点击事件分发
- **弹框组件**：使用 `useDialogV2` 处理表单弹框

### 2. 模板结构

**重要规范**：每个组件的最外层必须有一个具有语义化的容器class，该class应该作为CSS样式的最顶层class。

```vue
<template>
  <div class="user-manage-container table-container">
    <ZnTable 
      ref="refZnTable" 
      :request-api="requestApi" 
      :columns="columns" 
      :data-callback="dataCallback"
      @handle-operate-btns-click="handleOperateBtnsClick"
    >
      <!-- 表格头部插槽 -->
      <template #tableHeader="scope">
        <div class="flex items-center">
          <el-button type="primary" class="ml-20" @click="handleAdd">
            添加用户
          </el-button>
        </div>
      </template>
    </ZnTable>
  </div>
</template>
```

**容器class命名规范**：
- 使用 `模块名-container` 的格式，如：`user-manage-container`、`privilege-manage-container`
- 可以同时添加功能性class，如：`table-container`
- 在CSS中，该容器class必须作为最顶层的选择器

### 3. 必需的事件处理方法

- `handleOperateBtnsClick`：操作按钮统一事件分发
- `handleAdd`：新增用户
- `handleView`：查看用户详情
- `handleEdit`：编辑用户
- `handleDelete`：删除用户（需确认弹框）
- `handleResetPassword`：重置用户密码
- `handleToggleStatus`：启用/禁用用户

### 4. 弹框处理规范

**重要规范**：使用 `useDialogV2` 统一处理表单弹框，默认启用缓存模式：

- ✅ **必须设置 `destoryWhenClosed: false`** 启用缓存模式，提升性能
- ✅ **必须设置 `dialogId`** 为模块名-弹框名格式，如：`userManage-UserForm`
- ✅ **使用 `afterDialogOpen` 回调** 处理缓存模式下的数据初始化
- ✅ **使用新版 `onConfirm` 回调** 直接获取组件实例，无需异步调用

```typescript
const openUserDialog = (title: string, userData: any, mode: 'add' | 'edit' | 'view') => {
  const dialogRef = openDialog(
    {
      title,
      width: '800px',
      destoryWhenClosed: false,  // 🔥 必须设置为 false 启用缓存
      dialogId: 'userManage-UserForm',  // 🔥 必须设置唯一ID
      afterDialogOpen: async () => {
        // 🔥 缓存模式下的初始化逻辑
        const instance = await dialogRef.getInstance()
        instance?.initializeData?.(mode, userData)
      },
      afterDialogClose: (instance) => {
        // 🔥 缓存模式下的数据清理，确保下次打开时没有上一次的数据
        instance?.clearData?.()
      },
      onConfirm: async (close, loading, instance) => {  // 🔥 新版：直接传入实例
        if (mode === 'view') {
          close()
          return
        }

        // 🔥 现在可以直接使用传入的 instance，无需异步获取
        if (!instance) {
          ElMessage.error('获取表单实例失败')
          return
        }

        // 表单验证
        const isValid = await instance.validate()
        if (!isValid) return

        try {
          loading.value = true
          const formData = instance.getFormData()
          const res: any = await lib.api.userManageApi.save(formData)  // 🔥 使用类型断言
          
          if (res.success) {
            ElMessage.success(mode === 'add' ? '添加成功' : '编辑成功')
            refZnTable.value?.refreshTable()
            close()
          } else {
            ElMessage.error(res.message || '保存失败')
          }
        } catch (error) {
          console.error('保存失败:', error)
          ElMessage.error('保存失败')
        } finally {
          loading.value = false
        }
      }
    },
    UserForm,
    { mode, userData }
  )
}
```

**缓存模式注意事项**：
- 表单组件需要提供 `initializeData` 方法用于数据初始化
- 不能在 `onMounted` 中写初始化逻辑，因为缓存后第二次打开不会执行
- 使用 `afterDialogOpen` 回调确保每次打开都能正确初始化
- ✅ **使用 `afterDialogClose` 清除数据**：确保下次打开时没有上一次的数据残留

**onConfirm 回调增强**：
- ✅ **新版本**：`onConfirm: async (close, loading, instance) => {}` - 直接传入组件实例
- ❌ **旧版本**：需要通过 `await dialogRef.getInstance()` 异步获取实例
- 🔥 **优势**：减少异步操作，代码更简洁，性能更好

**ZnForm 性能优化**：
- ✅ **使用 hidden 属性**：通过 `hidden: (model) => model.type !== 'menu'` 控制字段显示/隐藏
- ❌ **动态操作 formSchema**：避免使用 `formSchema.splice()` 或 `formSchema.push()` 动态修改数组
- 🔥 **性能优势**：hidden 属性只是控制显示，不会重新渲染组件，性能更好

**数据清理机制**：
- ✅ **afterDialogClose 回调**：在弹框关闭后清除数据，防止数据残留
- 表单组件必须提供 `clearData` 方法用于数据清理
- 清理内容包括：表单数据重置、验证状态清除、组件状态重置

```typescript
// 表单组件中的 clearData 方法示例
const clearData = () => {
  // 重置表单数据为默认值
  Object.assign(formData, { ...defaultFormData })
  
  // 重置表单验证状态
  if (refZnForm.value?.form) {
    refZnForm.value.form.resetFields()
    refZnForm.value.form.clearValidate()
  }
  
  // 重置当前状态
  currentMode.value = 'add'
  currentData.value = null
}

// 暴露给父组件
defineExpose({
  // ... 其他方法
  clearData // 🔥 必须暴露 clearData 方法
})
```

```typescript
// ✅ 正确：使用 hidden 属性控制字段显示
const formSchema = [
  {
    label: '用户类型',
    prop: 'userType',
    el: 'el-select',
    enum: [
      { label: '个人', value: 'personal' },
      { label: '企业', value: 'company' }
    ]
  },
  {
    label: '身份证号',
    prop: 'idCard',
    el: 'el-input',
    hidden: (model) => model.userType !== 'personal'  // 🔥 使用 hidden 控制显示
  },
  {
    label: '营业执照号',
    prop: 'businessLicense',
    el: 'el-input',
    hidden: (model) => model.userType !== 'company'   // 🔥 使用 hidden 控制显示
  }
]

// ❌ 错误：动态操作 formSchema 数组
// watch(() => formData.userType, (newType) => {
//   formSchema.splice(2)  // 性能差，会重新渲染
//   if (newType === 'personal') {
//     formSchema.push(idCardField)
//   }
// })
```

## 表格配置规范 ([helper/index.tsx](mdc:src/views/userManage/helper/index.tsx))

### 1. 遵循ZnTable使用规范

表格配置必须严格遵循 [zntable-usage规则](mdc:.cursor/rules/zntable-usage.mdc)：

- 使用 `useUserManage` 作为hooks函数名（具体模块名称）
- 配置完整的Mock数据用于开发测试
- 使用标准的 `dataCallback` 处理数据
- 操作列使用 `type: 'operationBtns'` 配置

### 2. 列配置要求

必须包含以下标准列：

```typescript
const columns = reactive<ColumnProps[]>([
  { type: 'index', width: 60, label: '序号' },
  {
    prop: 'name',
    label: '用户名',
    width: 120,
    search: { el: 'input' }  // 支持搜索
  },
  {
    prop: 'mobile',
    label: '手机号',
    width: 130
  },
  {
    prop: 'email',
    label: '邮箱',
    width: 180
  },
  {
    prop: 'enabled',
    label: '状态',
    width: 80,
    search: { el: 'select' },
    enum: [
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ]
  },
  {
    prop: 'lastLoginTime',
    label: '最后登录时间',
    width: 160,
    render: 'dateFormat@YYYY-MM-DD HH:mm:ss'
  },
  // 操作列配置
  {
    prop: 'operation',
    label: '操作',
    type: 'operationBtns',
    width: 280,
    fixed: 'right',
    props: {
      btns: [
        { name: 'view', label: '查看', show: true },
        { name: 'edit', label: '编辑' },
        { name: 'resetPassword', label: '重置密码', show: (row) => row.enabled },
        { name: 'delete', label: '删除', show: (row) => !row.enabled }
      ],
      maxCount: 3,
      moreText: '更多'
    }
  }
])
```

### 3. API调用规范

**重要规范**：API 响应数据取值必须使用 `res.success` 和 `res.result`，并使用类型断言：

```typescript
const requestApi = async (params: any) => {
  try {
    const response = await lib.api.userManageApi.list(params)
    return response
  } catch (error) {
    console.error('API调用失败，使用Mock数据:', error)
    return mockData  // 开发阶段使用Mock数据
  }
}

// ✅ 正确的数据取值方式
const getUserDetail = async (id: string | number) => {
  try {
    const res: any = await lib.api.userManageApi.get({ id })  // 🔥 使用类型断言
    if (res.success && res.result) {  // 🔥 使用 res.success 和 res.result
      setFormData(res.result)
    } else {
      ElMessage.error(res.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

// ✅ 正确的保存操作
const saveUser = async (formData: any) => {
  try {
    const res: any = await lib.api.userManageApi.save(formData)  // 🔥 使用类型断言
    if (res.success) {
      ElMessage.success('保存成功')
      return true
    } else {
      ElMessage.error(res.message || '保存失败')
      return false
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
    return false
  }
}

// ❌ 错误的数据取值方式
// const res = await lib.api.userManageApi.get({ id })
// if (res.data.code === 200 && res.data.data) {  // 错误：不要使用 res.data
//   setFormData(res.data.data)
// }
```

### 4. Mock数据格式

使用标准的后端返回格式：

```typescript
const mockData = {
  message: '用户查询成功！',
  result: {
    currPage: 1,
    limit: 15,
    list: [
      {
        id: 1,
        username: 'admin',
        realName: '系统管理员',
        mobile: '13800138000',
        email: '<EMAIL>',
        orgName: '技术部',
        roleName: '管理员',
        enabled: true,
        lastLoginTime: '2024-12-25 10:30:00',
        recordCreateDate: '2024-01-01 09:00:00'
      }
      // ... 更多数据
    ],
    total: 5
  },
  success: true
}
```

## 用户表单组件规范 ([UserForm](mdc:src/views/userManage/components/UserForm))

### 1. 遵循ZnForm使用规范

表单组件必须严格遵循 [znform-usage规则](mdc:.cursor/rules/znform-usage.mdc)：

- 支持三种状态模式：`add`、`edit`、`view`
- 使用 `ZnForm` 组件进行表单渲染
- 配置完整的验证规则
- 支持数据转换和API调用

### 2. 组件属性定义

```typescript
interface Props {
  mode: 'add' | 'edit' | 'view'  // 模式：添加、编辑、查看
  userData?: any                  // 用户数据，编辑和查看模式使用
}
```

### 3. 状态管理规范

- **新增模式**：`formData.id = null`，表单为空白状态
- **编辑模式**：通过API获取用户详情填充表单
- **查看模式**：表单为只读状态，不可编辑

### 4. API数据获取

在编辑和查看模式下，通过API获取用户详情：

```typescript
const getUserDetail = async (id: string | number) => {
  try {
    const res = await lib.api.userManageApi.get({ id })
    if (res.success && res.result) {  // 🔥 使用 res.result
      setFormData(res.result)
    } else {
      ElMessage.error(res.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}
```

### 5. 数据转换处理

实现前后端数据格式转换：

```typescript
// 后端格式转前端格式
const transformDataFromBackend = (backendData: any) => {
  const frontendData = { ...backendData }
  // 清空密码字段，避免显示
  frontendData.password = ''
  frontendData.confirmPassword = ''
  return frontendData
}

// 前端格式转后端格式
const transformDataForSubmit = () => {
  const submitData = { ...formData }
  
  // 新增模式不传递id
  if (props.mode === 'add') {
    delete submitData.id
  }
  
  // 处理密码字段
  if (props.mode === 'edit' && !submitData.password) {
    delete submitData.password
    delete submitData.confirmPassword
  }
  
  // 移除确认密码字段
  delete submitData.confirmPassword
  
  return submitData
}
```

## 表单字段配置规范 ([UserForm/helper](mdc:src/views/userManage/components/UserForm/helper/index.tsx))

**重要规范**：在表单配置中禁止使用 Vue 的 `h` 函数，必须使用 TSX 语法：

- ✅ **必须使用 TSX 语法**：`render: () => <ComponentName v-model={value} />`
- ❌ **禁止使用 h 函数**：`render: () => h(ComponentName, { modelValue: value })`

```typescript
// ✅ 正确的 TSX 语法
{
  prop: 'orgId',
  label: '组织单位',
  col: { span: 12 },
  render: () => <DynamicOrgSelect v-model={formData.orgId} />,  // 🔥 使用 TSX 语法
  formItem: {
    rules: [{ required: true, message: '请选择组织单位', trigger: 'change' }]
  }
}

// ❌ 错误的 h 函数语法
// {
//   prop: 'orgId',
//   label: '组织单位',
//   col: { span: 12 },
//   render: () => h(DynamicOrgSelect, {  // 错误：不要使用 h 函数
//     modelValue: formData.orgId,
//     'onUpdate:modelValue': (value) => { formData.orgId = value }
//   })
// }
```

### 1. 默认数据定义

```typescript
export const defaultFormData = {
  // 基本信息字段
  username: '',         // 用户名
  password: '',         // 密码
  confirmPassword: '',  // 确认密码
  realName: '',         // 真实姓名
  mobile: '',          // 手机号
  email: '',           // 邮箱
  
  // 组织和角色
  orgId: '',           // 组织ID
  roleIds: [],         // 角色ID列表
  
  // 状态信息
  enabled: true,       // 是否启用
  
  // ID字段
  id: null            // ID（新增时为null，编辑时有值）
  
  // 注意：不包含 recordCreateDate、recordUpdateDate 等系统管理字段
}
```

### 2. 验证规则要求

必须包含以下验证规则：

- **用户名**：必填，3-20字符长度
- **真实姓名**：必填，最大50字符
- **手机号**：必填，正确的手机号格式
- **邮箱**：正确的邮箱格式（非必填）
- **密码**：新增时必填，最少6位，需要确认密码
- **组织和角色**：必选项

### 3. 密码字段处理

密码字段需要特殊处理：

```typescript
// 密码验证规则
const validatePassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else {
    callback()
  }
}

// 确认密码验证规则
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}
```

### 4. 字段配置示例

```typescript
{
  prop: 'username',
  label: '用户名',
  el: 'el-input',
  props: {
    placeholder: '请输入用户名'
  },
  col: { span: 12 },
  formItem: {
    rules: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
    ]
  }
}
```

## API接口配置

### 1. 接口定义

在 [src/api/index.ts](mdc:src/api/index.ts) 中定义用户管理相关接口：

```typescript
// 用户管理专用API
export const userManageApi = {
  list: (params: any) => requestApi('rest/user/list', params),
  save: (params: any) => requestApi('rest/user/save', params),
  get: (params: any) => requestApi('rest/user/get', params),
  delete: (params: any) => requestApi('rest/sysUserDiy/logicallyDelete', params),
  enabled: (params: any) => requestApi('rest/user/enabled', params),
  resetPassword: (params: any) => requestApi('rest/user/resetPassword', params)
}
```

### 2. API调用规范

统一使用 `lib.api.userManageApi.*` 进行接口调用：

```typescript
// 保存用户
const res = await lib.api.userManageApi.save(formData)

// 获取用户详情
const res = await lib.api.userManageApi.get({ id })

// 删除用户
const res = await lib.api.userManageApi.delete({ id })
```

## 样式规范

### 1. 容器class规范

**重要规范**：每个组件的CSS样式必须以容器class作为最顶层选择器：

```scss
<style scoped lang="scss">
  .user-manage-container {
    &.table-container {
      height: 100%;
    }
    
    :deep(.el-card) {
      height: 100%;
      
      .el-card__body {
        height: calc(100% - 60px);
        padding: 20px;
      }
    }
  }
  
  .user-form {
    padding: 0;
    
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }
  }
</style>
```

**CSS结构要求**：
- 容器class必须作为最顶层选择器
- 所有子样式都应该嵌套在容器class内部
- 使用 `&` 符号连接多个class，如：`&.table-container`
- 深度选择器 `:deep()` 用于修改第三方组件样式

### 2. 使用SCSS语法

所有样式必须使用 `<style scoped lang="scss">` 语法。

## 错误处理规范

### 1. API调用错误处理

```typescript
try {
  const res = await lib.api.userManageApi.save(formData)
  if (res.success) {
    ElMessage.success('操作成功')
    refZnTable.value?.refreshTable()
  }
} catch (error) {
  console.error('操作失败:', error)
  ElMessage.error('操作失败')
}
```

### 2. 确认操作处理

删除、重置密码等操作需要用户确认：

```typescript
try {
  await ElMessageBox.confirm(
    `确定要删除用户 "${row.name}" 吗？`, 
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  
  // 执行删除操作
  const res = await lib.api.userManageApi.delete({ id: row.id })
  if (res.success) {
    ElMessage.success('删除成功')
    refZnTable.value?.refreshTable()
  }
} catch (error) {
  console.log('取消操作')
}
```

## 质量检查清单

在开发用户管理页面时，必须检查：

✅ **页面结构**
- [ ] 使用ZnTable组件展示数据
- [ ] 配置完整的操作按钮
- [ ] 使用useDialogV2处理弹框
- [ ] 实现所有CRUD操作

✅ **弹框配置** 🔥
- [ ] 设置 `destoryWhenClosed: false` 启用缓存模式
- [ ] 设置 `dialogId` 为模块名-弹框名格式
- [ ] 使用 `afterDialogOpen` 回调处理初始化
- [ ] 表单组件提供 `initializeData` 方法

✅ **表格配置**
- [ ] 遵循zntable-usage规则
- [ ] 使用operationBtns配置操作列
- [ ] 配置完整的Mock数据
- [ ] 实现正确的API调用

✅ **表单配置**
- [ ] 遵循znform-usage规则
- [ ] 支持三种状态模式
- [ ] 配置完整的验证规则
- [ ] 实现数据转换处理
- [ ] 使用TSX语法，禁止使用h函数 🔥

✅ **数据处理** 🔥
- [ ] 编辑/查看模式通过API获取详情
- [ ] 正确处理系统管理字段
- [ ] 实现前后端数据格式转换
- [ ] 完善的错误处理机制
- [ ] 使用 `res.success` 和 `res.result` 取值，并使用类型断言 `const res: any`

✅ **代码规范**
- [ ] 使用SCSS编写样式
- [ ] 遵循命名规范
- [ ] 完善的注释说明
- [ ] 统一的错误处理

✅ **样式规范**
- [ ] 最外层有语义化的容器class
- [ ] 容器class作为CSS最顶层选择器
- [ ] 正确使用SCSS嵌套语法
- [ ] 深度选择器使用规范

该规范确保用户管理页面的开发符合项目标准，具有良好的可维护性和扩展性。

