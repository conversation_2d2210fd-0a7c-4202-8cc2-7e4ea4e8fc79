<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-06-11 14:55:49
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-05 16:42:06
 * @FilePath: \bigscreen-qj-web\src\views\Screen\StructureDiagram\index.vue
 * @Description: 
 * 
-->
<template>
  <div class="structure-diagram-container" id="structureDiagram">
    <SwitchCanvas class="absolute left-1/2 top-160 z-10 -translate-x-1/2" v-model="code" @change="handleCodeChange"></SwitchCanvas>
    <canvas id="canvas" width="5120px" height="1440px"></canvas>
  </div>
</template>

<script setup lang="ts">
  import SwitchCanvas from './Components/SwitchCanvas.vue'

  import lib from '@/utils/lib'
  import StructureDiagram from '@/utils/structureDiagram.js'
  import { CanvasDragHandler, DragData, injectDragStyles } from '@/utils/CanvasDragHandler/CanvasDragHandler'
  import { fabric } from 'fabric'
  import VideoWall from '@/views/Screen/StructureDiagram/Components/VideoWall/index.vue'
  import { usePopWindow, closePopWindowByTag } from 'znyg-frontend-common'
  const { showPopWindow } = usePopWindow()
  const code = ref('QJSDJKJC')
  const { appContext } = getCurrentInstance()
  const zoom = 2
  let dragHandler: CanvasDragHandler | null = null
  const activeZone = ref('')
  const dropData = ref(null)
  const activeCodeMap = new Map()
  onMounted(() => {
    lib.structureDiagram = new StructureDiagram('canvas')
    lib.structureDiagram.getData(code.value)
    lib.structureDiagram.setZoom(zoom)
    lib.structureDiagram.setMouseDownFunc((target, e) => {
      console.log('鼠标点击事件', target, e)
      const { itemData, left, top } = target
      // const { x, y } = e.pointer
      if (itemData) {
        if (itemData.name === 'robot') {
          lib.popWindow.removeDialog('robotInfo')
          // 机器人
          lib.popWindow.createPopWindow('../StructureDiagram/Components/RobotInfo/index.vue', {
            left: left * zoom + 100,
            top: top * zoom + 30,
            appendParent: 'structureDiagram',
            tag: 'robotInfo',
            appContext
          })
        }
        if (itemData.device?.length > 0) {
          lib.popWindow.removeDialog('deviceInfo')

          const deviceData = itemData.device[0]
          lib.popWindow.createPopWindow(
            '../StructureDiagram/Components/DeviceInfo/index.vue',
            {
              left: left * zoom + 30,
              top: top * zoom - 120,
              appendParent: 'structureDiagram',
              tag: 'deviceInfo',
              appContext
            },
            deviceData
          )
        }
      }
    })

    //设置拖拽
    // 创建拖拽处理器
    dragHandler = new CanvasDragHandler({
      canvas: lib.structureDiagram.canvas,
      dropZoneSelector: '.camera-item', // 目标区域选择器

      // 自定义拖拽条件：只有isCamera=true的元素可以拖拽
      canDrag: (obj: fabric.Object) => {
        const isCamera = (obj as any).isCamera
        console.log(`检查元素 ${obj.type} 是否可拖拽: ${isCamera}`)
        // return !!isCamera
        return true
      },

      // 自定义数据提取
      getCustomData: (obj: fabric.Object) => {
        return (
          (obj as any).itemData || {
            id: `element-${Date.now()}`,
            type: obj.type,
            isCamera: (obj as any).isCamera
          }
        )
      },

      // 拖拽成功回调
      onDrop: (data: DragData, zoneId: string, zoneElement: Element) => {
        console.log('元素放置成功:', { data, zoneId })

        const dropItem = {
          ...data,
          droppedAt: new Date().toISOString(),
          zoneId
        }

        // 根据zone ID分配到不同数组
        // if (zoneId === 'project-zone') {
        //   projectItems.value.push(dropItem)
        // } else if (zoneId === 'workspace-zone') {
        //   workspaceItems.value.push(dropItem)
        // }
        console.log('dropItem', dropItem)
        dropData.value = dropItem
        // 清除活动区域状态
        activeZone.value = ''

        const deviceCode = dropItem.object.name.split('|')[0]
        //如果map中已经存在deviceCode,则把原有的置空
        activeCodeMap.forEach((value, key) => {
          if (value === deviceCode) {
            activeCodeMap.set(key, '')
          }
        })
        activeCodeMap.set(zoneId, deviceCode)

        //获取所有activeCodeMap的值
        const activeCodes = Array.from(activeCodeMap.values())
        console.log('activeCodes', activeCodes)

        lib.structureDiagram.canvas.forEachObject((item) => {
          if (item?.itemData?.name?.includes('|')) {
            const deviceCode = item.itemData.name.split('|')[0]
            if (activeCodes.includes(deviceCode)) {
              const filterBrightness = new fabric.Image.filters.Brightness({ brightness: 0.45 })
              item.filters.push(filterBrightness)
              item.applyFilters()
            } else {
              item.filters = []
              item.applyFilters()
            }
          }
        })
        lib.structureDiagram.canvas.renderAll()
      },

      // 拖拽开始回调
      onDragStart: (obj: fabric.Object, data: DragData) => {
        console.log('拖拽开始:', { obj: obj.type, data })
      },

      // 拖拽结束回调
      onDragEnd: (success: boolean, data?: DragData, zoneId?: string) => {
        console.log('拖拽结束:', { success, zoneId, data })
        activeZone.value = ''
      },

      // 区域变化回调（实时更新activeZone）
      onZoneChange: (zoneId: string | null) => {
        activeZone.value = zoneId || ''
      },

      // 自定义预览样式
      previewStyle: {
        borderRadius: '12px',
        border: '3px solid #007bff',
        fontSize: '14px'
      },

      // 启用调试日志
      debug: true
    })

    // 注入拖拽样式
    injectDragStyles()
  })

  const handleCodeChange = async (c) => {
    console.log(c, code.value)
    closePopWindowByTag('videoWall')

    await lib.structureDiagram.getData(code.value)
    lib.popWindow.removeDialog('robotInfo')
    lib.popWindow.removeDialog('deviceInfo')
    console.log(lib.structureDiagram.getJSONData())
    const isQJSDZHJK = code.value === 'QJSDZHJK'
    lib.structureDiagram.getJSONData()?.components?.forEach((item, index) => {
      const left = item.style.x
      const top = item.style.y
      const width = item.style.w
      const height = item.style.h
      if (isQJSDZHJK && item.name === '视频定位') {
        const op = {
          width: width * 2,
          height: height * 2,
          left: left * 2,
          top: top * 2,
          zIndex: 999,
          appendParent: 'structureDiagram',
          tag: 'videoWall'
        }
        showPopWindow(op, VideoWall, { width: width * 2, height: height * 2, activeZone: activeZone, dropData: dropData })
      }
    })
  }
  onBeforeUnmount(() => {
    if (dragHandler) {
      dragHandler.destroy()
    }
  })
  onUnmounted(() => {
    lib.structureDiagram.dispose()
    lib.popWindow.removeDialog('deviceInfo')
  })
</script>

<style lang="scss" scoped>
  .structure-diagram-container {
    position: absolute;
    z-index: 0;
    width: 5120px;
    height: 1440px;
    background: black;
    #canvas {
      width: 5120px;
      height: 1440px;
    }
  }
</style>
