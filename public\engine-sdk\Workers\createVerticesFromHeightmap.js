define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./PrimitiveType-ec02f806","./AttributeCompression-a01059cd","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./WebMercatorProjection-ab2cf572","./createTaskProcessorWorker","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./OrientedBoundingBox-d0a49c02","./CreatePhysicalArray-935f3db5","./materem-d090bcd4","./TerrainEncoding-308a828e","./TerrainProvider-16cd8dff"],(function(e,t,i,r,a,n,s,l,o,f,u,d,c,h,m,g,p,w,x,y,k,v,I,b){"use strict";var U={NONE:0,LERC:1},T=Object.freeze(U),M={};M.DEFAULT_STRUCTURE=Object.freeze({heightScale:1,heightOffset:0,elementsPerHeight:1,stride:1,elementMultiplier:256,isBigEndian:!1});var A=new i.Cartesian3,V=new s.Matrix4,B=new i.Cartesian3,D=new i.Cartesian3;M.computeVertices=function(l){if(!e.defined(l)||!e.defined(l.heightmap))throw new t.DeveloperError("options.heightmap is required.");if(!e.defined(l.width)||!e.defined(l.height))throw new t.DeveloperError("options.width and options.height are required.");if(!e.defined(l.nativeRectangle))throw new t.DeveloperError("options.nativeRectangle is required.");if(!e.defined(l.skirtHeight))throw new t.DeveloperError("options.skirtHeight is required.");var o,f,u,d,c=Math.cos,h=Math.sin,m=Math.sqrt,p=Math.atan,x=Math.exp,k=i.CesiumMath.PI_OVER_TWO,v=i.CesiumMath.toRadians,b=l.heightmap,U=l.width,T=l.height,S=l.skirtHeight,P=e.defaultValue(l.isGeographic,!0),E=e.defaultValue(l.ellipsoid,r.Ellipsoid.WGS84),C=1/E.maximumRadius,F=l.nativeRectangle,L=l.rectangle;e.defined(L)?(o=L.west,f=L.south,u=L.east,d=L.north):P?(o=v(F.west),f=v(F.south),u=v(F.east),d=v(F.north)):(o=F.west*C,f=k-2*p(x(-F.south*C)),u=F.east*C,d=k-2*p(x(-F.north*C)));var R=l.relativeToCenter,O=e.defined(R);R=O?R:i.Cartesian3.ZERO;var N=e.defaultValue(l.exaggeration,1),z=e.defaultValue(l.includeWebMercatorT,!1),H=e.defaultValue(l.structure,M.DEFAULT_STRUCTURE),_=e.defaultValue(H.heightScale,M.DEFAULT_STRUCTURE.heightScale),Y=e.defaultValue(H.heightOffset,M.DEFAULT_STRUCTURE.heightOffset),W=e.defaultValue(H.elementsPerHeight,M.DEFAULT_STRUCTURE.elementsPerHeight),X=e.defaultValue(H.stride,M.DEFAULT_STRUCTURE.stride),Z=e.defaultValue(H.elementMultiplier,M.DEFAULT_STRUCTURE.elementMultiplier),G=e.defaultValue(H.isBigEndian,M.DEFAULT_STRUCTURE.isBigEndian),j=r.Rectangle.computeWidth(F),q=r.Rectangle.computeHeight(F),Q=j/(U-1),J=q/(T-1);P||(j*=C,q*=C);var K,$,ee=E.radiiSquared,te=ee.x,ie=ee.y,re=ee.z,ae=65536,ne=-65536,se=n.Transforms.eastNorthUpToFixedFrame(R,E),le=s.Matrix4.inverseTransformation(se,V);z&&(K=g.WebMercatorProjection.geodeticLatitudeToMercatorAngle(f),$=1/(g.WebMercatorProjection.geodeticLatitudeToMercatorAngle(d)-K));var oe=B;oe.x=Number.POSITIVE_INFINITY,oe.y=Number.POSITIVE_INFINITY,oe.z=Number.POSITIVE_INFINITY;var fe=D;fe.x=Number.NEGATIVE_INFINITY,fe.y=Number.NEGATIVE_INFINITY,fe.z=Number.NEGATIVE_INFINITY;var ue=Number.POSITIVE_INFINITY,de=U*T,ce=S>0?2*U+2*T:0,he=de+ce,me=new Array(he),ge=new Array(he),pe=new Array(he),we=z?new Array(he):[],xe=0,ye=T,ke=0,ve=U;S>0&&(--xe,++ye,--ke,++ve);for(var Ie=1e-5,be=xe;be<ye;++be){var Ue=be;Ue<0&&(Ue=0),Ue>=T&&(Ue=T-1);var Te=F.north-J*Ue;Te=P?v(Te):k-2*p(x(-Te*C));var Me=(Te-f)/(d-f);Me=i.CesiumMath.clamp(Me,0,1);var Ae=be===xe,Ve=be===ye-1;S>0&&(Ae?Te+=Ie*q:Ve&&(Te-=Ie*q));var Be,De=c(Te),Se=h(Te),Pe=re*Se;z&&(Be=(g.WebMercatorProjection.geodeticLatitudeToMercatorAngle(Te)-K)*$);for(var Ee=ke;Ee<ve;++Ee){var Ce=Ee;Ce<0&&(Ce=0),Ce>=U&&(Ce=U-1);var Fe,Le,Re=Ue*(U*X)+Ce*X;if(1===W)Fe=b[Re];else if(Fe=0,G)for(Le=0;Le<W;++Le)Fe=Fe*Z+b[Re+Le];else for(Le=W-1;Le>=0;--Le)Fe=Fe*Z+b[Re+Le];Fe=(Fe*_+Y)*N,ne=Math.max(ne,Fe),ae=Math.min(ae,Fe);var Oe=F.west+Q*Ce;P?Oe=v(Oe):Oe*=C;var Ne=(Oe-o)/(u-o);Ne=i.CesiumMath.clamp(Ne,0,1);var ze=Ue*U+Ce;if(S>0){var He=Ee===ke,_e=Ee===ve-1,Ye=Ae||Ve||He||_e,We=(Ae||Ve)&&(He||_e);if(We)continue;Ye&&(Fe-=S,He?(ze=de+(T-Ue-1),Oe-=Ie*j):Ve?ze=de+T+(U-Ce-1):_e?(ze=de+T+U+Ue,Oe+=Ie*j):Ae&&(ze=de+T+U+T+Ce))}var Xe=De*c(Oe),Ze=De*h(Oe),Ge=te*Xe,je=ie*Ze,qe=m(Ge*Xe+je*Ze+Pe*Se),Qe=1/qe,Je=Ge*Qe,Ke=je*Qe,$e=Pe*Qe,et=new i.Cartesian3;et.x=Je+Xe*Fe,et.y=Ke+Ze*Fe,et.z=$e+Se*Fe,me[ze]=et,ge[ze]=Fe,pe[ze]=new i.Cartesian2(Ne,Me),z&&(we[ze]=Be),s.Matrix4.multiplyByPoint(le,et,A),i.Cartesian3.minimumByComponent(A,oe,oe),i.Cartesian3.maximumByComponent(A,fe,fe),ue=Math.min(ue,Fe)}}var tt,it,rt=a.BoundingSphere.fromPoints(me);if(e.defined(L)&&(tt=y.OrientedBoundingBox.fromRectangle(L,ae,ne,E)),O){var at=new I.EllipsoidalOccluder(E);it=at.computeHorizonCullingPointPossiblyUnderEllipsoid(R,me,ae)}for(var nt=new w.AxisAlignedBoundingBox(oe,fe,R),st=new I.TerrainEncoding(nt,ue,ne,se,!1,z),lt=new Float32Array(he*st.getStride()),ot=0,ft=0;ft<he;++ft)ot=st.encode(lt,ot,me[ft],pe[ft],ge[ft],void 0,we[ft]);return{vertices:lt,positions:me,maximumHeight:ne,minimumHeight:ae,encoding:st,boundingSphere3D:rt,orientedBoundingBox:tt,occludeePointInScaledSpace:it}};var S={};(function(){var e=function(){var e={defaultNoDataValue:-34027999387901484e22,decode:function(n,s){s=s||{};var l=s.encodedMaskData||null===s.encodedMaskData,o=a(n,s.inputOffset||0,l),f=null!==s.noDataValue?s.noDataValue:e.defaultNoDataValue,u=t(o,s.pixelType||Float32Array,s.encodedMaskData,f,s.returnMask),d={width:o.width,height:o.height,pixelData:u.resultPixels,minValue:u.minValue,maxValue:o.pixels.maxValue,noDataValue:f};return u.resultMask&&(d.maskData=u.resultMask),s.returnEncodedMask&&o.mask&&(d.encodedMaskData=o.mask.bitset?o.mask.bitset:null),s.returnFileInfo&&(d.fileInfo=i(o),s.computeUsedBitDepths&&(d.fileInfo.bitDepths=r(o))),d}},t=function(e,t,i,r,a){var s,l,o,f=0,u=e.pixels.numBlocksX,d=e.pixels.numBlocksY,c=Math.floor(e.width/u),h=Math.floor(e.height/d),m=2*e.maxZError,g=Number.MAX_VALUE;i=i||(e.mask?e.mask.bitset:null),l=new t(e.width*e.height),a&&i&&(o=new Uint8Array(e.width*e.height));for(var p,w,x=new Float32Array(c*h),y=0;y<=d;y++){var k=y!==d?h:e.height%d;if(0!==k)for(var v=0;v<=u;v++){var I=v!==u?c:e.width%u;if(0!==I){var b,U,T,M,A=y*e.width*h+v*c,V=e.width-I,B=e.pixels.blocks[f];if(B.encoding<2?(0===B.encoding?b=B.rawData:(n(B.stuffedData,B.bitsPerPixel,B.numValidPixels,B.offset,m,x,e.pixels.maxValue),b=x),U=0):T=2===B.encoding?0:B.offset,i)for(w=0;w<k;w++){for(7&A&&(M=i[A>>3],M<<=7&A),p=0;p<I;p++)7&A||(M=i[A>>3]),128&M?(o&&(o[A]=1),s=B.encoding<2?b[U++]:T,g=g>s?s:g,l[A++]=s):(o&&(o[A]=0),l[A++]=r),M<<=1;A+=V}else if(B.encoding<2)for(w=0;w<k;w++){for(p=0;p<I;p++)s=b[U++],g=g>s?s:g,l[A++]=s;A+=V}else for(g=g>T?T:g,w=0;w<k;w++){for(p=0;p<I;p++)l[A++]=T;A+=V}if(1===B.encoding&&U!==B.numValidPixels)throw"Block and Mask do not match";f++}}}return{resultPixels:l,resultMask:o,minValue:g}},i=function(e){return{fileIdentifierString:e.fileIdentifierString,fileVersion:e.fileVersion,imageType:e.imageType,height:e.height,width:e.width,maxZError:e.maxZError,eofOffset:e.eofOffset,mask:e.mask?{numBlocksX:e.mask.numBlocksX,numBlocksY:e.mask.numBlocksY,numBytes:e.mask.numBytes,maxValue:e.mask.maxValue}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,numBytes:e.pixels.numBytes,maxValue:e.pixels.maxValue,noDataValue:e.noDataValue}}},r=function(e){for(var t=e.pixels.numBlocksX*e.pixels.numBlocksY,i={},r=0;r<t;r++){var a=e.pixels.blocks[r];0===a.encoding?i.float32=!0:1===a.encoding?i[a.bitsPerPixel]=!0:i[0]=!0}return Object.keys(i)},a=function(e,t,i){var r={},a=new Uint8Array(e,t,10);if(r.fileIdentifierString=String.fromCharCode.apply(null,a),"CntZImage"!==r.fileIdentifierString.trim())throw"Unexpected file identifier string: "+r.fileIdentifierString;t+=10;var n=new DataView(e,t,24);if(r.fileVersion=n.getInt32(0,!0),r.imageType=n.getInt32(4,!0),r.height=n.getUint32(8,!0),r.width=n.getUint32(12,!0),r.maxZError=n.getFloat64(16,!0),t+=24,!i)if(n=new DataView(e,t,16),r.mask={},r.mask.numBlocksY=n.getUint32(0,!0),r.mask.numBlocksX=n.getUint32(4,!0),r.mask.numBytes=n.getUint32(8,!0),r.mask.maxValue=n.getFloat32(12,!0),t+=16,r.mask.numBytes>0){var s=new Uint8Array(Math.ceil(r.width*r.height/8));n=new DataView(e,t,r.mask.numBytes);var l=n.getInt16(0,!0),o=2,f=0;do{if(l>0)while(l--)s[f++]=n.getUint8(o++);else{var u=n.getUint8(o++);l=-l;while(l--)s[f++]=u}l=n.getInt16(o,!0),o+=2}while(o<r.mask.numBytes);if(-32768!==l||f<s.length)throw"Unexpected end of mask RLE encoding";r.mask.bitset=s,t+=r.mask.numBytes}else 0===(r.mask.numBytes|r.mask.numBlocksY|r.mask.maxValue)&&(r.mask.bitset=new Uint8Array(Math.ceil(r.width*r.height/8)));n=new DataView(e,t,16),r.pixels={},r.pixels.numBlocksY=n.getUint32(0,!0),r.pixels.numBlocksX=n.getUint32(4,!0),r.pixels.numBytes=n.getUint32(8,!0),r.pixels.maxValue=n.getFloat32(12,!0),t+=16;var d=r.pixels.numBlocksX,c=r.pixels.numBlocksY,h=d+(r.width%d>0?1:0),m=c+(r.height%c>0?1:0);r.pixels.blocks=new Array(h*m);for(var g=0,p=0;p<m;p++)for(var w=0;w<h;w++){var x=0,y=e.byteLength-t;n=new DataView(e,t,Math.min(10,y));var k={};r.pixels.blocks[g++]=k;var v=n.getUint8(0);if(x++,k.encoding=63&v,k.encoding>3)throw"Invalid block encoding ("+k.encoding+")";if(2!==k.encoding){if(0!==v&&2!==v){if(v>>=6,k.offsetType=v,2===v)k.offset=n.getInt8(1),x++;else if(1===v)k.offset=n.getInt16(1,!0),x+=2;else{if(0!==v)throw"Invalid block offset type";k.offset=n.getFloat32(1,!0),x+=4}if(1===k.encoding)if(v=n.getUint8(x),x++,k.bitsPerPixel=63&v,v>>=6,k.numValidPixelsType=v,2===v)k.numValidPixels=n.getUint8(x),x++;else if(1===v)k.numValidPixels=n.getUint16(x,!0),x+=2;else{if(0!==v)throw"Invalid valid pixel count type";k.numValidPixels=n.getUint32(x,!0),x+=4}}var I,b;if(t+=x,3!==k.encoding)if(0===k.encoding){var U=(r.pixels.numBytes-1)/4;if(U!==Math.floor(U))throw"uncompressed block has invalid length";I=new ArrayBuffer(4*U),b=new Uint8Array(I),b.set(new Uint8Array(e,t,4*U));var T=new Float32Array(I);k.rawData=T,t+=4*U}else if(1===k.encoding){var M=Math.ceil(k.numValidPixels*k.bitsPerPixel/8),A=Math.ceil(M/4);I=new ArrayBuffer(4*A),b=new Uint8Array(I),b.set(new Uint8Array(e,t,M)),k.stuffedData=new Uint32Array(I),t+=M}}else t++}return r.eofOffset=t,r},n=function(e,t,i,r,a,n,s){var l,o,f,u=(1<<t)-1,d=0,c=0,h=Math.ceil((s-r)/a),m=4*e.length-Math.ceil(t*i/8);for(e[e.length-1]<<=8*m,l=0;l<i;l++){if(0===c&&(f=e[d++],c=32),c>=t)o=f>>>c-t&u,c-=t;else{var g=t-c;o=(f&u)<<g&u,f=e[d++],c=32-g,o+=f>>>c}n[l]=o<h?r+o*a:s}return n};return e}(),t=function(){var e={unstuff:function(e,t,i,r,a,n,s,l){var o,f,u,d,c,h=(1<<i)-1,m=0,g=0,p=4*e.length-Math.ceil(i*r/8);if(e[e.length-1]<<=8*p,a)for(o=0;o<r;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&h,g-=i):(d=i-g,f=(u&h)<<d&h,u=e[m++],g=32-d,f+=u>>>g),t[o]=a[f];else for(c=Math.ceil((l-n)/s),o=0;o<r;o++)0===g&&(u=e[m++],g=32),g>=i?(f=u>>>g-i&h,g-=i):(d=i-g,f=(u&h)<<d&h,u=e[m++],g=32-d,f+=u>>>g),t[o]=f<c?n+f*s:l},unstuffLUT:function(e,t,i,r,a,n){var s,l=(1<<t)-1,o=0,f=0,u=0,d=0,c=0,h=[],m=4*e.length-Math.ceil(t*i/8);e[e.length-1]<<=8*m;var g=Math.ceil((n-r)/a);for(f=0;f<i;f++)0===d&&(s=e[o++],d=32),d>=t?(c=s>>>d-t&l,d-=t):(u=t-d,c=(s&l)<<u&l,s=e[o++],d=32-u,c+=s>>>d),h[f]=c<g?r+c*a:n;return h.unshift(r),h},unstuff2:function(e,t,i,r,a,n,s,l){var o,f,u,d,c=(1<<i)-1,h=0,m=0,g=0;if(a)for(o=0;o<r;o++)0===m&&(u=e[h++],m=32,g=0),m>=i?(f=u>>>g&c,m-=i,g+=i):(d=i-m,f=u>>>g&c,u=e[h++],m=32-d,f|=(u&(1<<d)-1)<<i-d,g=d),t[o]=a[f];else{var p=Math.ceil((l-n)/s);for(o=0;o<r;o++)0===m&&(u=e[h++],m=32,g=0),m>=i?(f=u>>>g&c,m-=i,g+=i):(d=i-m,f=u>>>g&c,u=e[h++],m=32-d,f|=(u&(1<<d)-1)<<i-d,g=d),t[o]=f<p?n+f*s:l}return t},unstuffLUT2:function(e,t,i,r,a,n){var s,l=(1<<t)-1,o=0,f=0,u=0,d=0,c=0,h=0,m=[],g=Math.ceil((n-r)/a);for(f=0;f<i;f++)0===d&&(s=e[o++],d=32,h=0),d>=t?(c=s>>>h&l,d-=t,h+=t):(u=t-d,c=s>>>h&l,s=e[o++],d=32-u,c|=(s&(1<<u)-1)<<t-u,h=u),m[f]=c<g?r+c*a:n;return m.unshift(r),m},originalUnstuff:function(e,t,i,r){var a,n,s,l,o=(1<<i)-1,f=0,u=0,d=4*e.length-Math.ceil(i*r/8);for(e[e.length-1]<<=8*d,a=0;a<r;a++)0===u&&(s=e[f++],u=32),u>=i?(n=s>>>u-i&o,u-=i):(l=i-u,n=(s&o)<<l&o,s=e[f++],u=32-l,n+=s>>>u),t[a]=n;return t},originalUnstuff2:function(e,t,i,r){var a,n,s,l,o=(1<<i)-1,f=0,u=0,d=0;for(a=0;a<r;a++)0===u&&(s=e[f++],u=32,d=0),u>=i?(n=s>>>d&o,u-=i,d+=i):(l=i-u,n=s>>>d&o,s=e[f++],u=32-l,n|=(s&(1<<l)-1)<<i-l,d=l),t[a]=n;return t}},t={HUFFMAN_LUT_BITS_MAX:12,computeChecksumFletcher32:function(e){var t=65535,i=65535,r=e.length,a=Math.floor(r/2),n=0;while(a){var s=a>=359?359:a;a-=s;do{t+=e[n++]<<8,i+=t+=e[n++]}while(--s);t=(65535&t)+(t>>>16),i=(65535&i)+(i>>>16)}return 1&r&&(i+=t+=e[n]<<8),t=(65535&t)+(t>>>16),i=(65535&i)+(i>>>16),(i<<16|t)>>>0},readHeaderInfo:function(e,t){var i=t.ptr,r=new Uint8Array(e,i,6),a={};if(a.fileIdentifierString=String.fromCharCode.apply(null,r),0!==a.fileIdentifierString.lastIndexOf("Lerc2",0))throw"Unexpected file identifier string (expect Lerc2 ): "+a.fileIdentifierString;i+=6;var n,s,l=new DataView(e,i,8),o=l.getInt32(0,!0);if(a.fileVersion=o,i+=4,o>=3&&(a.checksum=l.getUint32(4,!0),i+=4),l=new DataView(e,i,12),a.height=l.getUint32(0,!0),a.width=l.getUint32(4,!0),i+=8,o>=4?(a.numDims=l.getUint32(8,!0),i+=4):a.numDims=1,l=new DataView(e,i,40),a.numValidPixel=l.getUint32(0,!0),a.microBlockSize=l.getInt32(4,!0),a.blobSize=l.getInt32(8,!0),a.imageType=l.getInt32(12,!0),a.maxZError=l.getFloat64(16,!0),a.zMin=l.getFloat64(24,!0),a.zMax=l.getFloat64(32,!0),i+=40,t.headerInfo=a,t.ptr=i,o>=3&&(s=o>=4?52:48,n=this.computeChecksumFletcher32(new Uint8Array(e,i-s,a.blobSize-14)),n!==a.checksum))throw"Checksum failed.";return!0},checkMinMaxRanges:function(e,t){var i=t.headerInfo,r=this.getDataTypeArray(i.imageType),a=i.numDims*this.getDataTypeSize(i.imageType),n=this.readSubArray(e,t.ptr,r,a),s=this.readSubArray(e,t.ptr+a,r,a);t.ptr+=2*a;var l,o=!0;for(l=0;l<i.numDims;l++)if(n[l]!==s[l]){o=!1;break}return i.minValues=n,i.maxValues=s,o},readSubArray:function(e,t,i,r){var a;if(i===Uint8Array)a=new Uint8Array(e,t,r);else{var n=new ArrayBuffer(r),s=new Uint8Array(n);s.set(new Uint8Array(e,t,r)),a=new i(n)}return a},readMask:function(e,t){var i,r,a=t.ptr,n=t.headerInfo,s=n.width*n.height,l=n.numValidPixel,o=new DataView(e,a,4),f={};if(f.numBytes=o.getUint32(0,!0),a+=4,(0===l||s===l)&&0!==f.numBytes)throw"invalid mask";if(0===l)i=new Uint8Array(Math.ceil(s/8)),f.bitset=i,r=new Uint8Array(s),t.pixels.resultMask=r,a+=f.numBytes;else if(f.numBytes>0){i=new Uint8Array(Math.ceil(s/8)),o=new DataView(e,a,f.numBytes);var u=o.getInt16(0,!0),d=2,c=0,h=0;do{if(u>0)while(u--)i[c++]=o.getUint8(d++);else{h=o.getUint8(d++),u=-u;while(u--)i[c++]=h}u=o.getInt16(d,!0),d+=2}while(d<f.numBytes);if(-32768!==u||c<i.length)throw"Unexpected end of mask RLE encoding";r=new Uint8Array(s);var m=0,g=0;for(g=0;g<s;g++)7&g?(m=i[g>>3],m<<=7&g):m=i[g>>3],128&m&&(r[g]=1);t.pixels.resultMask=r,f.bitset=i,a+=f.numBytes}return t.ptr=a,t.mask=f,!0},readDataOneSweep:function(e,i,r){var a,n=i.ptr,s=i.headerInfo,l=s.numDims,o=s.width*s.height,f=s.imageType,u=s.numValidPixel*t.getDataTypeSize(f)*l,d=i.pixels.resultMask;if(r===Uint8Array)a=new Uint8Array(e,n,u);else{var c=new ArrayBuffer(u),h=new Uint8Array(c);h.set(new Uint8Array(e,n,u)),a=new r(c)}if(a.length===o*l)i.pixels.resultPixels=a;else{i.pixels.resultPixels=new r(o*l);var m=0,g=0,p=0,w=0;if(l>1)for(p=0;p<l;p++)for(w=p*o,g=0;g<o;g++)d[g]&&(i.pixels.resultPixels[w+g]=a[m++]);else for(g=0;g<o;g++)d[g]&&(i.pixels.resultPixels[g]=a[m++])}return n+=u,i.ptr=n,!0},readHuffmanTree:function(e,r){var a=this.HUFFMAN_LUT_BITS_MAX,n=new DataView(e,r.ptr,16);r.ptr+=16;var s=n.getInt32(0,!0);if(s<2)throw"unsupported Huffman version";var l=n.getInt32(4,!0),o=n.getInt32(8,!0),f=n.getInt32(12,!0);if(o>=f)return!1;var u=new Uint32Array(f-o);t.decodeBits(e,r,u);var d,c,h,m,g=[];for(d=o;d<f;d++)c=d-(d<l?0:l),g[c]={first:u[d-o],second:null};var p=e.byteLength-r.ptr,w=Math.ceil(p/4),x=new ArrayBuffer(4*w),y=new Uint8Array(x);y.set(new Uint8Array(e,r.ptr,p));var k,v=new Uint32Array(x),I=0,b=0;for(k=v[0],d=o;d<f;d++)c=d-(d<l?0:l),m=g[c].first,m>0&&(g[c].second=k<<I>>>32-m,32-I>=m?(I+=m,32===I&&(I=0,b++,k=v[b])):(I+=m-32,b++,k=v[b],g[c].second|=k>>>32-I));var U=0,T=0,M=new i;for(d=0;d<g.length;d++)void 0!==g[d]&&(U=Math.max(U,g[d].first));T=U>=a?a:U,U>=30&&console.log("WARning, large NUM LUT BITS IS "+U);var A,V,B,D,S,P,E=[];for(d=o;d<f;d++)if(c=d-(d<l?0:l),m=g[c].first,m>0)if(A=[m,c],m<=T)for(V=g[c].second<<T-m,B=1<<T-m,h=0;h<B;h++)E[V|h]=A;else for(V=g[c].second,P=M,D=m-1;D>=0;D--)S=V>>>D&1,S?(P.right||(P.right=new i),P=P.right):(P.left||(P.left=new i),P=P.left),0!==D||P.val||(P.val=A[1]);return{decodeLut:E,numBitsLUTQick:T,numBitsLUT:U,tree:M,stuffedData:v,srcPtr:b,bitPos:I}},readHuffman:function(e,t,i){var r,a,n,s,l,o,f,u,d,c,h=t.headerInfo,m=h.numDims,g=t.headerInfo.height,p=t.headerInfo.width,w=p*g,x=this.readHuffmanTree(e,t),y=x.decodeLut,k=x.tree,v=x.stuffedData,I=x.srcPtr,b=x.bitPos,U=x.numBitsLUTQick,T=x.numBitsLUT,M=0===t.headerInfo.imageType?128:0,A=t.pixels.resultMask,V=0;b>0&&(I++,b=0);var B,D=v[I],S=1===t.encodeMode,P=new i(w*m),E=P;for(B=0;B<h.numDims;B++){if(m>1&&(E=new i(P.buffer,w*B,w),V=0),t.headerInfo.numValidPixel===p*g)for(d=0,f=0;f<g;f++)for(u=0;u<p;u++,d++){if(a=0,s=D<<b>>>32-U,l=s,32-b<U&&(s|=v[I+1]>>>64-b-U,l=s),y[l])a=y[l][1],b+=y[l][0];else for(s=D<<b>>>32-T,l=s,32-b<T&&(s|=v[I+1]>>>64-b-T,l=s),r=k,c=0;c<T;c++)if(o=s>>>T-c-1&1,r=o?r.right:r.left,!r.left&&!r.right){a=r.val,b=b+c+1;break}b>=32&&(b-=32,I++,D=v[I]),n=a-M,S?(n+=u>0?V:f>0?E[d-p]:V,n&=255,E[d]=n,V=n):E[d]=n}else for(d=0,f=0;f<g;f++)for(u=0;u<p;u++,d++)if(A[d]){if(a=0,s=D<<b>>>32-U,l=s,32-b<U&&(s|=v[I+1]>>>64-b-U,l=s),y[l])a=y[l][1],b+=y[l][0];else for(s=D<<b>>>32-T,l=s,32-b<T&&(s|=v[I+1]>>>64-b-T,l=s),r=k,c=0;c<T;c++)if(o=s>>>T-c-1&1,r=o?r.right:r.left,!r.left&&!r.right){a=r.val,b=b+c+1;break}b>=32&&(b-=32,I++,D=v[I]),n=a-M,S?(u>0&&A[d-1]?n+=V:f>0&&A[d-p]?n+=E[d-p]:n+=V,n&=255,E[d]=n,V=n):E[d]=n}t.ptr=t.ptr+4*(I+1)+(b>0?4:0)}t.pixels.resultPixels=P},decodeBits:function(t,i,r,a,n){var s=i.headerInfo,l=s.fileVersion,o=0,f=t.byteLength-i.ptr>=5?5:t.byteLength-i.ptr,u=new DataView(t,i.ptr,f),d=u.getUint8(0);o++;var c=d>>6,h=0===c?4:3-c,m=(32&d)>0,g=31&d,p=0;if(1===h)p=u.getUint8(o),o++;else if(2===h)p=u.getUint16(o,!0),o+=2;else{if(4!==h)throw"Invalid valid pixel count type";p=u.getUint32(o,!0),o+=4}var w,x,y,k,v,I,b,U,T,M=2*s.maxZError,A=s.numDims>1?s.maxValues[n]:s.zMax;if(m){i.counter.lut++,U=u.getUint8(o),o++,k=Math.ceil((U-1)*g/8),v=Math.ceil(k/4),x=new ArrayBuffer(4*v),y=new Uint8Array(x),i.ptr+=o,y.set(new Uint8Array(t,i.ptr,k)),b=new Uint32Array(x),i.ptr+=k,T=0;while(U-1>>>T)T++;k=Math.ceil(p*T/8),v=Math.ceil(k/4),x=new ArrayBuffer(4*v),y=new Uint8Array(x),y.set(new Uint8Array(t,i.ptr,k)),w=new Uint32Array(x),i.ptr+=k,I=l>=3?e.unstuffLUT2(b,g,U-1,a,M,A):e.unstuffLUT(b,g,U-1,a,M,A),l>=3?e.unstuff2(w,r,T,p,I):e.unstuff(w,r,T,p,I)}else i.counter.bitstuffer++,T=g,i.ptr+=o,T>0&&(k=Math.ceil(p*T/8),v=Math.ceil(k/4),x=new ArrayBuffer(4*v),y=new Uint8Array(x),y.set(new Uint8Array(t,i.ptr,k)),w=new Uint32Array(x),i.ptr+=k,l>=3?null===a?e.originalUnstuff2(w,r,T,p):e.unstuff2(w,r,T,p,!1,a,M,A):null===a?e.originalUnstuff(w,r,T,p):e.unstuff(w,r,T,p,!1,a,M,A))},readTiles:function(e,i,r){var a=i.headerInfo,n=a.width,s=a.height,l=a.microBlockSize,o=a.imageType,f=t.getDataTypeSize(o),u=Math.ceil(n/l),d=Math.ceil(s/l);i.pixels.numBlocksY=d,i.pixels.numBlocksX=u,i.pixels.ptr=0;var c,h,m,g,p,w,x,y,k,v=0,I=0,b=0,U=0,T=0,M=0,A=0,V=0,B=0,D=0,S=0,P=0,E=0,C=0,F=0,L=0,R=new r(l*l),O=s%l||l,N=n%l||l,z=a.numDims,H=i.pixels.resultMask,_=i.pixels.resultPixels;for(b=0;b<d;b++)for(T=b!==d-1?l:O,U=0;U<u;U++)for(M=U!==u-1?l:N,S=b*n*l+U*l,P=n-M,k=0;k<z;k++){if(z>1&&(_=new r(i.pixels.resultPixels.buffer,n*s*k*f,n*s)),A=e.byteLength-i.ptr,c=new DataView(e,i.ptr,Math.min(10,A)),h={},L=0,V=c.getUint8(0),L++,B=V>>6&255,D=V>>2&15,D!==(U*l>>3&15))throw"integrity issue";if(w=3&V,w>3)throw i.ptr+=L,"Invalid block encoding ("+w+")";if(2!==w)if(0===w){if(i.counter.uncompressed++,i.ptr+=L,E=T*M*f,C=e.byteLength-i.ptr,E=E<C?E:C,m=new ArrayBuffer(E%f===0?E:E+f-E%f),g=new Uint8Array(m),g.set(new Uint8Array(e,i.ptr,E)),p=new r(m),F=0,H)for(v=0;v<T;v++){for(I=0;I<M;I++)H[S]&&(_[S]=p[F++]),S++;S+=P}else for(v=0;v<T;v++){for(I=0;I<M;I++)_[S++]=p[F++];S+=P}i.ptr+=F*f}else if(x=t.getDataTypeUsed(o,B),y=t.getOnePixel(h,L,x,c),L+=t.getDataTypeSize(x),3===w)if(i.ptr+=L,i.counter.constantoffset++,H)for(v=0;v<T;v++){for(I=0;I<M;I++)H[S]&&(_[S]=y),S++;S+=P}else for(v=0;v<T;v++){for(I=0;I<M;I++)_[S++]=y;S+=P}else if(i.ptr+=L,t.decodeBits(e,i,R,y,k),L=0,H)for(v=0;v<T;v++){for(I=0;I<M;I++)H[S]&&(_[S]=R[L++]),S++;S+=P}else for(v=0;v<T;v++){for(I=0;I<M;I++)_[S++]=R[L++];S+=P}else i.counter.constant++,i.ptr+=L}},formatFileInfo:function(e){return{fileIdentifierString:e.headerInfo.fileIdentifierString,fileVersion:e.headerInfo.fileVersion,imageType:e.headerInfo.imageType,height:e.headerInfo.height,width:e.headerInfo.width,numValidPixel:e.headerInfo.numValidPixel,microBlockSize:e.headerInfo.microBlockSize,blobSize:e.headerInfo.blobSize,maxZError:e.headerInfo.maxZError,pixelType:t.getPixelType(e.headerInfo.imageType),eofOffset:e.eofOffset,mask:e.mask?{numBytes:e.mask.numBytes}:null,pixels:{numBlocksX:e.pixels.numBlocksX,numBlocksY:e.pixels.numBlocksY,maxValue:e.headerInfo.zMax,minValue:e.headerInfo.zMin,noDataValue:e.noDataValue}}},constructConstantSurface:function(e){var t=e.headerInfo.zMax,i=e.headerInfo.numDims,r=e.headerInfo.height*e.headerInfo.width,a=r*i,n=0,s=0,l=0,o=e.pixels.resultMask;if(o)if(i>1)for(n=0;n<i;n++)for(l=n*r,s=0;s<r;s++)o[s]&&(e.pixels.resultPixels[l+s]=t);else for(s=0;s<r;s++)o[s]&&(e.pixels.resultPixels[s]=t);else if(e.pixels.resultPixels.fill)e.pixels.resultPixels.fill(t);else for(s=0;s<a;s++)e.pixels.resultPixels[s]=t},getDataTypeArray:function(e){var t;switch(e){case 0:t=Int8Array;break;case 1:t=Uint8Array;break;case 2:t=Int16Array;break;case 3:t=Uint16Array;break;case 4:t=Int32Array;break;case 5:t=Uint32Array;break;case 6:t=Float32Array;break;case 7:t=Float64Array;break;default:t=Float32Array}return t},getPixelType:function(e){var t;switch(e){case 0:t="S8";break;case 1:t="U8";break;case 2:t="S16";break;case 3:t="U16";break;case 4:t="S32";break;case 5:t="U32";break;case 6:t="F32";break;case 7:t="F64";break;default:t="F32"}return t},isValidPixelValue:function(e,t){if(null===t)return!1;var i;switch(e){case 0:i=t>=-128&&t<=127;break;case 1:i=t>=0&&t<=255;break;case 2:i=t>=-32768&&t<=32767;break;case 3:i=t>=0&&t<=65536;break;case 4:i=t>=-2147483648&&t<=2147483647;break;case 5:i=t>=0&&t<=4294967296;break;case 6:i=t>=-34027999387901484e22&&t<=34027999387901484e22;break;case 7:i=t>=5e-324&&t<=17976931348623157e292;break;default:i=!1}return i},getDataTypeSize:function(e){var t=0;switch(e){case 0:case 1:t=1;break;case 2:case 3:t=2;break;case 4:case 5:case 6:t=4;break;case 7:t=8;break;default:t=e}return t},getDataTypeUsed:function(e,t){var i=e;switch(e){case 2:case 4:i=e-t;break;case 3:case 5:i=e-2*t;break;case 6:i=0===t?e:1===t?2:1;break;case 7:i=0===t?e:e-2*t+1;break;default:i=e;break}return i},getOnePixel:function(e,t,i,r){var a=0;switch(i){case 0:a=r.getInt8(t);break;case 1:a=r.getUint8(t);break;case 2:a=r.getInt16(t,!0);break;case 3:a=r.getUint16(t,!0);break;case 4:a=r.getInt32(t,!0);break;case 5:a=r.getUInt32(t,!0);break;case 6:a=r.getFloat32(t,!0);break;case 7:a=r.getFloat64(t,!0);break;default:throw"the decoder does not understand this pixel type"}return a}},i=function(e,t,i){this.val=e,this.left=t,this.right=i},r={decode:function(e,i){i=i||{};var r=i.noDataValue,a=0,n={};if(n.ptr=i.inputOffset||0,n.pixels={},t.readHeaderInfo(e,n)){var s=n.headerInfo,l=s.fileVersion,o=t.getDataTypeArray(s.imageType);t.readMask(e,n),s.numValidPixel===s.width*s.height||n.pixels.resultMask||(n.pixels.resultMask=i.maskData);var f,u=s.width*s.height;if(n.pixels.resultPixels=new o(u*s.numDims),n.counter={onesweep:0,uncompressed:0,lut:0,bitstuffer:0,constant:0,constantoffset:0},0!==s.numValidPixel)if(s.zMax===s.zMin)t.constructConstantSurface(n);else if(l>=4&&t.checkMinMaxRanges(e,n))t.constructConstantSurface(n);else{var d=new DataView(e,n.ptr,2),c=d.getUint8(0);if(n.ptr++,c)t.readDataOneSweep(e,n,o);else if(l>1&&s.imageType<=1&&Math.abs(s.maxZError-.5)<1e-5){var h=d.getUint8(1);if(n.ptr++,n.encodeMode=h,h>2||l<4&&h>1)throw"Invalid Huffman flag "+h;h?t.readHuffman(e,n,o):t.readTiles(e,n,o)}else t.readTiles(e,n,o)}n.eofOffset=n.ptr,i.inputOffset?(f=n.headerInfo.blobSize+i.inputOffset-n.ptr,Math.abs(f)>=1&&(n.eofOffset=i.inputOffset+n.headerInfo.blobSize)):(f=n.headerInfo.blobSize-n.ptr,Math.abs(f)>=1&&(n.eofOffset=n.headerInfo.blobSize));var m={width:s.width,height:s.height,pixelData:n.pixels.resultPixels,minValue:s.zMin,maxValue:s.zMax,validPixelCount:s.numValidPixel,dimCount:s.numDims,dimStats:{minValues:s.minValues,maxValues:s.maxValues},maskData:n.pixels.resultMask};if(n.pixels.resultMask&&t.isValidPixelValue(s.imageType,r)){var g=n.pixels.resultMask;for(a=0;a<u;a++)g[a]||(m.pixelData[a]=r);m.noDataValue=r}return n.noDataValue=r,i.returnFileInfo&&(m.fileInfo=t.formatFileInfo(n)),m}},getBandCount:function(e){var i=0,r=0,a={ptr:0,pixels:{}};while(r<e.byteLength-58)t.readHeaderInfo(e,a),r+=a.headerInfo.blobSize,i++,a.ptr=r;return i}};return r}(),i=function(){var e=new ArrayBuffer(4),t=new Uint8Array(e),i=new Uint32Array(e);return i[0]=1,1===t[0]}(),r={decode:function(r,a){if(!i)throw"Big endian system is not supported.";a=a||{};var n,s,l=a.inputOffset||0,o=new Uint8Array(r,l,10),f=String.fromCharCode.apply(null,o);if("CntZImage"===f.trim())n=e,s=1;else{if("Lerc2"!==f.substring(0,5))throw"Unexpected file identifier string: "+f;n=t,s=2}var u,d,c,h,m,g,p=0,w=r.byteLength-10,x=[],y={width:0,height:0,pixels:[],pixelType:a.pixelType,mask:null,statistics:[]};while(l<w){var k=n.decode(r,{inputOffset:l,encodedMaskData:u,maskData:c,returnMask:0===p,returnEncodedMask:0===p,returnFileInfo:!0,pixelType:a.pixelType||null,noDataValue:a.noDataValue||null});l=k.fileInfo.eofOffset,0===p&&(u=k.encodedMaskData,c=k.maskData,y.width=k.width,y.height=k.height,y.dimCount=k.dimCount||1,y.pixelType=k.pixelType||k.fileInfo.pixelType,y.mask=k.maskData),s>1&&k.fileInfo.mask&&k.fileInfo.mask.numBytes>0&&x.push(k.maskData),p++,y.pixels.push(k.pixelData),y.statistics.push({minValue:k.minValue,maxValue:k.maxValue,noDataValue:k.noDataValue,dimStats:k.dimStats})}if(s>1&&x.length>1){for(g=y.width*y.height,y.bandMasks=x,c=new Uint8Array(g),c.set(x[0]),h=1;h<x.length;h++)for(d=x[h],m=0;m<g;m++)c[m]=c[m]&d[m];y.maskData=c}return y}};S.Lerc=r})();var P,E,C=S.Lerc;function F(e,t){if(e.encoding===T.LERC){var i;try{i=C.decode(e.heightmap)}catch(u){throw new l.RuntimeError(u)}var a=i.statistics[0];if(a.minValue===Number.MAX_VALUE)throw new l.RuntimeError("Invalid tile data");e.heightmap=i.pixels[0],e.width=i.width,e.height=i.height}e.ellipsoid=r.Ellipsoid.clone(e.ellipsoid),e.rectangle=r.Rectangle.clone(e.rectangle);var n,s=M.computeVertices(e),o=s.vertices;n=e.skirtHeight>0?b.TerrainProvider.getRegularGridAndSkirtIndicesAndEdgeIndices(e.width,e.height):b.TerrainProvider.getRegularGridIndicesAndEdgeIndices(e.width,e.height);var f=k.CreatePhysicalArray.createPhysicalArrayFromTerrain(P,E,e.relativeToCenter,s.positions,n.indices);return t.push(o.buffer,f.buffer),{vertices:o.buffer,numberOfAttributes:s.encoding.getStride(),minimumHeight:s.minimumHeight,maximumHeight:s.maximumHeight,gridWidth:e.width,gridHeight:e.height,boundingSphere3D:s.boundingSphere3D,orientedBoundingBox:s.orientedBoundingBox,occludeePointInScaledSpace:s.occludeePointInScaledSpace,encoding:s.encoding,westIndicesSouthToNorth:s.westIndicesSouthToNorth,southIndicesEastToWest:s.southIndicesEastToWest,eastIndicesNorthToSouth:s.eastIndicesNorthToSouth,northIndicesWestToEast:s.northIndicesWestToEast,physicalArray:f}}function L(t){var i=t.data,r=i.webAssemblyConfig;e.defined(r)&&fetch(r.wasmBinaryFileES6).then((e=>e.arrayBuffer())).then((function(e){var t={wasmBinary:e,onModuleLoaded:function(e){P=e,E=new P.LBSpaMgr,self.onmessage=p(F),self.postMessage(!0)}};v.materem(t)}))}return L}));