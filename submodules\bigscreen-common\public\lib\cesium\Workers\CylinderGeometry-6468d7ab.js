/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./CylinderGeometryLibrary-76efdd62","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889","./VertexFormat-86c096b8"],(function(t,e,n,a,o,r,i,s,m,u,c,d){"use strict";const l=new n.Cartesian2,p=new n.Cartesian3,f=new n.Cartesian3,y=new n.Cartesian3,b=new n.Cartesian3;function A(t){const e=(t=r.defaultValue(t,r.defaultValue.EMPTY_OBJECT)).length,n=t.topRadius,a=t.bottomRadius,o=r.defaultValue(t.vertexFormat,d.VertexFormat.DEFAULT),i=r.defaultValue(t.slices,128);this._length=e,this._topRadius=n,this._bottomRadius=a,this._vertexFormat=d.VertexFormat.clone(o),this._slices=i,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderGeometry"}A.packedLength=d.VertexFormat.packedLength+5,A.pack=function(t,e,n){return n=r.defaultValue(n,0),d.VertexFormat.pack(t._vertexFormat,e,n),n+=d.VertexFormat.packedLength,e[n++]=t._length,e[n++]=t._topRadius,e[n++]=t._bottomRadius,e[n++]=t._slices,e[n]=r.defaultValue(t._offsetAttribute,-1),e};const g=new d.VertexFormat,x={vertexFormat:g,length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,offsetAttribute:void 0};let _;A.unpack=function(t,e,n){e=r.defaultValue(e,0);const a=d.VertexFormat.unpack(t,e,g);e+=d.VertexFormat.packedLength;const o=t[e++],i=t[e++],s=t[e++],m=t[e++],u=t[e];return r.defined(n)?(n._vertexFormat=d.VertexFormat.clone(a,n._vertexFormat),n._length=o,n._topRadius=i,n._bottomRadius=s,n._slices=m,n._offsetAttribute=-1===u?void 0:u,n):(x.length=o,x.topRadius=i,x.bottomRadius=s,x.slices=m,x.offsetAttribute=-1===u?void 0:u,new A(x))},A.createGeometry=function(t){let d=t._length;const A=t._topRadius,g=t._bottomRadius,x=t._vertexFormat,_=t._slices;if(d<=0||A<0||g<0||0===A&&0===g)return;const h=_+_,C=_+h,F=h+h,v=o.CylinderGeometryLibrary.computePositions(d,A,g,_,!0),w=x.st?new Float32Array(2*F):void 0,G=x.normal?new Float32Array(3*F):void 0,D=x.tangent?new Float32Array(3*F):void 0,R=x.bitangent?new Float32Array(3*F):void 0;let V;const T=x.normal||x.tangent||x.bitangent;if(T){const t=x.tangent||x.bitangent;let e=0,a=0,o=0;const r=Math.atan2(g-A,d),i=p;i.z=Math.sin(r);const s=Math.cos(r);let m=y,u=f;for(V=0;V<_;V++){const r=V/_*c.CesiumMath.TWO_PI,d=s*Math.cos(r),l=s*Math.sin(r);T&&(i.x=d,i.y=l,t&&(m=n.Cartesian3.normalize(n.Cartesian3.cross(n.Cartesian3.UNIT_Z,i,m),m)),x.normal&&(G[e++]=i.x,G[e++]=i.y,G[e++]=i.z,G[e++]=i.x,G[e++]=i.y,G[e++]=i.z),x.tangent&&(D[a++]=m.x,D[a++]=m.y,D[a++]=m.z,D[a++]=m.x,D[a++]=m.y,D[a++]=m.z),x.bitangent&&(u=n.Cartesian3.normalize(n.Cartesian3.cross(i,m,u),u),R[o++]=u.x,R[o++]=u.y,R[o++]=u.z,R[o++]=u.x,R[o++]=u.y,R[o++]=u.z))}for(V=0;V<_;V++)x.normal&&(G[e++]=0,G[e++]=0,G[e++]=-1),x.tangent&&(D[a++]=1,D[a++]=0,D[a++]=0),x.bitangent&&(R[o++]=0,R[o++]=-1,R[o++]=0);for(V=0;V<_;V++)x.normal&&(G[e++]=0,G[e++]=0,G[e++]=1),x.tangent&&(D[a++]=1,D[a++]=0,D[a++]=0),x.bitangent&&(R[o++]=0,R[o++]=1,R[o++]=0)}const O=12*_-12,L=u.IndexDatatype.createTypedArray(F,O);let P=0,M=0;for(V=0;V<_-1;V++)L[P++]=M,L[P++]=M+2,L[P++]=M+3,L[P++]=M,L[P++]=M+3,L[P++]=M+1,M+=2;for(L[P++]=h-2,L[P++]=0,L[P++]=1,L[P++]=h-2,L[P++]=1,L[P++]=h-1,V=1;V<_-1;V++)L[P++]=h+V+1,L[P++]=h+V,L[P++]=h;for(V=1;V<_-1;V++)L[P++]=C,L[P++]=C+V,L[P++]=C+V+1;let k=0;if(x.st){const t=Math.max(A,g);for(V=0;V<F;V++){const e=n.Cartesian3.fromArray(v,3*V,b);w[k++]=(e.x+t)/(2*t),w[k++]=(e.y+t)/(2*t)}}const z=new s.GeometryAttributes;x.position&&(z.position=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:v})),x.normal&&(z.normal=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G})),x.tangent&&(z.tangent=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:D})),x.bitangent&&(z.bitangent=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),x.st&&(z.st=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:w})),l.x=.5*d,l.y=Math.max(g,A);const E=new e.BoundingSphere(n.Cartesian3.ZERO,n.Cartesian2.magnitude(l));if(r.defined(t._offsetAttribute)){d=v.length;const e=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(d/3).fill(e);z.applyOffset=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new i.Geometry({attributes:z,indices:L,primitiveType:i.PrimitiveType.TRIANGLES,boundingSphere:E,offsetAttribute:t._offsetAttribute})},A.getUnitCylinder=function(){return r.defined(_)||(_=A.createGeometry(new A({topRadius:1,bottomRadius:1,length:1,vertexFormat:d.VertexFormat.POSITION_ONLY}))),_},t.CylinderGeometry=A}));
