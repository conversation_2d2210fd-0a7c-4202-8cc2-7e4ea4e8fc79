define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./GeometryInstance-d4317835","./arrayRemoveDuplicates-7c710eac","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./OrientedBoundingBox-d0a49c02","./CoplanarPolygonGeometryLibrary-b66dad27","./ArcType-10662e8b","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./PolygonGeometryLibrary-4b76b18a"],(function(e,r,t,n,o,i,a,y,c,l,p,d,s,u,f,m,g,b,h,v,P,G,C,k,L,T,E,H,A){"use strict";function w(e){for(var r=e.length,t=new Float64Array(3*r),n=g.IndexDatatype.createTypedArray(r,2*r),o=0,i=0,a=0;a<r;a++){var y=e[a];t[o++]=y.x,t[o++]=y.y,t[o++]=y.z,n[i++]=a,n[i++]=(a+1)%r}var c=new s.GeometryAttributes({position:new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:t})});return new p.Geometry({attributes:c,indices:n,primitiveType:d.PrimitiveType.LINES})}function x(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT);var n=t.polygonHierarchy;r.Check.defined("options.polygonHierarchy",n),this._polygonHierarchy=n,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=A.PolygonGeometryLibrary.computeHierarchyPackedLength(n)+1}x.fromPositions=function(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT),r.Check.defined("options.positions",t.positions);var n={polygonHierarchy:{positions:t.positions}};return new x(n)},x.pack=function(t,n,o){return r.Check.typeOf.object("value",t),r.Check.defined("array",n),o=e.defaultValue(o,0),o=A.PolygonGeometryLibrary.packPolygonHierarchy(t._polygonHierarchy,n,o),n[o]=t.packedLength,n};var B={polygonHierarchy:{}};function I(r,t){return e.defined(t)&&(r=x.unpack(r,t)),r._ellipsoid=n.Ellipsoid.clone(r._ellipsoid),x.createGeometry(r)}return x.unpack=function(t,n,o){r.Check.defined("array",t),n=e.defaultValue(n,0);var i=A.PolygonGeometryLibrary.unpackPolygonHierarchy(t,n);n=i.startingIndex,delete i.startingIndex;var a=t[n];return e.defined(o)||(o=new x(B)),o._polygonHierarchy=i,o.packedLength=a,o},x.createGeometry=function(e){var r=e._polygonHierarchy,n=r.positions;if(n=P.arrayRemoveDuplicates(n,t.Cartesian3.equalsEpsilon,!0),!(n.length<3)){var i=L.CoplanarPolygonGeometryLibrary.validOutline(n);if(i){var a=A.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(r,!1);if(0!==a.length){for(var y=[],c=0;c<a.length;c++){var l=new v.GeometryInstance({geometry:w(a[c])});y.push(l)}var d=f.GeometryPipeline.combineInstances(y)[0],s=o.BoundingSphere.fromPoints(r.positions);return new p.Geometry({attributes:d.attributes,indices:d.indices,primitiveType:d.primitiveType,boundingSphere:s})}}}},I}));