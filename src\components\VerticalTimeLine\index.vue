<template>
  <div class="time-line">
    <div class="line"></div>
    <div class="month-list">
      <div
        :title="item.name"
        class="month"
        :class="item.selected ? 'month-select' : ''"
        v-for="(item, index) in monthList"
        :key="item.name"
        @click="selectMonth(item, index)">
        <div v-show="item.name.split('年')[1].split('月')[0] == 1" class="month-name">{{ item.name.split('年')[0] }}年</div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import moment from 'moment'
  const monthList = ref([])

  const model = defineModel()
  const emits = defineEmits(['change'])

  const selectedMonth = computed(() => {
    return monthList.value.filter((_) => _.selected)
  })
  const selectedMonthCount = computed(() => {
    return selectedMonth.value.length
  })

  const selectMonth = (item, index) => {
    item.selected = !item.selected
    console.log(selectedMonthCount.value)
    if (selectedMonthCount.value > 2) {
      monthList.value.forEach((item) => {
        item.selected = item.index === index
      })
    }
    if (selectedMonthCount.value > 1) {
      const startIndex = selectedMonth.value[0].index
      const endIndex = selectedMonth.value[selectedMonthCount.value - 1].index
      monthList.value.forEach((item) => {
        item.selected = item.index >= startIndex && item.index <= endIndex
      })
    }
    setValue()
  }

  const setValue = () => {
    let startDate = null
    let endDate = null
    if (selectedMonthCount.value === 0) {
      model.value = null
      return
    }
    if (selectedMonthCount.value === 1) {
      startDate = moment(selectedMonth.value[0].value).format('YYYY-MM-DD 00:00:00')
      endDate = moment(selectedMonth.value[0].value).endOf('month').format('YYYY-MM-DD 23:59:59')
    } else {
      endDate = moment(selectedMonth.value[0].value).endOf('month').format('YYYY-MM-DD 23:59:59')
      startDate = moment(selectedMonth.value[selectedMonthCount.value - 1].value).format('YYYY-MM-DD 00:00:00')
    }
    model.value = [startDate, endDate]
    emits('change', model.value)
  }

  onMounted(() => {
    for (let i = 0; i < 36; i++) {
      monthList.value.push({
        // selected: i === 0,
        selected: i <= 12,
        name: moment().add(-i, 'month').format('YYYY年MM月'),
        value: moment().add(-i, 'month').format('YYYY-MM-01'),
        index: i
      })
    }
    // model.value = [moment().format('YYYY-MM-01 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')]
    model.value = [moment().add(-1, 'year').format('YYYY-MM-01 00:00:00'), moment().endOf('month').format('YYYY-MM-DD 23:59:59')]
  })
</script>
<style lang="scss" scoped>
  .time-line {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 38px;
    height: 900px;
    background: #06182d;
    border: 1px solid #707070;
    border-radius: 100px;
    opacity: 0.8;
    .line {
      position: absolute;
      width: 0;
      height: 840px;
      margin-top: 20px;
      margin-left: 17px;
      border: 1px solid #ffffff;
      opacity: 0.5;
    }
    .month-list {
      .month {
        position: relative;
        width: 9px;
        height: 9px;
        margin-top: 15px;
        margin-left: 14px;
        cursor: pointer;
        background: #ffffff;
        border-radius: 50%;
        opacity: 1;
        .month-name {
          position: absolute;
          top: -50%;
          left: 27px;
          width: 60px;
          color: #ffffff;
        }
      }
      .month-select {
        background: #038df6;
        box-shadow: 0 0 7px 1px #038df6;
        opacity: 1;
      }
    }
  }
</style>
