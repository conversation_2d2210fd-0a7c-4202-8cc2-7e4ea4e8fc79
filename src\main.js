import { createApp } from 'vue'
import * as echarts from 'echarts'

import App from './App.vue'

import '@/assets/font/font.css' // 引入字体

// import '@/style/var.scss'
import 'element-plus/dist/index.css'
// import 'suit-datav/lib/styles/index.css'
import './permission.js'
import 'animate.css'
import 'vue3-video-play/dist/style.css' // 引入css
import 'virtual:uno.css'
import router from './router'
import directives from '@Common/directives' // 全局指令
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { createPinia } from 'pinia'
// import BinDatav from 'suit-datav'
import vue3videoPlay from 'vue3-video-play' // 引入组件
import znyg from 'znyg-frontend-common'
import ElementPlusX from 'vue-element-plus-x'

const app = createApp(App)
// console.log(import.meta.env.VITE_BASE_URL, process.env.NODE_ENV)

// if (process.env.NODE_ENV !== 'development') {
//   // 只在开发环境打印到控制台
//   console.log = function() {}
//   console.error = function() {}
// }
app.use(znyg)
app.use(ElementPlus, { locale: zhCn })
app.use(router)
app.use(vue3videoPlay)
app.use(createPinia())
app.use(ElementPlusX)

// app.use(BinDatav)
app.use(directives)
app.config.globalProperties.$echarts = echarts
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
