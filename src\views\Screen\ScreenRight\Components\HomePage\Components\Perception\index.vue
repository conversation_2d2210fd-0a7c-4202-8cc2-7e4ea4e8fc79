<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-07 14:49:12
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 10:51:44
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\Components\HomePage\Components\Perception\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="perception-container">
    <HeadLine>
      <template #title>态势感知</template>
      <div class="content">
        <el-row class="h-687">
          <el-col :span="12">
            <Device></Device>
          </el-col>
          <el-col :span="12">
            <Monitor></Monitor>
          </el-col>
        </el-row>
        <el-row>
          <Traffic></Traffic>
        </el-row>
      </div>
    </HeadLine>
  </div>
</template>

<script setup>
  import Device from './Components/Device/index.vue'
  import Monitor from './Components/Monitor/index.vue'
  import Traffic from './Components/Traffic/index.vue'
  import HeadLine from '@/components/HeadLine/index.vue'
  defineOptions({
    name: 'Perception'
  })
</script>

<style lang="scss" scoped>
  .perception-container {
    .content {
      width: 1284px;
      height: 1130px;
    }
  }
</style>
