/*
 * @Description:存储页面弹出框相关数据
 * @Autor: wangjialing
 * @Date: 2023-06-28 09:30:13
 * @LastEditors: wangjialing
 * @LastEditTime: 2023-06-28 17:23:32
 */

import { defineStore } from 'pinia'

const useScreenPopWindowStore = defineStore('screenPopWindowData', {
  state: () => ({
    /**
     * @description: 通过render函数渲染的节点   格式：{ tag: tag, node: mountNode }
     */
    dialogNodes: [],
    testNum: 0,

    screenPopArr: []
  }),
  getters: {},
  actions: {}
})

export default useScreenPopWindowStore
