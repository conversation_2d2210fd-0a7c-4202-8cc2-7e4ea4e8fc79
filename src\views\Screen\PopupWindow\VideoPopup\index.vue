
<template>
  <PopupBg width="1573px" height="728px" :title="title">
    <videoPlay type="m3u8" width="1485px" height="630px" :src="url" auto-play muted :control="false"></videoPlay>
  </PopupBg>
</template>

<script setup lang="ts">
  import PopupBg from '@/components/PopupBg/index.vue'
  import { videoPlay } from 'vue3-video-play'

  defineProps({
    url: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  })
</script>

<style scoped>
  :deep(.d-player-wrap) {
    background-color: transparent !important;
  }
</style>
