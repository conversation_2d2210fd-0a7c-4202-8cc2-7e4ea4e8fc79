<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 11:30:51
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 16:47:27
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\EmergencyResources\device.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="device-container">
    <SubHeadLine>设备</SubHeadLine>
    <!-- <pieChart :chartData="chartDataDevice" :chartTitle="'设备'"></pieChart> -->
    <ThreePie :data="chartDataDevice" :chartTitle="'设备'"></ThreePie>
  </div>
</template>

<script setup>
  import pieChart from './pieChart.vue'
  import ThreePie from './threePie.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  const chartDataDevice = ref([
    { value: 2, name: '空压机' },
    { value: 2, name: '融雪撒布机' },
    { value: 2, name: '排水泵' },
    { value: 2, name: '潜水泵' },
    { value: 2, name: '小型燃气发电机组' }
  ])
</script>

<style lang="scss" scoped>
  .device-container {
    width: 288px;
    height: 160px;
  }
</style>
