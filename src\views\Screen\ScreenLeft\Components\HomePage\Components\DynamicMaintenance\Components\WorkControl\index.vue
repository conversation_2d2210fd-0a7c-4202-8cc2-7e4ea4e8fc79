<template>
  <div class="work-control-container">
    <div class="relative">
      <SubTitle @tab-change="changeTab('click', $event)"></SubTitle>
      <DatePicker
        :type="tabCur === 1 ? 'month' : 'date'"
        v-model="date"
        :clearable="false"
        @change="handleDateChange"
        :disabled-date="disabledDate"
        style="top: -14px; right: 0"></DatePicker>
    </div>
    <div class="flex justify-between">
      <div class="tab flex-col">
        <div class="tab-item" :class="item.active ? 'active' : ''" v-for="(item, index) in tabList" :key="index" @click="handlerTab(item)">
          <div class="content">
            <img :src="getAssetsFile(`ScreenLeft/DynamicMaintenance/${item.icon}.png`)" />
          </div>
          <div class="text">
            <span>{{ tabCur === 0 ? item.name : item.defectName }}</span>
            <div class="num" :style="{ color: item.color }">{{ item.num }}</div>
          </div>
        </div>
      </div>
      <div class="chart-box flex-col">
        <div v-for="item in chartData" :key="item.id" class="chart-item">
          <SmallHeadLine>
            <template #title>{{ item.name }}</template>
            <Mychart :option="item.barData" width="394px" height="153px"></Mychart>
            <div class="barBg"></div>
          </SmallHeadLine>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import DatePicker from '@/components/DatePicker/index.vue'
  import SmallHeadLine from '@/components/SmallHeadLine/index.vue'
  import SubTitle from '@/components/SubTitle/index.vue'
  import WorkList from '@/views/Screen/MapPopupWindow/Components/WorkList/index.vue'
  import Mychart from '@Common/components/MyChart/index.vue'
  const { appContext } = getCurrentInstance()

  import { getAssetsFile } from '@/utils'
  import lib from '@/utils/lib.ts'
  const tabCur = ref(0)
  const date = ref('')
  const tabList = ref([
    {
      name: '总数',
      defectName: '总数',
      icon: 'sumIcon',
      num: 0,
      color: '#48E6FF',
      active: false
    },
    {
      name: '已完成',
      defectName: '已处理',
      icon: 'finishIcon',
      num: 0,
      color: '#48FFB6',
      active: false
    },
    {
      name: '未完成',
      defectName: '未处理',
      icon: 'unfinishIcon',
      num: 0,
      color: '#FFD448',
      active: false
    }
  ])
  const workList = ref([])
  const planList = ref([])
  const defectList = ref([])
  const chartData = ref([])
  const planAndFinishCountMap = ref({})
  const defectAndRepairCountMap = ref({})
  const specialTypeMap = ref({
    nameList: [],
    dataList: []
  })
  const workPlanTypeMap = ref({
    nameList: [],
    dataList: []
  })
  const defectSourceDataMap = ref({
    nameList: [],
    dataList: []
  })
  const defectDistributionMap = ref({
    nameList: [],
    dataList: []
  })
  const colorList = [
    ['#4ebdff', '#0369a8', '#2590d4', '#259cf4'], // 新增左侧面和右侧面颜色
    ['#2860c9', '#2358b6', '#194e9c', '#2164e0'],
    ['#28f2f2', '#0496a5', '#1aadb4', '#1ecfd3'],
    ['#4effdb', '#069870', '#1baf8b', '#23ebb6'],
    ['#ffe74e', '#a28309', '#eace25', '#d6bb25']
  ]
  let status = ''
  onMounted(() => {
    date.value = moment().format('YYYY-MM-DD')
    // getAllData()
    useIntervalFn(
      () => {
        getAllData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })
  const changeTab = (type, index) => {
    if (type == 'click') {
      tabList.value.forEach((data) => {
        data.active = false
      })
      lib.popWindow.removeDialog('WorkList')
    }
    tabCur.value = index
    if (index === 0) {
      tabList.value[0].num = planAndFinishCountMap.value.planCount
      tabList.value[1].num = planAndFinishCountMap.value.finishCount
      tabList.value[2].num = planAndFinishCountMap.value.unFinishCount
      chartData.value = [
        {
          id: 1,
          name: '养护作业单类型'
        },
        {
          id: 2,
          name: '养护专业类型'
        }
      ]
      chartData.value[0].barData = upDateYhBar(workPlanTypeMap.value.nameList, workPlanTypeMap.value.dataList)
      chartData.value[1].barData = upDateYhBar(specialTypeMap.value.nameList, specialTypeMap.value.dataList)
    } else {
      tabList.value[0].num = defectAndRepairCountMap.value.total
      tabList.value[1].num = defectAndRepairCountMap.value.repairCount
      tabList.value[2].num = defectAndRepairCountMap.value.total - defectAndRepairCountMap.value.repairCount
      chartData.value = [
        {
          id: 1,
          name: '缺陷来源'
        },
        {
          id: 2,
          name: '缺陷分类'
        }
      ]
      chartData.value[0].barData = upDateBhBar(defectSourceDataMap.value.nameList, defectSourceDataMap.value.dataList)
      chartData.value[1].barData = upDateBhBar(defectDistributionMap.value.nameList, defectDistributionMap.value.dataList)
    }
  }
  const handleDateChange = (val) => {
    getAllData()
  }
  const handlerTab = (item) => {
    lib.popWindow.removeDialog('WorkList')
    status = item.name == '总数' ? '' : item.name == '未完成' || item.name == '未处理' ? 'unfinish' : 'finish'
    getData(status)
    tabList.value.forEach((data) => {
      data.active = item.name === data.name ? !data.active : false
    })
    if (item.active) {
      workList.value = tabCur.value ? defectList.value.find((_) => _.name === item.defectName).list : planList.value.find((_) => _.name === item.name).list
      lib.popWindow.createPopWindow(
        './Components/WorkList/index.vue',
        {
          left: 1596,
          top: 205,
          tag: 'WorkList',
          appContext,
          appendParent: 'player',
          closeFunc: () => {}
        },
        {
          title: tabCur.value == 0 ? '作业列表' : '缺陷列表',
          list: workList.value
        }
      )
    }
  }
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
  const upDateYhBar = (xData, barData) => {
    return {
      grid: {
        top: '18%',
        bottom: '1%',
        left: '10',
        right: '10',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = `${params[0].name}:${params[0].value}`
          return result
        }
      },
      xAxis: {
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 13
          }
          // formatter: function (value) {
          //   return value.substring(0, 2) + '\n' + value.substring(2)
          // }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#5C91C4'
          }
        },
        data: xData
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#5C91C4',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: 30,
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  x: 0,
                  x2: 1,
                  y: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: colorList[params.dataIndex][0] // 最左边
                    },
                    {
                      offset: 0.5,
                      color: colorList[params.dataIndex][1] // 左边的右边 颜色
                    },
                    {
                      offset: 0.5,
                      color: colorList[params.dataIndex][2] // 右边的左边 颜色
                    },
                    {
                      offset: 1,
                      color: colorList[params.dataIndex][3]
                    }
                  ]
                }
              }
            }
          },
          label: {
            show: false
          },
          data: barData
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: barData,
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [30, 15],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colorList[params.dataIndex][0]
              }
            }
          }
        }
      ]
    }
  }
  const upDateBhBar = (xData, barData) => {
    return {
      grid: {
        top: '18%',
        bottom: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = `${params[0].name}:${params[0].value}`
          return result
        }
      },
      xAxis: {
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 13
          }
          // formatter: function (value) {
          //   return value.substring(0, 2) + '\n' + value.substring(2)
          // }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#5C91C4'
          }
        },
        data: xData
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#5C91C4',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: 30,
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  x: 0,
                  x2: 1,
                  y: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: colorList[params.dataIndex][0] // 最左边
                    },
                    {
                      offset: 0.5,
                      color: colorList[params.dataIndex][1] // 左边的右边 颜色
                    },
                    {
                      offset: 0.5,
                      color: colorList[params.dataIndex][2] // 右边的左边 颜色
                    },
                    {
                      offset: 1,
                      color: colorList[params.dataIndex][3]
                    }
                  ]
                }
              }
            }
          },
          label: {
            show: false
          },
          data: barData
        },
        {
          z: 3,
          type: 'pictorialBar',
          symbolPosition: 'end',
          data: barData,
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [30, 15],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return colorList[params.dataIndex][0]
              }
            }
          }
        }
      ]
    }
  }
  const getData = (status) => {
    const startDate = moment(date.value).format('YYYY-MM-DD 00:00:00')
    const endDate = moment(date.value).format('YYYY-MM-DD 23:59:59')
    // 获取作业管控及缺陷管理数据
    lib.api.bigscreenApi.smartMaintenance({ startDate, endDate, status }).then((response) => {
      if (response.success) {
        planAndFinishCountMap.value = response.result.overallMaintenanceMap.key.planAndFinishCountMap
        const specialTypeMapList = response.result.overallMaintenanceMap.key.specialTypeMapList
        const workPlanTypeMapList = response.result.overallMaintenanceMap.key.workPlanTypeMapList
        defectAndRepairCountMap.value = response.result.defectRepairMap.key.defectAndRepairCountMap
        const defectSourceDataMapList = response.result.defectRepairMap.key.defectSourceDataMapList
        const defectDistributionMapList = response.result.defectRepairMap.key.defectDistributionMapList
        specialTypeMap.value.nameList = specialTypeMapList.map((_) => _.professionalName)
        specialTypeMap.value.dataList = specialTypeMapList.map((_) => _.count)
        workPlanTypeMap.value.nameList = workPlanTypeMapList.map((_) => _.workPlanTypeName)
        workPlanTypeMap.value.dataList = workPlanTypeMapList.map((_) => _.count)
        defectSourceDataMap.value.nameList = defectSourceDataMapList.map((_) => _.name)
        defectSourceDataMap.value.dataList = defectSourceDataMapList.map((_) => _.value)
        defectDistributionMap.value.nameList = defectDistributionMapList.map((_) => _.name)
        defectDistributionMap.value.dataList = defectDistributionMapList.map((_) => _.value)
      }
      changeTab('get', tabCur.value)
    })
  }
  const getAllData = () => {
    getData(status)
    // 获取作业列表数据
    lib.api.bigscreenApi.jobControl({ date: date.value }).then((response) => {
      if (response.success) {
        planList.value = response.result
      }
    })
    // 获取缺陷列表数据
    lib.api.bigscreenApi
      .getDefectOrders({
        startTime: moment(date.value).format('YYYY-MM-01 00:00:00'),
        endTime: moment(date.value).endOf('month').format('YYYY-MM-DD 23:59:59')
      })
      .then((response) => {
        if (response.success) {
          defectList.value = response.result
        }
      })
  }
</script>

<style lang="scss" scoped>
  .work-control-container {
    width: 636px;
    height: 439px;
    .tab {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .tab-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 222px;
        height: 118px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/workTab.png');
        background-size: 100% 100%;
        &.active {
          background: url('@/assets/ScreenLeft/DynamicMaintenance/workTabSelected.png');
          background-size: 100% 100%;
        }
      }
      .content {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .text {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span {
          margin-top: 9px;
          font-family: 'Source Han Sans CN';
          font-size: 18px;
          font-weight: 400;
          line-height: 24px;
          color: #ffffff;
        }
        .num {
          font-family: PangMenZhengDao;
          font-size: 32px;
          font-weight: 400;
          line-height: 34px;
          color: #ffffff;
          text-align: center;
        }
      }
    }
    .chart-box {
      display: flex;
      justify-content: space-between;
      width: 385px;
      height: 210px;
      margin-top: 25px;
      .barBg {
        position: absolute;
        bottom: 0;
        width: 394px;
        height: 53px;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/barBg.png');
        background-size: 100% 100%;
      }
    }
    .popup-container {
      position: fixed;
      top: 194px;
      left: 1390px;
    }
  }
</style>
