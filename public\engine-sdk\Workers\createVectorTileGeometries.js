define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./createTaskProcessorWorker","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./BoxGeometry-adae7805","./CylinderGeometryLibrary-03a8620f","./CylinderGeometry-d51717b5","./EllipsoidGeometry-b5925121","./Color-eff039bb"],(function(e,t,n,r,a,i,o,d,s,c,f,l,u,h,b,p,y,g,v,x,C,m){"use strict";function I(e){this.offset=e.offset,this.count=e.count,this.color=e.color,this.batchIds=e.batchIds}var k=new n.Cartesian3,B=o.Matrix4.packedLength+n.Cartesian3.packedLength,M=o.Matrix4.packedLength+2,w=o.Matrix4.packedLength+n.Cartesian3.packedLength,A=n.Cartesian3.packedLength+1,O={modelMatrix:new o.Matrix4,boundingVolume:new a.BoundingSphere};function L(e,t){var r=t*B,a=n.Cartesian3.unpack(e,r,k);r+=n.Cartesian3.packedLength;var i=o.Matrix4.unpack(e,r,O.modelMatrix);o.Matrix4.multiplyByScale(i,a,i);var d=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,d.center),d.radius=Math.sqrt(3),O}function E(e,t){var r=t*M,a=e[r++],i=e[r++],d=n.Cartesian3.fromElements(a,a,i,k),s=o.Matrix4.unpack(e,r,O.modelMatrix);o.Matrix4.multiplyByScale(s,d,s);var c=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,c.center),c.radius=Math.sqrt(2),O}function U(e,t){var r=t*w,a=n.Cartesian3.unpack(e,r,k);r+=n.Cartesian3.packedLength;var i=o.Matrix4.unpack(e,r,O.modelMatrix);o.Matrix4.multiplyByScale(i,a,i);var d=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,d.center),d.radius=1,O}function G(e,t){var r=t*A,a=e[r++],i=n.Cartesian3.unpack(e,r,k),d=o.Matrix4.fromTranslation(i,O.modelMatrix);o.Matrix4.multiplyByUniformScale(d,a,d);var s=O.boundingVolume;return n.Cartesian3.clone(n.Cartesian3.ZERO,s.center),s.radius=1,O}var S=new n.Cartesian3;function T(t,r,i,d,s){if(e.defined(r)){for(var c=i.length,f=d.attributes.position.values,l=d.indices,u=t.positions,h=t.vertexBatchIds,b=t.indices,p=t.batchIds,y=t.batchTableColors,g=t.batchedIndices,v=t.indexOffsets,x=t.indexCounts,C=t.boundingVolumes,k=t.modelMatrix,B=t.center,M=t.positionOffset,w=t.batchIdIndex,A=t.indexOffset,O=t.batchedIndicesOffset,L=0;L<c;++L){var E=s(r,L),U=E.modelMatrix;o.Matrix4.multiply(k,U,U);for(var G=i[L],T=f.length,V=0;V<T;V+=3){var F=n.Cartesian3.unpack(f,V,S);o.Matrix4.multiplyByPoint(U,F,F),n.Cartesian3.subtract(F,B,F),n.Cartesian3.pack(F,u,3*M+V),h[w++]=G}for(var R=l.length,P=0;P<R;++P)b[A+P]=l[P]+M;var Z=L+O;g[Z]=new I({offset:A,count:R,color:m.Color.fromRgba(y[G]),batchIds:[G]}),p[Z]=G,v[Z]=A,x[Z]=R,C[Z]=a.BoundingSphere.transform(E.boundingVolume,U),M+=T/3,A+=R}t.positionOffset=M,t.batchIdIndex=w,t.indexOffset=A,t.batchedIndicesOffset+=c}}var V=new n.Cartesian3,F=new o.Matrix4;function R(e){var t=new Float64Array(e),r=0;n.Cartesian3.unpack(t,r,V),r+=n.Cartesian3.packedLength,o.Matrix4.unpack(t,r,F)}function P(e){for(var t=e.length,n=0,r=0;r<t;++r)n+=m.Color.packedLength+3+e[r].batchIds.length;return n}function Z(e,t,n){var r=n.length,i=2+r*a.BoundingSphere.packedLength+1+P(t),o=new Float64Array(i),d=0;o[d++]=e,o[d++]=r;for(var s=0;s<r;++s)a.BoundingSphere.pack(n[s],o,d),d+=a.BoundingSphere.packedLength;var c=t.length;o[d++]=c;for(var f=0;f<c;++f){var l=t[f];m.Color.pack(l.color,o,d),d+=m.Color.packedLength,o[d++]=l.offset,o[d++]=l.count;var u=l.batchIds,h=u.length;o[d++]=h;for(var b=0;b<h;++b)o[d++]=u[b]}return o}function D(t,n){var r=e.defined(t.boxes)?new Float32Array(t.boxes):void 0,a=e.defined(t.boxBatchIds)?new Uint16Array(t.boxBatchIds):void 0,i=e.defined(t.cylinders)?new Float32Array(t.cylinders):void 0,o=e.defined(t.cylinderBatchIds)?new Uint16Array(t.cylinderBatchIds):void 0,d=e.defined(t.ellipsoids)?new Float32Array(t.ellipsoids):void 0,s=e.defined(t.ellipsoidBatchIds)?new Uint16Array(t.ellipsoidBatchIds):void 0,c=e.defined(t.spheres)?new Float32Array(t.spheres):void 0,f=e.defined(t.sphereBatchIds)?new Uint16Array(t.sphereBatchIds):void 0,l=e.defined(r)?a.length:0,u=e.defined(i)?o.length:0,b=e.defined(d)?s.length:0,p=e.defined(c)?f.length:0,y=g.BoxGeometry.getUnitBox(),v=x.CylinderGeometry.getUnitCylinder(),m=C.EllipsoidGeometry.getUnitEllipsoid(),I=y.attributes.position.values,k=v.attributes.position.values,B=m.attributes.position.values,M=I.length*l;M+=k.length*u,M+=B.length*(b+p);var w=y.indices,A=v.indices,O=m.indices,S=w.length*l;S+=A.length*u,S+=O.length*(b+p);var P=new Float32Array(M),D=new Uint16Array(M/3),q=h.IndexDatatype.createTypedArray(M/3,S),W=l+u+b+p,_=new Uint16Array(W),N=new Array(W),Y=new Uint32Array(W),j=new Uint32Array(W),z=new Array(W);R(t.packedBuffer);var H={batchTableColors:new Uint32Array(t.batchTableColors),positions:P,vertexBatchIds:D,indices:q,batchIds:_,batchedIndices:N,indexOffsets:Y,indexCounts:j,boundingVolumes:z,positionOffset:0,batchIdIndex:0,indexOffset:0,batchedIndicesOffset:0,modelMatrix:F,center:V};T(H,r,a,y,L),T(H,i,o,v,E),T(H,d,s,m,U),T(H,c,f,m,G);var J=Z(q.BYTES_PER_ELEMENT,N,z);return n.push(P.buffer,D.buffer,q.buffer),n.push(_.buffer,Y.buffer,j.buffer),n.push(J.buffer),{positions:P.buffer,vertexBatchIds:D.buffer,indices:q.buffer,indexOffsets:Y.buffer,indexCounts:j.buffer,batchIds:_.buffer,packedBuffer:J.buffer}}var q=b(D);return q}));