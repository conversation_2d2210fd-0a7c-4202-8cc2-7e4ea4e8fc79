<!--
 * @Description: 弹窗组件
 * @Autor: qian
 * @Date: 2023-06-19 17:07:00
 * @LastEditors: wangjialing <EMAIL>
 * @LastEditTime: 2024-06-07 14:20:09
-->
<template>
  <div :id="id" class="mappop-window-container" :class="{ 'pointer-events-none': !clickable }" v-drag="{ draggableClass }">
    <component :is="showComponent" :data="data" :id="id"></component>
  </div>
</template>

<script setup>
  const modules = Object.assign(
    {},
    import.meta.glob(
      [
        '../ScreenMiddlePage/Components/**/*.vue',
        '../ScreenTop/Components/**/*.vue',
        '../MapPopupWindow/Components/**/*.vue',
        '../StructureDiagram/Components/**/*.vue'
      ],
      { eager: true }
    )
  )
  import { onMounted, onUnmounted, reactive, shallowRef, toRefs } from 'vue'

  import lib from '@/utils/lib.ts'
  // import useStore from '@/store'
  import { removeDialogById } from '@/views/Screen/MapPopupWindow/MapPopWindow.js'
  import vDrag from '@Common/directives/drag/index.js'

  // const { storeScreenPage } = useStore()

  const props = defineProps({
    componentName: {
      type: String,
      default: ''
    },
    left: {
      type: [String, Number],
      default: null
    },
    top: {
      type: [String, Number],
      default: null
    },
    right: {
      type: [String, Number],
      default: null
    },
    bottom: {
      type: [String, Number],
      default: null
    },
    id: {
      type: String,
      default: 'mappopwindow_' + Math.ceil(Math.random() * 1000000)
    },
    data: {
      type: Object,
      default: () => {}
    },
    closeFunc: {
      type: Function,
      default: null
    },
    popPosition: {
      type: Array,
      default: () => []
    },
    offset: {
      type: Array,
      default: () => []
    },
    followPoint: {
      type: Object,
      default: null
    },
    tag: {
      type: String,
      default: ''
    },
    zIndex: {
      type: Number,
      default: null
    },
    clickable: {
      type: Boolean,
      default: true
    },
    position: {
      type: String,
      default: 'absolute'
    }
  })
  const showComponent = shallowRef('')

  // let componentPath = ''
  // if (props.componentName.startsWith('Screen/')) {
  //   componentPath = `../${props.componentName}`
  //   const module=import.meta.glob(componentPath, { eager: true })
  // } else {
  //   componentPath = `./${props.tag == 'mapPoint' ? 'MapComponents' : 'components'}/${props.componentName}${
  //     props.componentName.endsWith('.vue') ? '' : '/index.vue'
  //   }`
  // }
  // const module = modules[componentPath]
  // if (module) {
  //   showComponent.value = module.default
  // } else {
  //   console.error(componentPath + '组件路径错误')
  // }

  // const module = import.meta.glob(props.componentName, { eager: true })
  if (props.componentName) {
    // showComponent.value = defineAsyncComponent(() => import(/* @vite-ignore */ props.componentName, { eager: true }))
    const module = modules[props.componentName]
    if (module) {
      showComponent.value = module.default
    } else {
      console.error(componentPath + '组件路径错误')
    }
  }

  const state = reactive({
    popupLeft: '',
    popupTop: '',
    popupRight: '',
    popupBottom: '',
    popupPosition: 'absolute',
    draggableClass: 'popup-title',
    popZIndex: null
  })
  // 判断是否为数字类型
  const isNumber = (value) => {
    return typeof value === 'number'
  }

  state.popupLeft = props.left !== null && isNumber(props.left) ? props.left + 'px' : props.left
  state.popupTop = props.top !== null && isNumber(props.top) ? props.top + 'px' : props.top
  state.popupRight = props.right !== null && isNumber(props.right) ? props.right + 'px' : props.right
  state.popupBottom = props.bottom !== null && isNumber(props.bottom) ? props.bottom + 'px' : props.bottom
  state.popupPosition = props.position
  // state.draggableClass = props.draggableClass

  const { popupLeft, popupTop, draggableClass, popZIndex, popupRight, popupBottom, popupPosition } = toRefs(state)
  const handleClose = () => {
    if (props.closeFunc) {
      props.closeFunc()
    }
    removeDialogById(props.id)
  }
  lib.provideTools.handleClose.provide(handleClose)
  // provide('handleClose', handleClose)

  onMounted(() => {
    state.popZIndex = props.zIndex || 999

    // if (props.followPoint?.typeId && props.followPoint?.id) {
    //   // 跟随点的位置信息
    //   const { clickModel } = lib.ue()
    //   watch(
    //     () => clickModel.updateCustomPOILocationInScreen,
    //     (val) => {
    //       const { typeId, id, location } = val
    //       if (typeId === props.followPoint.typeId && id === props.followPoint.id) {
    //         state.popupLeft = location.x + 'px'
    //         state.popupTop = location.y + 'px'
    //       }
    //     }
    //   )
    // }
  })
  onUnmounted(() => {})

  defineExpose({
    left: props.left,
    right: props.right
  })
</script>
<style lang="scss" scoped>
  .mappop-window-container {
    //position: absolute;
    position: v-bind('popupPosition');

    // inset: v-bind('popupTop') v-bind('popupRight') v-bind('popupBottom') v-bind('popupLeft');
    left: v-bind('popupLeft');
    /* stylelint-disable-next-line order/properties-order */
    top: v-bind('popupTop');
    right: v-bind('popupRight');
    /* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
    bottom: v-bind('popupBottom');
    z-index: v-bind('popZIndex');
    overflow: visible;

    // cursor: move;

    // background: #ccc;
  }
</style>
