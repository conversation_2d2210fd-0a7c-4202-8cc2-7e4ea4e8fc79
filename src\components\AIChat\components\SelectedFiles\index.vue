<template>
  <div v-if="selectedFiles.length > 0" class="selected-files-wrapper">
    <!-- 收起状态：右侧简洁提示 -->
    <Transition name="collapse-indicator">
      <div v-if="!isExpanded" class="collapsed-indicator" @click="toggleExpanded">
        <div class="indicator-content">
          <el-icon class="file-icon"><Document /></el-icon>
          <span class="file-count">{{ selectedFiles.length }}</span>
          <el-icon class="expand-arrow"><ArrowDown /></el-icon>
        </div>
        <div class="expand-tooltip">点击展开文件列表</div>
      </div>
    </Transition>

    <!-- 展开状态：完整文件列表 -->
    <Transition name="expand">
      <div v-if="isExpanded" class="files-expanded">
        <div class="files-container">
          <!-- 文件列表 -->
          <div class="files-list">
            <div v-for="(file, index) in selectedFiles" :key="file.id" class="file-card">
              <div class="file-icon">
                <div v-if="getFileExtension(file.name)" class="file-type-icon" :class="getFileTypeClass(file.name)">
                  {{ getFileExtension(file.name) }}
                </div>
                <el-icon v-else size="16px"><Document /></el-icon>
              </div>
              <div class="file-info" @click="handleFileClick(file)">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
              </div>
              <el-icon class="remove-btn" size="14px" @click="handleRemove(index)">
                <Close />
              </el-icon>
            </div>
          </div>

          <!-- 右侧操作区域 -->
          <div class="actions-sidebar">
            <!-- <div class="files-count">{{ selectedFiles.length }}个文件</div> -->
            <div class="action-buttons">
              <el-button type="text" size="small" @click="handleClearAll" class="clear-all-btn" title="清空全部">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
  import { ref, readonly } from 'vue'
  import { Document, Close, ArrowDown, Delete } from '@element-plus/icons-vue'

  const props = defineProps<{
    selectedFiles: Array<{ id: string; name: string; url?: string }>
  }>()

  const emits = defineEmits<{
    remove: [index: number]
    'clear-all': []
  }>()

  // 展开收起状态，默认展开
  const isExpanded = ref(true)

  // 切换展开收起状态
  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
  }

  // 删除单个文件
  const handleRemove = (index: number) => {
    emits('remove', index)
  }

  // 清空所有文件
  const handleClearAll = () => {
    emits('clear-all')
  }

  // 点击文件打开链接
  const handleFileClick = (file: { id: string; name: string; url?: string }) => {
    if (file.url) {
      window.open(file.url, '_blank')
    }
  }

  // 获取文件扩展名（用于显示）
  const getFileExtension = (fileName: string): string => {
    const supportedExtensions = ['.pdf', '.docx', '.xlsx']
    const lowerFileName = fileName.toLowerCase()

    for (const ext of supportedExtensions) {
      if (lowerFileName.endsWith(ext)) {
        return ext.substring(1).toUpperCase() // 去掉点号并转大写
      }
    }

    return '' // 不支持的扩展名返回空字符串，使用默认图标
  }

  // 获取文件类型样式类名
  const getFileTypeClass = (fileName: string): string => {
    const lowerFileName = fileName.toLowerCase()

    if (lowerFileName.endsWith('.pdf')) return 'pdf-file'
    if (lowerFileName.endsWith('.docx')) return 'docx-file'
    if (lowerFileName.endsWith('.xlsx')) return 'xlsx-file'

    return 'default-file'
  }

  // 暴露方法给父组件
  defineExpose({
    collapse: () => {
      isExpanded.value = false
    },
    expand: () => {
      isExpanded.value = true
    },
    toggle: toggleExpanded,
    isExpanded: readonly(isExpanded)
  })
</script>

<style scoped>
  .selected-files-wrapper {
    position: relative;
  }

  .collapsed-indicator {
    position: absolute;
    top: 0;
    right: -90px;
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(12px);
    z-index: 20;
  }

  .collapsed-indicator:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
  }

  .indicator-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
  }

  .indicator-content .file-icon {
    color: #333;
    font-size: 18px;
  }

  .file-count {
    color: #333;
    font-size: 12px;
    font-weight: 600;
    line-height: 1;
  }

  .expand-arrow {
    color: #333;
    font-size: 12px;
    opacity: 0.7;
  }

  .expand-tooltip {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .expand-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
  }

  .collapsed-indicator:hover .expand-tooltip {
    opacity: 1;
    top: -45px;
  }

  /* 展开状态：完整文件列表 */
  .files-expanded {
    margin-bottom: 0;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .files-container {
    display: flex;
    gap: 12px;
    align-items: flex-start;
  }

  /* 右侧操作区域 */
  .actions-sidebar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 60px;
    padding: 4px;
  }

  .files-count {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .clear-all-btn {
    color: #ff4757;
    font-size: 12px;
    padding: 6px;
    background: rgba(255, 71, 87, 0.1);
    border: 1px solid rgba(255, 71, 87, 0.2);
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    min-height: 28px;
  }

  .clear-all-btn:hover {
    color: #ff3742;
    background: rgba(255, 71, 87, 0.2);
    border-color: rgba(255, 71, 87, 0.3);
    transform: translateY(-1px);
  }

  .collapse-btn {
    color: #0e1df0;
    font-size: 14px;
    padding: 6px 12px;
    background: rgba(14, 29, 240, 0.1);
    border: 1px solid rgba(14, 29, 240, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .collapse-btn:hover {
    color: #0c1bd9;
    background: rgba(14, 29, 240, 0.2);
    border-color: rgba(14, 29, 240, 0.3);
    transform: translateY(-1px);
  }

  .files-list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-height: 120px;
    overflow-y: auto;
  }

  .file-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 14px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    max-width: 220px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .file-card:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .file-card .file-icon {
    color: #409eff;
    flex-shrink: 0;
  }

  .file-type-icon {
    width: 24px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.2s;
  }

  /* PDF文件样式 */
  .pdf-file {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
  }

  .pdf-file:hover {
    background: linear-gradient(135deg, #ff3742, #ff2f3a);
    transform: scale(1.05);
  }

  /* DOCX文件样式 */
  .docx-file {
    background: linear-gradient(135deg, #2e86de, #54a0ff);
    box-shadow: 0 2px 4px rgba(46, 134, 222, 0.3);
  }

  .docx-file:hover {
    background: linear-gradient(135deg, #54a0ff, #74b9ff);
    transform: scale(1.05);
  }

  /* XLSX文件样式 */
  .xlsx-file {
    background: linear-gradient(135deg, #20bf6b, #26d0ce);
    box-shadow: 0 2px 4px rgba(32, 191, 107, 0.3);
  }

  .xlsx-file:hover {
    background: linear-gradient(135deg, #26d0ce, #45aaf2);
    transform: scale(1.05);
  }

  .file-info {
    flex: 1;
    min-width: 0;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .file-info:hover {
    opacity: 0.8;
  }

  .file-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .remove-btn {
    color: #999;
    cursor: pointer;
    flex-shrink: 0;
    transition: color 0.2s ease;
  }

  .remove-btn:hover {
    color: #ff4757;
  }

  /* 展开状态动画 */
  .expand-enter-active {
    transition: all 0.4s ease-out;
    overflow: hidden;
  }

  .expand-leave-active {
    transition: all 0.3s ease-in;
    overflow: hidden;
  }

  .expand-enter-from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  .expand-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }

  .expand-enter-to,
  .expand-leave-from {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }

  /* 收起指示器动画 */
  .collapse-indicator-enter-active {
    transition: all 0.3s ease-out;
    transition-delay: 0.3s;
  }

  .collapse-indicator-leave-active {
    transition: all 0.2s ease-in;
  }

  .collapse-indicator-enter-from {
    opacity: 0;
    transform: translateX(-30px) scale(0.5);
  }

  .collapse-indicator-leave-to {
    opacity: 0;
    transform: translateX(-30px) scale(0.5);
  }

  .collapse-indicator-enter-to,
  .collapse-indicator-leave-from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
</style>
