/**
 * 用于发布
 */
import chalk from 'chalk' // 颜色的插件
import ora from 'ora' // 颜色的插件
import scpClient from 'scp2' // 自动部署

const hostInfo = {
  host: '*************', // 服务器地址ip
  port: '22', // 服务器端口
  username: 'root',
  password: 'Suit_b1mf',
  path: '/home/<USER>/static-view/bigscreen-pddd-view'
}
const spinner = ora('正在发布到服务器...')
spinner.start()

scpClient.scp(
  './dist',
  {
    ...hostInfo,
    host: hostInfo.host,
    port: hostInfo.port,
    username: hostInfo.username,
    password: hostInfo.password,
    path: hostInfo.path
  },
  function(err) {
    spinner.stop()
    if (err) {
      console.log(chalk.red('发布失败.\n'))
      throw err
    } else {
      console.log(chalk.green('Success! 成功发布到服务器!'))
    }
  }
)
