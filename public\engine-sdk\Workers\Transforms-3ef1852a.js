define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./Matrix4-a50b021f","./RuntimeError-d0e509ca"],(function(e,t,r,n,i,a,o){"use strict";var s,u,l,d,f,c,p,h,m,y,v,w,g,E,C,_,x,D,O={OUTSIDE:-1,INTERSECTING:0,INSIDE:1},A=Object.freeze(O),q={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},S={};function R(e){for(var t=e.split("."),r=0,n=t.length;r<n;++r)t[r]=parseInt(t[r],10);return t}function b(){if(!t.defined(l)&&(l=!1,!L())){var e=/ Chrome\/([\.0-9]+)/.exec(u.userAgent);null!==e&&(l=!0,d=R(e[1]))}return l}function T(){return b()&&d}function M(){if(!t.defined(f)&&(f=!1,!b()&&!L()&&/ Safari\/[\.0-9]+/.test(u.userAgent))){var e=/ Version\/([\.0-9]+)/.exec(u.userAgent);null!==e&&(f=!0,c=R(e[1]))}return f}function I(){return M()&&c}function P(){if(!t.defined(p)){p=!1;var e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(u.userAgent);null!==e&&(p=!0,h=R(e[1]),h.isNightly=!!e[2])}return p}function U(){return P()&&h}function N(){var e;t.defined(m)||(m=!1,"Microsoft Internet Explorer"===u.appName?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(u.userAgent),null!==e&&(m=!0,y=R(e[1]))):"Netscape"===u.appName&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(u.userAgent),null!==e&&(m=!0,y=R(e[1]))));return m}function F(){return N()&&y}function L(){if(!t.defined(v)){v=!1;var e=/ Edge\/([\.0-9]+)/.exec(u.userAgent);null!==e&&(v=!0,w=R(e[1]))}return v}function j(){return L()&&w}function z(){if(!t.defined(g)){g=!1;var e=/Firefox\/([\.0-9]+)/.exec(u.userAgent);null!==e&&(g=!0,E=R(e[1]))}return g}function B(){return t.defined(C)||(C=/Windows/i.test(u.appVersion)),C}function V(){return z()&&E}function k(){return t.defined(_)||(_=!z()&&"undefined"!==typeof PointerEvent&&(!t.defined(u.pointerEnabled)||u.pointerEnabled)),_}function W(){if(!t.defined(D)){var e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");var r=e.style.imageRendering;D=t.defined(r)&&""!==r,D&&(x=r)}return D}function Y(){return W()?x:void 0}function H(){if(!H.initialized)throw new r.DeveloperError("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return H._result}Object.defineProperties(S,{element:{get:function(){if(S.supportsFullscreen())return document[q.fullscreenElement]}},changeEventName:{get:function(){if(S.supportsFullscreen())return q.fullscreenchange}},errorEventName:{get:function(){if(S.supportsFullscreen())return q.fullscreenerror}},enabled:{get:function(){if(S.supportsFullscreen())return document[q.fullscreenEnabled]}},fullscreen:{get:function(){if(S.supportsFullscreen())return null!==S.element}}}),S.supportsFullscreen=function(){if(t.defined(s))return s;s=!1;var e=document.body;if("function"===typeof e.requestFullscreen)return q.requestFullscreen="requestFullscreen",q.exitFullscreen="exitFullscreen",q.fullscreenEnabled="fullscreenEnabled",q.fullscreenElement="fullscreenElement",q.fullscreenchange="fullscreenchange",q.fullscreenerror="fullscreenerror",s=!0,s;for(var r,n=["webkit","moz","o","ms","khtml"],i=0,a=n.length;i<a;++i){var o=n[i];r=o+"RequestFullscreen","function"===typeof e[r]?(q.requestFullscreen=r,s=!0):(r=o+"RequestFullScreen","function"===typeof e[r]&&(q.requestFullscreen=r,s=!0)),r=o+"ExitFullscreen","function"===typeof document[r]?q.exitFullscreen=r:(r=o+"CancelFullScreen","function"===typeof document[r]&&(q.exitFullscreen=r)),r=o+"FullscreenEnabled",void 0!==document[r]?q.fullscreenEnabled=r:(r=o+"FullScreenEnabled",void 0!==document[r]&&(q.fullscreenEnabled=r)),r=o+"FullscreenElement",void 0!==document[r]?q.fullscreenElement=r:(r=o+"FullScreenElement",void 0!==document[r]&&(q.fullscreenElement=r)),r=o+"fullscreenchange",void 0!==document["on"+r]&&("ms"===o&&(r="MSFullscreenChange"),q.fullscreenchange=r),r=o+"fullscreenerror",void 0!==document["on"+r]&&("ms"===o&&(r="MSFullscreenError"),q.fullscreenerror=r)}return s},S.requestFullscreen=function(e,t){S.supportsFullscreen()&&e[q.requestFullscreen]({vrDisplay:t})},S.exitFullscreen=function(){S.supportsFullscreen()&&document[q.exitFullscreen]()},S._names=q,u="undefined"!==typeof navigator?navigator:{},H._promise=void 0,H._result=void 0,H.initialize=function(){if(t.defined(H._promise))return H._promise;var e=t.when.defer();if(H._promise=e.promise,L())return H._result=!1,e.resolve(H._result),e.promise;var r=new Image;return r.onload=function(){H._result=r.width>0&&r.height>0,e.resolve(H._result)},r.onerror=function(){H._result=!1,e.resolve(H._result)},r.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",e.promise},Object.defineProperties(H,{initialized:{get:function(){return t.defined(H._result)}}});var X=[];"undefined"!==typeof ArrayBuffer&&(X.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!==typeof Uint8ClampedArray&&X.push(Uint8ClampedArray),"undefined"!==typeof Uint8ClampedArray&&X.push(Uint8ClampedArray));var J={isChrome:b,chromeVersion:T,isSafari:M,safariVersion:I,isWebkit:P,webkitVersion:U,isInternetExplorer:N,internetExplorerVersion:F,isEdge:L,edgeVersion:j,isFirefox:z,firefoxVersion:V,isWindows:B,hardwareConcurrency:t.defaultValue(u.hardwareConcurrency,3),supportsPointerEvents:k,supportsImageRenderingPixelated:W,supportsWebP:H,imageRenderingValue:Y,typedArrayTypes:X};function G(e,r,n,i){this.x=t.defaultValue(e,0),this.y=t.defaultValue(r,0),this.z=t.defaultValue(n,0),this.w=t.defaultValue(i,0)}J.supportsFullscreen=function(){return S.supportsFullscreen()},J.supportsTypedArrays=function(){return"undefined"!==typeof ArrayBuffer},J.supportsWebWorkers=function(){return"undefined"!==typeof Worker},J.supportsWebAssembly=function(){return"undefined"!==typeof WebAssembly&&!J.isEdge()},J.isInternetExplorer=function(){return N()},J.isEdge=function(){return L()};var Z=new n.Cartesian3;G.fromAxisAngle=function(e,r,i){var a=r/2,o=Math.sin(a);Z=n.Cartesian3.normalize(e,Z);var s=Z.x*o,u=Z.y*o,l=Z.z*o,d=Math.cos(a);return t.defined(i)?(i.x=s,i.y=u,i.z=l,i.w=d,i):new G(s,u,l,d)};var Q=[1,2,0],K=new Array(3);G.fromRotationMatrix=function(e,r){var n,i,o,s,u,l=e[a.Matrix3.COLUMN0ROW0],d=e[a.Matrix3.COLUMN1ROW1],f=e[a.Matrix3.COLUMN2ROW2],c=l+d+f;if(c>0)n=Math.sqrt(c+1),u=.5*n,n=.5/n,i=(e[a.Matrix3.COLUMN1ROW2]-e[a.Matrix3.COLUMN2ROW1])*n,o=(e[a.Matrix3.COLUMN2ROW0]-e[a.Matrix3.COLUMN0ROW2])*n,s=(e[a.Matrix3.COLUMN0ROW1]-e[a.Matrix3.COLUMN1ROW0])*n;else{var p=Q,h=0;d>l&&(h=1),f>l&&f>d&&(h=2);var m=p[h],y=p[m];n=Math.sqrt(e[a.Matrix3.getElementIndex(h,h)]-e[a.Matrix3.getElementIndex(m,m)]-e[a.Matrix3.getElementIndex(y,y)]+1);var v=K;v[h]=.5*n,n=.5/n,u=(e[a.Matrix3.getElementIndex(y,m)]-e[a.Matrix3.getElementIndex(m,y)])*n,v[m]=(e[a.Matrix3.getElementIndex(m,h)]+e[a.Matrix3.getElementIndex(h,m)])*n,v[y]=(e[a.Matrix3.getElementIndex(y,h)]+e[a.Matrix3.getElementIndex(h,y)])*n,i=-v[0],o=-v[1],s=-v[2]}return t.defined(r)?(r.x=i,r.y=o,r.z=s,r.w=u,r):new G(i,o,s,u)};var $=new G,ee=new G,te=new G,re=new G;G.fromHeadingPitchRoll=function(e,t){return re=G.fromAxisAngle(n.Cartesian3.UNIT_X,e.roll,$),te=G.fromAxisAngle(n.Cartesian3.UNIT_Y,-e.pitch,t),t=G.multiply(te,re,te),ee=G.fromAxisAngle(n.Cartesian3.UNIT_Z,-e.heading,$),G.multiply(ee,t,t)};var ne=new n.Cartesian3,ie=new n.Cartesian3,ae=new G,oe=new G,se=new G;G.packedLength=4,G.pack=function(e,r,n){return n=t.defaultValue(n,0),r[n++]=e.x,r[n++]=e.y,r[n++]=e.z,r[n]=e.w,r},G.unpack=function(e,r,n){return r=t.defaultValue(r,0),t.defined(n)||(n=new G),n.x=e[r],n.y=e[r+1],n.z=e[r+2],n.w=e[r+3],n},G.packedInterpolationLength=3,G.convertPackedArrayForInterpolation=function(e,r,n,i){G.unpack(e,4*n,se),G.conjugate(se,se);for(var a=0,o=n-r+1;a<o;a++){var s=3*a;G.unpack(e,4*(r+a),ae),G.multiply(ae,se,ae),ae.w<0&&G.negate(ae,ae),G.computeAxis(ae,ne);var u=G.computeAngle(ae);t.defined(i)||(i=[]),i[s]=ne.x*u,i[s+1]=ne.y*u,i[s+2]=ne.z*u}},G.unpackInterpolationResult=function(e,r,i,a,o){t.defined(o)||(o=new G),n.Cartesian3.fromArray(e,0,ie);var s=n.Cartesian3.magnitude(ie);return G.unpack(r,4*a,oe),0===s?G.clone(G.IDENTITY,ae):G.fromAxisAngle(ie,s,ae),G.multiply(ae,oe,o)},G.clone=function(e,r){if(t.defined(e))return t.defined(r)?(r.x=e.x,r.y=e.y,r.z=e.z,r.w=e.w,r):new G(e.x,e.y,e.z,e.w)},G.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},G.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},G.magnitude=function(e){return Math.sqrt(G.magnitudeSquared(e))},G.normalize=function(e,t){var r=1/G.magnitude(e),n=e.x*r,i=e.y*r,a=e.z*r,o=e.w*r;return t.x=n,t.y=i,t.z=a,t.w=o,t},G.inverse=function(e,t){var r=G.magnitudeSquared(e);return t=G.conjugate(e,t),G.multiplyByScalar(t,1/r,t)},G.add=function(e,t,r){return r.x=e.x+t.x,r.y=e.y+t.y,r.z=e.z+t.z,r.w=e.w+t.w,r},G.subtract=function(e,t,r){return r.x=e.x-t.x,r.y=e.y-t.y,r.z=e.z-t.z,r.w=e.w-t.w,r},G.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},G.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},G.multiply=function(e,t,r){var n=e.x,i=e.y,a=e.z,o=e.w,s=t.x,u=t.y,l=t.z,d=t.w,f=o*s+n*d+i*l-a*u,c=o*u-n*l+i*d+a*s,p=o*l+n*u-i*s+a*d,h=o*d-n*s-i*u-a*l;return r.x=f,r.y=c,r.z=p,r.w=h,r},G.multiplyByScalar=function(e,t,r){return r.x=e.x*t,r.y=e.y*t,r.z=e.z*t,r.w=e.w*t,r},G.divideByScalar=function(e,t,r){return r.x=e.x/t,r.y=e.y/t,r.z=e.z/t,r.w=e.w/t,r},G.computeAxis=function(e,t){var r=e.w;if(Math.abs(r-1)<n.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;var i=1/Math.sqrt(1-r*r);return t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t},G.computeAngle=function(e){return Math.abs(e.w-1)<n.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};var ue=new G;G.lerp=function(e,t,r,n){return ue=G.multiplyByScalar(t,r,ue),n=G.multiplyByScalar(e,1-r,n),G.add(ue,n,n)};var le=new G,de=new G,fe=new G;G.slerp=function(e,t,r,i){var a=G.dot(e,t),o=t;if(a<0&&(a=-a,o=le=G.negate(t,le)),1-a<n.CesiumMath.EPSILON6)return G.lerp(e,o,r,i);var s=Math.acos(a);return de=G.multiplyByScalar(e,Math.sin((1-r)*s),de),fe=G.multiplyByScalar(o,Math.sin(r*s),fe),i=G.add(de,fe,i),G.multiplyByScalar(i,1/Math.sin(s),i)},G.log=function(e,t){var r=n.CesiumMath.acosClamped(e.w),i=0;return 0!==r&&(i=r/Math.sin(r)),n.Cartesian3.multiplyByScalar(e,i,t)},G.exp=function(e,t){var r=n.Cartesian3.magnitude(e),i=0;return 0!==r&&(i=Math.sin(r)/r),t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t.w=Math.cos(r),t};var ce=new n.Cartesian3,pe=new n.Cartesian3,he=new G,me=new G;G.computeInnerQuadrangle=function(e,t,r,i){var a=G.conjugate(t,he);G.multiply(a,r,me);var o=G.log(me,ce);G.multiply(a,e,me);var s=G.log(me,pe);return n.Cartesian3.add(o,s,o),n.Cartesian3.multiplyByScalar(o,.25,o),n.Cartesian3.negate(o,o),G.exp(o,he),G.multiply(t,he,i)},G.squad=function(e,t,r,n,i,a){var o=G.slerp(e,t,i,he),s=G.slerp(r,n,i,me);return G.slerp(o,s,2*i*(1-i),a)};for(var ye=new G,ve=1.9011074535173003,we=J.supportsTypedArrays()?new Float32Array(8):[],ge=J.supportsTypedArrays()?new Float32Array(8):[],Ee=J.supportsTypedArrays()?new Float32Array(8):[],Ce=J.supportsTypedArrays()?new Float32Array(8):[],_e=0;_e<7;++_e){var xe=_e+1,De=2*xe+1;we[_e]=1/(xe*De),ge[_e]=xe/De}function Oe(e,t,n){r.Check.defined("array",e),r.Check.defined("itemToFind",t),r.Check.defined("comparator",n);var i,a,o=0,s=e.length-1;while(o<=s)if(i=~~((o+s)/2),a=n(e[i],t),a<0)o=i+1;else{if(!(a>0))return i;s=i-1}return~(s+1)}function Ae(e,t,r,n,i){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=r,this.yPoleOffset=n,this.ut1MinusUtc=i}function qe(){var e=/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,t=arguments,r=0,n=t[r++],i=function(e,t,r,n){r||(r=" ");var i=e.length>=t?"":Array(1+t-e.length>>>0).join(r);return n?e+i:i+e},a=function(e,t,r,n,a,o){var s=n-e.length;return s>0&&(e=r||!a?i(e,n,o,r):e.slice(0,t.length)+i("",s,"0",!0)+e.slice(t.length)),e},o=function(e,t,r,n,o,s,u){var l=e>>>0;return r=r&&l&&{2:"0b",8:"0",16:"0x"}[t]||"",e=r+i(l.toString(t),s||0,"0",!1),a(e,r,n,o,u)},s=function(e,t,r,n,i,o){return null!=n&&(e=e.slice(0,n)),a(e,"",t,r,i,o)},u=function(e,n,u,l,d,f,c){var p,h,m,y,v;if("%%"==e)return"%";for(var w=!1,g="",E=!1,C=!1,_=" ",x=u.length,D=0;u&&D<x;D++)switch(u.charAt(D)){case" ":g=" ";break;case"+":g="+";break;case"-":w=!0;break;case"'":_=u.charAt(D+1);break;case"0":E=!0;break;case"#":C=!0;break}if(l=l?"*"==l?+t[r++]:"*"==l.charAt(0)?+t[l.slice(1,-1)]:+l:0,l<0&&(l=-l,w=!0),!isFinite(l))throw new Error("sprintf: (minimum-)width must be finite");switch(f=f?"*"==f?+t[r++]:"*"==f.charAt(0)?+t[f.slice(1,-1)]:+f:"fFeE".indexOf(c)>-1?6:"d"==c?0:void 0,v=n?t[n.slice(0,-1)]:t[r++],c){case"s":return s(String(v),w,l,f,E,_);case"c":return s(String.fromCharCode(+v),w,l,f,E);case"b":return o(v,2,C,w,l,f,E);case"o":return o(v,8,C,w,l,f,E);case"x":return o(v,16,C,w,l,f,E);case"X":return o(v,16,C,w,l,f,E).toUpperCase();case"u":return o(v,10,C,w,l,f,E);case"i":case"d":return p=+v||0,p=Math.round(p-p%1),h=p<0?"-":g,v=h+i(String(Math.abs(p)),f,"0",!1),a(v,h,w,l,E);case"e":case"E":case"f":case"F":case"g":case"G":return p=+v,h=p<0?"-":g,m=["toExponential","toFixed","toPrecision"]["efg".indexOf(c.toLowerCase())],y=["toString","toUpperCase"]["eEfFgG".indexOf(c)%2],v=h+Math.abs(p)[m](f),a(v,h,w,l,E)[y]();default:return e}};return n.replace(e,u)}function Se(e,t,r,n,i,a,o,s){this.year=e,this.month=t,this.day=r,this.hour=n,this.minute=i,this.second=a,this.millisecond=o,this.isLeapSecond=s}function Re(e){if(null===e||isNaN(e))throw new r.DeveloperError("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}function be(e,t){this.julianDate=e,this.offset=t}we[7]=ve/136,ge[7]=8*ve/17,G.fastSlerp=function(e,t,r,n){var i,a=G.dot(e,t);a>=0?i=1:(i=-1,a=-a);for(var o=a-1,s=1-r,u=r*r,l=s*s,d=7;d>=0;--d)Ee[d]=(we[d]*u-ge[d])*o,Ce[d]=(we[d]*l-ge[d])*o;var f=i*r*(1+Ee[0]*(1+Ee[1]*(1+Ee[2]*(1+Ee[3]*(1+Ee[4]*(1+Ee[5]*(1+Ee[6]*(1+Ee[7])))))))),c=s*(1+Ce[0]*(1+Ce[1]*(1+Ce[2]*(1+Ce[3]*(1+Ce[4]*(1+Ce[5]*(1+Ce[6]*(1+Ce[7])))))))),p=G.multiplyByScalar(e,c,ye);return G.multiplyByScalar(t,f,n),G.add(p,n,n)},G.fastSquad=function(e,t,r,n,i,a){var o=G.fastSlerp(e,t,i,he),s=G.fastSlerp(r,n,i,me);return G.fastSlerp(o,s,2*i*(1-i),a)},G.equals=function(e,r){return e===r||t.defined(e)&&t.defined(r)&&e.x===r.x&&e.y===r.y&&e.z===r.z&&e.w===r.w},G.equalsEpsilon=function(e,r,n){return n=t.defaultValue(n,0),e===r||t.defined(e)&&t.defined(r)&&Math.abs(e.x-r.x)<=n&&Math.abs(e.y-r.y)<=n&&Math.abs(e.z-r.z)<=n&&Math.abs(e.w-r.w)<=n},G.ZERO=Object.freeze(new G(0,0,0,0)),G.IDENTITY=Object.freeze(new G(0,0,0,1)),G.prototype.clone=function(e){return G.clone(this,e)},G.prototype.equals=function(e){return G.equals(this,e)},G.prototype.equalsEpsilon=function(e,t){return G.equalsEpsilon(this,e,t)},G.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var Te={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5},Me=Object.freeze(Te),Ie={UTC:0,TAI:1},Pe=Object.freeze(Ie),Ue=new Se,Ne=[31,28,31,30,31,30,31,31,30,31,30,31],Fe=29;function Le(e,t){return et.compare(e.julianDate,t.julianDate)}var je=new be;function ze(e){je.julianDate=e;var t=et.leapSeconds,r=Oe(t,je,Le);r<0&&(r=~r),r>=t.length&&(r=t.length-1);var n=t[r].offset;if(r>0){var i=et.secondsDifference(t[r].julianDate,e);i>n&&(r--,n=t[r].offset)}et.addSeconds(e,n,e)}function Be(e,t){je.julianDate=e;var r=et.leapSeconds,n=Oe(r,je,Le);if(n<0&&(n=~n),0===n)return et.addSeconds(e,-r[0].offset,t);if(n>=r.length)return et.addSeconds(e,-r[n-1].offset,t);var i=et.secondsDifference(r[n].julianDate,e);return 0===i?et.addSeconds(e,-r[n].offset,t):i<=1?void 0:et.addSeconds(e,-r[--n].offset,t)}function Ve(e,t,r){var n=t/Me.SECONDS_PER_DAY|0;return e+=n,t-=Me.SECONDS_PER_DAY*n,t<0&&(e--,t+=Me.SECONDS_PER_DAY),r.dayNumber=e,r.secondsOfDay=t,r}function ke(e,t,r,n,i,a,o){var s=(t-14)/12|0,u=e+4800+s,l=(1461*u/4|0)+(367*(t-2-12*s)/12|0)-(3*((u+100)/100|0)/4|0)+r-32075;n-=12,n<0&&(n+=24);var d=a+(n*Me.SECONDS_PER_HOUR+i*Me.SECONDS_PER_MINUTE+o*Me.SECONDS_PER_MILLISECOND);return d>=43200&&(l-=1),[l,d]}var We=/^(\d{4})$/,Ye=/^(\d{4})-(\d{2})$/,He=/^(\d{4})-?(\d{3})$/,Xe=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,Je=/^(\d{4})-?(\d{2})-?(\d{2})$/,Ge=/([Z+\-])?(\d{2})?:?(\d{2})?$/,Ze=/^(\d{2})(\.\d+)?/.source+Ge.source,Qe=/^(\d{2}):?(\d{2})(\.\d+)?/.source+Ge.source,Ke=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+Ge.source,$e="Invalid ISO 8601 date.";function et(e,r,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=t.defaultValue(e,0),r=t.defaultValue(r,0),n=t.defaultValue(n,Pe.UTC);var i=0|e;r+=(e-i)*Me.SECONDS_PER_DAY,Ve(i,r,this),n===Pe.UTC&&ze(this)}et.fromGregorianDate=function(e,n){if(!(e instanceof Se))throw new r.DeveloperError("date must be a valid GregorianDate.");var i=ke(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return t.defined(n)?(Ve(i[0],i[1],n),ze(n),n):new et(i[0],i[1],Pe.UTC)},et.fromDate=function(e,n){if(!(e instanceof Date)||isNaN(e.getTime()))throw new r.DeveloperError("date must be a valid JavaScript Date.");var i=ke(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return t.defined(n)?(Ve(i[0],i[1],n),ze(n),n):new et(i[0],i[1],Pe.UTC)},et.fromIso8601=function(e,n){if("string"!==typeof e)throw new r.DeveloperError($e);e=e.replace(",",".");var i,a,o,s,u,l=e.split("T"),d=1,f=1,c=0,p=0,h=0,m=0,y=l[0],v=l[1];if(!t.defined(y))throw new r.DeveloperError($e);if(l=y.match(Je),null!==l){if(s=y.split("-").length-1,s>0&&2!==s)throw new r.DeveloperError($e);i=+l[1],d=+l[2],f=+l[3]}else if(l=y.match(Ye),null!==l)i=+l[1],d=+l[2];else if(l=y.match(We),null!==l)i=+l[1];else{var w;if(l=y.match(He),null!==l){if(i=+l[1],w=+l[2],o=Re(i),w<1||o&&w>366||!o&&w>365)throw new r.DeveloperError($e)}else{if(l=y.match(Xe),null===l)throw new r.DeveloperError($e);i=+l[1];var g=+l[2],E=+l[3]||0;if(s=y.split("-").length-1,s>0&&(!t.defined(l[3])&&1!==s||t.defined(l[3])&&2!==s))throw new r.DeveloperError($e);var C=new Date(Date.UTC(i,0,4));w=7*g+E-C.getUTCDay()-3}a=new Date(Date.UTC(i,0,1)),a.setUTCDate(w),d=a.getUTCMonth()+1,f=a.getUTCDate()}if(o=Re(i),d<1||d>12||f<1||(2!==d||!o)&&f>Ne[d-1]||o&&2===d&&f>Fe)throw new r.DeveloperError($e);if(t.defined(v)){if(l=v.match(Ke),null!==l){if(s=v.split(":").length-1,s>0&&2!==s&&3!==s)throw new r.DeveloperError($e);c=+l[1],p=+l[2],h=+l[3],m=1e3*+(l[4]||0),u=5}else if(l=v.match(Qe),null!==l){if(s=v.split(":").length-1,s>2)throw new r.DeveloperError($e);c=+l[1],p=+l[2],h=60*+(l[3]||0),u=4}else{if(l=v.match(Ze),null===l)throw new r.DeveloperError($e);c=+l[1],p=60*+(l[2]||0),u=3}if(p>=60||h>=61||c>24||24===c&&(p>0||h>0||m>0))throw new r.DeveloperError($e);var _=l[u],x=+l[u+1],D=+(l[u+2]||0);switch(_){case"+":c-=x,p-=D;break;case"-":c+=x,p+=D;break;case"Z":break;default:p+=new Date(Date.UTC(i,d-1,f,c,p)).getTimezoneOffset();break}}var O=60===h;O&&h--;while(p>=60)p-=60,c++;while(c>=24)c-=24,f++;a=o&&2===d?Fe:Ne[d-1];while(f>a)f-=a,d++,d>12&&(d-=12,i++),a=o&&2===d?Fe:Ne[d-1];while(p<0)p+=60,c--;while(c<0)c+=24,f--;while(f<1)d--,d<1&&(d+=12,i--),a=o&&2===d?Fe:Ne[d-1],f+=a;var A=ke(i,d,f,c,p,h,m);return t.defined(n)?(Ve(A[0],A[1],n),ze(n)):n=new et(A[0],A[1],Pe.UTC),O&&et.addSeconds(n,1,n),n},et.now=function(e){return et.fromDate(new Date,e)};var tt=new et(0,0,Pe.TAI);function rt(e){if(e instanceof rt)this.scheme=e.scheme,this.authority=e.authority,this.path=e.path,this.query=e.query,this.fragment=e.fragment;else if(e){var t=nt.exec(e);this.scheme=t[1],this.authority=t[2],this.path=t[3],this.query=t[4],this.fragment=t[5]}}et.toGregorianDate=function(e,n){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");var i=!1,a=Be(e,tt);t.defined(a)||(et.addSeconds(e,-1,tt),a=Be(tt,tt),i=!0);var o=a.dayNumber,s=a.secondsOfDay;s>=43200&&(o+=1);var u=o+68569|0,l=4*u/146097|0;u=u-((146097*l+3)/4|0)|0;var d=4e3*(u+1)/1461001|0;u=u-(1461*d/4|0)+31|0;var f=80*u/2447|0,c=u-(2447*f/80|0)|0;u=f/11|0;var p=f+2-12*u|0,h=100*(l-49)+d+u|0,m=s/Me.SECONDS_PER_HOUR|0,y=s-m*Me.SECONDS_PER_HOUR,v=y/Me.SECONDS_PER_MINUTE|0;y-=v*Me.SECONDS_PER_MINUTE;var w=0|y,g=(y-w)/Me.SECONDS_PER_MILLISECOND;return m+=12,m>23&&(m-=24),i&&(w+=1),t.defined(n)?(n.year=h,n.month=p,n.day=c,n.hour=m,n.minute=v,n.second=w,n.millisecond=g,n.isLeapSecond=i,n):new Se(h,p,c,m,v,w,g,i)},et.toDate=function(e){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");var n=et.toGregorianDate(e,Ue),i=n.second;return n.isLeapSecond&&(i-=1),new Date(Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,i,n.millisecond))},et.toIso8601=function(e,n){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");var i,a=et.toGregorianDate(e,Ue),o=a.year,s=a.month,u=a.day,l=a.hour,d=a.minute,f=a.second,c=a.millisecond;return 1e4===o&&1===s&&1===u&&0===l&&0===d&&0===f&&0===c&&(o=9999,s=12,u=31,l=24),t.defined(n)||0===c?t.defined(n)&&0!==n?(i=(.01*c).toFixed(n).replace(".","").slice(0,n),qe("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",o,s,u,l,d,f,i)):qe("%04d-%02d-%02dT%02d:%02d:%02dZ",o,s,u,l,d,f):(i=(.01*c).toString().replace(".",""),qe("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",o,s,u,l,d,f,i))},et.clone=function(e,r){if(t.defined(e))return t.defined(r)?(r.dayNumber=e.dayNumber,r.secondsOfDay=e.secondsOfDay,r):new et(e.dayNumber,e.secondsOfDay,Pe.TAI)},et.compare=function(e,n){if(!t.defined(e))throw new r.DeveloperError("left is required.");if(!t.defined(n))throw new r.DeveloperError("right is required.");var i=e.dayNumber-n.dayNumber;return 0!==i?i:e.secondsOfDay-n.secondsOfDay},et.equals=function(e,r){return e===r||t.defined(e)&&t.defined(r)&&e.dayNumber===r.dayNumber&&e.secondsOfDay===r.secondsOfDay},et.equalsEpsilon=function(e,r,n){return n=t.defaultValue(n,0),e===r||t.defined(e)&&t.defined(r)&&Math.abs(et.secondsDifference(e,r))<=n},et.totalDays=function(e){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");return e.dayNumber+e.secondsOfDay/Me.SECONDS_PER_DAY},et.secondsDifference=function(e,n){if(!t.defined(e))throw new r.DeveloperError("left is required.");if(!t.defined(n))throw new r.DeveloperError("right is required.");var i=(e.dayNumber-n.dayNumber)*Me.SECONDS_PER_DAY;return i+(e.secondsOfDay-n.secondsOfDay)},et.daysDifference=function(e,n){if(!t.defined(e))throw new r.DeveloperError("left is required.");if(!t.defined(n))throw new r.DeveloperError("right is required.");var i=e.dayNumber-n.dayNumber,a=(e.secondsOfDay-n.secondsOfDay)/Me.SECONDS_PER_DAY;return i+a},et.computeTaiMinusUtc=function(e){je.julianDate=e;var t=et.leapSeconds,r=Oe(t,je,Le);return r<0&&(r=~r,--r,r<0&&(r=0)),t[r].offset},et.addSeconds=function(e,n,i){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");if(!t.defined(n))throw new r.DeveloperError("seconds is required.");if(!t.defined(i))throw new r.DeveloperError("result is required.");return Ve(e.dayNumber,e.secondsOfDay+n,i)},et.addMinutes=function(e,n,i){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");if(!t.defined(n))throw new r.DeveloperError("minutes is required.");if(!t.defined(i))throw new r.DeveloperError("result is required.");var a=e.secondsOfDay+n*Me.SECONDS_PER_MINUTE;return Ve(e.dayNumber,a,i)},et.addHours=function(e,n,i){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");if(!t.defined(n))throw new r.DeveloperError("hours is required.");if(!t.defined(i))throw new r.DeveloperError("result is required.");var a=e.secondsOfDay+n*Me.SECONDS_PER_HOUR;return Ve(e.dayNumber,a,i)},et.addDays=function(e,n,i){if(!t.defined(e))throw new r.DeveloperError("julianDate is required.");if(!t.defined(n))throw new r.DeveloperError("days is required.");if(!t.defined(i))throw new r.DeveloperError("result is required.");var a=e.dayNumber+n;return Ve(a,e.secondsOfDay,i)},et.lessThan=function(e,t){return et.compare(e,t)<0},et.lessThanOrEquals=function(e,t){return et.compare(e,t)<=0},et.greaterThan=function(e,t){return et.compare(e,t)>0},et.greaterThanOrEquals=function(e,t){return et.compare(e,t)>=0},et.prototype.clone=function(e){return et.clone(this,e)},et.prototype.equals=function(e){return et.equals(this,e)},et.prototype.equalsEpsilon=function(e,t){return et.equalsEpsilon(this,e,t)},et.prototype.toString=function(){return et.toIso8601(this)},et.leapSeconds=[new be(new et(2441317,43210,Pe.TAI),10),new be(new et(2441499,43211,Pe.TAI),11),new be(new et(2441683,43212,Pe.TAI),12),new be(new et(2442048,43213,Pe.TAI),13),new be(new et(2442413,43214,Pe.TAI),14),new be(new et(2442778,43215,Pe.TAI),15),new be(new et(2443144,43216,Pe.TAI),16),new be(new et(2443509,43217,Pe.TAI),17),new be(new et(2443874,43218,Pe.TAI),18),new be(new et(2444239,43219,Pe.TAI),19),new be(new et(2444786,43220,Pe.TAI),20),new be(new et(2445151,43221,Pe.TAI),21),new be(new et(2445516,43222,Pe.TAI),22),new be(new et(2446247,43223,Pe.TAI),23),new be(new et(2447161,43224,Pe.TAI),24),new be(new et(2447892,43225,Pe.TAI),25),new be(new et(2448257,43226,Pe.TAI),26),new be(new et(2448804,43227,Pe.TAI),27),new be(new et(2449169,43228,Pe.TAI),28),new be(new et(2449534,43229,Pe.TAI),29),new be(new et(2450083,43230,Pe.TAI),30),new be(new et(2450630,43231,Pe.TAI),31),new be(new et(2451179,43232,Pe.TAI),32),new be(new et(2453736,43233,Pe.TAI),33),new be(new et(2454832,43234,Pe.TAI),34),new be(new et(2456109,43235,Pe.TAI),35),new be(new et(2457204,43236,Pe.TAI),36),new be(new et(2457754,43237,Pe.TAI),37)],rt.prototype.scheme=null,rt.prototype.authority=null,rt.prototype.path="",rt.prototype.query=null,rt.prototype.fragment=null;var nt=new RegExp("^(?:([^:/?#]+):)?(?://([^/?#]*))?([^?#]*)(?:\\?([^#]*))?(?:#(.*))?$");rt.prototype.getScheme=function(){return this.scheme},rt.prototype.getAuthority=function(){return this.authority},rt.prototype.getPath=function(){return this.path},rt.prototype.getQuery=function(){return this.query},rt.prototype.getFragment=function(){return this.fragment},rt.prototype.isAbsolute=function(){return!!this.scheme&&!this.fragment},rt.prototype.isSameDocumentAs=function(e){return e.scheme==this.scheme&&e.authority==this.authority&&e.path==this.path&&e.query==this.query},rt.prototype.equals=function(e){return this.isSameDocumentAs(e)&&e.fragment==this.fragment},rt.prototype.normalize=function(){this.removeDotSegments(),this.scheme&&(this.scheme=this.scheme.toLowerCase()),this.authority&&(this.authority=this.authority.replace(ot,ut).replace(it,st)),this.path&&(this.path=this.path.replace(it,st)),this.query&&(this.query=this.query.replace(it,st)),this.fragment&&(this.fragment=this.fragment.replace(it,st))};var it=/%[0-9a-z]{2}/gi,at=/[a-zA-Z0-9\-\._~]/,ot=/(.*@)?([^@:]*)(:.*)?/;function st(e){var t=unescape(e);return at.test(t)?t:e.toUpperCase()}function ut(e,t,r,n){return(t||"")+r.toLowerCase()+(n||"")}function lt(e){return 0!==e.length&&"/"===e[e.length-1]||(e+="/"),e}function dt(e,r){if(null===e||"object"!==typeof e)return e;r=t.defaultValue(r,!1);var n=new e.constructor;for(var i in e)if(e.hasOwnProperty(i)){var a=e[i];r&&(a=dt(a,r)),n[i]=a}return n}function ft(e,r,n){n=t.defaultValue(n,!1);var i,a,o,s={},u=t.defined(e),l=t.defined(r);if(u)for(i in e)e.hasOwnProperty(i)&&(a=e[i],l&&n&&"object"===typeof a&&r.hasOwnProperty(i)?(o=r[i],s[i]="object"===typeof o?ft(a,o,n):a):s[i]=a);if(l)for(i in r)r.hasOwnProperty(i)&&!s.hasOwnProperty(i)&&(o=r[i],s[i]=o);return s}function ct(e,t){var r;return"undefined"!==typeof document&&(r=document),ct._implementation(e,t,r)}function pt(e,n){if(!t.defined(e))throw new r.DeveloperError("uri is required.");var i="",a=e.lastIndexOf("/");return-1!==a&&(i=e.substring(0,a+1)),n?(e=new rt(e),t.defined(e.query)&&(i+="?"+e.query),t.defined(e.fragment)&&(i+="#"+e.fragment),i):i}function ht(e){if(!t.defined(e))throw new r.DeveloperError("uri is required.");var n=new rt(e);n.normalize();var i=n.path,a=i.lastIndexOf("/");return-1!==a&&(i=i.substr(a+1)),a=i.lastIndexOf("."),i=-1===a?"":i.substr(a+1),i}rt.prototype.resolve=function(e){var t=new rt;return this.scheme?(t.scheme=this.scheme,t.authority=this.authority,t.path=this.path,t.query=this.query):(t.scheme=e.scheme,this.authority?(t.authority=this.authority,t.path=this.path,t.query=this.query):(t.authority=e.authority,""==this.path?(t.path=e.path,t.query=this.query||e.query):("/"==this.path.charAt(0)?(t.path=this.path,t.removeDotSegments()):(e.authority&&""==e.path?t.path="/"+this.path:t.path=e.path.substring(0,e.path.lastIndexOf("/")+1)+this.path,t.removeDotSegments()),t.query=this.query))),t.fragment=this.fragment,t},rt.prototype.removeDotSegments=function(){var e,t=this.path.split("/"),r=[],n=""==t[0];n&&t.shift();""==t[0]&&t.shift();while(t.length)e=t.shift(),".."==e?r.pop():"."!=e&&r.push(e);"."!=e&&".."!=e||r.push(""),n&&r.unshift(""),this.path=r.join("/")},rt.prototype.toString=function(){var e="";return this.scheme&&(e+=this.scheme+":"),this.authority&&(e+="//"+this.authority),e+=this.path,this.query&&(e+="?"+this.query),this.fragment&&(e+="#"+this.fragment),e},ct._implementation=function(e,n,i){if(!t.defined(e))throw new r.DeveloperError("relative uri is required.");if(!t.defined(n)){if("undefined"===typeof i)return e;n=t.defaultValue(i.baseURI,i.location.href)}var a=new rt(n),o=new rt(e);return o.resolve(a).toString()};var mt=/^blob:/i;function yt(e){return r.Check.typeOf.string("uri",e),mt.test(e)}function vt(e){var r;t.defined(r)||(r=document.createElement("a")),r.href=window.location.href;var n=r.host,i=r.protocol;return r.href=e,r.href=r.href,i!==r.protocol||n!==r.host}var wt=/^data:/i;function gt(e){return r.Check.typeOf.string("uri",e),wt.test(e)}function Et(e){var r=t.when.defer(),n=document.createElement("script");n.async=!0,n.src=e;var i=document.getElementsByTagName("head")[0];return n.onload=function(){n.onload=void 0,i.removeChild(n),r.resolve()},n.onerror=function(e){r.reject(e)},i.appendChild(n),r.promise}function Ct(e){if(!t.defined(e))throw new r.DeveloperError("obj is required.");var n="";for(var i in e)if(e.hasOwnProperty(i)){var a=e[i],o=encodeURIComponent(i)+"=";if(Array.isArray(a))for(var s=0,u=a.length;s<u;++s)n+=o+encodeURIComponent(a[s])+"&";else n+=o+encodeURIComponent(a)+"&"}return n=n.slice(0,-1),n}function _t(e){if(!t.defined(e))throw new r.DeveloperError("queryString is required.");var n={};if(""===e)return n;for(var i=e.replace(/\+/g,"%20").split(/[&;]/),a=0,o=i.length;a<o;++a){var s=i[a].split("="),u=decodeURIComponent(s[0]),l=s[1];l=t.defined(l)?decodeURIComponent(l):"";var d=n[u];"string"===typeof d?n[u]=[d,l]:Array.isArray(d)?d.push(l):n[u]=l}return n}var xt={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},Dt=Object.freeze(xt),Ot={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},At=Object.freeze(Ot);function qt(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var r=t.defaultValue(e.throttleByServer,!1),n=t.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=t.defaultValue(e.priority,0),this.throttle=n,this.throttleByServer=r,this.type=t.defaultValue(e.type,At.OTHER),this.serverKey=void 0,this.state=Dt.UNISSUED,this.deferred=void 0,this.cancelled=!1}function St(e){var t={};if(!e)return t;for(var r=e.split("\r\n"),n=0;n<r.length;++n){var i=r[n],a=i.indexOf(": ");if(a>0){var o=i.substring(0,a),s=i.substring(a+2);t[o]=s}}return t}function Rt(e,t,r){this.statusCode=e,this.response=t,this.responseHeaders=r,"string"===typeof this.responseHeaders&&(this.responseHeaders=St(this.responseHeaders))}function bt(){this._listeners=[],this._scopes=[],this._toRemove=[],this._removeListenerFnAry=[],this._insideRaiseEvent=!1}function Tt(e,t){return t-e}function Mt(e){r.Check.typeOf.object("options",e),r.Check.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function It(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function Pt(e,t){return e.priority-t.priority}qt.prototype.cancel=function(){this.cancelled=!0},qt.prototype.clone=function(e){return t.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=this.RequestState.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new qt(this)},Rt.prototype.toString=function(){var e="Request has failed.";return t.defined(this.statusCode)&&(e+=" Status Code: "+this.statusCode),e},Object.defineProperties(bt.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),bt.prototype.addEventListener=function(e,t){r.Check.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(t);var n=this;const i=function(){n.removeEventListener(e,t)};return this._removeListenerFnAry.push(i),i},bt.prototype.removeAllListener=function(){while(this._removeListenerFnAry.length>0){var e=this._removeListenerFnAry.pop();e()}},bt.prototype.removeEventListener=function(e,t){r.Check.typeOf.func("listener",e);for(var n=this._listeners,i=this._scopes,a=-1,o=0;o<n.length;o++)if(n[o]===e&&i[o]===t){a=o;break}return-1!==a&&(this._insideRaiseEvent?(this._toRemove.push(a),n[a]=void 0,i[a]=void 0):(n.splice(a,1),i.splice(a,1)),!0)},bt.prototype.raiseEvent=function(){var e;this._insideRaiseEvent=!0;var r=this._listeners,n=this._scopes,i=r.length;for(e=0;e<i;e++){var a=r[e];t.defined(a)&&r[e].apply(n[e],arguments)}var o=this._toRemove;if(i=o.length,i>0){for(o.sort(Tt),e=0;e<i;e++){var s=o[e];r.splice(s,1),n.splice(s,1)}o.length=0}this._insideRaiseEvent=!1},Object.defineProperties(Mt.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){this._maximumLength=e,this._length>e&&e>0&&(this._length=e,this._array.length=e)}},comparator:{get:function(){return this._comparator}}}),Mt.prototype.reserve=function(e){e=t.defaultValue(e,this._length),this._array.length=e},Mt.prototype.heapify=function(e){e=t.defaultValue(e,0);var r=this._length,n=this._comparator,i=this._array,a=-1,o=!0;while(o){var s=2*(e+1),u=s-1;a=u<r&&n(i[u],i[e])<0?u:e,s<r&&n(i[s],i[a])<0&&(a=s),a!==e?(It(i,a,e),e=a):o=!1}},Mt.prototype.resort=function(){for(var e=this._length,t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},Mt.prototype.insert=function(e){r.Check.defined("element",e);var n,i=this._array,a=this._comparator,o=this._maximumLength,s=this._length++;s<i.length?i[s]=e:i.push(e);while(0!==s){var u=Math.floor((s-1)/2);if(!(a(i[s],i[u])<0))break;It(i,s,u),s=u}return t.defined(o)&&this._length>o&&(n=i[o],this._length=o),n},Mt.prototype.pop=function(e){if(e=t.defaultValue(e,0),0!==this._length){r.Check.typeOf.number.lessThan("index",e,this._length);var n=this._array,i=n[e];return It(n,e,--this._length),this.heapify(e),i}};var Ut={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},Nt=20,Ft=new Mt({comparator:Pt});Ft.maximumLength=Nt,Ft.reserve(Nt);var Lt=[],jt={},zt="undefined"!==typeof document?new rt(document.location.href):new rt,Bt=new bt;function Vt(){}function kt(e){t.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function Wt(e){var r=t.defaultValue(Vt.requestsByServer[e],Vt.maximumRequestsPerServer);return jt[e]<r}function Yt(e){return e.state===Dt.UNISSUED&&(e.state=Dt.ISSUED,e.deferred=t.when.defer()),e.deferred.promise}function Ht(e){return function(t){e.state!==Dt.CANCELLED&&(--Ut.numberOfActiveRequests,--jt[e.serverKey],Bt.raiseEvent(),e.state=Dt.RECEIVED,e.deferred.resolve(t))}}function Xt(e){return function(t){e.state!==Dt.CANCELLED&&(++Ut.numberOfFailedRequests,--Ut.numberOfActiveRequests,--jt[e.serverKey],Bt.raiseEvent(t),e.state=Dt.FAILED,e.deferred.reject(t))}}function Jt(e){var t=Yt(e);return e.state=Dt.ACTIVE,Lt.push(e),++Ut.numberOfActiveRequests,++Ut.numberOfActiveRequestsEver,++jt[e.serverKey],e.requestFunction().then(Ht(e)).otherwise(Xt(e)),t}function Gt(e){var r=e.state===Dt.ACTIVE;e.state=Dt.CANCELLED,++Ut.numberOfCancelledRequests,e.deferred.reject(),r&&(--Ut.numberOfActiveRequests,--jt[e.serverKey],++Ut.numberOfCancelledActiveRequests),t.defined(e.cancelFunction)&&e.cancelFunction()}function Zt(){Vt.debugShowStatistics&&(0===Ut.numberOfActiveRequests&&Ut.lastNumberOfActiveRequests>0&&(Ut.numberOfAttemptedRequests>0&&(console.log("Number of attempted requests: "+Ut.numberOfAttemptedRequests),Ut.numberOfAttemptedRequests=0),Ut.numberOfCancelledRequests>0&&(console.log("Number of cancelled requests: "+Ut.numberOfCancelledRequests),Ut.numberOfCancelledRequests=0),Ut.numberOfCancelledActiveRequests>0&&(console.log("Number of cancelled active requests: "+Ut.numberOfCancelledActiveRequests),Ut.numberOfCancelledActiveRequests=0),Ut.numberOfFailedRequests>0&&(console.log("Number of failed requests: "+Ut.numberOfFailedRequests),Ut.numberOfFailedRequests=0)),Ut.lastNumberOfActiveRequests=Ut.numberOfActiveRequests)}Vt.maximumRequests=50,Vt.maximumRequestsPerServer=6,Vt.requestsByServer={"api.cesium.com:443":18,"assets.cesium.com:443":18},Vt.throttleRequests=!0,Vt.debugShowStatistics=!1,Vt.requestCompletedEvent=Bt,Object.defineProperties(Vt,{statistics:{get:function(){return Ut}},priorityHeapLength:{get:function(){return Nt},set:function(e){if(e<Nt)while(Ft.length>e){var t=Ft.pop();Gt(t)}Nt=e,Ft.maximumLength=e,Ft.reserve(e)}}}),Vt.update=function(){var e,t,r=0,n=Lt.length;for(e=0;e<n;++e)t=Lt[e],t.cancelled&&Gt(t),t.state===Dt.ACTIVE?r>0&&(Lt[e-r]=t):++r;Lt.length-=r;var i=Ft.internalArray,a=Ft.length;for(e=0;e<a;++e)kt(i[e]);Ft.resort();var o=Math.max(Vt.maximumRequests-Lt.length,0),s=0;while(s<o&&Ft.length>0)t=Ft.pop(),t.cancelled?Gt(t):!t.throttleByServer||Wt(t.serverKey)?(Jt(t),++s):Gt(t);Zt()},Vt.getServerKey=function(e){r.Check.typeOf.string("url",e);var n=new rt(e).resolve(zt),i=n.authority;/:/.test(i)||(i=i+":"+("https"===n.scheme?"443":"80"));var a=jt[i];return t.defined(a)||(jt[i]=0),i},Vt.request=function(e){if(r.Check.typeOf.object("request",e),r.Check.typeOf.string("request.url",e.url),r.Check.typeOf.func("request.requestFunction",e.requestFunction),gt(e.url)||yt(e.url))return Bt.raiseEvent(),e.state=Dt.RECEIVED,e.requestFunction();if(++Ut.numberOfAttemptedRequests,t.defined(e.serverKey)||(e.serverKey=Vt.getServerKey(e.url)),!Vt.throttleRequests||!e.throttleByServer||Wt(e.serverKey)){if(!Vt.throttleRequests||!e.throttle)return Jt(e);if(!(Lt.length>=Vt.maximumRequests)){kt(e);var n=Ft.insert(e);if(t.defined(n)){if(n===e)return;Gt(n)}return Yt(e)}}},Vt.clearForSpecs=function(){while(Ft.length>0){var e=Ft.pop();Gt(e)}for(var t=Lt.length,r=0;r<t;++r)Gt(Lt[r]);Lt.length=0,jt={},Ut.numberOfAttemptedRequests=0,Ut.numberOfActiveRequests=0,Ut.numberOfCancelledRequests=0,Ut.numberOfCancelledActiveRequests=0,Ut.numberOfFailedRequests=0,Ut.numberOfActiveRequestsEver=0,Ut.lastNumberOfActiveRequests=0},Vt.numberOfActiveRequestsByServer=function(e){return jt[e]},Vt.requestHeap=Ft,Vt.destroy=function(){Ft._array=[]};var Qt={},Kt={};function $t(e){var r=new rt(e);r.normalize();var n=r.getAuthority();if(t.defined(n)){if(-1!==n.indexOf("@")){var i=n.split("@");n=i[1]}if(-1===n.indexOf(":")){var a=r.getScheme();if(t.defined(a)||(a=window.location.protocol,a=a.substring(0,a.length-1)),"http"===a)n+=":80";else{if("https"!==a)return;n+=":443"}}return n}}Qt.add=function(e,n){if(!t.defined(e))throw new r.DeveloperError("host is required.");if(!t.defined(n)||n<=0)throw new r.DeveloperError("port is required to be greater than 0.");var i=e.toLowerCase()+":"+n;t.defined(Kt[i])||(Kt[i]=!0)},Qt.remove=function(e,n){if(!t.defined(e))throw new r.DeveloperError("host is required.");if(!t.defined(n)||n<=0)throw new r.DeveloperError("port is required to be greater than 0.");var i=e.toLowerCase()+":"+n;t.defined(Kt[i])&&delete Kt[i]},Qt.contains=function(e){if(!t.defined(e))throw new r.DeveloperError("url is required.");var n=$t(e);return!(!t.defined(n)||!t.defined(Kt[n]))},Qt.clear=function(){Kt={}};var er,tr=function(){try{var e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(t){return!1}}();function rr(e,r,n,i){var a,o=e.query;if(!t.defined(o)||0===o.length)return{};if(-1===o.indexOf("=")){var s={};s[o]=void 0,a=s}else a=_t(o);r._queryParameters=n?or(a,r._queryParameters,i):a,e.query=void 0}function nr(e,r){var n=r._queryParameters,i=Object.keys(n);1!==i.length||t.defined(n[i[0]])?e.query=Ct(n):e.query=i[0]}function ir(e,r){return t.defined(e)?t.defined(e.clone)?e.clone():dt(e):r}function ar(e){if(e.state===Dt.ISSUED||e.state===Dt.ACTIVE)throw new o.RuntimeError("The Resource is already being fetched.");e.state=Dt.UNISSUED,e.deferred=void 0}function or(e,r,n){if(!n)return ft(e,r);var i=dt(e,!0);for(var a in r)if(r.hasOwnProperty(a)){var o=i[a],s=r[a];t.defined(o)?(Array.isArray(o)||(o=i[a]=[o]),i[a]=o.concat(s)):i[a]=Array.isArray(s)?s.slice():s}return i}function sr(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),"string"===typeof e&&(e={url:e}),r.Check.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=ir(e.templateValues,{}),this._queryParameters=ir(e.queryParameters,{}),this.headers=ir(e.headers,{}),this.request=t.defaultValue(e.request,new qt),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=t.defaultValue(e.retryAttempts,0),this._retryCount=0;var n=new rt(e.url);rr(n,this,!0,!0),n.fragment=void 0,this._url=n.toString()}function ur(e){var r=e.resource,n=e.flipY,i=e.preferImageBitmap,a=r.request;a.url=r.url,a.requestFunction=function(){var e=!1;r.isDataUri||r.isBlobUri||(e=r.isCrossOriginUrl);var o=t.when.defer();return sr._Implementations.createImage(a,e,o,n,i),o.promise};var o=Vt.request(a);if(t.defined(o))return o.otherwise((function(e){return a.state!==Dt.FAILED?t.when.reject(e):r.retryOnError(e).then((function(o){return o?(a.state=Dt.UNISSUED,a.deferred=void 0,ur({resource:r,flipY:n,preferImageBitmap:i})):t.when.reject(e)}))}))}function lr(e,r,n){var i={};i[r]=n,e.setQueryParameters(i);var a=e.request;a.url=e.url,a.requestFunction=function(){var r=t.when.defer();return window[n]=function(e){r.resolve(e);try{delete window[n]}catch(t){window[n]=void 0}},sr._Implementations.loadAndExecuteScript(e.url,n,r),r.promise};var o=Vt.request(a);if(t.defined(o))return o.otherwise((function(i){return a.state!==Dt.FAILED?t.when.reject(i):e.retryOnError(i).then((function(o){return o?(a.state=Dt.UNISSUED,a.deferred=void 0,lr(e,r,n)):t.when.reject(i)}))}))}sr.createIfNeeded=function(e){return e instanceof sr?e.getDerivedResource({request:e.request}):"string"!==typeof e?e:new sr({url:e})},sr.supportsImageBitmapOptions=function(){if(t.defined(er))return er;if("function"!==typeof createImageBitmap)return er=t.when.resolve(!1),er;var e="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWP4////fwAJ+wP9CNHoHgAAAABJRU5ErkJggg==";return er=sr.fetchBlob({url:e}).then((function(e){return createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none"})})).then((function(e){return!0})).otherwise((function(){return!1})),er},Object.defineProperties(sr,{isBlobSupported:{get:function(){return tr}}}),Object.defineProperties(sr.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){var t=new rt(e);rr(t,this,!1),t.fragment=void 0,this._url=t.toString()}},extension:{get:function(){return ht(this._url)}},isDataUri:{get:function(){return gt(this._url)}},isBlobUri:{get:function(){return yt(this._url)}},isCrossOriginUrl:{get:function(){return vt(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}}}),sr.prototype.getUrlComponent=function(e,r){if(this.isDataUri)return this._url;var n=new rt(this._url);e&&nr(n,this);var i=n.toString().replace(/%7B/g,"{").replace(/%7D/g,"}"),a=this._templateValues;return i=i.replace(/{(.*?)}/g,(function(e,r){var n=a[r];return t.defined(n)?encodeURIComponent(n):e})),r&&t.defined(this.proxy)&&(i=this.proxy.getURL(i)),i},sr.prototype.setQueryParameters=function(e,t){this._queryParameters=t?or(this._queryParameters,e,!1):or(e,this._queryParameters,!1)},sr.prototype.appendQueryParameters=function(e){this._queryParameters=or(e,this._queryParameters,!0)},sr.prototype.setTemplateValues=function(e,t){this._templateValues=t?ft(this._templateValues,e):ft(e,this._templateValues)},sr.prototype.getDerivedResource=function(e){var r=this.clone();if(r._retryCount=0,t.defined(e.url)){var n=new rt(e.url),i=t.defaultValue(e.preserveQueryParameters,!1);rr(n,r,!0,i),n.fragment=void 0,r._url=n.resolve(new rt(ct(this._url))).toString()}return t.defined(e.queryParameters)&&(r._queryParameters=ft(e.queryParameters,r._queryParameters)),t.defined(e.templateValues)&&(r._templateValues=ft(e.templateValues,r.templateValues)),t.defined(e.headers)&&(r.headers=ft(e.headers,r.headers)),t.defined(e.proxy)&&(r.proxy=e.proxy),t.defined(e.request)&&(r.request=e.request),t.defined(e.retryCallback)&&(r.retryCallback=e.retryCallback),t.defined(e.retryAttempts)&&(r.retryAttempts=e.retryAttempts),r},sr.prototype.retryOnError=function(e){var r=this.retryCallback;if("function"!==typeof r||this._retryCount>=this.retryAttempts)return t.when(!1);var n=this;return t.when(r(this,e)).then((function(e){return++n._retryCount,e}))},sr.prototype.clone=function(e){return t.defined(e)||(e=new sr({url:this._url})),e._url=this._url,e._queryParameters=dt(this._queryParameters),e._templateValues=dt(this._templateValues),e.headers=dt(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e},sr.prototype.getBaseUri=function(e){return pt(this.getUrlComponent(e),e)},sr.prototype.appendForwardSlash=function(){this._url=lt(this._url)},sr.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},sr.fetchArrayBuffer=function(e){var t=new sr(e);return t.fetchArrayBuffer()},sr.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},sr.fetchBlob=function(e){var t=new sr(e);return t.fetchBlob()},sr.prototype.fetchImage=function(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var r=t.defaultValue(e.preferImageBitmap,!1),n=t.defaultValue(e.preferBlob,!1),i=t.defaultValue(e.flipY,!1);if(ar(this.request),!tr||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return ur({resource:this,flipY:i,preferImageBitmap:r});var a,o,s,u,l=this.fetchBlob();return t.defined(l)?sr.supportsImageBitmapOptions().then((function(e){return a=e,o=a&&r,l})).then((function(e){if(t.defined(e)){if(u=e,o)return sr.createImageBitmapFromBlob(e,{flipY:i,premultiplyAlpha:!1});var r=window.URL.createObjectURL(e);return s=new sr({url:r}),ur({resource:s,flipY:i,preferImageBitmap:!1})}})).then((function(e){if(t.defined(e))return e.blob=u,o||window.URL.revokeObjectURL(s.url),e})).otherwise((function(e){return t.defined(s)&&window.URL.revokeObjectURL(s.url),e.blob=u,t.when.reject(e)})):void 0},sr.fetchImage=function(e){var t=new sr(e);return t.fetchImage({flipY:e.flipY,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},sr.prototype.fetchText=function(){return this.fetch({responseType:"text"})},sr.fetchText=function(e){var t=new sr(e);return t.fetchText()},sr.prototype.fetchJson=function(){var e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});const r=this;if(t.defined(e))return e.then((function(e){if(!t.defined(e))return;let n={};try{n=JSON.parse(e)}catch(i){console.error("====JSON parse ERR:===="),console.log(e),console.log(r),console.log(i)}return n}))},sr.fetchJson=function(e){var t=new sr(e);return t.fetchJson()},sr.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},sr.fetchXML=function(e){var t=new sr(e);return t.fetchXML()},sr.prototype.fetchJsonp=function(e){var r;e=t.defaultValue(e,"callback"),ar(this.request);do{r="loadJsonp"+Math.random().toString().substring(2,8)}while(t.defined(window[r]));return lr(this,e,r)},sr.fetchJsonp=function(e){var t=new sr(e);return t.fetchJsonp(e.callbackParameterName)},sr.prototype._makeRequest=function(e){var r=this;ar(r.request);var n=r.request;n.url=r.url,n.requestFunction=function(){var i=e.responseType,a=ft(e.headers,r.headers),o=e.overrideMimeType,s=e.method,u=e.data,l=t.when.defer(),d=sr._Implementations.loadWithXhr(r.url,i,s,u,a,l,o);return t.defined(d)&&t.defined(d.abort)&&(n.cancelFunction=function(){d.abort()}),l.promise};var i=Vt.request(n);if(t.defined(i))return i.then((function(e){return n.cancelFunction=void 0,e})).otherwise((function(i){return n.cancelFunction=void 0,n.state!==Dt.FAILED?t.when.reject(i):r.retryOnError(i).then((function(a){return a?(n.state=Dt.UNISSUED,n.deferred=void 0,r.fetch(e)):t.when.reject(i)}))}))};var dr=/^data:(.*?)(;base64)?,(.*)$/;function fr(e,t){var r=decodeURIComponent(t);return e?atob(r):r}function cr(e,t){for(var r=fr(e,t),n=new ArrayBuffer(r.length),i=new Uint8Array(n),a=0;a<r.length;a++)i[a]=r.charCodeAt(a);return n}function pr(e,n){n=t.defaultValue(n,"");var i=e[1],a=!!e[2],o=e[3];switch(n){case"":case"text":return fr(a,o);case"arraybuffer":return cr(a,o);case"blob":var s=cr(a,o);return new Blob([s],{type:i});case"document":var u=new DOMParser;return u.parseFromString(fr(a,o),i);case"json":return JSON.parse(fr(a,o));default:throw new r.DeveloperError("Unhandled responseType: "+n)}}function hr(e,t,r){var n=new Image;n.onload=function(){r.resolve(n)},n.onerror=function(e){r.reject(e)},t&&(Qt.contains(e)?n.crossOrigin="use-credentials":n.crossOrigin=""),n.src=e}function mr(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}function yr(e,t,r,n,i,a,s){var u=require("url").parse(e),l="https:"===u.protocol?require("https"):require("http"),d=require("zlib"),f={protocol:u.protocol,hostname:u.hostname,port:u.port,path:u.path,query:u.query,method:r,headers:i};l.request(f).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)a.reject(new Rt(e.statusCode,e,e.headers));else{var r=[];e.on("data",(function(e){r.push(e)})),e.on("end",(function(){var n=Buffer.concat(r);"gzip"===e.headers["content-encoding"]?d.gunzip(n,(function(e,r){e?a.reject(new o.RuntimeError("Error decompressing response.")):a.resolve(mr(r,t))})):a.resolve(mr(n,t))}))}})).on("error",(function(e){a.reject(new Rt)})).end()}sr.prototype.fetch=function(e){return e=ir(e,{}),e.method="GET",this._makeRequest(e)},sr.fetch=function(e){var t=new sr(e);return t.fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr.prototype.delete=function(e){return e=ir(e,{}),e.method="DELETE",this._makeRequest(e)},sr.delete=function(e){var t=new sr(e);return t.delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},sr.prototype.head=function(e){return e=ir(e,{}),e.method="HEAD",this._makeRequest(e)},sr.head=function(e){var t=new sr(e);return t.head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr.prototype.options=function(e){return e=ir(e,{}),e.method="OPTIONS",this._makeRequest(e)},sr.options=function(e){var t=new sr(e);return t.options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr.prototype.post=function(e,t){return r.Check.defined("data",e),t=ir(t,{}),t.method="POST",t.data=e,this._makeRequest(t)},sr.post=function(e){var t=new sr(e);return t.post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr.prototype.put=function(e,t){return r.Check.defined("data",e),t=ir(t,{}),t.method="PUT",t.data=e,this._makeRequest(t)},sr.put=function(e){var t=new sr(e);return t.put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr.prototype.patch=function(e,t){return r.Check.defined("data",e),t=ir(t,{}),t.method="PATCH",t.data=e,this._makeRequest(t)},sr.patch=function(e){var t=new sr(e);return t.patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},sr._Implementations={},sr._Implementations.createImage=function(e,r,n,i,a){var s=e.url;sr.supportsImageBitmapOptions().then((function(u){if(u&&a){var l="blob",d="GET",f=t.when.defer(),c=sr._Implementations.loadWithXhr(s,l,d,void 0,void 0,f,void 0,void 0,void 0);return t.defined(c)&&t.defined(c.abort)&&(e.cancelFunction=function(){c.abort()}),f.promise.then((function(r){if(e.cancelFunction=void 0,t.defined(r))return sr.createImageBitmapFromBlob(r,{flipY:i,premultiplyAlpha:!1});n.reject(new o.RuntimeError("Successfully retrieved "+s+" but it contained no content."))})).then(n.resolve)}hr(s,r,n)})).otherwise(n.reject)},sr.createImageBitmapFromBlob=function(e,t){return r.Check.defined("options",t),r.Check.typeOf.bool("options.flipY",t.flipY),r.Check.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none"})};var vr="undefined"===typeof XMLHttpRequest;function wr(){return new Promise(((e,t)=>{var r=indexedDB.open("ResourceCache",1);r.onupgradeneeded=function(e){var t=e.target.result;t.objectStoreNames.contains("resources")||t.createObjectStore("resources",{keyPath:"url"})},r.onsuccess=function(t){e(t.target.result)},r.onerror=function(e){t(e.target.error)}}))}function gr(e,t){return new Promise(((r,n)=>{var i=e.transaction(["resources"],"readonly"),a=i.objectStore("resources"),o=a.get(t);o.onsuccess=function(e){r(e.target.result?e.target.result.data:null)},o.onerror=function(e){n(e.target.error)}}))}function Er(e,t,r){return new Promise(((n,i)=>{var a=e.transaction(["resources"],"readwrite"),o=a.objectStore("resources"),s=o.put({url:t,data:r});s.onsuccess=function(){n()},s.onerror=function(e){i(e.target.error)}}))}function Cr(e){if(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=t.defaultValue(e.addNewLeapSeconds,!0),t.defined(e.data))xr(this,e.data);else if(t.defined(e.url)){var r=sr.createIfNeeded(e.url),n=this;this._downloadPromise=t.when(r.fetchJson(),(function(e){xr(n,e)}),(function(){n._dataError="An error occurred while retrieving the EOP data from the URL "+r.url+"."}))}else xr(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function _r(e,t){return et.compare(e.julianDate,t)}function xr(e,r){if(t.defined(r.columnNames))if(t.defined(r.samples)){var n=r.columnNames.indexOf("modifiedJulianDateUtc"),i=r.columnNames.indexOf("xPoleWanderRadians"),a=r.columnNames.indexOf("yPoleWanderRadians"),o=r.columnNames.indexOf("ut1MinusUtcSeconds"),s=r.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=r.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=r.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||i<0||a<0||o<0||s<0||u<0||l<0)e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns";else{var d,f=e._samples=r.samples,c=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=i,e._yPoleWanderRadiansColumn=a,e._ut1MinusUtcSecondsColumn=o,e._xCelestialPoleOffsetRadiansColumn=s,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=l,e._columnCount=r.columnNames.length,e._lastIndex=void 0;for(var p=e._addNewLeapSeconds,h=0,m=f.length;h<m;h+=e._columnCount){var y=f[h+n],v=f[h+l],w=y+Me.MODIFIED_JULIAN_DATE_DIFFERENCE,g=new et(w,v,Pe.TAI);if(c.push(g),p){if(v!==d&&t.defined(d)){var E=et.leapSeconds,C=Oe(E,g,_r);if(C<0){var _=new be(g,v);E.splice(~C,0,_)}}d=v}}}}else e._dataError="Error in loaded EOP data: The samples property is required.";else e._dataError="Error in loaded EOP data: The columnNames property is required."}function Dr(e,t,r,n,i){var a=r*n;i.xPoleWander=t[a+e._xPoleWanderRadiansColumn],i.yPoleWander=t[a+e._yPoleWanderRadiansColumn],i.xPoleOffset=t[a+e._xCelestialPoleOffsetRadiansColumn],i.yPoleOffset=t[a+e._yCelestialPoleOffsetRadiansColumn],i.ut1MinusUtc=t[a+e._ut1MinusUtcSecondsColumn]}function Or(e,t,r){return t+e*(r-t)}function Ar(e,t,r,n,i,a,o){var s=e._columnCount;if(a>t.length-1)return o.xPoleWander=0,o.yPoleWander=0,o.xPoleOffset=0,o.yPoleOffset=0,o.ut1MinusUtc=0,o;var u=t[i],l=t[a];if(u.equals(l)||n.equals(u))return Dr(e,r,i,s,o),o;if(n.equals(l))return Dr(e,r,a,s,o),o;var d=et.secondsDifference(n,u)/et.secondsDifference(l,u),f=i*s,c=a*s,p=r[f+e._ut1MinusUtcSecondsColumn],h=r[c+e._ut1MinusUtcSecondsColumn],m=h-p;if(m>.5||m<-.5){var y=r[f+e._taiMinusUtcSecondsColumn],v=r[c+e._taiMinusUtcSecondsColumn];y!==v&&(l.equals(n)?p=h:h-=v-y)}return o.xPoleWander=Or(d,r[f+e._xPoleWanderRadiansColumn],r[c+e._xPoleWanderRadiansColumn]),o.yPoleWander=Or(d,r[f+e._yPoleWanderRadiansColumn],r[c+e._yPoleWanderRadiansColumn]),o.xPoleOffset=Or(d,r[f+e._xCelestialPoleOffsetRadiansColumn],r[c+e._xCelestialPoleOffsetRadiansColumn]),o.yPoleOffset=Or(d,r[f+e._yCelestialPoleOffsetRadiansColumn],r[c+e._yCelestialPoleOffsetRadiansColumn]),o.ut1MinusUtc=Or(d,p,h),o}function qr(e,r,n){this.heading=t.defaultValue(e,0),this.pitch=t.defaultValue(r,0),this.roll=t.defaultValue(n,0)}sr._Implementations.loadWithXhr=function(e,r,n,i,a,s,u){var l=dr.exec(e);if(null===l){if(!vr){var d=new XMLHttpRequest;if(Qt.contains(e)&&(d.withCredentials=!0),d.open(n,e,!0),t.defined(u)&&t.defined(d.overrideMimeType)&&d.overrideMimeType(u),t.defined(a))for(var f in a)a.hasOwnProperty(f)&&d.setRequestHeader(f,a[f]);t.defined(r)&&(d.responseType=r);var c=!1;return"string"===typeof e&&(c=0===e.indexOf("file://")||"undefined"!==typeof window&&"file://"===window.location.origin),d.onload=function(){if(!(d.status<200||d.status>=300)||c&&0===d.status){var e=d.response,i=d.responseType;if("HEAD"===n||"OPTIONS"===n){var a=d.getAllResponseHeaders(),u=a.trim().split(/[\r\n]+/),l={};return u.forEach((function(e){var t=e.split(": "),r=t.shift();l[r]=t.join(": ")})),void s.resolve(l)}if(204===d.status)s.resolve();else if(!t.defined(e)||t.defined(r)&&i!==r)if("json"===r&&"string"===typeof e)try{s.resolve(JSON.parse(e))}catch(f){s.reject(f)}else(""===i||"document"===i)&&t.defined(d.responseXML)&&d.responseXML.hasChildNodes()?s.resolve(d.responseXML):""!==i&&"text"!==i||!t.defined(d.responseText)?s.reject(new o.RuntimeError("Invalid XMLHttpRequest response type.")):s.resolve(d.responseText);else s.resolve(e)}else s.reject(new Rt(d.status,d.response,d.getAllResponseHeaders()))},d.onerror=function(e){s.reject(new Rt)},d.send(i),d}yr(e,r,n,i,a,s)}else s.resolve(pr(l,r))},sr._Implementations.loadWithIndexDBAndXhr=function(e,r,n,i,a,s,u){let l;var d=dr.exec(e);if(null===d){if(!vr)return wr().then((l=>{gr(l,e).then((d=>{if(null===d){var f=new XMLHttpRequest;if(Qt.contains(e)&&(f.withCredentials=!0),f.open(n,e,!0),t.defined(u)&&t.defined(f.overrideMimeType)&&f.overrideMimeType(u),t.defined(a))for(var c in a)a.hasOwnProperty(c)&&f.setRequestHeader(c,a[c]);t.defined(r)&&(f.responseType=r);var p=!1;"string"===typeof e&&(p=0===e.indexOf("file://")||"undefined"!==typeof window&&"file://"===window.location.origin),f.onload=function(){if(!(f.status<200||f.status>=300)||p&&0===f.status){var i=f.response,a=f.responseType;if("HEAD"===n||"OPTIONS"===n){var u=f.getAllResponseHeaders(),d=u.trim().split(/[\r\n]+/),c={};return d.forEach((function(e){var t=e.split(": "),r=t.shift();c[r]=t.join(": ")})),void s.resolve(c)}if(204===f.status)s.resolve();else if(!t.defined(i)||t.defined(r)&&a!==r)if("json"===r&&"string"===typeof i)try{const t=JSON.parse(i);Er(l,e,t).then((()=>{s.resolve(t)}))}catch(h){s.reject(h)}else(""===a||"document"===a)&&t.defined(f.responseXML)&&f.responseXML.hasChildNodes()?s.resolve(f.responseXML):""!==a&&"text"!==a||!t.defined(f.responseText)?s.reject(new o.RuntimeError("Invalid XMLHttpRequest response type.")):Er(l,e,f.responseText).then((()=>{s.resolve(f.responseText)}));else"arraybuffer"===r?Er(l,e,i).then((()=>{s.resolve(i)})):s.resolve(i)}else s.reject(new Rt(f.status,f.response,f.getAllResponseHeaders()))},f.onerror=function(e){s.reject(new Rt)},f.send(i)}else s.resolve(d)})).catch((e=>{s.reject(new o.RuntimeError("IndexedDB read error: "+e))}))})).catch((e=>{s.reject(new o.RuntimeError("IndexedDB open error: "+e))})),l;yr(e,r,n,i,a,s)}else s.resolve(pr(d,r))},sr._Implementations.loadAndExecuteScript=function(e,t,r){return Et(e).otherwise(r.reject)},sr._DefaultImplementations={},sr._DefaultImplementations.createImage=sr._Implementations.createImage,sr._DefaultImplementations.loadWithXhr=sr._Implementations.loadWithXhr,sr._DefaultImplementations.loadAndExecuteScript=sr._Implementations.loadAndExecuteScript,sr.DEFAULT=Object.freeze(new sr({url:"undefined"===typeof document?"":document.location.href.split("?")[0]})),Cr.NONE=Object.freeze({getPromiseToLoad:function(){return t.when()},compute:function(e,r){return t.defined(r)?(r.xPoleWander=0,r.yPoleWander=0,r.xPoleOffset=0,r.yPoleOffset=0,r.ut1MinusUtc=0):r=new Ae(0,0,0,0,0),r}}),Cr.prototype.getPromiseToLoad=function(){return t.when(this._downloadPromise)},Cr.prototype.compute=function(e,r){if(t.defined(this._samples)){if(t.defined(r)||(r=new Ae(0,0,0,0,0)),0===this._samples.length)return r.xPoleWander=0,r.yPoleWander=0,r.xPoleOffset=0,r.yPoleOffset=0,r.ut1MinusUtc=0,r;var n=this._dates,i=this._lastIndex,a=0,s=0;if(t.defined(i)){var u=n[i],l=n[i+1],d=et.lessThanOrEquals(u,e),f=!t.defined(l),c=f||et.greaterThanOrEquals(l,e);if(d&&c)return a=i,!f&&l.equals(e)&&++a,s=a+1,Ar(this,n,this._samples,e,a,s,r),r}var p=Oe(n,e,et.compare,this._dateColumn);return p>=0?(p<n.length-1&&n[p+1].equals(e)&&++p,a=p,s=p):(s=~p,a=s-1,a<0&&(a=0)),this._lastIndex=a,Ar(this,n,this._samples,e,a,s,r),r}if(t.defined(this._dataError))throw new o.RuntimeError(this._dataError)},qr.fromQuaternion=function(e,i){if(!t.defined(e))throw new r.DeveloperError("quaternion is required");t.defined(i)||(i=new qr);var a=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),s=2*(e.w*e.x+e.y*e.z),u=1-2*(e.y*e.y+e.z*e.z),l=2*(e.w*e.z+e.x*e.y);return i.heading=-Math.atan2(l,u),i.roll=Math.atan2(s,o),i.pitch=-n.CesiumMath.asinClamped(a),i},qr.fromDegrees=function(e,i,a,o){if(!t.defined(e))throw new r.DeveloperError("heading is required");if(!t.defined(i))throw new r.DeveloperError("pitch is required");if(!t.defined(a))throw new r.DeveloperError("roll is required");return t.defined(o)||(o=new qr),o.heading=e*n.CesiumMath.RADIANS_PER_DEGREE,o.pitch=i*n.CesiumMath.RADIANS_PER_DEGREE,o.roll=a*n.CesiumMath.RADIANS_PER_DEGREE,o},qr.clone=function(e,r){if(t.defined(e))return t.defined(r)?(r.heading=e.heading,r.pitch=e.pitch,r.roll=e.roll,r):new qr(e.heading,e.pitch,e.roll)},qr.equals=function(e,r){return e===r||t.defined(e)&&t.defined(r)&&e.heading===r.heading&&e.pitch===r.pitch&&e.roll===r.roll},qr.equalsEpsilon=function(e,r,i,a){return e===r||t.defined(e)&&t.defined(r)&&n.CesiumMath.equalsEpsilon(e.heading,r.heading,i,a)&&n.CesiumMath.equalsEpsilon(e.pitch,r.pitch,i,a)&&n.CesiumMath.equalsEpsilon(e.roll,r.roll,i,a)},qr.prototype.clone=function(e){return qr.clone(this,e)},qr.prototype.equals=function(e){return qr.equals(this,e)},qr.prototype.equalsEpsilon=function(e,t,r){return qr.equalsEpsilon(this,e,t,r)},qr.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var Sr,Rr,br,Tr=/((?:.*\/)|^)Cesium\.js$/;function Mr(){for(var e=document.getElementsByTagName("script"),t=0,r=e.length;t<r;++t){var n=e[t].getAttribute("src"),i=Tr.exec(n);if(null!==i)return i[1]}}function Ir(e){return"undefined"===typeof document?e:(t.defined(Sr)||(Sr=document.createElement("a")),Sr.href=e,Sr.href=Sr.href,Sr.href)}function Pr(){if(t.defined(Rr))return Rr;var e;if(e="undefined"!==typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"===typeof define&&t.defined(define.amd)&&!define.amd.toUrlUndefined&&t.defined(require.toUrl)?ct("..",Fr("Core/buildModuleUrl.js")):Mr(),!t.defined(e))throw new r.DeveloperError("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Rr=new sr({url:Ir(e)}),Rr.appendForwardSlash(),Rr}function Ur(e){return Ir(require.toUrl("../"+e))}function Nr(e){var t=Pr().getDerivedResource({url:e});return t.url}function Fr(e){t.defined(br)||(br="object"===typeof define&&t.defined(define.amd)&&!define.amd.toUrlUndefined&&t.defined(require.toUrl)?Ur:Nr);var r=br(e);return r}function Lr(e,t,r){this.x=e,this.y=t,this.s=r}function jr(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=sr.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=t.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=t.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new et(this._sampleZeroJulianEphemerisDate,0,Pe.TAI),this._stepSizeDays=t.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=t.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=t.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];for(var r=this._interpolationOrder,n=this._denominators=new Array(r+1),i=this._xTable=new Array(r+1),a=Math.pow(this._stepSizeDays,r),o=0;o<=r;++o){n[o]=a,i[o]=o*this._stepSizeDays;for(var s=0;s<=r;++s)s!==o&&(n[o]*=o-s);n[o]=1/n[o]}this._work=new Array(r+1),this._coef=new Array(r+1)}Fr._cesiumScriptRegex=Tr,Fr._buildModuleUrlFromBaseUrl=Nr,Fr._clearBaseResource=function(){Rr=void 0},Fr.setBaseUrl=function(e){Rr=sr.DEFAULT.getDerivedResource({url:e})},Fr.getCesiumBaseUrl=Pr,Fr.getModUrl=function(e){return Fr(e)};var zr=new et(0,0,Pe.TAI);function Br(e,t,r){var n=zr;return n.dayNumber=t,n.secondsOfDay=r,et.daysDifference(n,e._sampleZeroDateTT)}function Vr(e,r){if(e._chunkDownloadsInProgress[r])return e._chunkDownloadsInProgress[r];var n,i=t.when.defer();e._chunkDownloadsInProgress[r]=i;var a=e._xysFileUrlTemplate;return n=t.defined(a)?a.getDerivedResource({templateValues:{0:r}}):new sr({url:Fr("Assets/IAU2006_XYS/IAU2006_XYS_"+r+".json")}),t.when(n.fetchJson(),(function(t){e._chunkDownloadsInProgress[r]=!1;for(var n=e._samples,a=t.samples,o=r*e._samplesPerXysFile*3,s=0,u=a.length;s<u;++s)n[o+s]=a[s];i.resolve()})),i.promise}jr.prototype.preload=function(e,r,n,i){var a=Br(this,e,r),o=Br(this,n,i),s=a/this._stepSizeDays-this._interpolationOrder/2|0;s<0&&(s=0);var u=o/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);for(var l=s/this._samplesPerXysFile|0,d=u/this._samplesPerXysFile|0,f=[],c=l;c<=d;++c)f.push(Vr(this,c));return t.when.all(f)},jr.prototype.computeXysRadians=function(e,r,n){var i=Br(this,e,r);if(!(i<0)){var a=i/this._stepSizeDays|0;if(!(a>=this._totalSamples)){var o=this._interpolationOrder,s=a-(o/2|0);s<0&&(s=0);var u=s+o;u>=this._totalSamples&&(u=this._totalSamples-1,s=u-o,s<0&&(s=0));var l=!1,d=this._samples;if(t.defined(d[3*s])||(Vr(this,s/this._samplesPerXysFile|0),l=!0),t.defined(d[3*u])||(Vr(this,u/this._samplesPerXysFile|0),l=!0),!l){t.defined(n)?(n.x=0,n.y=0,n.s=0):n=new Lr(0,0,0);var f,c,p=i-s*this._stepSizeDays,h=this._work,m=this._denominators,y=this._coef,v=this._xTable;for(f=0;f<=o;++f)h[f]=p-v[f];for(f=0;f<=o;++f){for(y[f]=1,c=0;c<=o;++c)c!==f&&(y[f]*=h[c]);y[f]*=m[f];var w=3*(s+f);n.x+=y[f]*d[w++],n.y+=y[f]*d[w++],n.s+=y[f]*d[w]}return n}}}};var kr={},Wr={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Yr={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Hr={},Xr={east:new n.Cartesian3,north:new n.Cartesian3,up:new n.Cartesian3,west:new n.Cartesian3,south:new n.Cartesian3,down:new n.Cartesian3},Jr=new n.Cartesian3,Gr=new n.Cartesian3,Zr=new n.Cartesian3;kr.localFrameToFixedFrameGenerator=function(e,o){if(!Wr.hasOwnProperty(e)||!Wr[e].hasOwnProperty(o))throw new r.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");var s,u=Wr[e][o],l=e+o;return t.defined(Hr[l])?s=Hr[l]:(s=function(s,l,d){if(!t.defined(s))throw new r.DeveloperError("origin is required.");if(t.defined(d)||(d=new a.Matrix4),n.Cartesian3.equalsEpsilon(s,n.Cartesian3.ZERO,n.CesiumMath.EPSILON14))n.Cartesian3.unpack(Yr[e],0,Jr),n.Cartesian3.unpack(Yr[o],0,Gr),n.Cartesian3.unpack(Yr[u],0,Zr);else if(n.CesiumMath.equalsEpsilon(s.x,0,n.CesiumMath.EPSILON14)&&n.CesiumMath.equalsEpsilon(s.y,0,n.CesiumMath.EPSILON14)){var f=n.CesiumMath.sign(s.z);n.Cartesian3.unpack(Yr[e],0,Jr),"east"!==e&&"west"!==e&&n.Cartesian3.multiplyByScalar(Jr,f,Jr),n.Cartesian3.unpack(Yr[o],0,Gr),"east"!==o&&"west"!==o&&n.Cartesian3.multiplyByScalar(Gr,f,Gr),n.Cartesian3.unpack(Yr[u],0,Zr),"east"!==u&&"west"!==u&&n.Cartesian3.multiplyByScalar(Zr,f,Zr)}else{l=t.defaultValue(l,i.Ellipsoid.WGS84),l.geodeticSurfaceNormal(s,Xr.up);var c=Xr.up,p=Xr.east;p.x=-s.y,p.y=s.x,p.z=0,n.Cartesian3.normalize(p,Xr.east),n.Cartesian3.cross(c,p,Xr.north),n.Cartesian3.multiplyByScalar(Xr.up,-1,Xr.down),n.Cartesian3.multiplyByScalar(Xr.east,-1,Xr.west),n.Cartesian3.multiplyByScalar(Xr.north,-1,Xr.south),Jr=Xr[e],Gr=Xr[o],Zr=Xr[u]}return d[0]=Jr.x,d[1]=Jr.y,d[2]=Jr.z,d[3]=0,d[4]=Gr.x,d[5]=Gr.y,d[6]=Gr.z,d[7]=0,d[8]=Zr.x,d[9]=Zr.y,d[10]=Zr.z,d[11]=0,d[12]=s.x,d[13]=s.y,d[14]=s.z,d[15]=1,d},Hr[l]=s),s},kr.eastNorthUpToFixedFrame=kr.localFrameToFixedFrameGenerator("east","north"),kr.northEastDownToFixedFrame=kr.localFrameToFixedFrameGenerator("north","east"),kr.northUpEastToFixedFrame=kr.localFrameToFixedFrameGenerator("north","up"),kr.northWestUpToFixedFrame=kr.localFrameToFixedFrameGenerator("north","west");var Qr=new G,Kr=new n.Cartesian3(1,1,1),$r=new a.Matrix4;kr.headingPitchRollToFixedFrame=function(e,i,o,s,u){r.Check.typeOf.object("HeadingPitchRoll",i),s=t.defaultValue(s,kr.eastNorthUpToFixedFrame);var l=G.fromHeadingPitchRoll(i,Qr),d=a.Matrix4.fromTranslationQuaternionRotationScale(n.Cartesian3.ZERO,l,Kr,$r);return u=s(e,o,u),a.Matrix4.multiply(u,d,u)};var en=new a.Matrix4,tn=new a.Matrix3;kr.headingPitchRollQuaternion=function(e,t,n,i,o){r.Check.typeOf.object("HeadingPitchRoll",t);var s=kr.headingPitchRollToFixedFrame(e,t,n,i,en),u=a.Matrix4.getMatrix3(s,tn);return G.fromRotationMatrix(u,o)};var rn=new n.Cartesian3(1,1,1),nn=new n.Cartesian3,an=new a.Matrix4,on=new a.Matrix4,sn=new a.Matrix3,un=new G;kr.fixedFrameToHeadingPitchRoll=function(e,o,s,u){r.Check.defined("transform",e),o=t.defaultValue(o,i.Ellipsoid.WGS84),s=t.defaultValue(s,kr.eastNorthUpToFixedFrame),t.defined(u)||(u=new qr);var l=a.Matrix4.getTranslation(e,nn);if(n.Cartesian3.equals(l,n.Cartesian3.ZERO))return u.heading=0,u.pitch=0,u.roll=0,u;var d=a.Matrix4.inverseTransformation(s(l,o,an),an),f=a.Matrix4.setScale(e,rn,on);f=a.Matrix4.setTranslation(f,n.Cartesian3.ZERO,f),d=a.Matrix4.multiply(d,f,d);var c=G.fromRotationMatrix(a.Matrix4.getMatrix3(d,sn),un);return c=G.normalize(c,c),qr.fromQuaternion(c,u)};var ln=24110.54841,dn=8640184.812866,fn=.093104,cn=-62e-7,pn=11772758384668e-32,hn=72921158553e-15,mn=n.CesiumMath.TWO_PI/86400,yn=new et;kr.computeTemeToPseudoFixedMatrix=function(e,i){if(!t.defined(e))throw new r.DeveloperError("date is required.");yn=et.addSeconds(e,-et.computeTaiMinusUtc(e),yn);var o,s=yn.dayNumber,u=yn.secondsOfDay,l=s-2451545;o=u>=43200?(l+.5)/Me.DAYS_PER_JULIAN_CENTURY:(l-.5)/Me.DAYS_PER_JULIAN_CENTURY;var d=ln+o*(dn+o*(fn+o*cn)),f=d*mn%n.CesiumMath.TWO_PI,c=hn+pn*(s-2451545.5),p=(u+.5*Me.SECONDS_PER_DAY)%Me.SECONDS_PER_DAY,h=f+c*p,m=Math.cos(h),y=Math.sin(h);return t.defined(i)?(i[0]=m,i[1]=-y,i[2]=0,i[3]=y,i[4]=m,i[5]=0,i[6]=0,i[7]=0,i[8]=1,i):new a.Matrix3(m,y,0,-y,m,0,0,0,1)},kr.iau2006XysData=new jr,kr.earthOrientationParameters=Cr.NONE;var vn=32.184,wn=2451545;kr.preloadIcrfFixed=function(e){var r=e.start.dayNumber,n=e.start.secondsOfDay+vn,i=e.stop.dayNumber,a=e.stop.secondsOfDay+vn,o=kr.iau2006XysData.preload(r,n,i,a),s=kr.earthOrientationParameters.getPromiseToLoad();return t.when.all([o,s])},kr.computeIcrfToFixedMatrix=function(e,n){if(!t.defined(e))throw new r.DeveloperError("date is required.");t.defined(n)||(n=new a.Matrix3);var i=kr.computeFixedToIcrfMatrix(e,n);if(t.defined(i))return a.Matrix3.transpose(i,n)};var gn=new Lr(0,0,0),En=new Ae(0,0,0,0,0,0),Cn=new a.Matrix3,_n=new a.Matrix3;kr.computeFixedToIcrfMatrix=function(e,i){if(!t.defined(e))throw new r.DeveloperError("date is required.");t.defined(i)||(i=new a.Matrix3);var o=kr.earthOrientationParameters.compute(e,En);if(t.defined(o)){var s=e.dayNumber,u=e.secondsOfDay+vn,l=kr.iau2006XysData.computeXysRadians(s,u,gn);if(t.defined(l)){var d=l.x+o.xPoleOffset,f=l.y+o.yPoleOffset,c=1/(1+Math.sqrt(1-d*d-f*f)),p=Cn;p[0]=1-c*d*d,p[3]=-c*d*f,p[6]=d,p[1]=-c*d*f,p[4]=1-c*f*f,p[7]=f,p[2]=-d,p[5]=-f,p[8]=1-c*(d*d+f*f);var h=a.Matrix3.fromRotationZ(-l.s,_n),m=a.Matrix3.multiply(p,h,Cn),y=e.dayNumber,v=e.secondsOfDay-et.computeTaiMinusUtc(e)+o.ut1MinusUtc,w=y-2451545,g=v/Me.SECONDS_PER_DAY,E=.779057273264+g+.00273781191135448*(w+g);E=E%1*n.CesiumMath.TWO_PI;var C=a.Matrix3.fromRotationZ(E,_n),_=a.Matrix3.multiply(m,C,Cn),x=Math.cos(o.xPoleWander),D=Math.cos(o.yPoleWander),O=Math.sin(o.xPoleWander),A=Math.sin(o.yPoleWander),q=s-wn+u/Me.SECONDS_PER_DAY;q/=36525;var S=-47e-6*q*n.CesiumMath.RADIANS_PER_DEGREE/3600,R=Math.cos(S),b=Math.sin(S),T=_n;return T[0]=x*R,T[1]=x*b,T[2]=O,T[3]=-D*b+A*O*R,T[4]=D*R+A*O*b,T[5]=-A*x,T[6]=-A*b-D*O*R,T[7]=A*R-D*O*b,T[8]=D*x,a.Matrix3.multiply(_,T,i)}}};var xn=new a.Cartesian4;kr.pointToWindowCoordinates=function(e,t,r,n){return n=kr.pointToGLWindowCoordinates(e,t,r,n),n.y=2*t[5]-n.y,n},kr.pointToGLWindowCoordinates=function(e,i,o,s){if(!t.defined(e))throw new r.DeveloperError("modelViewProjectionMatrix is required.");if(!t.defined(i))throw new r.DeveloperError("viewportTransformation is required.");if(!t.defined(o))throw new r.DeveloperError("point is required.");t.defined(s)||(s=new n.Cartesian2);var u=xn;return a.Matrix4.multiplyByVector(e,a.Cartesian4.fromElements(o.x,o.y,o.z,1,u),u),a.Cartesian4.multiplyByScalar(u,1/u.w,u),a.Matrix4.multiplyByVector(i,u,u),n.Cartesian2.fromCartesian4(u,s)};var Dn=new n.Cartesian3,On=new n.Cartesian3;kr.rotationMatrixFromPositionVelocity=function(e,i,o,s){if(!t.defined(e))throw new r.DeveloperError("position is required.");if(!t.defined(i))throw new r.DeveloperError("velocity is required.");let u=n.Cartesian3.UNIT_Z;var l=n.Cartesian3.cross(i,u,Dn);n.Cartesian3.equalsEpsilon(l,n.Cartesian3.ZERO,n.CesiumMath.EPSILON6)&&(l=n.Cartesian3.clone(n.Cartesian3.UNIT_X,l));var d=n.Cartesian3.cross(l,i,On);return n.Cartesian3.normalize(d,d),n.Cartesian3.cross(i,d,l),n.Cartesian3.negate(l,l),n.Cartesian3.normalize(l,l),t.defined(s)||(s=new a.Matrix3),s[0]=i.x,s[1]=i.y,s[2]=i.z,s[3]=l.x,s[4]=l.y,s[5]=l.z,s[6]=d.x,s[7]=d.y,s[8]=d.z,s};var An=new a.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),qn=new i.Cartographic,Sn=new n.Cartesian3,Rn=new n.Cartesian3,bn=new a.Matrix3,Tn=new a.Matrix4,Mn=new a.Matrix4;kr.basisTo2D=function(e,i,o){if(!t.defined(e))throw new r.DeveloperError("projection is required.");if(!t.defined(i))throw new r.DeveloperError("matrix is required.");if(!t.defined(o))throw new r.DeveloperError("result is required.");var s=a.Matrix4.getTranslation(i,Rn),u=e.ellipsoid,l=u.cartesianToCartographic(s,qn),d=e.project(l,Sn);n.Cartesian3.fromElements(d.z,d.x,d.y,d);var f=kr.eastNorthUpToFixedFrame(s,u,Tn),c=a.Matrix4.inverseTransformation(f,Mn),p=a.Matrix4.getMatrix3(i,bn),h=a.Matrix4.multiplyByMatrix3(c,p,o);return a.Matrix4.multiply(An,h,o),a.Matrix4.setTranslation(o,d,o),o},kr.wgs84To2DModelMatrix=function(e,i,o){if(!t.defined(e))throw new r.DeveloperError("projection is required.");if(!t.defined(i))throw new r.DeveloperError("center is required.");if(!t.defined(o))throw new r.DeveloperError("result is required.");var s=e.ellipsoid,u=kr.eastNorthUpToFixedFrame(i,s,Tn),l=a.Matrix4.inverseTransformation(u,Mn),d=s.cartesianToCartographic(i,qn),f=e.project(d,Sn);n.Cartesian3.fromElements(f.z,f.x,f.y,f);var c=a.Matrix4.fromTranslation(f,Tn);return a.Matrix4.multiply(An,l,o),a.Matrix4.multiply(c,o,o),o},e.FeatureDetection=J,e.Intersect=A,e.Quaternion=G,e.Resource=sr,e.Transforms=kr,e.buildModuleUrl=Fr}));