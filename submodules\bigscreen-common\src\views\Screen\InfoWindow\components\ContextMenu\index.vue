<!--
 * @Description: 右键菜单弹窗
 * @Autor: yaozhen
 * @Date: 2023-06-28 14:39:58
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2023-10-11 17:52:42
-->
<template>
  <ul class="context-menu">
    <li @click="gaugePoint">{{contextMenu.isGaugePoint ? '取消标记点' : '标记点'}}</li>
    <li @click="copyCoord">复制此处坐标</li>
    <li @click="copyAngle">复制当前视角</li>
  </ul>
</template>

<script setup>
  import useStore from '@Common/store'
  import { copyText } from '@Common/utils/index.js'
  import { ElMessage } from 'element-plus'
  const { contextMenu } = useStore()
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  })
  // 复制坐标
  const copyCoord = () => {
    const position = __Cesium.getWgs84ByWindowPosition(props.data.clientX, props.data.clientY)
    const result = copyText(JSON.stringify(position))
    // 判断是否复制成功
    ElMessage({
      message: result ? '复制成功' : '复制失败',
      type: result ? 'success' : 'warning'
    })
  }
  // 复制视角
  const copyAngle = () => {
    const result = copyText(__Cesium.ref.getViewpoint().toString())
    ElMessage({
      message: result ? '复制成功' : '复制失败',
      type: result ? 'success' : 'warning'
    })
  }
  const gaugePoint = () => {
    document.querySelector('#app').style.cursor = contextMenu.isGaugePoint ? 'default' : 'crosshair'
    contextMenu.isGaugePoint = !contextMenu.isGaugePoint
  }
</script>

<style lang="scss" scoped>
  .context-menu {
    background: rgba(23, 49, 71, 0.8);
    border: 1px solid #2b2c2f;
    li {
      position: relative;
      padding: 6px 10px;
      transition: background-color 0.25s;
      line-height: 22px;
      color: #edffff;
      cursor: pointer;
      &:hover {
        background: #3ea6ff;
      }
      &:before {
        content: '';
        display: block;
        height: 1px;
        width: 100%;
        background: linear-gradient(to left, transparent, rgba(255, 255, 255, 0.2), transparent);
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
</style>
