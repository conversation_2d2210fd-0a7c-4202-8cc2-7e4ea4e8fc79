define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Transforms-3ef1852a","./Matrix4-a50b021f","./AxisAlignedBoundingBox-9ebc7d89","./PolygonPipeline-cdeb6a0b","./EmWrapperManager-1ae94128"],(function(t,e,r,i,a,n,s,o,h){"use strict";class l{constructor(){this._pt2DAry=[],this._pt3DAry=[],this._under=0,this._height=0,this._matrix=n.Matrix4.clone(n.Matrix4.IDENTITY),this._originalMatrix=n.Matrix4.clone(n.Matrix4.IDENTITY)}static clone(t){const e=t._under,r=t._height,a=[];t._pt2DAry.forEach((t=>{a.push(new i.Cartesian2(t.x,t.y))}));const s=[];t._pt3DAry.forEach((t=>{s.push(new i.Cartesian3(t.x,t.y,t.z))}));const o=new l;return o._under=e,o._height=r,o._pt2DAry=a,o._pt3DAry=s,n.Matrix4.pack(t._matrix,o._matrix),n.Matrix4.pack(t._originalMatrix,o._originalMatrix),o}getBoundingbox(){const t=[];return this.getPtAry(!0,t),this.getPtAry(!1,t),s.AxisAlignedBoundingBox.fromPoints(t)}createTriangle(t,e){let r=t.length;this.getPtAry(!0,t);let i=t.length-r;this.getPtAry(!1,t);let a=0;for(let s=0;s<i;s++)a=s===i-1?0:s+1,e.push(r+s),e.push(r+a),e.push(r+i+a),e.push(r+s),e.push(r+i+a),e.push(r+i+s);const n=o.PolygonPipeline.triangulate(this._pt2DAry);e.push(...n.map((t=>r+i+t))),e.push(...n.reverse().map((t=>r+t)))}get pt3DAry(){return this._pt3DAry}set pt3DAry(t){this._pt3DAry=e.defaultValue(t,[])}set pt2DAry(t){this._pt2DAry=e.defaultValue(t,[]),this._pt2DAry.length>2&&o.PolygonPipeline.computeWindingOrder2D(this._pt2DAry)===o.WindingOrder.CLOCKWISE&&this._pt2DAry.reverse()}get pt2DAry(){return this._pt2DAry}set under(t){this._under=t}get under(){return this._under}set height(t){this._height=t}get height(){return this._height}get originalMatrix(){return this._originalMatrix}set originalMatrix(t){t.clone(this._originalMatrix)}set matrix(t){t.clone(this._matrix)}get matrix(){return this._matrix}get finalMatrix(){let t=n.Matrix4.clone(this._matrix);return n.Matrix4.multiply(t,this._originalMatrix,t),t}getPtAry(t,e){this._pt2DAry.forEach((r=>{let a=new i.Cartesian3(r.x,r.y,this._under);t||(a.z+=this._height),n.Matrix4.multiplyByPoint(this.finalMatrix,a,a),e.push(a)}))}transformXOY(t){let e=1e9;for(let r=0;r<this._pt2DAry.length;r++){let a=this._pt2DAry[r],s=new i.Cartesian3(a.x,a.y,this._under);n.Matrix4.multiplyByPoint(t,s,s),a.x=s.x,a.y=s.y,s.z<e&&(e=s.z)}this._under=e<1e9?e:0}}const y=new i.Cartesian3,p=new i.Cartesian3,_=new n.Matrix3,m=new n.Matrix3,d=new n.Matrix4;class A{constructor(){}init(t,s){if(!e.defined(h.emMod))throw new r.DeveloperError("initWebAssembly\u521d\u59cb\u5316\u5c1a\u672a\u5b8c\u6210");return this._LBDeal=new h.emMod.LBDeal,this._LBDeal.Init(t,s.x,s.y,s.z)?(this._centerENMatrix=a.Transforms.eastNorthUpToFixedFrame(i.Cartesian3.fromDegrees(s.x,s.y,s.z)),this._matrixArray=new Float64Array(n.Matrix4.toArray(n.Matrix4.IDENTITY)),this._pMatrixAry=h.emMod._malloc(this._matrixArray.byteLength),this._oneTyped32Array=new Float32Array([0,0,0]),this._pOnePt32Ary=h.emMod._malloc(this._oneTyped32Array.byteLength),this._oneTyped64Array=new Float64Array([0,0,0]),this._pOnePt64Ary=h.emMod._malloc(this._oneTyped64Array.byteLength),this._planishAry=null,!0):(h.emMod.destroy(this._LBDeal),this._LBDeal=void 0,!1)}destroy(){e.defined(this._LBDeal)&&(h.emMod.destroy(this._LBDeal),this._LBDeal=void 0,h.emMod._free(this._pMatrixAry),h.emMod._free(this._pOnePt32Ary),h.emMod._free(this._pOnePt64Ary),e.defined(this._planishAry)&&(h.emMod.destroy(this._planishAry),this._planishAry=null))}setPlanishBorderInfoAry(t){if(Array.isArray(t)){e.defined(this._planishAry)&&h.emMod.destroy(this._planishAry),this._planishAry=new h.emMod.LBPlanishAry,this._planishAry.SetPlanishNum(t.length);for(let e=0;e<t.length;e++){const r=l.clone(t[e]);this._planishAry.SetPlanishBot(e,r.under);const i=r.pt2DAry.length;this._planishAry.SetPlanishPtNum(e,i);for(let t=0;t<i;t++)this._planishAry.SetPlanishPtVal(e,t,r.pt2DAry[t].x,r.pt2DAry[t].y)}}}computeProjToCartesianAry(t,e,r){this.computeProjToCartesianAryImp(r,t,e,!1,this._LBDeal,!1)}computeCartesianToProjAry(t,e,r){this.computeProjToCartesianAryImp(r,t,e,!0,this._LBDeal,!1)}computeProjToCartesian(t,r,a,n){r=e.defaultValue(r,!1);let s=[t.x,t.y,t.z];return this.computeProjToCartesianAryImp(a,s,r,!1,this._LBDeal,!0),e.defined(n)?(n.x=s[0],n.y=s[1],n.z=s[2],n):new i.Cartesian3(s[0],s[1],s[2])}computeCartesianToProj(t,r,a,n){r=e.defaultValue(r,!1);let s=[t.x,t.y,t.z];return this.computeProjToCartesianAryImp(a,s,r,!0,this._LBDeal,!0),e.defined(n)?(n.x=s[0],n.y=s[1],n.z=s[2],n):new i.Cartesian3(s[0],s[1],s[2])}computeDegreeToProjAry(t,e){this.computeDegreeToProjImp(t,e,!1,this._LBDeal,!1)}computeProjToDegreeAry(t,e){this.computeDegreeToProjImp(t,e,!0,this._LBDeal,!1)}computeDegreeToProj(t,r,a){let n=[t.x,t.y,t.z];return this.computeDegreeToProjImp(n,r,!1,this._LBDeal,!0),e.defined(a)?(a.x=n[0],a.y=n[1],a.z=n[2],a):new i.Cartesian3(n[0],n[1],n[2])}computeProjToDegree(t,r,a){let n=[t.x,t.y,t.z];return this.computeDegreeToProjImp(n,r,!0,this._LBDeal,!0),e.defined(a)?(a.x=n[0],a.y=n[1],a.z=n[2],a):new i.Cartesian3(n[0],n[1],n[2])}cvtRelProjMatrixToCartesianMatrix(t,r){return e.defined(r)||(r=new n.Matrix4),n.Matrix4.getTranslation(t,y),n.Matrix4.getScale(t,p),n.Matrix4.getMatrix3(t,_),n.Matrix3.getRotation(_,m),n.Matrix4.fromScale(p,r),n.Matrix4.multiplyByMatrix3(r,m,r),this.computeProjToCartesian(y,!1,void 0,y),n.Matrix4.multiplyByPoint(this._centerENMatrix,y,y),a.Transforms.eastNorthUpToFixedFrame(y,void 0,d),n.Matrix4.multiply(d,r,r),r}computeProjToCartesianAryImp(t,i,a,s,o,l){if(!e.defined(o))throw new r.DeveloperError("\u8bf7\u5148\u6267\u884c init\u8fdb\u884c\u521d\u59cb\u5316");let y,p;l?(y=this._oneTyped32Array,y.set(i),p=this._pOnePt32Ary):(y=new Float32Array(i),p=h.emMod._malloc(y.byteLength)),h.emMod.HEAPF32.set(y,p/4),t=e.defaultValue(t,n.Matrix4.IDENTITY),this._matrixArray.set(n.Matrix4.toArray(t)),h.emMod.HEAPF64.set(this._matrixArray,this._pMatrixAry/8),s?o.ComputeCartesianToProj(p,y.length,this._pMatrixAry,a,this._planishAry):o.ComputeProjToCartesian(p,y.length,this._pMatrixAry,a);for(let e=0;e<i.length;++e)i[e]=h.emMod.HEAPF32[(p>>2)+e];l||h.emMod._free(p)}computeDegreeToProjImp(t,i,a,n,s){if(!e.defined(n))throw new r.DeveloperError("\u8bf7\u5148\u6267\u884c init\u8fdb\u884c\u521d\u59cb\u5316");let o,l;s?(o=this._oneTyped64Array,o.set(t),l=this._pOnePt64Ary):(o=new Float64Array(t),l=h.emMod._malloc(o.byteLength)),h.emMod.HEAPF64.set(o,l/8),a?n.TranformProjToDegree(l,o.length,i):n.TranformDegreeToProj(l,o.length,i);for(let e=0;e<t.length;++e)t[e]=h.emMod.HEAPF64[(l>>3)+e];s||h.emMod._free(l)}}t.BorderInfo=l,t.EmLBDeal=A}));