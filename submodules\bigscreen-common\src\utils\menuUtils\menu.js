// import { onUnmounted } from 'vue'
import useStore from '@Common/store/index.js'

import { tryOnUnmounted } from '@vueuse/core'

// console.log(useStore())
export default {
  // menu:useStore().menu,
  /**
   * @callback ActiveCallback
   * @param {boolean} bool 对应code的bool值
   * @param {Object} item 对应code的item项
   */
  /**
   *
   * @param {string} code 对应的code
   * @param {ActiveCallback} func 要添加的函数
   * @description 函数参数
   * @param {boolean} bool 对应code的bool值
   * @param {Object} item 对应code的item项
   */
  addFunction: function (code, funcion, id, context) {
    const { menu } = useStore()
    // console.log('传入id' + id)
    const finalId = id || (id === 0 ? 0 : undefined)
    const key = menu.addFunction(code, { func: funcion, disabled: false, id: finalId, context: context || undefined })
    tryOnUnmounted(() => {
      menu.deleteFunction(code, key)
    })
  },
  /**
   * 添加函数并禁止默认行为
   */
  addFunctionAndDisableDefault: function (code, func, id, context) {
    const { menu } = useStore()
    menu.disableFunction(code, 0)
    // console.log('传入id' + id)
    const finalId = id || (id === 0 ? 0 : undefined)
    const key = menu.addFunction(code, { func: func, disabled: false, id: finalId, context: context || undefined })
    tryOnUnmounted(() => {
      menu.deleteFunction(code, key)
    })
  },
  /**
   * @callback ActiveClickCallback
   * @param {Object} item 对应设备种类的Item对象
   * @param {Object} e 点击时传入的事件对象
   * @param {Object} movement 点击时传入的对象
   */
  /**
   *
   * @param {string} code 对应的code
   * @param {ActiveClickCallback} func 要添加的函数
   * @description 函数参数
   * @param {boolean} bool 对应code的bool值
   * @param {Object} item 对应code的item项
   */
  addClickFunction: function (code, funcion, id, context) {
    const { menu } = useStore()
    const key = menu.addClickFunction(code, { func: funcion, disabled: false, id: id || undefined, context: context || undefined })
    tryOnUnmounted(() => {
      menu.deleteClickFunction(code, key)
    })
  },
  /**
   * 添加点击函数并禁止默认行为
   */
  addClickFunctionAndDisableDefault: function (code, funcion, id, context) {
    const { menu } = useStore()
    menu.disableClickFunction(code, 0)
    const key = menu.addClickFunction(code, { func: funcion, disabled: false, id: id || undefined, context: context || undefined })
    tryOnUnmounted(() => {
      menu.deleteClickFunction(code, key)
    })
  },
  /**
   * 设置设备列表
   * @param {Array} arr 设备列表
   */
  setDevices: function (arr) {
    const { menu } = useStore()
    menu.setDevices(arr)
  },
  /**
   *
   * @returns 获取设备列表的promise
   */
  getDevices: function () {
    const { menu } = useStore()
    return menu.getDevices()
  },
  /**
   * @description 设置唯一辨识字段,默认为code
   * @param {string} key 字段名
   */
  setKeyword: function (key) {
    const { menu } = useStore()
    menu.keyword = key
  },
  /**
   * @description 设置子菜单字段,默认为subs
   * @param {string} key 字段名
   */
  setSubs: function (key) {
    const { menu } = useStore()
    menu.subs = key
  },
  /**
   * 返回所有设备，包含子菜单项目
   * @param {Array} filter 需要排除的层级数组，如[0]表示排除最顶级的菜单项
   * @returns promise
   */
  getAlldevices: function (filter = []) {
    const { menu } = useStore()
    return menu.getAllDevices(filter)
  },
  /**
   * 根据code返回设备
   */
  getDeviceByCode: function (code) {
    const { menu } = useStore()
    return menu.getItemByCode(code)
  },
  /**
   * 调用某个code对应的函数
   */
  callFunction: function (code) {
    const { menu } = useStore()
    menu.callFunc(code)
  },
  /**
   * 调用点击撒点函数
   */
  callClickFunction: function (code, e, pointState, obj) {
    const { menu } = useStore()
    menu.callClickFunc(code, e, pointState, obj)
  }
}
