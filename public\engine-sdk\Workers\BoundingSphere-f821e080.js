define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./Transforms-3ef1852a","./Matrix4-a50b021f"],(function(a,e,n,r,t,i,s){"use strict";function u(a){this._ellipsoid=e.defaultValue(a,t.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}function c(a,n){this.start=e.defaultValue(a,0),this.stop=e.defaultValue(n,0)}function d(a,n){this.center=r.Cartesian3.clone(e.defaultValue(a,r.Cartesian3.ZERO)),this.radius=e.defaultValue(n,0)}Object.defineProperties(u.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),u.prototype.project=function(a,n){var t=this._semimajorAxis,i=a.longitude*t,s=a.latitude*t,u=a.height;return e.defined(n)?(n.x=i,n.y=s,n.z=u,n):new r.Cartesian3(i,s,u)},u.prototype.unproject=function(a,r){if(!e.defined(a))throw new n.DeveloperError("cartesian is required");var i=this._oneOverSemimajorAxis,s=a.x*i,u=a.y*i,c=a.z;return e.defined(r)?(r.longitude=s,r.latitude=u,r.height=c,r):new t.Cartographic(s,u,c)};var o=new r.Cartesian3,C=new r.Cartesian3,l=new r.Cartesian3,f=new r.Cartesian3,y=new r.Cartesian3,h=new r.Cartesian3,m=new r.Cartesian3,v=new r.Cartesian3,x=new r.Cartesian3,p=new r.Cartesian3,g=new r.Cartesian3,z=new r.Cartesian3,w=4/3*r.CesiumMath.PI;d.fromPoints=function(a,n){if(e.defined(n)||(n=new d),!e.defined(a)||0===a.length)return n.center=r.Cartesian3.clone(r.Cartesian3.ZERO,n.center),n.radius=0,n;var t,i=r.Cartesian3.clone(a[0],m),s=r.Cartesian3.clone(i,o),u=r.Cartesian3.clone(i,C),c=r.Cartesian3.clone(i,l),w=r.Cartesian3.clone(i,f),b=r.Cartesian3.clone(i,y),S=r.Cartesian3.clone(i,h),q=a.length;for(t=1;t<q;t++){r.Cartesian3.clone(a[t],i);var R=i.x,E=i.y,O=i.z;R<s.x&&r.Cartesian3.clone(i,s),R>w.x&&r.Cartesian3.clone(i,w),E<u.y&&r.Cartesian3.clone(i,u),E>b.y&&r.Cartesian3.clone(i,b),O<c.z&&r.Cartesian3.clone(i,c),O>S.z&&r.Cartesian3.clone(i,S)}var M=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(w,s,v)),P=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(b,u,v)),V=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(S,c,v)),I=s,j=w,B=M;P>B&&(B=P,I=u,j=b),V>B&&(B=V,I=c,j=S);var D=x;D.x=.5*(I.x+j.x),D.y=.5*(I.y+j.y),D.z=.5*(I.z+j.z);var T=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(j,D,v)),Z=Math.sqrt(T),_=p;_.x=s.x,_.y=u.y,_.z=c.z;var A=g;A.x=w.x,A.y=b.y,A.z=S.z;var N=r.Cartesian3.midpoint(_,A,z),k=0;for(t=0;t<q;t++){r.Cartesian3.clone(a[t],i);var W=r.Cartesian3.magnitude(r.Cartesian3.subtract(i,N,v));W>k&&(k=W);var G=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(i,D,v));if(G>T){var U=Math.sqrt(G);Z=.5*(Z+U),T=Z*Z;var H=U-Z;D.x=(Z*D.x+H*i.x)/U,D.y=(Z*D.y+H*i.y)/U,D.z=(Z*D.z+H*i.z)/U}}return Z<k?(r.Cartesian3.clone(D,n.center),n.radius=Z):(r.Cartesian3.clone(N,n.center),n.radius=k),n},d.fromInstancePoints=function(a,n,t){if(e.defined(n)||(n=new d),!e.defined(a)||0===t)return n.center=r.Cartesian3.clone(r.Cartesian3.ZERO,n.center),n.radius=0,n;var i,s=r.Cartesian3.clone(a[0],m),u=r.Cartesian3.clone(s,o),c=r.Cartesian3.clone(s,C),w=r.Cartesian3.clone(s,l),b=r.Cartesian3.clone(s,f),S=r.Cartesian3.clone(s,y),q=r.Cartesian3.clone(s,h);for(i=1;i<t;i++){r.Cartesian3.clone(a[i],s);var R=s.x,E=s.y,O=s.z;R<u.x&&r.Cartesian3.clone(s,u),R>b.x&&r.Cartesian3.clone(s,b),E<c.y&&r.Cartesian3.clone(s,c),E>S.y&&r.Cartesian3.clone(s,S),O<w.z&&r.Cartesian3.clone(s,w),O>q.z&&r.Cartesian3.clone(s,q)}var M=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(b,u,v)),P=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(S,c,v)),V=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(q,w,v)),I=u,j=b,B=M;P>B&&(B=P,I=c,j=S),V>B&&(B=V,I=w,j=q);var D=x;D.x=.5*(I.x+j.x),D.y=.5*(I.y+j.y),D.z=.5*(I.z+j.z);var T=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(j,D,v)),Z=Math.sqrt(T),_=p;_.x=u.x,_.y=c.y,_.z=w.z;var A=g;A.x=b.x,A.y=S.y,A.z=q.z;var N=r.Cartesian3.midpoint(_,A,z),k=0;for(i=0;i<t;i++){r.Cartesian3.clone(a[i],s);var W=r.Cartesian3.magnitude(r.Cartesian3.subtract(s,N,v));W>k&&(k=W);var G=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(s,D,v));if(G>T){var U=Math.sqrt(G);Z=.5*(Z+U),T=Z*Z;var H=U-Z;D.x=(Z*D.x+H*s.x)/U,D.y=(Z*D.y+H*s.y)/U,D.z=(Z*D.z+H*s.z)/U}}return Z<k?(r.Cartesian3.clone(D,n.center),n.radius=Z):(r.Cartesian3.clone(N,n.center),n.radius=k),n},d.isIntersect=function(a,e){let n=r.Cartesian3.distance(a.center,e.center);return!(n>a.radius+e.radius)},d.isInclude=function(a,e){let n=r.Cartesian3.distance(a.center,e.center);return a.radius>n+e.radius};var b=new u,S=new r.Cartesian3,q=new r.Cartesian3,R=new t.Cartographic,E=new t.Cartographic;d.fromRectangle2D=function(a,e,n){return d.fromRectangleWithHeights2D(a,e,0,0,n)},d.fromRectangleWithHeights2D=function(a,n,i,s,u){if(e.defined(u)||(u=new d),!e.defined(a))return u.center=r.Cartesian3.clone(r.Cartesian3.ZERO,u.center),u.radius=0,u;n=e.defaultValue(n,b),t.Rectangle.southwest(a,R),R.height=i,t.Rectangle.northeast(a,E),E.height=s;var c=n.project(R,S),o=n.project(E,q),C=o.x-c.x,l=o.y-c.y,f=o.z-c.z;u.radius=.5*Math.sqrt(C*C+l*l+f*f);var y=u.center;return y.x=c.x+.5*C,y.y=c.y+.5*l,y.z=c.z+.5*f,u};var O=[];d.fromRectangle3D=function(a,n,i,s){if(n=e.defaultValue(n,t.Ellipsoid.WGS84),i=e.defaultValue(i,0),e.defined(s)||(s=new d),!e.defined(a))return s.center=r.Cartesian3.clone(r.Cartesian3.ZERO,s.center),s.radius=0,s;var u=t.Rectangle.subsample(a,n,i,O);return d.fromPoints(u,s)},d.fromVertices=function(a,t,i,s){if(e.defined(s)||(s=new d),!e.defined(a)||0===a.length)return s.center=r.Cartesian3.clone(r.Cartesian3.ZERO,s.center),s.radius=0,s;t=e.defaultValue(t,r.Cartesian3.ZERO),i=e.defaultValue(i,3),n.Check.typeOf.number.greaterThanOrEquals("stride",i,3);var u=m;u.x=a[0]+t.x,u.y=a[1]+t.y,u.z=a[2]+t.z;var c,w=r.Cartesian3.clone(u,o),b=r.Cartesian3.clone(u,C),S=r.Cartesian3.clone(u,l),q=r.Cartesian3.clone(u,f),R=r.Cartesian3.clone(u,y),E=r.Cartesian3.clone(u,h),O=a.length;for(c=0;c<O;c+=i){var M=a[c]+t.x,P=a[c+1]+t.y,V=a[c+2]+t.z;u.x=M,u.y=P,u.z=V,M<w.x&&r.Cartesian3.clone(u,w),M>q.x&&r.Cartesian3.clone(u,q),P<b.y&&r.Cartesian3.clone(u,b),P>R.y&&r.Cartesian3.clone(u,R),V<S.z&&r.Cartesian3.clone(u,S),V>E.z&&r.Cartesian3.clone(u,E)}var I=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(q,w,v)),j=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(R,b,v)),B=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(E,S,v)),D=w,T=q,Z=I;j>Z&&(Z=j,D=b,T=R),B>Z&&(Z=B,D=S,T=E);var _=x;_.x=.5*(D.x+T.x),_.y=.5*(D.y+T.y),_.z=.5*(D.z+T.z);var A=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(T,_,v)),N=Math.sqrt(A),k=p;k.x=w.x,k.y=b.y,k.z=S.z;var W=g;W.x=q.x,W.y=R.y,W.z=E.z;var G=r.Cartesian3.midpoint(k,W,z),U=0;for(c=0;c<O;c+=i){u.x=a[c]+t.x,u.y=a[c+1]+t.y,u.z=a[c+2]+t.z;var H=r.Cartesian3.magnitude(r.Cartesian3.subtract(u,G,v));H>U&&(U=H);var L=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(u,_,v));if(L>A){var X=Math.sqrt(L);N=.5*(N+X),A=N*N;var F=X-N;_.x=(N*_.x+F*u.x)/X,_.y=(N*_.y+F*u.y)/X,_.z=(N*_.z+F*u.z)/X}}return N<U?(r.Cartesian3.clone(_,s.center),s.radius=N):(r.Cartesian3.clone(G,s.center),s.radius=U),s},d.fromEncodedCartesianVertices=function(a,n,t){if(e.defined(t)||(t=new d),!e.defined(a)||!e.defined(n)||a.length!==n.length||0===a.length)return t.center=r.Cartesian3.clone(r.Cartesian3.ZERO,t.center),t.radius=0,t;var i=m;i.x=a[0]+n[0],i.y=a[1]+n[1],i.z=a[2]+n[2];var s,u=r.Cartesian3.clone(i,o),c=r.Cartesian3.clone(i,C),w=r.Cartesian3.clone(i,l),b=r.Cartesian3.clone(i,f),S=r.Cartesian3.clone(i,y),q=r.Cartesian3.clone(i,h),R=a.length;for(s=0;s<R;s+=3){var E=a[s]+n[s],O=a[s+1]+n[s+1],M=a[s+2]+n[s+2];i.x=E,i.y=O,i.z=M,E<u.x&&r.Cartesian3.clone(i,u),E>b.x&&r.Cartesian3.clone(i,b),O<c.y&&r.Cartesian3.clone(i,c),O>S.y&&r.Cartesian3.clone(i,S),M<w.z&&r.Cartesian3.clone(i,w),M>q.z&&r.Cartesian3.clone(i,q)}var P=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(b,u,v)),V=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(S,c,v)),I=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(q,w,v)),j=u,B=b,D=P;V>D&&(D=V,j=c,B=S),I>D&&(D=I,j=w,B=q);var T=x;T.x=.5*(j.x+B.x),T.y=.5*(j.y+B.y),T.z=.5*(j.z+B.z);var Z=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(B,T,v)),_=Math.sqrt(Z),A=p;A.x=u.x,A.y=c.y,A.z=w.z;var N=g;N.x=b.x,N.y=S.y,N.z=q.z;var k=r.Cartesian3.midpoint(A,N,z),W=0;for(s=0;s<R;s+=3){i.x=a[s]+n[s],i.y=a[s+1]+n[s+1],i.z=a[s+2]+n[s+2];var G=r.Cartesian3.magnitude(r.Cartesian3.subtract(i,k,v));G>W&&(W=G);var U=r.Cartesian3.magnitudeSquared(r.Cartesian3.subtract(i,T,v));if(U>Z){var H=Math.sqrt(U);_=.5*(_+H),Z=_*_;var L=H-_;T.x=(_*T.x+L*i.x)/H,T.y=(_*T.y+L*i.y)/H,T.z=(_*T.z+L*i.z)/H}}return _<W?(r.Cartesian3.clone(T,t.center),t.radius=_):(r.Cartesian3.clone(k,t.center),t.radius=W),t},d.fromCornerPoints=function(a,n,t){e.defined(t)||(t=new d);var i=r.Cartesian3.midpoint(a,n,t.center);return t.radius=r.Cartesian3.distance(i,n),t},d.fromEllipsoid=function(a,n){return e.defined(n)||(n=new d),r.Cartesian3.clone(r.Cartesian3.ZERO,n.center),n.radius=a.maximumRadius,n};var M=new r.Cartesian3;d.fromBoundingSpheres=function(a,n){if(e.defined(n)||(n=new d),!e.defined(a)||0===a.length)return n.center=r.Cartesian3.clone(r.Cartesian3.ZERO,n.center),n.radius=0,n;var t=a.length;if(1===t)return d.clone(a[0],n);if(2===t)return d.union(a[0],a[1],n);var i,s=[];for(i=0;i<t;i++)s.push(a[i].center);n=d.fromPoints(s,n);var u=n.center,c=n.radius;for(i=0;i<t;i++){var o=a[i];c=Math.max(c,r.Cartesian3.distance(u,o.center,M)+o.radius)}return n.radius=c,n};var P=new r.Cartesian3,V=new r.Cartesian3,I=new r.Cartesian3;d.fromOrientedBoundingBox=function(a,n){e.defined(n)||(n=new d);var t=a.halfAxes,i=s.Matrix3.getColumn(t,0,P),u=s.Matrix3.getColumn(t,1,V),c=s.Matrix3.getColumn(t,2,I);return r.Cartesian3.add(i,u,i),r.Cartesian3.add(i,c,i),n.center=r.Cartesian3.clone(a.center,n.center),n.radius=r.Cartesian3.magnitude(i),n},d.clone=function(a,n){if(e.defined(a))return e.defined(n)?(n.center=r.Cartesian3.clone(a.center,n.center),n.radius=a.radius,n):new d(a.center,a.radius)},d.packedLength=4,d.pack=function(a,n,r){r=e.defaultValue(r,0);var t=a.center;return n[r++]=t.x,n[r++]=t.y,n[r++]=t.z,n[r]=a.radius,n},d.unpack=function(a,n,r){n=e.defaultValue(n,0),e.defined(r)||(r=new d);var t=r.center;return t.x=a[n++],t.y=a[n++],t.z=a[n++],r.radius=a[n],r};var j=new r.Cartesian3,B=new r.Cartesian3;d.union=function(a,n,t){e.defined(t)||(t=new d);var i=a.center,s=a.radius,u=n.center,c=n.radius,o=r.Cartesian3.subtract(u,i,j),C=r.Cartesian3.magnitude(o);if(s>=C+c)return a.clone(t),t;if(c>=C+s)return n.clone(t),t;var l=.5*(s+C+c),f=r.Cartesian3.multiplyByScalar(o,(-s+l)/C,B);return r.Cartesian3.add(f,i,f),r.Cartesian3.clone(f,t.center),t.radius=l,t};var D=new r.Cartesian3;d.expandDistance=function(a,e,n,t){r.Cartesian3.multiplyByScalar(n,e,D);let i=t.center;return r.Cartesian3.add(i,D,i),t.radius=Math.sqrt(a.radius*a.radius+e*e),t},d.expand=function(a,e,n){n=d.clone(a,n);var t=r.Cartesian3.magnitude(r.Cartesian3.subtract(e,n.center,D));return t>n.radius&&(n.radius=t),n},d.intersectPlane=function(a,e){var n=a.center,t=a.radius,s=e.normal,u=r.Cartesian3.dot(s,n)+e.distance;return u<-t?i.Intersect.OUTSIDE:u<t?i.Intersect.INTERSECTING:i.Intersect.INSIDE},d.transform=function(a,n,r){return e.defined(r)||(r=new d),r.center=s.Matrix4.multiplyByPoint(n,a.center,r.center),r.radius=s.Matrix4.getMaximumScale(n)*a.radius,r};var T=new r.Cartesian3;d.distanceSquaredTo=function(a,e){var n=r.Cartesian3.subtract(a.center,e,T);return r.Cartesian3.magnitudeSquared(n)-a.radius*a.radius},d.transformWithoutScale=function(a,n,r){return e.defined(r)||(r=new d),r.center=s.Matrix4.multiplyByPoint(n,a.center,r.center),r.radius=a.radius,r};var Z=new r.Cartesian3;d.computePlaneDistances=function(a,n,t,i){e.defined(i)||(i=new c);var s=r.Cartesian3.subtract(a.center,n,Z),u=r.Cartesian3.dot(t,s);return i.start=u-a.radius,i.stop=u+a.radius,i};for(var _=new r.Cartesian3,A=new r.Cartesian3,N=new r.Cartesian3,k=new r.Cartesian3,W=new r.Cartesian3,G=new t.Cartographic,U=new Array(8),H=0;H<8;++H)U[H]=new r.Cartesian3;var L=new u;d.projectTo2D=function(a,n,t){n=e.defaultValue(n,L);var i,s=n.ellipsoid,u=a.center,c=a.radius;i=r.Cartesian3.equals(u,r.Cartesian3.ZERO)?r.Cartesian3.clone(r.Cartesian3.UNIT_X,_):s.geodeticSurfaceNormal(u,_);var o=r.Cartesian3.cross(r.Cartesian3.UNIT_Z,i,A);r.Cartesian3.normalize(o,o);var C=r.Cartesian3.cross(i,o,N);r.Cartesian3.normalize(C,C),r.Cartesian3.multiplyByScalar(i,c,i),r.Cartesian3.multiplyByScalar(C,c,C),r.Cartesian3.multiplyByScalar(o,c,o);var l=r.Cartesian3.negate(C,W),f=r.Cartesian3.negate(o,k),y=U,h=y[0];r.Cartesian3.add(i,C,h),r.Cartesian3.add(h,o,h),h=y[1],r.Cartesian3.add(i,C,h),r.Cartesian3.add(h,f,h),h=y[2],r.Cartesian3.add(i,l,h),r.Cartesian3.add(h,f,h),h=y[3],r.Cartesian3.add(i,l,h),r.Cartesian3.add(h,o,h),r.Cartesian3.negate(i,i),h=y[4],r.Cartesian3.add(i,C,h),r.Cartesian3.add(h,o,h),h=y[5],r.Cartesian3.add(i,C,h),r.Cartesian3.add(h,f,h),h=y[6],r.Cartesian3.add(i,l,h),r.Cartesian3.add(h,f,h),h=y[7],r.Cartesian3.add(i,l,h),r.Cartesian3.add(h,o,h);for(var m=y.length,v=0;v<m;++v){var x=y[v];r.Cartesian3.add(u,x,x);var p=s.cartesianToCartographic(x,G);n.project(p,x)}t=d.fromPoints(y,t),u=t.center;var g=u.x,z=u.y,w=u.z;return u.x=w,u.y=g,u.z=z,t},d.isOccluded=function(a,e){return!e.isBoundingSphereVisible(a)},d.equals=function(a,n){return a===n||e.defined(a)&&e.defined(n)&&r.Cartesian3.equals(a.center,n.center)&&a.radius===n.radius},d.prototype.intersectPlane=function(a){return d.intersectPlane(this,a)},d.prototype.distanceSquaredTo=function(a){return d.distanceSquaredTo(this,a)},d.prototype.computePlaneDistances=function(a,e,n){return d.computePlaneDistances(this,a,e,n)},d.prototype.isOccluded=function(a){return d.isOccluded(this,a)},d.prototype.equals=function(a){return d.equals(this,a)},d.prototype.clone=function(a){return d.clone(this,a)},d.prototype.volume=function(){var a=this.radius;return w*a*a*a},d.prototype.getCornerAry=function(){var a=[],e=new r.Cartesian3,n=new r.Cartesian3(this.radius,0,0),t=new r.Cartesian3(0,this.radius,0),i=new r.Cartesian3(0,0,this.radius),s=r.Cartesian3.add(n,t,e);return r.Cartesian3.add(s,i,s),r.Cartesian3.add(s,this.center,s),a.push(s.clone()),r.Cartesian3.add(this.center,n,s),r.Cartesian3.add(s,t,s),r.Cartesian3.subtract(s,i,s),a.push(s.clone()),r.Cartesian3.add(this.center,n,s),r.Cartesian3.subtract(s,t,s),r.Cartesian3.add(s,i,s),a.push(s.clone()),r.Cartesian3.add(this.center,n,s),r.Cartesian3.subtract(s,t,s),r.Cartesian3.subtract(s,i,s),a.push(s.clone()),r.Cartesian3.subtract(this.center,n,s),r.Cartesian3.add(s,t,s),r.Cartesian3.add(s,i,s),a.push(s.clone()),r.Cartesian3.subtract(this.center,n,s),r.Cartesian3.add(s,t,s),r.Cartesian3.subtract(s,i,s),a.push(s.clone()),r.Cartesian3.subtract(this.center,n,s),r.Cartesian3.subtract(s,t,s),r.Cartesian3.add(s,i,s),a.push(s.clone()),r.Cartesian3.subtract(this.center,n,s),r.Cartesian3.subtract(s,t,s),r.Cartesian3.subtract(s,i,s),a.push(s.clone()),a},a.BoundingSphere=d,a.GeographicProjection=u,a.Interval=c}));