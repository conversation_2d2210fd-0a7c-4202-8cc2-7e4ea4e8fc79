# cesiumController.js 使用说明

> 基于 SUIT-DATAV 中的 suit-cesium 进行的封装

## 1. 初始化引擎

> 只是使用 cesiumController.js 中的方法可直接跳到下方 2. cesiumController.js 常用方法介绍

### 示例

```js
<template>
    <div style="width: 100%;height:600px;">
        <suit-cesium
        ref="childRef"
        @getViewer="getViewer"
        style="width: 100%; height: 100%"
        :suit-base-settings="false"
        :suit-measure="false"
        :suit-clippings="false"></suit-cesium>
    </div>
</template>
<script setup>
  import { ref } from 'vue'
  const childRef = ref(null)

  const getViewer = (viewer) => {
    // 基础配置
    // proxy.$refs.childRef.enableCameraUnderground(false) // 相机是否允许进入地下 // 新版proxy可以用ref替代
    childRef.value.enableCameraUnderground(false) // 相机是否允许进入地下

    viewer.scene.msaaSamples = 4 // mass 抗锯齿
    viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK) // 禁用双击事件
  }
</script>
```

### 初始化 cesiumController.js

```js
// 引入cesiumController.js
import CesiumController from '@Common/utils/cesiumUtils/cesiumController.js'

// 在getViewer方法中执行
new CesiumController({
  ref: proxy.$refs.childRef,
  viewer
  // _cesium: '__CesiumEventEnd' // 可自定义全局名称，默认是__Cesium。此方法在需要加载多个引擎时使用
})

注: 加载多个引擎时，需要在<suit-cesium>< /suit-cesium>标签上添加domId="自定义名称，不能相同"
```

## 2. cesiumController.js 方法介绍（只介绍常用方法，后续有时间再补充）

> 1. 设置视口（切换视角的功能）
> 2. 根据经纬度拉近视角
> 3. 渲染和清除标签（撒点）
> 4. 渲染和清除线段（画线）
> 5. 渲染和清除面层
> 6. 全部清除

### 1. 设置视口（切换视角的功能）

> 详细介绍

```js
/**
 * @description: 设置视口
 * @param {Object} camera // 视口对象
 * @param {Number} duration // 动画时长 默认值是3 注：单位是秒
 */

__Cesium.setCamera(camera, duration)

注: 如何获取camera对象
// 回车打印视口数据 或者使用__Cesium.onContextMenu()去注册右键功能菜单
window.addEventListener('keydown', (e) => {
  if (e.keyCode == 13) {
    proxy.$refs.childRef.getViewpoint()
  }
})
```

> 示例

```js
// 视口数据
const camera = {
  destination: { x: -2876741.89242937, y: 4829624.043158877, z: 3301567.387247216 },
  orientation: { heading: 6.283185307137857, pitch: -1.5306353350669806, roll: 0 }
}

__Cesium.setCamera(camera, 5)
```

### 2. 根据经纬度拉近视角

> 详细介绍

```js
/**
 * @description: 根据经纬度拉近视角
 * @param {Object} position // [lng, lat]
 * @param {Number} height // 视角高度 默认值是100000
 * @param {Number} duration // 动画时长 默认值是2 注：单位是秒
 */

__Cesium.setCameraByLngAndLat(position, height, duration)
```

> 示例

```js
const position = [116.3975, 39.9087]
__Cesium.setCameraByLngAndLat(position, 50000, 3)
```

### 3. 渲染和清除标签（撒点）

> 详细介绍

```js
/**
 * @description: 渲染和清除标签
 * @param {*} data 配置数据 注释部分非必传，position和image必传
   [{
        position: { longitude: item.Lon_bd, latitude: item.Lat_bd }, // height不传默认为0
        image: item.image,
        // scale: 0.5, // 标签缩放 默认为1
        // pixelOffset: {x: 0, y: 40}, // 标签在屏幕空间中的偏移，x指向右侧，y指向下方
        // scaleByDistance: {near: 0, nearScale: 0, far: 3000, farScale: 1 } // 标签大小根据同相机的距离进行缩放,在near和far之间进行插值
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(1000, 100000), // 控制在距离范围内可见
        // clickFunction: () => {}, // 点击事件
        // isAllowAllDeletion: true // 是否允许全部删除 默认为true,允许被removeAllExamples方法全部清除
    }]
 * @param {*} isShow 渲染和清除 默认为true渲染
 */

const list = []

list = __Cesium.setBillboard(data) // 渲染

__Cesium.setBillboard(list, false) // 清除 清除需要用渲染成功的返回值
```

> 示例

```js
import baseIcon from '@/assets/MapIcon/base.png' //引入图片

const list = []
const data = [
  {
    position: { longitude: 116.3975, latitude: 39.9087 }, // height不传默认为0
    image: baseIcon
  },
  ...
]
// 批量撒点
list = __Cesium.setBillboard(data) // 批量渲染标签

// 批量删除
if (list.length) {
  __Cesium.setBillboard(list, false) // 批量清除标签 清除时需要用渲染成功的返回值
}
```

### 4. 渲染和清除线段（画线）

> 详细介绍

```js
/**
 * @description: 渲染和清除线段
 * @param {*} data
 * {
    positions: [longitude, latitude，longitude, latitude, ...] // ! 创建多边形需要改为 positions: Cesium.Cartesian3.fromDegreesArray(positions)
    // width: 3, // 宽度
    // color: '#15dc23', // 颜色
    // clickFunction: () => {}, // 点击事件
    // isAllowAllDeletion: true // 是否允许全部删除 默认为true,允许被removeAllExamples方法全部清除
 * }
 * @param {*} isShow
 */
const list = []

list = __Cesium.setPolyline(data) // 渲染

__Cesium.setPolyline(list, false) // 清除 清除需要用渲染成功的返回值
```

> 示例

```js

const list = []
const data = [
  {
    positions: [116.3975, 39.9087, 116.253, 39.456],
    width: 5,
    color: '#47B8FF'
  },
  ...
]
// 批量画线
list = __Cesium.setPolyline(data) // 批量渲染线段

// 批量删除
if (list.length) {
  __Cesium.setPolyline(list, false) // 批量清除线段时需要用渲染成功的返回值
}
```

### 5. 渲染和清除面层

> 详细介绍

```js
/**
 * @description: 渲染和清除面层
 * @param {*} data
 * {
    positions: [longitude, latitude，longitude, latitude, ...] // 首尾经纬度需要相同
    color: '#15dc23', // 颜色
    alpha: 1,
    // isAllowAllDeletion: true // 是否允许全部删除 默认为true,允许被removeAllExamples方法全部清除
 * }
 * @param {*} isShow
 */
const list = []

list = __Cesium.setPolygon(data) // 渲染

__Cesium.setPolygon(list, false) // 清除 清除需要用渲染成功的返回值
```

> 示例

```js
const list = []
const data = [
  {
    position:  [
            [103.959168405691287, 30.597253632161159],
            [103.9558681, 30.5800074],
            [103.9582796, 30.5877413],
            [103.959168405691287, 30.597253632161159]
          ],
    color: '#47B8FF',
    alpha: 1,
    // isAllowAllDeletion: false
  },
  ...
]
// 批量渲染面层
list = __Cesium.setPolygon(data) // 批量渲染面层

// 批量删除
if (list.length) {
  __Cesium.setPolygon(list, false) // 批量清除面层时需要用渲染成功的返回值
}
```

### 6. 全部清除

> 详细介绍

```js
清除地图上的所有覆盖物

注: 标签，线段，面层：可通过将isAllowAllDeletion字段设为false，避免被removeAllExamples方法清除
```

> 示例

```js
__Cesium.removeAllExamples()
```
