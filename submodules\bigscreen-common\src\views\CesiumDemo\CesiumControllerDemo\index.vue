<template>
  <div>
    <span>流线控制器</span>
    <el-button @click="showDialog = true">设置流动线样式</el-button>
    <dialog class="dialog-overlay" v-if="showDialog">
      <div class="dialog-content">
        <label>宽度：</label>
        <input type="number" v-model="width" />
        <br />
        <label>速度：</label>
        <input type="number" v-model="speed" />
        <br />
        <label>图片：</label>
        <input type="text" v-model="image" />
        <br />
        <el-button @click="updateFlowLines">确定</el-button>
        <el-button class="custom-button" @click="showDialog = false">取消</el-button>
      </div>
    </dialog>
    <div id="screen-cesium">
      <suit-cesium
        ref="childRef"
        @getViewer="getViewer"
        style="width: 100%; height: 100%"
        :suit-base-settings="false"
        :suit-measure="false"
        :suit-clippings="false"></suit-cesium>
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref } from 'vue'

import CesiumController from '@Common/utils/cesiumUtils/cesiumController.js'
import { wayLines } from '@Common/utils/mapData.js'
  const childRef = ref(null)
  const { proxy } = getCurrentInstance()
  let highwayFlowLineList = null
  const defaultView16 = {
    camera1: {
      // 应急管理和综合概览默认视角
      destination: { x: -2876741.89242937, y: 4829624.043158877, z: 3301567.387247216 },
      orientation: { heading: 6.283185307137857, pitch: -1.5306353350669806, roll: 0 }
    },
    camera2: {
      // 三维展示默认视角
      destination: { x: -2859132.7683700775, y: 4803727.607560208, z: 3166342.898140181 },
      orientation: { heading: 0.010212416596449003, pitch: -0.5205461163164751, roll: 6.28136407067231 }
    }
  }

  const showDialog = ref(false)
  const width = ref(15)
  const speed = ref(3)
  const image = ref(1)
  // 获取viewer对象
  function getViewer(viewer) {
    // 基础配置
    // viewer.scene.debugShowFramesPerSecond = true // 显示每秒帧数和帧之间的时间
    proxy.$refs.childRef.enableCameraUnderground(true) // 相机是否允许进入地下
    viewer.scene.msaaSamples = 4 // mass 抗锯齿

    new CesiumController({
      ref: proxy.$refs.childRef,
      viewer
    })
    __Cesium.setCamera(defaultView16.camera1)
  }

  function updateFlowLines() {
    if (highwayFlowLineList) {
      __Cesium.setFlowPolylineNew(highwayFlowLineList, false)
      highwayFlowLineList = null
    }
    const flowPolylines = []
    console.log(wayLines, 'wayLines')
    wayLines.forEach((item) => {
      const data = {
        positions: item.position,
        width: width.value,
        speed: speed.value,
        image: `images/style${image.value}.png`
      }
      flowPolylines.push(data)
    })
    console.log(flowPolylines, ' flowPolylines')
    if (flowPolylines.length) {
      highwayFlowLineList = __Cesium.setFlowPolylineNew(flowPolylines)
    }
  //   showDialog.value = false
  }
</script>
<style lang="scss" scoped>
  .dialog-overlay {
    position: fixed;
    top: 200px;
    left: 200px;
    width: 500px;
    height: 300px;
    background-color: rgba(1, 85, 155, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid red; /* 添加边框样式 */
    z-index: 3; /* 将弹窗放在图层的最上方 */
  }

  .dialog-content {
    padding: 0px;
  }
</style>
