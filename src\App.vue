<!--
 * @Author: wangjialing <EMAIL>
 * @Date: 2024-01-18 09:44:14
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-30 18:59:28
 * @FilePath: \bigscreen-qj-web\src\App.vue
 * @Description:
 *
-->
<template>
  <router-view />
</template>
<script>
  export default {
    name: 'App'
  }
</script>
<script setup>
  import { getAssetsFile } from '@/utils'
  import lib from '@/utils/lib.ts'
  // import useStore from '@/store'
  // const { storeScreenData } = useStore()

  // 获取基础数据
  // storeScreenData.getModeCodeMileageRelation()
  // storeScreenData.getDeviceTreeType()
  // import { onMounted } from 'vue'

  // import autofit from 'autofit.js'
  // onMounted(() => {
  //   // 自适应
  //   autofit.init({
  //     designHeight: 1080,
  //     designWidth: 1920,
  //     renderDom: '#app',
  //     resize: true,
  //     ignore: [
  //       {
  //         el: '.ignoreAutoFit'
  //       }
  //     ]
  //   })
  // })


</script>
<style lang="scss">
  * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    list-style: none;

    //user-select: none;
  }
</style>
