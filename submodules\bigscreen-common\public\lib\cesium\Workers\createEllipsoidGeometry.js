/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./EllipsoidGeometry-539f6ba4","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Math-b5f4d889","./Rectangle-9bffefe4","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./VertexFormat-86c096b8"],(function(e,t,r,n,o,a,b,d,i,f,s,m,c,u,y,G){"use strict";return function(r,n){return e.defined(n)&&(r=t.EllipsoidGeometry.unpack(r,n)),t.EllipsoidGeometry.createGeometry(r)}}));
