<template>
  <div class="carTypeChart">
    <div class="title">
      <span>{{ itemData.name }}</span>
      <span>{{ itemData.value }}%</span>
    </div>
    <div class="content">
      <Mychart :option="options" width="300px" height="79px" style="margin-top: 20px"></Mychart>
      <!-- 数字滚动组件，覆盖在ECharts label位置上 -->
      <div class="number-scroll-overlay" :style="numberPosition">
        <span class="custom-number-display">
          <NumberScroll 
            ref="numberScrollRef"
            :start="previousNumber" 
            :end="currentNumber" 
            :duration="1200"
            :autoPlay="true"
            :decimals="0"
            :useEasing="true"
            :refreshTrigger="refreshTrigger"
            :dataSource="dataSource"
          />
        </span>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed, watch } from 'vue'
  import Mychart from '@Common/components/MyChart/index.vue'
  import NumberScroll from '@/components/NumberScroll/index.vue'
  
  const props = defineProps({
    itemData: {
      type: Object,
      default: () => ({})
    },
    carTotalNumber: {
      type: Number,
      default: 0
    }
  })

  // 数字滚动相关状态
  const previousNumber = ref(0)
  const currentNumber = ref(0)
  const numberScrollRef = ref(null)
  const refreshTrigger = ref(0)
  const dataSource = ref('mqtt') // 标识数据源：'mqtt' | 'api' | 'init'

  // 计算数字位置，基于柱状图长度
  const numberPosition = computed(() => {
    const value = parseFloat(props.itemData.value) || 0
    // 柱状图宽度基于value百分比，加上左边距和一些间距
    const leftPosition = 8 + (value * 2.7) + 15 // 8px左边距 + 柱状图长度(按比例) + 15px间距
    return {
      left: `${leftPosition}px`,
      top: '50%', // 图表容器的50%位置
      transform: 'translateY(-50%)', // 向上偏移自身高度的50%，实现完美垂直居中
    }
  })

  // 监听数据变化，触发数字滚动动画
  watch(() => props.itemData.data, (newVal, oldVal) => {
    if (newVal !== undefined && newVal !== null) {
      // 保存前一个数字作为动画起点
      if (oldVal !== undefined && oldVal !== null) {
        previousNumber.value = parseInt(oldVal)
      } else {
        previousNumber.value = 0
      }
      // 设置新的目标数字
      currentNumber.value = parseInt(newVal)
    }
  }, { immediate: true })

  // 监听itemData变化（用于处理整体数据切换的情况）
  watch(() => props.itemData, (newVal, oldVal) => {
    if (newVal && newVal.data !== undefined && newVal.data !== null) {
      // 数据切换时，触发动画刷新
      dataSource.value = `switch-${Date.now()}`
    }
  }, { immediate: false, deep: true })

  // 初始化数字
  if (props.itemData.data) {
    currentNumber.value = parseInt(props.itemData.data)
  }

  const options = computed(() => {
    // 计算 symbolOffset 的逻辑，根据 value 动态计算偏移量
    // 假设 value 与 offset 成线性关系，通过已知的两组值 (51.75, -126) 和 (13.41, -25) 计算斜率和截距
    // 斜率 m = (y2 - y1) / (x2 - x1)
    // 截距 b = y1 - m * x1
    const m = (-126 - -25) / (51.75 - 13.41)
    const b = -25 - m * 13.41
    const value = parseFloat(props.itemData.value)
    return {
      grid: {
        top: '-52%',
        left: '8',
        right: '10%',
        bottom: 0
      },
      xAxis: [
        {
          type: 'value',
          show: false,
          gridIndex: 0,
          max: 100
        }
      ],
      yAxis: {
        type: 'category',
        show: false,
        axisLine: { show: false },
        axisTick: { show: false },
        splitArea: { show: false },
        splitLine: { show: false },
        axisLabel: {
          fontFamily: 'Alibaba PuHuiTi',
          fontSize: 20,
          color: '#ffffff'
        },
        inverse: true
      },
      series: [
        {
          zlevel: 1, // 确保 pictorialBar 在柱状图之上
          type: 'pictorialBar',
          symbolPosition: 'end',
          // 关联对应的柱状图系列索引
          barGap: '-100%',
          data: [props.itemData.value == '0.0' ? null : props.itemData.value],
          symbol: 'diamond', // 菱形
          symbolRotate: 90,
          symbolOffset: [Math.round(m * value + b), 0],
          symbolSize: [20, 10],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return props.itemData.color
              }
            }
          }
        },
        {
          data: [props.itemData.value],
          type: 'bar',
          barWidth: 20,
          zlevel: 1,
          // 恢复ECharts原始label显示，正常颜色
          label: {
            show: true,
            position: 'right',
            fontSize: 14,
            fontFamily: 'PangMenZhengDao',
            color: 'transparent', // 设置为透明
            fontWeight: 'bold',
            formatter: (params) => {
              // return parseInt((props.carTotalNumber * props.itemData.value) / 100)
              return parseInt(props.itemData.data)
            }
          },
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  // 修改渐变方向为从上到下
                  x: 0,
                  x2: 0,
                  y: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      // 根据基础颜色生成较亮的顶部颜色
                      color: lightenColor(props.itemData.color, 0.3)
                    },
                    {
                      offset: 0.5,
                      // 根据基础颜色生成稍亮的中间颜色
                      color: lightenColor(props.itemData.color, 0.1)
                    },
                    {
                      offset: 0.5,
                      // 根据基础颜色生成稍暗的中间颜色
                      color: darkenColor(props.itemData.color, 0.1)
                    },
                    {
                      offset: 1,
                      // 根据基础颜色生成较暗的底部颜色
                      color: darkenColor(props.itemData.color, 0.3)
                    }
                  ]
                }
              }
            }
          }
        },
        {
          zlevel: 2, // 确保 pictorialBar 在柱状图之上
          type: 'pictorialBar',
          symbolPosition: 'end',
          // 关联对应的柱状图系列索引
          barGap: '-100%',
          data: [props.itemData.value == '0.0' ? null : props.itemData.value],
          symbol: 'diamond', // 菱形
          symbolRotate: 90,
          symbolOffset: [10, 0],
          symbolSize: [20, 10],
          itemStyle: {
            normal: {
              borderWidth: 0,
              color: function (params) {
                return props.itemData.color
              }
            }
          }
        }
      ]
    }
  })
  // 辅助函数：变亮颜色
  function lightenColor(hex, percent) {
    hex = hex.replace(/^#/, '')
    let r = parseInt(hex.substr(0, 2), 16)
    let g = parseInt(hex.substr(2, 2), 16)
    let b = parseInt(hex.substr(4, 2), 16)

    r = Math.min(255, Math.round(r * (1 + percent)))
    g = Math.min(255, Math.round(g * (1 + percent)))
    b = Math.min(255, Math.round(b * (1 + percent)))

    return `#${[r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('')}`
  }

  // 辅助函数：变暗颜色
  function darkenColor(hex, percent) {
    hex = hex.replace(/^#/, '')
    let r = parseInt(hex.substr(0, 2), 16)
    let g = parseInt(hex.substr(2, 2), 16)
    let b = parseInt(hex.substr(4, 2), 16)

    r = Math.max(0, Math.round(r * (1 - percent)))
    g = Math.max(0, Math.round(g * (1 - percent)))
    b = Math.max(0, Math.round(b * (1 - percent)))

    return `#${[r, g, b].map((x) => x.toString(16).padStart(2, '0')).join('')}`
  }
</script>
<style lang="scss" scoped>
  .carTypeChart {
    position: relative;
    width: 300px;
    height: 50px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .content {
      position: absolute;
      top: -2px;
      width: 300px;
      height: 79px;
      background: url('@/assets/ScreenRight/Perception/Traffic/chartBg.png') no-repeat center;
      background-size: cover;
      
      .number-scroll-overlay {
        position: absolute;
        z-index: 10;
        pointer-events: none;
        display: flex;
        align-items: center; // 垂直居中对齐
        justify-content: flex-start; // 水平方向左对齐，因为left已经定位了
        height: 20px; // 设置固定高度便于居中计算
        
        .custom-number-display {
          font-size: 14px;
          color: #AEF5FF;
          font-family: 'PangMenZhengDao';
          font-weight: bold;
          line-height: 14px; // 与字体大小一致，确保垂直对齐
          
          :deep(.count-to) {
            font-size: 14px !important;
            color: #AEF5FF !important;
            font-family: 'PangMenZhengDao' !important;
            font-weight: bold !important;
            line-height: 14px !important; // 与字体大小一致
            vertical-align: baseline; // 基线对齐
          }
        }
      }
    }
  }
</style>
