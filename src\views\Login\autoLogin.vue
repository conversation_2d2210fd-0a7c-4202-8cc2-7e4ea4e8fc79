<!--
 * @Description: 自动登录中转页面
 * @Autor: qianlong
 * @Date: 2025-04-22 14:14:35
 * @LastEditors: qianlong
 * @LastEditTime: 2025-04-22 18:17:32
-->
<template>
  <div></div>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  import { setToken } from '@/utils/auth'

  const router = useRouter()
  // 获取当前页面地址携带的参数
  const hashParams = new URLSearchParams(window.location.hash.split('?')[1])

  const token = hashParams.get('token')
  if (token) {
    // 存储token
    setToken(token)
    // 跳转到首页
    router.push({ path: '/' })
  }
</script>

<style scoped></style>
