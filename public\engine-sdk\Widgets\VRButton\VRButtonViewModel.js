import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import EventHelper from"../../Core/EventHelper.js";import Fullscreen from"../../Core/Fullscreen.js";import OrthographicFrustum from"../../Core/OrthographicFrustum.js";import knockout from"../../ThirdParty/knockout.js";import NoSleep from"../../ThirdParty/NoSleep.js";import createCommand from"../createCommand.js";import getElement from"../getElement.js";function lockScreen(e){var t=!1,o=window.screen;return defined(o)&&(defined(o.lockOrientation)?t=o.lockOrientation(e):defined(o.mozLockOrientation)?t=o.mozLockOrientation(e):defined(o.msLockOrientation)?t=o.msLockOrientation(e):defined(o.orientation&&o.orientation.lock)&&(t=o.orientation.lock(e))),t}function unlockScreen(){var e=window.screen;defined(e)&&(defined(e.unlockOrientation)?e.unlockOrientation():defined(e.mozUnlockOrientation)?e.mozUnlockOrientation():defined(e.msUnlockOrientation)?e.msUnlockOrientation():defined(e.orientation&&e.orientation.unlock)&&e.orientation.unlock())}function toggleVR(e,t,o,n){n()||(o()?(t.useWebVR=!1,e._locked&&(unlockScreen(),e._locked=!1),e._noSleep.disable(),Fullscreen.exitFullscreen(),o(!1)):(Fullscreen.fullscreen||Fullscreen.requestFullscreen(e._vrElement),e._noSleep.enable(),e._locked||(e._locked=lockScreen("landscape")),t.useWebVR=!0,o(!0)))}function VRButtonViewModel(e,t){if(!defined(e))throw new DeveloperError("scene is required.");var o=this,n=knockout.observable(Fullscreen.enabled),r=knockout.observable(!1);this.isVRMode=void 0,knockout.defineProperty(this,"isVRMode",{get:function(){return r()}}),this.isVREnabled=void 0,knockout.defineProperty(this,"isVREnabled",{get:function(){return n()},set:function(e){n(e&&Fullscreen.enabled)}}),this.tooltip=void 0,knockout.defineProperty(this,"tooltip",(function(){return n()?r()?"Exit VR mode":"Enter VR mode":"VR mode is unavailable"}));var i=knockout.observable(!1);this._isOrthographic=void 0,knockout.defineProperty(this,"_isOrthographic",{get:function(){return i()}}),this._eventHelper=new EventHelper,this._eventHelper.add(e.preRender,(function(){i(e.camera.frustum instanceof OrthographicFrustum)})),this._locked=!1,this._noSleep=new NoSleep,this._command=createCommand((function(){toggleVR(o,e,r,i)}),knockout.getObservable(this,"isVREnabled")),this._vrElement=defaultValue(getElement(t),document.body),this._callback=function(){!Fullscreen.fullscreen&&r()&&(e.useWebVR=!1,o._locked&&(unlockScreen(),o._locked=!1),o._noSleep.disable(),r(!1))},document.addEventListener(Fullscreen.changeEventName,this._callback)}Object.defineProperties(VRButtonViewModel.prototype,{vrElement:{get:function(){return this._vrElement},set:function(e){if(!(e instanceof Element))throw new DeveloperError("value must be a valid Element.");this._vrElement=e}},command:{get:function(){return this._command}}}),VRButtonViewModel.prototype.isDestroyed=function(){return!1},VRButtonViewModel.prototype.destroy=function(){this._eventHelper.removeAll(),document.removeEventListener(Fullscreen.changeEventName,this._callback),destroyObject(this)};export default VRButtonViewModel;