define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./GeometryPipeline-f727231c","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./EllipseGeometryLibrary-98898859","./GeometryInstance-d4317835"],(function(e,t,r,a,i,n,o,s,u,l,m,p,c,y,d,f,h,A){"use strict";var x=new a.Cartesian3,g=new a.Cartesian3,b=new a.Cartesian3,v=new a.Cartesian3,_=new a.Cartesian2,C=new s.Matrix3,w=new s.Matrix3,M=new o.Quaternion,E=new a.Cartesian3,I=new a.Cartesian3,T=new a.Cartesian3,G=new i.Cartographic,N=new a.Cartesian3,P=new a.Cartesian2,F=new a.Cartesian2;function D(e,r,i){var m=r.vertexFormat,c=r.center,y=r.semiMajorAxis,f=r.semiMinorAxis,A=r.ellipsoid,v=r.stRotation,D=i?e.length/3*2:e.length/3,V=r.shadowVolume,O=m.st?new Float32Array(2*D):void 0,S=m.normal?new Float32Array(3*D):void 0,L=m.tangent?new Float32Array(3*D):void 0,R=m.bitangent?new Float32Array(3*D):void 0,j=V?new Float32Array(3*D):void 0,k=0,z=E,B=I,Y=T,H=new n.GeographicProjection(A),U=H.project(A.cartesianToCartographic(c,G),N),Q=A.scaleToGeodeticSurface(c,x);A.geodeticSurfaceNormal(Q,Q);var W=C,q=w;if(0!==v){var J=o.Quaternion.fromAxisAngle(Q,v,M);W=s.Matrix3.fromQuaternion(J,W),J=o.Quaternion.fromAxisAngle(Q,-v,M),q=s.Matrix3.fromQuaternion(J,q)}else W=s.Matrix3.clone(s.Matrix3.IDENTITY,W),q=s.Matrix3.clone(s.Matrix3.IDENTITY,q);for(var Z=a.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,P),K=a.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,F),X=e.length,$=i?X:0,ee=$/3*2,te=0;te<X;te+=3){var re=te+1,ae=te+2,ie=a.Cartesian3.fromArray(e,te,x);if(m.st){var ne=s.Matrix3.multiplyByVector(W,ie,g),oe=H.project(A.cartesianToCartographic(ne,G),b);a.Cartesian3.subtract(oe,U,oe),_.x=(oe.x+y)/(2*y),_.y=(oe.y+f)/(2*f),Z.x=Math.min(_.x,Z.x),Z.y=Math.min(_.y,Z.y),K.x=Math.max(_.x,K.x),K.y=Math.max(_.y,K.y),i&&(O[k+ee]=_.x,O[k+1+ee]=_.y),O[k++]=_.x,O[k++]=_.y}(m.normal||m.tangent||m.bitangent||V)&&(z=A.geodeticSurfaceNormal(ie,z),V&&(j[te+$]=-z.x,j[re+$]=-z.y,j[ae+$]=-z.z),(m.normal||m.tangent||m.bitangent)&&((m.tangent||m.bitangent)&&(B=a.Cartesian3.normalize(a.Cartesian3.cross(a.Cartesian3.UNIT_Z,z,B),B),s.Matrix3.multiplyByVector(q,B,B)),m.normal&&(S[te]=z.x,S[re]=z.y,S[ae]=z.z,i&&(S[te+$]=-z.x,S[re+$]=-z.y,S[ae+$]=-z.z)),m.tangent&&(L[te]=B.x,L[re]=B.y,L[ae]=B.z,i&&(L[te+$]=-B.x,L[re+$]=-B.y,L[ae+$]=-B.z)),m.bitangent&&(Y=a.Cartesian3.normalize(a.Cartesian3.cross(z,B,Y),Y),R[te]=Y.x,R[re]=Y.y,R[ae]=Y.z,i&&(R[te+$]=Y.x,R[re+$]=Y.y,R[ae+$]=Y.z))))}if(m.st){X=O.length;for(var se=0;se<X;se+=2)O[se]=(O[se]-Z.x)/(K.x-Z.x),O[se+1]=(O[se+1]-Z.y)/(K.y-Z.y)}var ue=new p.GeometryAttributes;if(m.position){var le=h.EllipseGeometryLibrary.raisePositionsToHeight(e,r,i);ue.position=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:le})}if(m.st&&(ue.st=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:O})),m.normal&&(ue.normal=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),m.tangent&&(ue.tangent=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),m.bitangent&&(ue.bitangent=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),V&&(ue.extrudeDirection=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),i&&t.defined(r.offsetAttribute)){var me=new Uint8Array(D);if(r.offsetAttribute===d.GeometryOffsetAttribute.TOP)me=d.arrayFill(me,1,0,D/2);else{var pe=r.offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;me=d.arrayFill(me,pe)}ue.applyOffset=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:me})}return ue}function V(e){var t,r,a,i,n,o=new Array(e*(e+1)*12-6),s=0;for(t=0,a=1,i=0;i<3;i++)o[s++]=a++,o[s++]=t,o[s++]=a;for(i=2;i<e+1;++i){for(a=i*(i+1)-1,t=(i-1)*i-1,o[s++]=a++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=a++,o[s++]=t,o[s++]=a}for(r=2*e,++a,++t,i=0;i<r-1;++i)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;for(o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t++,o[s++]=t,++t,i=e-1;i>1;--i){for(o[s++]=t++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=t++,o[s++]=t++,o[s++]=a++}for(i=0;i<3;i++)o[s++]=t++,o[s++]=t,o[s++]=a;return o}var O=new a.Cartesian3;function S(e){var t=e.center;O=a.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,O),e.height,O),O=a.Cartesian3.add(t,O,O);var r=new n.BoundingSphere(O,e.semiMajorAxis),i=h.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!1),o=i.positions,s=i.numPts,u=D(o,e,!1),l=V(s);return l=y.IndexDatatype.createTypedArray(o.length/3,l),{boundingSphere:r,attributes:u,indices:l}}function L(e,r){var i=r.vertexFormat,m=r.center,c=r.semiMajorAxis,y=r.semiMinorAxis,f=r.ellipsoid,h=r.height,A=r.extrudedHeight,w=r.stRotation,D=e.length/3*2,V=new Float64Array(3*D),O=i.st?new Float32Array(2*D):void 0,S=i.normal?new Float32Array(3*D):void 0,L=i.tangent?new Float32Array(3*D):void 0,R=i.bitangent?new Float32Array(3*D):void 0,j=r.shadowVolume,k=j?new Float32Array(3*D):void 0,z=0,B=E,Y=I,H=T,U=new n.GeographicProjection(f),Q=U.project(f.cartesianToCartographic(m,G),N),W=f.scaleToGeodeticSurface(m,x);f.geodeticSurfaceNormal(W,W);for(var q=o.Quaternion.fromAxisAngle(W,w,M),J=s.Matrix3.fromQuaternion(q,C),Z=a.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,P),K=a.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,F),X=e.length,$=X/3*2,ee=0;ee<X;ee+=3){var te,re=ee+1,ae=ee+2,ie=a.Cartesian3.fromArray(e,ee,x);if(i.st){var ne=s.Matrix3.multiplyByVector(J,ie,g),oe=U.project(f.cartesianToCartographic(ne,G),b);a.Cartesian3.subtract(oe,Q,oe),_.x=(oe.x+c)/(2*c),_.y=(oe.y+y)/(2*y),Z.x=Math.min(_.x,Z.x),Z.y=Math.min(_.y,Z.y),K.x=Math.max(_.x,K.x),K.y=Math.max(_.y,K.y),O[z+$]=_.x,O[z+1+$]=_.y,O[z++]=_.x,O[z++]=_.y}ie=f.scaleToGeodeticSurface(ie,ie),te=a.Cartesian3.clone(ie,g),B=f.geodeticSurfaceNormal(ie,B),j&&(k[ee+X]=-B.x,k[re+X]=-B.y,k[ae+X]=-B.z);var se=a.Cartesian3.multiplyByScalar(B,h,v);if(ie=a.Cartesian3.add(ie,se,ie),se=a.Cartesian3.multiplyByScalar(B,A,se),te=a.Cartesian3.add(te,se,te),i.position&&(V[ee+X]=te.x,V[re+X]=te.y,V[ae+X]=te.z,V[ee]=ie.x,V[re]=ie.y,V[ae]=ie.z),i.normal||i.tangent||i.bitangent){H=a.Cartesian3.clone(B,H);var ue=a.Cartesian3.fromArray(e,(ee+3)%X,v);a.Cartesian3.subtract(ue,ie,ue);var le=a.Cartesian3.subtract(te,ie,b);B=a.Cartesian3.normalize(a.Cartesian3.cross(le,ue,B),B),i.normal&&(S[ee]=B.x,S[re]=B.y,S[ae]=B.z,S[ee+X]=B.x,S[re+X]=B.y,S[ae+X]=B.z),i.tangent&&(Y=a.Cartesian3.normalize(a.Cartesian3.cross(H,B,Y),Y),L[ee]=Y.x,L[re]=Y.y,L[ae]=Y.z,L[ee+X]=Y.x,L[ee+1+X]=Y.y,L[ee+2+X]=Y.z),i.bitangent&&(R[ee]=H.x,R[re]=H.y,R[ae]=H.z,R[ee+X]=H.x,R[re+X]=H.y,R[ae+X]=H.z)}}if(i.st){X=O.length;for(var me=0;me<X;me+=2)O[me]=(O[me]-Z.x)/(K.x-Z.x),O[me+1]=(O[me+1]-Z.y)/(K.y-Z.y)}var pe=new p.GeometryAttributes;if(i.position&&(pe.position=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:V})),i.st&&(pe.st=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:O})),i.normal&&(pe.normal=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:S})),i.tangent&&(pe.tangent=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),i.bitangent&&(pe.bitangent=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),j&&(pe.extrudeDirection=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:k})),t.defined(r.offsetAttribute)){var ce=new Uint8Array(D);if(r.offsetAttribute===d.GeometryOffsetAttribute.TOP)ce=d.arrayFill(ce,1,0,D/2);else{var ye=r.offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;ce=d.arrayFill(ce,ye)}pe.applyOffset=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:ce})}return pe}function R(e){for(var t=e.length/3,r=y.IndexDatatype.createTypedArray(t,6*t),a=0,i=0;i<t;i++){var n=i,o=i+t,s=(n+1)%t,u=s+t;r[a++]=n,r[a++]=o,r[a++]=s,r[a++]=s,r[a++]=o,r[a++]=u}return r}var j=new n.BoundingSphere,k=new n.BoundingSphere;function z(e){var t=e.center,r=e.ellipsoid,i=e.semiMajorAxis,o=a.Cartesian3.multiplyByScalar(r.geodeticSurfaceNormal(t,x),e.height,x);j.center=a.Cartesian3.add(t,o,j.center),j.radius=i,o=a.Cartesian3.multiplyByScalar(r.geodeticSurfaceNormal(t,o),e.extrudedHeight,o),k.center=a.Cartesian3.add(t,o,k.center),k.radius=i;var s=h.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!0),u=s.positions,p=s.numPts,d=s.outerPositions,f=n.BoundingSphere.union(j,k),g=D(u,e,!0),b=V(p),v=b.length;b.length=2*v;for(var _=u.length/3,C=0;C<v;C+=3)b[C+v]=b[C+2]+_,b[C+1+v]=b[C+1]+_,b[C+2+v]=b[C]+_;var w=y.IndexDatatype.createTypedArray(2*_/3,b),M=new l.Geometry({attributes:g,indices:w,primitiveType:m.PrimitiveType.TRIANGLES}),E=L(d,e);b=R(d);var I=y.IndexDatatype.createTypedArray(2*d.length/3,b),T=new l.Geometry({attributes:E,indices:I,primitiveType:m.PrimitiveType.TRIANGLES}),G=c.GeometryPipeline.combineInstances([new A.GeometryInstance({geometry:M}),new A.GeometryInstance({geometry:T})]);return{boundingSphere:f,attributes:G[0].attributes,indices:G[0].indices}}function B(e,t,r,n,o,s,u){for(var l=h.EllipseGeometryLibrary.computeEllipsePositions({center:e,semiMajorAxis:t,semiMinorAxis:r,rotation:n,granularity:o},!1,!0),m=l.outerPositions,p=m.length/3,c=new Array(p),y=0;y<p;++y)c[y]=a.Cartesian3.fromArray(m,3*y);var d=i.Rectangle.fromCartesianArray(c,s,u);return d.width>a.CesiumMath.PI&&(d.north=d.north>0?a.CesiumMath.PI_OVER_TWO-a.CesiumMath.EPSILON7:d.north,d.south=d.south<0?a.CesiumMath.EPSILON7-a.CesiumMath.PI_OVER_TWO:d.south,d.east=a.CesiumMath.PI,d.west=-a.CesiumMath.PI),d}function Y(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var n=e.center,o=t.defaultValue(e.ellipsoid,i.Ellipsoid.WGS84),s=e.semiMajorAxis,u=e.semiMinorAxis,l=t.defaultValue(e.granularity,a.CesiumMath.RADIANS_PER_DEGREE),m=t.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT);if(r.Check.defined("options.center",n),r.Check.typeOf.number("options.semiMajorAxis",s),r.Check.typeOf.number("options.semiMinorAxis",u),s<u)throw new r.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(l<=0)throw new r.DeveloperError("granularity must be greater than zero.");var p=t.defaultValue(e.height,0),c=t.defaultValue(e.extrudedHeight,p);this._center=a.Cartesian3.clone(n),this._semiMajorAxis=s,this._semiMinorAxis=u,this._ellipsoid=i.Ellipsoid.clone(o),this._rotation=t.defaultValue(e.rotation,0),this._stRotation=t.defaultValue(e.stRotation,0),this._height=Math.max(c,p),this._granularity=l,this._vertexFormat=f.VertexFormat.clone(m),this._extrudedHeight=Math.min(c,p),this._shadowVolume=t.defaultValue(e.shadowVolume,!1),this._workerName="createEllipseGeometry",this._offsetAttribute=e.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}Y.packedLength=a.Cartesian3.packedLength+i.Ellipsoid.packedLength+f.VertexFormat.packedLength+9,Y.pack=function(e,n,o){return r.Check.defined("value",e),r.Check.defined("array",n),o=t.defaultValue(o,0),a.Cartesian3.pack(e._center,n,o),o+=a.Cartesian3.packedLength,i.Ellipsoid.pack(e._ellipsoid,n,o),o+=i.Ellipsoid.packedLength,f.VertexFormat.pack(e._vertexFormat,n,o),o+=f.VertexFormat.packedLength,n[o++]=e._semiMajorAxis,n[o++]=e._semiMinorAxis,n[o++]=e._rotation,n[o++]=e._stRotation,n[o++]=e._height,n[o++]=e._granularity,n[o++]=e._extrudedHeight,n[o++]=e._shadowVolume?1:0,n[o]=t.defaultValue(e._offsetAttribute,-1),n};var H=new a.Cartesian3,U=new i.Ellipsoid,Q=new f.VertexFormat,W={center:H,ellipsoid:U,vertexFormat:Q,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};function q(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];for(var r=h.EllipseGeometryLibrary.computeEllipsePositions({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,rotation:e._rotation,granularity:e._granularity},!1,!0),i=r.outerPositions,n=i.length/3,o=new Array(n),s=0;s<n;++s)o[s]=a.Cartesian3.fromArray(i,3*s);var u=e._ellipsoid,m=e.rectangle;return l.Geometry._textureCoordinateRotationPoints(o,t,u,m)}Y.unpack=function(e,n,o){r.Check.defined("array",e),n=t.defaultValue(n,0);var s=a.Cartesian3.unpack(e,n,H);n+=a.Cartesian3.packedLength;var u=i.Ellipsoid.unpack(e,n,U);n+=i.Ellipsoid.packedLength;var l=f.VertexFormat.unpack(e,n,Q);n+=f.VertexFormat.packedLength;var m=e[n++],p=e[n++],c=e[n++],y=e[n++],d=e[n++],h=e[n++],A=e[n++],x=1===e[n++],g=e[n];return t.defined(o)?(o._center=a.Cartesian3.clone(s,o._center),o._ellipsoid=i.Ellipsoid.clone(u,o._ellipsoid),o._vertexFormat=f.VertexFormat.clone(l,o._vertexFormat),o._semiMajorAxis=m,o._semiMinorAxis=p,o._rotation=c,o._stRotation=y,o._height=d,o._granularity=h,o._extrudedHeight=A,o._shadowVolume=x,o._offsetAttribute=-1===g?void 0:g,o):(W.height=d,W.extrudedHeight=A,W.granularity=h,W.stRotation=y,W.rotation=c,W.semiMajorAxis=m,W.semiMinorAxis=p,W.shadowVolume=x,W.offsetAttribute=-1===g?void 0:g,new Y(W))},Y.computeRectangle=function(e,n){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var o=e.center,s=t.defaultValue(e.ellipsoid,i.Ellipsoid.WGS84),u=e.semiMajorAxis,l=e.semiMinorAxis,m=t.defaultValue(e.granularity,a.CesiumMath.RADIANS_PER_DEGREE),p=t.defaultValue(e.rotation,0);if(r.Check.defined("options.center",o),r.Check.typeOf.number("options.semiMajorAxis",u),r.Check.typeOf.number("options.semiMinorAxis",l),u<l)throw new r.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(m<=0)throw new r.DeveloperError("granularity must be greater than zero.");return B(o,u,l,p,m,s,n)},Y.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var r=e._height,i=e._extrudedHeight,n=!a.CesiumMath.equalsEpsilon(r,i,0,a.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var o,s={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:r,granularity:e._granularity,vertexFormat:e._vertexFormat,stRotation:e._stRotation};if(n)s.extrudedHeight=i,s.shadowVolume=e._shadowVolume,s.offsetAttribute=e._offsetAttribute,o=z(s);else if(o=S(s),t.defined(e._offsetAttribute)){var p=o.attributes.position.values.length,c=new Uint8Array(p/3),y=e._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;d.arrayFill(c,y),o.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:u.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:c})}return new l.Geometry({attributes:o.attributes,indices:o.indices,primitiveType:m.PrimitiveType.TRIANGLES,boundingSphere:o.boundingSphere,offsetAttribute:e._offsetAttribute})}},Y.createShadowVolume=function(e,t,r){var a=e._granularity,i=e._ellipsoid,n=t(a,i),o=r(a,i);return new Y({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:i,rotation:e._rotation,stRotation:e._stRotation,granularity:a,extrudedHeight:n,height:o,vertexFormat:f.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(Y.prototype,{rectangle:{get:function(){return t.defined(this._rectangle)||(this._rectangle=B(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return t.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=q(this)),this._textureCoordinateRotationPoints}}}),e.EllipseGeometry=Y}));