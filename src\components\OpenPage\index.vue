<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-23 14:55:16
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 14:58:54
 * @FilePath: \bigscreen-qj-web\src\components\OpenPage\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="open-page-coantainer">
    <img class="icon" src="@/assets/ScreenLeft/DynamicMaintenance/icon.png" alt="" @click="handleClick" />
  </div>
</template>
<script setup lang="ts">
  import { ref, defineProps, defineEmits } from 'vue'
  import { closePopWindowByTag, PopWindowProps, usePopWindow } from 'znyg-frontend-common'
  import SystemIframe from '@/views/Screen/PopupWindow/SystemIframe/index.vue'

  import lib from '@/utils/lib'

  const props = defineProps({
    path: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => ({})
    }
  })
  const { showPopWindow } = usePopWindow()
  const handleClick = () => {
    console.log('props-------', props)

    // console.log('handleClick')
    closePopWindowByTag(lib.enumMap.PopWindowTag.后台管理)
    const op: PopWindowProps = {
      left: 1587,
      top: 166,
      tag: lib.enumMap.PopWindowTag.后台管理,
      zIndex: 999,
      draggable: true
    }
    showPopWindow(op, SystemIframe, { path: props.path, params: props.params })
  }
</script>

<style lang="scss" scoped>
  .open-page-coantainer {
    position: absolute;
    cursor: pointer;
  }
</style>
