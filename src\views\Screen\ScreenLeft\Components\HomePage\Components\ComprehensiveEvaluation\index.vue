<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-07 14:41:17
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-30 10:23:02
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\ComprehensiveEvaluation\index.vue
 * @Description
 *
-->
<template>
  <div class="dynamic-maintenance-container">
    <HeadLine>
      <template #title>
        综合评价
        <el-popover placement="top" trigger="hover" popper-class="image-popover2" effect="customized">
          <template #reference>
            <el-icon :size="20" style="color: #00a3ff; cursor: pointer">
              <component :is="Picture" />
            </el-icon>
          </template>
          <div class="image-box">
            <slot name="image-content">
              <img src="@/assets/ScreenLeft/ComprehensiveEvaluation/zhpj.png" class="preview-image" :style="{ width: '220px', height: '280px' }" />
            </slot>
          </div>
        </el-popover>
      </template>
      <DatePicker
        v-model="dateRange"
        @change="handleDateChange"
        type="daterange"
        dateStyle="width:360px;height:36px"
        style="top: -80px; right: 122px"></DatePicker>
      <!-- 自定义跑马灯组件 -->
      <div class="custom-marquee-container" style="height: 190px">
        <div
          class="marquee-wrapper"
          ref="marqueeWrapper"
          :style="{ transform: `translateX(${translateX}px)` }"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd">
          <div class="marquee-item" v-for="(parent, index) in chartList" :key="index">
            <div class="content">
              <evaluation-item
                :item="item"
                v-for="(item, index2) in parent"
                :key="index2"
                :showImageTooltip="true"
                :tooltipIcon="Picture"
                :tooltipIconSize="20"
                :tooltipIconColor="'#00a3ff'"
                :imageUrl="item.formulaIcon"
                :imageWidth="'392px'"
                :imageHeight="'154px'"></evaluation-item>
            </div>
          </div>
        </div>
        <!-- 导航指示器 -->
        <div class="marquee-indicators">
          <span
            v-for="(item, index) in chartList"
            :key="index"
            :class="['indicator', { active: currentIndex === index }]"
            @mouseenter="goToSlide(index)"
            @click="goToSlide(index)"></span>
        </div>
      </div>
    </HeadLine>
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, onUnmounted, nextTick } from 'vue'
  import moment from 'moment'
  import DatePicker from '@/components/DatePicker/index.vue'
  import HeadLine from '@/components/HeadLine/index.vue'
  import evaluationItem from './Components/evaluationItem.vue'
  import lib from '@/utils/lib'
  import { useIntervalFn } from '@vueuse/core'
  import { getAssetsFile } from '@/utils'
  import { Picture } from '@element-plus/icons-vue'

  // const dateRange = ref([moment().add(-1, 'W').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')])
  const dateRange = ref([moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')])

  // 跑马灯相关状态
  const marqueeWrapper = ref(null)
  const currentIndex = ref(0)
  const translateX = ref(0)
  const itemWidth = ref(1284) // 每个项目的宽度，和原来的 content 宽度保持一致
  const isTransitioning = ref(false)

  // 触摸事件相关状态
  const touchState = reactive({
    startX: 0,
    startY: 0,
    isTouching: false,
    startTime: 0
  })

  const chartList = reactive([
    [
      {
        chartData: [],
        name: '综合状况评价',
        value: null,
        describe: '综合状况指数',
        color: '#148DFF',
        tooltipTitle: '对隧道技术性能及隧道运行服务质量的综合评分。',
        tooltipContent:
          '[90,100]分:性能完好；服务水平优秀<br>[80,90)分:性能退化；服务水平良好<br>[65,80)分：性能劣化；服务水平一般<br>[50,65)分：性能恶化；无法提供正常服务<br>[0,50)分：性能严重恶化；危及运行服务安全',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg1.png')
      },
      {
        chartData: [],
        name: '事件评价',
        value: null,
        describe: '牵引及时率',
        color: '#14DFFF',
        tooltipTitle: '对突发事件发生后牵引车辆到达现场的及时率的评分。',
        tooltipContent: '100分：非常及时<br>90分：及时<br>80分：一般及时<br>70分：滞后<br>50分：严重滞后',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg2.png')
      },
      {
        chartData: [],
        name: '通行安全评价',
        value: null,
        describe: '交通事故率',
        color: '#FFC014',
        tooltipTitle: '对隧道年度百万车公里事故次数的评分。',
        tooltipContent: '100分：不太可能<br>90分：偶尔<br>80分：少量<br>70分：经常<br>50分：频繁',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg3.png')
      },
      {
        chartData: [],
        name: '通行效率评价',
        value: null,
        describe: '高峰期饱和度',
        color: '#4A86FF',
        tooltipTitle: '对隧道高峰期车流量饱和度的评分。',
        tooltipContent: '100分：高车流服务<br>90分：正常车流服务<br>80分：较低车流服务<br>70分：低车流服务<br>50分：极低车流服务',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg4.png')
      }
    ],
    [
      {
        chartData: [],
        name: '土建设施评价',
        value: null,
        describe: '技术性能指数',
        color: '#825DFF',
        tooltipTitle: '对隧道土建设施技术性能的综合评分。',
        tooltipContent:
          '[90,100]分：结构性能完好<br>[80,90)分：结构性能退化<br>[65,80)分：结构性能劣化<br>[50,65)分：结构性能恶化<br>[0,50)分：结构性能严重恶化',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg5.png')
      },
      {
        chartData: [],
        name: '机电系统评价',
        value: null,
        describe: '技术性能指数',
        color: '#76E27D',
        tooltipTitle: '对隧道机电系统技术性能的综合评分。',
        tooltipContent: '[90,100]分：设备完好率高<br>[80,90)分：设备完好率较高<br>[65,80)分：运行基本正常<br>[50,65)分：完好率较低<br>[0,50)分：无法正常运行',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg6.png')
      },
      {
        chartData: [],
        name: '运行服务评价',
        value: null,
        describe: '质量指数',
        color: '#FFB871',
        tooltipTitle: '对隧道运行服务质量状况的综合评分。',
        tooltipContent: '[90,100]分：总体水平高<br>[80,90)分：总体水平较高<br>[70,80)分：总体水平一般<br>[60,70)分：总体水平较低<br>[0,60)分：总体水平差',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg7.png')
      },
      {
        chartData: [],
        name: '附属设施评价',
        value: null,
        describe: '技术性能指数',
        color: '#67E3D7',
        tooltipTitle: '对隧道附属设施技术性能的综合评分。',
        tooltipContent:
          '[90,100]分：设施完好无异常<br>[80,90)分：设施基本完好<br>[65,80)分：设施存在破损<br>[50,65)分：设施存在较多破损<br>[0,50)分：设施存在严重破损',
        formulaIcon: getAssetsFile('ScreenLeft/ComprehensiveEvaluation/formulaImg8.png')
      }
    ]
  ])
  const color = reactive([
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#017DB2'
        },
        {
          offset: 1,
          color: '#01FF39'
        }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#7B6000'
        },
        {
          offset: 1,
          color: '#FEC601'
        }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#00503C'
        },
        {
          offset: 1,
          color: '#01FEC1'
        }
      ]
    },
    {
      type: 'linear',
      x: 0,
      y: 0,
      x2: 1,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#017DB2'
        },
        {
          offset: 1,
          color: '#01FE3A'
        }
      ]
    }
  ])
  // 跳转到指定幻灯片
  const goToSlide = (index) => {
    if (isTransitioning.value || index === currentIndex.value) return

    isTransitioning.value = true
    currentIndex.value = index
    translateX.value = -index * itemWidth.value

    setTimeout(() => {
      isTransitioning.value = false
    }, 500) // 与 CSS 过渡时间保持一致
  }

  // 下一张
  const nextSlide = () => {
    const nextIndex = (currentIndex.value + 1) % chartList.length
    goToSlide(nextIndex)
  }

  // 上一张
  const prevSlide = () => {
    const prevIndex = currentIndex.value === 0 ? chartList.length - 1 : currentIndex.value - 1
    goToSlide(prevIndex)
  }

  // 键盘事件处理
  const handleKeydown = (event) => {
    if (event.key === 'ArrowLeft') {
      prevSlide()
    } else if (event.key === 'ArrowRight') {
      nextSlide()
    }
  }

  // 触摸事件处理
  const handleTouchStart = (event) => {
    if (isTransitioning.value) return

    touchState.isTouching = true
    touchState.startX = event.touches[0].clientX
    touchState.startY = event.touches[0].clientY
    touchState.startTime = Date.now()
  }

  const handleTouchMove = (event) => {
    if (!touchState.isTouching || isTransitioning.value) return
    event.preventDefault()
  }

  const handleTouchEnd = (event) => {
    if (!touchState.isTouching || isTransitioning.value) return

    const endX = event.changedTouches[0].clientX
    const endY = event.changedTouches[0].clientY
    const deltaX = endX - touchState.startX
    const deltaY = endY - touchState.startY
    const deltaTime = Date.now() - touchState.startTime

    // 判断是否为有效滑动（水平滑动距离大于垂直滑动距离，且滑动距离超过阈值）
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50 && deltaTime < 500) {
      if (deltaX > 0) {
        prevSlide() // 向右滑动，显示上一张
      } else {
        nextSlide() // 向左滑动，显示下一张
      }
    }

    touchState.isTouching = false
  }

  onMounted(() => {
    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown)

    // getData()
    useIntervalFn(
      () => {
        getData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })

  // 清理事件监听器
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
  const handleDateChange = (val) => {
    if (dateRange.value.length) {
      getData()
    }
  }
  const getData = async (val) => {
    // [chartList[0], chartList[1]].forEach((chart) => {
    //   chart.forEach((data) => {
    //     data.value = null
    //     data.chartData = {}
    //   })
    // })
    // let endDate = ''
    const date = dateRange.value[1]
    const week = moment(date).day()
    // 如果选择的时间范围结束时间是周六取结束时间，否则取结束时间的上一个周六
    const endDate =
      week === 6
        ? date
        : moment(date)
            .week(moment(date).week() - 1)
            .startOf('week')
            .add(6, 'days')
            .format('YYYY-MM-DD')
    await lib.api.bigscreenApi.accurateEvaluation({ endDate }).then((response) => {
      if (response.success && response.result) {
        ;[chartList[0], chartList[1]].forEach((chart) => {
          chart.forEach((data) => {
            const resultData = response.result.find((_) => _.title === data.name)
            data.value = resultData ? resultData.proValue : data.value
            data.chartData = upDataChart(data.value, data.color)
          })
        })
        console.log('chartList[0]', chartList[0])
      }
    })
  }
  const upDataChart = (value, color) => {
    return [
      {
        value: value,
        name: '已完成',
        itemStyle: {
          color: color
        }
      },
      {
        value: 100 - value,
        name: '未完成',
        itemStyle: {
          color: '#3F689C'
        }
      }
    ]
  }
</script>

<style lang="scss" scoped>
  .dynamic-maintenance-container {
    .content {
      display: flex;
      justify-content: space-between;
      width: 1284px;
      height: 155px;
    }

    // 自定义跑马灯样式
    .custom-marquee-container {
      position: relative;
      width: 100%;
      overflow: hidden;
      .marquee-wrapper {
        display: flex;
        user-select: none; // 防止拖拽时选中文本
        transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        -webkit-touch-callout: none; // 禁用 iOS Safari 中的长按菜单
        .marquee-item {
          flex-shrink: 0;
          width: 1284px; // 与原来的 content 宽度保持一致
          .content {
            display: flex;
            justify-content: space-between;
            width: 1284px;
            height: 155px;
          }
        }
      }

      // 导航指示器样式
      .marquee-indicators {
        position: absolute;
        bottom: 30px;
        left: 50%;
        z-index: 2;
        display: flex;
        gap: 8px;
        transform: translateX(-50%);
        .indicator {
          width: 24px;
          height: 4px;
          cursor: pointer;
          background-color: rgb(255 255 255 / 40%);
          border-radius: 2px;
          transition: all 0.3s ease;
          &:hover {
            background-color: rgb(255 255 255 / 70%);
            transform: scaleY(1.5);
          }
          &.active {
            background-color: #00a3ff;
            transform: scaleY(1.5);
          }
        }
      }
    }
  }
</style>
<style lang="scss">
  .image-popover2 {
    .image-box {
      width: 220px;
      height: 280px;
    }
  }
</style>
