define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./PrimitivePipeline-d98b7ff1","./WebMercatorProjection-ab2cf572","./createTaskProcessorWorker"],(function(e,t,i,r,n,a,o,c,f,s,m,b,d,P,p,u,y,l,C,v,G,k){"use strict";function T(e,t){var i=v.PrimitivePipeline.unpackCombineGeometryParameters(e),r=v.PrimitivePipeline.combineGeometry(i);return v.PrimitivePipeline.packCombineGeometryResults(r,t)}var h=k(T);return h}));