import CartographicGeocoderService from"../../Core/CartographicGeocoderService.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import DeveloperError from"../../Core/DeveloperError.js";import Event from"../../Core/Event.js";import GeocodeType from"../../Core/GeocodeType.js";import IonGeocoderService from"../../Core/IonGeocoderService.js";import CesiumMath from"../../Core/Math.js";import Matrix4 from"../../Core/Matrix4.js";import Rectangle from"../../Core/Rectangle.js";import sampleTerrainMostDetailed from"../../Core/sampleTerrainMostDetailed.js";import computeFlyToLocationForRectangle from"../../Scene/computeFlyToLocationForRectangle.js";import knockout from"../../ThirdParty/knockout.js";import when from"../../ThirdParty/when.js";import createCommand from"../createCommand.js";import getElement from"../getElement.js";var DEFAULT_HEIGHT=1e3;function GeocoderViewModel(e){if(!defined(e)||!defined(e.scene))throw new DeveloperError("options.scene is required.");defined(e.geocoderServices)?this._geocoderServices=e.geocoderServices:this._geocoderServices=[new CartographicGeocoderService,new IonGeocoderService({scene:e.scene})],this._viewContainer=e.container,this._scene=e.scene,this._flightDuration=e.flightDuration,this._searchText="",this._isSearchInProgress=!1,this._geocodePromise=void 0,this._complete=new Event,this._suggestions=[],this._selectedSuggestion=void 0,this._showSuggestions=!0,this._handleArrowDown=handleArrowDown,this._handleArrowUp=handleArrowUp;var o=this;this._suggestionsVisible=knockout.pureComputed((function(){var e=knockout.getObservable(o,"_suggestions"),t=e().length>0,i=knockout.getObservable(o,"_showSuggestions")();return t&&i})),this._searchCommand=createCommand((function(e){if(e=defaultValue(e,GeocodeType.SEARCH),o._focusTextbox=!1,defined(o._selectedSuggestion))return o.activateSuggestion(o._selectedSuggestion),!1;o.hideSuggestions(),o.isSearchInProgress?cancelGeocode(o):geocode(o,o._geocoderServices,e)})),this.deselectSuggestion=function(){o._selectedSuggestion=void 0},this.handleKeyDown=function(e,o){var t="ArrowDown"===o.key||"Down"===o.key||40===o.keyCode,i="ArrowUp"===o.key||"Up"===o.key||38===o.keyCode;return(t||i)&&o.preventDefault(),!0},this.handleKeyUp=function(e,t){var i="ArrowDown"===t.key||"Down"===t.key||40===t.keyCode,n="ArrowUp"===t.key||"Up"===t.key||38===t.keyCode,s="Enter"===t.key||13===t.keyCode;return n?handleArrowUp(o):i?handleArrowDown(o):s&&o._searchCommand(),!0},this.activateSuggestion=function(e){o.hideSuggestions(),o._searchText=e.displayName;var t=e.destination;clearSuggestions(o),o.destinationFound(o,t)},this.hideSuggestions=function(){o._showSuggestions=!1,o._selectedSuggestion=void 0},this.showSuggestions=function(){o._showSuggestions=!0},this.handleMouseover=function(e,t){e!==o._selectedSuggestion&&(o._selectedSuggestion=e)},this.keepExpanded=!1,this.autoComplete=defaultValue(e.autocomplete,!0),this.destinationFound=defaultValue(e.destinationFound,GeocoderViewModel.flyToDestination),this._focusTextbox=!1,knockout.track(this,["_searchText","_isSearchInProgress","keepExpanded","_suggestions","_selectedSuggestion","_showSuggestions","_focusTextbox"]);var t=knockout.getObservable(this,"_searchText");t.extend({rateLimit:{timeout:500}}),this._suggestionSubscription=t.subscribe((function(){GeocoderViewModel._updateSearchSuggestions(o)})),this.isSearchInProgress=void 0,knockout.defineProperty(this,"isSearchInProgress",{get:function(){return this._isSearchInProgress}}),this.searchText=void 0,knockout.defineProperty(this,"searchText",{get:function(){return this.isSearchInProgress?"Searching...":this._searchText},set:function(e){if("string"!==typeof e)throw new DeveloperError("value must be a valid string.");this._searchText=e}}),this.flightDuration=void 0,knockout.defineProperty(this,"flightDuration",{get:function(){return this._flightDuration},set:function(e){if(defined(e)&&e<0)throw new DeveloperError("value must be positive.");this._flightDuration=e}})}function handleArrowUp(e){if(0!==e._suggestions.length){var o,t=e._suggestions.indexOf(e._selectedSuggestion);-1!==t&&0!==t?(o=t-1,e._selectedSuggestion=e._suggestions[o],GeocoderViewModel._adjustSuggestionsScroll(e,o)):e._selectedSuggestion=void 0}}function handleArrowDown(e){if(0!==e._suggestions.length){var o=e._suggestions.length,t=e._suggestions.indexOf(e._selectedSuggestion),i=(t+1)%o;e._selectedSuggestion=e._suggestions[i],GeocoderViewModel._adjustSuggestionsScroll(e,i)}}function computeFlyToLocationForCartographic(e,o){var t=defined(o)?o.availability:void 0;return defined(t)?sampleTerrainMostDetailed(o,[e]).then((function(o){return e=o[0],e.height+=DEFAULT_HEIGHT,e})):(e.height+=DEFAULT_HEIGHT,when.resolve(e))}function flyToDestination(e,o){var t,i=e._scene,n=i.mapProjection,s=n.ellipsoid,r=i.camera,c=i.terrainProvider,a=o;o instanceof Rectangle?CesiumMath.equalsEpsilon(o.south,o.north,CesiumMath.EPSILON7)&&CesiumMath.equalsEpsilon(o.east,o.west,CesiumMath.EPSILON7)?o=Rectangle.center(o):t=computeFlyToLocationForRectangle(o,i):o=s.cartesianToCartographic(o),defined(t)||(t=computeFlyToLocationForCartographic(o,c)),t.then((function(e){a=s.cartographicToCartesian(e)})).always((function(){r.flyTo({destination:a,complete:function(){e._complete.raiseEvent()},duration:e._flightDuration,endTransform:Matrix4.IDENTITY})}))}function chainPromise(e,o,t,i){return e.then((function(e){if(defined(e)&&"fulfilled"===e.state&&e.value.length>0)return e;var n=o.geocode(t,i).then((function(e){return{state:"fulfilled",value:e}})).otherwise((function(e){return{state:"rejected",reason:e}}));return n}))}function geocode(e,o,t){var i=e._searchText;if(hasOnlyWhitespace(i))e.showSuggestions();else{e._isSearchInProgress=!0;for(var n=when.resolve(),s=0;s<o.length;s++)n=chainPromise(n,o[s],i,t);e._geocodePromise=n,n.then((function(o){if(!n.cancel){e._isSearchInProgress=!1;var t=o.value;if("fulfilled"===o.state&&defined(t)&&t.length>0)return e._searchText=t[0].displayName,void e.destinationFound(e,t[0].destination);e._searchText=i+" (not found)"}}))}}function adjustSuggestionsScroll(e,o){var t=getElement(e._viewContainer),i=t.getElementsByClassName("search-results")[0],n=t.getElementsByTagName("li"),s=n[o];if(0!==o){var r=s.offsetTop;r+s.clientHeight>i.clientHeight?i.scrollTop=r+s.clientHeight:r<i.scrollTop&&(i.scrollTop=r)}else i.scrollTop=0}function cancelGeocode(e){e._isSearchInProgress=!1,defined(e._geocodePromise)&&(e._geocodePromise.cancel=!0,e._geocodePromise=void 0)}function hasOnlyWhitespace(e){return/^\s*$/.test(e)}function clearSuggestions(e){knockout.getObservable(e,"_suggestions").removeAll()}function updateSearchSuggestions(e){if(e.autoComplete){var o=e._searchText;if(clearSuggestions(e),!hasOnlyWhitespace(o)){var t=when.resolve([]);e._geocoderServices.forEach((function(e){t=t.then((function(t){return t.length>=5?t:e.geocode(o,GeocodeType.AUTOCOMPLETE).then((function(e){return t=t.concat(e),t}))}))})),t.then((function(o){for(var t=e._suggestions,i=0;i<o.length;i++)t.push(o[i])}))}}}Object.defineProperties(GeocoderViewModel.prototype,{complete:{get:function(){return this._complete}},scene:{get:function(){return this._scene}},search:{get:function(){return this._searchCommand}},selectedSuggestion:{get:function(){return this._selectedSuggestion}},suggestions:{get:function(){return this._suggestions}}}),GeocoderViewModel.prototype.destroy=function(){this._suggestionSubscription.dispose()},GeocoderViewModel.flyToDestination=flyToDestination,GeocoderViewModel._updateSearchSuggestions=updateSearchSuggestions,GeocoderViewModel._adjustSuggestionsScroll=adjustSuggestionsScroll;export default GeocoderViewModel;