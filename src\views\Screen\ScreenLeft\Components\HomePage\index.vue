<template>
  <div class="homepage-container">
    <DynamicMaintenance></DynamicMaintenance>
    <EventManage></EventManage>
    <ComprehensiveEvaluation></ComprehensiveEvaluation>
  </div>
</template>

<script setup>
  import ComprehensiveEvaluation from './Components/ComprehensiveEvaluation/index.vue'
  import DynamicMaintenance from './Components/DynamicMaintenance/index.vue'
  import EventManage from './Components/EventManage/index.vue'
</script>

<style lang="scss" scoped>
  .homepage-container {
  }
</style>
