export const provideTools = {
  /**
   * @description: 处理显示隐藏
   */
  handleHide: {
    provide: (e) => provide('handleHide', e),
    inject: () => inject('handleHide')
  },
  /**
   * @description: 点击的模型，可以使用Ue中的对象代替
   */
  clickModel: {
    provide: (e) => provide('clickModel', e),
    inject: () => inject('clickModel')
  },
  handleClose: {
    provide: (e) => provide('handleClose', e),
    inject: () => inject('handleClose')
  },
  handleShowBothSide: {
    provide: (e) => provide('handleShowBothSide', e),
    inject: () => inject('handleShowBothSide')
  }
}
