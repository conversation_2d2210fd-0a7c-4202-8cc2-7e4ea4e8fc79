/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Cartesian3-bb0e6278","./defined-3b3eb2ba","./EllipseOutlineGeometry-6beac65c","./Rectangle-9bffefe4","./Math-b5f4d889","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./EllipseGeometryLibrary-07d021fe","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b"],(function(e,i,t,r,n,l,s,o,a,d,u,c,m,p,y,f){"use strict";function G(e){const r=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).radius,n={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,numberOfVerticalLines:e.numberOfVerticalLines};this._ellipseGeometry=new t.EllipseOutlineGeometry(n),this._workerName="createCircleOutlineGeometry"}G.packedLength=t.EllipseOutlineGeometry.packedLength,G.pack=function(e,i,r){return t.EllipseOutlineGeometry.pack(e._ellipseGeometry,i,r)};const b=new t.EllipseOutlineGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),_={center:new e.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,numberOfVerticalLines:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0};return G.unpack=function(n,l,s){const o=t.EllipseOutlineGeometry.unpack(n,l,b);return _.center=e.Cartesian3.clone(o._center,_.center),_.ellipsoid=r.Ellipsoid.clone(o._ellipsoid,_.ellipsoid),_.height=o._height,_.extrudedHeight=o._extrudedHeight,_.granularity=o._granularity,_.numberOfVerticalLines=o._numberOfVerticalLines,i.defined(s)?(_.semiMajorAxis=o._semiMajorAxis,_.semiMinorAxis=o._semiMinorAxis,s._ellipseGeometry=new t.EllipseOutlineGeometry(_),s):(_.radius=o._semiMajorAxis,new G(_))},G.createGeometry=function(e){return t.EllipseOutlineGeometry.createGeometry(e._ellipseGeometry)},function(t,n){return i.defined(n)&&(t=G.unpack(t,n)),t._ellipseGeometry._center=e.Cartesian3.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(t._ellipseGeometry._ellipsoid),G.createGeometry(t)}}));
