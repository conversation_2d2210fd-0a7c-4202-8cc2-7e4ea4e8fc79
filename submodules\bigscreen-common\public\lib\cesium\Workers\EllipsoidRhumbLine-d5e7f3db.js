/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Math-b5f4d889"],(function(t,i,e,a,n){"use strict";function s(t,i,e){if(0===t)return i*e;const a=t*t,n=a*a,s=n*a,h=s*a,u=h*a,o=u*a,l=e;return i*((1-a/4-3*n/64-5*s/256-175*h/16384-441*u/65536-4851*o/1048576)*l-(3*a/8+3*n/32+45*s/1024+105*h/4096+2205*u/131072+6237*o/524288)*Math.sin(2*l)+(15*n/256+45*s/1024+525*h/16384+1575*u/65536+155925*o/8388608)*Math.sin(4*l)-(35*s/3072+175*h/12288+3675*u/262144+13475*o/1048576)*Math.sin(6*l)+(315*h/131072+2205*u/524288+43659*o/8388608)*Math.sin(8*l)-(693*u/1310720+6237*o/5242880)*Math.sin(10*l)+1001*o/8388608*Math.sin(12*l))}function h(t,i){if(0===t)return Math.log(Math.tan(.5*(n.CesiumMath.PI_OVER_TWO+i)));const e=t*Math.sin(i);return Math.log(Math.tan(.5*(n.CesiumMath.PI_OVER_TWO+i)))-t/2*Math.log((1+e)/(1-e))}const u=new i.Cartesian3,o=new i.Cartesian3;function l(t,a,l,r){i.Cartesian3.normalize(r.cartographicToCartesian(a,o),u),i.Cartesian3.normalize(r.cartographicToCartesian(l,o),o);const d=r.maximumRadius,c=r.minimumRadius,M=d*d,g=c*c;t._ellipticitySquared=(M-g)/M,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=e.Cartographic.clone(a,t._start),t._start.height=0,t._end=e.Cartographic.clone(l,t._end),t._end.height=0,t._heading=function(t,i,e,a,s){const u=h(t._ellipticity,e),o=h(t._ellipticity,s);return Math.atan2(n.CesiumMath.negativePiToPi(a-i),o-u)}(t,a.longitude,a.latitude,l.longitude,l.latitude),t._distance=function(t,i,e,a,h,u,o){const l=t._heading,r=u-a;let d=0;if(n.CesiumMath.equalsEpsilon(Math.abs(l),n.CesiumMath.PI_OVER_TWO,n.CesiumMath.EPSILON8))if(i===e)d=i*Math.cos(h)*n.CesiumMath.negativePiToPi(r);else{const e=Math.sin(h);d=i*Math.cos(h)*n.CesiumMath.negativePiToPi(r)/Math.sqrt(1-t._ellipticitySquared*e*e)}else{const e=s(t._ellipticity,i,h);d=(s(t._ellipticity,i,o)-e)/Math.cos(l)}return Math.abs(d)}(t,r.maximumRadius,r.minimumRadius,a.longitude,a.latitude,l.longitude,l.latitude)}function r(t,i,u,o,l,r){if(0===u)return e.Cartographic.clone(t,r);const d=l*l;let c,M,g;if(Math.abs(n.CesiumMath.PI_OVER_TWO-Math.abs(i))>n.CesiumMath.EPSILON8){M=function(t,i,e){const a=t/e;if(0===i)return a;const n=a*a,s=n*a,h=s*a,u=i*i,o=u*u,l=o*u,r=l*u,d=r*u,c=d*u,M=Math.sin(2*a),g=Math.cos(2*a),m=Math.sin(4*a),_=Math.cos(4*a),p=Math.sin(6*a),f=Math.cos(6*a),C=Math.sin(8*a),P=Math.cos(8*a),O=Math.sin(10*a);return a+a*u/4+7*a*o/64+15*a*l/256+579*a*r/16384+1515*a*d/65536+16837*a*c/1048576+(3*a*o/16+45*a*l/256-a*(32*n-561)*r/4096-a*(232*n-1677)*d/16384+a*(399985-90560*n+512*h)*c/5242880)*g+(21*a*l/256+483*a*r/4096-a*(224*n-1969)*d/16384-a*(33152*n-112599)*c/1048576)*_+(151*a*r/4096+4681*a*d/65536+1479*a*c/16384-453*s*c/32768)*f+(1097*a*d/65536+42783*a*c/1048576)*P+8011*a*c/1048576*Math.cos(10*a)+(3*u/8+3*o/16+213*l/2048-3*n*l/64+255*r/4096-33*n*r/512+20861*d/524288-33*n*d/512+h*d/1024+28273*c/1048576-471*n*c/8192+9*h*c/4096)*M+(21*o/256+21*l/256+533*r/8192-21*n*r/512+197*d/4096-315*n*d/4096+584039*c/16777216-12517*n*c/131072+7*h*c/2048)*m+(151*l/6144+151*r/4096+5019*d/131072-453*n*d/16384+26965*c/786432-8607*n*c/131072)*p+(1097*r/131072+1097*d/65536+225797*c/10485760-1097*n*c/65536)*C+(8011*d/2621440+8011*c/1048576)*O+293393*c/251658240*Math.sin(12*a)}(s(l,o,t.latitude)+u*Math.cos(i),l,o);const e=h(l,t.latitude),a=h(l,M);g=Math.tan(i)*(a-e),c=n.CesiumMath.negativePiToPi(t.longitude+g)}else{let e;if(M=t.latitude,0===l)e=o*Math.cos(t.latitude);else{const i=Math.sin(t.latitude);e=o*Math.cos(t.latitude)/Math.sqrt(1-d*i*i)}g=u/e,c=i>0?n.CesiumMath.negativePiToPi(t.longitude+g):n.CesiumMath.negativePiToPi(t.longitude-g)}return a.defined(r)?(r.longitude=c,r.latitude=M,r.height=0,r):new e.Cartographic(c,M,0)}function d(t,i,n){const s=a.defaultValue(n,e.Ellipsoid.WGS84);this._ellipsoid=s,this._start=new e.Cartographic,this._end=new e.Cartographic,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,a.defined(t)&&a.defined(i)&&l(this,t,i,s)}Object.defineProperties(d.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return this._heading}}}),d.fromStartHeadingDistance=function(t,i,s,h,u){const o=a.defaultValue(h,e.Ellipsoid.WGS84),l=o.maximumRadius,c=o.minimumRadius,M=l*l,g=c*c,m=Math.sqrt((M-g)/M),_=r(t,i=n.CesiumMath.negativePiToPi(i),s,o.maximumRadius,m);return!a.defined(u)||a.defined(h)&&!h.equals(u.ellipsoid)?new d(t,_,o):(u.setEndPoints(t,_),u)},d.prototype.setEndPoints=function(t,i){l(this,t,i,this._ellipsoid)},d.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},d.prototype.interpolateUsingSurfaceDistance=function(t,i){return r(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,i)},d.prototype.findIntersectionWithLongitude=function(t,i){const s=this._ellipticity,h=this._heading,u=Math.abs(h),o=this._start;if(t=n.CesiumMath.negativePiToPi(t),n.CesiumMath.equalsEpsilon(Math.abs(t),Math.PI,n.CesiumMath.EPSILON14)&&(t=n.CesiumMath.sign(o.longitude)*Math.PI),a.defined(i)||(i=new e.Cartographic),Math.abs(n.CesiumMath.PI_OVER_TWO-u)<=n.CesiumMath.EPSILON8)return i.longitude=t,i.latitude=o.latitude,i.height=0,i;if(n.CesiumMath.equalsEpsilon(Math.abs(n.CesiumMath.PI_OVER_TWO-u),n.CesiumMath.PI_OVER_TWO,n.CesiumMath.EPSILON8)){if(n.CesiumMath.equalsEpsilon(t,o.longitude,n.CesiumMath.EPSILON12))return;return i.longitude=t,i.latitude=n.CesiumMath.PI_OVER_TWO*n.CesiumMath.sign(n.CesiumMath.PI_OVER_TWO-h),i.height=0,i}const l=o.latitude,r=s*Math.sin(l),d=Math.tan(.5*(n.CesiumMath.PI_OVER_TWO+l))*Math.exp((t-o.longitude)/Math.tan(h)),c=(1+r)/(1-r);let M,g=o.latitude;do{M=g;const t=s*Math.sin(M),i=(1+t)/(1-t);g=2*Math.atan(d*Math.pow(i/c,s/2))-n.CesiumMath.PI_OVER_TWO}while(!n.CesiumMath.equalsEpsilon(g,M,n.CesiumMath.EPSILON12));return i.longitude=t,i.latitude=g,i.height=0,i},d.prototype.findIntersectionWithLatitude=function(t,i){const s=this._ellipticity,u=this._heading,o=this._start;if(n.CesiumMath.equalsEpsilon(Math.abs(u),n.CesiumMath.PI_OVER_TWO,n.CesiumMath.EPSILON8))return;const l=h(s,o.latitude),r=h(s,t),d=Math.tan(u)*(r-l),c=n.CesiumMath.negativePiToPi(o.longitude+d);return a.defined(i)?(i.longitude=c,i.latitude=t,i.height=0,i):new e.Cartographic(c,t,0)},t.EllipsoidRhumbLine=d}));
