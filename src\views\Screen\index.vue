<!--
 * @Description: 大屏主页面
 * @Autor: qian
 * @Date: 2023-06-19 17:04:41
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-22 09:50:40
-->
<template>
  <div class="Screen" id="ScreenBox">
    <!-- <div id="player" v-show="show3DModel"></div> -->
    <BimEngine v-show="show3DModel"></BimEngine>

    <StructureDiagram v-if="!show3DModel"></StructureDiagram>

    <ScreenTop></ScreenTop>
    <div class="screen-movable-area">
      <!-- 使用 transition 组件为 ScreenLeft 添加动画 -->
      <transition name="screen-left-fade">
        <ScreenLeft class="left-pannel backdrop-filter backdrop-blur-30" v-if="isShowLeft" :class="{ show: rotationLeft }" />
      </transition>

      <div
        class="screen-movable-area-main"
        :class="{
          'tilt-enter': rotationLeft && rotationRight,
          'tilt-exit': !rotationLeft && !rotationRight
        }"
        id="popUpRoot"
        :style="{ marginLeft: `${leftMargin}px`, marginRight: `${rightMargin}px` }">
        <!-- <AiButtons @btn-click="handleBtnClick" /> -->
        <OtherButtons :clearSelected="!rotationLeft && !rotationRight" :showModel="show3DModel" @btn-click="handleBtnClick" />
        <AIRobot class="absolute top-1000 -right-18 z-1000" />
        <div class="switch-clear" @click="clearAllPoint">清除撒点</div>
        <div class="switch-model" @click="show3DModel = !show3DModel">场景切换</div>
        <!-- <div class="button switch-weather" v-show="show3DModel" :class="{ selected: isRealWeather }" @click="openRealWeather">实时天气</div>
        <div class="button switch-views" v-show="show3DModel" @click="handleViewpoint">默认视口</div>
        <div class="button switch-car" v-show="show3DModel" :class="{ selected: isOpen }" @click="handleShowCars">车辆孪生</div>
        <div class="button switch-follow" v-show="show3DModel" @click="stopFollowCar">停止跟随</div>
        <div class="button switch-roam" v-show="show3DModel" :class="{ selected: isRoam }" @click="handleRoam">漫游</div> -->
        <el-switch
          v-model="showBothSide"
          active-text="打开"
          inactive-text="关闭"
          inline-prompt
          style="z-index: 999; float: right; margin-top: 1255px; margin-right: 20px"
          @change="handleShowBothSide" />
        <!-- <SelectViewPort ref="refSelectViewPort"></SelectViewPort> -->
        <ScreenMiddlePage></ScreenMiddlePage>
      </div>
      <!-- 使用 transition 组件为 ScreenRight 添加动画 -->
      <transition name="screen-right-fade">
        <ScreenRight class="right-pannel backdrop-filter backdrop-blur-30" v-if="isShowRight" :class="{ show: rotationRight }" />
      </transition>
    </div>
    <!-- 大图预览 -->
    <el-image-viewer v-if="storeDictionary.showBigImgList.length > 0" @close="closeViewer" :url-list="storeDictionary.showBigImgList" />
  </div>
</template>

<script>
  export default {
    name: 'Screen'
  }
</script>
<script setup>
  import moment from 'moment'

  import SelectViewPort from './components/selectViewPort.vue'
  import ScreenLeft from './ScreenLeft/index.vue'
  import ScreenMiddlePage from './ScreenMiddlePage/index.vue'
  import ScreenRight from './ScreenRight/index.vue'
  import ScreenTop from './ScreenTop/index.vue'
  import StructureDiagram from './StructureDiagram/index.vue'
  import BimEngine from '@/components/BimEngine/index.vue'
  import AiButtons from './components/AiButtons/index.vue'
  import AIRobot from '@/components/AIRobot/index.vue'

  import { roamList } from '@/hooks/useUE/data/data.js'
  import { toUe5 } from '@/hooks/useUE/tools.js'
  import useStore from '@/store/index.js'
  import lib from '@/utils/lib.ts'
  import OtherButtons from './components/OtherButtons/index.vue'
  // import { initLoad } from '@/utils/webRtcVideo.js'

  // #region mqtt
  // const isSendToUe = ref(false)
  // const isOpen = ref(false)
  const refSelectViewPort = ref(null)
  // const { initUE } = useUe()
  const isOpen = computed(() => {
    return lib.store().storeScreenData.vehicleTwinOpen
  })
  const handleShowCars = async () => {
    // lib.ueTools.toUe5('stopFollowVehicle', {})
    lib.store().storeScreenData.vehicleTwinOpen = !lib.store().storeScreenData.vehicleTwinOpen
    await nextTick()
    lib.ai.startTrafficStreamCommon(isOpen.value, 'ws://172.22.51.1:10000/mqtt/publish', 'tessng', 'mosquito', 'Cjxx-2024')

  }
  const stopFollowCar = () => {
    // lib._engineController.setBowlingEnable(true, false)
    lib.currentFollowCarPlate = null
    lib._engineController.enableClickToFollow(false)
    lib._engineController.viewer.ueViewer.setBowlingOptions({
      plateId: '',
      shiftUp: 5,
      drawBack: 10,
      lerpTime_is_0_to_1: '',
      lerpTime: 0.5
    })
  }
  // #endregion

  const { storeDictionary } = useStore()
  const showBothSide = ref(true)
  const isShowLeft = ref(true)
  const isShowRight = ref(true)
  const rotationLeft = ref(false)
  const rotationRight = ref(false)
  const show3DModel = ref(true)

  setTimeout(() => {
    isShowLeft.value = true
    isShowRight.value = true
  }, 300)
  const handleBtnClick = (val) => {
    console.log('handleBtnClick', val)
    rotationLeft.value = val
    rotationRight.value = val
  }
  const handleShowBothSide = (val) => {
    console.log('handleShowBothSide', val)
    isShowLeft.value = val
    isShowRight.value = val
    rotationLeft.value = false
    rotationRight.value = false
  }

  lib.provideTools.handleShowBothSide.provide(handleShowBothSide)
  // lib.provideTools.clickModel.provide(lib.ue().clickModel)

  // 关闭 清空 大图预览
  const closeViewer = () => {
    lib.store().storeDictionary.DIALOG_IMG([])
  }
  // 清除撒点
  const clearAllPoint = () => {
    lib.popWindow.removeDialog()
    // toUe5('customPOIList', {
    //   isOn: false, // 是否开启标签
    //   typeId: 0 // 设施设备类型ID（为0时指全部类型）
    // })
    // lib.bus.busTreeTableStructure.emit('clear')
    lib._engineController.clearCzml()
    lib._engineController.currentProject.clearIsolate()
    lib._engineController.clearSelect()
  }
  // const modelLoaded = ref(false)

  const isRoam = ref(false)
  const handleRoam = async () => {
    // lib.ueTools.toUe5('stopFollowVehicle', {})
    if (lib.currentFollowCarPlate) {
      await lib._engineController.cameraFollowCar(lib.currentFollowCarPlate, false)
      lib.currentFollowCarPlate = null
    }

    isRoam.value = !isRoam.value
    if (isRoam.value) {
      // const obj = roamList[0]
      // obj.roamStatus = 'playing'
      // lib.ueTools.toUe5('roam', obj)
      lib._engineController.romaByName('漫游')
    } else {
      // lib.ueTools.toUe5('roam', { roamStatus: 'ending' })
      lib._engineController.romaPause()
    }
  }
  // 回到默认视角
  const handleViewpoint = async () => {
    // lib.ueTools.toUe5Viewpoint('默认')
    // lib.ueTools.toUe5('stopFollowVehicle', {})
    if (lib.currentFollowCarPlate) {
      await lib._engineController.cameraFollowCar(lib.currentFollowCarPlate, false)
      lib.currentFollowCarPlate = null
    }
    lib._engineController.gotoViewportByName('默认视口')
    refSelectViewPort.value.setViewPort('默认视口')
  }

  // #region 实时天气
  const isRealWeather = ref(false)
  const openRealWeather = () => {
    isRealWeather.value = !isRealWeather.value
    if (isRealWeather.value) {
      // 获取天气信息
      lib.api.bigscreenApi.getWeather({}).then((res) => {
        if (res.success && res.result) {
          console.log('天气信息', res)
          const weatherMap = new Map()
          weatherMap.set(0, [100, 150, 900, 999])
          weatherMap.set(1, [101, 102, 151, 152])
          weatherMap.set(2, [500, 501, 502, 509, 510, 511, 512, 513, 514, 515])
          weatherMap.set(3, [104, 901])
          weatherMap.set(4, [103, 153])
          weatherMap.set(5, [300, 306, 307, 308, 313, 315, 316, 350, 399])
          weatherMap.set(6, [305, 309, 314])
          weatherMap.set(7, [301, 302, 303, 304, 310, 311, 312, 317, 318, 351])
          weatherMap.set(8, [503, 504])
          weatherMap.set(9, [507, 508])
          weatherMap.set(10, [401, 402, 404, 405, 406, 407, 409, 456, 457, 499])
          weatherMap.set(11, [403, 410])
          weatherMap.set(12, [400, 408])

          const getWeatherKey = (code) => {
            // 遍历 weatherMap
            for (const [key, values] of weatherMap) {
              // 检查当前值是否在数组中
              if (values.includes(code)) {
                return key
              }
            }
            // 如果没找到匹配值，返回 0 或其他默认值  ClearSky
            return 0
          }
          const weatherKey = getWeatherKey(res.result?.condCode)
          lib._engineController.setWeatherTime(weatherKey, parseInt(moment().format('HHmm')))
          // lib._engineController.setWeatherTime(0, 1800)
        }
      })
    } else {
      lib._engineController.setWeatherTime(0, 1200)
    }
  }
  // #endregion

  onMounted(() => {
    console.log('onMounted')

    lib.store().storeDictionary.setDictMap()
    lib.store().storeDictionary.setDictMapQJ()
    // initLoad()
    // initUE()
  })

  watchEffect(() => {
    lib._engineController.vehicleTwinOpen = isOpen.value
  })
  const leftMargin = ref(0)
  const rightMargin = ref(0)

  // 监听 rotationLeft 和 rotationRight 的变化
  watch(
    () => [rotationLeft.value, rotationRight.value],
    ([newLeft, newRight]) => {
      leftMargin.value = newLeft ? -183 : 0
      rightMargin.value = newRight ? -173 : 0
    }
  )
  // const { appContext } = getCurrentInstance()

  // lib.popWindow.createPopWindow(
  //         './Components/Test/index.vue',
  //         // './Components/StructureFile/index.vue',
  //         {
  //           left: 1420,
  //           top: 580,
  //           tag: 'structureWindow',
  //           appContext,
  //           appendParent: 'body',
  //           closeFunc: () => {
  //           }
  //         },
  //         {

  //         }
  //       )

 
</script>
<style lang="scss" scoped>
  .Screen {
    position: relative;
    width: $screen-width;
    height: $screen-height;
    overflow: hidden;

    // background: #04112031;
    .screen-movable-area {
      position: absolute;
      top: $screen-top-height;
      right: 0;
      left: 0;
      display: flex;
      pointer-events: none;
      .screen-movable-area-main {
        position: relative;
        flex: 1;
        height: 0;
        pointer-events: auto;
        background: red;
        &.tilt-exit {
          transition: all 0.5s ease-out;
        }
        &.tilt-enter {
          transition: all 0.5s ease-in;
        }
      }
      .left-pannel {
        transition: all 0.5s;
        &.show {
          // margin-left: 33px;
          // 距离 z=0 平面 800 像素,往内推 5 度
          transform: perspective(800px) rotateY(5deg);
          transform-origin: left;
        }
      }
      .right-pannel {
        transition: all 0.5s;
        &.show {
          // margin-right: 33px;
          transform: perspective(800px) rotateY(-5deg);
          transform-origin: right;
        }
      }
      .switch-model {
        position: absolute;
        top: 1250px;
        left: 40px;
        z-index: 999;
        width: 244px;
        height: 64px;
        padding-left: 82px;
        font-family: YouSheBiaoTiHei;
        font-size: 33px;
        font-weight: 400;
        line-height: 68px;
        color: #ffffff;
        cursor: pointer;
        background-image: url('@/assets/ScreenMiddle/switchModel.png');
        background-size: cover;
        &::before {
          position: absolute;
          top: 19px;
          left: 43px;
          width: 26px;
          height: 28px;
          content: '';
          background-image: url('@/assets/ScreenMiddle/switchModelIIcon.png');
          background-size: cover;
        }
      }
      .switch-clear {
        position: absolute;
        top: 1165px;
        left: 40px;
        z-index: 999;
        width: 244px;
        height: 64px;
        padding-left: 82px;
        font-family: YouSheBiaoTiHei;
        font-size: 33px;
        font-weight: 400;
        line-height: 68px;
        color: #ffffff;
        cursor: pointer;
        background-image: url('@/assets/ScreenMiddle/switchModel.png');
        background-size: cover;
        &::before {
          position: absolute;
          top: 19px;
          left: 43px;
          width: 26px;
          height: 28px;
          content: '';
          background-image: url('@/assets/ScreenMiddle/clearPoint.png');
          background-size: cover;
        }
      }
      .button {
        z-index: 999;
        width: 103px;
        height: 35px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        line-height: 30px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        background: #4b6f86;
        border: 3px solid #1161ab;
        border-radius: 18px;
        &.selected {
          color: #ffffff;
          background: #82b6f0;
        }
      }
      .switch-weather {
        position: absolute;
        top: 1008px;
        right: 20px;
      }
      .switch-views {
        position: absolute;
        top: 1058px;
        right: 20px;
      }
      .switch-car {
        position: absolute;
        top: 1108px;
        right: 20px;
      }
      .switch-follow {
        position: absolute;
        top: 1158px;
        right: 20px;
      }
      .switch-roam {
        position: absolute;
        top: 1209px;
        right: 20px;
      }
    }
    .show-hide-btn {
      position: absolute;
      top: 464px;
      width: 24px;
      height: 80px;
      cursor: pointer;
      &.show-hide-btn-left {
        left: 0;
      }
      &.show-hide-btn-right {
        right: 0;
      }
    }
  }
</style>
<style>
  .el-switch.is-checked .el-switch__core {
    background-color: #4b6f86;
  }
  .el-switch__core {
    min-width: 103px;
    height: 35px;

    /* border: 2px solid #00d1ff; */
    border: 3px solid #1161ab;
    border-radius: 18px;
  }
  .el-switch.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 26px);
    width: 21px;
    height: 21px;
  }
  .el-switch__core .el-switch__inner .is-text {
    font-family: 'Alibaba PuHuiTi';
    font-size: 20px;
  }
  #player,
  #streamingVideo {
    position: absolute;
    z-index: 0;
    width: 5120px;
    height: 1440px;
  }
</style>

<style lang="scss" scoped>
  .screen-left-fade-enter-active,
  .screen-left-fade-leave-active {
    transition: all 1s ease;
  }
  .screen-left-fade-enter-from,
  .screen-left-fade-leave-to {
    opacity: 0;
    transform: translateX(-100%);
  }
  .screen-right-fade-enter-active,
  .screen-right-fade-leave-active {
    transition: all 1s ease;
  }
  .screen-right-fade-enter-from,
  .screen-right-fade-leave-to {
    opacity: 0;
    transform: translateX(100%);
  }
</style>
