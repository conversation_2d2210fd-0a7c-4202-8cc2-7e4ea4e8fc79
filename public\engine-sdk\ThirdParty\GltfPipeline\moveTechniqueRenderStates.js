import addExtensionsUsed from"./addExtensionsUsed.js";import ForEach from"./ForEach.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import WebGLConstants from"../../Core/WebGLConstants.js";var defaultBlendEquation=[WebGLConstants.FUNC_ADD,WebGLConstants.FUNC_ADD],defaultBlendFactors=[WebGLConstants.ONE,WebGLConstants.ZERO,WebGLConstants.ONE,WebGLConstants.ZERO];function isStateEnabled(e,n){var t=e.enable;return!!defined(t)&&t.indexOf(n)>-1}var supportedBlendFactors=[WebGLConstants.ZERO,WebGLConstants.ONE,WebGLConstants.SRC_COLOR,WebGLConstants.ONE_MINUS_SRC_COLOR,WebGLConstants.SRC_ALPHA,WebGLConstants.ONE_MINUS_SRC_ALPHA,WebGLConstants.DST_ALPHA,WebGLConstants.ONE_MINUS_DST_ALPHA,WebGLConstants.DST_COLOR,WebGLConstants.ONE_MINUS_DST_COLOR];function getSupportedBlendFactors(e,n){if(!defined(e))return n;for(var t=0;t<4;t++)if(-1===supportedBlendFactors.indexOf(e[t]))return n;return e}function moveTechniqueRenderStates(e){var n={},t={},a=e.techniques;return defined(a)?(ForEach.technique(e,(function(e,a){var s=e.states;if(defined(s)){var o=t[a]={};if(isStateEnabled(s,WebGLConstants.BLEND)){o.alphaMode="BLEND";var d=s.functions;defined(d)&&(defined(d.blendEquationSeparate)||defined(d.blendFuncSeparate))&&(n[a]={blendEquation:defaultValue(d.blendEquationSeparate,defaultBlendEquation),blendFactors:getSupportedBlendFactors(d.blendFuncSeparate,defaultBlendFactors)})}isStateEnabled(s,WebGLConstants.CULL_FACE)||(o.doubleSided=!0),delete e.states}})),Object.keys(n).length>0&&(defined(e.extensions)||(e.extensions={}),addExtensionsUsed(e,"KHR_blend")),ForEach.material(e,(function(e){if(defined(e.technique)){var a=t[e.technique];ForEach.objectLegacy(a,(function(n,t){e[t]=n}));var s=n[e.technique];defined(s)&&(defined(e.extensions)||(e.extensions={}),e.extensions.KHR_blend=s)}})),e):e}export default moveTechniqueRenderStates;