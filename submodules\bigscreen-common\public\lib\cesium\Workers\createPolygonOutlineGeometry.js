/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./ArcType-e42cfb05","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./EllipsoidTangentPlane-c2c2ef6e","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryOffsetAttribute-5a4c2801","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonGeometryLibrary-1842a245","./PolygonPipeline-805d6577","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./arrayRemoveDuplicates-5b666c82","./EllipsoidRhumbLine-d5e7f3db"],(function(e,t,i,o,r,n,a,s,l,y,u,d,p,c,f,g,m,h,b,P,E,A,_,G,L,T,H){"use strict";const C=[],v=[];function O(e,t,o,r,u){const d=a.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,C);let c,m;g.PolygonPipeline.computeWindingOrder2D(d)===g.WindingOrder.CLOCKWISE&&(d.reverse(),t=t.slice().reverse());let h=t.length,b=0;if(r)for(c=new Float64Array(2*h*3),m=0;m<h;m++){const e=t[m],i=t[(m+1)%h];c[b++]=e.x,c[b++]=e.y,c[b++]=e.z,c[b++]=i.x,c[b++]=i.y,c[b++]=i.z}else{let r=0;if(u===i.ArcType.GEODESIC)for(m=0;m<h;m++)r+=f.PolygonGeometryLibrary.subdivideLineCount(t[m],t[(m+1)%h],o);else if(u===i.ArcType.RHUMB)for(m=0;m<h;m++)r+=f.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[m],t[(m+1)%h],o);for(c=new Float64Array(3*r),m=0;m<h;m++){let r;u===i.ArcType.GEODESIC?r=f.PolygonGeometryLibrary.subdivideLine(t[m],t[(m+1)%h],o,v):u===i.ArcType.RHUMB&&(r=f.PolygonGeometryLibrary.subdivideRhumbLine(e,t[m],t[(m+1)%h],o,v));const n=r.length;for(let e=0;e<n;++e)c[b++]=r[e]}}h=c.length/3;const P=2*h,E=p.IndexDatatype.createTypedArray(h,P);for(b=0,m=0;m<h-1;m++)E[b++]=m,E[b++]=m+1;return E[b++]=h-1,E[b++]=0,new y.GeometryInstance({geometry:new s.Geometry({attributes:new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:E,primitiveType:s.PrimitiveType.LINES})})}function x(e,t,o,r,u){const d=a.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,C);let c,m;g.PolygonPipeline.computeWindingOrder2D(d)===g.WindingOrder.CLOCKWISE&&(d.reverse(),t=t.slice().reverse());let h=t.length;const b=new Array(h);let P=0;if(r)for(c=new Float64Array(2*h*3*2),m=0;m<h;++m){b[m]=P/3;const e=t[m],i=t[(m+1)%h];c[P++]=e.x,c[P++]=e.y,c[P++]=e.z,c[P++]=i.x,c[P++]=i.y,c[P++]=i.z}else{let r=0;if(u===i.ArcType.GEODESIC)for(m=0;m<h;m++)r+=f.PolygonGeometryLibrary.subdivideLineCount(t[m],t[(m+1)%h],o);else if(u===i.ArcType.RHUMB)for(m=0;m<h;m++)r+=f.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[m],t[(m+1)%h],o);for(c=new Float64Array(3*r*2),m=0;m<h;++m){let r;b[m]=P/3,u===i.ArcType.GEODESIC?r=f.PolygonGeometryLibrary.subdivideLine(t[m],t[(m+1)%h],o,v):u===i.ArcType.RHUMB&&(r=f.PolygonGeometryLibrary.subdivideRhumbLine(e,t[m],t[(m+1)%h],o,v));const n=r.length;for(let e=0;e<n;++e)c[P++]=r[e]}}h=c.length/6;const E=b.length,A=2*(2*h+E),_=p.IndexDatatype.createTypedArray(h+E,A);for(P=0,m=0;m<h;++m)_[P++]=m,_[P++]=(m+1)%h,_[P++]=m+h,_[P++]=(m+1)%h+h;for(m=0;m<E;m++){const e=b[m];_[P++]=e,_[P++]=e+h}return new y.GeometryInstance({geometry:new s.Geometry({attributes:new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:_,primitiveType:s.PrimitiveType.LINES})})}function D(o){const n=o.polygonHierarchy,a=e.defaultValue(o.ellipsoid,t.Ellipsoid.WGS84),s=e.defaultValue(o.granularity,c.CesiumMath.RADIANS_PER_DEGREE),l=e.defaultValue(o.perPositionHeight,!1),y=l&&e.defined(o.extrudedHeight),u=e.defaultValue(o.arcType,i.ArcType.GEODESIC);let d=e.defaultValue(o.height,0),p=e.defaultValue(o.extrudedHeight,d);if(!y){const e=Math.max(d,p);p=Math.min(d,p),d=e}this._ellipsoid=t.Ellipsoid.clone(a),this._granularity=s,this._height=d,this._extrudedHeight=p,this._arcType=u,this._polygonHierarchy=n,this._perPositionHeight=l,this._perPositionHeightExtrude=y,this._offsetAttribute=o.offsetAttribute,this._workerName="createPolygonOutlineGeometry",this.packedLength=f.PolygonGeometryLibrary.computeHierarchyPackedLength(n,r.Cartesian3)+t.Ellipsoid.packedLength+8}D.pack=function(i,o,n){return n=e.defaultValue(n,0),n=f.PolygonGeometryLibrary.packPolygonHierarchy(i._polygonHierarchy,o,n,r.Cartesian3),t.Ellipsoid.pack(i._ellipsoid,o,n),n+=t.Ellipsoid.packedLength,o[n++]=i._height,o[n++]=i._extrudedHeight,o[n++]=i._granularity,o[n++]=i._perPositionHeightExtrude?1:0,o[n++]=i._perPositionHeight?1:0,o[n++]=i._arcType,o[n++]=e.defaultValue(i._offsetAttribute,-1),o[n]=i.packedLength,o};const I=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),w={polygonHierarchy:{}};return D.unpack=function(i,o,n){o=e.defaultValue(o,0);const a=f.PolygonGeometryLibrary.unpackPolygonHierarchy(i,o,r.Cartesian3);o=a.startingIndex,delete a.startingIndex;const s=t.Ellipsoid.unpack(i,o,I);o+=t.Ellipsoid.packedLength;const l=i[o++],y=i[o++],u=i[o++],d=1===i[o++],p=1===i[o++],c=i[o++],g=i[o++],m=i[o];return e.defined(n)||(n=new D(w)),n._polygonHierarchy=a,n._ellipsoid=t.Ellipsoid.clone(s,n._ellipsoid),n._height=l,n._extrudedHeight=y,n._granularity=u,n._perPositionHeight=p,n._perPositionHeightExtrude=d,n._arcType=c,n._offsetAttribute=-1===g?void 0:g,n.packedLength=m,n},D.fromPositions=function(t){return new D({polygonHierarchy:{positions:(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions},height:t.height,extrudedHeight:t.extrudedHeight,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,arcType:t.arcType,offsetAttribute:t.offsetAttribute})},D.createGeometry=function(t){const i=t._ellipsoid,r=t._granularity,a=t._polygonHierarchy,l=t._perPositionHeight,y=t._arcType,p=f.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(a,!l,i);if(0===p.length)return;let m;const h=[],b=c.CesiumMath.chordLength(r,i.maximumRadius),P=t._height,E=t._extrudedHeight;let A,_;if(t._perPositionHeightExtrude||!c.CesiumMath.equalsEpsilon(P,E,0,c.CesiumMath.EPSILON2))for(_=0;_<p.length;_++){if(m=x(i,p[_],b,l,y),m.geometry=f.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(m.geometry,P,E,i,l),e.defined(t._offsetAttribute)){const e=m.geometry.attributes.position.values.length/3;let i=new Uint8Array(e);t._offsetAttribute===u.GeometryOffsetAttribute.TOP?i=i.fill(1,0,e/2):(A=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,i=i.fill(A)),m.geometry.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}h.push(m)}else for(_=0;_<p.length;_++){if(m=O(i,p[_],b,l,y),m.geometry.attributes.position.values=g.PolygonPipeline.scaleToGeodeticHeight(m.geometry.attributes.position.values,P,i,!l),e.defined(t._offsetAttribute)){const e=m.geometry.attributes.position.values.length;A=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;const i=new Uint8Array(e/3).fill(A);m.geometry.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}h.push(m)}const G=d.GeometryPipeline.combineInstances(h)[0],L=o.BoundingSphere.fromVertices(G.attributes.position.values);return new s.Geometry({attributes:G.attributes,indices:G.indices,primitiveType:G.primitiveType,boundingSphere:L,offsetAttribute:t._offsetAttribute})},function(i,o){return e.defined(o)&&(i=D.unpack(i,o)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),D.createGeometry(i)}}));
