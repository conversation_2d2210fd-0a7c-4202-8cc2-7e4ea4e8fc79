import useCesiumStore from './modules/screenCesium.js'
import useMenuStore from './modules/menu.js'
import useScreenPopWindowStore from './modules/screenPopWindow.js'
import useContextMenuStore from './modules/contextMenu.js'
const useStore = () => ({
  storeCesium: useCesiumStore(),
  menu: useMenuStore(),
  storeScreenPopWindowData: useScreenPopWindowStore(),
  contextMenu:useContextMenuStore()
})
export default useStore
