/*
 * @Description:根据经纬度获取位置信息
 * @Autor: wangjialing
 * @Date: 2023-10-15 11:12:59
 * @LastEditors: wangjialing
 * @LastEditTime: 2023-11-06 14:38:24
 */
import { onMounted, onUnmounted, ref, watch } from 'vue'
export default function useGetWindowPosition(cesiumInstance, positions) {
  const left = ref(0)
  const top = ref(0)

  const getWindowPositionByWgs84 = function (data) {
    return Cesium.SceneTransforms.wgs84ToWindowCoordinates(cesiumInstance.viewer.scene, Cesium.Cartesian3.fromDegrees(data[0], data[1], 0))
  }

  onMounted(() => {})
  onUnmounted(() => {
    cesiumInstance.viewer.scene.postRender.removeEventListener(updateWheel)
  })
  // 订阅位置信息
  const updateWheel = () => {
    const p = getWindowPositionByWgs84(positions.value)
    if (p && getWindowPositionByWgs84(positions.value)) {
      left.value = p.x
      top.value = p.y
    }

    // state.popupLeft = p.x + (props.offset[0] || 0) + 'px'
    // state.popupTop = p.y + (props.offset[1] || 0) + 'px'
  }

  watch(
    positions,
    () => {
      if (cesiumInstance && getWindowPositionByWgs84(positions.value)) {
        cesiumInstance.viewer.scene.postRender.removeEventListener(updateWheel)
        left.value = getWindowPositionByWgs84(positions.value).x + 'px'
        top.value = getWindowPositionByWgs84(positions.value).y + 'px'
        cesiumInstance.viewer.scene.postRender.addEventListener(updateWheel)
      }
    },
    {
      immediate: true
    }
  )
  return { left, top }
}
