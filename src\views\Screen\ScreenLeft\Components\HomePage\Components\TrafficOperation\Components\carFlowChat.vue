<template>
  <div>
    <MyChart width="480px" height="130px" :option="option"></MyChart>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import * as echarts from 'echarts'

  import MyChart from '@Common/components/MyChart/index.vue'

  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    }
  })

  const option = ref({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '20',
      right: '10',
      bottom: '0',
      top: '30',
      containLabel: true
    },
    legend: {
      top: '-5',
      left: 'center',
      data: ['流量'],
      textStyle: {
        color: '#fff',
        fontSize: 16
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false, // 坐标轴两边留白
      data: [],
      axisLabel: {
        // interval: 0,
        textStyle: {
          color: '#9DD7FF',
          fontSize: 14
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '流量/辆',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#F7F7F7'
        },
        splitNumber: 5,
        axisLabel: {
          textStyle: {
            color: '#9DD7FF',
            fontSize: 14
          },
          formatter: `{value}`
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#2E4867',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '流量',
        type: 'line',
        smooth: true,
        itemStyle: {
          normal: {
            color: '#FDCB00',
            lineStyle: {
              color: '#FDCB00',
              width: 1
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(253, 203, 0, 0)'
                },
                {
                  offset: 1,
                  color: 'rgba(253, 203, 0, 0.55)'
                }
              ])
            }
          }
        },
        data: []
      }
    ]
  })

  watch(
    () => props.data,
    () => {
      if (props.data && props.data.length > 0) {
        option.value.xAxis.data = props.data.map((item) => item.hour + 'h')
        option.value.series[0].data = props.data.map((item) => (item.value === 0 ? null : item.value))
        console.log('option', option.value)
      }
    }
  )
</script>

<style lang="scss" scoped></style>
