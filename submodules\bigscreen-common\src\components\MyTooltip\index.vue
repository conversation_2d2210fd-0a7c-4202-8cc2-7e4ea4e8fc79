<template>
  <el-tooltip v-bind="$attrs" :disabled="disabledTooltip" :popper-class="popperClass">
    <div class="text-content" :style="contentStyle" ref="textContentRef">{{ $attrs.content }}</div>
  </el-tooltip>
</template>

<script setup>
  import { computed, ref } from 'vue'

  import { ElTooltip } from 'element-plus'

  defineOptions({
    name: 'MyTooltip',
    inheritAttrs: false
  })
  const props = defineProps({
    // 是否展示提示框
    showTooltip: {
      type: Boolean,
      default: false
    },
    // 是否溢出隐藏显示省略号
    showEllipsis: {
      type: Boolean,
      default: true
    },
    // 最大展示行数
    row: {
      type: Number,
      default: 1
    },
    // 为 Tooltip 的 popper 添加类名
    popperClass: {
      type: String,
      default: 'default-popper-class-143214032185091285'
    }
  })
  const textContentRef = ref(null)
  // 提示框是否禁用
  const disabledTooltip = computed(() => {
    // 如果需要直接展示提示框,直接返回false
    if (props.showTooltip) return false
    let bool = true
    // 获取文本元素
    const el = textContentRef.value || {}
    if (props.showEllipsis && props.row > 1 && el.scrollHeight > el.offsetHeight) {
      // 判断多行文本是否溢出
      bool = false
    } else if (props.showEllipsis && el.scrollWidth > el.offsetWidth) {
      // 判断单行文本是否溢出
      bool = false
    }
    return bool
  })
  const contentStyle = computed(() => {
    let styles = {}
    if (props.showEllipsis && props.row > 1) {
      styles = {
        display: '-webkit-box',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': props.row
      }
    } else if (props.showEllipsis) {
      styles = {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }
    }
    return styles
  })
</script>

<style lang="scss" scoped>
  .text-content {
    width: 100%;
    height: 100%;
  }
</style>
<style>
  .default-popper-class-143214032185091285 {
    max-width: 50%;
  }
</style>
