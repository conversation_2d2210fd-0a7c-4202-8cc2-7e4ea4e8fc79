/**
 * @description: 列表自动滚动指令
 * @param {Boolean} mouse 指令后面直接.mouse就可以加入鼠标移入暂停移出继续运动
 * @param {Boolean} speed 滚动速度
 */

const RAF = window.requestAnimationFrame
const CancelRAF = window.cancelAnimationFrame

// 滚动元素
const scrollElement = (el) => {
  const maxScrollTop = el.scrollHeight - el.clientHeight
  // 直接在缓存滚动高度上进行计算
  el.cacheScrollTop = el.cacheScrollTop + el.speed >= maxScrollTop ? 0 : el.cacheScrollTop + el.speed
  // 将缓存高度设置为当前滚动高度
  el.scrollTo({
    top: el.cacheScrollTop
  })
  // 执行下一帧
  el.animationId = RAF(scrollElement.bind(null, el))
}

// 鼠标移入暂停
const mouseEnterHandler = (el) => {
  if (el.animationId) {
    CancelRAF(el.animationId)
    el.animationId = undefined
    el.mouseInside = true // 这里判断鼠标是否已经在元素里面
  }
}

// 鼠标移出继续运行
const mouseLeaveHandler = (el) => {
  if (el.animationId) {
    CancelRAF(el.animationId)
    el.animationId = undefined
  }
  el.mouseInside = false
  el.animationId = RAF(scrollElement.bind(null, el))
}

// 处理用户的滚动事件
const elementScrollHandler = (el) => (el.cacheScrollTop = el.scrollTop)

// 移除事件监听
const removeEventListeners = (el, binding) => {
  if (el.animationId) {
    CancelRAF(el.animationId)
    el.animationId = undefined
  }
  el.removeEventListener('scroll', elementScrollHandler.bind(null, el))
  if (binding.modifiers.mouse) {
    el.removeEventListener('mouseenter', mouseEnterHandler.bind(null, el))
    el.removeEventListener('mouseleave', mouseLeaveHandler.bind(null, el))
  }
}

// 初始化自动滚动
const initAutoScroll = (el, binding) => {
  const maxScrollTop = el.scrollHeight - el.clientHeight
  // 无需滚动（这里 - 1因为scrollHeight会四舍五入）
  if (maxScrollTop - 1 <= 0) {
    return
  }
  removeEventListeners(el, binding)
  // 滚动速度
  el.speed = binding.value || 1
  el.cacheScrollTop = el.cacheScrollTop || 0

  // PS:因为我们使用 cacheScrollTop 来代替 el.scrollTop 处理滚动高度，所以这里需要同步一下用户滚动操作后的 scrollTop
  el.addEventListener('scroll', elementScrollHandler.bind(null, el))

  // 鼠标移入暂停移出继续运动
  if (binding.modifiers.mouse) {
    if (!el.mouseInside) {
      el.animationId = RAF(scrollElement.bind(null, el))
    }
    el.addEventListener('mouseenter', mouseEnterHandler.bind(null, el))
    el.addEventListener('mouseleave', mouseLeaveHandler.bind(null, el))
  } else {
    if (el.animationId) {
      CancelRAF(el.animationId)
      el.animationId = undefined
    }
    el.animationId = RAF(scrollElement.bind(null, el))
  }
}

const vAutoScroll = {
  mounted(el, binding) {
    initAutoScroll(el, binding)
  },
  updated(el, binding) {
    initAutoScroll(el, binding)
  },
  unmounted(el, binding) {
    removeEventListeners(el, binding)
  }
}

export default vAutoScroll
