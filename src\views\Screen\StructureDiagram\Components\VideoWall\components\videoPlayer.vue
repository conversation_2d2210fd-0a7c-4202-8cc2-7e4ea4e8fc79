<template>
  <div style="width: 100%" :style="{ height: props.height + 'px' }">
    <videoPlay v-if="url" type="m3u8" width="100%" :height="props.height + 'px'" :src="url" auto-play muted :control="false"></videoPlay>
    <div v-else class="w-1/1 h-1/1 flex items-center justify-center text-center text-gray-500 font-size-36">{{ props.name || '请拖入摄像头' }}</div>
  </div>
</template>

<script setup lang="ts">
  import { mediaApi } from '@/api/media'
  import { videoPlay } from 'vue3-video-play'

  const props = defineProps<{
    // width: number
    height: number
    code: string
    name: string
  }>()
  const url = ref('')
  let keepaliveTimer = null
  watch(
    () => props.code,
    async (newVal, oldVal) => {
      if (oldVal) {
        // await mediaApi.stop({ code: oldVal })
        url.value = ''
      }
      if (newVal) {
        const res = await mediaApi.start({ code: newVal })
        // 清除之前的定时器
        if (keepaliveTimer) {
          clearInterval(keepaliveTimer)
          keepaliveTimer = null
        }

        // 设置新的定时器，每30秒调用一次keepalive
        if (newVal) {
          keepaliveTimer = setInterval(async () => {
            try {
              await mediaApi.keepalive({ code: newVal })
            } catch (error) {
              console.error('keepalive error:', error)
            }
          }, 30000) // 30秒 = 30000毫秒
        }
        console.log('res', res)
        url.value = res.result
      }
    },
    {
      immediate: true
    }
  )

  onBeforeUnmount(() => {
    if (props.code) {
      // mediaApi.stop({ code: props.code })
      url.value = ''
    }
    if (keepaliveTimer) {
      clearInterval(keepaliveTimer)
      keepaliveTimer = null
    }
  })
</script>

<style scoped></style>
