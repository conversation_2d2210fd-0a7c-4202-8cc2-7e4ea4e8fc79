<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-07 09:57:09
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 14:28:27
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\index.vue
 * @Description:
 *
-->
<template>
  <div class="event-manage-container">
    <HeadLine>
      <template #title>事件管理</template>
      <div class="content">
        <EmergencyResources></EmergencyResources>
        <SuddenEvents></SuddenEvents>
      </div>
    </HeadLine>
  </div>
</template>

<script setup>
  import EmergencyResources from './Components/EmergencyResources/index.vue'
  import SuddenEvents from './Components/SuddenEvents/index.vue'
  import HeadLine from '@/components/HeadLine/index.vue'
  defineOptions({
    name: 'EventManage'
  })
</script>

<style lang="scss" scoped>
  .event-manage-container {
    .content {
      display: flex;
      justify-content: space-between;
      width: 1317px;
      height: 474px;
    }
  }
</style>
