<template>
  <div class="bim-engine-parent" id="player">
    <div id="BimEngineContainer"></div>
  </div>
</template>

<script setup lang="ts">
  import moment from 'moment'

  import { useBimEngine } from './helper'
  import EngineController from './index'
  import { getToken } from '@/utils/auth'
  import lib from '@/utils/lib'

  let viewer = null
  let _engineController: EngineController = null
  const { addRadarPoint, addBuildingPoint } = useBimEngine()
  onMounted(async () => {
    const BimEngine = window.BimEngine


    const { serverUrl, appId, secret, projectId, baseUrl } = window.config
    BimEngine.setBaseUrl(baseUrl)
    _engineController = new EngineController('BimEngineContainer', serverUrl, appId, secret, projectId)
    window._engineController = _engineController
    lib._engineController = _engineController
    await _engineController.openProj(projectId)
    _engineController.enableClickToFollow(true)

    addRadarPoint()
    addBuildingPoint()
  })

  defineExpose({
    viewer
  })
</script>

<style scoped lang="scss">
  .bim-engine-parent {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    // width: 1920px;
    // height: 1080px;
    width: $screen-width;
    height: $screen-height;
    #BimEngineContainer {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: block;
      width: 100%;
      height: 100%;
      font-family: sans-serif;
      font-size: 9pt;
      color: #eeeeee;
      border: none;
    }
  }
</style>
