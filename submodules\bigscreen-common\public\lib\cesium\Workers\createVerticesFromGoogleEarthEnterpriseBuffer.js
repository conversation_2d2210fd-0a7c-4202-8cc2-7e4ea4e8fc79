/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AxisAlignedBoundingBox-6489d16d","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./TerrainEncoding-389ca311","./Math-b5f4d889","./OrientedBoundingBox-e47c7a90","./RuntimeError-592f0d41","./WebMercatorProjection-ce967e48","./createTaskProcessorWorker","./Resource-41d99fe7","./combine-0bec9016","./AttributeCompression-d661357e","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./EllipsoidTangentPlane-c2c2ef6e","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(t,e,n,i,o,a,r,s,c,u,h,d,l,g,m,p,f,I,E){"use strict";const T=Uint16Array.BYTES_PER_ELEMENT,C=Int32Array.BYTES_PER_ELEMENT,M=Uint32Array.BYTES_PER_ELEMENT,x=Float32Array.BYTES_PER_ELEMENT,N=Float64Array.BYTES_PER_ELEMENT;function b(t,e,n){n=o.defaultValue(n,r.CesiumMath);const i=t.length;for(let o=0;o<i;++o)if(n.equalsEpsilon(t[o],e,r.CesiumMath.EPSILON12))return o;return-1}const S=new i.Cartographic,w=new n.Cartesian3,B=new n.Cartesian3,P=new n.Cartesian3,A=new i.Matrix4;function R(t,e,a,s,c,u,h,d,l,g,m){const p=d.length;for(let f=0;f<p;++f){const I=d[f],E=I.cartographic,T=I.index,C=t.length,M=E.longitude;let x=E.latitude;x=r.CesiumMath.clamp(x,-r.CesiumMath.PI_OVER_TWO,r.CesiumMath.PI_OVER_TWO);const N=E.height-h.skirtHeight;h.hMin=Math.min(h.hMin,N),i.Cartographic.fromRadians(M,x,N,S),g&&(S.longitude+=l),g?f===p-1?S.latitude+=m:0===f&&(S.latitude-=m):S.latitude+=l;const b=h.ellipsoid.cartographicToCartesian(S);t.push(b),e.push(N),a.push(n.Cartesian2.clone(a[T])),s.length>0&&s.push(s[T]),c.length>0&&c.push(c[T]),i.Matrix4.multiplyByPoint(h.toENU,b,w);const B=h.minimum,P=h.maximum;n.Cartesian3.minimumByComponent(w,B,B),n.Cartesian3.maximumByComponent(w,P,P);const A=h.lastBorderPoint;if(o.defined(A)){const t=A.index;u.push(t,C-1,C,C,T,t)}h.lastBorderPoint=I}}return h((function(h,d){h.ellipsoid=i.Ellipsoid.clone(h.ellipsoid),h.rectangle=i.Rectangle.clone(h.rectangle);const l=function(h,d,l,g,m,p,f,I,E,y,_){let W,v,F,O,Y,U;o.defined(g)?(W=g.west,v=g.south,F=g.east,O=g.north,Y=g.width,U=g.height):(W=r.CesiumMath.toRadians(m.west),v=r.CesiumMath.toRadians(m.south),F=r.CesiumMath.toRadians(m.east),O=r.CesiumMath.toRadians(m.north),Y=r.CesiumMath.toRadians(g.width),U=r.CesiumMath.toRadians(g.height));const V=[v,O],k=[W,F],H=e.Transforms.eastNorthUpToFixedFrame(d,l),L=i.Matrix4.inverseTransformation(H,A);let D,G;E&&(D=u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(v),G=1/(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(O)-D));const j=1!==p,z=new DataView(h);let q=Number.POSITIVE_INFINITY,J=Number.NEGATIVE_INFINITY;const K=B;K.x=Number.POSITIVE_INFINITY,K.y=Number.POSITIVE_INFINITY,K.z=Number.POSITIVE_INFINITY;const Q=P;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;let X,Z,$=0,tt=0,et=0;for(Z=0;Z<4;++Z){let t=$;X=z.getUint32(t,!0),t+=M;const e=r.CesiumMath.toRadians(180*z.getFloat64(t,!0));t+=N,-1===b(k,e)&&k.push(e);const n=r.CesiumMath.toRadians(180*z.getFloat64(t,!0));t+=N,-1===b(V,n)&&V.push(n),t+=2*N;let i=z.getInt32(t,!0);t+=C,tt+=i,i=z.getInt32(t,!0),et+=3*i,$+=X+M}const nt=[],it=[],ot=new Array(tt),at=new Array(tt),rt=new Array(tt),st=E?new Array(tt):[],ct=j?new Array(tt):[],ut=new Array(et),ht=[],dt=[],lt=[],gt=[];let mt=0,pt=0;for($=0,Z=0;Z<4;++Z){X=z.getUint32($,!0),$+=M;const t=$,e=r.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=N;const o=r.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=N;const a=r.CesiumMath.toRadians(180*z.getFloat64($,!0)),s=.5*a;$+=N;const h=r.CesiumMath.toRadians(180*z.getFloat64($,!0)),d=.5*h;$+=N;const g=z.getInt32($,!0);$+=C;const m=z.getInt32($,!0);$+=C,$+=C;const p=new Array(g);for(let t=0;t<g;++t){const c=e+z.getUint8($++)*a;S.longitude=c;const g=o+z.getUint8($++)*h;S.latitude=g;let m=z.getFloat32($,!0);if($+=x,0!==m&&m<_&&(m*=-Math.pow(2,y)),m*=6371010,S.height=m,-1!==b(k,c)||-1!==b(V,g)){const e=b(nt,S,i.Cartographic);if(-1!==e){p[t]=it[e];continue}nt.push(i.Cartographic.clone(S)),it.push(mt)}p[t]=mt,Math.abs(c-W)<s?ht.push({index:mt,cartographic:i.Cartographic.clone(S)}):Math.abs(c-F)<s?lt.push({index:mt,cartographic:i.Cartographic.clone(S)}):Math.abs(g-v)<d?dt.push({index:mt,cartographic:i.Cartographic.clone(S)}):Math.abs(g-O)<d&&gt.push({index:mt,cartographic:i.Cartographic.clone(S)}),q=Math.min(m,q),J=Math.max(m,J),rt[mt]=m;const f=l.cartographicToCartesian(S);if(ot[mt]=f,E&&(st[mt]=(u.WebMercatorProjection.geodeticLatitudeToMercatorAngle(g)-D)*G),j){const t=l.geodeticSurfaceNormal(f);ct[mt]=t}i.Matrix4.multiplyByPoint(L,f,w),n.Cartesian3.minimumByComponent(w,K,K),n.Cartesian3.maximumByComponent(w,Q,Q);let I=(c-W)/(F-W);I=r.CesiumMath.clamp(I,0,1);let T=(g-v)/(O-v);T=r.CesiumMath.clamp(T,0,1),at[mt]=new n.Cartesian2(I,T),++mt}const f=3*m;for(let t=0;t<f;++t,++pt)ut[pt]=p[z.getUint16($,!0)],$+=T;if(X!==$-t)throw new c.RuntimeError("Invalid terrain tile.")}ot.length=mt,at.length=mt,rt.length=mt,E&&(st.length=mt);j&&(ct.length=mt);const ft=mt,It=pt,Et={hMin:q,lastBorderPoint:void 0,skirtHeight:I,toENU:L,ellipsoid:l,minimum:K,maximum:Q};ht.sort((function(t,e){return e.cartographic.latitude-t.cartographic.latitude})),dt.sort((function(t,e){return t.cartographic.longitude-e.cartographic.longitude})),lt.sort((function(t,e){return t.cartographic.latitude-e.cartographic.latitude})),gt.sort((function(t,e){return e.cartographic.longitude-t.cartographic.longitude}));const Tt=1e-5;if(R(ot,rt,at,st,ct,ut,Et,ht,-Tt*Y,!0,-Tt*U),R(ot,rt,at,st,ct,ut,Et,dt,-Tt*U,!1),R(ot,rt,at,st,ct,ut,Et,lt,Tt*Y,!0,Tt*U),R(ot,rt,at,st,ct,ut,Et,gt,Tt*U,!1),ht.length>0&&gt.length>0){const t=ht[0].index,e=ft,n=gt[gt.length-1].index,i=ot.length-1;ut.push(n,i,e,e,t,n)}tt=ot.length;const Ct=e.BoundingSphere.fromPoints(ot);let Mt;o.defined(g)&&(Mt=s.OrientedBoundingBox.fromRectangle(g,q,J,l));const xt=new a.EllipsoidalOccluder(l).computeHorizonCullingPointPossiblyUnderEllipsoid(d,ot,q),Nt=new t.AxisAlignedBoundingBox(K,Q,d),bt=new a.TerrainEncoding(d,Nt,Et.hMin,J,H,!1,E,j,p,f),St=new Float32Array(tt*bt.stride);let wt=0;for(let t=0;t<tt;++t)wt=bt.encode(St,wt,ot[t],at[t],rt[t],void 0,st[t],ct[t]);const Bt=ht.map((function(t){return t.index})).reverse(),Pt=dt.map((function(t){return t.index})).reverse(),At=lt.map((function(t){return t.index})).reverse(),Rt=gt.map((function(t){return t.index})).reverse();return Pt.unshift(At[At.length-1]),Pt.push(Bt[0]),Rt.unshift(Bt[Bt.length-1]),Rt.push(At[0]),{vertices:St,indices:new Uint16Array(ut),maximumHeight:J,minimumHeight:q,encoding:bt,boundingSphere3D:Ct,orientedBoundingBox:Mt,occludeePointInScaledSpace:xt,vertexCountWithoutSkirts:ft,indexCountWithoutSkirts:It,westIndicesSouthToNorth:Bt,southIndicesEastToWest:Pt,eastIndicesNorthToSouth:At,northIndicesWestToEast:Rt}}(h.buffer,h.relativeToCenter,h.ellipsoid,h.rectangle,h.nativeRectangle,h.exaggeration,h.exaggerationRelativeHeight,h.skirtHeight,h.includeWebMercatorT,h.negativeAltitudeExponentBias,h.negativeElevationThreshold),g=l.vertices;d.push(g.buffer);const m=l.indices;return d.push(m.buffer),{vertices:g.buffer,indices:m.buffer,numberOfAttributes:l.encoding.stride,minimumHeight:l.minimumHeight,maximumHeight:l.maximumHeight,boundingSphere3D:l.boundingSphere3D,orientedBoundingBox:l.orientedBoundingBox,occludeePointInScaledSpace:l.occludeePointInScaledSpace,encoding:l.encoding,vertexCountWithoutSkirts:l.vertexCountWithoutSkirts,indexCountWithoutSkirts:l.indexCountWithoutSkirts,westIndicesSouthToNorth:l.westIndicesSouthToNorth,southIndicesEastToWest:l.southIndicesEastToWest,eastIndicesNorthToSouth:l.eastIndicesNorthToSouth,northIndicesWestToEast:l.northIndicesWestToEast}}))}));
