define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./CylinderGeometryLibrary-03a8620f","./CylinderGeometry-d51717b5"],(function(e,t,r,n,a,i,d,f,o,y,c,b,m,u,s,G,C,p){"use strict";function l(t,r){return e.defined(r)&&(t=p.CylinderGeometry.unpack(t,r)),p.CylinderGeometry.createGeometry(t)}return l}));