define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./AttributeCompression-a01059cd","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./WebMercatorProjection-ab2cf572","./createTaskProcessorWorker","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./OrientedBoundingBox-d0a49c02","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./CreatePhysicalArray-935f3db5","./materem-d090bcd4","./EmWrapperManager-1ae94128","./TerrainEncoding-308a828e","./TerrainProvider-16cd8dff","./EmLBDeal-04aa737f"],(function(e,t,r,n,i,a,o,d,s,l,u,m,h,c,g,I,f,p,y,T,N,M,b,x,C,v,P,A,E){"use strict";var w,B=32767,S=new r.Cartesian3,F=new r.Cartesian3,V=new r.Cartesian3,Y=new n.Cartographic,_=new r.Cartesian2,G=new r.Cartesian3,L=new o.Matrix4,O=new o.Matrix4;function W(t,d){var s=t.quantizedVertices,l=s.length/3;if(l<3)return{holeAllData:!0};var u,m=t.octEncodedNormals,g=t.westIndices.length+t.eastIndices.length+t.southIndices.length+t.northIndices.length-4,I=t.includeWebMercatorT,p=t.holeAry,T=t.planishBorderInfoAry,M=e.defined(p)&&p.length>0,b=e.defined(T)&&T.length>0,C=(t.tileInfo,t.needDownLoad),W=t.isNeedPhy,k=t.isUpsampling,j=n.Rectangle.clone(t.rectangle),H=j.west,R=j.south,U=j.east,q=j.north,X=n.Ellipsoid.clone(t.ellipsoid),Z=t.exaggeration,J=t.minimumHeight*Z,K=t.maximumHeight*Z,Q=t.relativeToCenter;u=t.isPlaneMode?o.Matrix4.fromTranslation(Q):a.Transforms.eastNorthUpToFixedFrame(Q,X);var $,ee,te=o.Matrix4.inverseTransformation(u,new o.Matrix4);I&&($=f.WebMercatorProjection.geodeticLatitudeToMercatorAngle(R),ee=1/(f.WebMercatorProjection.geodeticLatitudeToMercatorAngle(q)-$));var re,ne=s.subarray(0,l),ie=s.subarray(l,2*l),ae=s.subarray(2*l,3*l),oe=e.defined(m),de=new Array(l),se=new Array(l),le=new Array(l),ue=I?new Array(l):[];oe&&(re=[...m]);var me=[...t.indices],he=F;he.x=Number.POSITIVE_INFINITY,he.y=Number.POSITIVE_INFINITY,he.z=Number.POSITIVE_INFINITY;var ce=V;ce.x=Number.NEGATIVE_INFINITY,ce.y=Number.NEGATIVE_INFINITY,ce.z=Number.NEGATIVE_INFINITY;var ge,Ie=Number.POSITIVE_INFINITY,fe=Number.NEGATIVE_INFINITY,pe=Number.POSITIVE_INFINITY,ye=Number.NEGATIVE_INFINITY,Te=!1;if(t.isPlaneMode){var Ne=t.projectionString,Me=t.dCenX,be=t.dCenY,xe=t.dCenZ;ge=new E.EmLBDeal;var Ce=ge.init(Ne,new r.Cartesian3(Me,be,xe));Ce?b&&ge.setPlanishBorderInfoAry(T):(ge.destroy(),ge=void 0),Te=t.isDefaultTer}for(var ve=0;ve<l;++ve){var Pe=ne[ve],Ae=ie[ve],Ee=Pe/B,we=Ae/B,Be=r.CesiumMath.lerp(J,K,ae[ve]/B);Y.longitude=r.CesiumMath.lerp(H,U,Ee),Y.latitude=r.CesiumMath.lerp(R,q,we),Y.height=Be,Ie=Math.min(Y.longitude,Ie),fe=Math.max(Y.longitude,fe),pe=Math.min(Y.latitude,pe),ye=Math.max(Y.latitude,ye);var Se=X.cartographicToCartesian(Y);e.defined(ge)&&(Se=ge.computeCartesianToProj(Se),Te&&(Se.z=0)),de[ve]=new r.Cartesian2(Ee,we),se[ve]=Be,le[ve]=Se,I&&(ue[ve]=(f.WebMercatorProjection.geodeticLatitudeToMercatorAngle(Y.latitude)-$)*ee),o.Matrix4.multiplyByPoint(te,Se,S),r.Cartesian3.minimumByComponent(S,he,he),r.Cartesian3.maximumByComponent(S,ce,ce)}var Fe,Ve,Ye,_e=D(t.westIndices,(function(e,t){return de[e].y-de[t].y})),Ge=D(t.eastIndices,(function(e,t){return de[t].y-de[e].y})),Le=D(t.southIndices,(function(e,t){return de[t].x-de[e].x})),Oe=D(t.northIndices,(function(e,t){return de[e].x-de[t].x})),We=1e-4,ze=(fe-Ie)*We,De=(ye-pe)*We,ke=-ze,je=0,He=ze,Re=0,Ue=0,qe=De,Xe=0,Ze=-De,Je=le.length,Ke=me.length,Qe=J;if(!k){var $e=le.length;Qe=Math.min(Qe,z(_e,le,se,de,re,ue,X,j,t.westSkirtHeight,ke,je,ge));var et=le.length;Qe=Math.min(Qe,z(Le,le,se,de,re,ue,X,j,t.southSkirtHeight,Xe,Ze,ge));var tt=le.length;Qe=Math.min(Qe,z(Ge,le,se,de,re,ue,X,j,t.eastSkirtHeight,He,Re,ge));var rt=le.length;Qe=Math.min(Qe,z(Oe,le,se,de,re,ue,X,j,t.northSkirtHeight,Ue,qe,ge));for(var nt=3*Math.max(0,2*g),it=0;it<nt;it++)me.push(0);A.TerrainProvider.addSkirtIndices(_e,Le,Ge,Oe,$e,et,tt,rt,me,Ke)}if(1!==Z&&(Ve=i.BoundingSphere.fromPoints(le),Fe=N.OrientedBoundingBox.fromRectangle(j,J,K,X)),1!==Z||J<0){var at=new P.EllipsoidalOccluder(X);Ye=at.computeHorizonCullingPointPossiblyUnderEllipsoid(Q,le,J)}J=Qe;const ot=[];var dt=(e,t)=>{let r=new v.emMod.LBSpaSerial;r.WriteTriangle(t);let n=new Uint8Array(r.GetBufferSize());for(let i=0;i<n.length;++i)n[i]=r.GetBufferVal(i);v.emMod.destroy(r),ot.push({serial:n,fileName:e})},st=(e,t,r)=>{let n=0,i=new v.emMod.LBSpaTriangle;i.SetPtNum(e.length,!1,!1);for(let o=0;o<e.length;o++){const t=e[o];i.SetPtVal(o,t.x,t.y,t.z)}i.SetIndexNum(t.length);for(let o=0;o<t.length;o++)i.SetIndexVal(o,t[o]);C&&dt(r>0?"ter2.tri":"hole2.tri",i);let a=new v.emMod.LBSpaBody;return a.Init(i,n),v.emMod.destroy(i),a},lt=t=>{le.length=0,me.length=0,de.length=0,se.length=0,I&&(ue.length=0),oe&&(re.length=0,oe=!1);let i=new v.emMod.LBSpaTriangle,a=new v.emMod.LBSpaSkirtInfo;t.GetTriangle(i,a);let d=i.GetPtNum(),s=i.GetIndexNum();if(d<3||s<3)return v.emMod.destroy(i),void v.emMod.destroy(a);Je=d,Ke=s,he.x=Number.POSITIVE_INFINITY,he.y=Number.POSITIVE_INFINITY,he.z=Number.POSITIVE_INFINITY,ce.x=Number.NEGATIVE_INFINITY,ce.y=Number.NEGATIVE_INFINITY,ce.z=Number.NEGATIVE_INFINITY,J=Number.POSITIVE_INFINITY,K=Number.NEGATIVE_INFINITY;for(let l=0;l<d;l++){let t=i.GetPt(l),a=new r.Cartesian3(t.x,t.y,t.z);le.push(a),o.Matrix4.multiplyByPoint(te,a,S),r.Cartesian3.minimumByComponent(S,he,he),r.Cartesian3.maximumByComponent(S,ce,ce),e.defined(ge)?(ge.computeProjToDegree(a,!0,S),n.Cartographic.fromDegrees(S.x,S.y,S.z,Y)):X.cartesianToCartographic(a,Y),Y.longitude=r.CesiumMath.clamp(Y.longitude,H,U),Y.latitude=r.CesiumMath.clamp(Y.latitude,R,q);let d=new r.Cartesian2((Y.longitude-H)/(U-H),(Y.latitude-R)/(q-R));de.push(d),J=Math.min(Y.height,J),K=Math.max(Y.height,K),se.push(Y.height),I&&ue.push((f.WebMercatorProjection.geodeticLatitudeToMercatorAngle(Y.latitude)-$)*ee)}for(let e=0;e<s;e++)me.push(i.GetIndex(e));v.emMod.destroy(i),v.emMod.destroy(a)},ut=()=>{let e,t=!1;const r=y.AxisAlignedBoundingBox.fromPoints(le);for(let n=0;n<p.length;n++){const i=E.BorderInfo.clone(p[n]);let a=[],o=[];i.createTriangle(a,o);const d=y.AxisAlignedBoundingBox.fromPoints(a);if(!r.isOverlap(d))continue;void 0===e&&(e=st(le,me,Ke));let s=st(a,o);e.CheckReference(s)?e.ComputeDifference(s)?(v.emMod.destroy(s),t=!0):v.emMod.destroy(s):v.emMod.destroy(s)}return t&&lt(e),void 0!==e&&v.emMod.destroy(e),t};let mt=!1;if(M&&(mt=ut(),le.length<3))return{holeAllData:!0};for(var ht=new y.AxisAlignedBoundingBox(he,ce,Q),ct=new P.TerrainEncoding(ht,J,K,u,oe,I),gt=ct.getStride(),It=le.length*gt,ft=new Float32Array(It),pt=0,yt=0;yt<le.length;++yt){if(oe){var Tt=2*yt;if(_.x=re[Tt],_.y=re[Tt+1],1!==Z){var Nt=h.AttributeCompression.octDecode(_.x,_.y,G),Mt=a.Transforms.eastNorthUpToFixedFrame(le[yt],X,O),bt=o.Matrix4.inverseTransformation(Mt,L);o.Matrix4.multiplyByPointAsVector(bt,Nt,Nt),Nt.z*=Z,r.Cartesian3.normalize(Nt,Nt),o.Matrix4.multiplyByPointAsVector(Mt,Nt,Nt),r.Cartesian3.normalize(Nt,Nt),h.AttributeCompression.octEncode(Nt,_)}}pt=ct.encode(ft,pt,le[yt],de[yt],se[yt],_,ue[yt])}var xt,Ct=c.IndexDatatype.createTypedArray(le.length,me.length);return Ct.set(me,0),W?(xt=x.CreatePhysicalArray.createPhysicalArrayFromTerrain(v.emMod,w,t.relativeToCenter,le,me),d.push(ft.buffer,Ct.buffer,xt.buffer)):d.push(ft.buffer,Ct.buffer),e.defined(ge)&&ge.destroy(),{vertices:ft.buffer,indices:Ct.buffer,vertexStride:gt,center:Q,minimumHeight:J,maximumHeight:K,boundingSphere:Ve,orientedBoundingBox:Fe,occludeePointInScaledSpace:Ye,encoding:ct,indexCountWithoutSkirts:Ke,vertexCountWithoutSkirts:Je,vertexCount:le.length,physicalArray:xt,downloadAry:ot,haveDiff:mt}}function z(t,n,i,a,o,d,s,l,u,m,h,c){var g=e.defined(o),I=d.length>0,f=Number.POSITIVE_INFINITY,p=l.north,y=l.south,T=l.east,N=l.west;T<N&&(T+=r.CesiumMath.TWO_PI);for(var M=t.length-1,b=0;b<M;++b){var x=t[b],C=i[x],v=a[x];Y.longitude=r.CesiumMath.lerp(N,T,v.x)+m,Y.latitude=r.CesiumMath.lerp(y,p,v.y)+h,Y.height=C-u;var P=s.cartographicToCartesian(Y,S);if(e.defined(c)&&(P=c.computeCartesianToProj(P)),n.push(P),a.push(v),i.push(Y.height),g){var A=2*x;o.push(o[A]),o.push(o[A+1])}if(I){var E=d[x];d.push(E)}f=Math.min(f,Y.height)}return f}function D(t,r){var n;return"function"===typeof t.slice&&(n=t.slice(),"function"!==typeof n.sort&&(n=void 0)),e.defined(n)||(n=Array.prototype.slice.call(t)),n.sort(r),n}function k(t){var r=t.data,n=r.webAssemblyConfig;e.defined(n)&&v.EmWrapperManager.initWebAssembly(n.wasmBinaryFileES6).then((function(){w=new v.emMod.LBSpaMgr,self.onmessage=p(W),self.postMessage(!0)}))}return k}));