<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 09:50:08
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-22 16:00:36
 * @FilePath: \bigscreen-qj-web\src\components\TableList\index.vue
 * @Description:
 *
-->
<template>
  <div class="table-list-container">
    <div class="content" v-if="list.length > 0">
      <div class="content-item headline">
        <div v-for="(head, ind) in headList" :key="ind + 'head'" v-tooltip>{{ head.label }}</div>
      </div>
      <div class="list-data scroll-bar-style" :style="{ height: listDataHeight }">
        <div class="content-item" @click="handlerClick(item)" :class="index % 2 === 0 ? 'even' : 'odd'" v-for="(item, index) in list" :key="index">
          <div v-for="(it, index2) in headList" :key="index2 + 'content'">
            <!-- 时间转换 -->
            <div v-if="it.formatDate" v-tooltip>{{ formatDate(item[it.prop], it.formatDate) }}</div>
            <!-- 默认展示 -->
            <div v-else-if="it.dicp" v-tooltip>
              {{ getNameByCode(it.dicp, item[it.prop]) }}
            </div>
            <el-tooltip v-else :disabled="item[it.prop] && item[it.prop].length < 6" :content="item[it.prop]" placement="top">
              <div :style="{ color: it.alarm && item.state == '1' ? '#FF9900' : '' }">
                {{ item[it.prop] || '/' }}
                <!-- 单位 -->
                <div v-if="it.unit" style="display: inline">{{ it.unit }}</div>
                <!-- 备注弹出框 -->
                <div v-if="it.remark && item[it.remark]" style="display: inline">
                  <el-popover placement="top" popper-class="tip-box" effect="customized" width="200px" trigger="hover">
                    <template #reference>
                      <img class="icon" src="../../assets/ScreenLeft/DynamicMaintenance/remark.png" />
                    </template>
                    <div class="tip-box-content">{{ item[it.remark] }}</div>
                  </el-popover>
                </div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div class="no-data" v-else>暂无数据</div>
  </div>
</template>

<script setup>
  import moment from 'moment'

  import lib from '@/utils/lib'

  const props = defineProps({
    /**
     * @description: 表头数据
     */
    headList: {
      type: Array,
      default: () => []
    },
    /**
     * @description: 列表数据
     */
    list: {
      type: Array,
      default: () => []
    },
    /**
     * @description: 列表宽度
     */
    listWidth: {
      type: String,
      default: '617px'
    },
    /**
     * @description: 列表高度
     */
    listHeight: {
      type: String,
      default: '148px'
    },
    /**
     * @description: 列表数据高度
     */
    listDataHeight: {
      type: String,
      default: '108px'
    }
  })
  console.log('props-table', props.list)
  const formatDate = (val, formatDate) => {
    return moment(val).format(formatDate)
  }
  const dictMap = {
    // 作业单类型
    work_order_type: {
      defect: '维修作业单',
      maintenance: '养护作业单',
      inspection: '巡检作业单',
      block_road: '封道作业单',
      special: '专项作业单'
    }
  }
  const getNameByCode = (dicp, code) => {
    if (dicp && code) {
      // const dic = dictMap[dicp]
      // if (dic) {
      // return dic[code]
      return lib.store().storeDictionary.dictMap.find((_) => _.code === code).name
      // }
    }
  }
  const emit = defineEmits(['clickItem'])
  const handlerClick = (item) => {
    emit('clickItem', item)
  }
</script>

<style lang="scss" scoped>
  .table-list-container {
    width: v-bind(listwidth);
    height: v-bind(listheight);
    font-family: 'Alibaba PuHuiTi';
    font-size: 16px;
    font-weight: 400;
    line-height: 36px;
    color: #dbefff;
    .content {
      .list-data {
        overflow: auto;
      }
      .content-item {
        display: flex;
        margin-bottom: 4px;
        &.headline {
          width: 100%;
          height: 36px;
          margin-top: 7px;
          margin-bottom: 4px;
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          font-weight: bold;
          line-height: 36px;
          color: #bfd9ff;
          background: linear-gradient(90deg, rgb(0 40 85 / 73%) 0%, rgb(4 61 117 / 94%) 53%, rgb(0 79 129 / 56%) 100%);
          border-radius: 7px;
        }
        &.odd {
          width: 100%;
          height: 36px;
          cursor: pointer;
          background: linear-gradient(90deg, rgb(13 58 110 / 40%) 0%, #0d366e 50%, rgb(13 58 110 / 42%) 100%);
          border-radius: 7px;
        }
        &.even {
          width: 100%;
          height: 36px;
          cursor: pointer;
          background: linear-gradient(90deg, rgb(0 40 85 / 40%) 0%, rgb(65 141 255 / 30%) 51%, rgb(0 79 129 / 40%) 100%);
          border-radius: 7px;
        }
        div {
          flex: 1;
          overflow: hidden;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .icon {
      width: 12px;
      height: 12px;
      margin-left: 5px;
    }
    .no-data {
      width: v-bind(listwidth);
      height: v-bind(listheight);
      line-height: v-bind(listheight);
      color: #ffffff;
      text-align: center;
    }
  }
</style>
<style>
  .tip-box {
    .tip-box-content {
      padding: 10px;
    }
  }
</style>
