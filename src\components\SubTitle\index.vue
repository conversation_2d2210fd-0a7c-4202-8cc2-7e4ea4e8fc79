<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-23 11:10:31
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-25 10:14:16
 * @FilePath: \bigscreen-qj-web\src\components\SubTitle\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="sub-headline-container" :class="{ active: activeTab === 1 }">
    <div class="tabs">
      <!-- 循环渲染 tabs 按钮 -->
      <div
        class="tab-item"
        v-for="(tab, index) in tabs"
        :key="index"
        :class="{ active: activeTab === index }"
        @click="handleTabClick(index)"
        v-show-ai-question>
        {{ tab }}
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
  import { ref, defineProps, defineEmits } from 'vue'
  import lib from '@/utils/lib'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'

  const props = defineProps({
    tabs: {
      type: Array,
      default: () => ['作业管控', '缺陷管理']
    }
  })
  const emits = defineEmits(['tab-change'])
  const activeTab = ref(0)
  const handleTabClick = (tab) => {
    if (showAIQuestion.value) {
      // const question = '请介绍下' + props.tabs[tab] + '模块'
      lib.utils.sendQuestionToAIChat(props.tabs[tab])
    } else {
      activeTab.value = tab
      emits('tab-change', tab)
    }
  }
  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
</script>

<style lang="scss" scoped>
  .sub-headline-container {
    position: relative;
    width: 410px;
    height: 59px;
    padding-left: 12px;
    font-family: YouSheBiaoTiHei;
    font-size: 32px;
    font-weight: 400;
    line-height: 45px;
    color: #ffffff;
    background: url('@/assets/subTitle.png') no-repeat;
    &.active {
      background: url('@/assets/subTitle2.png') no-repeat;
    }
    .tabs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      padding: 0 40px;
      .tab-item {
        width: 123px;
        font-family: 'Source Han Sans CN';
        font-size: 26px;
        font-weight: 400;
        color: #a4d1ff;
        cursor: pointer;
        &.active {
          font-family: YouSheBiaoTiHei;
          font-size: 32px;
          font-weight: 400;
          color: #d4e9ff;
        }
      }
    }
  }
</style>
