/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./createTaskProcessorWorker","./Resource-41d99fe7","./defined-3b3eb2ba","./combine-0bec9016","./Math-b5f4d889","./RuntimeError-592f0d41"],(function(e,r,n,t,c,i){"use strict";return e((function(e,n){const t=e.url;return r.Resource.fetchBlob(t).then((function(e){return createImageBitmap(e,{imageOrientation:"flipY"})})).then((function(e){return n.push(e),e}))}))}));
