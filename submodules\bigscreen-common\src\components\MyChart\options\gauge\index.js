import * as echarts from 'echarts'

export const gauge1 = {
  angleAxis: {
    show: false,
    max: (100 * 360) / 180, // -45度到225度，二者偏移值是270度除360度
    type: 'value',
    startAngle: 180, // 极坐标初始角度
    splitLine: {
      show: false
    }
  },
  barMaxWidth: 10, // 圆环宽度
  radiusAxis: {
    show: false,
    type: 'category'
  },
  // 圆环位置和大小
  polar: {
    center: ['50%', '50%'],
    radius: '120'
  },
  series: [
    {
      type: 'bar',
      data: [
        {
          // 上层圆环，显示数据
          value: 92.1,
          itemStyle: {
            color: {
              // 图形渐变颜色方法，四个数字分别代表，右，下，左，上，offset表示0%到100%
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1, // 从左到右 0-1
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: '#06427A'
                },
                {
                  offset: 0.3,
                  color: '#0483B3'
                },
                {
                  offset: 0.5,
                  color: '#02B1DC'
                },
                {
                  offset: 1,
                  color: '#08D7FD'
                }
              ]
            }
          }
        }
      ],
      barGap: '100%', // 柱间距离,上下两层圆环重合
      coordinateSystem: 'polar',
      roundCap: true, // 顶端圆角从 v4.5.0 开始支持
      z: 2 // 圆环层级，同zindex
    },
    {
      // 下层圆环，显示最大值
      type: 'bar',
      data: [
        {
          value: 100,
          itemStyle: {
            color: '#0055A3'
          }
        }
      ],
      barGap: '-90%',
      coordinateSystem: 'polar',
      roundCap: true,
      z: 1
    },
    // 仪表盘
    {
      type: 'gauge',
      startAngle: 225, // 起始角度，同极坐标
      endAngle: -45, // 终止角度，同极坐标
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLabel: {
        show: false
      },
      pointer: {
        show: false
      },
      data: [
        {
          value: 92.1,
          name: '申嘉湖高速嘉兴段',
          title: {
            offsetCenter: [0, '20%'],
            color: '#FFFFFF',
            fontSize: 16,
            fontFamily: 'Alibaba-PuHuiTi-M, Alibaba-PuHuiTi'
          },
          detail: {
            formatter: function (value) {
              return `{a|${value}}` // 使用富文本标签设置样式
            },
            rich: {
              a: {
                color: '#fff',
                padding: [0, 0, 140, 0], // 设置上、右、下、左的内边距
                fontSize: 32,
                fontFamily: 'YouSheBiaoTiHei'
              }
            }
          }
        }
      ]
    },
    // 添加圆形的配置
    {
      type: 'scatter',
      coordinateSystem: 'polar',
      symbol: 'circle',
      symbolSize: 15,
      itemStyle: {
        color: '#6FEEFF',
        shadowColor: '#FFFFFF',
        shadowBlur: 10
      },
      data: [
        {
          value: 92.1,
          symbolOffset: [0, 0]
        }
      ]
    }
  ],
  animationDuration: 5000
}

