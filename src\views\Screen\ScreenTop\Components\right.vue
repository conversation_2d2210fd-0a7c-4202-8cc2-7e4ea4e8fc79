<template>
  <div class="right-container">
    <el-badge :value="noticeCount" :show-zero="false" class="w42 h51 absolute right-1430 cursor-pointer" @click="handleNoticeClick">
      <img src="@/assets/ScreenTop/notice.png" />
    </el-badge>
    <div class="btn-container" :class="{ selected: isSelected }" @click="handleBtnClick">大事件</div>
    <div class="traffic-info-container">
      <div class="traffic-info-item" v-for="(item, index) in trafficInfoList" :key="index">
        <div class="left">{{ item.name }}</div>
        <div class="icon"><img src="@/assets/ScreenTop/icon/traffic2.png" /></div>
        <div class="right">
          <div class="label">{{ item.title }}</div>
          <div :class="['value', item.value]">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="date-container">{{ date }}</div>
    <!-- <milestone v-if="isSelected"></milestone> -->
  </div>
</template>

<script setup lang="ts">
  import { useDateFormat, useNow } from '@vueuse/core'
  import { useIntervalFn } from '@vueuse/core'

  import lib from '@/utils/lib'
  import Notice from './Components/notice.vue'
  import { usePopWindow, type PopWindowProps,closePopWindowByTag } from 'znyg-frontend-common'
  const { showPopWindow } = usePopWindow()

  const { appContext } = getCurrentInstance()

  const date = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm:ss')
  const trafficInfoList = ref([
    { id: 1, name: '东线', value: '通畅', title: '拥堵状态' },
    { id: 2, name: '西线', value: '通畅', title: '拥堵状态' }
  ])

  const isSelected = ref(false)
  const handleBtnClick = () => {
    isSelected.value = !isSelected.value
    if (isSelected.value) {
      lib.popWindow.createPopWindow('../ScreenTop/Components/milestone.vue', {
        left: 420,
        top: 280,
        tag: 'milestone',
        appContext,
        closeFunc: () => {
          isSelected.value = false
        }
      })
    } else {
      lib.popWindow.removeDialog('milestone')
    }
  }

  const getData = () => {
    lib.api.bigscreenApi.congestion({}).then((response) => {
      if (response.success) {
        trafficInfoList.value = response.result
      }
    })
  }

  const noticeCount = ref(0)
  const handleNoticeClick = () => {
    closePopWindowByTag('notice')
    const op: PopWindowProps = {
      top: 300,
      width: '1815px',
      height: '838px',
      appendParent: 'player',
      style: {
        left: '50%',
        transform: 'translateX(-50%)'
      },
      tag: 'notice',
      zIndex: 999,
      draggable: true,
      draggableClass:'popup-title'
    }
    showPopWindow(op, Notice,)
  }

  onMounted(() => {
    // getData()
    useIntervalFn(
      () => {
        getData()
      },
      1000 * 60,
      { immediateCallback: true }
    )

    lib.api.UpgradeMessageApi.list({ currPage: 1, pageSize: 1000 }).then((res) => {
      if (res.success) {
        noticeCount.value = res.result.total
      }
    })
  })
</script>

<style lang="scss" scoped>
  .right-container {
    position: absolute;
    top: 18px;
    right: 0;
    display: flex;
    justify-content: space-between;
    width: 1700px;
    height: 50px;
    line-height: 50px;
    .date-container {
      position: absolute;
      right: 40px;
      width: 297px;
      height: 56px;
      font-family: 'Source Han Sans CN', 'Source Han Sans CN';
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 56px;
      color: #ffffff;

      // letter-spacing: 60px;
      text-align: left;
      text-transform: none;
    }
    .btn-container {
      position: absolute;

      // top: 6px;
      right: 1196px;
      width: 216px;
      height: 55px;
      font-family: YouSheBiaoTiHei;
      font-size: 31px;
      font-style: normal;
      font-weight: 400;
      line-height: 55px;
      color: #ffffff;
      text-align: center;
      cursor: pointer;
      background: url('@/assets/ScreenTop/icon/btnDSJ2.png') no-repeat;
      background-position: 9px 5px;
      background-size: 195px 44px;
      &.selected {
        background: url('@/assets/ScreenTop/icon/btnDSJ2Selected.png');
        background-size: 100% 100%;
      }
    }
    .traffic-info-container {
      position: absolute;
      right: 396px;
      display: flex;
      justify-content: space-between;

      // top: 18px;
      width: 714px;
      .traffic-info-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 325px;
        height: 56px;
        background: url('@/assets/ScreenTop/trafficInfoItem.png') no-repeat;
        background-size: 100% 100%;

        // padding: 0 10px;
        .left {
          width: 48px;
          height: 24px;
          font-family: 'Source Han Sans CN', 'Source Han Sans CN';
          font-size: 24px;
          font-weight: 400;
          line-height: 24px;
          color: #a9dcfe;
        }
        .icon {
          width: 59px;
          height: 51px;
        }
        .right {
          display: flex;
          flex-direction: row;
          align-items: center;
          width: 177px;
          height: 40px;
          .label {
            width: 120px;
            height: 24px;
            font-size: 24px;
            font-weight: 400;
            line-height: 24px;
            color: #ffffff;
          }
          .value {
            width: 58px;
            height: 40px;
            font-family: YouSheBiaoTiHei;
            font-size: 31px;
            font-weight: 400;
            line-height: 40px;

            // text-shadow: 0 4px 4px rgb(0 0 0 / 25%);
            &.通畅 {
              color: #5affcc;
            }
            &.拥堵 {
              color: #fe9647;
            }
          }
        }
      }
    }
  }
</style>
