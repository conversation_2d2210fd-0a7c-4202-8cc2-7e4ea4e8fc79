<!-- 养护运营&节点任务弹窗列表页 -->
<template>
  <div class="work-list-container">
    <PopupBg :title="props.data.title" width="800px" height="490px" @close="handlerClose">
      <tableList list-width="780px" list-data-height="360px" :head-list="tableHead" :list="tableData" @click-item="tableClick($event)"></tableList>
    </PopupBg>
  </div>
</template>

<script setup>
  import { onUnmounted, ref } from 'vue'
  import PopupBg from '@/components/PopupBg/index.vue'
  import tableList from '@/components/TableList/index.vue'

  import { toUe5 } from '@/hooks/useUE/tools.js'
  const { appContext } = getCurrentInstance()

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    data: {
      type: String,
      default: '作业列表'
    }
  })
  const emit = defineEmits(['close'])
  const handlerClose = () => {
    lib.popWindow.removeDialog('maintenanceWindow')
    lib.popWindow.removeDialog('MissionList')
    emit('close')
  }
  const tableHead = ref([])
  const tableData = ref([])
  const typeId = lib.typeIdMap.动态养护
  watch(
    () => props.data,
    () => {
      if (props.data.title === '养护作业列表') {
        if (props.data.item.name === '应完成总量') {
          tableHead.value = [
            { label: '作业内容', prop: 'content' },
            { label: '作业要求', prop: 'requirement' },
            { label: '作业类型', prop: 'workOrderTypeNName' },
            { label: '考核频次', prop: 'frequencyName' },
            { label: '应完成量', prop: 'shouldFinishCount' }
          ]
        } else {
          tableHead.value = [
            { label: '作业名称', prop: 'name' },
            { label: '作业类型', prop: 'typeName' },
            { label: '作业时间', prop: 'startDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
            { label: '当前状态', prop: 'statusName' }
          ]
        }
      } else {
        tableHead.value = [
          { label: '时间', prop: 'taskLimitDate', formatDate: 'YYYY-MM' },
          { label: '任务清单名称', prop: 'name' },
          { label: '任务类型', prop: 'typeName' },
          { label: '任务清单要求', prop: 'requirement' },
          { label: '状态', prop: 'statusName' }
        ]
      }
    },
    {
      immediate: true
    }
  )
  watch(
    () => props.data.list,
    () => {
      if (props.data.list.length) {
        props.data.list.forEach((data) => {
          if (props.data.title === '养护作业列表') {
            if (props.data.item.name !== '应完成总量') {
              data.statusName = lib.store().storeDictionary.dictMap.find((_) => _.code === data.status)?.name
              data.typeName = lib.store().storeDictionary.dictMap.find((_) => _.code === data.type)?.name
            }
          }
        })
      }
      tableData.value = props.data.list
    },
    {
      deep: true,
      immediate: true
    }
  )
  const isSelectedItem = ref(false)
  lib.bus.busTreeTableStructure.on((type) => {
    if (type === 'clear') {
      tableData.value.forEach((_) => {
        _.isOn = false
      })
    }
  })
  const tableClick = (item) => {
    console.log('列表点击-----', item)
    if(props.data.item.name === '应完成总量'){
      return
    }
    lib.popWindow.removeDialog('maintenanceWindow')
    item.isOn = !item.isOn
    isSelectedItem.value = true

    lib._engineController.clearCzml()
    // 养护运营有撒点及弹窗 | 节点任务只有列表
    if (props.data.title == '养护作业列表') {
      // 缺陷列表有里程号
      // if (!item.position) {
      //   item.mileage = 'EK53+825'
      //   lib._engineController.clearSelect()

      //   // ! 没有里程号，直接弹窗
      //   lib.popWindow.createPopWindow(
      //     './Components/MaintenanceDetails/index.vue',
      //     {
      //       left: 2000,
      //       top: 200,
      //       tag: 'maintenanceWindow',
      //       appContext,
      //       appendParent: 'player'
      //     },
      //     item
      //   )
      //   return
      // } else {
      //   if (!item.mileage) {
      //     item.mileage = item.position
      //   }
      // }

      if (!item.mileage && !item.position) {
        item.mileage = 'EK53+825'
      }
      lib._engineController.clearSelect()

      // ! 没有里程号，直接弹窗
      lib.popWindow.createPopWindow(
        './Components/MaintenanceDetails/index.vue',
        {
          left: 2000,
          top: 200,
          tag: 'maintenanceWindow',
          appContext,
          appendParent: 'player'
        },
        {
          ...item,
          subTitle: props.data.title
        }
      )
      return
    } else {
      return
    }
    lib.api.getPointCode
      .getModeCodeFromMileage({
        mileage: item.mileage,
        structureType: 4 // 4代表路面，默认查4
      })
      .then(async (res) => {
        if (res.success) {
          item.modelCode = res.result.code

          const bimInfo = await lib._engineController.getBimInfoByCode(item.modelCode)
          if (bimInfo) {
            const icon = await lib.utils.convertImageToBase64(`images/${props.data.title == '养护作业列表' ? 'workIcon' : 'defectIcon'}.png`)
            const cartesian = bimInfo.position

            const czml = {
              id: lib.utils.getRandomString(10),
              name: lib.utils.getRandomString(5),
              billboard: {
                // 图片
                image: icon,
                scale: 0.5,
                disableDepthTestDistance: 999999,
                horizontalOrigin: 'CENTER',
                verticalOrigin: 'BOTTOM'
              },
              position: {
                cartesian: [0, 0, 0]
              },
              onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
                const componentPaths = {
                  养护作业列表: './Components/MaintenanceDetails/index.vue'
                }
                lib.popWindow.createPopWindow(
                  componentPaths[props.data.title],
                  {
                    left: clickClinetX,
                    top: clickClinetY,
                    tag: 'maintenanceWindow',
                    appContext,
                    appendParent: 'player',
                    zIndex: 999,
                    closeFunc: () => {}
                  },
                  {
                    ...item,
                    subTitle: props.data.title
                  }
                )
              }
            }
            lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
            lib._engineController.flyToBimId(bimInfo.bimId)
          }
        }
      })
  }
  onUnmounted(() => {
    isSelectedItem.value = false
    toUe5('customPOIList', {
      isOn: false, // 是否开启标签
      typeId: typeId // 设施设备类型ID（为0时指全部类型）
    })
    lib.popWindow.removeDialog('maintenanceWindow')
  })
</script>

<style lang="scss" scoped>
  .work-list-container {
    width: 435px;
    height: 280px;
    background: url('@/assets/CommonPopup/popupBg.png');
    background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 14px 0 45px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .content {
      width: 407px;
      height: 200px;
      margin: 13px 21px 0 17px;
    }
  }
</style>
