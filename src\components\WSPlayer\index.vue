<template>
  <div>
    <!-- <video id="videoPlayer" ref="" autoplay style="width: 1000px; height: 800px"></video> -->
    <canvas id="video" ref="videoCanvas" :width="width" :height="height"></canvas>
  </div>
</template>

<script setup>
  import { watchEffect } from 'vue'

  const props = defineProps({
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '400px'
    },
    url: {
      type: String,
      // default: 'wss://*************/stream/?url=rtsp://admin:xm123456@*************:554/h265/ch1/sub/av_stream&width=704&height=396&bitrate=800'
      default: ''
    }
  })
  const videoCanvas = ref(null)
  let player = null
  watchEffect(
    () => {
      if (player) {
        player.destroy()
        player = null
      }
      if (props.url) {
        player = new JSMpeg.Player(props.url, {
          canvas: videoCanvas.value,
          disableGl: true
        })
      }
    },
    {
      flush: 'post'
    }
  )

  const destoryPlayer = () => {
    if (player) {
      player.destroy()
      player = null
    }
  }
  defineExpose({
    destoryPlayer
  })
</script>

<style lang="scss" scoped>
  #video {
    width: v-bind('width');
    height: v-bind('height');
  }
</style>
