<template>
  <div class="radar-video-container">
    <div class="radar-video-title">
      视频监控
      <img class="absolute right-20 top-16 cursor-pointer" src="@/assets/CommonPopup/closeIcon.png" @click="handleClose" />
    </div>
    <div class="radar-video-content">
      <videoPlay type="m3u8" width="698px" height="408px" :src="props.url" auto-play muted :control="false"></videoPlay>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { videoPlay } from 'vue3-video-play'

  const props = defineProps<{
    data: any
    url?: string
    handleClose?: () => void
  }>()

  const handleClose = () => {
    props.handleClose?.()
  }
</script>

<style scoped>
  .radar-video-container {
    position: absolute;
    width: 731px;
    height: 503px;
    padding: 10px 15px 20px 20px;
    background: url('@/assets/CommonPopup/radarBg.png');
    background-size: cover;
    .radar-video-title {
      position: relative;
      width: 100%;
      height: 32px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      line-height: 32px;
      color: #ffffff;
    }
    .radar-video-content {
      width: 698px;
      height: 408px;
      margin-top: 20px;
      overflow: hidden;
      border: 1px solid #30a2ff;
    }
  }
</style>
