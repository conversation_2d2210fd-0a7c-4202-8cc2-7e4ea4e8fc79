/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["require","exports","./defined-3b3eb2ba","./combine-0bec9016","./Math-b5f4d889","./RuntimeError-592f0d41"],(function(e,t,r,n,o,i){"use strict";function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a,u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},c={exports:{}},l={exports:{}};var p,h={exports:{}};
/*!
	 * URI.js - Mutating URLs
	 * IPv6 Support
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */var f,d,m,g,v={exports:{}};
/*!
	 * URI.js - Mutating URLs
	 * Second Level Domain (SLD) Support
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */m=u,g=function(e,t,r,n){var o=n&&n.URI;function i(e,t){var r=arguments.length>=1;if(!(this instanceof i))return r?arguments.length>=2?new i(e,t):new i(e):new i;if(void 0===e){if(r)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&r)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}i.version="1.19.11";var s=i.prototype,a=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function c(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function l(e){return"Array"===c(e)}function p(e,t){var r,n,o={};if("RegExp"===c(t))o=null;else if(l(t))for(r=0,n=t.length;r<n;r++)o[t[r]]=!0;else o[t]=!0;for(r=0,n=e.length;r<n;r++)(o&&void 0!==o[e[r]]||!o&&t.test(e[r]))&&(e.splice(r,1),n--,r--);return e}function h(e,t){var r,n;if(l(t)){for(r=0,n=t.length;r<n;r++)if(!h(e,t[r]))return!1;return!0}var o=c(t);for(r=0,n=e.length;r<n;r++)if("RegExp"===o){if("string"==typeof e[r]&&e[r].match(t))return!0}else if(e[r]===t)return!0;return!1}function f(e,t){if(!l(e)||!l(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function d(e){return e.replace(/^\/+|\/+$/g,"")}function m(e){return escape(e)}function g(e){return encodeURIComponent(e).replace(/[!'()*]/g,m).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return i.domAttributes[t]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(e,t){var r=i.encode(e+"");return void 0===t&&(t=i.escapeQuerySpace),t?r.replace(/%20/g,"+"):r},i.decodeQuery=function(e,t){e+="",void 0===t&&(t=i.escapeQuerySpace);try{return i.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var v,y={encode:"encode",decode:"decode"},b=function(e,t){return function(r){try{return i[t](r+"").replace(i.characters[e][t].expression,(function(r){return i.characters[e][t].map[r]}))}catch(e){return r}}};for(v in y)i[v+"PathSegment"]=b("pathname",y[v]),i[v+"UrnPathSegment"]=b("urnpath",y[v]);var _=function(e,t,r){return function(n){var o;o=r?function(e){return i[t](i[r](e))}:i[t];for(var s=(n+"").split(e),a=0,u=s.length;a<u;a++)s[a]=o(s[a]);return s.join(e)}};function w(e){return function(t,r){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!r),this)}}function A(e,t){return function(r,n){return void 0===r?this._parts[e]||"":(null!==r&&(r+="").charAt(0)===t&&(r=r.substring(1)),this._parts[e]=r,this.build(!n),this)}}i.decodePath=_("/","decodePathSegment"),i.decodeUrnPath=_(":","decodeUrnPathSegment"),i.recodePath=_("/","encodePathSegment","decode"),i.recodeUrnPath=_(":","encodeUrnPathSegment","decode"),i.encodeReserved=b("reserved","encode"),i.parse=function(e,t){var r;return t||(t={preventInvalidHostname:i.preventInvalidHostname}),(r=(e=(e=e.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(t.fragment=e.substring(r+1)||null,e=e.substring(0,r)),(r=e.indexOf("?"))>-1&&(t.query=e.substring(r+1)||null,e=e.substring(0,r)),"//"===(e=(e=e.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(t.protocol=null,e=e.substring(2),e=i.parseAuthority(e,t)):(r=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,r)||null,t.protocol&&!t.protocol.match(i.protocol_expression)?t.protocol=void 0:"//"===e.substring(r+1,r+3).replace(/\\/g,"/")?(e=e.substring(r+3),e=i.parseAuthority(e,t)):(e=e.substring(r+1),t.urn=!0)),t.path=e,t},i.parseHost=function(e,t){e||(e="");var r,n,o=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))r=e.indexOf("]"),t.hostname=e.substring(1,r)||null,t.port=e.substring(r+2,o)||null,"/"===t.port&&(t.port=null);else{var s=e.indexOf(":"),a=e.indexOf("/"),u=e.indexOf(":",s+1);-1!==u&&(-1===a||u<a)?(t.hostname=e.substring(0,o)||null,t.port=null):(n=e.substring(0,o).split(":"),t.hostname=n[0]||null,t.port=n[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),t.preventInvalidHostname&&i.ensureValidHostname(t.hostname,t.protocol),t.port&&i.ensureValidPort(t.port),e.substring(o)||"/"},i.parseAuthority=function(e,t){return e=i.parseUserinfo(e,t),i.parseHost(e,t)},i.parseUserinfo=function(e,t){var r=e;-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/"));var n,o=e.indexOf("/"),s=e.lastIndexOf("@",o>-1?o:e.length-1);return s>-1&&(-1===o||s<o)?(n=e.substring(0,s).split(":"),t.username=n[0]?i.decode(n[0]):null,n.shift(),t.password=n[0]?i.decode(n.join(":")):null,e=r.substring(s+1)):(t.username=null,t.password=null),e},i.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,o,s={},u=e.split("&"),c=u.length,l=0;l<c;l++)r=u[l].split("="),n=i.decodeQuery(r.shift(),t),o=r.length?i.decodeQuery(r.join("="),t):null,"__proto__"!==n&&(a.call(s,n)?("string"!=typeof s[n]&&null!==s[n]||(s[n]=[s[n]]),s[n].push(o)):s[n]=o);return s},i.build=function(e){var t="",r=!1;return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//",r=!0),t+=i.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&r&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},i.buildHost=function(e){var t="";return e.hostname?(i.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},i.buildAuthority=function(e){return i.buildUserinfo(e)+i.buildHost(e)},i.buildUserinfo=function(e){var t="";return e.username&&(t+=i.encode(e.username)),e.password&&(t+=":"+i.encode(e.password)),t&&(t+="@"),t},i.buildQuery=function(e,t,r){var n,o,s,u,c="";for(o in e)if("__proto__"!==o&&a.call(e,o))if(l(e[o]))for(n={},s=0,u=e[o].length;s<u;s++)void 0!==e[o][s]&&void 0===n[e[o][s]+""]&&(c+="&"+i.buildQueryParameter(o,e[o][s],r),!0!==t&&(n[e[o][s]+""]=!0));else void 0!==e[o]&&(c+="&"+i.buildQueryParameter(o,e[o],r));return c.substring(1)},i.buildQueryParameter=function(e,t,r){return i.encodeQuery(e,r)+(null!==t?"="+i.encodeQuery(t,r):"")},i.addQuery=function(e,t,r){if("object"==typeof t)for(var n in t)a.call(t,n)&&i.addQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=r);"string"==typeof e[t]&&(e[t]=[e[t]]),l(r)||(r=[r]),e[t]=(e[t]||[]).concat(r)}},i.setQuery=function(e,t,r){if("object"==typeof t)for(var n in t)a.call(t,n)&&i.setQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===r?null:r}},i.removeQuery=function(e,t,r){var n,o,s;if(l(t))for(n=0,o=t.length;n<o;n++)e[t[n]]=void 0;else if("RegExp"===c(t))for(s in e)t.test(s)&&(e[s]=void 0);else if("object"==typeof t)for(s in t)a.call(t,s)&&i.removeQuery(e,s,t[s]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===c(r)?!l(e[t])&&r.test(e[t])?e[t]=void 0:e[t]=p(e[t],r):e[t]!==String(r)||l(r)&&1!==r.length?l(e[t])&&(e[t]=p(e[t],r)):e[t]=void 0:e[t]=void 0}},i.hasQuery=function(e,t,r,n){switch(c(t)){case"String":break;case"RegExp":for(var o in e)if(a.call(e,o)&&t.test(o)&&(void 0===r||i.hasQuery(e,o,r)))return!0;return!1;case"Object":for(var s in t)if(a.call(t,s)&&!i.hasQuery(e,s,t[s]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(c(r)){case"Undefined":return t in e;case"Boolean":return r===Boolean(l(e[t])?e[t].length:e[t]);case"Function":return!!r(e[t],t,e);case"Array":return!!l(e[t])&&(n?h:f)(e[t],r);case"RegExp":return l(e[t])?!!n&&h(e[t],r):Boolean(e[t]&&e[t].match(r));case"Number":r=String(r);case"String":return l(e[t])?!!n&&h(e[t],r):e[t]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var e=[],t=[],r=0,n=0;n<arguments.length;n++){var o=new i(arguments[n]);e.push(o);for(var s=o.segment(),a=0;a<s.length;a++)"string"==typeof s[a]&&t.push(s[a]),s[a]&&r++}if(!t.length||!r)return new i("");var u=new i("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(e,t){var r,n=Math.min(e.length,t.length);for(r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r)){r--;break}return r<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(r)&&"/"===t.charAt(r)||(r=e.substring(0,r).lastIndexOf("/")),e.substring(0,r+1))},i.withinString=function(e,t,r){r||(r={});var n=r.start||i.findUri.start,o=r.end||i.findUri.end,s=r.trim||i.findUri.trim,a=r.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var c=n.exec(e);if(!c)break;var l=c.index;if(r.ignoreHtml){var p=e.slice(Math.max(l-3,0),l);if(p&&u.test(p))continue}for(var h=l+e.slice(l).search(o),f=e.slice(l,h),d=-1;;){var m=a.exec(f);if(!m)break;var g=m.index+m[0].length;d=Math.max(d,g)}if(!((f=d>-1?f.slice(0,d)+f.slice(d).replace(s,""):f.replace(s,"")).length<=c[0].length||r.ignore&&r.ignore.test(f))){var v=t(f,l,h=l+f.length,e);void 0!==v?(v=String(v),e=e.slice(0,l)+v+e.slice(h),n.lastIndex=l+v.length):n.lastIndex=h}}return n.lastIndex=0,e},i.ensureValidHostname=function(t,r){var n=!!t,o=!1;if(!!r&&(o=h(i.hostProtocols,r)),o&&!n)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(t&&t.match(i.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(e){if(e){var t=Number(e);if(!(/^[0-9]+$/.test(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},i.noConflict=function(e){if(e){var t={URI:this.noConflict()};return n.URITemplate&&"function"==typeof n.URITemplate.noConflict&&(t.URITemplate=n.URITemplate.noConflict()),n.IPv6&&"function"==typeof n.IPv6.noConflict&&(t.IPv6=n.IPv6.noConflict()),n.SecondLevelDomains&&"function"==typeof n.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=n.SecondLevelDomains.noConflict()),t}return n.URI===this&&(n.URI=o),this},s.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=w("protocol"),s.username=w("username"),s.password=w("password"),s.hostname=w("hostname"),s.port=w("port"),s.query=A("query","?"),s.fragment=A("fragment","#"),s.search=function(e,t){var r=this.query(e,t);return"string"==typeof r&&r.length?"?"+r:r},s.hash=function(e,t){var r=this.fragment(e,t);return"string"==typeof r&&r.length?"#"+r:r},s.pathname=function(e,t){if(void 0===e||!0===e){var r=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=e?i.recodeUrnPath(e):"":this._parts.path=e?i.recodePath(e):"/",this.build(!t),this},s.path=s.pathname,s.href=function(e,t){var r;if(void 0===e)return this.toString();this._string="",this._parts=i._parts();var n=e instanceof i,o="object"==typeof e&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[i.getDomAttribute(e)]||"",o=!1),!n&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=i.parse(String(e),this._parts);else{if(!n&&!o)throw new TypeError("invalid input");var s=n?e._parts:e;for(r in s)"query"!==r&&a.call(this._parts,r)&&(this._parts[r]=s[r]);s.query&&this.query(s.query,!1)}return this.build(!t),this},s.is=function(e){var t=!1,n=!1,o=!1,s=!1,a=!1,u=!1,c=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,n=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),a=(s=!(t=n||o))&&r&&r.has(this._parts.hostname),u=s&&i.idn_expression.test(this._parts.hostname),c=s&&i.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return s;case"sld":return a;case"ip":return t;case"ip4":case"ipv4":case"inet4":return n;case"ip6":case"ipv6":case"inet6":return o;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return c}return null};var q=s.protocol,x=s.port,k=s.hostname;s.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return q.call(this,e,t)},s.scheme=s.protocol,s.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),i.ensureValidPort(e))),x.call(this,e,t))},s.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(e,r))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(e,this._parts.protocol)}return k.call(this,e,t)},s.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var n=i(e);return this.protocol(n.protocol()).authority(n.authority()).build(!t),this},s.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==e[e.length-1]&&(e+="@"),i.parseUserinfo(e,this._parts),this.build(!t),this},s.resource=function(e,t){var r;return void 0===e?this.path()+this.search()+this.hash():(r=i.parse(e),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!t),this)},s.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,n),s=new RegExp("^"+u(o));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&i.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(s,e),this.build(!t),this},s.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(t).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},s.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(n+1);return!0!==t&&r&&r.list[o.toLowerCase()]&&r.get(this._parts.hostname)||o}var i;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}return this.build(!t),this},s.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return e?i.decodePath(n):n}var o=this._parts.path.length-this.filename().length,s=this._parts.path.substring(0,o),a=new RegExp("^"+u(s));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e),this.build(!t),this},s.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return e?i.decodePathSegment(n):n}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var s=new RegExp(u(this.filename())+"$");return e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e),o?this.normalizePath(t):this.build(!t),this},s.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,o=this.filename(),s=o.lastIndexOf(".");return-1===s?"":(r=o.substring(s+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",e?i.decodePathSegment(n):n)}"."===e.charAt(0)&&(e=e.substring(1));var a,c=this.suffix();if(c)a=e?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!e)return this;this._parts.path+="."+i.recodePath(e)}return a&&(e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e)),this.build(!t),this},s.segment=function(e,t,r){var n=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),s=o.split(n);if(void 0!==e&&"number"!=typeof e&&(r=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&s.shift(),e<0&&(e=Math.max(s.length+e,0)),void 0===t)return void 0===e?s:s[e];if(null===e||void 0===s[e])if(l(t)){s=[];for(var a=0,u=t.length;a<u;a++)(t[a].length||s.length&&s[s.length-1].length)&&(s.length&&!s[s.length-1].length&&s.pop(),s.push(d(t[a])))}else(t||"string"==typeof t)&&(t=d(t),""===s[s.length-1]?s[s.length-1]=t:s.push(t));else t?s[e]=d(t):s.splice(e,1);return i&&s.unshift(""),this.path(s.join(n),r)},s.segmentCoded=function(e,t,r){var n,o,s;if("number"!=typeof e&&(r=t,t=e,e=void 0),void 0===t){if(l(n=this.segment(e,t,r)))for(o=0,s=n.length;o<s;o++)n[o]=i.decode(n[o]);else n=void 0!==n?i.decode(n):void 0;return n}if(l(t))for(o=0,s=t.length;o<s;o++)t[o]=i.encode(t[o]);else t="string"==typeof t||t instanceof String?i.encode(t):t;return this.segment(e,t,r)};var I=s.query;return s.query=function(e,t){if(!0===e)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=e.call(this,r);return this._parts.query=i.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=i.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):I.call(this,e,t)},s.setQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)n[e]=void 0!==t?t:null;else{if("object"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)a.call(e,o)&&(n[o]=e[o])}return this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.addQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(n,e,void 0===t?null:t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.removeQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(n,e,t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.hasQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(n,e,t,r)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},s.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},s.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},s.normalizePath=function(e){var t,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var n,o,s="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(t=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(s=r.substring(1).match(/^(\.\.\/)+/)||"")&&(s=s[0]);-1!==(n=r.search(/\/\.\.(\/|$)/));)0!==n?(-1===(o=r.substring(0,n).lastIndexOf("/"))&&(o=n),r=r.substring(0,o)+r.substring(n+3)):r=r.substring(3);return t&&this.is("relative")&&(r=s+r.substring(1)),this._parts.path=r,this.build(!e),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},s.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var e=i.encode,t=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.unicode=function(){var e=i.encode,t=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.readable=function(){var t=this.clone();t.username("").password("").normalize();var r="";if(t._parts.protocol&&(r+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(r+=e.toUnicode(t._parts.hostname),t._parts.port&&(r+=":"+t._parts.port)):r+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(r+="/"),r+=t.path(!0),t._parts.query){for(var n="",o=0,s=t._parts.query.split("&"),a=s.length;o<a;o++){var u=(s[o]||"").split("=");n+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(n+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+n.substring(1)}return r+=i.decodeQuery(t.hash(),!0)},s.absoluteTo=function(e){var t,r,n,o=this.clone(),s=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof i||(e=new i(e)),o._parts.protocol)return o;if(o._parts.protocol=e._parts.protocol,this._parts.hostname)return o;for(r=0;n=s[r];r++)o._parts[n]=e._parts[n];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),o.build(),o},s.relativeTo=function(e){var t,r,n,o,s,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new i(e).normalize(),t=a._parts,r=e._parts,o=a.path(),s=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==s.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===r.protocol&&(t.protocol=null),t.username!==r.username||t.password!==r.password)return a.build();if(null!==t.protocol||null!==t.username||null!==t.password)return a.build();if(t.hostname!==r.hostname||t.port!==r.port)return a.build();if(t.hostname=null,t.port=null,o===s)return t.path="",a.build();if(!(n=i.commonPath(o,s)))return a.build();var u=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=u+t.path.substring(n.length)||"./",a.build()},s.equals=function(e){var t,r,n,o,s,u=this.clone(),c=new i(e),p={};if(u.normalize(),c.normalize(),u.toString()===c.toString())return!0;if(n=u.query(),o=c.query(),u.query(""),c.query(""),u.toString()!==c.toString())return!1;if(n.length!==o.length)return!1;for(s in t=i.parseQuery(n,this._parts.escapeQuerySpace),r=i.parseQuery(o,this._parts.escapeQuerySpace),t)if(a.call(t,s)){if(l(t[s])){if(!f(t[s],r[s]))return!1}else if(t[s]!==r[s])return!1;p[s]=!0}for(s in r)if(a.call(r,s)&&!p[s])return!1;return!0},s.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},s.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},s.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},i},(
/*!
	 * URI.js - Mutating URLs
	 *
	 * Version: 1.19.11
	 *
	 * Author: Rodney Rehm
	 * Web: http://medialize.github.io/URI.js/
	 *
	 * Licensed under
	 *   MIT License http://www.opensource.org/licenses/mit-license
	 *
	 */
d=c).exports?d.exports=g((a||(a=1,function(e,t){!function(r){var n=t&&!t.nodeType&&t,o=e&&!e.nodeType&&e,i="object"==typeof u&&u;i.global!==i&&i.window!==i&&i.self!==i||(r=i);var s,a,c=**********,l=36,p=1,h=26,f=38,d=700,m=72,g=128,v="-",y=/^xn--/,b=/[^\x20-\x7E]/,_=/[\x2E\u3002\uFF0E\uFF61]/g,w={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},A=l-p,q=Math.floor,x=String.fromCharCode;function k(e){throw new RangeError(w[e])}function I(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function O(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+I((e=e.replace(_,".")).split("."),t).join(".")}function E(e){for(var t,r,n=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(r=e.charCodeAt(o++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),o--):n.push(t);return n}function S(e){return I(e,(function(e){var t="";return e>65535&&(t+=x((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+x(e)})).join("")}function R(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function C(e,t,r){var n=0;for(e=r?q(e/d):e>>1,e+=q(e/t);e>A*h>>1;n+=l)e=q(e/A);return q(n+(A+1)*e/(e+f))}function P(e){var t,r,n,o,i,s,a,u,f,d,y,b=[],_=e.length,w=0,A=g,x=m;for((r=e.lastIndexOf(v))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&k("not-basic"),b.push(e.charCodeAt(n));for(o=r>0?r+1:0;o<_;){for(i=w,s=1,a=l;o>=_&&k("invalid-input"),((u=(y=e.charCodeAt(o++))-48<10?y-22:y-65<26?y-65:y-97<26?y-97:l)>=l||u>q((c-w)/s))&&k("overflow"),w+=u*s,!(u<(f=a<=x?p:a>=x+h?h:a-x));a+=l)s>q(c/(d=l-f))&&k("overflow"),s*=d;x=C(w-i,t=b.length+1,0==i),q(w/t)>c-A&&k("overflow"),A+=q(w/t),w%=t,b.splice(w++,0,A)}return S(b)}function T(e){var t,r,n,o,i,s,a,u,f,d,y,b,_,w,A,I=[];for(b=(e=E(e)).length,t=g,r=0,i=m,s=0;s<b;++s)(y=e[s])<128&&I.push(x(y));for(n=o=I.length,o&&I.push(v);n<b;){for(a=c,s=0;s<b;++s)(y=e[s])>=t&&y<a&&(a=y);for(a-t>q((c-r)/(_=n+1))&&k("overflow"),r+=(a-t)*_,t=a,s=0;s<b;++s)if((y=e[s])<t&&++r>c&&k("overflow"),y==t){for(u=r,f=l;!(u<(d=f<=i?p:f>=i+h?h:f-i));f+=l)A=u-d,w=l-d,I.push(x(R(d+A%w,0))),u=q(A/w);I.push(x(R(u,0))),i=C(r,_,n==o),r=0,++n}++r,++t}return I.join("")}if(s={version:"1.3.2",ucs2:{decode:E,encode:S},decode:P,encode:T,toASCII:function(e){return O(e,(function(e){return b.test(e)?"xn--"+T(e):e}))},toUnicode:function(e){return O(e,(function(e){return y.test(e)?P(e.slice(4).toLowerCase()):e}))}},n&&o)if(e.exports==n)o.exports=s;else for(a in s)s.hasOwnProperty(a)&&(n[a]=s[a]);else r.punycode=s}(u)}(l,l.exports)),l.exports),function(){return p||(p=1,t=u,r=function(e){var t=e&&e.IPv6;return{best:function(e){var t,r,n=e.toLowerCase().split(":"),o=n.length,i=8;for(""===n[0]&&""===n[1]&&""===n[2]?(n.shift(),n.shift()):""===n[0]&&""===n[1]?n.shift():""===n[o-1]&&""===n[o-2]&&n.pop(),-1!==n[(o=n.length)-1].indexOf(".")&&(i=7),t=0;t<o&&""!==n[t];t++);if(t<i)for(n.splice(t,1,"0000");n.length<i;)n.splice(t,0,"0000");for(var s=0;s<i;s++){r=n[s].split("");for(var a=0;a<3&&"0"===r[0]&&r.length>1;a++)r.splice(0,1);n[s]=r.join("")}var u=-1,c=0,l=0,p=-1,h=!1;for(s=0;s<i;s++)h?"0"===n[s]?l+=1:(h=!1,l>c&&(u=p,c=l)):"0"===n[s]&&(h=!0,p=s,l=1);l>c&&(u=p,c=l),c>1&&n.splice(u,c,""),o=n.length;var f="";for(""===n[0]&&(f=":"),s=0;s<o&&(f+=n[s],s!==o-1);s++)f+=":";return""===n[o-1]&&(f+=":"),f},noConflict:function(){return e.IPv6===this&&(e.IPv6=t),this}}},(e=h).exports?e.exports=r():t.IPv6=r(t)),h.exports;var e,t,r}(),function(){return f||(f=1,t=u,r=function(e){var t=e&&e.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return!1;var o=r.list[e.slice(t+1)];return!!o&&o.indexOf(" "+e.slice(n+1,t)+" ")>=0},is:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;if(e.lastIndexOf(".",t-1)>=0)return!1;var n=r.list[e.slice(t+1)];return!!n&&n.indexOf(" "+e.slice(0,t)+" ")>=0},get:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return null;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return null;var o=r.list[e.slice(t+1)];return o?o.indexOf(" "+e.slice(n+1,t)+" ")<0?null:e.slice(n+1):null},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return r},(e=v).exports?e.exports=r():t.SecondLevelDomains=r(t)),v.exports;var e,t,r}()):m.URI=g(m.punycode,m.IPv6,m.SecondLevelDomains,m);var y=c.exports;function b(e){let t;this.name="DeveloperError",this.message=e;try{throw new Error}catch(e){t=e.stack}this.stack=t}r.defined(Object.create)&&(b.prototype=Object.create(Error.prototype),b.prototype.constructor=b),b.prototype.toString=function(){let e=`${this.name}: ${this.message}`;return r.defined(this.stack)&&(e+=`\n${this.stack.toString()}`),e},b.throwInstantiationError=function(){throw new b("This function defines an interface and should not be called directly.")};const _={};function w(e,t,r){return`Expected ${r} to be typeof ${t}, actual typeof was ${e}`}_.typeOf={},_.defined=function(e,t){if(!r.defined(t))throw new b(function(e){return`${e} is required, actual value was undefined`}(e))},_.typeOf.func=function(e,t){if("function"!=typeof t)throw new b(w(typeof t,"function",e))},_.typeOf.string=function(e,t){if("string"!=typeof t)throw new b(w(typeof t,"string",e))},_.typeOf.number=function(e,t){if("number"!=typeof t)throw new b(w(typeof t,"number",e))},_.typeOf.number.lessThan=function(e,t,r){if(_.typeOf.number(e,t),t>=r)throw new b(`Expected ${e} to be less than ${r}, actual value was ${t}`)},_.typeOf.number.lessThanOrEquals=function(e,t,r){if(_.typeOf.number(e,t),t>r)throw new b(`Expected ${e} to be less than or equal to ${r}, actual value was ${t}`)},_.typeOf.number.greaterThan=function(e,t,r){if(_.typeOf.number(e,t),t<=r)throw new b(`Expected ${e} to be greater than ${r}, actual value was ${t}`)},_.typeOf.number.greaterThanOrEquals=function(e,t,r){if(_.typeOf.number(e,t),t<r)throw new b(`Expected ${e} to be greater than or equal to ${r}, actual value was ${t}`)},_.typeOf.object=function(e,t){if("object"!=typeof t)throw new b(w(typeof t,"object",e))},_.typeOf.bool=function(e,t){if("boolean"!=typeof t)throw new b(w(typeof t,"boolean",e))},_.typeOf.bigint=function(e,t){if("bigint"!=typeof t)throw new b(w(typeof t,"bigint",e))},_.typeOf.number.equals=function(e,t,r,n){if(_.typeOf.number(e,r),_.typeOf.number(t,n),r!==n)throw new b(`${e} must be equal to ${t}, the actual values are ${r} and ${n}`)};var A=_;function q(e,t){if(null===e||"object"!=typeof e)return e;t=r.defaultValue(t,!1);const n=new e.constructor;for(const r in e)if(e.hasOwnProperty(r)){let o=e[r];t&&(o=q(o,t)),n[r]=o}return n}function x(){let e,t;const r=new Promise((function(r,n){e=r,t=n}));return{resolve:e,reject:t,promise:r}}function k(e,t){let r;return"undefined"!=typeof document&&(r=document),k._implementation(e,t,r)}k._implementation=function(e,t,n){if(!r.defined(t)){if(void 0===n)return e;t=r.defaultValue(n.baseURI,n.location.href)}const o=new y(e);return""!==o.scheme()?o.toString():o.absoluteTo(t).toString()};const I={};function O(e,t,n){r.defined(t)||(t=e.width),r.defined(n)||(n=e.height);let o=I[t];r.defined(o)||(o={},I[t]=o);let i=o[n];if(!r.defined(i)){const e=document.createElement("canvas");e.width=t,e.height=n,i=e.getContext("2d"),i.globalCompositeOperation="copy",o[n]=i}return i.drawImage(e,0,0,t,n),i.getImageData(0,0,t,n).data}const E=/^blob:/i;function S(e){return E.test(e)}let R;const C=/^data:/i;function P(e){return C.test(e)}var T=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5});var z=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3});function U(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT);const t=r.defaultValue(e.throttleByServer,!1),n=r.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=r.defaultValue(e.priority,0),this.throttle=n,this.throttleByServer=t,this.type=r.defaultValue(e.type,z.OTHER),this.serverKey=void 0,this.state=T.UNISSUED,this.deferred=void 0,this.cancelled=!1}function j(e,t,r){this.statusCode=e,this.response=t,this.responseHeaders=r,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){const t={};if(!e)return t;const r=e.split("\r\n");for(let e=0;e<r.length;++e){const n=r[e],o=n.indexOf(": ");if(o>0){const e=n.substring(0,o),r=n.substring(o+2);t[e]=r}}return t}(this.responseHeaders))}function Q(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function F(e,t){return t-e}function B(e){this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function D(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}U.prototype.cancel=function(){this.cancelled=!0},U.prototype.clone=function(e){return r.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=this.RequestState.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new U(this)},j.prototype.toString=function(){let e="Request has failed.";return r.defined(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e},Object.defineProperties(Q.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),Q.prototype.addEventListener=function(e,t){this._listeners.push(e),this._scopes.push(t);const r=this;return function(){r.removeEventListener(e,t)}},Q.prototype.removeEventListener=function(e,t){const r=this._listeners,n=this._scopes;let o=-1;for(let i=0;i<r.length;i++)if(r[i]===e&&n[i]===t){o=i;break}return-1!==o&&(this._insideRaiseEvent?(this._toRemove.push(o),r[o]=void 0,n[o]=void 0):(r.splice(o,1),n.splice(o,1)),!0)},Q.prototype.raiseEvent=function(){let e;this._insideRaiseEvent=!0;const t=this._listeners,n=this._scopes;let o=t.length;for(e=0;e<o;e++){const o=t[e];r.defined(o)&&t[e].apply(n[e],arguments)}const i=this._toRemove;if(o=i.length,o>0){for(i.sort(F),e=0;e<o;e++){const r=i[e];t.splice(r,1),n.splice(r,1)}i.length=0}this._insideRaiseEvent=!1},Object.defineProperties(B.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){const t=this._length;if(e<t){const r=this._array;for(let n=e;n<t;++n)r[n]=void 0;this._length=e,r.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}}),B.prototype.reserve=function(e){e=r.defaultValue(e,this._length),this._array.length=e},B.prototype.heapify=function(e){e=r.defaultValue(e,0);const t=this._length,n=this._comparator,o=this._array;let i=-1,s=!0;for(;s;){const r=2*(e+1),a=r-1;i=a<t&&n(o[a],o[e])<0?a:e,r<t&&n(o[r],o[i])<0&&(i=r),i!==e?(D(o,i,e),e=i):s=!1}},B.prototype.resort=function(){const e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},B.prototype.insert=function(e){const t=this._array,n=this._comparator,o=this._maximumLength;let i,s=this._length++;for(s<t.length?t[s]=e:t.push(e);0!==s;){const e=Math.floor((s-1)/2);if(!(n(t[s],t[e])<0))break;D(t,s,e),s=e}return r.defined(o)&&this._length>o&&(i=t[o],this._length=o),i},B.prototype.pop=function(e){if(e=r.defaultValue(e,0),0===this._length)return;const t=this._array,n=t[e];return D(t,e,--this._length),this.heapify(e),t[this._length]=void 0,n};const $={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0};let L=20;const V=new B({comparator:function(e,t){return e.priority-t.priority}});V.maximumLength=L,V.reserve(L);const H=[];let M={};const N="undefined"!=typeof document?new y(document.location.href):new y,Y=new Q;function J(){}function X(e){r.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function K(e){return e.state===T.UNISSUED&&(e.state=T.ISSUED,e.deferred=x()),e.deferred.promise}function Z(e){const t=K(e);return e.state=T.ACTIVE,H.push(e),++$.numberOfActiveRequests,++$.numberOfActiveRequestsEver,++M[e.serverKey],e.requestFunction().then(function(e){return function(t){if(e.state===T.CANCELLED)return;const r=e.deferred;--$.numberOfActiveRequests,--M[e.serverKey],Y.raiseEvent(),e.state=T.RECEIVED,e.deferred=void 0,r.resolve(t)}}(e)).catch(function(e){return function(t){e.state!==T.CANCELLED&&(++$.numberOfFailedRequests,--$.numberOfActiveRequests,--M[e.serverKey],Y.raiseEvent(t),e.state=T.FAILED,e.deferred.reject(t))}}(e)),t}function G(e){const t=e.state===T.ACTIVE;if(e.state=T.CANCELLED,++$.numberOfCancelledRequests,r.defined(e.deferred)){const t=e.deferred;e.deferred=void 0,t.reject()}t&&(--$.numberOfActiveRequests,--M[e.serverKey],++$.numberOfCancelledActiveRequests),r.defined(e.cancelFunction)&&e.cancelFunction()}J.maximumRequests=50,J.maximumRequestsPerServer=6,J.requestsByServer={"api.cesium.com:443":18,"assets.cesium.com:443":18},J.throttleRequests=!0,J.debugShowStatistics=!1,J.requestCompletedEvent=Y,Object.defineProperties(J,{statistics:{get:function(){return $}},priorityHeapLength:{get:function(){return L},set:function(e){if(e<L)for(;V.length>e;){G(V.pop())}L=e,V.maximumLength=e,V.reserve(e)}}}),J.serverHasOpenSlots=function(e,t){t=r.defaultValue(t,1);const n=r.defaultValue(J.requestsByServer[e],J.maximumRequestsPerServer);return M[e]+t<=n},J.heapHasOpenSlots=function(e){return V.length+e<=L},J.update=function(){let e,t,r=0;const n=H.length;for(e=0;e<n;++e)t=H[e],t.cancelled&&G(t),t.state===T.ACTIVE?r>0&&(H[e-r]=t):++r;H.length-=r;const o=V.internalArray,i=V.length;for(e=0;e<i;++e)X(o[e]);V.resort();const s=Math.max(J.maximumRequests-H.length,0);let a=0;for(;a<s&&V.length>0;)t=V.pop(),t.cancelled?G(t):!t.throttleByServer||J.serverHasOpenSlots(t.serverKey)?(Z(t),++a):G(t);!function(){if(!J.debugShowStatistics)return;0===$.numberOfActiveRequests&&$.lastNumberOfActiveRequests>0&&($.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${$.numberOfAttemptedRequests}`),$.numberOfAttemptedRequests=0),$.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${$.numberOfCancelledRequests}`),$.numberOfCancelledRequests=0),$.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${$.numberOfCancelledActiveRequests}`),$.numberOfCancelledActiveRequests=0),$.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${$.numberOfFailedRequests}`),$.numberOfFailedRequests=0));$.lastNumberOfActiveRequests=$.numberOfActiveRequests}()},J.getServerKey=function(e){let t=new y(e);""===t.scheme()&&(t=new y(e).absoluteTo(N),t.normalize());let n=t.authority();/:/.test(n)||(n=`${n}:${"https"===t.scheme()?"443":"80"}`);const o=M[n];return r.defined(o)||(M[n]=0),n},J.request=function(e){if(P(e.url)||S(e.url))return Y.raiseEvent(),e.state=T.RECEIVED,e.requestFunction();if(++$.numberOfAttemptedRequests,r.defined(e.serverKey)||(e.serverKey=J.getServerKey(e.url)),J.throttleRequests&&e.throttleByServer&&!J.serverHasOpenSlots(e.serverKey))return;if(!J.throttleRequests||!e.throttle)return Z(e);if(H.length>=J.maximumRequests)return;X(e);const t=V.insert(e);if(r.defined(t)){if(t===e)return;G(t)}return K(e)},J.clearForSpecs=function(){for(;V.length>0;){G(V.pop())}const e=H.length;for(let t=0;t<e;++t)G(H[t]);H.length=0,M={},$.numberOfAttemptedRequests=0,$.numberOfActiveRequests=0,$.numberOfCancelledRequests=0,$.numberOfCancelledActiveRequests=0,$.numberOfFailedRequests=0,$.numberOfActiveRequestsEver=0,$.lastNumberOfActiveRequests=0},J.numberOfActiveRequestsByServer=function(e){return M[e]},J.requestHeap=V;const W={};let ee={};W.add=function(e,t){const n=`${e.toLowerCase()}:${t}`;r.defined(ee[n])||(ee[n]=!0)},W.remove=function(e,t){const n=`${e.toLowerCase()}:${t}`;r.defined(ee[n])&&delete ee[n]},W.contains=function(e){const t=function(e){const t=new y(e);t.normalize();let r=t.authority();if(0!==r.length){if(t.authority(r),-1!==r.indexOf("@")){const e=r.split("@");r=e[1]}if(-1===r.indexOf(":")){let e=t.scheme();if(0===e.length&&(e=window.location.protocol,e=e.substring(0,e.length-1)),"http"===e)r+=":80";else{if("https"!==e)return;r+=":443"}}return r}}(e);return!(!r.defined(t)||!r.defined(ee[t]))},W.clear=function(){ee={}};var te=W;const re=function(){try{const e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(e){return!1}}();function ne(e,t,n,o){const i=e.query();if(0===i.length)return{};let s;if(-1===i.indexOf("=")){const e={};e[i]=void 0,s=e}else s=function(e){const t={};if(""===e)return t;const n=e.replace(/\+/g,"%20").split(/[&;]/);for(let e=0,o=n.length;e<o;++e){const o=n[e].split("="),i=decodeURIComponent(o[0]);let s=o[1];s=r.defined(s)?decodeURIComponent(s):"";const a=t[i];"string"==typeof a?t[i]=[a,s]:Array.isArray(a)?a.push(s):t[i]=s}return t}(i);t._queryParameters=n?ae(s,t._queryParameters,o):s,e.search("")}function oe(e,t){const n=t._queryParameters,o=Object.keys(n);1!==o.length||r.defined(n[o[0]])?e.search(function(e){let t="";for(const r in e)if(e.hasOwnProperty(r)){const n=e[r],o=`${encodeURIComponent(r)}=`;if(Array.isArray(n))for(let e=0,r=n.length;e<r;++e)t+=`${o+encodeURIComponent(n[e])}&`;else t+=`${o+encodeURIComponent(n)}&`}return t=t.slice(0,-1),t}(n)):e.search(o[0])}function ie(e,t){return r.defined(e)?r.defined(e.clone)?e.clone():q(e):t}function se(e){if(e.state===T.ISSUED||e.state===T.ACTIVE)throw new i.RuntimeError("The Resource is already being fetched.");e.state=T.UNISSUED,e.deferred=void 0}function ae(e,t,o){if(!o)return n.combine(e,t);const i=q(e,!0);for(const e in t)if(t.hasOwnProperty(e)){let n=i[e];const o=t[e];r.defined(n)?(Array.isArray(n)||(n=i[e]=[n]),i[e]=n.concat(o)):i[e]=Array.isArray(o)?o.slice():o}return i}function ue(e){"string"==typeof(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT))&&(e={url:e}),this._url=void 0,this._templateValues=ie(e.templateValues,{}),this._queryParameters=ie(e.queryParameters,{}),this.headers=ie(e.headers,{}),this.request=r.defaultValue(e.request,new U),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=r.defaultValue(e.retryAttempts,0),this._retryCount=0;const t=new y(e.url);ne(t,this,!0,!0),t.fragment(""),this._url=t.toString()}let ce;function le(e){const t=e.resource,n=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,s=t.request;s.url=t.url,s.requestFunction=function(){let e=!1;t.isDataUri||t.isBlobUri||(e=t.isCrossOriginUrl);const r=x();return ue._Implementations.createImage(s,e,r,n,o,i),r.promise};const a=J.request(s);if(r.defined(a))return a.catch((function(e){return s.state!==T.FAILED?Promise.reject(e):t.retryOnError(e).then((function(r){return r?(s.state=T.UNISSUED,s.deferred=void 0,le({resource:t,flipY:n,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(e)}))}))}function pe(e,t,n){const o={};o[t]=n,e.setQueryParameters(o);const i=e.request;i.url=e.url,i.requestFunction=function(){const t=x();return window[n]=function(e){t.resolve(e);try{delete window[n]}catch(e){window[n]=void 0}},ue._Implementations.loadAndExecuteScript(e.url,n,t),t.promise};const s=J.request(i);if(r.defined(s))return s.catch((function(r){return i.state!==T.FAILED?Promise.reject(r):e.retryOnError(r).then((function(o){return o?(i.state=T.UNISSUED,i.deferred=void 0,pe(e,t,n)):Promise.reject(r)}))}))}ue.createIfNeeded=function(e){return e instanceof ue?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new ue({url:e})},ue.supportsImageBitmapOptions=function(){if(r.defined(ce))return ce;if("function"!=typeof createImageBitmap)return ce=Promise.resolve(!1),ce;return ce=ue.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then((function(e){return Promise.all([createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"}),createImageBitmap(e)])})).then((function(e){const t=O(e[0]),r=O(e[1]);return t[1]!==r[1]})).catch((function(){return!1})),ce},Object.defineProperties(ue,{isBlobSupported:{get:function(){return re}}}),Object.defineProperties(ue.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){const t=new y(e);ne(t,this,!1),t.fragment(""),this._url=t.toString()}},extension:{get:function(){return function(e){const t=new y(e);t.normalize();let r=t.path(),n=r.lastIndexOf("/");return-1!==n&&(r=r.substr(n+1)),n=r.lastIndexOf("."),r=-1===n?"":r.substr(n+1),r}(this._url)}},isDataUri:{get:function(){return P(this._url)}},isBlobUri:{get:function(){return S(this._url)}},isCrossOriginUrl:{get:function(){return function(e){r.defined(R)||(R=document.createElement("a")),R.href=window.location.href;const t=R.host,n=R.protocol;return R.href=e,R.href=R.href,n!==R.protocol||t!==R.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}}}),ue.prototype.toString=function(){return this.getUrlComponent(!0,!0)},ue.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;const n=new y(this._url);e&&oe(n,this);let o=n.toString().replace(/%7B/g,"{").replace(/%7D/g,"}");const i=this._templateValues;return o=o.replace(/{(.*?)}/g,(function(e,t){const n=i[t];return r.defined(n)?encodeURIComponent(n):e})),t&&r.defined(this.proxy)&&(o=this.proxy.getURL(o)),o},ue.prototype.setQueryParameters=function(e,t){this._queryParameters=t?ae(this._queryParameters,e,!1):ae(e,this._queryParameters,!1)},ue.prototype.appendQueryParameters=function(e){this._queryParameters=ae(e,this._queryParameters,!0)},ue.prototype.setTemplateValues=function(e,t){this._templateValues=t?n.combine(this._templateValues,e):n.combine(e,this._templateValues)},ue.prototype.getDerivedResource=function(e){const t=this.clone();if(t._retryCount=0,r.defined(e.url)){const n=new y(e.url);ne(n,t,!0,r.defaultValue(e.preserveQueryParameters,!1)),n.fragment(""),""!==n.scheme()?t._url=n.toString():t._url=n.absoluteTo(new y(k(this._url))).toString()}return r.defined(e.queryParameters)&&(t._queryParameters=n.combine(e.queryParameters,t._queryParameters)),r.defined(e.templateValues)&&(t._templateValues=n.combine(e.templateValues,t.templateValues)),r.defined(e.headers)&&(t.headers=n.combine(e.headers,t.headers)),r.defined(e.proxy)&&(t.proxy=e.proxy),r.defined(e.request)&&(t.request=e.request),r.defined(e.retryCallback)&&(t.retryCallback=e.retryCallback),r.defined(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},ue.prototype.retryOnError=function(e){const t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);const r=this;return Promise.resolve(t(this,e)).then((function(e){return++r._retryCount,e}))},ue.prototype.clone=function(e){return r.defined(e)||(e=new ue({url:this._url})),e._url=this._url,e._queryParameters=q(this._queryParameters),e._templateValues=q(this._templateValues),e.headers=q(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e},ue.prototype.getBaseUri=function(e){return function(e,t){let r="";const n=e.lastIndexOf("/");return-1!==n&&(r=e.substring(0,n+1)),t?(0!==(e=new y(e)).query().length&&(r+=`?${e.query()}`),0!==e.fragment().length&&(r+=`#${e.fragment()}`),r):r}(this.getUrlComponent(e),e)},ue.prototype.appendForwardSlash=function(){var e;this._url=(0!==(e=this._url).length&&"/"===e[e.length-1]||(e=`${e}/`),e)},ue.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},ue.fetchArrayBuffer=function(e){return new ue(e).fetchArrayBuffer()},ue.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},ue.fetchBlob=function(e){return new ue(e).fetchBlob()},ue.prototype.fetchImage=function(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT);const t=r.defaultValue(e.preferImageBitmap,!1),n=r.defaultValue(e.preferBlob,!1),o=r.defaultValue(e.flipY,!1),i=r.defaultValue(e.skipColorSpaceConversion,!1);if(se(this.request),!re||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!n)return le({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:t});const s=this.fetchBlob();if(!r.defined(s))return;let a,u,c,l;return ue.supportsImageBitmapOptions().then((function(e){return a=e,u=a&&t,s})).then((function(e){if(!r.defined(e))return;if(l=e,u)return ue.createImageBitmapFromBlob(e,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});const t=window.URL.createObjectURL(e);return c=new ue({url:t}),le({resource:c,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})})).then((function(e){if(r.defined(e))return e.blob=l,u||window.URL.revokeObjectURL(c.url),e})).catch((function(e){return r.defined(c)&&window.URL.revokeObjectURL(c.url),e.blob=l,Promise.reject(e)}))},ue.fetchImage=function(e){return new ue(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},ue.prototype.fetchText=function(){return this.fetch({responseType:"text"})},ue.fetchText=function(e){return new ue(e).fetchText()},ue.prototype.fetchJson=function(){const e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(r.defined(e))return e.then((function(e){if(r.defined(e))return JSON.parse(e)}))},ue.fetchJson=function(e){return new ue(e).fetchJson()},ue.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},ue.fetchXML=function(e){return new ue(e).fetchXML()},ue.prototype.fetchJsonp=function(e){let t;e=r.defaultValue(e,"callback"),se(this.request);do{t=`loadJsonp${o.CesiumMath.nextRandomNumber().toString().substring(2,8)}`}while(r.defined(window[t]));return pe(this,e,t)},ue.fetchJsonp=function(e){return new ue(e).fetchJsonp(e.callbackParameterName)},ue.prototype._makeRequest=function(e){const t=this;se(t.request);const o=t.request;o.url=t.url,o.requestFunction=function(){const i=e.responseType,s=n.combine(e.headers,t.headers),a=e.overrideMimeType,u=e.method,c=e.data,l=x(),p=ue._Implementations.loadWithXhr(t.url,i,u,c,s,l,a);return r.defined(p)&&r.defined(p.abort)&&(o.cancelFunction=function(){p.abort()}),l.promise};const i=J.request(o);if(r.defined(i))return i.then((function(e){return o.cancelFunction=void 0,e})).catch((function(r){return o.cancelFunction=void 0,o.state!==T.FAILED?Promise.reject(r):t.retryOnError(r).then((function(n){return n?(o.state=T.UNISSUED,o.deferred=void 0,t.fetch(e)):Promise.reject(r)}))}))};const he=/^data:(.*?)(;base64)?,(.*)$/;function fe(e,t){const r=decodeURIComponent(t);return e?atob(r):r}function de(e,t){const r=fe(e,t),n=new ArrayBuffer(r.length),o=new Uint8Array(n);for(let e=0;e<r.length;e++)o[e]=r.charCodeAt(e);return n}function me(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}ue.prototype.fetch=function(e){return(e=ie(e,{})).method="GET",this._makeRequest(e)},ue.fetch=function(e){return new ue(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue.prototype.delete=function(e){return(e=ie(e,{})).method="DELETE",this._makeRequest(e)},ue.delete=function(e){return new ue(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},ue.prototype.head=function(e){return(e=ie(e,{})).method="HEAD",this._makeRequest(e)},ue.head=function(e){return new ue(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue.prototype.options=function(e){return(e=ie(e,{})).method="OPTIONS",this._makeRequest(e)},ue.options=function(e){return new ue(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue.prototype.post=function(e,t){return A.defined("data",e),(t=ie(t,{})).method="POST",t.data=e,this._makeRequest(t)},ue.post=function(e){return new ue(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue.prototype.put=function(e,t){return A.defined("data",e),(t=ie(t,{})).method="PUT",t.data=e,this._makeRequest(t)},ue.put=function(e){return new ue(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue.prototype.patch=function(e,t){return A.defined("data",e),(t=ie(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},ue.patch=function(e){return new ue(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ue._Implementations={},ue._Implementations.loadImageElement=function(e,t,r){const n=new Image;n.onload=function(){0===n.naturalWidth&&0===n.naturalHeight&&0===n.width&&0===n.height&&(n.width=300,n.height=150),r.resolve(n)},n.onerror=function(e){r.reject(e)},t&&(te.contains(e)?n.crossOrigin="use-credentials":n.crossOrigin=""),n.src=e},ue._Implementations.createImage=function(e,t,n,o,s,a){const u=e.url;ue.supportsImageBitmapOptions().then((function(c){if(!c||!a)return void ue._Implementations.loadImageElement(u,t,n);const l=x(),p=ue._Implementations.loadWithXhr(u,"blob","GET",void 0,void 0,l,void 0,void 0,void 0);return r.defined(p)&&r.defined(p.abort)&&(e.cancelFunction=function(){p.abort()}),l.promise.then((function(e){if(r.defined(e))return ue.createImageBitmapFromBlob(e,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:s});n.reject(new i.RuntimeError(`Successfully retrieved ${u} but it contained no content.`))})).then((function(e){n.resolve(e)}))})).catch((function(e){n.reject(e)}))},ue.createImageBitmapFromBlob=function(e,t){return A.defined("options",t),A.typeOf.bool("options.flipY",t.flipY),A.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),A.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};const ge="undefined"==typeof XMLHttpRequest;ue._Implementations.loadWithXhr=function(t,n,o,a,u,c,l){const p=he.exec(t);if(null!==p)return void c.resolve(function(e,t){t=r.defaultValue(t,"");const n=e[1],o=!!e[2],i=e[3];let s,a;switch(t){case"":case"text":return fe(o,i);case"arraybuffer":return de(o,i);case"blob":return s=de(o,i),new Blob([s],{type:n});case"document":return a=new DOMParser,a.parseFromString(fe(o,i),n);case"json":return JSON.parse(fe(o,i))}}(p,n));if(ge)return void function(t,r,n,o,a,u,c){let l,p;Promise.all([new Promise((function(t,r){e(["url"],(function(e){t(s(e))}),r)})),new Promise((function(t,r){e(["zlib"],(function(e){t(s(e))}),r)}))]).then((([r,n])=>(l=r.parse(t),p=n,"https:"===l.protocol?new Promise((function(t,r){e(["https"],(function(e){t(s(e))}),r)})):new Promise((function(t,r){e(["http"],(function(e){t(s(e))}),r)}))))).then((e=>{const t={protocol:l.protocol,hostname:l.hostname,port:l.port,path:l.path,query:l.query,method:n,headers:a};e.request(t).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)return void u.reject(new j(e.statusCode,e,e.headers));const t=[];e.on("data",(function(e){t.push(e)})),e.on("end",(function(){const n=Buffer.concat(t);"gzip"===e.headers["content-encoding"]?p.gunzip(n,(function(e,t){e?u.reject(new i.RuntimeError("Error decompressing response.")):u.resolve(me(t,r))})):u.resolve(me(n,r))}))})).on("error",(function(e){u.reject(new j)})).end()}))}(t,n,o,0,u,c);const h=new XMLHttpRequest;if(te.contains(t)&&(h.withCredentials=!0),h.open(o,t,!0),r.defined(l)&&r.defined(h.overrideMimeType)&&h.overrideMimeType(l),r.defined(u))for(const e in u)u.hasOwnProperty(e)&&h.setRequestHeader(e,u[e]);r.defined(n)&&(h.responseType=n);let f=!1;return"string"==typeof t&&(f=0===t.indexOf("file://")||"undefined"!=typeof window&&"file://"===window.location.origin),h.onload=function(){if((h.status<200||h.status>=300)&&(!f||0!==h.status))return void c.reject(new j(h.status,h.response,h.getAllResponseHeaders()));const e=h.response,t=h.responseType;if("HEAD"===o||"OPTIONS"===o){const e=h.getAllResponseHeaders().trim().split(/[\r\n]+/),t={};return e.forEach((function(e){const r=e.split(": "),n=r.shift();t[n]=r.join(": ")})),void c.resolve(t)}if(204===h.status)c.resolve();else if(!r.defined(e)||r.defined(n)&&t!==n)if("json"===n&&"string"==typeof e)try{c.resolve(JSON.parse(e))}catch(e){c.reject(e)}else(""===t||"document"===t)&&r.defined(h.responseXML)&&h.responseXML.hasChildNodes()?c.resolve(h.responseXML):""!==t&&"text"!==t||!r.defined(h.responseText)?c.reject(new i.RuntimeError("Invalid XMLHttpRequest response type.")):c.resolve(h.responseText);else c.resolve(e)},h.onerror=function(e){c.reject(new j)},h.send(a),h},ue._Implementations.loadAndExecuteScript=function(e,t,r){return function(e){const t=document.createElement("script");return t.async=!0,t.src=e,new Promise(((e,r)=>{window.crossOriginIsolated&&t.setAttribute("crossorigin","anonymous");const n=document.getElementsByTagName("head")[0];t.onload=function(){t.onload=void 0,n.removeChild(t),e()},t.onerror=function(e){r(e)},n.appendChild(t)}))}(e).catch((function(e){r.reject(e)}))},ue._DefaultImplementations={},ue._DefaultImplementations.createImage=ue._Implementations.createImage,ue._DefaultImplementations.loadWithXhr=ue._Implementations.loadWithXhr,ue._DefaultImplementations.loadAndExecuteScript=ue._Implementations.loadAndExecuteScript,ue.DEFAULT=Object.freeze(new ue({url:"undefined"==typeof document?"":document.location.href.split("?")[0]})),t.DeveloperError=b,t.Resource=ue,t.getAbsoluteUri=k}));
