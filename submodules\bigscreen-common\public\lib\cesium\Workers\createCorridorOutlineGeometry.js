/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./arrayRemoveDuplicates-5b666c82","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./PolylineVolumeGeometryLibrary-fad92191","./CorridorGeometryLibrary-e676cbda","./defined-3b3eb2ba","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./WebGLConstants-433debbf","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db"],(function(e,t,i,r,o,n,s,a,l,d,u,p,f,c,h,y,g,b,m,A,_,E,C,G,T){"use strict";const P=new i.Cartesian3,v=new i.Cartesian3,w=new i.Cartesian3;function L(e,t){const a=[],u=e.positions,f=e.corners,c=e.endPositions,h=new d.GeometryAttributes;let y,g,b,m=0,A=0,_=0;for(g=0;g<u.length;g+=2)b=u[g].length-3,m+=b,_+=b/3*4,A+=u[g+1].length-3;for(m+=3,A+=3,g=0;g<f.length;g++){y=f[g];const e=f[g].leftPositions;s.defined(e)?(b=e.length,m+=b,_+=b/3*2):(b=f[g].rightPositions.length,A+=b,_+=b/3*2)}const E=s.defined(c);let C;E&&(C=c[0].length-3,m+=C,A+=C,C/=3,_+=4*C);const G=m+A,T=new Float64Array(G);let L,D,k,x,N,O,V=0,H=G-1;const I=C/2,R=p.IndexDatatype.createTypedArray(G/3,_+4);let S=0;if(R[S++]=V/3,R[S++]=(H-2)/3,E){a.push(V/3),O=P,N=v;const e=c[0];for(g=0;g<I;g++)O=i.Cartesian3.fromArray(e,3*(I-1-g),O),N=i.Cartesian3.fromArray(e,3*(I+g),N),n.CorridorGeometryLibrary.addAttribute(T,N,V),n.CorridorGeometryLibrary.addAttribute(T,O,void 0,H),D=V/3,x=D+1,L=(H-2)/3,k=L-1,R[S++]=L,R[S++]=k,R[S++]=D,R[S++]=x,V+=3,H-=3}let B=0,M=u[B++],U=u[B++];for(T.set(M,V),T.set(U,H-U.length+1),b=U.length-3,a.push(V/3,(H-2)/3),g=0;g<b;g+=3)D=V/3,x=D+1,L=(H-2)/3,k=L-1,R[S++]=L,R[S++]=k,R[S++]=D,R[S++]=x,V+=3,H-=3;for(g=0;g<f.length;g++){let e;y=f[g];const r=y.leftPositions,l=y.rightPositions;let d,p=w;if(s.defined(r)){for(H-=3,d=k,a.push(x),e=0;e<r.length/3;e++)p=i.Cartesian3.fromArray(r,3*e,p),R[S++]=d-e-1,R[S++]=d-e,n.CorridorGeometryLibrary.addAttribute(T,p,void 0,H),H-=3;a.push(d-Math.floor(r.length/6)),t===o.CornerType.BEVELED&&a.push((H-2)/3+1),V+=3}else{for(V+=3,d=x,a.push(k),e=0;e<l.length/3;e++)p=i.Cartesian3.fromArray(l,3*e,p),R[S++]=d+e,R[S++]=d+e+1,n.CorridorGeometryLibrary.addAttribute(T,p,V),V+=3;a.push(d+Math.floor(l.length/6)),t===o.CornerType.BEVELED&&a.push(V/3-1),H-=3}for(M=u[B++],U=u[B++],M.splice(0,3),U.splice(U.length-3,3),T.set(M,V),T.set(U,H-U.length+1),b=U.length-3,e=0;e<U.length;e+=3)x=V/3,D=x-1,k=(H-2)/3,L=k+1,R[S++]=L,R[S++]=k,R[S++]=D,R[S++]=x,V+=3,H-=3;V-=3,H+=3,a.push(V/3,(H-2)/3)}if(E){V+=3,H-=3,O=P,N=v;const e=c[1];for(g=0;g<I;g++)O=i.Cartesian3.fromArray(e,3*(C-g-1),O),N=i.Cartesian3.fromArray(e,3*g,N),n.CorridorGeometryLibrary.addAttribute(T,O,void 0,H),n.CorridorGeometryLibrary.addAttribute(T,N,V),x=V/3,D=x-1,k=(H-2)/3,L=k+1,R[S++]=L,R[S++]=k,R[S++]=D,R[S++]=x,V+=3,H-=3;a.push(V/3)}else a.push(V/3,(H-2)/3);return R[S++]=V/3,R[S++]=(H-2)/3,h.position=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:T}),{attributes:h,indices:R,wallIndices:a}}function D(e){const t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions,r=e.width,n=s.defaultValue(e.height,0),l=s.defaultValue(e.extrudedHeight,n);this._positions=t,this._ellipsoid=a.Ellipsoid.clone(s.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84)),this._width=r,this._height=Math.max(n,l),this._extrudedHeight=Math.min(n,l),this._cornerType=s.defaultValue(e.cornerType,o.CornerType.ROUNDED),this._granularity=s.defaultValue(e.granularity,f.CesiumMath.RADIANS_PER_DEGREE),this._offsetAttribute=e.offsetAttribute,this._workerName="createCorridorOutlineGeometry",this.packedLength=1+t.length*i.Cartesian3.packedLength+a.Ellipsoid.packedLength+6}D.pack=function(e,t,r){r=s.defaultValue(r,0);const o=e._positions,n=o.length;t[r++]=n;for(let e=0;e<n;++e,r+=i.Cartesian3.packedLength)i.Cartesian3.pack(o[e],t,r);return a.Ellipsoid.pack(e._ellipsoid,t,r),r+=a.Ellipsoid.packedLength,t[r++]=e._width,t[r++]=e._height,t[r++]=e._extrudedHeight,t[r++]=e._cornerType,t[r++]=e._granularity,t[r]=s.defaultValue(e._offsetAttribute,-1),t};const k=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),x={positions:void 0,ellipsoid:k,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,offsetAttribute:void 0};return D.unpack=function(e,t,r){t=s.defaultValue(t,0);const o=e[t++],n=new Array(o);for(let r=0;r<o;++r,t+=i.Cartesian3.packedLength)n[r]=i.Cartesian3.unpack(e,t);const l=a.Ellipsoid.unpack(e,t,k);t+=a.Ellipsoid.packedLength;const d=e[t++],u=e[t++],p=e[t++],f=e[t++],c=e[t++],h=e[t];return s.defined(r)?(r._positions=n,r._ellipsoid=a.Ellipsoid.clone(l,r._ellipsoid),r._width=d,r._height=u,r._extrudedHeight=p,r._cornerType=f,r._granularity=c,r._offsetAttribute=-1===h?void 0:h,r):(x.positions=n,x.width=d,x.height=u,x.extrudedHeight=p,x.cornerType=f,x.granularity=c,x.offsetAttribute=-1===h?void 0:h,new D(x))},D.createGeometry=function(o){let a=o._positions;const d=o._width,h=o._ellipsoid;a=function(e,t){for(let i=0;i<e.length;i++)e[i]=t.scaleToGeodeticSurface(e[i],e[i]);return e}(a,h);const y=e.arrayRemoveDuplicates(a,i.Cartesian3.equalsEpsilon);if(y.length<2||d<=0)return;const g=o._height,b=o._extrudedHeight,m=!f.CesiumMath.equalsEpsilon(g,b,0,f.CesiumMath.EPSILON2),A={ellipsoid:h,positions:y,width:d,cornerType:o._cornerType,granularity:o._granularity,saveAttributes:!1};let _;if(m)A.height=g,A.extrudedHeight=b,A.offsetAttribute=o._offsetAttribute,_=function(e){const t=e.ellipsoid,i=L(n.CorridorGeometryLibrary.computePositions(e),e.cornerType),o=i.wallIndices,a=e.height,d=e.extrudedHeight,f=i.attributes,h=i.indices;let y=f.position.values,g=y.length,b=new Float64Array(g);b.set(y);const m=new Float64Array(2*g);if(y=c.PolygonPipeline.scaleToGeodeticHeight(y,a,t),b=c.PolygonPipeline.scaleToGeodeticHeight(b,d,t),m.set(y),m.set(b,g),f.position.values=m,g/=3,s.defined(e.offsetAttribute)){let t=new Uint8Array(2*g);if(e.offsetAttribute===u.GeometryOffsetAttribute.TOP)t=t.fill(1,0,g);else{const i=e.offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;t=t.fill(i)}f.applyOffset=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}let A;const _=h.length,E=p.IndexDatatype.createTypedArray(m.length/3,2*(_+o.length));E.set(h);let C,G,T=_;for(A=0;A<_;A+=2){const e=h[A],t=h[A+1];E[T++]=e+g,E[T++]=t+g}for(A=0;A<o.length;A++)C=o[A],G=C+g,E[T++]=C,E[T++]=G;return{attributes:f,indices:E}}(A);else{if(_=L(n.CorridorGeometryLibrary.computePositions(A),A.cornerType),_.attributes.position.values=c.PolygonPipeline.scaleToGeodeticHeight(_.attributes.position.values,g,h),s.defined(o._offsetAttribute)){const e=_.attributes.position.values.length,t=o._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,i=new Uint8Array(e/3).fill(t);_.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}}const E=_.attributes,C=t.BoundingSphere.fromVertices(E.position.values,void 0,3);return new l.Geometry({attributes:E,indices:_.indices,primitiveType:l.PrimitiveType.LINES,boundingSphere:C,offsetAttribute:o._offsetAttribute})},function(e,t){return s.defined(t)&&(e=D.unpack(e,t)),e._ellipsoid=a.Ellipsoid.clone(e._ellipsoid),D.createGeometry(e)}}));
