define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./AttributeCompression-a01059cd","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd"],(function(e,t,r,i,a,n,o,s,u,p,d,l,v,f,m,y){"use strict";var c=new i.Cartesian3,h=new i.Cartesian3,C=new i.Cartesian3;function b(e,a,n,o,s){var u,p,d,l,v,f,m,y;if(r.Check.defined("point",e),r.Check.defined("p0",a),r.Check.defined("p1",n),r.Check.defined("p2",o),t.defined(s)||(s=new i.Cartesian3),t.defined(a.z)){if(i.Cartesian3.equalsEpsilon(e,a,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_X,s);if(i.Cartesian3.equalsEpsilon(e,n,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_Y,s);if(i.Cartesian3.equalsEpsilon(e,o,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_Z,s);u=i.Cartesian3.subtract(n,a,c),p=i.Cartesian3.subtract(o,a,h),d=i.Cartesian3.subtract(e,a,C),l=i.Cartesian3.dot(u,u),v=i.Cartesian3.dot(u,p),f=i.Cartesian3.dot(u,d),m=i.Cartesian3.dot(p,p),y=i.Cartesian3.dot(p,d)}else{if(i.Cartesian2.equalsEpsilon(e,a,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_X,s);if(i.Cartesian2.equalsEpsilon(e,n,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_Y,s);if(i.Cartesian2.equalsEpsilon(e,o,i.CesiumMath.EPSILON14))return i.Cartesian3.clone(i.Cartesian3.UNIT_Z,s);u=i.Cartesian2.subtract(n,a,c),p=i.Cartesian2.subtract(o,a,h),d=i.Cartesian2.subtract(e,a,C),l=i.Cartesian2.dot(u,u),v=i.Cartesian2.dot(u,p),f=i.Cartesian2.dot(u,d),m=i.Cartesian2.dot(p,p),y=i.Cartesian2.dot(p,d)}s.y=m*f-v*y,s.z=l*y-v*f;var b=l*m-v*v;return 0!==s.y&&(s.y/=b),0!==s.z&&(s.z/=b),s.x=1-s.y-s.z,s}var w={calculateACMR:function(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var i=e.indices,a=e.maximumIndex,n=t.defaultValue(e.cacheSize,24);if(!t.defined(i))throw new r.DeveloperError("indices is required.");var o=i.length;if(o<3||o%3!==0)throw new r.DeveloperError("indices length must be a multiple of three.");if(a<=0)throw new r.DeveloperError("maximumIndex must be greater than zero.");if(n<3)throw new r.DeveloperError("cacheSize must be greater than two.");if(!t.defined(a)){a=0;var s=0,u=i[s];while(s<o)u>a&&(a=u),++s,u=i[s]}for(var p=[],d=0;d<a+1;d++)p[d]=0;for(var l=n+1,v=0;v<o;++v)l-p[i[v]]>n&&(p[i[v]]=l,++l);return(l-n+1)/(o/3)},tipsify:function(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var i,a=e.indices,n=e.maximumIndex,o=t.defaultValue(e.cacheSize,24);function s(e,t,r,a){while(t.length>=1){var n=t[t.length-1];if(t.splice(t.length-1,1),e[n].numLiveTriangles>0)return n}while(i<a){if(e[i].numLiveTriangles>0)return++i,i-1;++i}return-1}function u(e,t,r,i,a,n,o){var u,p=-1,d=-1,l=0;while(l<r.length){var v=r[l];i[v].numLiveTriangles&&(u=0,a-i[v].timeStamp+2*i[v].numLiveTriangles<=t&&(u=a-i[v].timeStamp),(u>d||-1===d)&&(d=u,p=v)),++l}return-1===p?s(i,n,e,o):p}if(!t.defined(a))throw new r.DeveloperError("indices is required.");var p=a.length;if(p<3||p%3!==0)throw new r.DeveloperError("indices length must be a multiple of three.");if(n<=0)throw new r.DeveloperError("maximumIndex must be greater than zero.");if(o<3)throw new r.DeveloperError("cacheSize must be greater than two.");var d=0,l=0,v=a[l],f=p;if(t.defined(n))d=n+1;else{while(l<f)v>d&&(d=v),++l,v=a[l];if(-1===d)return 0;++d}var m,y=[];for(m=0;m<d;m++)y[m]={numLiveTriangles:0,timeStamp:0,vertexTriangles:[]};l=0;var c=0;while(l<f)y[a[l]].vertexTriangles.push(c),++y[a[l]].numLiveTriangles,y[a[l+1]].vertexTriangles.push(c),++y[a[l+1]].numLiveTriangles,y[a[l+2]].vertexTriangles.push(c),++y[a[l+2]].numLiveTriangles,++c,l+=3;var h=0,C=o+1;i=1;var b,w,g,T,A=[],E=[],D=0,x=[],P=p/3,S=[];for(m=0;m<P;m++)S[m]=!1;while(-1!==h){A=[],w=y[h],T=w.vertexTriangles.length;for(var I=0;I<T;++I)if(c=w.vertexTriangles[I],!S[c]){S[c]=!0,l=c+c+c;for(var N=0;N<3;++N)g=a[l],A.push(g),E.push(g),x[D]=g,++D,b=y[g],--b.numLiveTriangles,C-b.timeStamp>o&&(b.timeStamp=C,++C),++l}h=u(a,o,A,y,C,E,d)}return x}},g={};function T(e,t,r,i,a){e[t++]=r,e[t++]=i,e[t++]=i,e[t++]=a,e[t++]=a,e[t]=r}function A(e){for(var t=e.length,r=t/3*6,i=f.IndexDatatype.createTypedArray(t,r),a=0,n=0;n<t;n+=3,a+=6)T(i,a,e[n],e[n+1],e[n+2]);return i}function E(e){var t=e.length;if(t>=3){var r=6*(t-2),i=f.IndexDatatype.createTypedArray(t,r);T(i,0,e[0],e[1],e[2]);for(var a=6,n=3;n<t;++n,a+=6)T(i,a,e[n-1],e[n],e[n-2]);return i}return new Uint16Array}function D(e){if(e.length>0){for(var t=e.length-1,r=6*(t-1),i=f.IndexDatatype.createTypedArray(t,r),a=e[0],n=0,o=1;o<t;++o,n+=6)T(i,n,a,e[o],e[o+1]);return i}return new Uint16Array}function x(e){var r={};for(var i in e)if(e.hasOwnProperty(i)&&t.defined(e[i])&&t.defined(e[i].values)){var a=e[i];r[i]=new p.GeometryAttribute({componentDatatype:a.componentDatatype,componentsPerAttribute:a.componentsPerAttribute,normalize:a.normalize,values:[]})}return r}function P(e,r,i){for(var a in r)if(r.hasOwnProperty(a)&&t.defined(r[a])&&t.defined(r[a].values))for(var n=r[a],o=0;o<n.componentsPerAttribute;++o)e[a].values.push(n.values[i*n.componentsPerAttribute+o])}g.toWireframe=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var i=e.indices;if(t.defined(i)){switch(e.primitiveType){case d.PrimitiveType.TRIANGLES:e.indices=A(i);break;case d.PrimitiveType.TRIANGLE_STRIP:e.indices=E(i);break;case d.PrimitiveType.TRIANGLE_FAN:e.indices=D(i);break;default:throw new r.DeveloperError("geometry.primitiveType must be TRIANGLES, TRIANGLE_STRIP, or TRIANGLE_FAN.")}e.primitiveType=d.PrimitiveType.LINES}return e},g.createLineSegmentsForVectors=function(e,i,a){if(i=t.defaultValue(i,"normal"),!t.defined(e))throw new r.DeveloperError("geometry is required.");if(!t.defined(e.attributes.position))throw new r.DeveloperError("geometry.attributes.position is required.");if(!t.defined(e.attributes[i]))throw new r.DeveloperError("geometry.attributes must have an attribute with the same name as the attributeName parameter, "+i+".");a=t.defaultValue(a,1e4);for(var o,s=e.attributes.position.values,l=e.attributes[i].values,v=s.length,f=new Float64Array(2*v),m=0,y=0;y<v;y+=3)f[m++]=s[y],f[m++]=s[y+1],f[m++]=s[y+2],f[m++]=s[y]+l[y]*a,f[m++]=s[y+1]+l[y+1]*a,f[m++]=s[y+2]+l[y+2]*a;var c=e.boundingSphere;return t.defined(c)&&(o=new n.BoundingSphere(c.center,c.radius+a)),new p.Geometry({attributes:{position:new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f})},primitiveType:d.PrimitiveType.LINES,boundingSphere:o})},g.createAttributeLocations=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var i,a=["position","positionHigh","positionLow","position3DHigh","position3DLow","position2DHigh","position2DLow","pickColor","normal","st","tangent","bitangent","extrudeDirection","compressedAttributes"],n=e.attributes,o={},s=0,u=a.length;for(i=0;i<u;++i){var p=a[i];t.defined(n[p])&&(o[p]=s++)}for(var d in n)n.hasOwnProperty(d)&&!t.defined(o[d])&&(o[d]=s++);return o},g.reorderForPreVertexCache=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var i=p.Geometry.computeNumberOfVertices(e),a=e.indices;if(t.defined(a)){for(var n=new Int32Array(i),o=0;o<i;o++)n[o]=-1;var s,d=a,l=d.length,v=f.IndexDatatype.createTypedArray(i,l),m=0,y=0,c=0;while(m<l)s=n[d[m]],-1!==s?v[y]=s:(s=d[m],n[s]=c,v[y]=c,++c),++m,++y;e.indices=v;var h=e.attributes;for(var C in h)if(h.hasOwnProperty(C)&&t.defined(h[C])&&t.defined(h[C].values)){var b=h[C],w=b.values,g=0,T=b.componentsPerAttribute,A=u.ComponentDatatype.createTypedArray(b.componentDatatype,c*T);while(g<i){var E=n[g];if(-1!==E)for(var D=0;D<T;D++)A[T*E+D]=w[T*g+D];++g}b.values=A}}return e},g.reorderForPostVertexCache=function(e,i){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var a=e.indices;if(e.primitiveType===d.PrimitiveType.TRIANGLES&&t.defined(a)){for(var n=a.length,o=0,s=0;s<n;s++)a[s]>o&&(o=a[s]);e.indices=w.tipsify({indices:a,maximumIndex:o,cacheSize:i})}return e},g.fitToUnsignedShortIndices=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");if(t.defined(e.indices)&&e.primitiveType!==d.PrimitiveType.TRIANGLES&&e.primitiveType!==d.PrimitiveType.LINES&&e.primitiveType!==d.PrimitiveType.POINTS)throw new r.DeveloperError("geometry.primitiveType must equal to PrimitiveType.TRIANGLES, PrimitiveType.LINES, or PrimitiveType.POINTS.");var a=[],n=p.Geometry.computeNumberOfVertices(e);if(t.defined(e.indices)&&n>=i.CesiumMath.SIXTY_FOUR_KILOBYTES){var o,s=[],u=[],l=0,v=x(e.attributes),f=e.indices,m=f.length;e.primitiveType===d.PrimitiveType.TRIANGLES?o=3:e.primitiveType===d.PrimitiveType.LINES?o=2:e.primitiveType===d.PrimitiveType.POINTS&&(o=1);for(var y=0;y<m;y+=o){for(var c=0;c<o;++c){var h=f[y+c],C=s[h];t.defined(C)||(C=l++,s[h]=C,P(v,e.attributes,h)),u.push(C)}l+o>=i.CesiumMath.SIXTY_FOUR_KILOBYTES&&(a.push(new p.Geometry({attributes:v,indices:u,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV})),s=[],u=[],l=0,v=x(e.attributes))}0!==u.length&&a.push(new p.Geometry({attributes:v,indices:u,primitiveType:e.primitiveType,boundingSphere:e.boundingSphere,boundingSphereCV:e.boundingSphereCV}))}else a.push(e);return a};var S=new i.Cartesian3,I=new a.Cartographic;g.projectTo2D=function(e,a,o,s,d){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");if(!t.defined(a))throw new r.DeveloperError("attributeName is required.");if(!t.defined(o))throw new r.DeveloperError("attributeName3D is required.");if(!t.defined(s))throw new r.DeveloperError("attributeName2D is required.");if(!t.defined(e.attributes[a]))throw new r.DeveloperError("geometry must have attribute matching the attributeName argument: "+a+".");if(e.attributes[a].componentDatatype!==u.ComponentDatatype.DOUBLE)throw new r.DeveloperError("The attribute componentDatatype must be ComponentDatatype.DOUBLE.");var l=e.attributes[a];d=t.defined(d)?d:new n.GeographicProjection;for(var v=d.ellipsoid,f=l.values,m=new Float64Array(f.length),y=0,c=0;c<f.length;c+=3){var h=i.Cartesian3.fromArray(f,c,S),C=v.cartesianToCartographic(h,I);if(!t.defined(C))throw new r.DeveloperError("Could not project point ("+h.x+", "+h.y+", "+h.z+") to 2D.");var b=d.project(C,S);m[y++]=b.x,m[y++]=b.y,m[y++]=b.z}return e.attributes[o]=l,e.attributes[s]=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m}),delete e.attributes[a],e};var N={high:0,low:0};g.encodeAttribute=function(e,i,a,n){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");if(!t.defined(i))throw new r.DeveloperError("attributeName is required.");if(!t.defined(a))throw new r.DeveloperError("attributeHighName is required.");if(!t.defined(n))throw new r.DeveloperError("attributeLowName is required.");if(!t.defined(e.attributes[i]))throw new r.DeveloperError("geometry must have attribute matching the attributeName argument: "+i+".");if(e.attributes[i].componentDatatype!==u.ComponentDatatype.DOUBLE)throw new r.DeveloperError("The attribute componentDatatype must be ComponentDatatype.DOUBLE.");for(var o=e.attributes[i],s=o.values,d=s.length,l=new Float32Array(d),f=new Float32Array(d),m=0;m<d;++m)v.EncodedCartesian3.encode(s[m],N),l[m]=N.high,f[m]=N.low;var y=o.componentsPerAttribute;return e.attributes[a]=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:y,values:l}),e.attributes[n]=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:y,values:f}),delete e.attributes[i],e};var O=new i.Cartesian3;function L(e,r){if(t.defined(r))for(var a=r.values,n=a.length,o=0;o<n;o+=3)i.Cartesian3.unpack(a,o,O),s.Matrix4.multiplyByPoint(e,O,O),i.Cartesian3.pack(O,a,o)}function z(e,r){if(t.defined(r))for(var a=r.values,n=a.length,o=0;o<n;o+=3)i.Cartesian3.unpack(a,o,O),s.Matrix3.multiplyByVector(e,O,O),O=i.Cartesian3.normalize(O,O),i.Cartesian3.pack(O,a,o)}var G=new s.Matrix4,M=new s.Matrix3;function q(e,r){var i,a=e.length,n={},o=e[0][r].attributes;for(i in o)if(o.hasOwnProperty(i)&&t.defined(o[i])&&t.defined(o[i].values)){for(var s=o[i],d=s.values.length,l=!0,v=1;v<a;++v){var f=e[v][r].attributes[i];if(!t.defined(f)||s.componentDatatype!==f.componentDatatype||s.componentsPerAttribute!==f.componentsPerAttribute||s.normalize!==f.normalize){l=!1;break}d+=f.values.length}l&&(n[i]=new p.GeometryAttribute({componentDatatype:s.componentDatatype,componentsPerAttribute:s.componentsPerAttribute,normalize:s.normalize,values:u.ComponentDatatype.createTypedArray(s.componentDatatype,d)}))}return n}g.transformToWorldCoordinates=function(e){if(!t.defined(e))throw new r.DeveloperError("instance is required.");var i=e.modelMatrix;if(s.Matrix4.equals(i,s.Matrix4.IDENTITY))return e;var a=e.geometry.attributes;L(i,a.position),L(i,a.prevPosition),L(i,a.nextPosition),(t.defined(a.normal)||t.defined(a.tangent)||t.defined(a.bitangent))&&(s.Matrix4.inverse(i,G),s.Matrix4.transpose(G,G),s.Matrix4.getMatrix3(G,M),z(M,a.normal),z(M,a.tangent),z(M,a.bitangent));var o=e.geometry.boundingSphere;return t.defined(o)&&(e.geometry.boundingSphere=n.BoundingSphere.transform(o,i,o)),e.modelMatrix=s.Matrix4.clone(s.Matrix4.IDENTITY),e};var R=new i.Cartesian3;function B(e,a){var o,u,l,v,m=e.length,y=e[0].modelMatrix,c=t.defined(e[0][a].indices),h=e[0][a].primitiveType;for(u=1;u<m;++u){if(!s.Matrix4.equals(e[u].modelMatrix,y))throw new r.DeveloperError("All instances must have the same modelMatrix.");if(t.defined(e[u][a].indices)!==c)throw new r.DeveloperError("All instance geometries must have an indices or not have one.");if(e[u][a].primitiveType!==h)throw new r.DeveloperError("All instance geometries must have the same primitiveType.")}var C,b,w,g,T=q(e,a);for(o in T)if(T.hasOwnProperty(o))for(C=T[o].values,v=0,u=0;u<m;++u)for(b=e[u][a].attributes[o].values,w=b.length,l=0;l<w;++l)C[v++]=b[l];if(c){var A=0;for(u=0;u<m;++u)A+=e[u][a].indices.length;var E=p.Geometry.computeNumberOfVertices(new p.Geometry({attributes:T,primitiveType:d.PrimitiveType.POINTS})),D=f.IndexDatatype.createTypedArray(E,A),x=0,P=0;for(u=0;u<m;++u){var S=e[u][a].indices,I=S.length;for(v=0;v<I;++v)D[x++]=P+S[v];P+=p.Geometry.computeNumberOfVertices(e[u][a])}g=D}var N,O=new i.Cartesian3,L=0;for(u=0;u<m;++u){if(N=e[u][a].boundingSphere,!t.defined(N)){O=void 0;break}i.Cartesian3.add(N.center,O,O)}if(t.defined(O))for(i.Cartesian3.divideByScalar(O,m,O),u=0;u<m;++u){N=e[u][a].boundingSphere;var z=i.Cartesian3.magnitude(i.Cartesian3.subtract(N.center,O,R))+N.radius;z>L&&(L=z)}return new p.Geometry({attributes:T,indices:g,primitiveType:h,boundingSphere:t.defined(O)?new n.BoundingSphere(O,L):void 0})}g.combineInstances=function(e){if(!t.defined(e)||e.length<1)throw new r.DeveloperError("instances is required and must have length greater than zero.");for(var i=[],a=[],n=e.length,o=0;o<n;++o){var s=e[o];t.defined(s.geometry)?i.push(s):t.defined(s.westHemisphereGeometry)&&t.defined(s.eastHemisphereGeometry)&&a.push(s)}var u=[];return i.length>0&&u.push(B(i,"geometry")),a.length>0&&(u.push(B(a,"westHemisphereGeometry")),u.push(B(a,"eastHemisphereGeometry"))),u};var k=new i.Cartesian3,V=new i.Cartesian3,F=new i.Cartesian3,_=new i.Cartesian3;g.computeNormal=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");if(!t.defined(e.attributes.position)||!t.defined(e.attributes.position.values))throw new r.DeveloperError("geometry.attributes.position.values is required.");if(!t.defined(e.indices))throw new r.DeveloperError("geometry.indices is required.");if(e.indices.length<2||e.indices.length%3!==0)throw new r.DeveloperError("geometry.indices length must be greater than 0 and be a multiple of 3.");if(e.primitiveType!==d.PrimitiveType.TRIANGLES)throw new r.DeveloperError("geometry.primitiveType must be PrimitiveType.TRIANGLES.");var a,n=e.indices,o=e.attributes,s=o.position.values,l=o.position.values.length/3,v=n.length,f=new Array(l),m=new Array(v/3),y=new Array(v);for(a=0;a<l;a++)f[a]={indexOffset:0,count:0,currentCount:0};var c=0;for(a=0;a<v;a+=3){var h=n[a],C=n[a+1],b=n[a+2],w=3*h,g=3*C,T=3*b;V.x=s[w],V.y=s[w+1],V.z=s[w+2],F.x=s[g],F.y=s[g+1],F.z=s[g+2],_.x=s[T],_.y=s[T+1],_.z=s[T+2],f[h].count++,f[C].count++,f[b].count++,i.Cartesian3.subtract(F,V,F),i.Cartesian3.subtract(_,V,_),m[c]=i.Cartesian3.cross(F,_,new i.Cartesian3),c++}var A,E=0;for(a=0;a<l;a++)f[a].indexOffset+=E,E+=f[a].count;for(c=0,a=0;a<v;a+=3){A=f[n[a]];var D=A.indexOffset+A.currentCount;y[D]=c,A.currentCount++,A=f[n[a+1]],D=A.indexOffset+A.currentCount,y[D]=c,A.currentCount++,A=f[n[a+2]],D=A.indexOffset+A.currentCount,y[D]=c,A.currentCount++,c++}var x=new Float32Array(3*l);for(a=0;a<l;a++){var P=3*a;if(A=f[a],i.Cartesian3.clone(i.Cartesian3.ZERO,k),A.count>0){for(c=0;c<A.count;c++)i.Cartesian3.add(k,m[y[A.indexOffset+c]],k);i.Cartesian3.equalsEpsilon(i.Cartesian3.ZERO,k,i.CesiumMath.EPSILON10)&&i.Cartesian3.clone(m[y[A.indexOffset]],k)}i.Cartesian3.equalsEpsilon(i.Cartesian3.ZERO,k,i.CesiumMath.EPSILON10)&&(k.z=1),i.Cartesian3.normalize(k,k),x[P]=k.x,x[P+1]=k.y,x[P+2]=k.z}return e.attributes.normal=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:x}),e};var U=new i.Cartesian3,Y=new i.Cartesian3,Z=new i.Cartesian3;g.computeTangentAndBitangent=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var a=e.attributes,n=e.indices;if(!t.defined(a.position)||!t.defined(a.position.values))throw new r.DeveloperError("geometry.attributes.position.values is required.");if(!t.defined(a.normal)||!t.defined(a.normal.values))throw new r.DeveloperError("geometry.attributes.normal.values is required.");if(!t.defined(a.st)||!t.defined(a.st.values))throw new r.DeveloperError("geometry.attributes.st.values is required.");if(!t.defined(n))throw new r.DeveloperError("geometry.indices is required.");if(n.length<2||n.length%3!==0)throw new r.DeveloperError("geometry.indices length must be greater than 0 and be a multiple of 3.");if(e.primitiveType!==d.PrimitiveType.TRIANGLES)throw new r.DeveloperError("geometry.primitiveType must be PrimitiveType.TRIANGLES.");var o,s,l,v,f=e.attributes.position.values,m=e.attributes.normal.values,y=e.attributes.st.values,c=e.attributes.position.values.length/3,h=n.length,C=new Array(3*c);for(o=0;o<C.length;o++)C[o]=0;for(o=0;o<h;o+=3){var b=n[o],w=n[o+1],g=n[o+2];s=3*b,l=3*w,v=3*g;var T=2*b,A=2*w,E=2*g,D=f[s],x=f[s+1],P=f[s+2],S=y[T],I=y[T+1],N=y[A+1]-I,O=y[E+1]-I,L=1/((y[A]-S)*O-(y[E]-S)*N),z=(O*(f[l]-D)-N*(f[v]-D))*L,G=(O*(f[l+1]-x)-N*(f[v+1]-x))*L,M=(O*(f[l+2]-P)-N*(f[v+2]-P))*L;C[s]+=z,C[s+1]+=G,C[s+2]+=M,C[l]+=z,C[l+1]+=G,C[l+2]+=M,C[v]+=z,C[v+1]+=G,C[v+2]+=M}var q=new Float32Array(3*c),R=new Float32Array(3*c);for(o=0;o<c;o++){s=3*o,l=s+1,v=s+2;var B=i.Cartesian3.fromArray(m,s,U),k=i.Cartesian3.fromArray(C,s,Z),V=i.Cartesian3.dot(B,k);i.Cartesian3.multiplyByScalar(B,V,Y),i.Cartesian3.normalize(i.Cartesian3.subtract(k,Y,k),k),q[s]=k.x,q[l]=k.y,q[v]=k.z,i.Cartesian3.normalize(i.Cartesian3.cross(B,k,k),k),R[s]=k.x,R[l]=k.y,R[v]=k.z}return e.attributes.tangent=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:q}),e.attributes.bitangent=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R}),e};var H=new i.Cartesian2,W=new i.Cartesian3,X=new i.Cartesian3,j=new i.Cartesian3,J=new i.Cartesian2;function K(e){if(t.defined(e.indices))return e;var i=p.Geometry.computeNumberOfVertices(e);if(i<3)throw new r.DeveloperError("The number of vertices must be at least three.");if(i%3!==0)throw new r.DeveloperError("The number of vertices must be a multiple of three.");for(var a=f.IndexDatatype.createTypedArray(i,i),n=0;n<i;++n)a[n]=n;return e.indices=a,e}function Q(e){var t=p.Geometry.computeNumberOfVertices(e);if(t<3)throw new r.DeveloperError("The number of vertices must be at least three.");var i=f.IndexDatatype.createTypedArray(t,3*(t-2));i[0]=1,i[1]=0,i[2]=2;for(var a=3,n=3;n<t;++n)i[a++]=n-1,i[a++]=0,i[a++]=n;return e.indices=i,e.primitiveType=d.PrimitiveType.TRIANGLES,e}function $(e){var t=p.Geometry.computeNumberOfVertices(e);if(t<3)throw new r.DeveloperError("The number of vertices must be at least 3.");var i=f.IndexDatatype.createTypedArray(t,3*(t-2));i[0]=0,i[1]=1,i[2]=2,t>3&&(i[3]=0,i[4]=2,i[5]=3);for(var a=6,n=3;n<t-1;n+=2)i[a++]=n,i[a++]=n-1,i[a++]=n+1,n+2<t&&(i[a++]=n,i[a++]=n+1,i[a++]=n+2);return e.indices=i,e.primitiveType=d.PrimitiveType.TRIANGLES,e}function ee(e){if(t.defined(e.indices))return e;var i=p.Geometry.computeNumberOfVertices(e);if(i<2)throw new r.DeveloperError("The number of vertices must be at least two.");if(i%2!==0)throw new r.DeveloperError("The number of vertices must be a multiple of 2.");for(var a=f.IndexDatatype.createTypedArray(i,i),n=0;n<i;++n)a[n]=n;return e.indices=a,e}function te(e){var t=p.Geometry.computeNumberOfVertices(e);if(t<2)throw new r.DeveloperError("The number of vertices must be at least two.");var i=f.IndexDatatype.createTypedArray(t,2*(t-1));i[0]=0,i[1]=1;for(var a=2,n=2;n<t;++n)i[a++]=n-1,i[a++]=n;return e.indices=i,e.primitiveType=d.PrimitiveType.LINES,e}function re(e){var t=p.Geometry.computeNumberOfVertices(e);if(t<2)throw new r.DeveloperError("The number of vertices must be at least two.");var i=f.IndexDatatype.createTypedArray(t,2*t);i[0]=0,i[1]=1;for(var a=2,n=2;n<t;++n)i[a++]=n-1,i[a++]=n;return i[a++]=t-1,i[a]=0,e.indices=i,e.primitiveType=d.PrimitiveType.LINES,e}function ie(e){switch(e.primitiveType){case d.PrimitiveType.TRIANGLE_FAN:return Q(e);case d.PrimitiveType.TRIANGLE_STRIP:return $(e);case d.PrimitiveType.TRIANGLES:return K(e);case d.PrimitiveType.LINE_STRIP:return te(e);case d.PrimitiveType.LINE_LOOP:return re(e);case d.PrimitiveType.LINES:return ee(e)}return e}function ae(e,t){Math.abs(e.y)<i.CesiumMath.EPSILON6&&(e.y=t?-i.CesiumMath.EPSILON6:i.CesiumMath.EPSILON6)}function ne(e,t,r){if(0!==e.y&&0!==t.y&&0!==r.y)return ae(e,e.y<0),ae(t,t.y<0),void ae(r,r.y<0);var a,n=Math.abs(e.y),o=Math.abs(t.y),s=Math.abs(r.y);a=n>o?n>s?i.CesiumMath.sign(e.y):i.CesiumMath.sign(r.y):o>s?i.CesiumMath.sign(t.y):i.CesiumMath.sign(r.y);var u=a<0;ae(e,u),ae(t,u),ae(r,u)}g.compressVertices=function(e){if(!t.defined(e))throw new r.DeveloperError("geometry is required.");var a,n,o=e.attributes.extrudeDirection;if(t.defined(o)){var s=o.values;n=s.length/3;var d=new Float32Array(2*n),v=0;for(a=0;a<n;++a)i.Cartesian3.fromArray(s,3*a,W),i.Cartesian3.equals(W,i.Cartesian3.ZERO)?v+=2:(J=l.AttributeCompression.octEncodeInRange(W,65535,J),d[v++]=J.x,d[v++]=J.y);return e.attributes.compressedAttributes=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:d}),delete e.attributes.extrudeDirection,e}var f=e.attributes.normal,m=e.attributes.st,y=t.defined(f),c=t.defined(m);if(!y&&!c)return e;var h,C,b,w,g=e.attributes.tangent,T=e.attributes.bitangent,A=t.defined(g),E=t.defined(T);y&&(h=f.values),c&&(C=m.values),A&&(b=g.values),E&&(w=T.values);var D=y?h.length:C.length,x=y?3:2;n=D/x;var P=n,S=c&&y?2:1;S+=A||E?1:0,P*=S;var I=new Float32Array(P),N=0;for(a=0;a<n;++a){c&&(i.Cartesian2.fromArray(C,2*a,H),I[N++]=l.AttributeCompression.compressTextureCoordinates(H));var O=3*a;y&&t.defined(b)&&t.defined(w)?(i.Cartesian3.fromArray(h,O,W),i.Cartesian3.fromArray(b,O,X),i.Cartesian3.fromArray(w,O,j),l.AttributeCompression.octPack(W,X,j,H),I[N++]=H.x,I[N++]=H.y):(y&&(i.Cartesian3.fromArray(h,O,W),I[N++]=l.AttributeCompression.octEncodeFloat(W)),A&&(i.Cartesian3.fromArray(b,O,W),I[N++]=l.AttributeCompression.octEncodeFloat(W)),E&&(i.Cartesian3.fromArray(w,O,W),I[N++]=l.AttributeCompression.octEncodeFloat(W)))}return e.attributes.compressedAttributes=new p.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:S,values:I}),y&&delete e.attributes.normal,c&&delete e.attributes.st,E&&delete e.attributes.bitangent,A&&delete e.attributes.tangent,e};var oe=new i.Cartesian3;function se(e,t,r,a){i.Cartesian3.add(e,i.Cartesian3.multiplyByScalar(i.Cartesian3.subtract(t,e,oe),e.y/(e.y-t.y),oe),r),i.Cartesian3.clone(r,a),ae(r,!0),ae(a,!1)}var ue=new i.Cartesian3,pe=new i.Cartesian3,de=new i.Cartesian3,le=new i.Cartesian3,ve={positions:new Array(7),indices:new Array(9)};function fe(e,t,r){if(!(e.x>=0||t.x>=0||r.x>=0)){ne(e,t,r);var i=e.y<0,a=t.y<0,n=r.y<0,o=0;o+=i?1:0,o+=a?1:0,o+=n?1:0;var s=ve.indices;1===o?(s[1]=3,s[2]=4,s[5]=6,s[7]=6,s[8]=5,i?(se(e,t,ue,de),se(e,r,pe,le),s[0]=0,s[3]=1,s[4]=2,s[6]=1):a?(se(t,r,ue,de),se(t,e,pe,le),s[0]=1,s[3]=2,s[4]=0,s[6]=2):n&&(se(r,e,ue,de),se(r,t,pe,le),s[0]=2,s[3]=0,s[4]=1,s[6]=0)):2===o&&(s[2]=4,s[4]=4,s[5]=3,s[7]=5,s[8]=6,i?a?n||(se(r,e,ue,de),se(r,t,pe,le),s[0]=0,s[1]=1,s[3]=0,s[6]=2):(se(t,r,ue,de),se(t,e,pe,le),s[0]=2,s[1]=0,s[3]=2,s[6]=1):(se(e,t,ue,de),se(e,r,pe,le),s[0]=1,s[1]=2,s[3]=1,s[6]=0));var u=ve.positions;return u[0]=e,u[1]=t,u[2]=r,u.length=3,1!==o&&2!==o||(u[3]=ue,u[4]=pe,u[5]=de,u[6]=le,u.length=7),ve}}function me(e,r){var i=e.attributes;if(0!==i.position.values.length){for(var a in i)if(i.hasOwnProperty(a)&&t.defined(i[a])&&t.defined(i[a].values)){var o=i[a];o.values=u.ComponentDatatype.createTypedArray(o.componentDatatype,o.values)}var s=p.Geometry.computeNumberOfVertices(e);return e.indices=f.IndexDatatype.createTypedArray(s,e.indices),r&&(e.boundingSphere=n.BoundingSphere.fromVertices(i.position.values)),e}}function ye(e){var r=e.attributes,i={};for(var a in r)if(r.hasOwnProperty(a)&&t.defined(r[a])&&t.defined(r[a].values)){var n=r[a];i[a]=new p.GeometryAttribute({componentDatatype:n.componentDatatype,componentsPerAttribute:n.componentsPerAttribute,normalize:n.normalize,values:[]})}return new p.Geometry({attributes:i,indices:[],primitiveType:e.primitiveType})}function ce(e,r,i){var a=t.defined(e.geometry.boundingSphere);r=me(r,a),i=me(i,a),t.defined(i)&&!t.defined(r)?e.geometry=i:!t.defined(i)&&t.defined(r)?e.geometry=r:(e.westHemisphereGeometry=r,e.eastHemisphereGeometry=i,e.geometry=void 0)}function he(e,t){var r=new e,i=new e,a=new e;return function(n,o,s,u,p,d,l,v){var f=e.fromArray(p,n*t,r),m=e.fromArray(p,o*t,i),y=e.fromArray(p,s*t,a);e.multiplyByScalar(f,u.x,f),e.multiplyByScalar(m,u.y,m),e.multiplyByScalar(y,u.z,y);var c=e.add(f,m,f);e.add(c,y,c),v&&e.normalize(c,c),e.pack(c,d,l*t)}}var Ce=he(s.Cartesian4,4),be=he(i.Cartesian3,3),we=he(i.Cartesian2,2),ge=function(e,t,r,a,n,o,s){var u=n[e]*a.x,p=n[t]*a.y,d=n[r]*a.z;o[s]=u+p+d>i.CesiumMath.EPSILON6?1:0},Te=new i.Cartesian3,Ae=new i.Cartesian3,Ee=new i.Cartesian3,De=new i.Cartesian3;function xe(e,r,a,n,o,s,u,p,d,l,v,f,m,y,c,h){if(t.defined(s)||t.defined(u)||t.defined(p)||t.defined(d)||t.defined(l)||0!==y){var C=i.Cartesian3.fromArray(o,3*e,Te),w=i.Cartesian3.fromArray(o,3*r,Ae),g=i.Cartesian3.fromArray(o,3*a,Ee),T=b(n,C,w,g,De);if(t.defined(s)&&be(e,r,a,T,s,f.normal.values,h,!0),t.defined(l)){var A,E=i.Cartesian3.fromArray(l,3*e,Te),D=i.Cartesian3.fromArray(l,3*r,Ae),x=i.Cartesian3.fromArray(l,3*a,Ee);i.Cartesian3.multiplyByScalar(E,T.x,E),i.Cartesian3.multiplyByScalar(D,T.y,D),i.Cartesian3.multiplyByScalar(x,T.z,x),i.Cartesian3.equals(E,i.Cartesian3.ZERO)&&i.Cartesian3.equals(D,i.Cartesian3.ZERO)&&i.Cartesian3.equals(x,i.Cartesian3.ZERO)?(A=Te,A.x=0,A.y=0,A.z=0):(A=i.Cartesian3.add(E,D,E),i.Cartesian3.add(A,x,A),i.Cartesian3.normalize(A,A)),i.Cartesian3.pack(A,f.extrudeDirection.values,3*h)}if(t.defined(v)&&ge(e,r,a,T,v,f.applyOffset.values,h),t.defined(u)&&be(e,r,a,T,u,f.tangent.values,h,!0),t.defined(p)&&be(e,r,a,T,p,f.bitangent.values,h,!0),t.defined(d)&&we(e,r,a,T,d,f.st.values,h),y>0)for(var P=0;P<y;P++){var S=m[P];Pe(e,r,a,T,h,c[S],f[S])}}}function Pe(e,t,r,i,a,n,o){var s=n.componentsPerAttribute,u=n.values,p=o.values;switch(s){case 4:Ce(e,t,r,i,u,p,a,!1);break;case 3:be(e,t,r,i,u,p,a,!1);break;case 2:we(e,t,r,i,u,p,a,!1);break;default:p[a]=u[e]*i.x+u[t]*i.y+u[r]*i.z}}function Se(e,t,r,i,a,n){var o=e.position.values.length/3;if(-1!==a){var s=i[a],u=r[s];return-1===u?(r[s]=o,e.position.values.push(n.x,n.y,n.z),t.push(o),o):(t.push(u),u)}return e.position.values.push(n.x,n.y,n.z),t.push(o),o}var Ie={position:!0,normal:!0,bitangent:!0,tangent:!0,st:!0,extrudeDirection:!0,applyOffset:!0};function Ne(e){var r=e.geometry,a=r.attributes,n=a.position.values,o=t.defined(a.normal)?a.normal.values:void 0,s=t.defined(a.bitangent)?a.bitangent.values:void 0,u=t.defined(a.tangent)?a.tangent.values:void 0,p=t.defined(a.st)?a.st.values:void 0,d=t.defined(a.extrudeDirection)?a.extrudeDirection.values:void 0,l=t.defined(a.applyOffset)?a.applyOffset.values:void 0,v=r.indices,f=[];for(var m in a)a.hasOwnProperty(m)&&!Ie[m]&&t.defined(a[m])&&f.push(m);var y,c,h,C,b,w=f.length,g=ye(r),T=ye(r),A=[];A.length=n.length/3;var E=[];for(E.length=n.length/3,b=0;b<A.length;++b)A[b]=-1,E[b]=-1;var D=v.length;for(b=0;b<D;b+=3){var x=v[b],P=v[b+1],S=v[b+2],I=i.Cartesian3.fromArray(n,3*x),N=i.Cartesian3.fromArray(n,3*P),O=i.Cartesian3.fromArray(n,3*S),L=fe(I,N,O);if(t.defined(L)&&L.positions.length>3)for(var z=L.positions,G=L.indices,M=G.length,q=0;q<M;++q){var R=G[q],B=z[R];B.y<0?(y=T.attributes,c=T.indices,h=A):(y=g.attributes,c=g.indices,h=E),C=Se(y,c,h,v,R<3?b+R:-1,B),xe(x,P,S,B,n,o,u,s,p,d,l,y,f,w,a,C)}else t.defined(L)&&(I=L.positions[0],N=L.positions[1],O=L.positions[2]),I.y<0?(y=T.attributes,c=T.indices,h=A):(y=g.attributes,c=g.indices,h=E),C=Se(y,c,h,v,b,I),xe(x,P,S,I,n,o,u,s,p,d,l,y,f,w,a,C),C=Se(y,c,h,v,b+1,N),xe(x,P,S,N,n,o,u,s,p,d,l,y,f,w,a,C),C=Se(y,c,h,v,b+2,O),xe(x,P,S,O,n,o,u,s,p,d,l,y,f,w,a,C)}ce(e,T,g)}var Oe=y.Plane.fromPointNormal(i.Cartesian3.ZERO,i.Cartesian3.UNIT_Y),Le=new i.Cartesian3,ze=new i.Cartesian3;function Ge(e,r,a,n,o,s,u){if(t.defined(u)){var p=i.Cartesian3.fromArray(n,3*e,Te);i.Cartesian3.equalsEpsilon(p,a,i.CesiumMath.EPSILON10)?s.applyOffset.values[o]=u[e]:s.applyOffset.values[o]=u[r]}}function Me(e){var r,a=e.geometry,n=a.attributes,o=n.position.values,s=t.defined(n.applyOffset)?n.applyOffset.values:void 0,u=a.indices,p=ye(a),d=ye(a),l=u.length,v=[];v.length=o.length/3;var f=[];for(f.length=o.length/3,r=0;r<v.length;++r)v[r]=-1,f[r]=-1;for(r=0;r<l;r+=2){var y,c=u[r],h=u[r+1],C=i.Cartesian3.fromArray(o,3*c,Te),b=i.Cartesian3.fromArray(o,3*h,Ae);Math.abs(C.y)<i.CesiumMath.EPSILON6&&(C.y<0?C.y=-i.CesiumMath.EPSILON6:C.y=i.CesiumMath.EPSILON6),Math.abs(b.y)<i.CesiumMath.EPSILON6&&(b.y<0?b.y=-i.CesiumMath.EPSILON6:b.y=i.CesiumMath.EPSILON6);var w=p.attributes,g=p.indices,T=f,A=d.attributes,E=d.indices,D=v,x=m.IntersectionTests.lineSegmentPlane(C,b,Oe,Ee);if(t.defined(x)){var P=i.Cartesian3.multiplyByScalar(i.Cartesian3.UNIT_Y,5*i.CesiumMath.EPSILON9,Le);C.y<0&&(i.Cartesian3.negate(P,P),w=d.attributes,g=d.indices,T=v,A=p.attributes,E=p.indices,D=f);var S=i.Cartesian3.add(x,P,ze);y=Se(w,g,T,u,r,C),Ge(c,h,C,o,y,w,s),y=Se(w,g,T,u,-1,S),Ge(c,h,S,o,y,w,s),i.Cartesian3.negate(P,P),i.Cartesian3.add(x,P,S),y=Se(A,E,D,u,-1,S),Ge(c,h,S,o,y,A,s),y=Se(A,E,D,u,r+1,b),Ge(c,h,b,o,y,A,s)}else{var I,N,O;C.y<0?(I=d.attributes,N=d.indices,O=v):(I=p.attributes,N=p.indices,O=f),y=Se(I,N,O,u,r,C),Ge(c,h,C,o,y,I,s),y=Se(I,N,O,u,r+1,b),Ge(c,h,b,o,y,I,s)}}ce(e,d,p)}var qe=new i.Cartesian2,Re=new i.Cartesian2,Be=new i.Cartesian3,ke=new i.Cartesian3,Ve=new i.Cartesian3,Fe=new i.Cartesian3,_e=new i.Cartesian3,Ue=new i.Cartesian3,Ye=new s.Cartesian4;function Ze(e){for(var t=e.attributes,r=t.position.values,a=t.prevPosition.values,n=t.nextPosition.values,o=r.length,s=0;s<o;s+=3){var u=i.Cartesian3.unpack(r,s,Be);if(!(u.x>0)){var p=i.Cartesian3.unpack(a,s,ke);(u.y<0&&p.y>0||u.y>0&&p.y<0)&&(s-3>0?(a[s]=r[s-3],a[s+1]=r[s-2],a[s+2]=r[s-1]):i.Cartesian3.pack(u,a,s));var d=i.Cartesian3.unpack(n,s,Ve);(u.y<0&&d.y>0||u.y>0&&d.y<0)&&(s+3<o?(n[s]=r[s+3],n[s+1]=r[s+4],n[s+2]=r[s+5]):i.Cartesian3.pack(u,n,s))}}}var He=5*i.CesiumMath.EPSILON9,We=i.CesiumMath.EPSILON6;function Xe(e){var r,a,n,o=e.geometry,u=o.attributes,p=u.position.values,d=u.prevPosition.values,l=u.nextPosition.values,v=u.expandAndWidth.values,f=t.defined(u.st)?u.st.values:void 0,y=t.defined(u.color)?u.color.values:void 0,c=ye(o),h=ye(o),C=!1,b=p.length/3;for(r=0;r<b;r+=4){var w=r,g=r+2,T=i.Cartesian3.fromArray(p,3*w,Be),A=i.Cartesian3.fromArray(p,3*g,ke);if(Math.abs(T.y)<We)for(T.y=We*(A.y<0?-1:1),p[3*r+1]=T.y,p[3*(r+1)+1]=T.y,a=3*w;a<3*w+12;a+=3)d[a]=p[3*r],d[a+1]=p[3*r+1],d[a+2]=p[3*r+2];if(Math.abs(A.y)<We)for(A.y=We*(T.y<0?-1:1),p[3*(r+2)+1]=A.y,p[3*(r+3)+1]=A.y,a=3*w;a<3*w+12;a+=3)l[a]=p[3*(r+2)],l[a+1]=p[3*(r+2)+1],l[a+2]=p[3*(r+2)+2];var E=c.attributes,D=c.indices,x=h.attributes,P=h.indices,S=m.IntersectionTests.lineSegmentPlane(T,A,Oe,Fe);if(t.defined(S)){C=!0;var I=i.Cartesian3.multiplyByScalar(i.Cartesian3.UNIT_Y,He,_e);T.y<0&&(i.Cartesian3.negate(I,I),E=h.attributes,D=h.indices,x=c.attributes,P=c.indices);var N=i.Cartesian3.add(S,I,Ue);E.position.values.push(T.x,T.y,T.z,T.x,T.y,T.z),E.position.values.push(N.x,N.y,N.z),E.position.values.push(N.x,N.y,N.z),E.prevPosition.values.push(d[3*w],d[3*w+1],d[3*w+2]),E.prevPosition.values.push(d[3*w+3],d[3*w+4],d[3*w+5]),E.prevPosition.values.push(T.x,T.y,T.z,T.x,T.y,T.z),E.nextPosition.values.push(N.x,N.y,N.z),E.nextPosition.values.push(N.x,N.y,N.z),E.nextPosition.values.push(N.x,N.y,N.z),E.nextPosition.values.push(N.x,N.y,N.z),i.Cartesian3.negate(I,I),i.Cartesian3.add(S,I,N),x.position.values.push(N.x,N.y,N.z),x.position.values.push(N.x,N.y,N.z),x.position.values.push(A.x,A.y,A.z,A.x,A.y,A.z),x.prevPosition.values.push(N.x,N.y,N.z),x.prevPosition.values.push(N.x,N.y,N.z),x.prevPosition.values.push(N.x,N.y,N.z),x.prevPosition.values.push(N.x,N.y,N.z),x.nextPosition.values.push(A.x,A.y,A.z,A.x,A.y,A.z),x.nextPosition.values.push(l[3*g],l[3*g+1],l[3*g+2]),x.nextPosition.values.push(l[3*g+3],l[3*g+4],l[3*g+5]);var O=i.Cartesian2.fromArray(v,2*w,qe),L=Math.abs(O.y);E.expandAndWidth.values.push(-1,L,1,L),E.expandAndWidth.values.push(-1,-L,1,-L),x.expandAndWidth.values.push(-1,L,1,L),x.expandAndWidth.values.push(-1,-L,1,-L);var z=i.Cartesian3.magnitudeSquared(i.Cartesian3.subtract(S,T,Ve));if(z/=i.Cartesian3.magnitudeSquared(i.Cartesian3.subtract(A,T,Ve)),t.defined(y)){var G=s.Cartesian4.fromArray(y,4*w,Ye),M=s.Cartesian4.fromArray(y,4*g,Ye),q=i.CesiumMath.lerp(G.x,M.x,z),R=i.CesiumMath.lerp(G.y,M.y,z),B=i.CesiumMath.lerp(G.z,M.z,z),k=i.CesiumMath.lerp(G.w,M.w,z);for(a=4*w;a<4*w+8;++a)E.color.values.push(y[a]);for(E.color.values.push(q,R,B,k),E.color.values.push(q,R,B,k),x.color.values.push(q,R,B,k),x.color.values.push(q,R,B,k),a=4*g;a<4*g+8;++a)x.color.values.push(y[a])}if(t.defined(f)){var V=i.Cartesian2.fromArray(f,2*w,qe),F=i.Cartesian2.fromArray(f,2*(r+3),Re),_=i.CesiumMath.lerp(V.x,F.x,z);for(a=2*w;a<2*w+4;++a)E.st.values.push(f[a]);for(E.st.values.push(_,V.y),E.st.values.push(_,F.y),x.st.values.push(_,V.y),x.st.values.push(_,F.y),a=2*g;a<2*g+4;++a)x.st.values.push(f[a])}n=E.position.values.length/3-4,D.push(n,n+2,n+1),D.push(n+1,n+2,n+3),n=x.position.values.length/3-4,P.push(n,n+2,n+1),P.push(n+1,n+2,n+3)}else{var U,Y;for(T.y<0?(U=h.attributes,Y=h.indices):(U=c.attributes,Y=c.indices),U.position.values.push(T.x,T.y,T.z),U.position.values.push(T.x,T.y,T.z),U.position.values.push(A.x,A.y,A.z),U.position.values.push(A.x,A.y,A.z),a=3*r;a<3*r+12;++a)U.prevPosition.values.push(d[a]),U.nextPosition.values.push(l[a]);for(a=2*r;a<2*r+8;++a)U.expandAndWidth.values.push(v[a]),t.defined(f)&&U.st.values.push(f[a]);if(t.defined(y))for(a=4*r;a<4*r+16;++a)U.color.values.push(y[a]);n=U.position.values.length/3-4,Y.push(n,n+2,n+1),Y.push(n+1,n+2,n+3)}}C&&(Ze(h),Ze(c)),ce(e,h,c)}g.splitLongitude=function(e){if(!t.defined(e))throw new r.DeveloperError("instance is required.");var i=e.geometry,a=i.boundingSphere;if(t.defined(a)){var s=a.center.x-a.radius;if(s>0||n.BoundingSphere.intersectPlane(a,y.Plane.ORIGIN_ZX_PLANE)!==o.Intersect.INTERSECTING)return e}if(i.geometryType!==p.GeometryType.NONE)switch(i.geometryType){case p.GeometryType.POLYLINES:Xe(e);break;case p.GeometryType.TRIANGLES:Ne(e);break;case p.GeometryType.LINES:Me(e);break}else ie(i),i.primitiveType===d.PrimitiveType.TRIANGLES?Ne(e):i.primitiveType===d.PrimitiveType.LINES&&Me(e);return e},e.GeometryPipeline=g}));