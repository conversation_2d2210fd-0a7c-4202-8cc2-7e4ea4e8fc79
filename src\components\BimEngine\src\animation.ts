import EngineController from '../index'
const BimEngine = window.BimEngine

let animList = []
const getAnimationList = (_this: EngineController) => {
  if (animList.length === 0) {
    const animServerData = _this.currentProject.getModelAnimationListData()
    const animationManager = new BimEngine.ModelAnimManager(_this.viewer)
    animationManager.formatToClientData(_this.viewer.viewer, animServerData)
    animList = animationManager.animationList || []
  }
  console.log(animList)
}
export const playCrushAnimation = (_this: EngineController) => {
  _this.gotoViewportByName('碰撞视角')
  getAnimationList(_this)

  animList.forEach((animItem) => {
    if (['碰撞车', '碰撞车2'].includes(animItem.name)) {
      animItem.playAnimation()
    }
  })
}

export const stopCrushAnimation = (_this: EngineController) => {
  getAnimationList(_this)
  animList.forEach((animItem) => {
    if (['碰撞车', '碰撞车2'].includes(animItem.name)) {
      animItem.stopAnimation()
    }
  })
}
