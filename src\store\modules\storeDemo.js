/*
 * @Description: 百度地图相关方法
 * @Autor: qian
 * @Date: 2023-06-19 17:53:37
 * @LastEditors: wangjialing
 * @LastEditTime: 2023-08-28 16:38:10
 */
import { defineStore } from 'pinia'

const useScreenPageStore = defineStore('storeDemo', {
  state: () => ({
    activePage: 'RoadPlan',
    testObj: {
      user: [
        { id: 1, name: '张三', age: 18 },
        { id: 2, name: '李四', age: 20 }
      ]
    }
  }),
  getters: {
    getUser0Age: (state) => {
      return state.testObj.user[0].age
    },
    getUserById: (state) => {
      return (id) => {
        return state.testObj.user.find((_) => _.id === id)
      }
    }
  },
  actions: {
    // 当前激活的菜单
    setActivePage(activePage) {
      this.activePage = activePage
    },
    addUser0Age() {
      this.testObj.user[0].age++
    },
    changeRoadPlanByStore() {
      this.activePage += 'b'
    }
  }
})

export default useScreenPageStore
