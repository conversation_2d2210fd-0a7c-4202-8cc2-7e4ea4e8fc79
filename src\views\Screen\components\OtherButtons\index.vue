<template>
  <div class="ai-buttons-container" :style="{ height: showModel ? '826px' : '273px' }">
    <div v-for="item in list" :key="item.id" class="img-wrapper">
      <img
        class="w91 h77 cursor-pointer"
        :src="item.selected ? item.iconSelected : item.icon"
        @mouseenter="showTooltip(item)"
        @mouseleave="hideTooltip(item, $event)"
        @click="throttledClick(item)" />
      <div class="tooltip-container">
        <transition name="tooltip-slide">
          <div v-if="showTip && currentItem === item && item.name !== '视角'" class="custom-tooltip">
            {{ item.name }}
          </div>
        </transition>
      </div>
    </div>
    <SelectViewPort
      v-show="currentItem && currentItem.id == 9"
      ref="refSelectViewPort"
      @mouseenter="handleViewPortEnter"
      @mouseleave="handleViewPortLeave"
      @clearShow="clearShow"></SelectViewPort>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import lib from '@/utils/lib'
  import AIChat from '@/components/AIChat/index.vue'
  import SelectViewPort from '../selectViewPort.vue'
  import { useThrottleFn } from '@vueuse/core'
  import { usePopWindow, type PopWindowProps, closePopWindowByTag } from 'znyg-frontend-common'
  import moment from 'moment'
  const { showPopWindow } = usePopWindow()
  const { storeScreenData } = lib.store()
  const refSelectViewPort = ref(null)
  const props = defineProps({
    showModel: {
      type: Boolean,
      default: false
    },
    clearSelected: {
      type: Boolean,
      default: false
    }
  })
  // 提取天气映射
  const createWeatherMap = () => {
    const weatherMap = new Map()
    weatherMap.set(0, [100, 150, 900, 999])
    weatherMap.set(1, [101, 102, 151, 152])
    weatherMap.set(2, [500, 501, 502, 509, 510, 511, 512, 513, 514, 515])
    weatherMap.set(3, [104, 901])
    weatherMap.set(4, [103, 153])
    weatherMap.set(5, [300, 306, 307, 308, 313, 315, 316, 350, 399])
    weatherMap.set(6, [305, 309, 314])
    weatherMap.set(7, [301, 302, 303, 304, 310, 311, 312, 317, 318, 351])
    weatherMap.set(8, [503, 504])
    weatherMap.set(9, [507, 508])
    weatherMap.set(10, [401, 402, 404, 405, 406, 407, 409, 456, 457, 499])
    weatherMap.set(11, [403, 410])
    weatherMap.set(12, [400, 408])
    return weatherMap
  }

  // 提取获取天气 key 的函数
  const getWeatherKey = (code: number) => {
    const weatherMap = createWeatherMap()
    for (const [key, values] of weatherMap) {
      if (values.includes(code)) {
        return key
      }
    }
    return 0
  }
  watch(
    () => props.showModel,
    (newVal) => {
      if (newVal) {
        list.value = defaultList.value
      } else {
        list.value = defaultList.value.slice(0, 3)
      }
    }
  )
  watch(
    () => props.clearSelected,
    (newVal) => {
      if (newVal) {
        list.value[2].selected = false
      }
    }
  )
  const list = ref([
    {
      id: 1,
      name: '钱江智慧AI',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1Selected.png'),
      selected: false
    },
    {
      id: 2,
      name: 'AI帮助',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2Selected.png'),
      selected: false
    },
    {
      id: 3,
      name: '沉浸模式',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn3.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn3Selected.png'),
      selected: false
    },
    {
      id: 4,
      name: '实时天气',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn4.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn4Selected.png'),
      selected: false
    },
    {
      id: 5,
      name: '默认视口',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn5.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn5Selected.png'),
      selected: false
    },
    {
      id: 6,
      name: '车辆孪生',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn6.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn6Selected.png'),
      selected: false
    },
    {
      id: 7,
      name: '停止跟随',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn7.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn7Selected.png'),
      selected: false
    },
    {
      id: 8,
      name: '漫游',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn8.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn8Selected.png'),
      selected: false
    },
    {
      id: 9,
      name: '视角',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn9.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn9.png'),
      selected: false
    }
  ])
  const defaultList = ref([...list.value])
  const isQuestionType = computed(() => {
    return lib.store().storeScreenData.isQuestionType
  })
  watch(isQuestionType, (newVal) => {
    list.value[1].selected = newVal
  })
  const emits = defineEmits(['btnClick'])
  const handleClick = async (item) => {
    console.log(item)
    item.selected = !item.selected
    switch (item.id) {
      case 1:
        if (item.selected) {
          openAiChat()
        } else {
          closePopWindowByTag(lib.enumsList.widows.Ai对话框)
        }
        break
      case 2:
        storeScreenData.showAIQuestion = item.selected
        lib.store().storeScreenData.isQuestionType = item.selected
        break
      case 3:
        emits('btnClick', item.selected)
        break
      case 4:
        if (item.selected) {
          try {
            const res: any = await lib.api.bigscreenApi.getWeather({})
            if (res.success && res.result) {
              console.log('天气信息', res)
              const weatherKey = getWeatherKey(res.result?.condCode)
              lib._engineController.setWeatherTime(weatherKey, parseInt(moment().format('HHmm')))
            }
          } catch (error) {
            console.error('获取天气信息失败', error)
          }
        } else {
          lib._engineController.setWeatherTime(0, 1200)
        }
        break
      case 5:
        setTimeout(() => {
          list.value[4].selected = false
        }, 200)
        await handleViewpoint()
        break
      case 6:
        await handleShowCars()
        break
      case 7:
        setTimeout(() => {
          list.value[6].selected = false
        }, 200)
        stopFollowCar()
        break
      case 8:
        if (lib.currentFollowCarPlate) {
          await lib._engineController.cameraFollowCar(lib.currentFollowCarPlate, false)
          lib.currentFollowCarPlate = null
        }
        if (item.selected) {
          lib._engineController.romaByName('漫游')
        } else {
          lib._engineController.romaPause()
        }
        break
    }
  }

  const throttledClick = useThrottleFn(handleClick, 500)
  const openAiChat = () => {
    const op: PopWindowProps = {
      top: 150,
      left: 350,
      width: 1938,
      height: 1043,
      tag: lib.enumsList.widows.Ai对话框,
      id: 'ai-chat-container',
      appendParent: 'popUpRoot',
      style: {
        backdropFilter: 'blur(18px)',
        zIndex: 999,
        overflow: 'hidden',
        borderRadius: '26px'
      },
      draggable: true,
      draggableClass: 'drag-container',
      animate: true,
      animateInClass: 'animate__animated animate__faster animate__zoomIn',
      animateOutClass: 'animate__animated animate__faster animate__zoomOut',
      onClose: () => {
        list.value[0].selected = false
      }
    }
    showPopWindow(op, AIChat)
  }

  lib.bus.busAIButtons.on((isShow: boolean) => {
    if (isShow) {
      openAiChat()
    } else {
      closePopWindowByTag(lib.enumsList.widows.Ai对话框)
    }
    list.value[0].selected = isShow
  })

  onUnmounted(() => {
    lib.bus.busAIButtons.reset()
  })
  // 回到默认视角
  const handleViewpoint = async () => {
    if (lib.currentFollowCarPlate) {
      await lib._engineController.cameraFollowCar(lib.currentFollowCarPlate, false)
      lib.currentFollowCarPlate = null
    }
    lib._engineController.gotoViewportByName('默认视口')
    refSelectViewPort.value?.setViewPort('默认视口')
  }

  // 车辆孪生
  const handleShowCars = async () => {
    lib.store().storeScreenData.vehicleTwinOpen = !lib.store().storeScreenData.vehicleTwinOpen
    await nextTick()
    lib.ai.startTrafficStreamCommon(lib.store().storeScreenData.vehicleTwinOpen, 'ws://***********:10000/mqtt/publish', 'tessng', 'mosquito', 'Cjxx-2024')
  }

  // 停止跟随
  const stopFollowCar = () => {
    lib.currentFollowCarPlate = null
    lib._engineController.enableClickToFollow(false)
    lib._engineController.viewer.ueViewer.setBowlingOptions({
      plateId: '',
      shiftUp: 5,
      drawBack: 10,
      lerpTime_is_0_to_1: '',
      lerpTime: 0.5
    })
  }
  // 控制提示框显示状态
  const showTip = ref(false)
  // 当前显示提示框对应的列表项
  const currentItem = ref(null)
  // 显示提示框
  const showTooltip = (item) => {
    // 清除之前的延迟隐藏定时器
    showTip.value = true
    currentItem.value = item
  }
  // 增加关闭定时器
  let closeTimer = null
  // 隐藏提示框
  const hideTooltip = (item, event) => {
    // 如果鼠标移入到Select组件区域，则不关闭
    const selectViewPortEl = refSelectViewPort.value?.$el
    if (selectViewPortEl?.contains(event.relatedTarget)) {
      return
    }
    // 延迟关闭以便给鼠标进入Select组件留出时间
    closeTimer = setTimeout(() => {
      if (currentItem.value === item) {
        showTip.value = false
        currentItem.value = null
      }
    }, 150) // 150ms延迟足够完成鼠标移动
  }
  // 处理进入Select组件
  const handleViewPortEnter = () => {
    // 清除关闭定时器
    clearTimeout(closeTimer)
    closeTimer = null
  }
  let isShow = ref(true) // 新增一个变量来跟踪是否显示Select组件
  // 处理离开Select组件
  const handleViewPortLeave = () => {
    // 重新设置关闭定时器
    closeTimer = setTimeout(() => {
      if (isShow.value) {
        currentItem.value = null
      }
    }, 150)
  }
  const clearShow = (bool = true) => {
    isShow.value = bool
    handleViewPortLeave()
  }
</script>

<style lang="scss" scoped>
  .ai-buttons-container {
    position: absolute;
    top: 20px;
    left: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 91px;
    height: 826px;
  }
  .img-wrapper {
    position: relative;
  }
  .tooltip-container {
    position: absolute;
    top: 0;
    left: 100%;
    display: flex;
    align-items: center;
    height: 100%;
  }
  .custom-tooltip {
    z-index: 1000;
    width: 120px;
    height: 40px;
    font-family: 'Source Han Sans CN';
    font-size: 20px;
    font-weight: 400;
    line-height: 40px;
    color: #ffffff;
    text-align: center;
    background: rgb(81 120 212 / 45%);
    border-radius: 6px;
    box-shadow: 0 3px 10px 1px rgb(0 0 0 / 16%);
  }

  /* 定义 tooltip-slide 动画样式 */
  .tooltip-slide-enter-active,
  .tooltip-slide-leave-active {
    transition: all 1s ease;
  }
  .tooltip-slide-enter-from,
  .tooltip-slide-leave-to {
    opacity: 0;
    transform: translateX(-10px);
  }
  .tooltip-slide-enter-to,
  .tooltip-slide-leave-from {
    opacity: 1;
    transform: translateX(0);
  }
</style>
