<template>
  <div class="duty-container">
    <div class="duty-item" v-for="(item, index) in list" :key="index">
      <div class="icon">
        <img class="icon-weather" v-if="index === 0" :src="item.iconUrl" :style="{ width: item.iconW + 'px', height: item.iconH + 'px' }" />
        <img v-else :src="lib.utils.getAssetsFile(item.icon)" :style="{ width: item.iconW + 'px', height: item.iconH + 'px' }" />
      </div>

      <div class="text">
        <span class="text-27" v-if="item.label" style="min-width: 40px" v-html="item.label"></span>
        <PopoverSelect v-model="item.options" v-if="item.options">
          <span style="font-family: 'Source Han Sans CN-Bold'">{{ item.value }}</span>
        </PopoverSelect>
        <span v-else>{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useIntervalFn } from '@vueuse/core'

  import PopoverSelect from '@/components/PopoverSelect/index.vue'

  import lib from '@/utils/lib.ts'
  import myStroage from '@/utils/sessionStorage'
  import { initLoad } from '@/utils/webRtcVideo'
  import { ElMessage } from 'element-plus'

  const options_zb = ref([
    // hasDefault 有默认匹配值
    { id: 1, name: '於秀青', selected: true, hasDefault: true },
    { id: 2, name: '徐月峰', selected: false, hasDefault: true },
    { id: 3, name: '殷珠', selected: false, hasDefault: true },
    { id: 4, name: '朱晓侠', selected: false, hasDefault: true },
    { id: 5, name: '王新楠', selected: false },
    { id: 6, name: '徐莉丽', selected: false },
    { id: 7, name: '李炯', selected: false }
  ])
  const zbSelected = computed(() => {
    return options_zb.value.find((item) => item.selected).name
  })

  const options_jtjk = ref([
    { id: 1, name: '钱国琴', selected: true },
    { id: 2, name: '李利娟', selected: false },
    { id: 3, name: '封琴', selected: false },
    { id: 4, name: '王颖', selected: false },
    { id: 5, name: '王新楠', selected: false },
    { id: 6, name: '徐莉丽', selected: false },
    { id: 7, name: '李炯', selected: false }
  ])
  const jtjkSelected = computed(() => {
    return options_jtjk.value.find((item) => item.selected).name
  })

  const options_dldt = ref([
    { id: 1, name: '高敏', selected: true },
    { id: 2, name: '申仕雄', selected: false },
    { id: 3, name: '种传福', selected: false },
    { id: 4, name: '杨义', selected: false },
    { id: 5, name: '王新楠', selected: false },
    { id: 6, name: '徐莉丽', selected: false },
    { id: 7, name: '李炯', selected: false }
  ])
  const dldtSelected = computed(() => {
    return options_dldt.value.find((item) => item.selected).name
  })
  // 默认匹配
  const defaultValueMap = [
    { zbValue: '於秀青', jtjkValue: '钱国琴', dldtValue: '高敏' },
    { zbValue: '徐月峰', jtjkValue: '李利娟', dldtValue: '申仕雄' },
    { zbValue: '殷珠', jtjkValue: '封琴', dldtValue: '种传福' },
    { zbValue: '朱晓侠', jtjkValue: '王颖', dldtValue: '杨义' }
  ]
  // watch(
  //   () => options_zb.value,
  //   (value) => {
  //     myStroage.setStorage('QJ_zbz', { name: value.find((item) => item.selected).name, index: value.findIndex((item) => item.selected) })
  //   },
  //   {
  //     deep: true
  //   }
  // )
  // watch(
  //   () => options_jtjk.value,
  //   (value) => {
  //     myStroage.setStorage('QJ_jtjk', { name: value.find((item) => item.selected).name, index: value.findIndex((item) => item.selected) })
  //   },
  //   {
  //     deep: true
  //   }
  // )
  // watch(
  //   () => options_dldt.value,
  //   (value) => {
  //     myStroage.setStorage('QJ_dldd', { name: value.find((item) => item.selected).name, index: value.findIndex((item) => item.selected) })
  //   },
  //   {
  //     deep: true
  //   }
  // )
  // #region 交通监控和电力调度可自行修改
  let isSelected = false
  // watch(
  //   () => zbSelected.value,
  //   () => {
  //     isSelected = false
  //     saveDutyInfo()
  //   }
  // )
  // watch(
  //   () => jtjkSelected.value,
  //   () => {
  //     isSelected = true
  //     list.value[3].value = jtjkSelected.value
  //     saveDutyInfo()
  //   }
  // )
  // watch(
  //   () => dldtSelected.value,
  //   () => {
  //     isSelected = true
  //     list.value[4].value = dldtSelected.value
  //     saveDutyInfo()
  //   }
  // )

  // 统一监听
  watch([zbSelected, jtjkSelected, dldtSelected], ([zb, jtjk, dldt], [oldZb, oldJtjk, oldDldt]) => {
    if (isInit.value) return
    if (zb !== oldZb) {
      isSelected = false
    }
    if (jtjk !== oldJtjk) {
      isSelected = true
    }
    if (dldt !== oldDldt) {
      isSelected = true
    }
    const zbValue = defaultValueMap.find((item) => item.zbValue == zbSelected.value)?.zbValue
    const dldtValue = defaultValueMap.find((item) => item.zbValue == zbSelected.value)?.dldtValue
    const jtjkValue = defaultValueMap.find((item) => item.zbValue == zbSelected.value)?.jtjkValue
    // 更新值班长
    list.value[2].value = zbSelected.value
    // 更新交通监控和电力调度
    if (zbValue === zbSelected.value && !isSelected) {
      list.value[3].value = jtjkValue
      list.value[4].value = dldtValue
      options_jtjk.value.forEach((item) => {
        item.name == jtjkValue ? (item.selected = true) : (item.selected = false)
      })
      options_dldt.value.forEach((item) => {
        item.name == dldtValue ? (item.selected = true) : (item.selected = false)
      })
    } else {
      list.value[3].value = jtjkSelected.value // 保持交通监控的值不变
      list.value[4].value = dldtSelected.value // 保持电力调度的值不变
    }
    // 自动保存逻辑
    saveDutyInfo()
  })

  // #endregion 交通监控和电力调度可自行修改
  const list = ref([
    { icon: 'ScreenTop/icon/weather/1.png', iconW: 38, iconH: 38, label: '', value: '19~26℃', iconUrl: '' },
    { icon: 'ScreenTop/icon/weather/2.png', iconW: 46, iconH: 38, label: '', value: '东北风2级' },
    { icon: 'ScreenTop/icon/zbz.png', iconW: 54, iconH: 54, label: '值班长：', value: '於秀青', options: options_zb },
    { icon: 'ScreenTop/icon/jtjg.png', iconW: 54, iconH: 54, label: '交通监控：', value: '钱国华', options: options_jtjk },
    { icon: 'ScreenTop/icon/dldd.png', iconW: 54, iconH: 54, label: '电力调度：', value: '高 敏', options: options_dldt }
  ])

  // 设置选中值
  const setSelected = (options, target) => {
    return options.map((item) => ({
      ...item,
      selected: item.name === target
    }))
  }
  // 添加初始化状态
  const isInit = ref(true)
  const initialValues = reactive({
    zb: '',
    jtjk: '',
    dldt: ''
  })
  const initData = async () => {
    try {
      const res = await lib.api.bigscreenApi.getDutyInfo({})
      if (res.success && res.result) {
        // 记录初始值
        initialValues.zb = res.result.find((_) => _.dutyType === 'dutyLeader')?.dutyUserName
        initialValues.jtjk = res.result.find((_) => _.dutyType === 'trafficMonitor')?.dutyUserName
        initialValues.dldt = res.result.find((_) => _.dutyType === 'electricSchedule')?.dutyUserName

        // 批量设置避免多次触发
        isInit.value = true
        options_zb.value = setSelected(options_zb.value, initialValues.zb)
        options_jtjk.value = setSelected(options_jtjk.value, initialValues.jtjk)
        options_dldt.value = setSelected(options_dldt.value, initialValues.dldt)

        // 设置列表初始值
        nextTick(() => {
          list.value[2].value = initialValues.zb
          list.value[3].value = initialValues.jtjk
          list.value[4].value = initialValues.dldt
          isInit.value = false
        })
      }
    } catch (error) {
      console.error('初始化失败:', error)
      isInit.value = false
    }
    // // #region 值班长
    // const itemObjStr = myStroage.getStorage('QJ_zbz')
    // console.log('值班长：====', itemObjStr)

    // if (itemObjStr) {
    //   options_zb.value.map((item) => {
    //     item.selected = false
    //   })
    //   const itemObj = JSON.parse(itemObjStr)
    //   options_zb.value[itemObj.index].selected = true
    // }
    // // #endregion 值班长

    // // #region 交通监控
    // const itemObjStrOne = myStroage.getStorage('QJ_jtjk')
    // if (itemObjStrOne) {
    //   options_jtjk.value.map((item) => {
    //     item.selected = false
    //   })
    //   const itemObj = JSON.parse(itemObjStrOne)
    //   options_jtjk.value[itemObj.index].selected = true
    // }
    // // #endregion 交通监控

    // // #region 电力调度
    // const itemObjStrTwo = myStroage.getStorage('QJ_dldd')
    // if (itemObjStrTwo) {
    //   options_dldt.value.map((item) => {
    //     item.selected = false
    //   })
    //   const itemObj = JSON.parse(itemObjStrTwo)
    //   options_dldt.value[itemObj.index].selected = true
    // }
    // // #endregion 电力调度
  }
  const saveDutyInfo = () => {
    lib.api.bigscreenApi
      .saveDutyInfo({
        dutyLeader: zbSelected.value,
        trafficMonitor: jtjkSelected.value,
        electricSchedule: dldtSelected.value
      })
      .then((res) => {
        if (res.success) {
          isInit.value = false
          ElMessage({
            message: '更新成功！',
            type: res.success ? 'success' : 'warning'
          })
        }
      })
  }
  onMounted(() => {
    initData()
    useIntervalFn(
      () => {
        getWeather()
      },
      1000 * 60 * 60,
      { immediateCallback: true }
    )
  })
  const getWeather = () => {
    lib.api.bigscreenApi.getWeather({}).then((response) => {
      if (response.success) {
        list.value[0].value = response.result.tmp + '℃'
        list.value[0].label = response.result.condTxt
        list.value[0].iconUrl = response.result.condPic
      }
    })
  }
</script>

<style lang="scss" scoped>
  .duty-container {
    position: absolute;
    top: 18px;
    left: 0;
    display: flex;
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
    .duty-item {
      position: relative;
      display: flex;
      height: 50px;
      margin-right: 10px;
      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 76px;
        height: 50px;
        .icon-weather {
          filter: invert(100%);
        }
      }
      .text {
        display: flex;
        height: 50px;
        font-family: 'Source Han Sans CN';
        font-size: 28px;
        font-weight: 400;
        line-height: 46px;
        color: #e1efff;
        text-shadow: 0 2px 4px #001d44;
      }
      &::after {
        position: absolute;
        top: 7px;
        right: -15px;
        width: 2px;
        height: 39px;
        content: '';
        background: linear-gradient(180deg, rgb(184 221 255 / 0%) 0%, #b8e1ff 50%, rgb(184 225 255 / 0%) 100%);
      }
      &:last-child::after {
        display: none;
      }
    }
  }
</style>
