define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba"],(function(e,t,n,r,a,i,o,c,u,d,f,y,p){"use strict";function s(){this._workerName="createPlaneOutlineGeometry"}s.packedLength=0,s.pack=function(e,n){return t.Check.defined("value",e),t.Check.defined("array",n),n},s.unpack=function(n,r,a){return t.Check.defined("array",n),e.defined(a)?a:new s};var m=new n.Cartesian3(-.5,-.5,0),b=new n.Cartesian3(.5,.5,0);function h(t,n){return e.defined(n)&&(t=s.unpack(t,n)),s.createGeometry(t)}return s.createGeometry=function(){var e=new p.GeometryAttributes,t=new Uint16Array(8),r=new Float64Array(12);return r[0]=m.x,r[1]=m.y,r[2]=m.z,r[3]=b.x,r[4]=m.y,r[5]=m.z,r[6]=b.x,r[7]=b.y,r[8]=m.z,r[9]=m.x,r[10]=b.y,r[11]=m.z,e.position=new f.GeometryAttribute({componentDatatype:d.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:r}),t[0]=0,t[1]=1,t[2]=1,t[3]=2,t[4]=2,t[5]=3,t[6]=3,t[7]=0,new f.Geometry({attributes:e,indices:t,primitiveType:y.PrimitiveType.LINES,boundingSphere:new a.BoundingSphere(n.Cartesian3.ZERO,Math.sqrt(2))})},h}));