define(["./when-1807bd8d","./createTaskProcessorWorker"],(function(t,e){"use strict";function n(t){const e=t.byteLength>>2,n=new Float32Array(3*e);for(let r=0;r<e;r++){const e=Math.pow(2,t[4*r+3]-136);n[3*r]=t[4*r]*e,n[3*r+1]=t[4*r+1]*e,n[3*r+2]=t[4*r+2]*e}return n}function r(t){let e="",r=0;const l=t;let o;try{while(!e.match(/\n\n[^\n]+\n/g))e+=String.fromCharCode(l[r++])}catch(u){return void console.log(u)}if(o=e.match(/FORMAT=(.*)$/m),o&&o.length<2||!1===!!o)return;if(o=o[1],"32-bit_rle_rgbe"!==o)return console.warn("unknown format : "+o);let i=e.split(/\n/).reverse();if(i.length<2)return;if(i=i[1].split(" "),i.length<4)return;const c=1*i[3],s=1*i[1],a=new Uint8Array(c*s*4);let f=0;for(let n=0;n<s;n++){const t=[];let e=l.slice(r,r+=4);const n=2===e[0]&&2===e[1]&&e[2]===(c>>8&255)&&e[3]===(255&c);if(n&&c>=8&&c<32768){for(let e=0;e<4;e++){let n=e*c;const o=(e+1)*c;let i,s;while(n<o)if(i=l.slice(r,r+=2),i[0]>128){s=i[0]-128;while(s-- >0)t[n++]=i[1]}else{s=i[0]-1,t[n++]=i[1];while(s-- >0)t[n++]=l[r++]}}for(let e=0;e<c;e++)a[f++]=t[e+0*c],a[f++]=t[e+1*c],a[f++]=t[e+2*c],a[f++]=t[e+3*c]}else{r-=4;for(let t=0;t<c;t++)e=l.slice(r,r+=4),a[f++]=e[0],a[f++]=e[1],a[f++]=e[2],a[f++]=e[3]}}const h=n(a);return{dataFloat:h,dataUint8:a,width:c,height:s}}var l=e(r);return l}));