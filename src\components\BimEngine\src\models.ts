import { getGidByDeviceId } from '@/assets/data/data'

import EngineController from '../index'
import { Element } from 'BimEngine'
const BimEngine = window.BimEngine

/** 隐藏UE资源，'1'为标线  '2'为白模，'3'为地形 */
export const setUeResouseVisible = (_this: EngineController, isShow: boolean, IdAry: Array<string>) => {
  if (_this.viewer && _this.viewer.isUseUE) {
    const ueResourceIdAry = IdAry
    _this.viewer.ueViewer.setVisibilityByActorNameAry(ueResourceIdAry, isShow)
  }
}

export const setVisibilityByBimIds = async (_this: EngineController, visible: boolean, bimIds: Array<string>) => {
  const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])
  modelAry.forEach((model) => {
    model.setVisibilityByBimIds(visible, bimIds)
  })
}

export const getAllGid = async (_this: EngineController) => {
  const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])
  console.log(' modelAry --- ', modelAry)
  let strAry = []
  for (let i = 0; i < modelAry.length; i++) {
    const mod = modelAry[i]
    const res = await mod.queryBimInfoByCondition({
      compCondition: [{ conditionType: 'pty', operator: '!=', key: '构件编码', value: '', valueType: 0 }],
      pageNo: 1,
      pageSize: 50
    })
    if (res.length > 0) {
      let indexIdAry = []
      res.forEach((item) => {
        indexIdAry.push(item.index)
      })
      const compInfo = await mod.queryElementByIndex(indexIdAry)
      console.log('compInfo', compInfo)
      if (compInfo.length > 0) {
        compInfo.forEach((item) => {
          if (item.bimProperties && item.bimProperties['构件编码']) {
            strAry.push(item.bimProperties['构件编码'].value)
          }
        })
      }
    }
  }
  console.log(' 构建编码列表 ', strAry)
  return strAry
}

export const getBimInfoByCode = async (_this: EngineController, code: string) => {
  const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])
  console.log(' modelAry --- ', modelAry)
  // let strAry = []
  let bimInfo = null
  for (let i = 0; i < modelAry.length; i++) {
    if (bimInfo === null) {
      const mod = modelAry[i]
      const res = await mod.queryBimInfoByCondition({
        compCondition: [{ conditionType: 'pty', operator: '=', key: 'code', value: code, valueType: 0 }],
        pageNo: 1,
        pageSize: 500
      })
      if (res.length > 0) {
        let indexIdAry = []
        res.forEach((item) => {
          indexIdAry.push(item.index)
        })
        const compInfo = await mod.queryElementByIndex(indexIdAry)
        console.log('compInfo', compInfo)
        if (compInfo.length > 0) {
          compInfo.forEach((item) => {
            if (item.bimProperties && item.bimProperties['code']) {
              bimInfo = item
            }
          })
        }
      }
    }
  }
  let position = null

  if (bimInfo) {
    const models = await _this.currentProject.queryModel()
    for (let mod of models) {
      if (position === null) {
        const boxAry = await mod.queryAABBoxByBimIdAry([bimInfo.bimId])
        if (boxAry && boxAry.length > 0) {
          const box = boxAry[0]
          position = box.center
        }
      }
    }
    if (position) {
      bimInfo.position = position
    }
  }
  console.log(' 构建信息', bimInfo)
  return bimInfo
}

/**
 * 批量查询Bim信息
 * @param _this EngineController
 * @param codeList 需要查询的code列表
 * @param map 存储到map中
 */
export const getBimInfoByCodeList = async (_this: EngineController, codeList: Array<string>, map: Map<string, any>) => {
  const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])
  console.log(' modelAry --- ', modelAry)
  // let strAry = []
  for (let i = 0; i < modelAry.length; i++) {
    if (!codeList.every((_) => map.has(_))) {
      const mod = modelAry[i]
      const res = await mod.queryBimInfoByCondition({
        compCondition: [{ conditionType: 'pty', operator: 'in', key: 'code', value: codeList, valueType: 0 }],
        pageNo: 1,
        pageSize: 500
      })
      if (res.length > 0) {
        let indexIdAry = []
        res.forEach((item) => {
          indexIdAry.push(item.index)
        })
        const compInfo = await mod.queryElementByIndex(indexIdAry)
        console.log('compInfo', compInfo)
        if (compInfo.length > 0) {
          for (let compItem of compInfo) {
            const bimId = compItem.bimId
            const code = compItem?.bimProperties?.code?.value

            if (code && !map.has(code)) {
              const boxAry = await mod.queryAABBoxByBimIdAry([bimId])
              if (boxAry && boxAry.length > 0) {
                compItem.position = boxAry[0].center
                map.set(code, compItem)
              }
            }
          }

          // const bimIdList = compInfo.map((_) => _.bimId)
          // const boxAry = await mod.queryAABBoxByBimIdAry(bimIdList)
          // debugger
          // console.log(boxAry)
          // compInfo.forEach((item) => {
          //   if (item.bimProperties && item.bimProperties['code']) {
          //     bimInfo = item
          //   }
          // })
        }
      }
    }
  }
  codeList.forEach((code) => {
    if (!map.has(code)) {
      map.set(code, null)
    }
  })
}

/** 开启点击跟随实时车流  */
export const enableClickToFollow = (_this: EngineController, enable: boolean) => {
  _this.viewer.ueViewer.enableClickToFollow(enable)
}

/** 开启车辆仿真 */
export const startTrafficStream = (_this: EngineController, enable: boolean) => {
  _this.viewer.ueViewer.startTrafficStream(enable, false)
}

/** 开启车辆仿真通用方法 */
export const startTrafficStreamCommon = (_this: EngineController, enable?: boolean, socketUrl?: string, topic?: string, usr?: string, pwd?: string) => {
  _this.viewer.ueViewer.startTrafficStreamCommon(enable, socketUrl, topic, usr, pwd)
}

/** 开启保龄球推流 */
export const setBowlingEnable = (_this: EngineController, enable: boolean, enableFollow: boolean) => {
  _this.viewer.ueViewer.setBowlingEnable(enable, enableFollow)
}

/**
 * 大连路隧道开启车辆跟随，plate为空时，停止跟随
 * @param _this EngineController
 * @param plate 车牌号 为空或者不写就是取消跟随
 * @param camereRelativeUp 相机相对车辆插入点的高度
 * @param cameraRelativeBack 相机相对车辆插入点的后退距离
 * @param cameraAngle 相机的角度 0 水平视角 1 俯视视角
 */
export const setDLCarFollow = (_this: EngineController, plate: string, camereRelativeUp = 3, cameraRelativeBack = 6, cameraAngle = 0.5) => {
  _this.viewer.ueViewer.setDLCarFollow(plate, camereRelativeUp, cameraRelativeBack, cameraAngle)
}

/**
 * 道路线 默认开启道路显示，程序启动后不要立即调用本命令(简单起见)
 * @param _this EngineController
 * @param enableRoads  true 开启 false 关闭道路线
 * @param color 道路线颜色 默认[1，0，0，1]
 * @param radius 道路线半径 默认 1.1
 */
export const setDLRoadLineVisible = (_this: EngineController, enableRoads: boolean, color = [1, 0, 0, 1], radius = 1.1) => {
  _this.viewer.ueViewer.setDLRoadLineVisible(enableRoads, color, radius)
}

export const setColorByList = async (_this: EngineController, list: Array<{ bimId: string; color: string }>) => {
  const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])

  const groupedBimIds = list.reduce((acc, item) => {
    const { color, bimId } = item
    if (!acc[color]) {
      acc[color] = []
    }
    acc[color].push(bimId)
    return acc
  }, {})
  console.log('groupedBimIds', groupedBimIds)
  modelAry.forEach((mod) => {
    Object.entries(groupedBimIds).forEach(([color, bimIds]) => {
      const bimColor = BimEngine.Color.fromCssColorString(color)
      mod.setColorByBimIds(bimColor, bimIds)
    })
  })
}

export const resetColor = async (_this: EngineController) => {
  // const modelAry = await _this.currentProject.queryModel([BimEngine.ModelType.BIM])
  // modelAry.forEach((mod) => {
  //   mod.resetColor()
  // })
  _this.currentProject.resetColor()
}
