/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Cartesian3-bb0e6278","./EllipsoidOutlineGeometry-b8ec15c3","./Math-b5f4d889","./Transforms-42ed7720","./Rectangle-9bffefe4","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b"],(function(e,i,t,n,o,s,r,a,d,l,c,u,m,b,p){"use strict";function f(n){const o=e.defaultValue(n.radius,1),s={radii:new i.Cartesian3(o,o,o),stackPartitions:n.stackPartitions,slicePartitions:n.slicePartitions,subdivisions:n.subdivisions};this._ellipsoidGeometry=new t.EllipsoidOutlineGeometry(s),this._workerName="createSphereOutlineGeometry"}f.packedLength=t.EllipsoidOutlineGeometry.packedLength,f.pack=function(e,i,n){return t.EllipsoidOutlineGeometry.pack(e._ellipsoidGeometry,i,n)};const y=new t.EllipsoidOutlineGeometry,G={radius:void 0,radii:new i.Cartesian3,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};return f.unpack=function(n,o,s){const r=t.EllipsoidOutlineGeometry.unpack(n,o,y);return G.stackPartitions=r._stackPartitions,G.slicePartitions=r._slicePartitions,G.subdivisions=r._subdivisions,e.defined(s)?(i.Cartesian3.clone(r._radii,G.radii),s._ellipsoidGeometry=new t.EllipsoidOutlineGeometry(G),s):(G.radius=r._radii.x,new f(G))},f.createGeometry=function(e){return t.EllipsoidOutlineGeometry.createGeometry(e._ellipsoidGeometry)},function(i,t){return e.defined(t)&&(i=f.unpack(i,t)),f.createGeometry(i)}}));
