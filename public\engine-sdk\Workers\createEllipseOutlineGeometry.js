define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./EllipseGeometryLibrary-98898859","./EllipseOutlineGeometry-4ce65568"],(function(e,t,r,i,n,a,o,c,l,s,f,d,u,p,m,y,b){"use strict";function G(t,n){return e.defined(n)&&(t=b.EllipseOutlineGeometry.unpack(t,n)),t._center=r.Cartesian3.clone(t._center),t._ellipsoid=i.Ellipsoid.clone(t._ellipsoid),b.EllipseOutlineGeometry.createGeometry(t)}return G}));