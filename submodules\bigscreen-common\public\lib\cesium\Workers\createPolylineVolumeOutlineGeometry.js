/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./arrayRemoveDuplicates-5b666c82","./BoundingRectangle-b4242da0","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./PolylineVolumeGeometryLibrary-fad92191","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db"],(function(e,n,t,i,o,a,l,r,s,p,d,c,u,y,f,g,h,m,b,E,P,_,k,C){"use strict";function L(t){const i=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).polylinePositions,o=t.shapePositions;this._positions=i,this._shape=o,this._ellipsoid=n.Ellipsoid.clone(e.defaultValue(t.ellipsoid,n.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(t.cornerType,r.CornerType.ROUNDED),this._granularity=e.defaultValue(t.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";let l=1+i.length*a.Cartesian3.packedLength;l+=1+o.length*a.Cartesian2.packedLength,this.packedLength=l+n.Ellipsoid.packedLength+2}L.pack=function(t,i,o){let l;o=e.defaultValue(o,0);const r=t._positions;let s=r.length;for(i[o++]=s,l=0;l<s;++l,o+=a.Cartesian3.packedLength)a.Cartesian3.pack(r[l],i,o);const p=t._shape;for(s=p.length,i[o++]=s,l=0;l<s;++l,o+=a.Cartesian2.packedLength)a.Cartesian2.pack(p[l],i,o);return n.Ellipsoid.pack(t._ellipsoid,i,o),o+=n.Ellipsoid.packedLength,i[o++]=t._cornerType,i[o]=t._granularity,i};const T=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE),D={polylinePositions:void 0,shapePositions:void 0,ellipsoid:T,height:void 0,cornerType:void 0,granularity:void 0};L.unpack=function(t,i,o){let l;i=e.defaultValue(i,0);let r=t[i++];const s=new Array(r);for(l=0;l<r;++l,i+=a.Cartesian3.packedLength)s[l]=a.Cartesian3.unpack(t,i);r=t[i++];const p=new Array(r);for(l=0;l<r;++l,i+=a.Cartesian2.packedLength)p[l]=a.Cartesian2.unpack(t,i);const d=n.Ellipsoid.unpack(t,i,T);i+=n.Ellipsoid.packedLength;const c=t[i++],u=t[i];return e.defined(o)?(o._positions=s,o._shape=p,o._ellipsoid=n.Ellipsoid.clone(d,o._ellipsoid),o._cornerType=c,o._granularity=u,o):(D.polylinePositions=s,D.shapePositions=p,D.cornerType=c,D.granularity=u,new L(D))};const G=new i.BoundingRectangle;return L.createGeometry=function(e){const n=e._positions,c=t.arrayRemoveDuplicates(n,a.Cartesian3.equalsEpsilon);let y=e._shape;if(y=r.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(y),c.length<2||y.length<3)return;u.PolygonPipeline.computeWindingOrder2D(y)===u.WindingOrder.CLOCKWISE&&y.reverse();const f=i.BoundingRectangle.fromPoints(y,G);return function(e,n){const t=new p.GeometryAttributes;t.position=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});const i=n.length,a=t.position.values.length/3,r=e.length/3/i,c=d.IndexDatatype.createTypedArray(a,2*i*(r+1));let u,y,f=0;u=0;let g=u*i;for(y=0;y<i-1;y++)c[f++]=y+g,c[f++]=y+g+1;for(c[f++]=i-1+g,c[f++]=g,u=r-1,g=u*i,y=0;y<i-1;y++)c[f++]=y+g,c[f++]=y+g+1;for(c[f++]=i-1+g,c[f++]=g,u=0;u<r-1;u++){const e=i*u,n=e+i;for(y=0;y<i;y++)c[f++]=y+e,c[f++]=y+n}return new s.Geometry({attributes:t,indices:d.IndexDatatype.createTypedArray(a,c),boundingSphere:o.BoundingSphere.fromVertices(e),primitiveType:s.PrimitiveType.LINES})}(r.PolylineVolumeGeometryLibrary.computePositions(c,y,f,e,!1),y)},function(t,i){return e.defined(i)&&(t=L.unpack(t,i)),t._ellipsoid=n.Ellipsoid.clone(t._ellipsoid),L.createGeometry(t)}}));
