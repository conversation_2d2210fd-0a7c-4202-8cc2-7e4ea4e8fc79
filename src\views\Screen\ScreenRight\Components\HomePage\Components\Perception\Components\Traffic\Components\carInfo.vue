<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-23 09:48:37
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-07-10 09:11:09
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\Components\HomePage\Components\Perception\Components\Traffic\Components\carInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="car-info-container">
    <Tabs :type="type" :date="date" :carTotalNumber="carTotalNumber" :lineName="lineName" :carTopList="carTopList"></Tabs>
    <div class="flex flex-wrap justify-between w-640 mt15">
      <CarTypeChart v-for="(item, ind) in carTypeList" :key="ind" :itemData="item" :carTotalNumber="carTotalNumber"></CarTypeChart>
    </div>
  </div>
</template>

<script setup>
  import Tabs from './Components/tabs.vue'
  import Mychart from '@Common/components/MyChart/index.vue'
  import CarTypeChart from './Components/carTypeChart.vue'
  import lib from '@/utils/lib.ts'

  const props = defineProps({
    type: {
      type: String,
      default: 'day'
    },
    lineName: {
      type: String,
      default: '全部'
    },
    date: {
      type: String,
      default: ''
    },
    resultList: {
      type: Array,
      default: () => []
    }
  })

  const carTopList = ref([])
  const carList = ref([])
  const carTypeList = ref([])
  const carTotalNumber = ref(10000)
  const selectedIndex = ref(-1) // 添加选中索引状态

  // 使用计算属性替代watch
  const processedData = computed(() => {
    if (!props.resultList.length) return { carList: [], carTopList: [] }
    
    const arr = props.resultList.slice(0, props.resultList.length - 4)
    const topList = props.resultList.slice(props.resultList.length - 4, props.resultList.length - 1).reverse()
    
    // 保持选中状态
    topList.forEach((item, index) => {
      item.selected = index === selectedIndex.value
    })
    
    return {
      carList: arr,
      carTopList: topList
    }
  })

  // 使用计算属性处理图表数据
  const chartData = computed(() => {
    const labelData = carList.value.map((_) => _.name)
    return carList.value.map((_) => {
      const color = _.name.includes('客车') ? '#0FED9D' : _.name.includes('作业车') ? '#00D1FF' : '#F4B410'
      return {
        name: _.name,
        value: _.pro.split('%')[0],
        data: _.data,
        color: color
      }
    })
  })

  // 监听计算属性变化
  watch(processedData, (newVal) => {
    carList.value = newVal.carList
    carTopList.value = newVal.carTopList
    carTypeList.value = chartData.value
  }, { immediate: true })

  // 监听总数变化
  lib.bus.trafficMonitor.on((e) => {
    carTotalNumber.value = e
    carTypeList.value = chartData.value
  })

  // 监听tab选中状态变化
  lib.bus.trafficMonitorTab.on((data) => {
    // 更新选中索引
    const index = carTopList.value.findIndex(item => item.name === data.name)
    if (index !== -1) {
      selectedIndex.value = data.selected ? index : -1
      // 更新选中状态
      carTopList.value.forEach((item, idx) => {
        item.selected = idx === selectedIndex.value
      })
    }
    console.log(selectedIndex.value, 'selectedIndex.value', carTopList.value)
  })

  onUnmounted(() => {
    lib.bus.trafficMonitor.reset()
    lib.bus.trafficMonitorTab.reset()
  })
</script>

<style lang="scss" scoped>
  .car-info-container {
    width: 640px;
    height: 430px;
  }
</style>
