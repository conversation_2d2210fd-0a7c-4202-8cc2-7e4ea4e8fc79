<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-06-04 15:30:23
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-06 16:19:42
 * @FilePath: \bigscreen-qj-web\src\components\DatePickerNew\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="date-picker-new-container" :style="style">
    <div class="PickDate" :style="style">
      <el-select popper-class="select-list-com-popper" v-model="value" placeholder="请选择" style="width: 86px">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <QuarterPicker v-if="value == 'quarter'" ref="quarterPickerRef" v-model="date" format="YYYY年第q季度" :disabled-date="disabledQuarter"></QuarterPicker>
    <DatePicker v-else :type="value" v-model="date" style="top: 4px; right: 10px"></DatePicker>
  </div>
</template>

<script setup>
  import { computed, watch } from 'vue'

  import DatePicker from '@/components/DatePicker/index.vue'
  import QuarterPicker from '@/components/QuarterPicker/index.vue'
  const props = defineProps({
    style: {
      type: Object
    },
    currentDate: {
      type: String,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => [
        { id: 1, label: '按日', selected: true, value: 'day' },
        { id: 2, label: '按月', selected: false, value: 'month' },
        { id: 3, label: '按年', selected: false, value: 'year' }
      ]
    }
  })
  const disabledQuarter = (val) => {
    if (val <= new Date()) return false
    return true
  }
  const value = defineModel()
  // const options = [
  //   { id: 1, label: '按日', selected: true, value: 'day' },
  //   { id: 2, label: '按月', selected: false, value: 'month' },
  //   { id: 3, label: '按年', selected: false, value: 'year' }
  // ]
  const date = ref(props.currentDate)
  watch(
    () => props.currentDate,
    (val) => {
      date.value = val
    }
  )
  const emit = defineEmits(['change'])
  watch(
    () => date.value,
    (val) => {
      emit('change', val)
    }
  )
</script>

<style lang="scss" scoped>
  .date-picker-new-container {
    position: absolute;
    display: flex;
    justify-content: space-between;
    width: 320px;
    height: 32px;
    .PickDate {
      width: 86px;
      height: 36px;
      color: #8dd8ff;
      :deep(.el-select__wrapper) {
        // --el-fill-color-blank: transparent !important;
        // --el-select-input-focus-border-color: #47b7ff;
        // --el-border-color: #47b7ff;
        width: 78px;
        height: 42px;
        min-height: 42px;
        color: #8dd8ff;
        background: url('@/assets/CommonPopup/selectBg.png') no-repeat;
        background-size: 100% 100%;
      }
      :deep(.el-input) {
        height: 40px;
      }
      :deep(.el-input__inner) {
        height: 40px;
        padding-left: 0 !important;
        font-size: 18px;
        line-height: 40px;
      }
      :deep(.el-input__wrapper) {
        background-color: transparent !important;

        // border: 1px solid #47b7ff;
        box-shadow: none;
      }
      :deep(.el-select__placeholder) {
        color: #8dd8ff;
      }
    }
  }
</style>
<style>
  .select-list-com-popper {
    background: #093161;

    --el-fill-color-blank: transparent;
    --el-fill-color-light: transparent;
    --el-text-color-regular: #ffffff;
    --el-border-color-light: #47b7ff;
  }
</style>
