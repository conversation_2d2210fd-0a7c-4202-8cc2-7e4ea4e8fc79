import { useXStream } from 'vue-element-plus-x'
import type { BubbleListItemProps, BubbleListProps } from 'vue-element-plus-x/bubbleList/types'
// import mdTable from 'markdown-it-multimd-table'
// import MarkdownIt from 'markdown-it'
import lib from '@/utils/lib'
import { getToken } from '@/utils/auth'

let userId = ''
export const useAiChat = (appKey) => {
  const inputValue = ref('')
  const recording = ref(false)
  const submitLoading = ref(false)
  const currentConversationId = ref('')
  const showSender = ref(false)
  const isCarryHistory = ref(false)

  // 文件选择相关状态
  const showFileSelector = ref(false)
  const selectedFiles = ref<Array<{ id: string; name: string; url?: string }>>([])
  const fileRequestParams = ref({})
  onBeforeMount(async () => {
    //获取用户信息
    const userRes = await lib.api.authApi.getUserInfo()
    userId = userRes.result.user.id
  })

  // const tablePlugin = new MarkdownIt({
  //   html: true, // 允许 HTML 标签
  //   linkify: true // 自动转换链接
  // }).use(mdTable, {
  //   multiline: true, // 启用多行表格语法
  //   rowspan: true // 支持跨行单元格
  // })
  // const mdPlugins = [tablePlugin]

  const key = ref(0)
  type listType = BubbleListItemProps & {
    key: number
    role: 'user' | 'ai'
    thinkingContent?: string //思考内容
    thinkingStatus?: 'start' | 'thinking' | 'end' | 'error' //思考状态
    quoteList?: any[] //引用列表
    echartsJSONData?: any //echartsJSONData
  }
  const list: BubbleListProps<listType>['list'] = ref([])

  const { startStream, cancel, data, error, isLoading } = useXStream()

  /** 知识库-选择文件 */
  const handleFiles = () => {
    showFileSelector.value = true
  }

  // 处理文件选择确认
  const handleFileConfirm = (files: any[]) => {
    console.log('选择的文件:', files)
    selectedFiles.value = files.map((file) => ({
      id: file.id,
      name: file.label,
      url: file.path || undefined
    }))
  }

  // 删除单个文件
  const removeFile = (index: number) => {
    selectedFiles.value.splice(index, 1)
  }

  // 清空所有文件
  const clearAllFiles = () => {
    selectedFiles.value = []
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 文件请求API
  const fileRequestApi = lib.api.knowledgeApi.treeList

  /**录音 */
  const handleRecord = () => {
    recording.value = !recording.value
    console.log('正在录音', recording.value)
  }

  /**取消提交 */
  const handleCancel = () => {
    if (submitLoading.value) {
      cancel()
      list.value = list.value.slice(0, list.value.length - 2)
      submitLoading.value = false
    }
  }

  /** 清空 */
  const handleClear = () => {
    console.log('handleClear')
    list.value = []
    currentConversationId.value = ''
    inputValue.value = ''
    submitLoading.value = false
    cancel()
  }

  const handleThinkingChange = (payload: { value: boolean; status: 'start' | 'thinking' | 'end' | 'error' }) => {
    console.log('value', payload.value, 'status', payload.status)
  }

  const handleSubmit = () => {
    console.log('提交')
    submitLoading.value = true
    showWelcomePage.value = false // 提交时隐藏欢迎页

    key.value++
    list.value.push({
      key: key.value, // 唯一标识
      role: 'user', // user | ai 自行更据模型定义
      placement: 'end', // start | end 气泡位置
      content: inputValue.value // 消息内容 流式接受的时候，只需要改这个值即可
    })

    key.value++
    const newReply: BubbleListProps<listType> = {
      key: key.value, // 唯一标识
      role: 'ai', // user | ai 自行更据模型定义
      placement: 'start', // start | end 气泡位置
      content: '', // 消息内容 流式接受的时候，只需要改这个值即可
      loading: true, // 当前气泡的加载状态
      shape: 'corner', // 气泡的形状
      variant: 'filled', // 气泡的样式
      isMarkdown: true, // 是否渲染为 markdown
      // mdPlugins, // 自定义 markdown 插件
      typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
      isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
      maxWidth: '1700px',
      thinkingContent: '',
      thinkingStatus: 'thinking'
    }

    list.value.push(newReply)

    startSSE(inputValue.value)
  }

  const startSSE = async (query) => {
    if (isCarryHistory.value) {
      query = historyStr.value + '\n\n ---------- \n\n' + query
      isCarryHistory.value = false
    }
    try {
      const response = await fetch(`${window.config.difyAPIBaseUrl}/chat-messages`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'text/event-stream' },
        body: JSON.stringify({
          // app_key: window.config.appKey,
          app_key: appKey.value,
          query: query,
          response_mode: 'streaming',
          inputs: {
            document_names: selectedFiles.value.map((file) => file.name).join(','),
            biz_token: getToken()
          },
          conversation_id: currentConversationId.value,
          session_id: userId.toString()
        })
      })
      const readableStream = response.body!
      inputValue.value = ''

      await startStream({ readableStream })
      submitLoading.value = false

      //结束后保存会话信息
      // if (currentConversationId.value === '') {
      getConversationIdFromServer()
      getHistoryList()
      // }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }

  /**获取当前会话的conversation_id */
  const getConversationIdFromServer = async () => {
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)
    chatClient.setSessionId(userId)

    const conversationList = await chatClient.getConversations()
    console.log(conversationList)

    // 上面创建的新的聊天 在 conversationList.data[0]
    if (conversationList && conversationList.data.length > 0) {
      const conversation = conversationList.data[0]
      console.log(conversation)
      currentConversationId.value = conversation.id
    }
  }

  const getConversations = async () => {
    if (userId === '') {
      const userRes = await lib.api.authApi.getUserInfo()
      userId = userRes.result.user.id
    }
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)
    chatClient.setSessionId(userId)

    const conversationList = await chatClient.getConversations()
    return conversationList
  }

  const deleteConversation = async (conversationId: string) => {
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)
    chatClient.setSessionId(userId)
    await chatClient.deleteConversation(conversationId)
  }

  const getConversationMessages = async (conversationId: string) => {
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)
    chatClient.setSessionId(userId)
    currentConversationId.value = conversationId

    // 获取对话消息，参数：conversationId, first_id(可选), limit(可选，默认20)
    const messages = await chatClient.getConversationMessages(conversationId, null, 100)
    return messages
  }

  const historyStr = computed(() => {
    const aiHistory = list.value.filter((item) => item.content !== '').slice(-10, -1)
    let num = 1

    return aiHistory
      .map((item, index) => {
        if (item.role === 'user') {
          return `历史提问${num}/5  ${item.content}`
        } else {
          num++
          return `历史回答${num - 1}/5  ${item.content}`
        }
      })
      .join('\n\n')
  })

  const content = computed(() => {
    if (!data.value.length) return ''
    let text = ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message') {
            text += parsedChunk.answer
          } else if (parsedChunk.event === 'error') {
            return 'error'
          }
        } catch (error) {
          console.error('解析数据时出错:', error)
        }
      }
    }
    return text
  })
  watch(content, (newVal) => {
    if (newVal) {
      // console.log('newVal-------', newVal)
      const currentReply = list.value.find((_) => _.key === key.value)
      if (newVal === 'error') {
        currentReply.thinkingStatus = 'error'
        currentReply.loading = false
        return
      }
      if (newVal.includes('<think>')) {
        if (newVal.includes('</think>')) {
          currentReply.thinkingStatus = 'end'
          currentReply.thinkingContent = newVal.slice(newVal.indexOf('<think>') + 7, newVal.indexOf('</think>'))
          const noThink = newVal.slice(newVal.indexOf('</think>') + 8)
          // console.log('noThink-------', noThink)
          const echartsIndex = noThink.indexOf('echartsJSONData:')
          currentReply.content = echartsIndex !== -1 ? noThink.slice(0, echartsIndex) : noThink
          currentReply.loading = false
        } else {
          currentReply.thinkingContent = newVal.replace(/<think>/g, '')
        }
      } else {
        currentReply.thinkingStatus = 'end'
        const echartsIndex = newVal.indexOf('echartsJSONData:')
        currentReply.content = echartsIndex !== -1 ? newVal.slice(0, echartsIndex) : newVal
        currentReply.loading = false
      }
      if (newVal.includes('echartsJSONData:')) {
        const echartsData = newVal
          .slice(newVal.lastIndexOf('echartsJSONData:') + 16)
          .replace(/```json/g, '')
          .replace(/```/g, '')
        try {
          // 尝试解析JSON，如果成功说明是完整的JSON字符串
          JSON.parse(echartsData)
          // console.log('echartsJSONData-------', JSON.parse(echartsData))
          currentReply.echartsJSONData = JSON.parse(echartsData)
        } catch (error) {
          // JSON解析失败，说明数据不完整，不赋值
          console.log('echartsJSONData 数据不完整，等待更多数据...')
        }
      }
    }
  })

  // 处理引用
  const quoteList = computed(() => {
    if (!data.value.length) return ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message_end') {
            if (parsedChunk?.metadata?.retriever_resources?.length > 0) {
              const fileList = parsedChunk?.metadata?.retriever_resources || []
              fileList.forEach((_) => {
                _.title = _.document_name
                _.docId = _.document_id
              })
              return fileList
              // return parsedChunk?.metadata?.retriever_resources.map((item) => {
              //   return {
              //     title: item.document_name,
              //     docId: item.document_id
              //   }
              // })
            }
          }
        } catch (error) {
          console.error('解析文档列表时出错:', error)
        }
      }
    }
    return []
  })
  watch(quoteList, async (newVal) => {
    if (newVal?.length > 0) {
      console.log('引用列表-------', newVal)
      const currentReply = list.value.find((_) => _.key === key.value)
      const documentIdList = [...new Set(newVal.map((item) => item.docId))]
      const res = await lib.api.knowledgeFileApi.getFromDocumentId({
        documentIdList: documentIdList
      })
      // currentReply.quoteList = res?.result.filter((item) => item !== null) || []
      currentReply.quoteList = []
      newVal.forEach((item) => {
        const quotItem = res.result.find((resItem) => resItem.documentId === item.document_id)
        item.url = quotItem?.url
        item.fileName = quotItem?.fileName
        currentReply.quoteList.push(item)
      })
    }
  })
  // 历史对话相关状态
  const isHistoryCollapsed = ref(true) // 历史对话框是否折叠
  const searchKeyword = ref('') // 搜索关键词
  const isSelectMode = ref(false) // 是否处于选择模式
  const selectedHistoryItems = ref([]) // 选中的历史对话项ID数组
  const historyList = ref([]) // 历史对话列表
  const showWelcomePage = ref(true) // 控制欢迎页的显示

  // 处理对话数据的函数
  const processConversationData = (response) => {
    if (response && response.data) {
      // 将API返回的数据转换为组件需要的格式
      const processedData = response.data.map((item) => ({
        id: item.id,
        name: item.name,
        title: item.name, // 为了兼容现有的显示逻辑
        introduction: item.introduction,
        created_at: item.created_at,
        updated_at: item.updated_at,
        status: item.status,
        inputs: item.inputs
      }))

      // 按创建时间降序排序（最新的在前面）
      processedData.sort((a, b) => b.created_at - a.created_at)

      historyList.value = processedData
    }
  }

  // 获取历史对话列表
  const getHistoryList = async () => {
    try {
      const conversationList = await getConversations()
      processConversationData(conversationList)
    } catch (error) {
      console.error('获取历史对话失败:', error)
    }
  }

  // 历史对话分组计算属性
  const historyGroups = computed(() => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

    const groups = [
      { title: '今天', items: [] },
      { title: '最近7天', items: [] },
      { title: '更早', items: [] }
    ]

    historyList.value.forEach((item) => {
      // 将时间戳转换为Date对象
      const itemDate = new Date(item.created_at * 1000)
      const itemDateOnly = new Date(itemDate.getFullYear(), itemDate.getMonth(), itemDate.getDate())

      if (itemDateOnly.getTime() >= today.getTime()) {
        groups[0].items.push(item)
      } else if (itemDateOnly.getTime() >= sevenDaysAgo.getTime()) {
        groups[1].items.push(item)
      } else {
        groups[2].items.push(item)
      }
    })

    // 只返回有内容的分组
    return groups.filter((group) => group.items.length > 0)
  })

  // 过滤后的历史对话分组
  const filteredHistoryGroups = computed(() => {
    if (!searchKeyword.value.trim()) {
      return historyGroups.value
    }

    const keyword = searchKeyword.value.toLowerCase()
    return historyGroups.value
      .map((group) => ({
        ...group,
        items: group.items.filter(
          (item) => item.name.toLowerCase().includes(keyword) || (item.introduction && item.introduction.toLowerCase().includes(keyword))
        )
      }))
      .filter((group) => group.items.length > 0)
  })

  // 历史对话相关方法
  const toggleHistoryCollapse = () => {
    isHistoryCollapsed.value = !isHistoryCollapsed.value
  }

  const handleSearchHistory = () => {
    // 搜索逻辑已在计算属性中处理
    console.log('搜索历史对话:', searchKeyword.value)
  }

  // 处理历史消息数据，转换为BubbleList所需格式
  const processHistoryMessages = (messagesData) => {
    if (!messagesData || !messagesData.data) return []

    const processedMessages = []
    let keyCounter = 1

    // 按时间排序（升序，最早的在前面）
    const sortedMessages = messagesData.data.sort((a, b) => a.created_at - b.created_at)

    sortedMessages.forEach((message) => {
      // 用户消息
      // let splitIndex = message.query.indexOf('\n\n\n\n\n')
      let splitIndex = message.query.lastIndexOf('\n\n ---------- \n\n')
      if (splitIndex >= 0) {
        splitIndex = splitIndex + 16
      } else {
        const flag = message.query.lastIndexOf('</details>')
        if (flag > 0) {
          splitIndex = flag + 9
        }
      }
      processedMessages.push({
        key: keyCounter++,
        role: 'user',
        placement: 'end',
        content: splitIndex > 0 ? message.query.slice(splitIndex) : message.query,
        loading: false,
        shape: 'corner',
        variant: 'filled',
        isMarkdown: false,
        typing: false,
        maxWidth: '1700px'
      })

      // AI回复消息
      let aiContent = message.answer
      let echartsData = null

      // 去除 <think></think> 包裹的内容
      if (aiContent && aiContent.includes('<think>')) {
        aiContent = aiContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
      }

      // 检查是否包含图表数据
      if (aiContent && aiContent.includes('echartsJSONData:')) {
        const echartsIndex = aiContent.indexOf('echartsJSONData:')
        const textContent = aiContent.slice(0, echartsIndex).trim()
        const echartsJsonStr = aiContent
          .slice(echartsIndex + 16)
          .replace(/```json/g, '')
          .replace(/```/g, '')
          .trim()

        try {
          // 处理包含函数的JSON字符串
          let processedJsonStr = echartsJsonStr

          // 提取并替换函数
          const functionMatches = []
          let functionIndex = 0
          processedJsonStr = processedJsonStr.replace(/function\s*\([^)]*\)\s*\{[\s\S]*?\}/g, (match) => {
            functionMatches.push(match)
            return `"__FUNCTION_PLACEHOLDER_${functionIndex++}__"`
          })

          // 提取并替换Array.from等表达式
          const arrayMatches = []
          let arrayIndex = 0
          processedJsonStr = processedJsonStr.replace(/Array\.from\([^)]*\)/g, (match) => {
            arrayMatches.push(match)
            return `"__ARRAY_PLACEHOLDER_${arrayIndex++}__"`
          })

          // 先解析基本结构
          const baseData = JSON.parse(processedJsonStr)

          // 恢复函数和表达式
          const restoreFunctions = (obj) => {
            if (typeof obj === 'object' && obj !== null) {
              for (const key in obj) {
                if (typeof obj[key] === 'string') {
                  // 恢复函数
                  const functionMatch = obj[key].match(/__FUNCTION_PLACEHOLDER_(\d+)__/)
                  if (functionMatch) {
                    const index = parseInt(functionMatch[1])
                    if (functionMatches[index]) {
                      const funcStr = functionMatches[index]
                      const funcBody = funcStr.replace(/^function\s*\([^)]*\)\s*\{/, '').replace(/\}$/, '')
                      const paramMatch = funcStr.match(/function\s*\(([^)]*)\)/)
                      const params = paramMatch ? paramMatch[1] : 'params'
                      obj[key] = new Function(params, funcBody)
                    }
                  }

                  // 恢复Array表达式
                  const arrayMatch = obj[key].match(/__ARRAY_PLACEHOLDER_(\d+)__/)
                  if (arrayMatch) {
                    const index = parseInt(arrayMatch[1])
                    if (arrayMatches[index]) {
                      try {
                        obj[key] = eval(arrayMatches[index])
                      } catch (e) {
                        console.warn('无法恢复Array表达式:', e)
                        obj[key] = []
                      }
                    }
                  }
                } else if (typeof obj[key] === 'object') {
                  restoreFunctions(obj[key])
                }
              }
            }
          }

          restoreFunctions(baseData)
          echartsData = baseData
        } catch (error) {
          console.error('解析图表数据失败:', error)
          // 如果解析失败，尝试使用eval（不推荐，但作为备选方案）
          try {
            echartsData = eval('(' + echartsJsonStr + ')')
          } catch (evalError) {
            console.error('eval解析也失败:', evalError)
          }
        }
        aiContent = textContent
      }

      const aiMessage = {
        key: keyCounter++,
        role: 'ai',
        placement: 'start',
        content: aiContent,
        loading: false,
        shape: 'corner',
        variant: 'filled',
        isMarkdown: true,
        typing: false,
        maxWidth: '1700px'
      }

      // 如果有图表数据，添加到消息中
      if (echartsData) {
        aiMessage.echartsJSONData = echartsData
      }

      // 如果有引用资源，添加到消息中
      if (message.retriever_resources && message.retriever_resources.length > 0) {
        aiMessage.quoteList = message.retriever_resources.map((resource) => ({
          ...resource,
          id: resource.document_id,
          fileName: resource.document_name
          // url: resource.url || ''
        }))
      }

      processedMessages.push(aiMessage)
    })

    return processedMessages
  }

  const handleSelectHistory = async (item) => {
    console.log('选择历史对话:', item)
    showWelcomePage.value = false

    try {
      // 获取历史消息
      const messages = await getConversationMessages(item.id)
      console.log('获取到的历史消息:', messages)

      // 清空当前对话列表
      list.value = []

      // 处理历史消息并添加到列表中
      const processedMessages = processHistoryMessages(messages)
      console.log('处理后的消息:', processedMessages)

      // 更新key计数器，确保后续新消息的key不冲突
      key.value = processedMessages.length > 0 ? Math.max(...processedMessages.map((msg) => msg.key)) : 0

      // 设置处理后的消息到列表
      list.value = processedMessages

      // 设置当前会话ID（已在getConversationMessages中设置）
      // currentConversationId.value = item.id
    } catch (error) {
      console.error('加载历史对话失败:', error)
    }
  }

  // 工具栏操作处理
  const handleToolbarCommand = (command) => {
    if (command === 'select') {
      isSelectMode.value = !isSelectMode.value
      if (!isSelectMode.value) {
        selectedHistoryItems.value = []
      }
    } else if (command === 'clear') {
      // 清除所有对话的逻辑（已注释）
      console.log('清除所有对话')
    }
  }

  // 历史对话项选择处理
  const handleHistoryItemSelect = (checked, item) => {
    if (checked) {
      if (!selectedHistoryItems.value.includes(item.id)) {
        selectedHistoryItems.value.push(item.id)
      }
    } else {
      const index = selectedHistoryItems.value.indexOf(item.id)
      if (index > -1) {
        selectedHistoryItems.value.splice(index, 1)
      }
    }
  }

  // 确定选择（删除选中的对话）
  const handleConfirmSelection = async () => {
    if (selectedHistoryItems.value.length === 0) {
      return
    }

    console.log('selectedHistoryItems.value', selectedHistoryItems.value)
    const promiseList = selectedHistoryItems.value.map((item) => deleteConversation(item))

    try {
      await Promise.all(promiseList)
      isSelectMode.value = false
      selectedHistoryItems.value = []
      await getHistoryList()
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  }

  // 取消选择
  const handleCancelSelection = () => {
    isSelectMode.value = false
    selectedHistoryItems.value = []
  }

  return {
    inputValue,
    recording,
    submitLoading,
    list,
    showSender,
    currentConversationId,
    isCarryHistory,
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear,
    handleThinkingChange,
    handleFiles,
    // 文件选择相关
    showFileSelector,
    selectedFiles,
    handleFileConfirm,
    removeFile,
    clearAllFiles,
    formatFileSize,
    fileRequestApi,
    fileRequestParams,
    getConversations,
    deleteConversation,
    getConversationMessages,
    // 历史对话相关
    isHistoryCollapsed,
    searchKeyword,
    isSelectMode,
    selectedHistoryItems,
    historyList,
    historyGroups,
    filteredHistoryGroups,
    processConversationData,
    getHistoryList,
    toggleHistoryCollapse,
    handleSearchHistory,
    handleSelectHistory,
    handleToolbarCommand,
    handleHistoryItemSelect,
    handleConfirmSelection,
    handleCancelSelection,
    showWelcomePage,
    processHistoryMessages
  }
}
