<template>
  <div class="tabs-container">
    <div class="tabs-tabs">
      <div class="tabs-tabs-item" v-for="(item, index) in list" :key="item.id">
        <div class="icon"><img :src="getAssetsFile(`ScreenRight/Perception/Traffic/${item.icon}`)" /></div>
        <div class="content">
          <div class="content-title">{{ item.name }}</div>
          <div class="content-value">
            <span v-if="index == 0">{{ lib.utils.formatNumberText(item.number) }}</span>
            <span v-else>{{ item.number }}</span>
            <span>{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { watchEffect } from 'vue'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import { getAssetsFile } from '@/utils/index'
  import lib from '@/utils/lib.ts'

  const props = defineProps({
    type: {
      type: String,
      default: 'day'
    },
    lineName: {
      type: String,
      default: '东线'
    },
    date: {
      type: String,
      default: ''
    },
    carItemTop: {
      type: Object,
      default: () => {}
    },
    resultList: {
      type: Array,
      default: () => []
    }
  })

  const list = ref([
    { id: 1, name: '总车辆', number: '-', unit: '辆', icon: 'icon1.png', selected: true },
    { id: 2, name: '同比率', number: '-', unit: '%', icon: 'icon2.png', selected: false },
    { id: 3, name: '环比率', number: '-', unit: '%', icon: 'icon3.png', selected: false }
  ])

  watch(
    () => props.carItemTop,
    (value) => {
      if (value) {
        if (value.name == '客车') {
          const trucksItem = props.resultList.find((item) => item.name == '货车')
          list.value = [
            { id: 1, name: '总车辆', number: '-', unit: '辆', icon: 'icon1.png', selected: true },
            { id: 2, name: '同比率', number: '-', unit: '%', icon: 'icon2.png', selected: false },
            { id: 3, name: '环比率', number: '-', unit: '%', icon: 'icon3.png', selected: false },
            { id: 4, name: '客货比', number: `1:${(value.data / trucksItem.data).toFixed(2)}`, unit: '', icon: 'kh-process.png', selected: false }
          ]
        } else {
          list.value = [
            { id: 1, name: '总车辆', number: '-', unit: '辆', icon: 'icon1.png', selected: true },
            { id: 2, name: '同比率', number: '-', unit: '%', icon: 'icon2.png', selected: false },
            { id: 3, name: '环比率', number: '-', unit: '%', icon: 'icon3.png', selected: false }
          ]
        }
        list.value[0].name = value.name
        list.value[0].number = value.data || '-'
        list.value[1].number = value?.yOnY?.split('%')[0] || '-'
        list.value[2].number = value?.ring?.split('%')[0] || '-'
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<style lang="scss" scoped>
  .tabs-container {
    width: 100%;
    height: 57px;
    font-family: 'Alibaba PuHuiTi';
    .tabs-tabs {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 57px;
      .tabs-tabs-item {
        display: flex;
        flex: 1;
        height: 57px;
        .icon {
          width: 50px;
          height: 57px;
          img {
            width: 50px;
            height: 57px;
          }
        }
        .content {
          box-sizing: border-box;
          flex: 1;
          padding-top: 5px;
          padding-left: 6px;
          .content-title {
            font-size: 14px;
            font-weight: 400;
            line-height: 16px;
            color: #dbefff;
          }
          .content-value {
            margin-top: 6px;
            font-size: 21px;
            font-weight: bold;
            line-height: 24px;
            color: #ffffff;
            span {
              font-size: 16px;
            }
          }
        }
      }
    }
    .device-chart {
      width: 600px;
      height: 320px;
    }
  }
</style>
