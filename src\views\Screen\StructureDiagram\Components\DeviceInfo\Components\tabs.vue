<template>
  <div class="tab-container">
    <el-tabs v-model="activeName" class="channel-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="item in list" :key="item.id" :label="item.name" :name="item.id"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })

  const activeName = defineModel()
  const handleClick = (index) => {
    console.log(index)
  }
</script>

<style lang="scss" scoped>
  .tab-container {
    .channel-tabs {
      width: 300px;
      height: 25px;

      --el-text-color-primary: #ffffff;
      :deep(.el-tabs__nav-prev) {
        top: 10px;
        width: 12px;
        height: 16px;
        background-image: url('@/assets/StructureDiagram/device/prev.png');
        background-size: cover;
        .el-icon {
          display: none;
        }
      }
      :deep(.el-tabs__nav-next) {
        top: 10px;
        width: 12px;
        height: 16px;
        background-image: url('@/assets/StructureDiagram/device/next.png');
        background-size: cover;
        .el-icon {
          display: none;
        }
      }
      :deep(.el-tabs__item) {
        width: 80px;
        height: 25px;
        padding: 0;
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-weight: 400;
        text-align: center;
        &.is-active {
          color: #ffffff;
          background: url('@/assets/StructureDiagram/device/tabBg.png');
          background-size: contain;
        }
      }
      :deep(.el-tabs__nav-wrap::after) {
        display: none;
      }
      :deep(.el-tabs__active-bar) {
        display: none;
      }
    }
  }
</style>
