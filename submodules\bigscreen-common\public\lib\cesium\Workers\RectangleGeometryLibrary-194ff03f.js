/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Transforms-42ed7720","./Math-b5f4d889"],(function(t,n,a,e,o,s){"use strict";const r=Math.cos,i=Math.sin,c=Math.sqrt,g={computePosition:function(t,n,a,o,s,g,h){const u=n.radiiSquared,l=t.nwCorner,C=t.boundingRectangle;let d=l.latitude-t.granYCos*o+s*t.granXSin;const S=r(d),w=i(d),M=u.z*w;let f=l.longitude+o*t.granYSin+s*t.granXCos;const X=S*r(f),Y=S*i(f),m=u.x*X,p=u.y*Y,b=c(m*X+p*Y+M*w);if(g.x=m/b,g.y=p/b,g.z=M/b,a){const n=t.stNwCorner;e.defined(n)?(d=n.latitude-t.stGranYCos*o+s*t.stGranXSin,f=n.longitude+o*t.stGranYSin+s*t.stGranXCos,h.x=(f-t.stWest)*t.lonScalar,h.y=(d-t.stSouth)*t.latScalar):(h.x=(f-C.west)*t.lonScalar,h.y=(d-C.south)*t.latScalar)}}},h=new a.Matrix2;let u=new n.Cartesian3;const l=new a.Cartographic;let C=new n.Cartesian3;const d=new o.GeographicProjection;function S(t,e,o,s,r,i,c){const g=Math.cos(e),l=s*g,S=o*g,w=Math.sin(e),M=s*w,f=o*w;u=d.project(t,u),u=n.Cartesian3.subtract(u,C,u);const X=a.Matrix2.fromRotation(e,h);u=a.Matrix2.multiplyByVector(X,u,u),u=n.Cartesian3.add(u,C,u),i-=1,c-=1;const Y=(t=d.unproject(u,t)).latitude,m=Y+i*f,p=Y-l*c,b=Y-l*c+i*f,R=Math.max(Y,m,p,b),x=Math.min(Y,m,p,b),G=t.longitude,y=G+i*S,O=G+c*M,P=G+c*M+i*S;return{north:R,south:x,east:Math.max(G,y,O,P),west:Math.min(G,y,O,P),granYCos:l,granYSin:M,granXCos:S,granXSin:f,nwCorner:t}}g.computeOptions=function(t,n,e,o,r,i,c){let g,h=t.east,u=t.west,w=t.north,M=t.south,f=!1,X=!1;w===s.CesiumMath.PI_OVER_TWO&&(f=!0),M===-s.CesiumMath.PI_OVER_TWO&&(X=!0);const Y=w-M;g=u>h?s.CesiumMath.TWO_PI-u+h:h-u;const m=Math.ceil(g/n)+1,p=Math.ceil(Y/n)+1,b=g/(m-1),R=Y/(p-1),x=a.Rectangle.northwest(t,i),G=a.Rectangle.center(t,l);0===e&&0===o||(G.longitude<x.longitude&&(G.longitude+=s.CesiumMath.TWO_PI),C=d.project(G,C));const y=R,O=b,P=a.Rectangle.clone(t,r),W={granYCos:y,granYSin:0,granXCos:O,granXSin:0,nwCorner:x,boundingRectangle:P,width:m,height:p,northCap:f,southCap:X};if(0!==e){const t=S(x,e,b,R,0,m,p);w=t.north,M=t.south,h=t.east,u=t.west,W.granYCos=t.granYCos,W.granYSin=t.granYSin,W.granXCos=t.granXCos,W.granXSin=t.granXSin,P.north=w,P.south=M,P.east=h,P.west=u}if(0!==o){e-=o;const t=a.Rectangle.northwest(P,c),n=S(t,e,b,R,0,m,p);W.stGranYCos=n.granYCos,W.stGranXCos=n.granXCos,W.stGranYSin=n.granYSin,W.stGranXSin=n.granXSin,W.stNwCorner=t,W.stWest=n.west,W.stSouth=n.south}return W};var w=g;t.RectangleGeometryLibrary=w}));
