import { defineStore } from 'pinia'
import { bd09towgs84 } from '../../utils/cesiumUtils/cesiumMethod.js' // 坐标转换 百度转wgs84

const useCesiumStore = defineStore('screenCesium', {
  state: () => ({
    viewer: null,
    refCesium: null,
    entity: null,
    outline: null
  }),
  getters: {},
  actions: {
    setViewer(viewer) {
      this.viewer = viewer
    },
    setRefCesium(refCesium) {
      this.refCesium = refCesium
      this.entity = refCesium.createEntity()
      this.outline = refCesium.outline()
    },
    /**
     * @description: 切换视角
     * @param {Object} data 视口数据
     * @param {Boolean} noMove 是否需要动画效果 默认无动画
     */
    flyTo(data, noMove = true) {
      console.log('data', data);
      noMove ? this.refCesium.flyTo(Object.assign({}, data, { duration: 0 })) : this.refCesium.flyTo(data)
    },

    /**
     * @description: 生成image标签
     * @param {Array} [item]
     * @param {Object} item
     * @param {Object} item.position 百度地图经纬度坐标
     * @param {Number} item.position.longitude 经度坐标
     * @param {Number} item.position.latitude 纬度坐标
     * @param {String} item.id 唯一标识
     * @param {String} item.type 类型，可重复
     * @param {Image} item.image 图片
     * @param {String} type 坐标类型，默认wgs84 'wgs84' / 'baidu'
     * @author: qian
     */
    addBillboard(data, type = 'wgs84') {
      if (!data.length) return
      let examples = []
      data.forEach((item, index) => {
        let id = 'image' + Math.random().toString(16).slice(2) + index
        // 是否有默认高度 没有传高度，默认为0
        const height = !item.position.height ? 0 : item.position.height
        if (type == 'wgs84') {
          item.position = { longitude: item.position.longitude, latitude: item.position.latitude, height }
        } else if (type == 'baidu') {
          // 坐标转换 百度转wgs84
          const wgs84 = bd09towgs84(item.position.longitude, item.position.latitude)
          item.position = { longitude: wgs84[0], latitude: wgs84[1], height }
        }
        item.id = item.id ? item.id : id

        // 点击事件 待优化
        if (!item.content) {
          item.content = {}
        }
        if (item.clickFunction) {
          item.content.clickFunction = item.clickFunction
        }
        // 判断是否是gif
        let example = item.image.endsWith('.gif') ? this.entity.addGifBillboard(item) : this.refCesium.addBillboard(item)

        examples.push(example)
      })
      return examples
    },
    // 根据id删除部分image标签数组
    removeBillboard(data) {
      if (!data.length) return
      data.forEach((item) => {
        let id = typeof item.id === 'string' ? item.id : item.id.id
        // 判断是否是gif
        typeof item.image == 'string' ? this.refCesium.removeBillboardById(id) : this.entity.removeEntityById(id)
      })
    },
    /**
     * @description: 渲染线段
     * @param {Array} data 线段数据 // [{longitude: '', latitude: ''},{longitude: '', latitude: ''}] // [longitude, latitude，longitude, latitude]
     * @param {String} type 坐标类型，默认wgs84 'wgs84' / 'baidu'
     * @param {arrType} type 坐标数组类型，默认arr 'arr' / 'Cartesian3'
     */
    addPolyline(data, type = 'wgs84', arrType = 'arr') {
      if (!data.length) return
      let entitys = []
      data.forEach((item, index) => {
        let id = 'polyline' + Math.random().toString(16).slice(2) + index
        let positions = []
        let line = []

        const polyline = item.polyline ? item.polyline : item
        let firstValue = polyline.positions[0]
        if (typeof firstValue === 'object') {
          polyline.positions.forEach((val) => {
            if (type == 'wgs84' || type == 'Cartesian3') {
              positions.push(val.longitude)
              positions.push(val.latitude)
            } else if (type == 'baidu') {
              // 坐标转换 百度转wgs84
              const wgs84 = bd09towgs84(val.longitude, val.latitude)
              positions.push(wgs84[0])
              positions.push(wgs84[1])
            }
          })
        } else {
          if (type == 'wgs84' || type == 'Cartesian3') {
            positions = polyline.positions
          } else if (type == 'baidu') {
            // 坐标转换 百度转wgs84
            const chunkSize = 2
            const result = Array.from({ length: Math.ceil(longArray.length / chunkSize) }, (value, index) =>
              longArray.slice(index * chunkSize, index * chunkSize + chunkSize)
            )
            result.forEach((val) => {
              const wgs84 = bd09towgs84(val[0], val[1])
              positions.push(wgs84[0])
              positions.push(wgs84[1])
            })
          }
        }
        polyline.positions = arrType == 'Cartesian3' ? Cesium.Cartesian3.fromDegreesArray(positions) : positions
        polyline.clampToGround = true // 线段交汇处重叠不变色
        line = {
          id: item.id ? item.id : id,
          polyline,
          content: {},
          show: item.show
        }
        delete polyline['show']
        // 点击事件 待优化
        if (polyline.clickFunction) {
          line.content.clickFunction = polyline.clickFunction
        } else if (item.content && item.content.clickFunction) {
          line.content.clickFunction = item.content.clickFunction
        }
        let entity = this.entity.addEntity(line)
        entitys.push(entity)
      })
      return entitys
    },
    // 销毁线段
    removePolyline(data) {
      if (!data.length) return
      data.forEach((item) => {
        this.entity.removeEntityById(item.id)
      })
    },
    // 删除全部
    removeAll() {
      this.refCesium.removeAllBillboards()
      this.refCesium.removeAllLabels()
      // this.refCesium.removeLotsLabel()
      // this.viewer.scene.primitives.removeAll() // ! 此方法会清除白模
      this.entity.removeAllEntity()
      this.outline.removeOutline()
      // this.viewer.dataSources.removeAll() // 路网
    },
    // 注册cesium点击事件
    onClickCesium() {
      // 监听鼠标点击事件
      this.viewer.screenSpaceEventHandler.setInputAction((movement) => {
        const feature = this.viewer.scene.pick(movement.position) // 点击的物体
        // console.log(this.viewer.scene.drillPick(movement.position))
        if (feature) {
          if (feature.id) {
            // 点击非模型
            if (feature.id.content && feature.id.content.clickFunction && typeof feature.id.content.clickFunction === 'function') {
              // // todo 根据是否存在 feature.collection 去判断实现方式是primitive还是是entity
              const data = feature.collection ? feature.primitive : feature.id
              feature.id.content.clickFunction(data, movement.position) // 对象
            }
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    },
    /**
     * @description: 加载gltf文件
     * @param {Object} options
     * @param {Number} options.x 经度
     * @param {Number} options.y 纬度
     * @param {Number} options.z 高度
     * @param {Number} options.scale 缩放比例，默认1
     * @param {Object} options.distanceDisplayCondition 缩放比例，默认1
     * @param {String} options.gltfName gltf文件名，存储于public/gltfModel文件夹中
     */
    addGltfModel(options,cb) {
      const originalOption = {
        x: 0,
        y: 0,
        z: 0, //默认高度为0
        scale: 1, //默认缩放1
        gltfName: '',
        distanceDisplayCondition:undefined
      }
      const option = { ...originalOption, ...options }
      const modelItem = Cesium.Model.fromGltf({
        url: 'gltfModel/' + option.gltfName,
        modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(
          Cesium.Cartesian3.fromDegrees(option.x, option.y, option.z) //模型位置
        ),
        scale: option.scale, //模型大小
        id:options?.item||{}

      })
      cb(modelItem)
      const model = this.viewer.scene.primitives.add(modelItem)
      
      if(option.distanceDisplayCondition){
        model.distanceDisplayCondition=option.distanceDisplayCondition
      }
      model.readyPromise.then(function () {
        model.activeAnimations.addAll({
          loop: Cesium.ModelAnimationLoop.REPEAT
        })
      })
    },
    removeAllGltfModel(){
      this.viewer.scene.primitives.removeAll()
    },
    removeGltfModel(options) {
      this.viewer.scene.primitives.remove(options)
    }
  }
})

export default useCesiumStore
