<!--
 * @Description: 地图上方面板
 * @Autor: qian
 * @Date: 2023-06-19 17:04:41
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2024-06-11 16:40:13
-->
<template>
  <div class="ScreenMiddlePage">
    <!-- <Teleport to="#ScreenBox" v-if="isMounted && (activePage == 'Emergency' || activePage == 'Municipal')">
      <center-top></center-top>
    </Teleport>
    <Teleport to="#ScreenBox" v-if="isMounted">
      <class-top left="747px" top="165px"></class-top>
    </Teleport> -->
    <SwitchButtons></SwitchButtons>

    <component :is="activePage"></component>
    <!-- <process-com :top="styleItem['ProcessCom'].top" :right="styleItem['ProcessCom'].right"></process-com>
    <LayerButton :top="styleItem['LayerButton'].top" :left="styleItem['LayerButton'].left" /> -->
    <BottomNavigation v-if="activePage !== 'HomePage'"></BottomNavigation>
  </div>
</template>
<script>
  import HomePage from './Components/HomePage/index.vue'

  export default {
    name: 'ScreenMiddlePage',
    components: { HomePage }
  }
</script>
<script setup>
  import { computed, onMounted, ref } from 'vue'

  import { toUe5 } from '@/hooks/useUE/tools.js'
  import useStore from '@/store'
  const { appContext } = getCurrentInstance()
  import debounce from 'lodash/debounce'

  const { screen } = useStore()
  import lib from '@/utils/lib'
  const activePage = computed(() => {
    return screen.activePage
  })

  const isMounted = ref(false)
  onMounted(() => {
    isMounted.value = true
  })
  // 点击设施
  // const clickModel = lib.provideTools.clickModel.inject()

  // 点击设施
  const handleClick = debounce((val) => {
    const obj = val.object
    if (!obj || !obj.modelCode) return
    console.log('点击设施----', obj)
    lib.popWindow.removeDialog('structureWindow')
    lib.api.getPopupDetail.structureResumeCardStructure({ code: obj.modelCode }).then((res) => {
      if (res.success && res.result) {
        // 模型高亮
        const modelRenderList = {
          models: [
            {
              modelCode: obj.modelCode,
              hide: false, // 非必填,true为隐藏该构件，包括渲染和物理碰撞，false为取消隐藏
              color: [24, 147, 255, 0.7 * 255] // 非必填（有color的话是添加着色，没有color的话是取消着色）
            }
          ]
        }
        toUe5('modelRender', modelRenderList)
        const id = obj.modelId
        toUe5('openCustomPOIWindow', {
          id: id,
          isOpen: true
        })
        lib.popWindow.createPopWindow(
          './Components/StructureFile/index.vue',
          {
            left: 1420,
            top: 580,
            tag: 'structureWindow',
            appContext,
            appendParent: 'player',
            draggable: true,
            closeFunc: () => {
              toUe5('openCustomPOIWindow', {
                id: id,
                isOpen: false
              })
              // 取消着色
              const modelRenderList = {
                models: [
                  {
                    modelCode: obj.modelCode,
                    hide: false, // 非必填,true为隐藏该构件，包括渲染和物理碰撞，false为取消隐藏
                    color: [] // 非必填（有color的话是添加着色，没有color的话是取消着色）
                  }
                ]
              }
              toUe5('modelRender', modelRenderList)
            }
          },
          {
            ...res.result,
            code: obj.modelCode
          }
        )
      }
    })
  }, 1000) // 设置 debounce 的延迟时间为 1000 毫秒
</script>
<style lang="scss" scoped>
  .ScreenMiddlePage {
    position: absolute;
    width: 100%;
    height: 100%;
  }
</style>
