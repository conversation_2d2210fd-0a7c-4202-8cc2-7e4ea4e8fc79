define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./VertexFormat-9b18d410"],(function(e,t,r,a,n,o,i,m,u,p,c,y,f,s){"use strict";function d(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT);var r=e.defaultValue(t.vertexFormat,s.VertexFormat.DEFAULT);this._vertexFormat=r,this._workerName="createPlaneGeometry"}d.packedLength=s.VertexFormat.packedLength,d.pack=function(r,a,n){return t.Check.typeOf.object("value",r),t.Check.defined("array",a),n=e.defaultValue(n,0),s.VertexFormat.pack(r._vertexFormat,a,n),a};var b=new s.VertexFormat,l={vertexFormat:b};d.unpack=function(r,a,n){t.Check.defined("array",r),a=e.defaultValue(a,0);var o=s.VertexFormat.unpack(r,a,b);return e.defined(n)?(n._vertexFormat=s.VertexFormat.clone(o,n._vertexFormat),n):new d(l)};var v=new r.Cartesian3(-.5,-.5,0),A=new r.Cartesian3(.5,.5,0);function F(t,r){return e.defined(r)&&(t=d.unpack(t,r)),d.createGeometry(t)}return d.createGeometry=function(e){var t,a,o=e._vertexFormat,i=new f.GeometryAttributes;if(o.position){if(a=new Float64Array(12),a[0]=v.x,a[1]=v.y,a[2]=0,a[3]=A.x,a[4]=v.y,a[5]=0,a[6]=A.x,a[7]=A.y,a[8]=0,a[9]=v.x,a[10]=A.y,a[11]=0,i.position=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:a}),o.normal){var m=new Float32Array(12);m[0]=0,m[1]=0,m[2]=1,m[3]=0,m[4]=0,m[5]=1,m[6]=0,m[7]=0,m[8]=1,m[9]=0,m[10]=0,m[11]=1,i.normal=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:m})}if(o.st){var u=new Float32Array(8);u[0]=0,u[1]=0,u[2]=1,u[3]=0,u[4]=1,u[5]=1,u[6]=0,u[7]=1,i.st=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:u})}if(o.tangent){var s=new Float32Array(12);s[0]=1,s[1]=0,s[2]=0,s[3]=1,s[4]=0,s[5]=0,s[6]=1,s[7]=0,s[8]=0,s[9]=1,s[10]=0,s[11]=0,i.tangent=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:s})}if(o.bitangent){var d=new Float32Array(12);d[0]=0,d[1]=1,d[2]=0,d[3]=0,d[4]=1,d[5]=0,d[6]=0,d[7]=1,d[8]=0,d[9]=0,d[10]=1,d[11]=0,i.bitangent=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:d})}t=new Uint16Array(6),t[0]=0,t[1]=1,t[2]=2,t[3]=0,t[4]=2,t[5]=3}return new c.Geometry({attributes:i,indices:t,primitiveType:y.PrimitiveType.TRIANGLES,boundingSphere:new n.BoundingSphere(r.Cartesian3.ZERO,Math.sqrt(2))})},F}));