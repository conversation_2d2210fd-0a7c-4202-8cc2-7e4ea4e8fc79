<!--
 * @Description: 右键菜单信息弹窗
 * @Autor: yaozhen
 * @Date: 2023-06-28 14:39:58
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2023-10-12 10:44:11
-->
<template>
  <div class="menu-Info">
    <el-button type="primary" @click="allCopy">全部复制</el-button>
    <el-button type="primary" @click="allDelete">全部删除</el-button>
    <TabelList :tableHeader="tableHeader" :tableData="tableData" :showIndex="true" :highlight-current-row="false" max-height="233px"></TabelList>
    <div class="btn-box">
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>

<script setup lang="jsx">
  import { ElButton, ElMessage } from 'element-plus'
  import TabelList from '@Common/components/TabelList/index.vue'
  import { reactive, toRefs, inject } from 'vue'
  import useStore from '@Common/store'
  import { copyText } from '@Common/utils/index.js'
  const handleClose = inject('handleClose')
  const { contextMenu } = useStore()
  const state = reactive({
    tableHeader: [
      {
        prop: 'longitude',
        label: '经度'
      },
      {
        prop: 'latitude',
        label: '纬度'
      },
      {
        prop: 'operate',
        label: '操作',
        width: '100px',
        render: (h, { row, index }) => {
          return (
            <div>
              <span style="margin-right: 10px;" onClick={() => handleCopy(row, index)}>
                复制
              </span>
              <span onClick={() => handleDel(row, index)}>删除</span>
            </div>
          )
        }
      }
    ]
  })
  const { tableHeader } = toRefs(state)
  const { pointList: tableData } = toRefs(contextMenu)
  const handleDel = (row, index) => {
    __Cesium.setBillboard(row.examples, false)
    contextMenu.pointList.splice(index, 1)
  }

  const handleCopy = (row, index) => {
    const obj = { ...row }
    delete obj.examples
    const result = copyText(JSON.stringify(obj))
    ElMessage({
      message: result ? '复制成功' : '复制失败',
      type: result ? 'success' : 'warning'
    })
  }
  // 全部删除
  const allDelete = () => {
    tableData.value.forEach((item) => {
      __Cesium.setBillboard(item.examples, false)
    })
    contextMenu.pointList = []
  }
  // 全部复制
  const allCopy = () => {
    const list = tableData.value.map((item) => {
      const obj = { ...item }
      delete obj.examples
      return obj
    })
    const result = copyText(JSON.stringify(list))
    ElMessage({
      message: result ? '复制成功' : '复制失败',
      type: result ? 'success' : 'warning'
    })
  }
</script>

<style lang="scss" scoped>
  .menu-Info {
    cursor: move;
    width: 600px;
    box-sizing: border-box;
    padding: 10px;
    border: 1px solid #008aff70;
    box-shadow: 0 4px 15px 1px #02213bb3;
    background-color: rgba(23, 49, 71, 0.8);
    .btn-box {
      display: flex;
      justify-content: center;
      padding: 20px 0 10px 0;
    }
  }
</style>
