define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./IndexDatatype-e1c63859","./Plane-1b1689fd","./createTaskProcessorWorker","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./CreatePhysicalArray-935f3db5","./materem-d090bcd4","./EmWrapperManager-1ae94128","./EmLBDeal-04aa737f"],(function(e,r,t,a,n,o,i,I,d,s,y,f,u,c,m,A,l,N,T,b,p){"use strict";var v,P,O,w={DRACO_COMPRESSION:0,MATER_COMPRESSION:1,PROJECTIVE_TRANSFORM:2,POINT_CLOUD:3},E=Object.freeze(w);function C(e,r){for(var t=e.num_points(),a=e.num_faces(),n=new v.DracoInt32Array,o=3*a,i=f.IndexDatatype.createTypedArray(t,o),I=0,d=0;d<a;++d)r.GetFaceFromMesh(e,d,n),i[I+0]=n.GetValue(0),i[I+1]=n.GetValue(1),i[I+2]=n.GetValue(2),I+=3;return v.destroy(n),{typedArray:i,numberOfIndices:o}}function h(e,r,t,a,n){var o,i;a.quantizationBits<=8?(i=new v.DracoUInt8Array,o=new Uint8Array(n),r.GetAttributeUInt8ForAllPoints(e,t,i)):(i=new v.DracoUInt16Array,o=new Uint16Array(n),r.GetAttributeUInt16ForAllPoints(e,t,i));for(var I=0;I<n;++I)o[I]=i.GetValue(I);return v.destroy(i),o}function D(e,r,t,a){var n,o;switch(t.data_type()){case 1:case 11:o=new v.DracoInt8Array,n=new Int8Array(a),r.GetAttributeInt8ForAllPoints(e,t,o);break;case 2:o=new v.DracoUInt8Array,n=new Uint8Array(a),r.GetAttributeUInt8ForAllPoints(e,t,o);break;case 3:o=new v.DracoInt16Array,n=new Int16Array(a),r.GetAttributeInt16ForAllPoints(e,t,o);break;case 4:o=new v.DracoUInt16Array,n=new Uint16Array(a),r.GetAttributeUInt16ForAllPoints(e,t,o);break;case 5:case 7:o=new v.DracoInt32Array,n=new Int32Array(a),r.GetAttributeInt32ForAllPoints(e,t,o);break;case 6:case 8:o=new v.DracoUInt32Array,n=new Uint32Array(a),r.GetAttributeUInt32ForAllPoints(e,t,o);break;case 9:case 10:o=new v.DracoFloat32Array,n=new Float32Array(a),r.GetAttributeFloatForAllPoints(e,t,o);break}for(var i=0;i<a;++i)n[i]=o.GetValue(i);return v.destroy(o),n}function G(r,t,a){var n,o=r.num_points(),i=a.num_components(),I=new v.AttributeQuantizationTransform;if(I.InitFromAttribute(a)){for(var s=new Array(i),y=0;y<i;++y)s[y]=I.min_value(y);n={quantizationBits:I.quantization_bits(),minValues:s,range:I.range(),octEncoded:!1}}v.destroy(I),I=new v.AttributeOctahedronTransform,I.InitFromAttribute(a)&&(n={quantizationBits:I.quantization_bits(),octEncoded:!0}),v.destroy(I);var f,u=o*i;f=e.defined(n)?h(r,t,a,n,u):D(r,t,a,u);var c=d.ComponentDatatype.fromTypedArray(f);return{array:f,data:{componentsPerAttribute:i,componentDatatype:c,byteOffset:a.byte_offset(),byteStride:d.ComponentDatatype.getSizeInBytes(c)*i,normalized:a.normalized(),quantization:n}}}function F(e){var r=new v.Decoder;e.dequantizeInShader&&(r.SkipAttributeTransform(v.POSITION),r.SkipAttributeTransform(v.NORMAL));var t=new v.DecoderBuffer;t.Init(e.buffer,e.buffer.length);var a=r.GetEncodedGeometryType(t);if(a!==v.POINT_CLOUD)throw new i.RuntimeError("Draco geometry type must be POINT_CLOUD.");var n=new v.PointCloud,o=r.DecodeBufferToPointCloud(t,n);if(!o.ok()||0===n.ptr)throw new i.RuntimeError("Error decoding draco point cloud: "+o.error_msg());v.destroy(t);var I={},d=e.properties;for(var s in d)if(d.hasOwnProperty(s)){var y=d[s],f=r.GetAttributeByUniqueId(n,y);I[s]=G(n,r,f)}return v.destroy(n),v.destroy(r),I}function _(r){var a=new v.Decoder,n=["POSITION","NORMAL","COLOR","TEX_COORD"];if(r.dequantizeInShader)for(var I=0;I<n.length;++I)a.SkipAttributeTransform(v[n[I]]);var d=r.bufferView,s=new v.DecoderBuffer;s.Init(r.array,d.byteLength);var y=a.GetEncodedGeometryType(s);if(y!==v.TRIANGULAR_MESH)throw new i.RuntimeError("Unsupported draco mesh geometry type.");var f=new v.Mesh,u=a.DecodeBufferToMesh(s,f);if(!u.ok()||0===f.ptr)throw new i.RuntimeError("Error decoding draco mesh geometry: "+u.error_msg());v.destroy(s);var c={},m=[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY],A=[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],l=r.compressedAttributes;for(var T in l)if(l.hasOwnProperty(T)){var b=l[T],w=a.GetAttributeByUniqueId(f,b);if(c[T]=G(f,a,w),"POSITION"===T&&(r.projected||r.absPlaned)&&!r.isInstance){var E=r.isYUp,h=r.projectionString,D=r.dCenX,F=r.dCenY,_=r.dCenZ,S=new p.EmLBDeal,V=S.init(h,new t.Cartesian3(D,F,_));if(V){e.defined(r.planishBorderInfoAry)&&S.setPlanishBorderInfoAry(r.planishBorderInfoAry);var g=c[T].array,M=r.matrix;r.projected?S.computeProjToCartesianAry(g,E,o.Matrix4.fromArray(M)):S.computeCartesianToProjAry(g,E,o.Matrix4.fromArray(M));for(var B=new Float32Array(g.length),R=0;R<g.length;++R)B[R]=g[R],m[R%3]>B[R]&&(m[R%3]=B[R]),A[R%3]<B[R]&&(A[R%3]=B[R]);c[T].array=B}S.destroy()}}var U=C(f,a);if(r.isNeedPhy){var Y,x=e.defined(c["POSITION"])?c["POSITION"].array:void 0,L=e.defined(c["_BATCHID"])?c["_BATCHID"].array:void 0,k=U.typedArray,z=N.CreatePhysicalArray.createPhysicalArrayFromModel(P,O,r.primitiveMode,x,L,k,Y);c["_PHYSICAL"]={array:z}}var j={indexArray:U,attributeData:c,min:m,max:A};return v.destroy(f),v.destroy(a),j}function S(e,r){var a={},n=e.isYUp,i=e.projectionString,I=e.dCenX,d=e.dCenY,s=e.dCenZ,y=new p.EmLBDeal,f=[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY],u=[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],c=y.init(i,new t.Cartesian3(I,d,s));if(c){var m=new Float32Array(e.array.buffer,0),A=e.matrix;e.projected?y.computeProjToCartesianAry(m,n,o.Matrix4.fromArray(A)):y.computeCartesianToProjAry(m,n,o.Matrix4.fromArray(A));for(var l=new Float32Array(m.length),N=0;N<m.length;++N)l[N]=m[N];for(var T=0;T<m.length;++T)l[T]=m[T],f[T%3]>l[T]&&(f[T%3]=l[T]),u[T%3]<l[T]&&(u[T%3]=l[T]);a={attributeData:l,min:f,max:u},r.push(l.buffer)}return y.destroy(),a}function V(e,r,t){var a=d.ComponentDatatype.fromTypedArray(e);return{array:e,data:{componentsPerAttribute:r,componentDatatype:a,byteOffset:0,byteStride:d.ComponentDatatype.getSizeInBytes(a)*r,normalized:t,quantization:void 0}}}function g(r){var a=r.bufferView,n=new P.MaterPrimitiveDecoder;if(!n.Decode(r.array,a.byteLength))throw P.destroy(n),new i.RuntimeError("error mater compress.");for(var I,d=n.GetPtNum(),s=n.GetIndexNum(),y=f.IndexDatatype.createTypedArray(d,s),u=0;u<s;++u)y[u]=n.GetIndex(u);if(n.IsHaveEdgeCheck())for(I=new Int8Array(s),u=0;u<s;++u)I[u]=n.GetEdgeCheck(u);for(var c=n.GetInstanceNum(),m=n.GetInstanceBatchNum(),A=[],l=[],T=0;T<c;T++){for(var b=[],v=0;v<4;v++)for(var w=0;w<4;w++)b.push(n.GetInstanceMatVal(T,v,w));A[T]=b}for(var E=0;E<m;E++)l[E]=n.GetInstanceBatchId(E);var C={},h={instanceNum:c,instanceBatchNum:m,instanceIdxToMatAry:A,instanceIdxToBatchId:l},D=[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY],G=[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],F=new Float32Array(3*d);for(u=0;u<d;++u)F[3*u]=n.GetPtVal(u,0),F[3*u+1]=n.GetPtVal(u,1),F[3*u+2]=n.GetPtVal(u,2);if(C["POSITION"]=V(F,3,!1),(r.projected||r.absPlaned)&&!r.isInstance){var _=r.isYUp,S=r.projectionString,g=r.dCenX,M=r.dCenY,B=r.dCenZ,R=new p.EmLBDeal,U=R.init(S,new t.Cartesian3(g,M,B));if(U){var Y=C["POSITION"].array,x=r.matrix;r.projected?R.computeProjToCartesianAry(Y,_,o.Matrix4.fromArray(x)):R.computeCartesianToProjAry(Y,_,o.Matrix4.fromArray(x));for(var L=new Float32Array(Y.length),k=0;k<Y.length;++k)L[k]=Y[k],D[k%3]>L[k]&&(D[k%3]=L[k]),G[k%3]<L[k]&&(G[k%3]=L[k]);C["POSITION"].array=L}R.destroy()}if(n.IsHaveUV()){var z=new Float32Array(2*d);for(u=0;u<d;++u)z[2*u]=n.GetUVVal(u,0),z[2*u+1]=n.GetUVVal(u,1);C["TEXCOORD_0"]=V(z,2,!1)}if(n.IsHaveNormal()){var j=new Float32Array(3*d);for(u=0;u<d;++u)j[3*u]=n.GetNormalVal(u,0),j[3*u+1]=n.GetNormalVal(u,1),j[3*u+2]=n.GetNormalVal(u,2);C["NORMAL"]=V(j,3,!0)}if(n.IsHaveBatchId()){var H=new Float32Array(d);for(u=0;u<d;++u)H[u]=n.GetBatchId(u);C["_BATCHID"]=V(H,1,!1)}if(n.IsHaveMaterialId()){var q=new Float32Array(d);for(u=0;u<d;++u)q[u]=n.GetMaterialId(u);C["_MATERIALID"]=V(q,1,!1)}if(n.IsHaveOutlineCoord()){var W=new Int8Array(d);for(u=0;u<d;++u)W[u]=n.GetOutlineCoord(u);C["_OUTLINECOORD"]=V(W,1,!1)}if(r.isNeedPhy){var X=e.defined(C["POSITION"])?C["POSITION"].array:void 0,Z=e.defined(C["_BATCHID"])?C["_BATCHID"].array:void 0,J=y,Q=I,K=N.CreatePhysicalArray.createPhysicalArrayFromModel(P,O,r.primitiveMode,X,Z,J,Q);C["_PHYSICAL"]={array:K}}var $={indexArray:{typedArray:y,numberOfIndices:s},attributeData:C,instanceData:h,min:D,max:G};return P.destroy(n),$}function M(r,t){return e.defined(r.dracoType)&&r.dracoType===E.PROJECTIVE_TRANSFORM?S(r,t):e.defined(r.primitive)?e.defined(r.dracoType)&&r.dracoType===E.MATER_COMPRESSION?g(r):_(r):F(r)}function B(e,r){b.EmWrapperManager.initWebAssembly(r).then((function(){P=b.emMod,O=new P.LBSpaMgr,v=e,self.onmessage=c(M),self.postMessage(!0)}))}function R(r){var t=r.data,a=t.webAssemblyConfig;if(e.defined(a))return require([a.modulePath],(function(r){e.defined(a.wasmBinaryFile)?(e.defined(r)||(r=self.DracoDecoderModule),r(a).then((function(e){B(e,a.wasmBinaryFileES6)}))):B(r(),a.wasmBinaryFileES6)}))}return R}));