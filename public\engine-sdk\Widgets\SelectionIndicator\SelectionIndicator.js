import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import knockout from"../../ThirdParty/knockout.js";import getElement from"../getElement.js";import SelectionIndicatorViewModel from"./SelectionIndicatorViewModel.js";function SelectionIndicator(e,t){if(!defined(e))throw new DeveloperError("container is required.");e=getElement(e),this._container=e;var r=document.createElement("div");r.className="cesium-selection-wrapper",r.setAttribute("data-bind",'style: { "top" : _screenPositionY, "left" : _screenPositionX },css: { "cesium-selection-wrapper-visible" : isVisible }'),e.appendChild(r),this._element=r;var i="http://www.w3.org/2000/svg",o="M -34 -34 L -34 -11.25 L -30 -15.25 L -30 -30 L -15.25 -30 L -11.25 -34 L -34 -34 z M 11.25 -34 L 15.25 -30 L 30 -30 L 30 -15.25 L 34 -11.25 L 34 -34 L 11.25 -34 z M -34 11.25 L -34 34 L -11.25 34 L -15.25 30 L -30 30 L -30 15.25 L -34 11.25 z M 34 11.25 L 30 15.25 L 30 30 L 15.25 30 L 11.25 34 L 34 34 L 34 11.25 z",n=document.createElementNS(i,"svg:svg");n.setAttribute("width",160),n.setAttribute("height",160),n.setAttribute("viewBox","0 0 160 160");var s=document.createElementNS(i,"g");s.setAttribute("transform","translate(80,80)"),n.appendChild(s);var c=document.createElementNS(i,"path");c.setAttribute("data-bind","attr: { transform: _transform }"),c.setAttribute("d",o),s.appendChild(c),r.appendChild(n);var d=new SelectionIndicatorViewModel(t,this._element,this._container);this._viewModel=d,knockout.applyBindings(this._viewModel,this._element)}Object.defineProperties(SelectionIndicator.prototype,{container:{get:function(){return this._container}},viewModel:{get:function(){return this._viewModel}}}),SelectionIndicator.prototype.isDestroyed=function(){return!1},SelectionIndicator.prototype.destroy=function(){var e=this._container;return knockout.cleanNode(this._element),e.removeChild(this._element),destroyObject(this)};export default SelectionIndicator;