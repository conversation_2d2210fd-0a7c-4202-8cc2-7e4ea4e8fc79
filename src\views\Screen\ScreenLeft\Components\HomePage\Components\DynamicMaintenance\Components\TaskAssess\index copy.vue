<template>
  <!-- v-loading="isLoading" element-loading-background="rgba(122, 122, 122, 0)" -->
  <div class="work-control-container">
    <SubHeadLine>
      <div class="title">
        <span :class="tabCur === 0 ? 'select' : 'default'" @click="changeTab(0)">养护运营</span>
        <div class="line"></div>
        <span :class="tabCur === 1 ? 'select' : 'default'" @click="changeTab(1)">节点任务</span>
      </div>
      <div class="date-change">
        <DatePickerNew
          style="top: 0; right: -9px"
          v-model="type"
          :currentDate="date"
          @change="handleDateChange($event)"
          :options="dateTypeList"></DatePickerNew>
      </div>
    </SubHeadLine>
    <div class="tab">
      <div class="tab-item" :class="item.active ? 'active' : ''" v-for="(item, index) in tabList" :key="index" @click="handlerTab(item, index)">
        <div class="content">
          <img class="icon" :src="getAssetsFile(`ScreenLeft/DynamicMaintenance/${item.icon}.png`)" />
          <span>{{ item.name }}</span>
        </div>
        <div class="num" :style="{ color: item.color }">{{ item.num }}</div>
      </div>
    </div>
    <div class="content-list">
      <SmallHeadLine width="617px" :title-bg="'longheadline.png'">
        <template #title>{{ tabCur == 0 ? '养护列表' : '节点任务列表' }}</template>
        <div class="table-content">
          <tableList :headList="tabCur == 0 ? tableHead[0] : tableHead[1]" :list="tableData"></tableList>
        </div>
      </SmallHeadLine>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import DatePickerNew from '@/components/DatePickerNew/index.vue'
  import SmallHeadLine from '@/components/SmallHeadLine/index.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  import tableList from '@/components/TableList/index.vue'

  import { getAssetsFile } from '@/utils'
  import lib from '@/utils/lib'
  const tabCur = ref(0)
  const activeIndex = ref(0)
  const tabList = ref([
    {
      name: '养护内容数量',
      num: 0,
      icon: 'contentNum',
      active: true,
      color: '#00E7FF'
    },
    {
      name: '未完成数量',
      num: 0,
      icon: 'unfinishNum',
      active: false,
      color: '#FFB800'
    }
  ])
  const isLoading = ref(false)
  const dateTypeList = ref([
    { id: 1, label: '按月', selected: true, value: 'month' },
    { id: 2, label: '按季', selected: false, value: 'quarter' },
    { id: 3, label: '按年', selected: false, value: 'year' }
  ])
  const type = ref('month')
  const queryParams = ref({
    startDate: moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    endDate: moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
  })
  const date = moment().format('YYYY-MM-DD')
  const handleDate = ref(date)
  const handleDateChange = (val) => {
    handleDate.value = val
    console.log(val, 'handleDateChange', date)
  }
  const tableHead = ref([
    [
      {
        label: '内容',
        prop: 'content'
      },
      {
        label: '考核频率',
        prop: 'frequencyName',
        alarm: true
      },
      {
        label: '国标要求',
        prop: 'nationStandard',
        remark: 'inter_remark'
      },
      {
        label: '地标要求',
        prop: 'localStandard',
        remark: 'land_remark'
      },
      {
        label: '政府考核标准',
        prop: 'governStandard',
        remark: 'govern_remark'
      },
      {
        label: '完成情况',
        prop: 'finishRate',
        unit: ''
      }
    ],
    [
      {
        label: '时间',
        prop: 'taskLimitDate',
        formatDate: 'YYYY-MM-DD'
      },
      {
        label: '工作内容',
        prop: 'name'
      },
      // {
      //   label: '要求',
      //   prop: 'request'
      // },
      {
        label: '完成情况',
        prop: 'statusName'
        // dicp: 'task_mange'
      }
    ]
  ])
  const tableData = ref([])
  onMounted(() => {
    // getNodeData()
    // getData()
    // useIntervalFn(
    //   () => {
    //     getData()
    //   },
    //   1000 * 60
    //   // { immediateCallback: true }
    // )
    // setTimeout(() => {
    //   changeTab(0)
    // }, 500)
  })

  const getQuarterStartAndEnd = (year, quarter) => {
    const startMonth = (quarter - 1) * 3 // 计算季度的起始月份
    const endMonth = startMonth + 2 // 计算季度的结束月份

    const startDate = moment([year, startMonth, 1]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    const endDate = moment([year, endMonth, 1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')

    return {
      startDate,
      endDate
    }
  }
  const getMonthStartAndEnd = (year, month) => {
    // 注意：moment 的月份是从 0 开始的，所以 0 表示 1 月，1 表示 2 月，依此类推
    const startDate = moment([year, month - 1])
      .startOf('month')
      .format('YYYY-MM-DD HH:mm:ss')
    const endDate = moment([year, month - 1])
      .endOf('month')
      .format('YYYY-MM-DD HH:mm:ss')

    return {
      startDate,
      endDate
    }
  }
  // #region 养护运营接口
  const getData = () => {
    const objParams = {
      start: queryParams.value.startDate,
      end: queryParams.value.endDate,
      period: type.value
    }
    isLoading.value = true
    // tableData.value = []
    lib.api.bigscreenApi.workOrderStatistics(objParams).then((res) => {
      isLoading.value = false
      if (res.success) {
        tabList.value[0].num = res.result?.planTotal
        tabList.value[1].num = res.result?.unfinished
        workOrderContentList()
      }
    })
  }
  const workOrderContentList = () => {
    const params = {
      params: {
        period: type.value,
        computeFinishRate: true,
        startDate: queryParams.value.startDate,
        endDate: queryParams.value.endDate,
        unfinished: !!activeIndex.value,
        projectId: 28
      }
    }
    isLoading.value = true
    lib.api.bigscreenApi.workOrderContentList(params).then((res) => {
      isLoading.value = false
      if (res.success) {
        tableData.value = res.result
      }
    })
  }
  // #endregion 养护运营接口

  // #region 节点任务接口
  const getNodeData = () => {
    const objParams = {
      startDate: queryParams.value.startDate,
      endDate: queryParams.value.endDate,
      status: activeIndex.value == 1 ? [0, 1] : []
    }
    isLoading.value = true
    // tableData.value = []
    lib.api.bigscreenApi.pointTask(objParams).then((res) => {
      isLoading.value = false
      if (res.success) {
        tabList.value[0].num = res.result?.total
        tabList.value[1].num = res.result?.unfinished
        tableData.value = res.result?.list
      }
    })
  }
  // #endregion 节点任务接口
  const intervalTime = ref(null)
  const updateParams = () => {
    const year = moment(handleDate.value).format('YYYY')
    const month = moment(handleDate.value).format('MM')
    const season = moment(handleDate.value).format('Q')
    if (type.value == 'year') {
      // 年
      queryParams.value.startDate = `${year}-01-01 00:00:00`
      queryParams.value.endDate = `${year}-12-31 23:59:59`
    }
    if (type.value == 'quarter') {
      // 季度
      const { startDate, endDate } = getQuarterStartAndEnd(year, season)
      queryParams.value.startDate = startDate
      queryParams.value.endDate = endDate
    }
    if (type.value == 'month') {
      // 月
      queryParams.value.startDate = getMonthStartAndEnd(year, month).startDate
      queryParams.value.endDate = getMonthStartAndEnd(year, month).endDate
    }
    if (tabCur.value == 0) {
      getData()
    } else {
      getNodeData()
    }
    if (!intervalTime.value) {
      intervalTime.value = useIntervalFn(() => {
        if (tabCur.value == 0) {
          getData()
        } else {
          getNodeData()
        }
      }, 1000 * 60)
    }
  }
  watch(
    () => [handleDate.value, type.value],
    () => {
      updateParams()
    },
    { immediate: true }
  )
  const changeTab = (index) => {
    tabCur.value = index
    tabList.value[0].active = true
    tabList.value[1].active = false
    activeIndex.value = 0
    tableData.value = []
    if (tabCur.value == 0) {
      // 养护
      getData()
    } else {
      // 节点
      getNodeData()
    }
  }
  // 切换tab
  const handlerTab = (item, index) => {
    tabList.value.forEach((data) => {
      data.active = data.name === item.name
    })
    activeIndex.value = index
    if (tabCur.value == 0) {
      // 养护
      workOrderContentList() // 列表养护
    } else {
      // 节点
      getNodeData()
    }
  }
</script>

<style lang="scss" scoped>
  .work-control-container {
    width: 617px;
    height: 370px;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 280px;
      height: 34px;
      .line {
        width: 2px;
        height: 24px;
        background: #276897;
        border-radius: 0;
      }
      span {
        &.select {
          font-family: 'Alibaba PuHuiTi';
          font-size: 32px;
          font-weight: 500;
          color: #d5ebff;
          cursor: pointer;
        }
        &.default {
          font-family: 'Alibaba PuHuiTi';
          font-size: 32px;
          font-weight: 400;
          color: #8ec1ef;
          cursor: pointer;
        }
      }
    }
    .date-change {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 34px;
      .date-type-list {
        display: flex;
        font-size: 14px;
        cursor: pointer;
        .date-type-one {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 66px;
          height: 22px;
          margin-left: 12px;
          border-radius: 5px;
        }
        .date-type-select {
          background: #004d84;
          border: 1px solid #00b2ff;
        }
        .date-type-unselect {
          background: rgb(0 163 255 / 40%);
        }
      }
    }
    .tab {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .tab-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 296px;
        height: 87px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/taskAssessTab.png');
        background-size: 296px 87px;
        &.active {
          background: url('@/assets/ScreenLeft/DynamicMaintenance/taskAssessTabSelected.png');
          background-size: 296px 87px;
        }
      }
      .content {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 36px;
          height: 36px;
        }
        span {
          margin-top: 9px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 20px;
          font-weight: 400;
          line-height: 24px;
          color: #ffffff;
        }
      }
      .num {
        flex: 1;
        font-family: PangMenZhengDao;
        font-size: 32px;
        font-weight: 400;
        line-height: 34px;
        color: #ffffff;
        text-align: center;
      }
    }
    .content-list {
      margin-top: 25px;
      .table-content {
        position: absolute;
        top: 16px;
        width: 617px;
        height: 148px;
      }
    }
  }
</style>
