// Type definitions for dify-chatbox-sdk
// Generated on 2025-07-15T07:26:09.522Z
// This file was automatically generated and bundled

declare module 'DifyChatboxSDK' {
import type { Feedbacktype, IOnCompleted, IOnData, IOnError, IOnFile, IOnMessageEnd, IOnMessageReplace, IOnNodeFinished, IOnNodeStarted, IOnThought, IOnWorkflowFinished, IOnWorkflowStarted } from './base';
export { DifyChatboxComunication } from './iframe-communication-client';
export { DifyChatbot } from './DifyChatbot';
export * from './base';
export { BASE_URL, routes, DifyClientServer, CompletionClientServer, ChatClientServer, } from './DifyClientServer';
export { routes as webRoutes, DifyClientWeb, CompletionClientWeb, ChatClientWeb, } from './DifyClientWeb';
/**
 * Send a chat message with streaming response
 *
 * @param body - Request body with inputs, query, and optional conversation ID
 * @param callbacks - Callback functions for different events
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the request is complete
 */
export declare const sendChatMessage: (body: Record<string, any>, { onData, onCompleted, onThought, onFile, onError, getAbortController, onMessageEnd, onMessageReplace, onWorkflowStarted, onNodeStarted, onNodeFinished, onWorkflowFinished, }: {
    onData: IOnData;
    onCompleted: IOnCompleted;
    onFile: IOnFile;
    onThought: IOnThought;
    onMessageEnd: IOnMessageEnd;
    onMessageReplace: IOnMessageReplace;
    onError: IOnError;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted: IOnWorkflowStarted;
    onNodeStarted: IOnNodeStarted;
    onNodeFinished: IOnNodeFinished;
    onWorkflowFinished: IOnWorkflowFinished;
}, apiBaseUrl?: string) => Promise<void>;
/**
 * Helper function to handle streaming response from the Dify API
 * For use with the streamChatMessage and streamCompletionMessage methods
 * of the Web clients
 *
 * @param response - The Response object from fetch
 * @param callbacks - Callback functions for handling different events
 */
export declare const handleStreamingResponse: (response: Response, { onData, onCompleted, onThought, onFile, onError, onMessageEnd, onMessageReplace, onWorkflowStarted, onNodeStarted, onNodeFinished, onWorkflowFinished, }: {
    onData?: IOnData;
    onCompleted?: IOnCompleted;
    onFile?: IOnFile;
    onThought?: IOnThought;
    onMessageEnd?: IOnMessageEnd;
    onMessageReplace?: IOnMessageReplace;
    onError?: IOnError;
    onWorkflowStarted?: IOnWorkflowStarted;
    onNodeStarted?: IOnNodeStarted;
    onNodeFinished?: IOnNodeFinished;
    onWorkflowFinished?: IOnWorkflowFinished;
}) => void;
/**
 * Fetch conversations for a user
 *
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with conversations data
 */
export declare const fetchConversations: (apiBaseUrl?: string) => Promise<any>;
/**
 * Fetch chat messages for a conversation
 *
 * @param conversationId - ID of the conversation to fetch messages for
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with chat messages data
 */
export declare const fetchChatList: (conversationId: string, apiBaseUrl?: string) => Promise<any>;
/**
 * Delete a conversation
 *
 * @param conversationId - ID of the conversation to delete
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the conversation is deleted
 */
export declare const deleteConversation: (conversationId: string, apiBaseUrl?: string) => Promise<any>;
/**
 * Fetch application parameters
 *
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with application parameters
 */
export declare const fetchAppParams: (apiBaseUrl?: string) => Promise<any>;
/**
 * Update feedback for a message
 *
 * @param url - URL of the feedback endpoint
 * @param body - Feedback data
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the feedback is updated
 */
export declare const updateFeedback: ({ url, body, }: {
    url: string;
    body: Feedbacktype;
}, apiBaseUrl?: string) => Promise<any>;
/**
 * Generate or rename a conversation
 *
 * @param id - ID of the conversation to rename
 * @param name - New name for the conversation (optional)
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the conversation is renamed
 */
export declare const generationConversationName: (id: string, name?: string, apiBaseUrl?: string) => Promise<any>;
/**
 * Convert audio to text
 *
 * @param audioFile - Audio file to transcribe
 * @param audioType - MIME type of the audio file
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with transcription data
 */
export declare const audioToText: (audioFile: File, audioType?: string, apiBaseUrl?: string) => Promise<any>;


// From /DifyChatbot.d.ts
/**
 * Dify Chatbot SDK for embedding a chat interface in any web application
 * This class handles the creation and management of the chatbot UI and communication with the iframe
 */
export declare const MessageTypeObj: {
    ready: string;
    conversationCompleted: string;
    responseStream: string;
    userMessageSent: string;
    conversationHistory: string;
    error: string;
    feedbackUpdated: string;
    sendMessage: string;
    getConversationHistory: string;
    clearConversation: string;
    setInputs: string;
    getStatus: string;
    setLanguage: string;
};
export type MessageTypeEnum = 'ready' | 'conversation-completed' | 'response-stream' | 'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' | 'send-message' | 'get-conversation-history' | 'clear-conversation' | 'set-inputs' | 'get-status' | 'set-language';
/**
 * @typedef {'ready' |  'conversation-completed' |  'response-stream' |  'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' |  'send-message' |  'get-conversation-history' | 'clear-conversation' |  'set-inputs' |  'get-status' |  'set-language'} MessageTypeEnum
 */
type DifyChatbotOptions = {
    baseUrl: string;
    appKey: string;
    CHATBOT_CONFIG_NAME?: string;
    BUBBLE_BUTTON_ID?: string;
    BUBBLE_WINDOW_ID?: string;
    draggable?: boolean;
    ICONS?: {
        open: string;
        close: string;
    };
    bubbleWindowWidth?: number | string;
    bubbleWindowHeight?: number | string;
};
type SendMessageOptions = {
    query: string;
    files?: any[];
    inputs?: Record<string, any>;
};
export declare class DifyChatbot {
    private options;
    private iframe;
    private bubbleButton;
    private bubbleWindow;
    private isOpen;
    private communicationClient;
    private initialPosition;
    private dragOffset;
    private isDragging;
    /**
     * Create a new Dify Chatbot instance
     * @param options Configuration options for the chatbot
     */
    constructor(options: DifyChatbotOptions);
    /**
     * Initialize the chatbot UI elements
     */
    private init;
    /**
     * Create the chat bubble button element
     */
    private createBubbleButton;
    /**
     * Create the chat window element
     */
    private createBubbleWindow;
    /**
     * Create the iframe for the chatbot
     */
    private createIframe;
    /**
     * Initialize communication with the iframe
     */
    private initIframeCommunication;
    /**
     * Attach events to the bubble button
     */
    private attachButtonEvents;
    /**
     * Toggle the chat window visibility
     */
    private toggleChatWindow;
    /**
     * Attach drag events to the chat window if draggable is enabled
     */
    private attachDragEvents;
    /**
     * Send a message to the chatbot
     * @param query The message to send
     * @param files Optional files to attach
     * @param inputs Optional input variables
     */
    sendMessage({ query, files, inputs, }: SendMessageOptions): void;
    /**
     * Clear the current conversation
     */
    clearConversation(): void;
    /**
     * Set input variables for the conversation
     * @param inputs Key-value pairs of input variables
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * Request the conversation history
     */
    getConversationHistory(): void;
    /**
     * Set the interface language
     * @param language The language code
     */
    setLanguage(language: string): void;
    /**
     * Register a custom event handler
     * @param {MessageTypeEnum} type The message type to handle
     * @param handler The callback function
     */
    on(type: 'ready' | 'conversation-completed' | 'response-stream' | 'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' | 'send-message' | 'get-conversation-history' | 'clear-conversation' | 'set-inputs' | 'get-status' | 'set-language', handler: (data: any) => void): void;
    /**
     * Open the chat window
     */
    open(): void;
    /**
     * Close the chat window
     */
    close(): void;
    /**
     * Destroy the chatbot instance and clean up resources
     */
    destroy(): void;
}
export {};


// From /DifyClientServer.d.ts
export declare const BASE_URL = "https://api.dify.ai/v1";
export declare const routes: {
    application: {
        method: string;
        url: () => string;
    };
    feedback: {
        method: string;
        url: (message_id: string) => string;
    };
    createCompletionMessage: {
        method: string;
        url: () => string;
    };
    createChatMessage: {
        method: string;
        url: () => string;
    };
    getConversationMessages: {
        method: string;
        url: () => string;
    };
    getConversations: {
        method: string;
        url: () => string;
    };
    renameConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    deleteConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    fileUpload: {
        method: string;
        url: () => string;
    };
    runWorkflow: {
        method: string;
        url: () => string;
    };
    audioToText: {
        method: string;
        url: () => string;
    };
};
/**
 * Base client for interacting with the Dify API
 * This class is designed to be used in Node.js environments
 * since exposing API keys in browsers is not secure
 */
export declare class DifyClientServer {
    protected apiKey: string;
    protected baseUrl: string;
    /**
     * Create a new Dify client
     *
     * @param apiKey - Your Dify API key
     * @param baseUrl - The Dify API base URL, defaults to https://api.dify.ai/v1
     */
    constructor(apiKey: string, baseUrl?: string);
    /**
     * Update the API key used by the client
     *
     * @param apiKey - Your new Dify API key
     */
    updateApiKey(apiKey: string): void;
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param stream - Whether to request a streaming response
     * @param headerParams - Additional headers to include in the request
     * @returns Response from the Dify API
     */
    sendRequest(method: string, endpoint: string, data?: any, params?: any, stream?: boolean, headerParams?: Record<string, string>): Promise<any>;
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    messageFeedback(message_id: string, rating: string, user: string): Promise<any>;
    /**
     * Get application parameters
     *
     * @param user - User identifier
     * @returns Application parameters from the Dify API
     */
    getApplicationParameters(user: string): Promise<any>;
    /**
     * Upload a file to the Dify API
     *
     * @param data - Form data containing the file to upload
     * @returns Response from the Dify API
     */
    fileUpload(data: any): Promise<any>;
    /**
     * Convert audio to text
     *
     * @param data - Form data containing the audio file
     * @returns Transcription response from the Dify API
     */
    audioToText(data: any): Promise<any>;
}
/**
 * Client for interacting with the Dify Completion API
 */
export declare class CompletionClientServer extends DifyClientServer {
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    createCompletionMessage(inputs: Record<string, any>, user: string, stream?: boolean, files?: any): Promise<any>;
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    runWorkflow(inputs: Record<string, any>, user: string, stream?: boolean, files?: any): Promise<any>;
}
/**
 * Client for interacting with the Dify Chat API
 */
export declare class ChatClientServer extends DifyClientServer {
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat message
     * @param query - The user's message/query
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param conversation_id - ID of the conversation to add the message to
     * @param files - Files to include with the message
     * @returns Response from the Dify API
     */
    createChatMessage(inputs: Record<string, any>, query: string, user: string, stream?: boolean, conversation_id?: string | null, files?: any): Promise<any>;
    /**
     * Get messages from a conversation
     *
     * @param user - User identifier
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Chat messages from the Dify API
     */
    getConversationMessages(user: string, conversation_id?: string, first_id?: string | null, limit?: number | null): Promise<any>;
    /**
     * Get a list of conversations
     *
     * @param user - User identifier
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns List of conversations from the Dify API
     */
    getConversations(user: string, first_id?: string | null, limit?: number | null, pinned?: boolean | null): Promise<any>;
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param user - User identifier
     * @param auto_generate - Whether to auto-generate the name
     * @returns Response from the Dify API
     */
    renameConversation(conversation_id: string, name: string, user: string, auto_generate?: boolean): Promise<any>;
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    deleteConversation(conversation_id: string, user: string): Promise<any>;
}


// From /DifyClientWeb.d.ts
/**
 * Web client for interacting with the Dify API from browsers
 * This implementation uses the fetch API and is designed for frontend use
 */
import type { IOnCompleted, IOnData, IOnError, IOnFile, IOnMessageEnd, IOnMessageReplace, IOnNodeFinished, IOnNodeStarted, IOnThought, IOnWorkflowFinished, IOnWorkflowStarted, IOtherOptions } from './base';
export type StreamChatParams = {
    /** 输入参数 */
    inputs: Record<string, any>;
    /** 用户消息 */
    query: string;
    /** 会话ID */
    conversation_id?: string;
    /** 用户标识 */
    user?: string;
    /** 附带的文件 */
    files?: ChatSendFile[];
};
export type StreamChatCallbacks = {
    onData: IOnData;
    onCompleted: IOnCompleted;
    onFile: IOnFile;
    onThought: IOnThought;
    onMessageEnd: IOnMessageEnd;
    onMessageReplace: IOnMessageReplace;
    onError: IOnError;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted: IOnWorkflowStarted;
    onNodeStarted: IOnNodeStarted;
    onNodeFinished: IOnNodeFinished;
    onWorkflowFinished: IOnWorkflowFinished;
};
export type ChatSendFile = {
    type: string;
    transfer_method: string;
    url: string;
    upload_file_id: string;
};
export type MessageFile = {
    id: string;
    filename: string;
    type: string;
    url: string;
    mime_type: string;
    size: number;
    transfer_method: string;
    belongs_to: string;
};
export type ConversationMessage = {
    id: string;
    conversation_id: string;
    parent_message_id: string;
    inputs: {
        [key: string]: any;
    };
    query: string;
    answer: string;
    message_files: MessageFile[];
    feedback: any;
    retriever_resources: any[];
    created_at: number;
    agent_thoughts: any[];
    status: string;
    error: any;
};
export declare const routes: {
    application: {
        method: string;
        url: () => string;
    };
    feedback: {
        method: string;
        url: (message_id: string) => string;
    };
    createCompletionMessage: {
        method: string;
        url: () => string;
    };
    createChatMessage: {
        method: string;
        url: () => string;
    };
    stopChatMessage: {
        method: string;
        url: (task_id: string) => string;
    };
    getConversationMessages: {
        method: string;
        url: () => string;
    };
    getConversations: {
        method: string;
        url: () => string;
    };
    renameConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    deleteConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    fileUpload: {
        method: string;
        url: () => string;
    };
    runWorkflow: {
        method: string;
        url: () => string;
    };
    audioToText: {
        method: string;
        url: () => string;
    };
    datasets: {
        method: string;
        url: () => string;
    };
    createDocumentByFile: {
        method: string;
        url: (dataset_id: string) => string;
    };
    getDocumentIndexingStatus: {
        method: string;
        url: (dataset_id: string, batch: string) => string;
    };
    getDatasetDocuments: {
        method: string;
        url: (dataset_id: string) => string;
    };
    getDatasetDocument: {
        method: string;
        url: (dataset_id: string, document_id: string) => string;
    };
    deleteDatasetDocument: {
        method: string;
        url: (dataset_id: string, document_id: string) => string;
    };
};
/**
 * Base web client for interacting with the Dify API from browsers
 * This implementation is suitable for browser environments where
 * API authentication can be handled by the server
 */
export declare class DifyClientWeb {
    _enableWebSearch: boolean;
    protected baseUrl: string;
    protected appKey: string;
    protected sessionId?: string;
    protected inputs: Record<string, any>;
    /**
     * Create a new Dify web client
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     */
    constructor(baseUrl?: string, appKey?: string);
    get enableWebSearch(): boolean;
    set enableWebSearch(value: boolean);
    /**
     * Get the singleton instance of DifyClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): DifyClientWeb;
    /**
     * Set or update the app key
     *
     * @param appKey - The key to identify which application configuration to use
     */
    setAppKey(appKey: string): void;
    /**
     * Set inputs object (overwrites existing inputs)
     *
     * @param inputs - Input parameters to set
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * set conversion sessionId
     * @param sessionId
     */
    setSessionId(sessionId: string): void;
    /**
     * Add inputs to existing inputs object (merges with existing)
     *
     * @param inputs - Input parameters to add
     */
    addInputs(inputs: Record<string, any>): void;
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param headerParams - Additional headers to include in the request
     * @returns Promise with the response data
     */
    sendRequest(method: string, endpoint: string, data?: any, params?: any, headerParams?: Record<string, string>, otherOptions?: IOtherOptions): Promise<any>;
    /**
     * Get application parameters
     *
     * @returns Application parameters from the Dify API
     */
    getApplicationParameters(): Promise<any>;
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user_id - Optional user identifier
     * @returns Response from the Dify API
     */
    messageFeedback(message_id: string, rating: 'like' | 'dislike', user_id?: string): Promise<any>;
    /**
     * Upload a file to the Dify API
     *
     * @param file - File to upload
     * @returns Promise that resolves with the uploaded file ID
     */
    fileUpload(file: File): Promise<{
        id: string;
        name: string;
        mime_type: string;
        size: number;
        extension: string;
    }>;
    /**
     * Convert audio to text
     *
     * @param audioFile - Audio file to transcribe
     * @param audioType - MIME type of the audio file
     * @returns Transcription response from the Dify API
     */
    audioToText(audioFile: File, audioType?: string): Promise<any>;
    getDatasets(): Promise<any>;
    createDocumentByFile(dataset_id: string, data: FormData): Promise<any>;
    getDocumentIndexingStatus(dataset_id: string, batch: string): Promise<any>;
    getDatasetDocuments(dataset_id: string, keyword?: string, page?: number, limit?: number): Promise<any>;
    deleteDatasetDocument(dataset_id: string, document_id: string): Promise<any>;
    getDatasetDocument(dataset_id: string, document_id: string): Promise<any>;
    /**
     * Get session ID from localStorage as fallback when cookies fail
     */
    private getSessionFromStorage;
    /**
     * Save session ID to localStorage as fallback
     */
    private saveSessionToStorage;
    /**
     * Extract session ID from API response and save it
     */
    private handleSessionFromResponse;
}
/**
 * Completion client for web browsers
 */
export declare class CompletionClientWeb extends DifyClientWeb {
    /**
     * Get the singleton instance of CompletionClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): CompletionClientWeb;
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with completion response
     */
    createCompletionMessage(inputs: Record<string, any>, user?: string, files?: string[]): Promise<any>;
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with workflow response
     */
    runWorkflow(inputs: Record<string, any>, files?: string[]): Promise<any>;
    /**
     * Create a streaming completion message
     * This uses the ssePost method from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    streamCompletionMessage(inputs: Record<string, any>, user?: string, files?: string[], callbacks?: {
        onData: IOnData;
        onCompleted: IOnCompleted;
        onFile: IOnFile;
        onThought: IOnThought;
        onMessageEnd: IOnMessageEnd;
        onMessageReplace: IOnMessageReplace;
        onError: IOnError;
        getAbortController?: (abortController: AbortController) => void;
        onWorkflowStarted: IOnWorkflowStarted;
        onNodeStarted: IOnNodeStarted;
        onNodeFinished: IOnNodeFinished;
        onWorkflowFinished: IOnWorkflowFinished;
    }): void;
}
/**
 * Chat client for web browsers
 */
export declare class ChatClientWeb extends DifyClientWeb {
    message_files: MessageFile[];
    /**
     * Get the singleton instance of ChatClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): ChatClientWeb;
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @returns Promise with chat message response
     */
    createChatMessage(inputs: Record<string, any>, query: string, response_mode?: string, conversation_id?: string, user?: string, files?: string[]): Promise<any>;
    /**
     * Create a streaming chat message
     * This uses ssePost from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    streamChatMessage({ inputs, query, conversation_id, user, files, }: StreamChatParams, callbacks?: StreamChatCallbacks): void;
    /**
     * Get messages from a conversation
     *
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Promise with conversation messages
     */
    getConversationMessages(conversation_id: string, first_id?: string, limit?: number): Promise<any>;
    /**
     * Get a list of conversations
     *
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns Promise with list of conversations
     */
    getConversations(first_id?: string, limit?: number, pinned?: boolean): Promise<any>;
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param auto_generate - Whether to auto-generate the name
     * @returns Promise with rename response
     */
    renameConversation(conversation_id: string, name: string, auto_generate?: boolean): Promise<any>;
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @returns Promise with delete response
     */
    deleteConversation(conversation_id: string): Promise<any>;
    /**
     * Stop a chat message task
     *
     * @param task_id - ID of the task to stop
     * @param user - User identifier
     * @returns Promise with stop response
     */
    stopChatMessage(task_id: string, user?: string): Promise<any>;
}


// From /base.d.ts
export declare const API_PREFIX = "/api";
export type WorkflowStartedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        workflow_id: string;
        sequence_number: number;
        created_at: number;
    };
};
export type WorkflowFinishedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        workflow_id: string;
        status: string;
        outputs: any;
        error: string;
        elapsed_time: number;
        total_tokens: number;
        total_steps: number;
        created_at: number;
        finished_at: number;
    };
};
export type NodeStartedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        node_id: string;
        node_type: string;
        index: number;
        predecessor_node_id?: string;
        inputs: any;
        created_at: number;
        extras?: any;
    };
};
export type NodeFinishedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        node_id: string;
        node_type: string;
        index: number;
        predecessor_node_id?: string;
        inputs: any;
        process_data: any;
        outputs: any;
        status: string;
        error: string;
        elapsed_time: number;
        execution_metadata: {
            total_tokens: number;
            total_price: number;
            currency: string;
        };
        created_at: number;
    };
};
export type IOnDataMoreInfo = {
    conversationId?: string;
    taskId?: string;
    messageId: string;
    errorMessage?: string;
    errorCode?: string;
};
export type IOnData = (message: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => void;
export type IOnThought = (thought: ThoughtItem) => void;
export type IOnFile = (file: VisionFile) => void;
export type IOnMessageEnd = (messageEnd: MessageEnd) => void;
export type IOnMessageReplace = (messageReplace: MessageReplace) => void;
export type IOnAnnotationReply = (messageReplace: AnnotationReply) => void;
export type IOnCompleted = (hasError?: boolean) => void;
export type IOnError = (msg: string, code?: string) => void;
export type IOnWorkflowStarted = (workflowStarted: WorkflowStartedResponse) => void;
export type IOnWorkflowFinished = (workflowFinished: WorkflowFinishedResponse) => void;
export type IOnNodeStarted = (nodeStarted: NodeStartedResponse) => void;
export type IOnNodeFinished = (nodeFinished: NodeFinishedResponse) => void;
export type ThoughtItem = {
    id: string;
    thought: string;
    message_id: string;
    conversation_id: string;
};
export type VisionFile = {
    id: string;
    url: string;
    type: string;
    belongs_to: string;
    mime_type: string;
    name: string;
};
export type MessageEnd = {
    id: string;
    task_id: string;
    message_id: string;
    conversation_id: string;
    metadata: {
        usage: {
            completion_tokens: number;
            prompt_tokens: number;
            total_tokens: number;
        };
    };
};
export type MessageReplace = {
    id: string;
    message_id: string;
    conversation_id: string;
    answer: string;
};
export type AnnotationReply = {
    id: string;
    annotation_id: string;
    message_id: string;
    conversation_id: string;
    answer: string;
    error?: string;
};
export type Feedbacktype = {
    rating: 'like' | 'dislike';
    user_id?: string;
};
export type IOtherOptions = {
    isPublicAPI?: boolean;
    bodyStringify?: boolean;
    needAllResponseContent?: boolean;
    deleteContentType?: boolean;
    onData?: IOnData;
    onThought?: IOnThought;
    onFile?: IOnFile;
    onMessageEnd?: IOnMessageEnd;
    onMessageReplace?: IOnMessageReplace;
    onError?: IOnError;
    onCompleted?: IOnCompleted;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted?: IOnWorkflowStarted;
    onWorkflowFinished?: IOnWorkflowFinished;
    onNodeStarted?: IOnNodeStarted;
    onNodeFinished?: IOnNodeFinished;
    apiBaseUrl?: string;
};
export declare const upload: (fetchOptions: any, options: IOtherOptions) => Promise<any>;
export declare const ssePost: (url: string, fetchOptions: any, options: IOtherOptions) => Promise<void>;
export declare const request: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const get: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const post: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const put: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const del: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const postFormData: (url: string, formData: FormData, otherOptions?: IOtherOptions) => Promise<any>;


// From /iframe-communication-client.d.ts
/**
 * Demo client for integrating with Dify Chatbox iframe
 * This file demonstrates how a parent application can communicate with an embedded Dify Chatbot
 */
import type { MessageTypeEnum } from './DifyChatbot';
/**
 * Example implementation of parent-side communication with Dify Chatbox
 */
export declare class DifyChatboxComunication {
    private iframe;
    private iframeReady;
    private sendToIframe;
    private currentConversationId;
    private messageHandlers;
    /**
     * Initialize the Dify Chatbox client
     * @param iframeElement - The iframe element containing Dify Chatbox
     */
    constructor(iframeElement: HTMLIFrameElement);
    /**
     * Set up event listener for messages from the iframe
     */
    private setupMessageListener;
    /**
     * Handle chatbox ready message
     */
    private handleChatboxReady;
    /**
     * Handle conversation completed message
     */
    private handleConversationCompleted;
    /**
     * Handle streaming response chunks
     */
    private handleResponseStream;
    /**
     * Handle user message sent notification
     */
    private handleUserMessageSent;
    /**
     * Handle conversation history
     */
    private handleConversationHistory;
    /**
     * Handle error message
     */
    private handleError;
    /**
     * Handle feedback update
     */
    private handleFeedbackUpdated;
    /**
     * Send a message to the chatbox
     * @param query - The message text
     * @param files - Optional files to attach
     * @param inputs - Optional input variables
     */
    sendMessage({ query, files, inputs, }: {
        query: string;
        files?: any[];
        inputs?: Record<string, any>;
    }): void;
    /**
     * Clear the conversation
     */
    clearConversation(): void;
    /**
     * Set input variables
     * @param inputs - Key-value pairs of input variables
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * Request conversation history
     */
    getConversationHistory(): void;
    /**
     * Get current status
     */
    getStatus(): void;
    /**
     * Set interface language
     * @param language - Language code
     */
    setLanguage(language: string): void;
    /**
     * Register a custom handler for a message type
     * @param type - Message type to handle
     * @param handler - Callback function
     */
    on(type: MessageTypeEnum, handler: (data: any) => void): void;
}
/**
 * Usage example
 */
export declare function createDifyChatboxDemo(): void;


// From /iframe-communication.d.ts
/**
 * Utility functions for iframe communication between Dify Chatbox and parent applications
 */
import type { MessageTypeEnum } from './DifyChatbot';
export type MessagePayload = {
    source: 'dify-chatbox' | 'parent-app';
    type: MessageTypeEnum;
    data: any;
};
/**
 * Send a message to the parent window
 * @param type - The type of message
 * @param data - The data to send
 * @param targetOrigin - The target origin (use '*' for development, but specific origins for production)
 */
export declare const sendMessageToParent: (type: MessageTypeEnum, data: any, targetOrigin?: string) => void;
/**
 * Initialize listener for messages from parent
 * @param handlers - Object containing handler functions for different message types
 */
export declare const initMessageListener: (handlers: {
    error?: (data: any) => void;
    ready?: (data: any) => void;
    "conversation-completed"?: (data: any) => void;
    "response-stream"?: (data: any) => void;
    "user-message-sent"?: (data: any) => void;
    "conversation-history"?: (data: any) => void;
    "feedback-updated"?: (data: any) => void;
    "send-message"?: (data: any) => void;
    "get-conversation-history"?: (data: any) => void;
    "clear-conversation"?: (data: any) => void;
    "set-inputs"?: (data: any) => void;
    "get-status"?: (data: any) => void;
    "set-language"?: (data: any) => void;
}) => () => void;
/**
 * Notify the parent window that the chatbox is ready
 * @param features - List of supported features
 */
export declare const notifyReady: (features?: string[]) => void;
/**
 * Send conversation results to parent window
 * @param question - The user's question
 * @param answer - The AI's answer
 * @param messageId - The message ID
 * @param conversationId - The conversation ID
 * @param agentThoughts - Optional agent thoughts (for agent mode)
 */
export declare const sendConversationResults: (question: string, answer: string, messageId: string, conversationId: string, agentThoughts?: any[]) => void;
/**
 * Send streaming response data to parent
 * @param partial - The partial response
 * @param messageId - The message ID
 * @param conversationId - The conversation ID
 * @param isFirstChunk - Whether this is the first chunk of the response
 */
export declare const sendResponseStream: (partial: string, messageId: string, conversationId: string, isFirstChunk?: boolean) => void;
/**
 * Send user message event to parent
 * @param message - The user's message
 * @param files - Optional files attached to the message
 * @param inputs - Optional input variables
 */
export declare const sendUserMessageEvent: (message: string, files?: any[], inputs?: Record<string, any>) => void;
/**
 * Send conversation history to parent
 * @param history - Array of conversation items
 */
export declare const sendConversationHistory: (history: any[]) => void;
/**
 * Send error message to parent
 * @param message - Error message
 * @param code - Optional error code
 */
export declare const sendError: (message: string, code?: string) => void;
/**
 * Send feedback update to parent
 * @param messageId - The message ID that received feedback
 * @param rating - The feedback rating
 */
export declare const sendFeedbackUpdate: (messageId: string, rating: string) => void;
/**
 * Check if we're running in an iframe
 */
export declare const isInIframe: () => boolean;
/**
 * A higher-level function that integrates all the message handling in one place
 * @param callbacks - Object with callbacks for different events
 * @returns cleanup function
 */
export declare const setupIframeCommunication: (callbacks: {
    onSendMessage?: (message: string, files?: any[], inputs?: Record<string, any>) => void;
    onClearConversation?: () => void;
    onSetInputs?: (inputs: Record<string, any>) => void;
    onSetLanguage?: (language: any) => void;
    onGetStatus?: () => any;
}) => () => void;
/**
 * Helper function to create a postMessage handler in the parent window
 * @param targetIframe - The iframe element or iframe window object
 * @param handlers - Object with handler functions for different message types
 * @returns Function to send messages to the iframe
 */
export declare const createParentHandler: (targetIframe: HTMLIFrameElement | Window, handlers: {
    error?: (data: any) => void;
    ready?: (data: any) => void;
    "conversation-completed"?: (data: any) => void;
    "response-stream"?: (data: any) => void;
    "user-message-sent"?: (data: any) => void;
    "conversation-history"?: (data: any) => void;
    "feedback-updated"?: (data: any) => void;
    "send-message"?: (data: any) => void;
    "get-conversation-history"?: (data: any) => void;
    "clear-conversation"?: (data: any) => void;
    "set-inputs"?: (data: any) => void;
    "get-status"?: (data: any) => void;
    "set-language"?: (data: any) => void;
}) => (type: MessageTypeEnum, data: any) => void;


}
