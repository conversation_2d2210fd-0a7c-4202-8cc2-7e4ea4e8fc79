/**
 * @description: 列表触底指令
 */
const vLoadmore = {
  mounted(el, binding) {
    let timer
    const scrollHandler = () => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        const scrollDistance = el.scrollHeight - el.scrollTop - el.clientHeight
        if (scrollDistance <= 10) {
          binding.value()
        }
      }, 200)
    }

    el.addEventListener('scroll', scrollHandler)

    el.__loadMoreCleanup__ = () => {
      clearTimeout(timer)
      el.removeEventListener('scroll', scrollHandler)
    }
  },
  unmounted(el) {
    el.__loadMoreCleanup__()
  }
}
export default vLoadmore
