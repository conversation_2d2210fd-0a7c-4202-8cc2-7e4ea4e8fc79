{"name": "bigscreen-common", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.4.1", "animate.css": "^4.1.1", "axios": "^1.4.0", "del": "^7.0.0", "echarts": "^5.4.2", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.3.7", "eslint-plugin-simple-import-sort": "^10.0.0", "iscroll": "^5.2.0", "moment": "^2.29.4", "mqtt": "^4.3.7", "nprogress": "^0.2.0", "pinia": "^2.1.4", "postcss": "^8.4.30", "postcss-html": "^1.5.0", "recursive-copy": "^2.0.14", "suit-cim": "^1.0.27", "suit-datav": "^2.0.3", "vite-plugin-externals": "^0.6.2", "vite-plugin-html-config": "^1.0.11", "vue": "^3.3.0", "vue-router": "^4.2.2"}, "devDependencies": {"@unocss/eslint-config": "^0.55.7", "@unocss/preset-rem-to-px": "^0.55.7", "@unocss/transformer-directives": "^0.55.7", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "babel-eslint": "^10.1.0", "eslint": "^8.42.0", "eslint-plugin-vue": "^9.14.1", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "2.8.8", "sass": "^1.63.3", "sass-loader": "^13.3.2", "scp2": "^0.5.0", "stylelint": "^15.10.3", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "unocss": "^0.55.7", "vite": "^4.3.9", "vite-plugin-resolve-externals": "^0.2.2"}, "lint-staged": {"src/**/*.{ts,js,jsx,tsx,vue}": ["prettier --write"]}}