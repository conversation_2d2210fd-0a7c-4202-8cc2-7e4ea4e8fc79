export const scatter1 = {
  baseOption: {
    title: [
      {
        text: '单位：mm',
        textStyle: { fontSize: 12, fontWeight: 'normal', color: '#607D8B', borderRadius: 16 },
        backgroundColor: 'rgba(144, 164, 174, 0.15)',
        borderRadius: 16,
        padding: [5, 14, 5, 14],
        left: 'center',
        bottom: 80
      },
      {
        text: '南（-）← →（+）北',
        textStyle: { fontSize: 12, fontWeight: 'normal', color: '#607D8B', borderRadius: 16 },
        backgroundColor: 'rgba(144, 164, 174, 0.15)',
        borderRadius: 16,
        padding: [5, 14, 5, 14],
        left: 'center',
        top: 40
      }
    ],
    animation: false,
    timeline: {
      axisType: 'category',
      data: [
        { range: ['2023-11-30 00:00:00', '2023-11-30 01:00:00'], value: '2023-11-30 00:00:00' },
        { range: ['2023-11-30 01:00:00', '2023-11-30 02:00:00'], value: '2023-11-30 01:00:00' },
        { range: ['2023-11-30 02:00:00', '2023-11-30 03:00:00'], value: '2023-11-30 02:00:00' },
        { range: ['2023-11-30 03:00:00', '2023-11-30 04:00:00'], value: '2023-11-30 03:00:00' },
        { range: ['2023-11-30 04:00:00', '2023-11-30 05:00:00'], value: '2023-11-30 04:00:00' },
        { range: ['2023-11-30 05:00:00', '2023-11-30 06:00:00'], value: '2023-11-30 05:00:00' },
        { range: ['2023-11-30 06:00:00', '2023-11-30 07:00:00'], value: '2023-11-30 06:00:00' },
        { range: ['2023-11-30 07:00:00', '2023-11-30 08:00:00'], value: '2023-11-30 07:00:00' },
        { range: ['2023-11-30 08:00:00', '2023-11-30 09:00:00'], value: '2023-11-30 08:00:00' },
        { range: ['2023-11-30 09:00:00', '2023-11-30 10:00:00'], value: '2023-11-30 09:00:00' },
        { range: ['2023-11-30 10:00:00', '2023-11-30 10:28:02'], value: '2023-11-30 10:00:00' }
      ],
      left: 20,
      right: 20,
      autoPlay: true,
      playInterval: 3000,
      tooltip: {
        formatter: {
          _custom: {
            type: 'function',
            display: '<span style="opacity:.5;">function</span> formatter(param)',
            tooltip: "<pre>function formatter(param) {\n                return param.data.range[0] + ' \\u81F3 ' + param.data.range[1];\n              }</pre>",
            _reviveId: 63344
          }
        }
      },
      show: true,
      bottom: 12
    },
    grid: { top: 50, bottom: 100, left: 50, right: 50 },
    color: ['rgba(0, 184, 179, 1)', 'rgba(198, 136, 90, 1)', 'rgba(201, 73, 80, 1)'],
    xAxis: { type: 'value', axisLine: { show: false }, splitLine: { show: false }, axisLabel: { show: false }, axisTick: { show: false }, min: -40, max: 720 },
    yAxis: {
      type: 'value',
      nameTextStyle: { color: '#607D8B', fontSize: 12, backgroundColor: 'rgba(144, 164, 174, 0.15)', borderRadius: 16, padding: [7, 14, 7, 14] },
      axisLine: { show: false },
      splitLine: { show: false },
      axisLabel: { show: false },
      axisTick: { show: false },
      max: 40,
      min: -10
    },
    series: [],
    legend: [
      {
        show: true,
        data: [{ name: '数据正常' }, { name: '一级报警' }, { name: '二级报警' }],
        left: 10,
        top: 15,
        icon: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
        itemGap: 14,
        itemHeight: 14,
        align: 'right',
        textStyle: { fontSize: 12 }
      },
      { data: ['监测点'], right: 10, top: 15, icon: 'circle', itemGap: 14, itemWidth: 8, textStyle: { fontSize: 12 } }
    ],
    tooltip: {
      trigger: 'item',
      backgroundColor: '#607D8B',
      textStyle: { color: 'white' },
      formatter: {
        _custom: {
          type: 'function',
          display: '<span style="opacity:.5;">function</span> formatter()',
          tooltip: "<pre>function formatter() {\n              return '';\n            }</pre>",
          _reviveId: 63345
        }
      }
    }
  },
  options: [
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [100.72, 30], symbol: 'none' },
                { coord: [97.44, 0], symbol: 'none' }
              ],
              [
                { coord: [210.52, 30], symbol: 'none' },
                { coord: [211.64, 0], symbol: 'none' }
              ],
              [
                { coord: [469.96, 30], symbol: 'none' },
                { coord: [466.52, 0], symbol: 'none' }
              ],
              [
                { coord: [572.96, 30], symbol: 'none' },
                { coord: [548.4, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63346
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63347
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63348
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63349
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63350
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63351
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63352
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63353
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63354
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63355
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63356
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63357
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63358
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63359
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63360
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63361
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63362
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[100.72, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.52, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[469.96, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[572.96, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[97.44, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[211.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[466.52, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[548.4, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.92, 30], symbol: 'none' },
                { coord: [96.64, 0], symbol: 'none' }
              ],
              [
                { coord: [209.52, 30], symbol: 'none' },
                { coord: [210.44, 0], symbol: 'none' }
              ],
              [
                { coord: [467.6, 30], symbol: 'none' },
                { coord: [463.72, 0], symbol: 'none' }
              ],
              [
                { coord: [573.28, 30], symbol: 'none' },
                { coord: [548.44, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63363
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63364
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63365
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63366
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63367
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63368
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63369
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63370
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63371
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63372
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63373
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63374
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63375
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63376
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63377
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63378
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63379
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.92, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[209.52, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.6, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.28, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[96.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.44, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[463.72, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[548.44, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [96.64, 0], symbol: 'none' }
              ],
              [
                { coord: [210.36, 30], symbol: 'none' },
                { coord: [211.28, 0], symbol: 'none' }
              ],
              [
                { coord: [468.96, 30], symbol: 'none' },
                { coord: [465.8, 0], symbol: 'none' }
              ],
              [
                { coord: [573.28, 30], symbol: 'none' },
                { coord: [548.92, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63380
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63381
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63382
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63383
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63384
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63385
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63386
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63387
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63388
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63389
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63390
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63391
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63392
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63393
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63394
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63395
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63396
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.36, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[468.96, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.28, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[96.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[211.28, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[465.8, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[548.92, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [96.28, 0], symbol: 'none' }
              ],
              [
                { coord: [210, 30], symbol: 'none' },
                { coord: [210.52, 0], symbol: 'none' }
              ],
              [
                { coord: [468.36, 30], symbol: 'none' },
                { coord: [465.4, 0], symbol: 'none' }
              ],
              [
                { coord: [573.64, 30], symbol: 'none' },
                { coord: [549.16, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63397
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63398
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63399
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63400
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63401
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63402
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63403
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63404
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63405
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63406
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63407
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63408
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63409
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63410
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63411
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63412
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63413
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[468.36, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.64, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[96.28, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.52, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[465.4, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[549.16, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [95.56, 0], symbol: 'none' }
              ],
              [
                { coord: [209.2, 30], symbol: 'none' },
                { coord: [209.64, 0], symbol: 'none' }
              ],
              [
                { coord: [467.72, 30], symbol: 'none' },
                { coord: [464.88, 0], symbol: 'none' }
              ],
              [
                { coord: [573.04, 30], symbol: 'none' },
                { coord: [548.76, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63414
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63415
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63416
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63417
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63418
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63419
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63420
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63421
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63422
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63423
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63424
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63425
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63426
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63427
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63428
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63429
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63430
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[209.2, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.72, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.04, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[95.56, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[209.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[464.88, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[548.76, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [95.84, 0], symbol: 'none' }
              ],
              [
                { coord: [210.04, 30], symbol: 'none' },
                { coord: [210.48, 0], symbol: 'none' }
              ],
              [
                { coord: [468.68, 30], symbol: 'none' },
                { coord: [465.8, 0], symbol: 'none' }
              ],
              [
                { coord: [573.6, 30], symbol: 'none' },
                { coord: [549.16, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63431
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63432
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63433
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63434
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63435
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63436
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63437
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63438
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63439
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63440
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63441
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63442
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63443
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63444
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63445
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63446
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63447
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.04, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[468.68, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.6, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[95.84, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[210.48, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[465.8, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[549.16, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [95, 0], symbol: 'none' }
              ],
              [
                { coord: [209.2, 30], symbol: 'none' },
                { coord: [209.6, 0], symbol: 'none' }
              ],
              [
                { coord: [468.48, 30], symbol: 'none' },
                { coord: [465.4, 0], symbol: 'none' }
              ],
              [
                { coord: [573.28, 30], symbol: 'none' },
                { coord: [549.08, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63448
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63449
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63450
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63451
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63452
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63453
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63454
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63455
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63456
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63457
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63458
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63459
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63460
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63461
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63462
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63463
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63464
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[209.2, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[468.48, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.28, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[95, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[209.6, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[465.4, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[549.08, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [95.84, 0], symbol: 'none' }
              ],
              [
                { coord: [206, 30], symbol: 'none' },
                { coord: [207.07999999999998, 0], symbol: 'none' }
              ],
              [
                { coord: [471.08, 30], symbol: 'none' },
                { coord: [467.8, 0], symbol: 'none' }
              ],
              [
                { coord: [573.28, 30], symbol: 'none' },
                { coord: [548.92, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63465
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63466
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63467
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63468
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63469
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63470
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63471
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63472
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63473
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63474
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63475
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63476
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63477
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63478
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63479
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63480
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63481
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[206, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[471.08, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[573.28, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[95.84, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[207.07999999999998, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.8, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[548.92, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [99.12, 30], symbol: 'none' },
                { coord: [94.64, 0], symbol: 'none' }
              ],
              [
                { coord: [207.84, 30], symbol: 'none' },
                { coord: [208.07999999999998, 0], symbol: 'none' }
              ],
              [
                { coord: [470.84000000000003, 30], symbol: 'none' },
                { coord: [467.84000000000003, 0], symbol: 'none' }
              ],
              [
                { coord: [574.08, 30], symbol: 'none' },
                { coord: [549.8, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63482
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63483
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63484
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63485
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63486
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63487
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63488
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63489
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63490
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63491
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63492
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63493
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63494
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63495
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63496
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63497
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63498
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[99.12, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[207.84, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[470.84000000000003, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[574.08, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[94.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[208.07999999999998, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.84000000000003, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[549.8, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [98.32, 30], symbol: 'none' },
                { coord: [93.44, 0], symbol: 'none' }
              ],
              [
                { coord: [206.88, 30], symbol: 'none' },
                { coord: [207.56, 0], symbol: 'none' }
              ],
              [
                { coord: [470.2, 30], symbol: 'none' },
                { coord: [467.12, 0], symbol: 'none' }
              ],
              [
                { coord: [575.6, 30], symbol: 'none' },
                { coord: [550.72, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63499
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63500
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63501
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63502
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63503
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63504
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63505
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63506
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63507
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63508
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63509
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63510
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63511
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63512
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63513
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63514
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63515
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[98.32, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[206.88, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[470.2, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[575.6, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[93.44, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[207.56, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.12, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[550.72, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    },
    {
      series: [
        {
          name: '监测点',
          data: [
            {
              device: {
                code: 'HK-S-L-WYJ02',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3413,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"下游","inside_group_id":1,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ02',
                psn: '20210421182012018',
                recordUpdateDate: 1619000412000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3413,
                  id: 265,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ02-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ06',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3417,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"下游","inside_group_id":1,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ06',
                psn: '20210421182013022',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3417,
                  id: 269,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ06-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ10',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3421,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"下游","inside_group_id":1,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ10',
                psn: '20210421182014026',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3421,
                  id: 273,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ10-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ14',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":30},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":0,"positive":"向北","negative":"向南"}',
                id: 3425,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"下游","inside_group_id":1,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":0},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端下游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ14',
                psn: '20210421182014030',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3425,
                  id: 277,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ14-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 30]
            },
            {
              device: {
                code: 'HK-S-L-WYJ04',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+000',
                ex1: '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":0,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3415,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":0,"location_group":"上游","inside_group_id":0,"location_name":"南侧边跨南侧桥墩","coordination":{"x":0,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+0',
                modelId: '*********',
                monitor: true,
                name: '南侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ04',
                psn: '20210421182013020',
                recordUpdateDate: 1619000413000,
                startMileage: 'K0+000',
                structureId: 4384,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3415,
                  id: 267,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ04-WYBHL',
                  recordCreateDate: 1619000413000,
                  recordUpdateDate: 1619000413000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [0, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ08',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+147',
                ex1: '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":170,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":1,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3419,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":1,"location_group":"上游","inside_group_id":0,"location_name":"南桥塔塔梁结合部","coordination":{"x":1,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+147',
                modelId: '*********',
                monitor: true,
                name: '南塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ08',
                psn: '20210421182013024',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+147',
                structureId: 4363,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3419,
                  id: 271,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ08-WYBHL',
                  recordCreateDate: 1619000414000,
                  recordUpdateDate: 1619000414000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [170, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ12',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+487',
                ex1: '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":510,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":2,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3423,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":2,"location_group":"上游","inside_group_id":0,"location_name":"北桥塔塔梁结合部","coordination":{"x":2,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+487',
                modelId: '*********',
                monitor: true,
                name: '北塔塔梁结合部上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ12',
                psn: '20210421182014028',
                recordUpdateDate: 1619000414000,
                startMileage: 'K0+487',
                structureId: 4315,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3423,
                  id: 275,
                  maxLimit: 42.5,
                  maxValue: 34,
                  measurePointId: 7,
                  minLimit: -42.5,
                  minValue: -34,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ12-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1620992335000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [510, 0]
            },
            {
              device: {
                code: 'HK-S-L-WYJ16',
                deviceParentTypeId: 1,
                deviceTypeId: 102,
                endMileage: 'K0+643',
                ex1: '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":680,"y":0},"add_cord":"x","parameter":4,"plot_code":"3003", "history_group_id":3,"history_inside_group_id":1,"positive":"向北","negative":"向南"}',
                id: 3427,
                installCompany: '上海城建信息科技有限公司',
                installPosition:
                  '{"location_mileage":3,"location_group":"上游","inside_group_id":0,"location_name":"北侧边跨北侧桥墩","coordination":{"x":3,"y":1},"add_cord":"x","plot_code":"3003"}',
                mileage: 'K0+643',
                modelId: '*********',
                monitor: true,
                name: '北侧边跨梁端上游',
                projectId: 1,
                protocolCode: 'HK-S-L-WYJ16',
                psn: '20210421182015032',
                recordUpdateDate: 1619000415000,
                startMileage: 'K0+643',
                structureId: 4294,
                unit: 'mm',
                channel: {
                  alert: true,
                  code: '3003',
                  deviceId: 3427,
                  id: 279,
                  maxLimit: 85,
                  maxValue: 68,
                  measurePointId: 7,
                  minLimit: -85,
                  minValue: -68,
                  name: '纵向位移变化量',
                  period: '60M',
                  protocolCode: 'HK-S-L-WYJ16-WYBHL',
                  recordCreateDate: 1619000415000,
                  recordUpdateDate: 1619000415000,
                  standard: 0,
                  state: 1,
                  type: 'measure',
                  unit: 'mm'
                },
                status: '数据正常'
              },
              value: [680, 0]
            }
          ],
          type: 'scatter',
          zlevel: 3,
          symbolSize: 8,
          itemStyle: { color: '#0053B1' },
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'rgba(144, 164, 174, 0.18)', borderWidth: 1, borderColor: 'rgba(144, 164, 174, 1)' } },
            data: [
              [
                { xAxis: 0, yAxis: 0 },
                { xAxis: 680, yAxis: 30 }
              ],
              [
                { xAxis: 158, yAxis: -6.4 },
                { xAxis: 182, yAxis: -3.6 }
              ],
              [
                { xAxis: 164, yAxis: -5.7 },
                { xAxis: 176, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: -6.4 },
                { xAxis: 522, yAxis: -3.6 }
              ],
              [
                { xAxis: 504, yAxis: -5.7 },
                { xAxis: 516, yAxis: -4.3, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 498, yAxis: 33.1 },
                { xAxis: 522, yAxis: 35.9 }
              ],
              [
                { xAxis: 504, yAxis: 33.8 },
                { xAxis: 516, yAxis: 35.2, itemStyle: { color: 'white' } }
              ],
              [
                { xAxis: 158, yAxis: 33.1 },
                { xAxis: 182, yAxis: 35.9 }
              ],
              [
                { xAxis: 164, yAxis: 33.8 },
                { xAxis: 176, yAxis: 35.2, itemStyle: { color: 'white' } }
              ]
            ]
          },
          markLine: {
            silent: true,
            lineStyle: { width: 2 },
            data: [
              [
                { coord: [96.72, 30], symbol: 'none' },
                { coord: [92.64, 0], symbol: 'none' }
              ],
              [
                { coord: [206.84, 30], symbol: 'none' },
                { coord: [207.44, 0], symbol: 'none' }
              ],
              [
                { coord: [470.72, 30], symbol: 'none' },
                { coord: [467.84000000000003, 0], symbol: 'none' }
              ],
              [
                { coord: [575.6800000000001, 30], symbol: 'none' },
                { coord: [551.24, 0], symbol: 'none' }
              ]
            ]
          },
          markPoint: {
            data: [
              {
                coord: [-10, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63516
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63517
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63518
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, -2.5],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63519
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [-10, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63520
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [120, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63521
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [560, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63522
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              },
              {
                coord: [690, 37],
                symbolSize: 0,
                label: {
                  position: 'bottom',
                  formatter: {
                    _custom: {
                      type: 'function',
                      display: '<span style="opacity:.5;">function</span> formatter()',
                      tooltip: '<pre>function formatter() {\n              return text[index];\n            }</pre>',
                      _reviveId: 63523
                    }
                  },
                  fontSize: 12,
                  color: 'rgba(96, 125, 139, 1)'
                }
              }
            ]
          },
          tooltip: {
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter(param)',
                tooltip:
                  "<pre>function formatter(param) {\n            var device = param.data.device;\n            var deviceIndex = me.deviceList.findIndex(function (item) {\n              return item.id === device.id;\n            });\n            if (!device) {\n              return '';\n            }\n            var config = JSON.parse(device.ex1);\n            var value = data[deviceIndex];\n            var direction = '';\n            if (value > 0) {\n              direction = config.positive;\n            }\n            if (value < 0) {\n              direction = config.negative;\n            }\n            var status = device.status;\n            return '' + config.location_name + config.location_group + '<br>' + ('\\u7EB5\\u5411\\u4F4D\\u79FB\\uFF1A' + direction + ' ' + Math.abs(value) + device.unit + '<br>') + ('\\u6570\\u636E\\u72B6\\u6001\\uFF1A' + status);\n          }</pre>",
                _reviveId: 63524
              }
            }
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 30], symbol: 'none' }, [16, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63525
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 30], symbol: 'none' }, [186, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63526
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 30], symbol: 'none' }, [494, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63527
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 30], symbol: 'none' }, [664, 30]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63528
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, 10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [0, 0], symbol: 'none' }, [16, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63529
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [170, 0], symbol: 'none' }, [186, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 0,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'right',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63530
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [-10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [510, 0], symbol: 'none' }, [494, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63531
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [{ value: [680, 0], symbol: 'none' }, [664, 0]],
          zlevel: 2,
          type: 'line',
          symbol: 'path://M0 4.2H9.47091V0L16 6L9.47091 12V7.8H0V4.2Z"',
          symbolSize: 16,
          symbolRotate: 180,
          symbolKeepAspect: true,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 },
          label: {
            show: true,
            position: 'left',
            formatter: {
              _custom: {
                type: 'function',
                display: '<span style="opacity:.5;">function</span> formatter()',
                tooltip: '<pre>function formatter() {\n              return value;\n            }</pre>',
                _reviveId: 63532
              }
            },
            color: 'rgba(0, 81, 177, 1)',
            fontSize: 12,
            offset: [10, -10]
          }
        },
        {
          name: '数据正常',
          data: [[96.72, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[206.84, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[470.72, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[575.6800000000001, 30]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[92.64, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[207.44, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 0,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[467.84000000000003, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        },
        {
          name: '数据正常',
          data: [[551.24, 0]],
          zlevel: 1,
          type: 'line',
          symbol: 'emptyCircle',
          symbolSize: 4,
          symbolRotate: 180,
          symbolOffset: [0, 0],
          lineStyle: { show: false, width: 0 }
        }
      ]
    }
  ]
}

export const scatter2 = {
  baseOption: {
    timeline: {
      axisType: 'category',
      data: [
        { range: ['2023-11-30 00:00:00', '2023-11-30 01:00:00'], value: '2023-11-30 00:00:00' },
        { range: ['2023-11-30 01:00:00', '2023-11-30 02:00:00'], value: '2023-11-30 01:00:00' },
        { range: ['2023-11-30 02:00:00', '2023-11-30 03:00:00'], value: '2023-11-30 02:00:00' },
        { range: ['2023-11-30 03:00:00', '2023-11-30 04:00:00'], value: '2023-11-30 03:00:00' },
        { range: ['2023-11-30 04:00:00', '2023-11-30 05:00:00'], value: '2023-11-30 04:00:00' },
        { range: ['2023-11-30 05:00:00', '2023-11-30 06:00:00'], value: '2023-11-30 05:00:00' },
        { range: ['2023-11-30 06:00:00', '2023-11-30 07:00:00'], value: '2023-11-30 06:00:00' },
        { range: ['2023-11-30 07:00:00', '2023-11-30 08:00:00'], value: '2023-11-30 07:00:00' },
        { range: ['2023-11-30 08:00:00', '2023-11-30 09:00:00'], value: '2023-11-30 08:00:00' },
        { range: ['2023-11-30 09:00:00', '2023-11-30 10:00:00'], value: '2023-11-30 09:00:00' },
        { range: ['2023-11-30 10:00:00', '2023-11-30 10:28:02'], value: '2023-11-30 10:00:00' }
      ],
      left: 20,
      right: 20,
      autoPlay: true,
      playInterval: 1500,
      tooltip: {
        formatter: {
          _custom: {
            type: 'function',
            display: '<span style="opacity:.5;">function</span> formatter(param)',
            tooltip: "<pre>function formatter(param) {\n                return param.data.range[0] + ' \\u81F3 ' + param.data.range[1];\n              }</pre>",
            _reviveId: 131972
          }
        }
      }
    },
    title: [
      {
        text: '南 ←   → 北',
        textStyle: { fontSize: 12, fontWeight: 'normal', color: '#607D8B', borderRadius: 16 },
        backgroundColor: 'rgba(144, 164, 174, 0.15)',
        borderRadius: 16,
        padding: [5, 14, 5, 14],
        left: 'center',
        top: 65
      },
      { text: '南塔塔顶变形', subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      {
        text: '西\n\n ↑ \n\n ↓ \n\n东',
        textStyle: { fontSize: 12, textAlign: 'center', color: '#607D8B' },
        backgroundColor: 'rgba(144, 164, 174, 0.15)',
        borderRadius: 16,
        padding: [14, 5, 14, 5],
        right: 12,
        top: '40%'
      }
    ],
    color: ['#607D8B', '#F0C04C', '#F15081'],
    grid: { top: 100, bottom: 100 },
    xAxis: {
      name: '纵桥向倾角(°)',
      max: 0.1,
      min: -0.1,
      type: 'value',
      axisLiable: { show: false },
      splitLine: { show: false },
      axisTick: { show: false, alignWithLabel: true },
      nameLocation: 'center',
      nameGap: 30
    },
    yAxis: { type: 'value', max: 0.1, min: -0.1, name: '横桥向倾角(°)', nameLocation: 'center', nameGap: 30 },
    series: [
      { type: 'scatter', name: '正常范围' },
      { type: 'scatter', name: '一级报警' },
      { type: 'scatter', name: '二级报警' }
    ],
    legend: [{ width: '100%', show: true, right: 'center', top: 30, icon: 'circle', itemGap: 14, gridIndex: 0 }],
    tooltip: {
      backgroundColor: '#607D8B',
      textStyle: { color: 'white' },
      formatter: {
        _custom: {
          type: 'function',
          display: '<span style="opacity:.5;">function</span> formatter(params)',
          tooltip:
            "<pre>function formatter(params) {\n              if (params.value.length > 1) {\n                return '\\u7EB5\\u5411\\u503E\\u659C ' + params.value[0] + '<br/>\\u6A2A\\u5411\\u503E\\u659C ' + params.value[1];\n              } else {\n                return params.seriesName + ' :<br/>' + params.name + ' ' + params.value;\n              }\n            }</pre>",
          _reviveId: 131973
        }
      }
    }
  },
  options: [
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0171, 0.0189],
            [0.0186, 0.0198],
            [0.0157, 0.0187],
            [0.0124, 0.0191],
            [0.0067, 0.0196],
            [0.0078, 0.0193],
            [0.0073, 0.0196],
            [0.0104, 0.0189],
            [0.0124, 0.0184],
            [0.0126, 0.0196],
            [0.0115, 0.0193],
            [0.0135, 0.0198]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0015, 0.0191],
            [0.0053, 0.0173],
            [0.0089, 0.0189],
            [0.0162, 0.0171],
            [0.0149, 0.0191],
            [0.0184, 0.0196],
            [0.0086, 0.0191],
            [0.0246, 0.0198],
            [0.0151, 0.0193],
            [0.0109, 0.0191],
            [0.0171, 0.0193],
            [0.0095, 0.0184]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0146, 0.0187],
            [0.0191, 0.018],
            [0.0166, 0.0189],
            [0.008, 0.0191],
            [-0.0091, 0.0178],
            [0.0093, 0.0196],
            [0.0129, 0.0189],
            [0.0089, 0.0198],
            [0.0109, 0.0189],
            [0.0197, 0.02],
            [0.0047, 0.0218],
            [0.0126, 0.0191]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0111, 0.0196],
            [0.0106, 0.0196],
            [0.01, 0.0191],
            [0.0084, 0.0202],
            [0.0106, 0.0193],
            [0.0075, 0.0202],
            [0.0095, 0.0193],
            [0.0084, 0.0196],
            [0.0104, 0.02],
            [0.0069, 0.02],
            [0.0104, 0.02],
            [0.0084, 0.0189]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.008, 0.0189],
            [0.0071, 0.0191],
            [0.0104, 0.0198],
            [0.0106, 0.018],
            [0.0122, 0.0196],
            [0.0095, 0.0184],
            [0.0093, 0.0191],
            [0.0111, 0.0189],
            [0.0071, 0.0191],
            [0.0093, 0.0198],
            [0.0089, 0.0196],
            [0.0086, 0.0202]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0133, 0.0202],
            [0.0093, 0.0196],
            [0.0098, 0.0189],
            [0.0104, 0.0193],
            [0.0029, 0.0178],
            [0.0091, 0.0187],
            [-0.0004, 0.0173],
            [0.0089, 0.0184],
            [0.0104, 0.0187],
            [0.0109, 0.0182],
            [0.0111, 0.0184],
            [0.0102, 0.018]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0111, 0.0184],
            [0.0104, 0.0182],
            [0.0122, 0.018],
            [0.0086, 0.0189],
            [-0.0038, 0.0167],
            [0.0164, 0.0189],
            [0.0129, 0.018],
            [0.01, 0.0189],
            [0.0082, 0.018],
            [0.01, 0.0184],
            [0.0268, 0.0171],
            [0.0171, 0.0182]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0142, 0.0171],
            [0.014, 0.0176],
            [0.0206, 0.0193],
            [-0.0013, 0.0171],
            [0.0102, 0.0184],
            [0.0118, 0.0184],
            [0.0129, 0.0187],
            [0.0095, 0.0176],
            [0.0166, 0.018],
            [0, 0.0196],
            [0.0098, 0.0178],
            [0.0111, 0.02]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0129, 0.0171],
            [0.0133, 0.0189],
            [0.0175, 0.018],
            [0.0149, 0.022],
            [0.02, 0.0202],
            [0.0126, 0.02],
            [0.0095, 0.0193],
            [0.0204, 0.0193],
            [0.0109, 0.0189],
            [0.0126, 0.0209],
            [0.0069, 0.0187],
            [0.0124, 0.02]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0151, 0.0207],
            [0.0146, 0.0204],
            [0.012, 0.0207],
            [0.0053, 0.0196],
            [0.0073, 0.0198],
            [0.0222, 0.0204],
            [0.0109, 0.0193],
            [0.006, 0.0193],
            [0.0082, 0.0193],
            [0.0078, 0.0191],
            [0.0189, 0.0189],
            [0.0109, 0.0193]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    },
    {
      series: [
        {
          type: 'scatter',
          data: [
            [0.0151, 0.0236],
            [0.0153, 0.0193],
            [0.0093, 0.0198],
            [0.0013, 0.0191],
            [0.0149, 0.0189],
            [0.0135, 0.0198]
          ],
          symbolSize: 10
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.08, yAxis: -0.08 },
                { xAxis: 0.08, yAxis: 0.08 }
              ]
            ]
          }
        },
        {
          type: 'scatter',
          data: [],
          symbolSize: 10,
          markArea: {
            silent: true,
            itemStyle: { normal: { color: 'transparent', borderWidth: 1, borderType: 'dashed' } },
            data: [
              [
                { xAxis: -0.1, yAxis: -0.1 },
                { xAxis: 0.1, yAxis: 0.1 }
              ]
            ]
          }
        }
      ]
    }
  ]
}
