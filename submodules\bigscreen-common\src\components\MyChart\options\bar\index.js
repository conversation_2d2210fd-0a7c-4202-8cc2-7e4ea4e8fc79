import * as echarts from 'echarts'

export const bar1 = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    top: '30px',
    bottom: '10px',
    left: '10px',
    right: '10px',
    containLabel: true
  },
  legend: {
    top: '0',
    right: 'center',
    itemWidth: 10,
    itemHeight: 10,
    textStyle: {
      color: '#fff',
      fontSize: 14
    },
    data: ['使用', '剩余']
  },
  xAxis: [
    {
      data: ['双永路11', '环港路二段', '广牧路', '牧星路东段', '航志路', '牧晨路', '航创路', '双拥路', '航兴路'],
      axisLine: {
        lineStyle: {
          color: '#FFFFFF'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#93B8E2',
        fontSize: 14,
        interval: 0
      }
    }
  ],
  yAxis: {
    min: 0,
    max: 100,
    type: 'value',
    position: 'left',
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#2E4867'
      }
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#FFFFFF'
      }
    },
    axisLabel: {
      show: true,
      formatter: '{value}%',
      textStyle: {
        color: '#93B8E2'
      }
    }
  },
  series: [
    {
      name: '使用',
      type: 'bar',
      barWidth: '30%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#00C6FF'
            },
            {
              offset: 1,
              color: 'rgba(0,87,112,0)'
            }
          ],
          global: false
        }
      },
      data: [50, 0, 40, 33.333333333333336, 40, 33.333333333333336, 30, 20, 25],
      stack: 'parking',
      stackStrategy: 'all'
    },
    {
      name: '剩余',
      type: 'bar',
      barWidth: '30%',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#30F121'
            },
            {
              offset: 1,
              color: 'rgba(13,112,0,0)'
            }
          ],
          global: false
        }
      },
      data: [50, 100, 60, 66.66, 60, 66.66, 70, 80, 75],
      stack: 'parking',
      stackStrategy: 'all'
    }
  ]
}

export const bar2 = {
  grid: {
    top: '50px',
    bottom: '10px',
    left: '10px',
    right: '30px',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none'
    },
    formatter: function (val) {
      let ret = ''
      val.forEach((item) => {
        if (item.seriesType !== 'pictorialBar') {
          ret += `${item.marker}${item.seriesName}:${item.value}${item.seriesName === '饱和度' ? '%' : ''}<br />`
        }
      })
      return ret
    }
  },
  legend: {
    top: '20',
    right: '0',
    itemWidth: 10,
    itemHeight: 10,
    textStyle: {
      color: '#fff',
      fontSize: 14
    },
    data: ['入园', '出园', '饱和度']
  },
  xAxis: [
    {
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#93B8E2'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#93B8E2',
        fontSize: 14,
        interval: 0
      }
    }
  ],
  yAxis: [
    {
      name: '月度流量监测',
      nameTextStyle: {
        fontSize: 14,
        color: '#ffffff'
      },
      min: 0,
      // max: 600,
      type: 'value',
      position: 'left',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#2E4867'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true,
        formatter: '{value}',
        textStyle: {
          color: '#93B8E2'
        }
      }
    },
    {
      // name: '百分比',
      type: 'value',
      position: 'right',
      min: 0,
      max: 100,
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#FFFFFF'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#93B8E2'
        },
        formatter: function (value) {
          return value + '%'
        }
      }
    }
  ],
  series: [
    {
      name: '入园',
      type: 'bar', // 设置类型为象形柱状图
      barWidth: '10', // 柱图宽度
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 1,
            color: '#064883'
          },
          {
            offset: 0,
            color: '#00B3FF'
          }
        ])
      },
      z: 1,
      barGap: 0, // 柱间距离
      data: [400, 450, 150, 320, 400, 510, 530, 350, 480, 350, 510, 500]
    },
    {
      name: '出园',
      type: 'bar', // 设置类型为象形柱状图
      barWidth: '10', // 柱图宽度
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#FFAF0C'
          },
          {
            offset: 1,
            color: '#623F00'
          }
        ])
      },
      z: 1,
      barGap: '30%', // 柱间距离
      data: [320, 500, 450, 320, 470, 310, 430, 350, 480, 370, 510, 530]
    },
    {
      name: '饱和度',
      type: 'line',
      symbolSize: 0,
      yAxisIndex: 1, // 右边的y轴数据
      lineStyle: {
        width: 2,
        color: '#1DFF1C'
      },
      data: [400, 300, 500, 420, 370, 410, 530, 350, 480, 570, 410, 500],
      z: 5,
      zlevel: 5
    },
    {
      // 分隔
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: 'rgba(7,32,59,.5)'
        }
      },
      symbolRepeat: 'fixed',
      symbolMargin: 6,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [11, 2],
      symbolOffset: [-7, 0],
      symbolPosition: 'start',
      // symbolBoundingData: this.total,
      data: [400, 450, 150, 320, 400, 510, 530, 350, 480, 350, 510, 500],
      width: 25,
      z: 0,
      zlevel: 1
    },
    {
      // 分隔
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: 'rgba(7,32,59,.5)'
        }
      },
      symbolRepeat: 'fixed',
      symbolMargin: 6,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [11, 2],
      symbolPosition: 'start',
      symbolOffset: [6, 0],
      // symbolBoundingData: this.total,
      data: [320, 500, 450, 320, 470, 310, 430, 350, 480, 370, 510, 530],
      width: 25,
      z: 0,
      zlevel: 1
    }
  ]
}

export const bar3 = {
  grid: {
    top: '30px',
    bottom: '10px',
    left: '10px',
    right: '30px',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (val) {
      let ret = ''
      val.forEach((item) => {
        if (item.seriesType !== 'pictorialBar') {
          ret += `${item.marker}${item.seriesName}:${item.value}%<br />`
        }
      })
      return ret
    }
  },
  legend: {
    top: '0',
    right: '20',
    itemWidth: 10,
    itemHeight: 10,
    textStyle: {
      color: '#fff',
      fontSize: 14
    },
    data: ['优', '良', '中', '次', '差']
  },
  color: ['#00ff38', '#0079f7', '#5872fa', '#ff7e25', '#ff001c'],
  xAxis: [
    {
      data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
      axisLine: {
        lineStyle: {
          color: '#93B8E2'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#93B8E2',
        fontSize: 14,
        interval: 0
      }
    }
  ],
  yAxis: {
    name: '平整度指数各登记占比',
    nameTextStyle: {
      align: 'left'
    },
    min: 0,
    max: 100,
    type: 'value',
    position: 'left',
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#2E4867'
      }
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#FFFFFF'
      }
    },
    axisLabel: {
      show: true,
      formatter: '{value}',
      textStyle: {
        color: '#93B8E2'
      }
    }
  },
  series: [
    {
      name: '优',
      type: 'bar', // 设置类型为象形柱状图
      barWidth: '10', // 柱图宽度
      barMaxWidth: '20%', // 最大宽度
      data: [10, 0, 40, 33.333333333333336, 40, 33.333333333333336, 30, 20, 25],
      stack: 'evaluate',
      stackStrategy: 'all'
    },
    {
      name: '良',
      type: 'bar',
      data: [20, 100, 60, 66.66, 60, 66.66, 70, 80, 75],
      stack: 'evaluate',
      stackStrategy: 'all'
    },
    {
      name: '中',
      type: 'bar',
      data: [30, 100, 60, 66.66, 60, 66.66, 70, 80, 75],
      stack: 'evaluate',
      stackStrategy: 'all'
    },
    {
      name: '次',
      type: 'bar',
      data: [30, 100, 60, 66.66, 60, 66.66, 70, 80, 75],
      stack: 'evaluate',
      stackStrategy: 'all'
    },
    {
      name: '差',
      type: 'bar',
      data: [10, 100, 60, 66.66, 60, 66.66, 70, 80, 75],
      stack: 'evaluate',
      stackStrategy: 'all'
    },
    {
      // 分隔
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: '#0F375F'
        }
      },
      symbolRepeat: 'fixed',
      symbolMargin: 6,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [10, 2],
      symbolPosition: 'start',
      // symbolBoundingData: this.total,
      data: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
      width: 25,
      z: 0,
      zlevel: 1
    }
  ]
}

export const bar4 = {
  grid: {
    top: '50px',
    bottom: '5px',
    left: '14px',
    right: '30px',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none'
    },
    formatter: function (val) {
      let ret = ''
      val.forEach((item) => {
        if (item.seriesType !== 'pictorialBar') {
          ret += `${item.marker}${item.seriesName}:${item.value}${item.seriesName === '饱和度' ? '%' : ''}<br />`
        }
      })
      return ret
    }
  },
  legend: {
    //   icon: 'roundRect',
    top: '20',

    left: 'center',
    itemWidth: 12,

    itemHeight: 12,
    textStyle: {
      color: '#fff',
      fontSize: 14
    },
    //   data: ['车速1', '车速2', '车流量1', '车流量2']
    // data: [{ name: '车速1' }, { name: '车速2' }, { name: '车流量1', icon: 'roundRect' }, { name: '车流量2', icon: 'roundRect' }]
    data: [{ name: '车速1' }, { name: '车速2' }, { name: '车流量1', itemStyle: { opacity: 0 }}, { name: '车流量2', itemStyle: { opacity: 0 }}]
  },
  xAxis: [
    {
      data: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'],
      axisLine: {
        lineStyle: {
          color: '#93B8E2'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#93B8E2',
        fontSize: 14,
        interval: 0
      }
    }
  ],
  yAxis: [
    {
      name: '车速(km/h)',
      nameTextStyle: {
        fontSize: 14,
        color: '#ffffff'
        //   align: 'left'
      },

      min: 0,
      // max: 600,
      type: 'value',
      position: 'left',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#2E4867'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true,
        formatter: '{value}',
        //   formatter: function (value) {
        //     if (value == 0) {
        //       return '\n' + value + '\n月'
        //     }
        //     return value
        //   },
        textStyle: {
          color: '#93B8E2'
        }
      }
    },
    {
      name: '车流量(辆)',
      type: 'value',
      position: 'right',
      min: 0,
      // max: 100,
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#FFFFFF'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: '#93B8E2'
        }
        //   formatter: function (value) {
        //     return value + '%'
        //   }
      }
    }
  ],
  series: [
    {
      name: '车速1',
      type: 'bar', // 设置类型为象形柱状图
      barWidth: '10', // 柱图宽度
      // legend:{},
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 1,
            color: '#064883'
          },
          {
            offset: 0,
            color: '#00B3FF'
          }
        ])
      },
      z: 1,
      barGap: 0, // 柱间距离
      data: [400, 450, 150, 320, 400, 510, 530, 350, 480, 350, 510, 500]
    },
    {
      name: '车速2',
      type: 'bar', // 设置类型为象形柱状图
      barWidth: '10', // 柱图宽度

      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#FFAF0C'
          },
          {
            offset: 1,
            color: '#623F00'
          }
        ])
      },
      z: 1,
      barGap: '30%', // 柱间距离
      data: [320, 500, 450, 320, 470, 310, 430, 350, 480, 370, 510, 530]
    },
    {
      name: '车流量1',
      type: 'line',
      symbolSize: 0,
      yAxisIndex: 1, // 右边的y轴数据
      legend: {
        icon: 'roundRect',
        itemHeight: 1,
        itemStyle: {
          color: '#1DFF1C'
        }
      },
      lineStyle: {
        width: 2,
        color: '#1DFF1C'
      },
      data: [2200, 2300, 2500, 1420, 2370, 2410, 2530, 2350, 2480, 1570, 1410, 1500],
      z: 5,
      zlevel: 5
    },
    {
      name: '车流量2',
      type: 'line',
      // symbol: 'none',
      symbolSize: 0,
      yAxisIndex: 1, // 右边的y轴数据
      lineStyle: {
        width: 2,
        color: '#FF0080'
      },
      data: [2400, 2100, 2500, 1520, 2770, 2410, 2130, 2550, 2480, 2770, 2410, 2500],
      z: 5,
      zlevel: 5
    },
    {
      // 分隔
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: 'rgba(7,32,59,.5)'
        }
      },
      symbolRepeat: 'fixed',
      symbolMargin: 6,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [11, 2],
      symbolOffset: [-7, 0],
      symbolPosition: 'start',
      // symbolBoundingData: this.total,
      data: [400, 450, 150, 320, 400, 510, 530, 350, 480, 350, 510, 500],
      width: 25,
      z: 0,
      zlevel: 1
    },
    {
      // 分隔
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: 'rgba(7,32,59,.5)'
        }
      },
      symbolRepeat: 'fixed',
      symbolMargin: 6,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [11, 2],
      symbolPosition: 'start',
      symbolOffset: [6, 0],
      // symbolBoundingData: this.total,
      data: [320, 500, 450, 320, 470, 310, 430, 350, 480, 370, 510, 530],
      width: 25,
      z: 0,
      zlevel: 1
    }
  ]
}
export const bar5 = {
    title: {
      text: '●  平整度指数个等级占比',
      left: '0px',
      textStyle: {
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    legend: {
      itemWidth: 10,
      itemHeight: 10,
      left: '190px',
      data: ['优', '良', '中', '次', '差'],
      textStyle: {
        color: '#fff'
      }
    },
    xAxis: {
      axisTick: {
        show: false
      },
      data: ['某道路', '某道路', '某道路', '某道路', '某道路', '某道路', '某道路'],
      splitLine: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: '#93B8E2'
        }
      }
    },
    yAxis: {
      max: 100,
      axisLabel: {
        textStyle: {
          color: '#93B8E2',
          fontSize: 14,
          fontFamily: 'Alibaba-PuHuiTi-R'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          width: 0.5
        }
      }
    },
    series: [
      {
        data: [25, 25, 25, 25, 25, 25, 25],
        name: '优',
        symbol: 'rect',
        type: 'pictorialBar',
        symbolSize: [12, 6],
        symbolRepeat: true,
        itemStyle: {
          color: '#0ED74D',
          borderWidth: 2
          /*  borderColor: '#93B8E2' */
        },
        z: 5
      },
      {
        name: '良',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolSize: [12, 6],
        symbolRepeat: true,
        data: [45, 45, 45, 45, 45, 45, 45],
        itemStyle: {
          color: '#00B4FF',
          borderWidth: 2
          /* borderColor: '#93B8E2' */
        },
        z: 4
      },
      {
        name: '中',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolSize: [12, 6],
        symbolRepeat: true,
        data: [65, 65, 65, 65, 65, 65, 65],
        itemStyle: {
          color: '#A745FF',
          borderWidth: 2
          /*  borderColor: '#93B8E2' */
        },
        z: 3
      },
      {
        name: '次',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolSize: [12, 6],
        symbolRepeat: true,
        data: [75, 75, 75, 75, 75, 75, 75],
        itemStyle: {
          color: '#FF9700',
          borderWidth: 2
          /* borderColor: '#93B8E2' */
        },
        z: 2
      },
      {
        name: '差',
        type: 'pictorialBar',
        symbol: 'rect',
        symbolSize: [12, 6],
        symbolRepeat: true,
        data: [100, 100, 100, 100, 100, 100, 100],
        itemStyle: {
          color: '#FF0000',
          borderWidth: 2
          /* borderColor: '#93B8E2' */
        },
        z: 1
      }
    ]
}
export const bar6 = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    grid: {
      left: '5%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    },
    legend: {
      top: 15,
      right: 240,
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 20,
      icon: 'rect',
      data: ['实际使用年限', '预测使用年限'],
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      }
    },
    xAxis: [
      {
        type: 'category',
        data: ['某某路段', '某某路段', '某某路段', '某某路段', '某某路段', '某某路段', '某某路段', '某某路段', '某某路段'],
        axisTick: {
          show: false
        },
        axisLabel: {
          textStyle: {
            color: '#93B8E2'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '年限:',
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          textStyle: {
            color: '#93B8E2',
            fontSize: 12,
            fontFamily: 'Alibaba-PuHuiTi-R'
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#93B8E2'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            width: 0.5
          }
        }
      },
      {
        type: 'value',
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          textStyle: {
            color: '#93B8E2',
            fontSize: 12,
            fontFamily: 'Alibaba-PuHuiTi-R'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            width: 0.5
          }
        }
      }
    ],
    series: [
      {
        name: '实际使用年限',
        type: 'bar',
        data: [180, 110, 130, 80, 250, 170, 100, 150, 240],
        color: '#05B5FF',
        barWidth: '25%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: '#248ff7'
            },
            {
              offset: 1,
              color: 'rgba(22,75,247,0.1)'
            }
          ])
        }
      },
      {
        name: '预测使用年限',
        type: 'line',
        yAxisIndex: 1,
        data: [180, 110, 130, 220, 150, 140, 150, 100, 130],
        color: '#ECB40F',
        lineStyle: {
          width: 1
        }
      }
    ]
}
/* const titleTab = ref([
  {
    name: '处置耗时Top10',
    active: true,
    dataPeople: [4, 4, 1, 3, 4, 5, 3, 3, 2, 1],
    dataDevice: [1, 1, 1, 3, 5, 5, 3, 5, 2, 1]
  },
  {
    name: '设施险情处置效率',
    active: false,
    data: [40, 90, 15, 32, 40, 51, 53, 35, 48, 35, 51, 50]
  },
  {
    name: '险情总数占比Top5',
    active: false,
    data: [50, 45, 15, 32, 5]
  }
]) */
export const bar7 = {
    grid: {
      top: '50px',
      bottom: '10px',
      left: '10px',
      right: '30px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none'
      },
      formatter: function(val) {
        let ret = ''
        val.forEach((item) => {
          if (item.seriesType !== 'pictorialBar') {
            ret += `${item.marker}${item.seriesName}:${item.value}${item.seriesName === '饱和度' ? '%' : ''}<br />`
          }
        })
        return ret
      }
    },
    legend: {
      top: '15',
      //   right: '0',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      data: ['人为上报', '设备报警']
    },
    xAxis: [
      {
        data: ['事件1', '事件2', '事件3', '事件4', '事件5', '事件6', '事件7', '事件8', '事件9', '事件10'],
        axisLine: {
          lineStyle: {
            color: '#93B8E2'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#93B8E2',
          fontSize: 14,
          interval: 0
        }
      }
    ],
    yAxis: [
      {
        // name: '月度流量监测',
        nameTextStyle: {
          fontSize: 14,
          color: '#ffffff'
        },
        min: 0,
        // max: 600,
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#2E4867'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          formatter: '{value}',
          textStyle: {
            color: '#93B8E2'
          }
        }
      }
    ],
    series: [
      {
        name: '人为上报',
        type: 'bar', // 设置类型为象形柱状图
        barWidth: '10', // 柱图宽度
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 1,
              color: '#054700'
            },
            {
              offset: 0,
              color: '#2FEB21 '
            }
          ])
        },
        z: 1,
        barGap: 0, // 柱间距离
        data: [4, 4, 1, 3, 4, 5, 3, 3, 2, 1]
      },
      {
        name: '设备报警',
        type: 'bar', // 设置类型为象形柱状图
        barWidth: '10', // 柱图宽度
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: '#12EAFF'
            },
            {
              offset: 1,
              color: '#004147 '
            }
          ])
        },
        z: 1,
        barGap: '30%', // 柱间距离
        data: [1, 1, 1, 3, 5, 5, 3, 5, 2, 1]
      },
      //   {
      //     name: '饱和度',
      //     type: 'line',
      //     symbolSize: 0,
      //     yAxisIndex: 1, // 右边的y轴数据
      //     lineStyle: {
      //       width: 2,
      //       color: '#1DFF1C'
      //     },
      //     data: [400, 300, 500, 420, 370, 410, 530, 350, 480, 570, 410, 500],
      //     z: 5,
      //     zlevel: 5
      //   },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: 'rgba(7,32,59,.5)'
          }
        },
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [11, 2],
        symbolOffset: [-7, 0],
        symbolPosition: 'start',
        // symbolBoundingData: this.total,
        data: [4, 4, 1, 3, 4, 5, 3, 3, 2, 1],
        width: 25,
        z: 0,
        zlevel: 1
      },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: 'rgba(7,32,59,.5)'
          }
        },
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [11, 2],
        symbolPosition: 'start',
        symbolOffset: [6, 0],
        // symbolBoundingData: this.total,
        data: [1, 1, 1, 3, 5, 5, 3, 5, 2, 1],
        width: 25,
        z: 0,
        zlevel: 1
      }
    ]
}
export const bar8 = {
    grid: {
      top: '50px',
      bottom: '10px',
      left: '10px',
      right: '30px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none'
      },
      formatter: function (val) {
        let ret = ''
        val.forEach((item) => {
          if (item.seriesType !== 'pictorialBar') {
            ret += `${item.marker}${item.seriesName}:${item.value}%<br />`
          }
        })
        return ret
      }
    },
    legend: {
      top: '15',
      //   right: '0',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      data: ['设施占比']
    },
    xAxis: [
      {
        data: ['设施1', '设施2', '设施3', '设施4', '设施5', '设施6', '设施7', '设施8', '设施9', '设施10'],
        axisLine: {
          lineStyle: {
            color: '#93B8E2'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#93B8E2',
          fontSize: 14,
          interval: 0
        }
      }
    ],
    yAxis: [
      {
        // name: '百分比',
        type: 'value',
        // position: 'right',
        min: 0,
        max: 100,
        splitLine: {
          //   show: false
          show: true,
          lineStyle: {
            type: 'solid',
            color: '#2E4867'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#FFFFFF'
            // color: 'red'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#93B8E2'
          },
          formatter: function (value) {
            return value + '%'
          }
        }
      }
    ],
    series: [
      {
        name: '设施占比',
        type: 'bar', // 设置类型为象形柱状图
        barWidth: '10', // 柱图宽度
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 1,
              color: '#064883'
            },
            {
              offset: 0,
              color: '#00B3FF'
            }
          ])
        },
        z: 1,
        barGap: 0, // 柱间距离
        data: [40, 90, 15, 32, 40, 51, 53, 35, 48, 35, 51, 50]
      },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: 'rgba(7,32,59,.5)'
            // color: 'orange'
          }
        },
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [11, 2],
        symbolPosition: 'start',
        symbolOffset: [1, 0],
        // symbolBoundingData: this.total,
        data: [40, 90, 15, 32, 40, 51, 53, 35, 48, 35, 51, 50],
        width: 25,
        z: 0,
        zlevel: 1
      }
    ]
}
export const bar9 = {
    grid: {
      top: '50px',
      bottom: '10px',
      left: '10px',
      right: '30px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none'
      },
      formatter: function (val) {
        let ret = ''
        val.forEach((item) => {
          if (item.seriesType !== 'pictorialBar') {
            ret += `${item.marker}${item.seriesName}:${item.value}%<br />`
          }
        })
        return ret
      }
    },
    legend: {
      top: '15',
      //   right: '0',
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      data: ['险情占比']
    },
    xAxis: [
      {
        data: ['险情1', '险情2', '险情3', '险情4', '险情5'],
        axisLine: {
          lineStyle: {
            color: '#93B8E2'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#93B8E2',
          fontSize: 14,
          interval: 0
        }
      }
    ],
    yAxis: [
      {
        // name: '百分比',
        type: 'value',
        // position: 'right',
        min: 0,
        max: 100,
        splitLine: {
          show: true,
          lineStyle: {
            type: 'solid',
            color: '#2E4867'
          }
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#FFFFFF'
          }
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#93B8E2'
          },
          formatter: function (value) {
            return value + '%'
          }
        }
      }
    ],
    series: [
      {
        name: '险情占比',
        type: 'bar', // 设置类型为象形柱状图
        barWidth: '16', // 柱图宽度
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 1,
              color: '#064883'
            },
            {
              offset: 0,
              color: '#00B3FF'
            }
          ])
        },
        z: 1,
        barGap: 0, // 柱间距离
        data: [50, 45, 15, 32, 5]
      },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: 'rgba(7,32,59,.5)'
            // color: 'red'
          }
        },
        symbolRepeat: 'fixed',
        symbolMargin: 6,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [16, 2],
        symbolOffset: [0, 0],
        symbolPosition: 'start',
        // symbolBoundingData: this.total,
        data: [50, 45, 15, 32, 5],
        width: 25,
        z: 0,
        zlevel: 1
      }
    ]
}
