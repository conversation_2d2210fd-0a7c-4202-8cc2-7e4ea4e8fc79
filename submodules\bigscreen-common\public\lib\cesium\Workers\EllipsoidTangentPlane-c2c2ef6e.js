/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./AxisAlignedBoundingBox-6489d16d","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./IntersectionTests-25cff68e","./Plane-a268aa11","./Transforms-42ed7720"],(function(t,n,e,i,o,s,r,a){"use strict";const l=new i.Cartesian4;function c(t,n){t=(n=o.defaultValue(n,i.Ellipsoid.WGS84)).scaleToGeodeticSurface(t);const s=a.Transforms.eastNorthUpToFixedFrame(t,n);this._ellipsoid=n,this._origin=t,this._xAxis=e.Cartesian3.fromCartesian4(i.Matrix4.getColumn(s,0,l)),this._yAxis=e.Cartesian3.fromCartesian4(i.Matrix4.getColumn(s,1,l));const c=e.Cartesian3.fromCartesian4(i.Matrix4.getColumn(s,2,l));this._plane=r.Plane.fromPointNormal(t,c)}Object.defineProperties(c.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});const d=new n.AxisAlignedBoundingBox;c.fromPoints=function(t,e){return new c(n.AxisAlignedBoundingBox.fromPoints(t,d).center,e)};const f=new s.Ray,p=new e.Cartesian3;c.prototype.projectPointOntoPlane=function(t,n){const i=f;i.origin=t,e.Cartesian3.normalize(t,i.direction);let r=s.IntersectionTests.rayPlane(i,this._plane,p);if(o.defined(r)||(e.Cartesian3.negate(i.direction,i.direction),r=s.IntersectionTests.rayPlane(i,this._plane,p)),o.defined(r)){const t=e.Cartesian3.subtract(r,this._origin,r),i=e.Cartesian3.dot(this._xAxis,t),s=e.Cartesian3.dot(this._yAxis,t);return o.defined(n)?(n.x=i,n.y=s,n):new e.Cartesian2(i,s)}},c.prototype.projectPointsOntoPlane=function(t,n){o.defined(n)||(n=[]);let e=0;const i=t.length;for(let s=0;s<i;s++){const i=this.projectPointOntoPlane(t[s],n[e]);o.defined(i)&&(n[e]=i,e++)}return n.length=e,n},c.prototype.projectPointToNearestOnPlane=function(t,n){o.defined(n)||(n=new e.Cartesian2);const i=f;i.origin=t,e.Cartesian3.clone(this._plane.normal,i.direction);let r=s.IntersectionTests.rayPlane(i,this._plane,p);o.defined(r)||(e.Cartesian3.negate(i.direction,i.direction),r=s.IntersectionTests.rayPlane(i,this._plane,p));const a=e.Cartesian3.subtract(r,this._origin,r),l=e.Cartesian3.dot(this._xAxis,a),c=e.Cartesian3.dot(this._yAxis,a);return n.x=l,n.y=c,n},c.prototype.projectPointsToNearestOnPlane=function(t,n){o.defined(n)||(n=[]);const e=t.length;n.length=e;for(let i=0;i<e;i++)n[i]=this.projectPointToNearestOnPlane(t[i],n[i]);return n};const u=new e.Cartesian3;c.prototype.projectPointOntoEllipsoid=function(t,n){o.defined(n)||(n=new e.Cartesian3);const i=this._ellipsoid,s=this._origin,r=this._xAxis,a=this._yAxis,l=u;return e.Cartesian3.multiplyByScalar(r,t.x,l),n=e.Cartesian3.add(s,l,n),e.Cartesian3.multiplyByScalar(a,t.y,l),e.Cartesian3.add(n,l,n),i.scaleToGeocentricSurface(n,n),n},c.prototype.projectPointsOntoEllipsoid=function(t,n){const e=t.length;o.defined(n)?n.length=e:n=new Array(e);for(let i=0;i<e;++i)n[i]=this.projectPointOntoEllipsoid(t[i],n[i]);return n},t.EllipsoidTangentPlane=c}));
