/**
 * 用于发布
 */
// import scpClient from 'scp2' // 自动部署
// import chalk from 'chalk' // 颜色的插件

// const hostInfo = {
//   host: '*************', // 服务器地址ip
//   port: '22', // 服务器端口
//   username: 'root',
//   password: 'I_can_fly',
//   path: '/home/<USER>/static-view/tocc-screen-view'
// }

// scpClient.scp(
//   './dist',
//   {
//     ...hostInfo,
//     host: hostInfo.host,
//     port: hostInfo.port,
//     username: hostInfo.username,
//     password: hostInfo.password,
//     path: hostInfo.path
//   },
//   function(err) {
//     // spinner.stop();
//     if (err) {
//       console.log(chalk.red('发布失败.\n'))
//       throw err
//     } else {
//       console.log(chalk.green('Success! 成功发布到服务器!'))
//     }
//   }
// )
