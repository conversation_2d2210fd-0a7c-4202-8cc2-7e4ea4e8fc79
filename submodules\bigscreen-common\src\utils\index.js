/**
 * @description: 获取某个文件夹下的所有图片
 * @param {Objcect} iconModules 异步迭代器对象
 * @return {Objcect} 图片路径对象
 */
export function getIconData(iconModules) {
  const iconData = {}
  const keys = Object.keys(iconModules)
  for (const key of keys) {
    const iconName = key
      .split('/')
      .pop()
      .replace(/\.\w+$/, '')
    iconData[iconName] = iconModules[key].default
  }
  return iconData
}

/**
 * @description: 导入某个文件夹下的所有组件
 * @param {Objcect} modulesFiles 异步迭代器对象
 * @param {Objcect} app 当前组件的vue实例
 */
export function setComponents(modulesFiles, app) {
  const components = {}
  const keys = Object.keys(modulesFiles)
  for (const key of keys) {
    const splitArr = key.split('/')
    // 文件名
    const fileName = splitArr[splitArr.length - 1]
    // 文件夹名
    const folderName = splitArr[splitArr.length - 2]
    // 如果文件名为 index.vue，则使用文件夹名作为组件名称，否则使用文件名作为组件名称
    const componentName = fileName === 'index.vue' ? folderName : fileName.replace(/\.\w+$/, '')
    components[componentName] = modulesFiles[key].default
  }
  app.components = { ...app.components, ...components }
}

/**
 * @description: 获取assets静态资源
 * @param {string} assets文件夹下的路径
 */
export const getAssetsFile = (url) => {
  return new URL(`../assets/${url}`, import.meta.url).href
}

export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'shallowClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = source[keys].constructor === Array ? [] : {}
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * @description: 复制文本到剪切板
 * @param {String} text 需要复制的文本
 */
export function copyText(text) {
  // 复制结果
  let copyResult = true
  // 创建一个input元素
  const inputDom = document.createElement('textarea')
  // 设置为只读 防止移动端手机上弹出软键盘
  inputDom.setAttribute('readonly', 'readonly')
  // 给input元素赋值
  inputDom.value = text
  // 将创建的input添加到body
  document.body.appendChild(inputDom)
  // 选中input元素的内容
  inputDom.select()
  // 执行浏览器复制命令
  // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签中的内容）
  // Input要在正常的编辑状态下原生复制方法才会生效
  const result = document.execCommand('copy')
  if (!result) copyResult = false
  // 复制操作后再将构造的标签 移除
  document.body.removeChild(inputDom)
  // 返回复制操作的最终结果
  return copyResult
}

/**
 * @description: 把数组进行分组，且每一组的第一项为前一项的最后一个
 * @param {Array} arr   [1,2,3,4,5,6,7,8,9]
 * @param {Number} size  3
 * @return {Array}   [[1, 2, 3], [3, 4, 5], [5, 6, 7], [7, 8, 9]]
 */
export function groupArray(arr, size) {
  var result = []
  for (var i = 0; i < arr.length; i += size) {
    var group = arr.slice(i, i + size)
    if (result.length > 0) {
      group.unshift(result[result.length - 1][size - 1])
    }
    result.push(group)
  }
  return result
}

/**
 * @description: 获取16进制随机颜色
 * @return: #08FEE4
 */
export function getRandomColor() {
  const letters = '0123456789ABCDEF'
  let color = '#'
  do {
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)]
    }
  } while (color === '#FFFFFF' || color === '#000000')
  return color
}

/**
 * @description: 获取16进制随机颜色数组 不重复
 * @param {Number} num 返回数组长度 默认值为1
 * @return: [ '#0BED63' ]
 */
export function getRandomColorList(num = 1) {
  const colors = []
  for (let i = 0; i < num; i++) {
    let color = ''
    do {
      color = getRandomColor()
    } while (colors.indexOf(color) != -1)
    colors.push(color)
  }
  return colors
}
