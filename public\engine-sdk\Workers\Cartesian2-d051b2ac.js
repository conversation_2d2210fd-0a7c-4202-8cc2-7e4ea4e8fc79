define(["exports","./when-1807bd8d","./Check-1951f41f"],(function(t,e,n){"use strict";var r=function(t){void 0==t&&(t=(new Date).getTime()),this.N=624,this.M=397,this.MATRIX_A=2567483615,this.UPPER_MASK=2147483648,this.LOWER_MASK=2147483647,this.mt=new Array(this.N),this.mti=this.N+1,this.init_genrand(t)};r.prototype.init_genrand=function(t){for(this.mt[0]=t>>>0,this.mti=1;this.mti<this.N;this.mti++){t=this.mt[this.mti-1]^this.mt[this.mti-1]>>>30;this.mt[this.mti]=(1812433253*((4294901760&t)>>>16)<<16)+1812433253*(65535&t)+this.mti,this.mt[this.mti]>>>=0}},r.prototype.genrand_int32=function(){var t,e=new Array(0,this.MATRIX_A);if(this.mti>=this.N){var n;for(this.mti==this.N+1&&this.init_genrand(5489),n=0;n<this.N-this.M;n++)t=this.mt[n]&this.UPPER_MASK|this.mt[n+1]&this.LOWER_MASK,this.mt[n]=this.mt[n+this.M]^t>>>1^e[1&t];for(;n<this.N-1;n++)t=this.mt[n]&this.UPPER_MASK|this.mt[n+1]&this.LOWER_MASK,this.mt[n]=this.mt[n+(this.M-this.N)]^t>>>1^e[1&t];t=this.mt[this.N-1]&this.UPPER_MASK|this.mt[0]&this.LOWER_MASK,this.mt[this.N-1]=this.mt[this.M-1]^t>>>1^e[1&t],this.mti=0}return t=this.mt[this.mti++],t^=t>>>11,t^=t<<7&2636928640,t^=t<<15&4022730752,t^=t>>>18,t>>>0},r.prototype.random=function(){return this.genrand_int32()*(1/4294967296)};var a={EPSILON1:.1,EPSILON2:.01,EPSILON3:.001,EPSILON4:1e-4,EPSILON5:1e-5,EPSILON6:1e-6,EPSILON7:1e-7,EPSILON8:1e-8,EPSILON9:1e-9,EPSILON10:1e-10,EPSILON11:1e-11,EPSILON12:1e-12,EPSILON13:1e-13,EPSILON14:1e-14,EPSILON15:1e-15,EPSILON16:1e-16,EPSILON17:1e-17,EPSILON18:1e-18,EPSILON19:1e-19,EPSILON20:1e-20,EPSILON21:1e-21,GRAVITATIONALPARAMETER:3986004418e5,SOLAR_RADIUS:20865e5,LUNAR_RADIUS:5212200,SIXTY_FOUR_KILOBYTES:65536,FOUR_GIGABYTES:4294967296};a.sign=e.defaultValue(Math.sign,(function(t){return t=+t,0===t||t!==t?t:t>0?1:-1})),a.signNotZero=function(t){return t<0?-1:1},a.toSNorm=function(t,n){return n=e.defaultValue(n,255),Math.round((.5*a.clamp(t,-1,1)+.5)*n)},a.fromSNorm=function(t,n){return n=e.defaultValue(n,255),a.clamp(t,0,n)/n*2-1},a.normalize=function(t,e,n){return n=Math.max(n-e,0),0===n?0:a.clamp((t-e)/n,0,1)},a.sinh=e.defaultValue(Math.sinh,(function(t){return(Math.exp(t)-Math.exp(-t))/2})),a.cosh=e.defaultValue(Math.cosh,(function(t){return(Math.exp(t)+Math.exp(-t))/2})),a.lerp=function(t,e,n){return(1-n)*t+n*e},a.PI=Math.PI,a.ONE_OVER_PI=1/Math.PI,a.PI_OVER_TWO=Math.PI/2,a.PI_OVER_THREE=Math.PI/3,a.PI_OVER_FOUR=Math.PI/4,a.PI_OVER_SIX=Math.PI/6,a.THREE_PI_OVER_TWO=3*Math.PI/2,a.TWO_PI=2*Math.PI,a.ONE_OVER_TWO_PI=1/(2*Math.PI),a.RADIANS_PER_DEGREE=Math.PI/180,a.DEGREES_PER_RADIAN=180/Math.PI,a.RADIANS_PER_ARCSECOND=a.RADIANS_PER_DEGREE/3600,a.toRadians=function(t){return t*a.RADIANS_PER_DEGREE},a.toDegrees=function(t){return t*a.DEGREES_PER_RADIAN},a.convertLongitudeRange=function(t){var e=a.TWO_PI,n=t-Math.floor(t/e)*e;return n<-Math.PI?n+e:n>=Math.PI?n-e:n},a.clampToLatitudeRange=function(t){return a.clamp(t,-1*a.PI_OVER_TWO,a.PI_OVER_TWO)},a.negativePiToPi=function(t){return a.zeroToTwoPi(t+a.PI)-a.PI},a.zeroToTwoPi=function(t){var e=a.mod(t,a.TWO_PI);return Math.abs(e)<a.EPSILON14&&Math.abs(t)>a.EPSILON14?a.TWO_PI:e},a.mod=function(t,e){return(t%e+e)%e},a.equalsEpsilon=function(t,n,r,a){r=e.defaultValue(r,0),a=e.defaultValue(a,r);var i=Math.abs(t-n);return i<=a||i<=r*Math.max(Math.abs(t),Math.abs(n))},a.lessThan=function(t,e,n){return t-e<-n},a.lessThanOrEquals=function(t,e,n){return t-e<n},a.greaterThan=function(t,e,n){return t-e>n},a.greaterThanOrEquals=function(t,e,n){return t-e>-n};var i=[1];a.factorial=function(t){if("number"!==typeof t||t<0)throw new n.DeveloperError("A number greater than or equal to 0 is required.");var e=i.length;if(t>=e)for(var r=i[e-1],a=e;a<=t;a++){var o=r*a;i.push(o),r=o}return i[t]},a.incrementWrap=function(t,r,a){if(a=e.defaultValue(a,0),!e.defined(t))throw new n.DeveloperError("n is required.");if(r<=a)throw new n.DeveloperError("maximumValue must be greater than minimumValue.");return++t,t>r&&(t=a),t},a.isPowerOfTwo=function(t){if("number"!==typeof t||t<0)throw new n.DeveloperError("A number greater than or equal to 0 is required.");return 0!==t&&0===(t&t-1)},a.nextPowerOfTwo=function(t){if("number"!==typeof t||t<0)throw new n.DeveloperError("A number greater than or equal to 0 is required.");return--t,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,++t,t},a.clamp=function(t,e,n){return t<e?e:t>n?n:t};var o=new r;function u(t,n,r){this.x=e.defaultValue(t,0),this.y=e.defaultValue(n,0),this.z=e.defaultValue(r,0)}a.setRandomNumberSeed=function(t){o=new r(t)},a.nextRandomNumber=function(){return o.random()},a.randomBetween=function(t,e){return a.nextRandomNumber()*(e-t)+t},a.acosClamped=function(t){return Math.acos(a.clamp(t,-1,1))},a.asinClamped=function(t){return Math.asin(a.clamp(t,-1,1))},a.chordLength=function(t,e){return 2*e*Math.sin(.5*t)},a.logBase=function(t,e){return Math.log(t)/Math.log(e)},a.cbrt=e.defaultValue(Math.cbrt,(function(t){var e=Math.pow(Math.abs(t),1/3);return t<0?-e:e})),a.log2=e.defaultValue(Math.log2,(function(t){return Math.log(t)*Math.LOG2E})),a.fog=function(t,e){var n=t*e;return 1-Math.exp(-n*n)},a.fastApproximateAtan=function(t){return t*(-.1784*Math.abs(t)-.0663*t*t+1.0301)},a.fastApproximateAtan2=function(t,e){var r,i,o=Math.abs(t);r=Math.abs(e),i=Math.max(o,r),r=Math.min(o,r);var u=r/i;if(isNaN(u))throw new n.DeveloperError("either x or y must be nonzero");return o=a.fastApproximateAtan(u),o=Math.abs(e)>Math.abs(t)?a.PI_OVER_TWO-o:o,o=t<0?a.PI-o:o,o=e<0?-o:o,o},u.fromSpherical=function(t,n){e.defined(n)||(n=new u);var r=t.clock,a=t.cone,i=e.defaultValue(t.magnitude,1),o=i*Math.sin(a);return n.x=o*Math.cos(r),n.y=o*Math.sin(r),n.z=i*Math.cos(a),n},u.fromElements=function(t,n,r,a){return e.defined(a)?(a.x=t,a.y=n,a.z=r,a):new u(t,n,r)},u.clone=function(t,n){if(e.defined(t))return e.defined(n)?(n.x=t.x,n.y=t.y,n.z=t.z,n):new u(t.x,t.y,t.z)},u.fromCartesian4=u.clone,u.packedLength=3,u.pack=function(t,n,r){return r=e.defaultValue(r,0),n[r++]=t.x,n[r++]=t.y,n[r]=t.z,n},u.unpack=function(t,n,r){return n=e.defaultValue(n,0),e.defined(r)||(r=new u),r.x=t[n++],r.y=t[n++],r.z=t[n],r},u.packArray=function(t,r){var a=t.length,i=3*a;if(e.defined(r)){if(!Array.isArray(r)&&r.length!==i)throw new n.DeveloperError("If result is a typed array, it must have exactly array.length * 3 elements");r.length!==i&&(r.length=i)}else r=new Array(i);for(var o=0;o<a;++o)u.pack(t[o],r,3*o);return r},u.unpackArray=function(t,r){if(n.Check.defined("array",t),n.Check.typeOf.number.greaterThanOrEquals("array.length",t.length,3),t.length%3!==0)throw new n.DeveloperError("array length must be a multiple of 3.");var a=t.length;e.defined(r)?r.length=a/3:r=new Array(a/3);for(var i=0;i<a;i+=3){var o=i/3;r[o]=u.unpack(t,i,r[o])}return r},u.fromArray=u.unpack,u.maximumComponent=function(t){return Math.max(t.x,t.y,t.z)},u.minimumComponent=function(t){return Math.min(t.x,t.y,t.z)},u.minimumByComponent=function(t,e,n){return n.x=Math.min(t.x,e.x),n.y=Math.min(t.y,e.y),n.z=Math.min(t.z,e.z),n},u.maximumByComponent=function(t,e,n){return n.x=Math.max(t.x,e.x),n.y=Math.max(t.y,e.y),n.z=Math.max(t.z,e.z),n},u.magnitudeSquared=function(t){return t.x*t.x+t.y*t.y+t.z*t.z},u.magnitude=function(t){return Math.sqrt(u.magnitudeSquared(t))};var f=new u;u.distance=function(t,e){return u.subtract(t,e,f),u.magnitude(f)};var l=new u,s=new u;u.getProjectPtOnLine=function(t,e,n,r){u.subtract(n,e,l),u.normalize(l,l),u.subtract(t,e,s);var a=u.dot(s,l);return u.multiplyByScalar(l,a,s),u.add(e,s,r),r},u.distanceSquared=function(t,e){return u.subtract(t,e,f),u.magnitudeSquared(f)},u.normalize=function(t,e){var r=u.magnitude(t);if(e.x=t.x/r,e.y=t.y/r,e.z=t.z/r,isNaN(e.x)||isNaN(e.y)||isNaN(e.z))throw new n.DeveloperError("normalized result is not a number");return e},u.dot=function(t,e){return t.x*e.x+t.y*e.y+t.z*e.z},u.multiplyComponents=function(t,e,n){return n.x=t.x*e.x,n.y=t.y*e.y,n.z=t.z*e.z,n},u.divideComponents=function(t,e,n){return n.x=t.x/e.x,n.y=t.y/e.y,n.z=t.z/e.z,n},u.add=function(t,e,n){return n.x=t.x+e.x,n.y=t.y+e.y,n.z=t.z+e.z,n},u.subtract=function(t,e,n){return n.x=t.x-e.x,n.y=t.y-e.y,n.z=t.z-e.z,n},u.ceil=function(t,e){return e.x=Math.ceil(t.x),e.y=Math.ceil(t.y),e.z=Math.ceil(t.z),e},u.floor=function(t,e){return e.x=Math.floor(t.x),e.y=Math.floor(t.y),e.z=Math.floor(t.z),e},u.multiplyByScalar=function(t,e,n){return n.x=t.x*e,n.y=t.y*e,n.z=t.z*e,n},u.divideByScalar=function(t,e,n){return n.x=t.x/e,n.y=t.y/e,n.z=t.z/e,n},u.negate=function(t,e){return e.x=-t.x,e.y=-t.y,e.z=-t.z,e},u.abs=function(t,e){return e.x=Math.abs(t.x),e.y=Math.abs(t.y),e.z=Math.abs(t.z),e};var h=new u;u.lerp=function(t,e,n,r){return u.multiplyByScalar(e,n,h),r=u.multiplyByScalar(t,1-n,r),u.add(h,r,r)};var c=new u,m=new u;u.angleBetween=function(t,e){u.normalize(t,c),u.normalize(e,m);var n=u.dot(c,m),r=u.magnitude(u.cross(c,m,c));return Math.atan2(r,n)};var d=new u;u.mostOrthogonalAxis=function(t,e){var n=u.normalize(t,d);return u.abs(n,n),e=n.x<=n.y?n.x<=n.z?u.clone(u.UNIT_X,e):u.clone(u.UNIT_Z,e):n.y<=n.z?u.clone(u.UNIT_Y,e):u.clone(u.UNIT_Z,e),e},u.projectVector=function(t,e,n){var r=u.dot(t,e)/u.dot(e,e);return u.multiplyByScalar(e,r,n)},u.equals=function(t,n){return t===n||e.defined(t)&&e.defined(n)&&t.x===n.x&&t.y===n.y&&t.z===n.z},u.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]&&t.z===e[n+2]},u.equalsEpsilon=function(t,n,r,i){return t===n||e.defined(t)&&e.defined(n)&&a.equalsEpsilon(t.x,n.x,r,i)&&a.equalsEpsilon(t.y,n.y,r,i)&&a.equalsEpsilon(t.z,n.z,r,i)},u.cross=function(t,e,n){var r=t.x,a=t.y,i=t.z,o=e.x,u=e.y,f=e.z,l=a*f-i*u,s=i*o-r*f,h=r*u-a*o;return n.x=l,n.y=s,n.z=h,n},u.midpoint=function(t,e,n){return n.x=.5*(t.x+e.x),n.y=.5*(t.y+e.y),n.z=.5*(t.z+e.z),n},u.getCenter=function(t,n){return e.defined(n)||(n=new u),t.forEach((t=>{u.add(n,t,n)})),u.multiplyByScalar(n,1/t.length,n),n},u.fromDegrees=function(t,e,n,r,i){return t=a.toRadians(t),e=a.toRadians(e),u.fromRadians(t,e,n,r,i)};var y=new u,x=new u,p=new u(40680631590769,40680631590769,40408299984661.445);function E(t,n){this.x=e.defaultValue(t,0),this.y=e.defaultValue(n,0)}u.fromRadians=function(t,n,r,a,i){r=e.defaultValue(r,0);var o=e.defined(a)?a.radiiSquared:p,f=Math.cos(n);y.x=f*Math.cos(t),y.y=f*Math.sin(t),y.z=Math.sin(n),y=u.normalize(y,y),u.multiplyComponents(o,y,x);var l=Math.sqrt(u.dot(y,x));return x=u.divideByScalar(x,l,x),y=u.multiplyByScalar(y,r,y),e.defined(i)||(i=new u),u.add(x,y,i)},u.fromDegreesArray=function(t,r,a){if(n.Check.defined("coordinates",t),t.length<2||t.length%2!==0)throw new n.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");var i=t.length;e.defined(a)?a.length=i/2:a=new Array(i/2);for(var o=0;o<i;o+=2){var f=t[o],l=t[o+1],s=o/2;a[s]=u.fromDegrees(f,l,0,r,a[s])}return a},u.fromRadiansArray=function(t,r,a){if(n.Check.defined("coordinates",t),t.length<2||t.length%2!==0)throw new n.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");var i=t.length;e.defined(a)?a.length=i/2:a=new Array(i/2);for(var o=0;o<i;o+=2){var f=t[o],l=t[o+1],s=o/2;a[s]=u.fromRadians(f,l,0,r,a[s])}return a},u.fromDegreesArrayHeights=function(t,r,a){if(n.Check.defined("coordinates",t),t.length<3||t.length%3!==0)throw new n.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");var i=t.length;e.defined(a)?a.length=i/3:a=new Array(i/3);for(var o=0;o<i;o+=3){var f=t[o],l=t[o+1],s=t[o+2],h=o/3;a[h]=u.fromDegrees(f,l,s,r,a[h])}return a},u.fromRadiansArrayHeights=function(t,r,a){if(n.Check.defined("coordinates",t),t.length<3||t.length%3!==0)throw new n.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");var i=t.length;e.defined(a)?a.length=i/3:a=new Array(i/3);for(var o=0;o<i;o+=3){var f=t[o],l=t[o+1],s=t[o+2],h=o/3;a[h]=u.fromRadians(f,l,s,r,a[h])}return a},u.ZERO=Object.freeze(new u(0,0,0)),u.UNIT_X=Object.freeze(new u(1,0,0)),u.UNIT_Y=Object.freeze(new u(0,1,0)),u.UNIT_Z=Object.freeze(new u(0,0,1)),u.IDENTITY=Object.freeze(new u(1,1,1)),u.prototype.clone=function(t){return u.clone(this,t)},u.prototype.equals=function(t){return u.equals(this,t)},u.prototype.equalsEpsilon=function(t,e,n){return u.equalsEpsilon(this,t,e,n)},u.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+")"},E.fromElements=function(t,n,r){return e.defined(r)?(r.x=t,r.y=n,r):new E(t,n)},E.round=function(t,e){return e.x=Math.round(t.x),e.y=Math.round(t.y),e},E.floor=function(t,e){return e.x=Math.floor(t.x),e.y=Math.floor(t.y),e},E.ceil=function(t,e){return e.x=Math.ceil(t.x),e.y=Math.ceil(t.y),e},E.clone=function(t,n){if(e.defined(t))return e.defined(n)?(n.x=t.x,n.y=t.y,n):new E(t.x,t.y)},E.fromCartesian3=E.clone,E.fromCartesian4=E.clone,E.packedLength=2,E.pack=function(t,n,r){return r=e.defaultValue(r,0),n[r++]=t.x,n[r]=t.y,n},E.unpack=function(t,n,r){return n=e.defaultValue(n,0),e.defined(r)||(r=new E),r.x=t[n++],r.y=t[n],r},E.packArray=function(t,r){var a=t.length,i=2*a;if(e.defined(r)){if(!Array.isArray(r)&&r.length!==i)throw new n.DeveloperError("If result is a typed array, it must have exactly array.length * 2 elements");r.length!==i&&(r.length=i)}else r=new Array(i);for(var o=0;o<a;++o)E.pack(t[o],r,2*o);return r},E.unpackArray=function(t,r){if(n.Check.defined("array",t),n.Check.typeOf.number.greaterThanOrEquals("array.length",t.length,2),t.length%2!==0)throw new n.DeveloperError("array length must be a multiple of 2.");var a=t.length;e.defined(r)?r.length=a/2:r=new Array(a/2);for(var i=0;i<a;i+=2){var o=i/2;r[o]=E.unpack(t,i,r[o])}return r},E.fromArray=E.unpack,E.maximumComponent=function(t){return Math.max(t.x,t.y)},E.minimumComponent=function(t){return Math.min(t.x,t.y)},E.minimumByComponent=function(t,e,n){return n.x=Math.min(t.x,e.x),n.y=Math.min(t.y,e.y),n},E.maximumByComponent=function(t,e,n){return n.x=Math.max(t.x,e.x),n.y=Math.max(t.y,e.y),n},E.magnitudeSquared=function(t){return t.x*t.x+t.y*t.y},E.magnitude=function(t){return Math.sqrt(E.magnitudeSquared(t))};var M=new E;E.distance=function(t,e){return E.subtract(t,e,M),E.magnitude(M)};var g=new E,w=new E;E.getProjectPtOnLine=function(t,e,n,r){E.subtract(n,e,g),E.normalize(g,g),E.subtract(t,e,w);var a=E.dot(w,g);return E.multiplyByScalar(g,a,w),E.add(e,w,r),r},E.distanceSquared=function(t,e){return E.subtract(t,e,M),E.magnitudeSquared(M)},E.normalize=function(t,e){var r=E.magnitude(t);if(e.x=t.x/r,e.y=t.y/r,isNaN(e.x)||isNaN(e.y))throw new n.DeveloperError("normalized result is not a number");return e},E.dot=function(t,e){return t.x*e.x+t.y*e.y},E.multiplyComponents=function(t,e,n){return n.x=t.x*e.x,n.y=t.y*e.y,n},E.divideComponents=function(t,e,n){return n.x=t.x/e.x,n.y=t.y/e.y,n},E.add=function(t,e,n){return n.x=t.x+e.x,n.y=t.y+e.y,n},E.subtract=function(t,e,n){return n.x=t.x-e.x,n.y=t.y-e.y,n},E.multiplyByScalar=function(t,e,n){return n.x=t.x*e,n.y=t.y*e,n},E.divideByScalar=function(t,e,n){return n.x=t.x/e,n.y=t.y/e,n},E.negate=function(t,e){return e.x=-t.x,e.y=-t.y,e},E.abs=function(t,e){return e.x=Math.abs(t.x),e.y=Math.abs(t.y),e};var z=new E;E.lerp=function(t,e,n,r){return E.multiplyByScalar(e,n,z),r=E.multiplyByScalar(t,1-n,r),E.add(z,r,r)};var v=new E,I=new E;E.angleBetween=function(t,e){return E.normalize(t,v),E.normalize(e,I),a.acosClamped(E.dot(v,I))};var O=new E;E.mostOrthogonalAxis=function(t,e){var n=E.normalize(t,O);return E.abs(n,n),e=n.x<=n.y?E.clone(E.UNIT_X,e):E.clone(E.UNIT_Y,e),e},E.equals=function(t,n){return t===n||e.defined(t)&&e.defined(n)&&t.x===n.x&&t.y===n.y},E.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]},E.equalsEpsilon=function(t,n,r,i){return t===n||e.defined(t)&&e.defined(n)&&a.equalsEpsilon(t.x,n.x,r,i)&&a.equalsEpsilon(t.y,n.y,r,i)},E.IDENTIFY=Object.freeze(new E(1,1)),E.ZERO=Object.freeze(new E(0,0)),E.UNIT_X=Object.freeze(new E(1,0)),E.UNIT_Y=Object.freeze(new E(0,1)),E.prototype.clone=function(t){return E.clone(this,t)},E.prototype.equals=function(t){return E.equals(this,t)},E.prototype.equalsEpsilon=function(t,e,n){return E.equalsEpsilon(this,t,e,n)},E.prototype.toString=function(){return"("+this.x+", "+this.y+")"},t.Cartesian2=E,t.Cartesian3=u,t.CesiumMath=a}));