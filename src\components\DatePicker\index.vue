<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-23 11:10:31
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-22 11:21:19
 * @FilePath: \bigscreen-qj-web\src\components\DatePicker\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="date-picker-container" :style="style">
    <el-date-picker
      v-model="date"
      v-bind="$attrs"
      :type="type"
      :value-format="valueFormat"
      placeholder="请选择日期"
      :style="dateStyle"
      teleported
      @change="handleChange" />
  </div>
</template>

<script setup>
  import { ElDatePicker } from 'element-plus'
  defineOptions({
    name: 'datePicker'
  })
  defineProps({
    type: {
      type: String,
      default: 'date'
    },
    style: {
      type: Object
    },
    dateStyle: {
      type: Object,
      default: () => {
        return {
          width: '192px',
          height: '42px'
        }
      }
    },
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD'
    }
  })
  const date = defineModel()
  const emits = defineEmits(['change'])
  const handleChange = () => {
    emits('change', date.value)
  }
</script>

<style lang="scss" scoped>
  .date-picker-container {
    position: absolute;

    // right: 10px;
    // float: left;
    width: 192px;
    height: 42px;

    // margin-top: -57px;
    // margin-left: 320px;
    color: #8dd8ff;
    :deep(.el-input__inner),
    :deep(.el-range-input) {
      margin-left: -5px;
      font-size: 18px !important;
      color: #8dd8ff;
    }
    :deep(.el-input__wrapper) {
      // background-color: transparent !important;
      background: url('@/assets/CommonPopup/dateTime.png') no-repeat !important;
      background-size: 100% 100% !important;

      // border: 1px solid #47b7ff;
      box-shadow: none;
    }
    :deep(.el-input__prefix) {
      color: #8dd8ff;
    }
    :deep(.el-input__icon svg) {
      scale: 1.5;
    }
  }
</style>
