/**
 * @description: 悬浮提示指令
 * @param {Boolean} showEllipsis  是否溢出显示省略号,默认true
 * @param {Boolean} showTooltip  是否展示提示框,默认根据元素是否溢出判断
 * @param {Number} row  能够展示的行数,默认1
 * ...其它属性同el-tooltip的属性一致
 */
import { h, render } from 'vue'

import MyTooltip from '@Common/components/MyTooltip/index.vue'

const vTooltip = {
  mounted(el, binding) {
    renderElement(el, binding)
  },
  updated(el, binding, vnode) {
    render(null, el)
    el.innerText = vnode.children[0]?.children
    renderElement(el, binding)
  }
}
const renderElement = (el, binding) => {
  // 获取配置项
  const config = binding.value || {}
  const showEllipsis = config.showEllipsis !== undefined ? config.showEllipsis : true
  // 如果需要省略号,则设置样式
  if (showEllipsis) {
    el.style.overflow = 'hidden'
  }
  // 获取元素内容
  const content = el.innerText
  // 清空元素内容
  el.innerText = ''
  // 构建 tooltip 属性
  const attributes = {
    placement: 'top',
    content,
    ...config
  }
  // 创建 tooltip 虚拟节点
  const vnode = h(
    MyTooltip,
    // 传入 prop
    attributes,
    () => content
  )
  // 渲染 vnode
  render(vnode, el)
}
export default vTooltip
