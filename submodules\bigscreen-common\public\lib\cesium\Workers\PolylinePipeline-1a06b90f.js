/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./IntersectionTests-25cff68e","./Math-b5f4d889","./Plane-a268aa11"],(function(e,t,a,n,i,r,o,s,c){"use strict";const l={numberOfPoints:function(e,a,n){const i=t.Cartesian3.distance(e,a);return Math.ceil(i/n)},numberOfPointsRhumbLine:function(e,t,a){const n=Math.pow(e.longitude-t.longitude,2)+Math.pow(e.latitude-t.latitude,2);return Math.max(1,Math.ceil(Math.sqrt(n/(a*a))))}},u=new a.Cartographic;l.extractHeights=function(e,t){const a=e.length,n=new Array(a);for(let i=0;i<a;i++){const a=e[i];n[i]=t.cartesianToCartographic(a,u).height}return n};const h=new a.Matrix4,f=new t.Cartesian3,g=new t.Cartesian3,C=new c.Plane(t.Cartesian3.UNIT_X,0),d=new t.Cartesian3,p=new c.Plane(t.Cartesian3.UNIT_X,0),m=new t.Cartesian3,w=new t.Cartesian3,P=[];function T(e,t,a){const n=P;let i;if(n.length=e,t===a){for(i=0;i<e;i++)n[i]=t;return n}const r=(a-t)/e;for(i=0;i<e;i++){const e=t+i*r;n[i]=e}return n}const y=new a.Cartographic,b=new a.Cartographic,A=new t.Cartesian3,E=new t.Cartesian3,R=new t.Cartesian3,M=new i.EllipsoidGeodesic;let S=new r.EllipsoidRhumbLine;function D(e,a,n,i,r,o,s,c){const u=i.scaleToGeodeticSurface(e,E),h=i.scaleToGeodeticSurface(a,R),f=l.numberOfPoints(e,a,n),g=i.cartesianToCartographic(u,y),C=i.cartesianToCartographic(h,b),d=T(f,r,o);M.setEndPoints(g,C);const p=M.surfaceDistance/f;let m=c;g.height=r;let w=i.cartographicToCartesian(g,A);t.Cartesian3.pack(w,s,m),m+=3;for(let e=1;e<f;e++){const a=M.interpolateUsingSurfaceDistance(e*p,b);a.height=d[e],w=i.cartographicToCartesian(a,A),t.Cartesian3.pack(w,s,m),m+=3}return m}function x(e,a,n,i,o,s,c,u){const h=i.cartesianToCartographic(e,y),f=i.cartesianToCartographic(a,b),g=l.numberOfPointsRhumbLine(h,f,n);h.height=0,f.height=0;const C=T(g,o,s);S.ellipsoid.equals(i)||(S=new r.EllipsoidRhumbLine(void 0,void 0,i)),S.setEndPoints(h,f);const d=S.surfaceDistance/g;let p=u;h.height=o;let m=i.cartographicToCartesian(h,A);t.Cartesian3.pack(m,c,p),p+=3;for(let e=1;e<g;e++){const a=S.interpolateUsingSurfaceDistance(e*d,b);a.height=C[e],m=i.cartographicToCartesian(a,A),t.Cartesian3.pack(m,c,p),p+=3}return p}l.wrapLongitude=function(e,i){const r=[],s=[];if(n.defined(e)&&e.length>0){i=n.defaultValue(i,a.Matrix4.IDENTITY);const l=a.Matrix4.inverseTransformation(i,h),u=a.Matrix4.multiplyByPoint(l,t.Cartesian3.ZERO,f),P=t.Cartesian3.normalize(a.Matrix4.multiplyByPointAsVector(l,t.Cartesian3.UNIT_Y,g),g),T=c.Plane.fromPointNormal(u,P,C),y=t.Cartesian3.normalize(a.Matrix4.multiplyByPointAsVector(l,t.Cartesian3.UNIT_X,d),d),b=c.Plane.fromPointNormal(u,y,p);let A=1;r.push(t.Cartesian3.clone(e[0]));let E=r[0];const R=e.length;for(let a=1;a<R;++a){const i=e[a];if(c.Plane.getPointDistance(b,E)<0||c.Plane.getPointDistance(b,i)<0){const e=o.IntersectionTests.lineSegmentPlane(E,i,T,m);if(n.defined(e)){const a=t.Cartesian3.multiplyByScalar(P,5e-9,w);c.Plane.getPointDistance(T,E)<0&&t.Cartesian3.negate(a,a),r.push(t.Cartesian3.add(e,a,new t.Cartesian3)),s.push(A+1),t.Cartesian3.negate(a,a),r.push(t.Cartesian3.add(e,a,new t.Cartesian3)),A=1}}r.push(t.Cartesian3.clone(e[a])),A++,E=i}s.push(A)}return{positions:r,lengths:s}},l.generateArc=function(e){n.defined(e)||(e={});const i=e.positions,r=i.length,o=n.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84);let c=n.defaultValue(e.height,0);const u=Array.isArray(c);if(r<1)return[];if(1===r){const e=o.scaleToGeodeticSurface(i[0],E);if(c=u?c[0]:c,0!==c){const a=o.geodeticSurfaceNormal(e,A);t.Cartesian3.multiplyByScalar(a,c,a),t.Cartesian3.add(e,a,e)}return[e.x,e.y,e.z]}let h=e.minDistance;if(!n.defined(h)){const t=n.defaultValue(e.granularity,s.CesiumMath.RADIANS_PER_DEGREE);h=s.CesiumMath.chordLength(t,o.maximumRadius)}let f,g=0;for(f=0;f<r-1;f++)g+=l.numberOfPoints(i[f],i[f+1],h);const C=3*(g+1),d=new Array(C);let p=0;for(f=0;f<r-1;f++){p=D(i[f],i[f+1],h,o,u?c[f]:c,u?c[f+1]:c,d,p)}P.length=0;const m=i[r-1],w=o.cartesianToCartographic(m,y);w.height=u?c[r-1]:c;const T=o.cartographicToCartesian(w,A);return t.Cartesian3.pack(T,d,C-3),d};const N=new a.Cartographic,G=new a.Cartographic;l.generateRhumbArc=function(e){n.defined(e)||(e={});const i=e.positions,r=i.length,o=n.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84);let c=n.defaultValue(e.height,0);const u=Array.isArray(c);if(r<1)return[];if(1===r){const e=o.scaleToGeodeticSurface(i[0],E);if(c=u?c[0]:c,0!==c){const a=o.geodeticSurfaceNormal(e,A);t.Cartesian3.multiplyByScalar(a,c,a),t.Cartesian3.add(e,a,e)}return[e.x,e.y,e.z]}const h=n.defaultValue(e.granularity,s.CesiumMath.RADIANS_PER_DEGREE);let f,g,C=0,d=o.cartesianToCartographic(i[0],N);for(f=0;f<r-1;f++)g=o.cartesianToCartographic(i[f+1],G),C+=l.numberOfPointsRhumbLine(d,g,h),d=a.Cartographic.clone(g,N);const p=3*(C+1),m=new Array(p);let w=0;for(f=0;f<r-1;f++){w=x(i[f],i[f+1],h,o,u?c[f]:c,u?c[f+1]:c,m,w)}P.length=0;const T=i[r-1],b=o.cartesianToCartographic(T,y);b.height=u?c[r-1]:c;const R=o.cartographicToCartesian(b,A);return t.Cartesian3.pack(R,m,p-3),m},l.generateCartesianArc=function(e){const a=l.generateArc(e),n=a.length/3,i=new Array(n);for(let e=0;e<n;e++)i[e]=t.Cartesian3.unpack(a,3*e);return i},l.generateCartesianRhumbArc=function(e){const a=l.generateRhumbArc(e),n=a.length/3,i=new Array(n);for(let e=0;e<n;e++)i[e]=t.Cartesian3.unpack(a,3*e);return i};var I=l;e.PolylinePipeline=I}));
