<template>
  <div class="popup-title-container">
    <span class="title">{{ title }}</span>
    <div class="close-icon" @click="handleClose"></div>
    <div class="line"></div>
    <slot></slot>
  </div>
</template>

<script setup>
  defineProps({
    title: {
      type: String,
      default: ''
    }
  })
  const emits = defineEmits(['handleClose'])
  const handleClose = () => {
    emits('handleClose')
  }
</script>

<style lang="scss" scoped>
  .popup-title-container {
    position: relative;
    width: 100%;
    cursor: move;
    .title {
      font-family: Alibaba-PuHuiTi;
      font-size: 21px;
      font-weight: normal;
      line-height: 29px;
      color: #ffffff;
    }
    .close-icon {
      position: absolute;
      top: -10px;
      right: -10px;
      width: 44px;
      height: 44px;
      cursor: pointer;
      background: url('@/assets/ScreenMiddle/Emergency/close-icon.png') no-repeat center;
      background-size: 22px 22px;

      //   border: 1px solid red;
    }
    .line {
      width: 100%;
      height: 2px;
      margin-top: 4px;
      background: url('@/assets/CommonPopup/titleLine.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
