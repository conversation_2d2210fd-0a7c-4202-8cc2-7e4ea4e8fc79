<template>
  <div class="event-card-top">
    <div class="card-top">
      <div class="card-top-left">
        <span class="top-left-img"></span>
        <span class="top-left-text">
          {{ title }}
        </span>
        <span v-if="leftBtnText" class="top-left-btn" :style="{ background: leftBtnColor, border: '1px solid ' + leftBtnBorderColor,color: leftBtnBorderColor }">
          <span class="btn-text">{{ leftBtnText }}</span>
          <!-- <span class="btn-icon"></span> -->
        </span>
      </div>
      <span v-if="isClose" class="close-btn" @click="handleCloseClick">
        <img src="@/assets/images/pop/closePop.png"/>
      </span>
      <div v-show="btnText" :class="`card-top-right ${btnType}`">
        {{ btnText }}
      </div>
    </div>
    <img class="bg1" src="@/assets/images/pop/titleBg.png" />
  </div>
</template>

<script setup>
import {inject} from 'vue'
  defineProps({
    title: {
      // 标题
      type: String,
      default: ''
    },
    leftBtnColor: {
      type: String,
      default: 'rgba(0, 180, 255, 0.15)'
    },
    leftBtnBorderColor: {
      type: String,
      default: '#00beff'
    },
    btnText: {
      // 右侧按钮文字
      type: String,
      default: ''
    },
    btnType: {
      // success warning
      type: String,
      default: 'success'
    },
    leftBtnText: {
      // 左侧按钮
      type: String,
      default: ''
    },
    isClose: {
      type: Boolean,
      default: false
    }
  })
  const emits=defineEmits(['handleClose'])
  const handleClose=inject('handleClose')
  
  const handleCloseClick=()=>{
    emits('handleClose')
    handleClose()
  }
</script>

<style lang="scss" scoped>
  .event-card-top {
    // width: 240px;
    position: relative;
    width: 100%;
    height: 40px;
    > div {
      height: 100%;
      width: 100%;
      background: linear-gradient(90deg, rgba(0, 141, 255, 0.25) 0%, rgba(0, 141, 255, 0.15) 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px 0 16px;
    }

    .bg1 {
      position: absolute;
      bottom: 0;
      right: 5px;
    }
    .card-top-left {
      > span {
        display: inline-block;
        vertical-align: middle;
      }
      .top-left-img {
        width: 9px;
        height: 17px;
        // border: 1px solid red;
        // background: #00BEFF;
        // box-shadow: 2px 3px 5px 0px #000E2C;
        background: url(../../assets/images/pop/arrowRight.png) no-repeat center;
      }
      .top-left-text {
        padding-left: 8px;

        font-size: 18px;
        font-family: Alibaba-PuHuiTi-M, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #ffffff;
        line-height: 25px;
      }
      .top-left-btn {
        cursor: pointer;

        height: 26px;

        border-radius: 12px;
        font-size: 16px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        line-height: 22px;
        text-align: center;
        margin-left: 30px;
        .btn-text {
          padding: 0 5px;
        }
        .btn-icon {
          display: inline-block;
          width: 14px;
          height: 11px;
          border: 1px solid red;
        }
      }
    }
    .card-top-right {
      &.warning {
        width: 57px;
        height: 16px;
        background: rgba(255, 181, 0, 0.2);
        border-radius: 8px;
        border: 1px solid #ffb500;
        text-align: center;

        font-size: 12px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #ffb500;
        line-height: 14px;
        text-shadow: 0px 1px 2px #053873;
      }
      &.success {
        width: 57px;
        height: 16px;
        background: rgba(0, 255, 0, 0.2);
        border-radius: 8px;
        border: 1px solid #00ff00;
        text-align: center;

        font-size: 12px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #00ff00;
        line-height: 14px;
        text-shadow: 0px 1px 2px #053873;
      }
    }
    // 关闭
    .close-btn {
      width: 16px;
      height: 16px;
      // background: linear-gradient(180deg, #4574bc 0%, #193452 100%);
      // box-shadow: 0px 2px 4px 0px #000c2f;
      // border-radius: 2px;
      // border: 1px solid;
      // border-image: linear-gradient(180deg, rgba(139, 184, 255, 1), rgba(84, 144, 255, 1)) 1 1;
      cursor: pointer;
      z-index: 999;
    }
  }
</style>
