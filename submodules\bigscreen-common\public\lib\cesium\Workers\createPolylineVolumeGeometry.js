/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./arrayRemoveDuplicates-5b666c82","./BoundingRectangle-b4242da0","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./PolylineVolumeGeometryLibrary-fad92191","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./VertexFormat-86c096b8","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c"],(function(e,t,n,o,i,a,r,l,s,p,c,d,u,m,y,g,f,h,b,P,E,_,k,v,C,L,V,F){"use strict";function x(n){const o=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).polylinePositions,i=n.shapePositions;this._positions=o,this._shape=i,this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(n.cornerType,l.CornerType.ROUNDED),this._vertexFormat=y.VertexFormat.clone(e.defaultValue(n.vertexFormat,y.VertexFormat.DEFAULT)),this._granularity=e.defaultValue(n.granularity,u.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeGeometry";let r=1+o.length*a.Cartesian3.packedLength;r+=1+i.length*a.Cartesian2.packedLength,this.packedLength=r+t.Ellipsoid.packedLength+y.VertexFormat.packedLength+2}x.pack=function(n,o,i){let r;i=e.defaultValue(i,0);const l=n._positions;let s=l.length;for(o[i++]=s,r=0;r<s;++r,i+=a.Cartesian3.packedLength)a.Cartesian3.pack(l[r],o,i);const p=n._shape;for(s=p.length,o[i++]=s,r=0;r<s;++r,i+=a.Cartesian2.packedLength)a.Cartesian2.pack(p[r],o,i);return t.Ellipsoid.pack(n._ellipsoid,o,i),i+=t.Ellipsoid.packedLength,y.VertexFormat.pack(n._vertexFormat,o,i),i+=y.VertexFormat.packedLength,o[i++]=n._cornerType,o[i]=n._granularity,o};const T=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),A=new y.VertexFormat,G={polylinePositions:void 0,shapePositions:void 0,ellipsoid:T,vertexFormat:A,cornerType:void 0,granularity:void 0};x.unpack=function(n,o,i){let r;o=e.defaultValue(o,0);let l=n[o++];const s=new Array(l);for(r=0;r<l;++r,o+=a.Cartesian3.packedLength)s[r]=a.Cartesian3.unpack(n,o);l=n[o++];const p=new Array(l);for(r=0;r<l;++r,o+=a.Cartesian2.packedLength)p[r]=a.Cartesian2.unpack(n,o);const c=t.Ellipsoid.unpack(n,o,T);o+=t.Ellipsoid.packedLength;const d=y.VertexFormat.unpack(n,o,A);o+=y.VertexFormat.packedLength;const u=n[o++],m=n[o];return e.defined(i)?(i._positions=s,i._shape=p,i._ellipsoid=t.Ellipsoid.clone(c,i._ellipsoid),i._vertexFormat=y.VertexFormat.clone(d,i._vertexFormat),i._cornerType=u,i._granularity=m,i):(G.polylinePositions=s,G.shapePositions=p,G.cornerType=u,G.granularity=m,new x(G))};const D=new o.BoundingRectangle;return x.createGeometry=function(e){const t=e._positions,u=n.arrayRemoveDuplicates(t,a.Cartesian3.equalsEpsilon);let y=e._shape;if(y=l.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(y),u.length<2||y.length<3)return;m.PolygonPipeline.computeWindingOrder2D(y)===m.WindingOrder.CLOCKWISE&&y.reverse();const g=o.BoundingRectangle.fromPoints(y,D);return function(e,t,n,o){const a=new p.GeometryAttributes;o.position&&(a.position=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e}));const u=t.length,y=e.length/3,g=(y-2*u)/(2*u),f=m.PolygonPipeline.triangulate(t),h=(g-1)*u*6+2*f.length,b=d.IndexDatatype.createTypedArray(y,h);let P,E,_,k,v,C;const L=2*u;let V=0;for(P=0;P<g-1;P++){for(E=0;E<u-1;E++)_=2*E+P*u*2,C=_+L,k=_+1,v=k+L,b[V++]=k,b[V++]=_,b[V++]=v,b[V++]=v,b[V++]=_,b[V++]=C;_=2*u-2+P*u*2,k=_+1,v=k+L,C=_+L,b[V++]=k,b[V++]=_,b[V++]=v,b[V++]=v,b[V++]=_,b[V++]=C}if(o.st||o.tangent||o.bitangent){const e=new Float32Array(2*y),o=1/(g-1),i=1/n.height,l=n.height/2;let p,c,d=0;for(P=0;P<g;P++){for(p=P*o,c=i*(t[0].y+l),e[d++]=p,e[d++]=c,E=1;E<u;E++)c=i*(t[E].y+l),e[d++]=p,e[d++]=c,e[d++]=p,e[d++]=c;c=i*(t[0].y+l),e[d++]=p,e[d++]=c}for(E=0;E<u;E++)p=0,c=i*(t[E].y+l),e[d++]=p,e[d++]=c;for(E=0;E<u;E++)p=(g-1)*o,c=i*(t[E].y+l),e[d++]=p,e[d++]=c;a.st=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(e)})}const F=y-2*u;for(P=0;P<f.length;P+=3){const e=f[P]+F,t=f[P+1]+F,n=f[P+2]+F;b[V++]=e,b[V++]=t,b[V++]=n,b[V++]=n+u,b[V++]=t+u,b[V++]=e+u}let x=new s.Geometry({attributes:a,indices:b,boundingSphere:i.BoundingSphere.fromVertices(e),primitiveType:s.PrimitiveType.TRIANGLES});if(o.normal&&(x=c.GeometryPipeline.computeNormal(x)),o.tangent||o.bitangent){try{x=c.GeometryPipeline.computeTangentAndBitangent(x)}catch(e){l.oneTimeWarning("polyline-volume-tangent-bitangent","Unable to compute tangents and bitangents for polyline volume geometry")}o.tangent||(x.attributes.tangent=void 0),o.bitangent||(x.attributes.bitangent=void 0),o.st||(x.attributes.st=void 0)}return x}(l.PolylineVolumeGeometryLibrary.computePositions(u,y,g,e,!0),y,g,e._vertexFormat)},function(n,o){return e.defined(o)&&(n=x.unpack(n,o)),n._ellipsoid=t.Ellipsoid.clone(n._ellipsoid),x.createGeometry(n)}}));
