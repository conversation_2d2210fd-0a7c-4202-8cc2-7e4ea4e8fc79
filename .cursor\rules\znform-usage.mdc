---
description: 
globs: 
alwaysApply: false
---
# ZnForm 表单组件使用规范

## 🔥 关键要点

⚠️ **数据独立性原则**：为避免组件间数据共享问题，必须确保：
1. **函数化默认值**：所有默认值通过函数返回，禁用全局变量
2. **新实例保证**：每次调用 `useRoadInfo` 都创建新的表单数据实例


## 使用前置要求

⚠️ **重要**：在使用ZnForm组件之前，必须先调用MCP服务获取最新的组件文档：

```javascript
// 必须先查看最新文档
// URL: http://*************:10000/znyg-frontend-common-docs/components/ZnForm.html
```

⚠️ **API文档集成要求**：当用户指定了API路径时，必须调用MCP服务获取API文档内容：

### 1. API文档获取流程

当用户提供API路径（如：`/rest/structureDiy/roadSave`）时，必须按以下步骤执行：

1. **调用MCP服务**：使用 `mcp_apipost-server_findAndGetApiDoc` 工具获取API文档
2. **解析字段结构**：分析API文档中的请求参数和响应字段
3. **字段映射验证**：确保生成的表单字段与API文档中的字段完全对应
4. **字典配置识别**：识别API文档中的字典配置（如 `字典:dict_code`）

### 2. MCP服务调用示例

```javascript
// 调用MCP服务获取API文档
await mcp_apipost_server_findAndGetApiDoc({
  apiPath: "/rest/structureDiy/roadSave"
})
```

### 3. 字段对应规范

生成表单时必须确保：
- **字段名称**：表单字段的 `prop` 属性必须与API文档中的字段名完全一致
- **字段类型**：根据API文档中的字段类型设置正确的表单控件
- **必填验证**：API文档中标记为必填的字段必须添加验证规则
- **字典配置**：API文档中有字典配置的字段必须使用 `dictCode` 属性
- **字段描述**：表单字段的 `label` 应与API文档中的字段描述保持一致

## 默认值管理规范

⚠️ **重要规范**：所有默认值必须统一在helper文件中管理，确保数据一致性和可维护性。

⚠️ **关键原则**：为了避免组件间数据共享问题，必须确保每次调用都返回新的数组/对象实例。

### 1. 默认值结构定义

⚠️ **严格要求**：所有默认值必须通过函数返回，避免全局变量导致的数据共享问题。

```tsx
// helper/index.tsx

// ✅ 正确：使用函数返回新的数组实例
export const getDefaultAssetParamList = () => [
  { name: '道路面积', value: null, unit: 'm²', type: 'road_base_param' },
  { name: '红线宽度', value: null, unit: 'm', type: 'road_base_param' },
  // ... 更多参数
]

// ✅ 正确：使用函数返回新的对象实例
export const getDefaultEmptyMaterialItem = () => ({
  roadway: { name: '', value: '', unit: '', type: 'road_roadway_param' },
  sidewalk: { name: '', value: '', unit: '', type: 'road_sidewalk_param' }
})

// ✅ 正确：使用函数返回新的表单数据实例
const getDefaultFormData = () => ({
  name: '',
  code: '',
  assetParamList: getDefaultAssetParamList(), // 每次调用都获取新数组
  laneOtherMaterials: [getDefaultEmptyMaterialItem().roadway],
  sidewalkOtherMaterials: [getDefaultEmptyMaterialItem().sidewalk],
  id: null
})

// ❌ 错误：使用全局变量（会导致数据共享问题）
export const defaultAssetParamList = [
  { name: '道路面积', value: null, unit: 'm²', type: 'road_base_param' },
  // ... 这种方式会导致所有组件实例共享同一个数组引用
]
```

### 2. Hook函数实现规范

⚠️ **关键要求**：Hook函数必须每次调用都创建新的表单数据实例，避免数据共享。

```tsx
// ✅ 正确：每次调用都创建新的表单数据实例
export const useRoadInfo = (refZnForm: Ref<any>, isView: Ref<boolean>) => {
  // 每次调用都创建新的表单数据实例
  const formData = reactive(getDefaultFormData())
  const formSchema = useRoadInfoSchema(formData, isView)

  return {
    formData,
    formSchema,
    elFormProps,
    // 辅助函数
    extractOtherMaterialsFromAssetParam: (dataSource: any, type: string) => 
      extractOtherMaterialsFromAssetParam(dataSource, type),
    // 默认值获取函数 - 每次都返回新实例
    getDefaultAssetParamList: () => getDefaultAssetParamList(),
    getDefaultEmptyMaterialItem: (type: 'roadway' | 'sidewalk') => 
      getDefaultEmptyMaterialItem()[type]
  }
}

// ❌ 错误：使用全局对象引用
export const useRoadInfo = (refZnForm: Ref<any>, isView: boolean = false) => {
  const formData = reactive({ ...defaultFormData }) // 如果defaultFormData是全局对象，仍然有风险
  // ...
}
```

### 3. 组件中使用默认值规范

⚠️ **数据处理要求**：在处理后端数据时，必须确保 `assetParamList` 中的 `value` 为 `undefined` 时转换为 `null`。

```vue
<script setup lang="ts">
  const { 
    formData, 
    formSchema, 
    elFormProps, 
    getDefaultAssetParamList,
    getDefaultEmptyMaterialItem
  } = useRoadInfo(refZnForm, toRef(props, 'isView'))

  // 数据转换函数中使用默认值
  const transformDataFromBackend = (backendData: any) => {
    const frontendData = { ...backendData }

    // 使用helper中的默认值函数，确保每次都是新实例
    if (!frontendData.assetParamList || !Array.isArray(frontendData.assetParamList)) {
      frontendData.assetParamList = getDefaultAssetParamList()
    } else {
      // ✅ 重要：确保value为undefined时转换为null
      frontendData.assetParamList = frontendData.assetParamList.map((item: any) => ({
        ...item,
        value: item.value === undefined ? null : item.value
      }))
    }

    // 确保至少有一个空项
    if (!frontendData.laneOtherMaterials || frontendData.laneOtherMaterials.length === 0) {
      frontendData.laneOtherMaterials = [getDefaultEmptyMaterialItem('roadway')]
    }

    if (!frontendData.sidewalkOtherMaterials || frontendData.sidewalkOtherMaterials.length === 0) {
      frontendData.sidewalkOtherMaterials = [getDefaultEmptyMaterialItem('sidewalk')]
    }

    return frontendData
  }


</script>
```

### 4. 默认值管理最佳实践

#### ✅ 正确做法：

1. **函数化管理**：所有默认值必须通过函数返回，避免全局变量
2. **新实例保证**：每次调用都返回新的数组/对象实例
3. **类型安全**：为默认值定义TypeScript类型
4. **命名规范**：使用 `getDefault` 前缀 + 描述性名称

```tsx
// ✅ 正确：函数化的默认值管理
const getDefaultFormData = () => ({
  assetParamList: getDefaultAssetParamList(), // 每次调用都获取新数组
  // ...
})

// ✅ 正确：函数返回新实例
export const getDefaultAssetParamList = () => [
  { name: '道路面积', value: null, unit: 'm²', type: 'road_base_param' },
  // ... 每次调用都返回新数组
]

// ✅ 正确：Hook中使用函数
export const useRoadInfo = (refZnForm: Ref<any>, isView: Ref<boolean>) => {
  const formData = reactive(getDefaultFormData()) // 每次都是新实例
  // ...
}

// ✅ 正确：类型定义
interface AssetParam {
  name: string
  value: number | null
  unit: string
  type: 'road_base_param' | 'road_roadway_param' | 'road_sidewalk_param'
}
```

#### ❌ 错误做法：

1. **全局变量**：使用全局常量导致数据共享
2. **组件内硬编码**：在组件中直接定义默认值
3. **引用传递**：直接使用数组/对象引用
4. **未处理undefined**：不处理后端数据中的undefined值

```tsx
// ❌ 错误：全局变量导致数据共享
export const defaultAssetParamList = [
  { name: '道路面积', value: null, unit: 'm²' }, // 所有组件实例共享同一个数组
]

// ❌ 错误：Hook中使用全局对象
export const useRoadInfo = () => {
  const formData = reactive({ ...defaultFormData }) // 如果defaultFormData是全局对象，仍有风险
}

// ❌ 错误：组件内硬编码
const transformDataFromBackend = (backendData: any) => {
  if (!backendData.assetParamList) {
    frontendData.assetParamList = [
      { name: '道路面积', value: null, unit: 'm²' }, // 硬编码
    ]
  }
}

// ❌ 错误：直接引用，可能导致意外修改
frontendData.assetParamList = defaultAssetParamList // 应该使用函数获取新实例

// ❌ 错误：未处理undefined值
frontendData.assetParamList = backendData.assetParamList // 可能包含undefined值
```

### 5. 数据一致性保证

通过函数化的默认值管理确保数据一致性：

```tsx
// 验证函数：确保数据结构一致
const validateAssetParamList = (assetParamList: any[]) => {
  const requiredFields = ['name', 'value', 'unit', 'type']
  return assetParamList.every(item => 
    requiredFields.every(field => field in item)
  )
}

// 数据修复函数：自动修复缺失字段和undefined值
const fixAssetParamList = (assetParamList: any[]) => {
  return assetParamList.map(item => ({
    name: item.name || '',
    value: item.value === undefined ? null : item.value, // 处理undefined
    unit: item.unit || '',
    type: item.type || 'road_base_param',
    ...item // 保留其他字段
  }))
}
```

### 6. 默认值更新流程

当需要更新默认值时：

1. **只修改helper文件**：所有默认值变更只在helper文件中进行
2. **版本控制**：重要的默认值变更要有版本说明
3. **向后兼容**：考虑现有数据的兼容性
4. **测试验证**：确保默认值变更不影响现有功能

```tsx
// 版本化的默认值管理
export const defaultAssetParamListV2 = [
  // 新版本的默认值结构
]

// 兼容性处理
export const migrateAssetParamList = (oldList: any[], version: string = 'v1') => {
  if (version === 'v1') {
    return oldList.map(item => ({
      ...item,
      type: item.type || 'road_base_param' // 添加默认type字段
    }))
  }
  return oldList
}
```


使用函数返回默认值避免意外的引用修改：

```tsx
// ✅ 正确：每次返回新实例，避免引用问题
const getDefaultAssetParamList = () => [
  { name: '道路面积', value: null, unit: 'm²', type: 'road_base_param' },
  // ...
]

// ❌ 错误：直接导出数组，可能被意外修改
export const defaultAssetParamList = [
  { name: '道路面积', value: null, unit: 'm²', type: 'road_base_param' }
]
```

## 严格执行规则

⚠️ **关键原则**：

1. **严格按用户需求生成**：

   - 根据用户提供的图片、文档或具体要求创建表单字段
   - **API文档优先**：当用户指定API路径时，必须先调用MCP服务获取API文档，字段生成严格按照API文档执行
   - 字段名前有星号(\*)的为必填项，必须添加验证规则
   - 不得随意添加、删除或修改字段名称
   - 字段标签必须与用户要求或API文档中的字段描述完全一致

2. **数据一致性要求**：

   - 表单字段的 `prop` 属性必须与 `defaultFormData` 中的字段名一致
   - **API字段映射**：当使用API文档时，表单字段必须与API文档中的字段名完全对应
   - 所有form schema中使用的字段都必须在defaultFormData中定义
   - **el-select选择器选项配置**：
     - 当API文档或接口文档中字段有字典配置（如 `字典:dict_code`）时，优先使用 `dictCode` 属性
     - 当没有字典配置时，使用 `enum` 属性而不是 `options` 属性
     - 选择器选项必须与实际业务场景匹配
   - **系统管理字段处理**：不要在 `defaultFormData` 中包含 `recordCreateDate`、`recordUpdateDate` 等由后端系统自动管理的时间字段

3. **组件属性使用**：
   - 严格按照最新文档使用组件属性
   - **必须启用 `lazy-load` 属性**：所有 ZnForm 组件都必须设置 `lazy-load` 为 true
   - 使用 `modelValue` 而不是 `value` 进行数据绑定
   - 自定义组件事件使用 `onUpdate:modelValue` 而不是 `onChange`
   - **推荐使用 `defineModel`** 处理自定义组件的双向绑定，替代传统的 `props` + `emit` 方式
   - **字典数据配置**：对于el-select选择器，优先使用 `dictCode` 属性而不是 `enum` 属性，当接口文档中字段有字典配置时

4. **样式规范要求**：
   - **必须使用 SCSS** 编写所有组件样式
   - 使用 `<style scoped lang="scss">` 语法
   - 遵循项目统一的样式规范和命名约定

## 标准目录结构

⚠️ **重要规范**：对于非单文件的子组件，必须统一放到一个文件夹中，遵循以下目录结构：

### 1. 单文件组件
```
ComponentName.vue          # 简单的单文件组件
```

### 2. 复杂组件目录结构
```
ComponentName/
├── index.vue              # 主组件入口文件
├── helper/
│   └── index.tsx          # 表单配置和数据处理
├── components/            # 子组件目录
│   ├── SubComponent1/     # 子组件1
│   │   ├── index.vue      # 子组件入口
│   │   └── helper/        # 子组件配置（如需要）
│   │       └── index.tsx
│   └── SubComponent2.vue  # 简单子组件
├── test-demo.vue          # 组件测试演示页面（可选）
└── README.md              # 组件使用文档（可选）
```

### 3. 实际应用示例
参考 [UserForm组件](mdc:src/views/userManage/components/UserForm) 的目录结构：

```
userManage/
├── index.vue              # 用户管理主页面
├── helper/
│   └── index.tsx          # 表格配置和数据处理
├── components/
│   └── UserForm/          # 用户表单组件目录
│       ├── index.vue      # 表单组件入口
│       ├── helper/
│       │   └── index.tsx  # 表单配置和验证规则
│       └── components/    # 表单子组件（如需要）
│           └── ...
└── demo.vue               # 功能演示页面
```

### 目录结构规范说明

1. **组件入口文件**：
   - 所有复杂组件必须使用 `index.vue` 作为入口文件
   - 单文件组件可以直接使用 `ComponentName.vue`

2. **helper目录**：
   - 每个组件的配置、数据处理逻辑统一放在 `helper/index.tsx` 中
   - 包含数据模型、选项配置、字段schema、验证规则等

3. **components子目录**：
   - 组件的子组件统一放在 `components/` 目录下
   - 复杂子组件使用目录结构，简单子组件使用单文件
   - 子组件如果有自己的子组件，继续在其 `components/` 目录下创建

4. **导入路径规范**：
   ```typescript
   // ✅ 正确：导入复杂组件
   import UserForm from './components/UserForm/index.vue'
   
   // ✅ 正确：导入简单组件
   import SimpleComponent from './components/SimpleComponent.vue'
   
   // ✅ 正确：导入helper
   import { useUserForm } from './helper'
   ```

5. **命名规范**：
   - 组件目录使用 PascalCase：`UserForm/`、`BaseInfo/`
   - 组件文件使用 PascalCase：`UserForm.vue`、`BaseInfo.vue`
   - helper文件统一使用：`helper/index.tsx`

## 组件状态管理

### 三种状态模式

所有基于ZnForm的组件都应支持以下三种状态：

1. **新增模式 (create)**
   - 当 `id` 为 `null` 或未传入时
   - 表单为空白状态，可以输入新数据
   - 所有字段可编辑

2. **编辑模式 (edit)**
   - 当传入有效的 `id` 且 `isView` 为 `false` 时
   - 表单字段可编辑，用于修改已有数据
   - 支持数据验证和提交

3. **查看模式 (view)**
   - 当 `isView` 为 `true` 时
   - 表单为只读状态，不可编辑
   - 用于数据展示，自定义组件需支持查看模式

### 状态判断逻辑

```typescript
// 组件内部状态判断
const currentState = computed(() => {
  if (props.isView) return 'view'  // 查看模式
  if (formData.id === null || formData.id === undefined) return 'create'  // 新增模式
  return 'edit'  // 编辑模式
})
```

## 字典数据配置规范

### dictCode vs enum 使用原则

当配置 `el-select` 选择器时，需要根据接口文档中的字段说明来决定使用 `dictCode` 还是 `enum`：

#### 1. 使用 dictCode 的情况

当接口文档中字段有字典配置注释时（如 `字典:dict_code`、`dictCode:xxx`、`dict:xxx`），应使用 `dictCode` 属性：

```tsx
// ✅ 正确：使用 dictCode
{
  prop: 'manageRange',
  label: '管辖范围',
  el: 'el-select',
  dictCode: 'administer_range',  // 对应接口文档中的字典配置
  props: {
    placeholder: '请选择管辖范围'
  },
  col: { span: 12 },
  formItem: {
    rules: [{ required: true, message: '请选择管辖范围', trigger: 'change' }]
  }
}

// ❌ 错误：不应该同时使用 dictCode 和 enum
{
  prop: 'manageRange',
  label: '管辖范围',
  el: 'el-select',
  dictCode: 'administer_range',
  enum: manageRangeOptions,  // 多余的配置
  // ...
}
```

#### 2. 使用 enum 的情况

当接口文档中字段没有字典配置，或者是前端特有的选项时，使用 `enum` 属性：

```tsx
// ✅ 正确：使用 enum（无字典配置的字段）
{
  prop: 'isEnabled',
  label: '是否启用',
  el: 'el-select',
  enum: [
    { label: '是', value: true },
    { label: '否', value: false }
  ],
  props: {
    placeholder: '请选择是否启用'
  },
  col: { span: 12 }
}
```

#### 3. 常见字典配置示例

根据通过MCP服务获取的API文档 `/rest/structureDiy/roadSave` 的字段配置：

```tsx
// 管辖范围 - 字典:administer_range
{
  prop: 'manageRange',
  label: '管辖范围',
  el: 'el-select',
  dictCode: 'administer_range',
  // 不需要 enum 配置
}

// 所属区域 - 字典：area
{
  prop: 'areaList',
  label: '所属区域',
  el: 'el-select',
  dictCode: 'area',
  props: {
    multiple: true,
    collapseTags: true
  }
}

// 技术规范分类 - 字典：structure_tech_category
{
  prop: 'type',
  label: '技术规范分类',
  el: 'el-select',
  dictCode: 'structure_tech_category'
}

// 城市管理分类 - 字典:city_manage_category
{
  prop: 'cityManageCategory',
  label: '城市管理分类',
  el: 'el-select',
  dictCode: 'city_manage_category'
}

// 道路养护等级分类 - 字典：structure_maintain_level
{
  prop: 'level',
  label: '道路养护等级分类',
  el: 'el-select',
  dictCode: 'structure_maintain_level'
}

// 管路类型 - 字典：structure_road_type
{
  prop: 'roadType',
  label: '管路类型',
  el: 'el-select',
  dictCode: 'structure_road_type'
}

// 车行道结构 - 字典：lane_structure
{
  prop: 'laneStructure',
  label: '车行道结构',
  el: 'el-select',
  dictCode: 'lane_structure'
}

// 人行道结构 - 字典：sidewalk_structure
{
  prop: 'sidewalkStructure',
  label: '人行道结构',
  el: 'el-select',
  dictCode: 'sidewalk_structure'
}
```

#### 4. 配置清理规范

使用 `dictCode` 时，需要清理相关的静态配置：

```tsx
// ❌ 错误：保留了不必要的选项配置
export const formOptions = {
  manageRangeOptions: [  // 不再需要
    { label: '市属', value: 'municipal' },
    { label: '区属', value: 'district' }
  ],
  // 其他不使用字典的选项保留
  topOptions: [
    { label: '是', value: true },
    { label: '否', value: false }
  ]
}

// ✅ 正确：只保留非字典配置的选项
export const formOptions = {
  // 移除了使用字典的选项配置
  topOptions: [
    { label: '是', value: true },
    { label: '否', value: false }
  ],
  lengthUnitOptions: [
    { label: 'm', value: 'm' },
    { label: 'km', value: 'km' }
  ]
}
```

### 字典配置检查清单

在配置表单时，请按以下步骤检查：

1. **调用MCP服务**：当用户指定API路径时，先调用 `mcp_apipost-server_findAndGetApiDoc` 获取API文档
2. **查看API文档**：确认字段是否有字典配置注释
3. **字段映射验证**：确保表单字段与API文档字段完全对应
4. **选择配置方式**：有字典配置用 `dictCode`，无字典配置用 `enum`
5. **清理冗余配置**：移除使用 `dictCode` 字段对应的静态选项配置
6. **更新解构赋值**：从 `formOptions` 解构中移除不再使用的选项
7. **测试验证**：确保字典数据能正确加载和显示

## API文档集成示例

### 完整的API文档集成流程示例

以用户指定API路径 `/rest/structureDiy/roadSave` 为例：

#### 1. 调用MCP服务获取API文档

```javascript
// 第一步：调用MCP服务获取API文档
const apiDoc = await mcp_apipost_server_findAndGetApiDoc({
  apiPath: "/rest/structureDiy/roadSave"
})

// 第二步：分析API文档结构
// 假设API文档返回的字段结构如下：
/*
{
  "structureExt": {
    "designOrgId": "string", // 设计单位ID
    "designOrgName": "string", // 设计单位名称 
    "designOrgSource": "number", // 设计单位来源 字典:org_source
    "constructOrgId": "string", // 施工单位ID
    "constructOrgName": "string", // 施工单位名称
    "manageOrgId": "string", // 管理单位ID (必填)
    "manageOrgName": "string", // 管理单位名称 (必填)
    "transferStatus": "number", // 移交状态 字典:transfer_status
    "transferTime": "string", // 移交时间
    "isRequired": "boolean", // 是否必检
    "lastCheckTime": "string" // 最后检测时间
  }
}
*/
```

#### 2. 根据API文档生成表单配置

```tsx
// 根据API文档生成的defaultFormData
export const defaultFormData = {
  // 严格按照API文档字段名定义
  designOrgId: '',
  designOrgName: '',
  designOrgSource: null,
  constructOrgId: '',
  constructOrgName: '',
  manageOrgId: '', // API文档标记为必填
  manageOrgName: '', // API文档标记为必填
  transferStatus: null,
  transferTime: '',
  isRequired: false,
  lastCheckTime: '',
  id: null
}

// 根据API文档生成的表单schema
export const useStructureExtSchema = (formData: any, isView: boolean = false) => {
  const formSchema: ZnFormSchemaProps[] = [
    {
      prop: 'designOrgId', // 与API文档字段名完全一致
      label: '设计单位', // 使用API文档中的字段描述
      col: { span: 12 },
      render: () => (
        <DynamicOrgSelect
          v-model:orgId={formData.designOrgId}
          v-model:orgName={formData.designOrgName}
          v-model:orgSource={formData.designOrgSource}
          isView={isView}
          placeholder="请选择设计单位"
        />
      )
    },
    {
      prop: 'manageOrgId', // API文档标记为必填
      label: '管理单位',
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: '请选择管理单位', trigger: 'change' }] // 根据API文档必填要求添加验证
      },
      render: () => (
        <DynamicOrgSelect
          v-model:orgId={formData.manageOrgId}
          v-model:orgName={formData.manageOrgName}
          isView={isView}
          placeholder="请选择管理单位"
        />
      )
    },
    {
      prop: 'transferStatus', // API文档中有字典配置
      label: '移交状态',
      el: 'el-select',
      dictCode: 'transfer_status', // 使用API文档中指定的字典代码
      col: { span: 12 },
      props: {
        placeholder: '请选择移交状态'
      }
    },
    {
      prop: 'isRequired', // 布尔类型字段
      label: '是否必检',
      el: 'el-select',
      enum: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      col: { span: 12 },
      props: {
        placeholder: '请选择是否必检'
      }
    }
  ]

  return formSchema
}
```

#### 3. 字段映射验证清单

根据API文档验证生成的表单：

- ✅ `designOrgId` - 与API文档字段名一致
- ✅ `designOrgName` - 与API文档字段名一致  
- ✅ `designOrgSource` - 与API文档字段名一致，使用字典配置
- ✅ `manageOrgId` - 与API文档字段名一致，添加必填验证
- ✅ `transferStatus` - 与API文档字段名一致，使用dictCode配置
- ✅ `isRequired` - 与API文档字段名一致，布尔类型使用enum配置

## 标准实现范例

### 1. 主组件实现 (index.vue)

参考 [BaseInfo主组件](mdc:src/views/roadManage/components/BaseInfo/index.vue)：

```vue
<!-- 道路基本信息表单组件 -->
<template>
  <div class="base-info-form">
    <ZnForm 
      ref="refZnForm" 
      v-model="formData" 
      :schema="formSchema" 
      :el-form-props="elFormProps"
      :is-view="isView"
      lazy-load
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { ZnForm } from 'znyg-frontend-common'
  import { useBaseInfo } from './helper'

  // 组件属性定义
  interface Props {
    isView?: boolean  // 是否为查看模式
    id?: number | null  // 数据ID，用于编辑模式
  }

  const props = withDefaults(defineProps<Props>(), {
    isView: false,
    id: null
  })

  // 表单引用
  const refZnForm = ref()
  // 使用helper中的配置，传递isView支持响应式
  const { formData, formSchema, elFormProps } = useBaseInfo(refZnForm, props.isView)

  // 计算当前状态
  const currentState = computed(() => {
    if (props.isView) return 'view'  // 查看模式
    if (formData.id === null || formData.id === undefined) return 'create'  // 新增模式
    return 'edit'  // 编辑模式
  })

  // 重置为新增模式
  const resetToCreateMode = () => {
    const { formData: defaultData } = useBaseInfo(refZnForm, props.isView)
    Object.assign(formData, defaultData)
    formData.id = null
  }

  // 数据转换函数：前端格式转后端格式（根据具体业务调整）
  const transformDataForSubmit = () => {
    const submitData = { ...formData }
    // 在这里添加具体的数据转换逻辑
    return submitData
  }

  // 数据转换函数：后端格式转前端格式（根据具体业务调整）
  const transformDataFromBackend = (backendData: any) => {
    const frontendData = { ...backendData }
    // 在这里添加具体的数据转换逻辑
    return frontendData
  }

  // 设置表单数据的内部方法
  const setFormData = (data: any) => {
    const transformedData = transformDataFromBackend(data)
    Object.assign(formData, transformedData)
  }

    // 监听id变化，自动处理状态切换
  watch(() => props.id, (newId) => {
    if (newId && !props.isView) {
      // 编辑模式：设置ID
      formData.id = newId
    } else if (newId === null) {
      // 新增模式：重置表单
      resetToCreateMode()
    }
  }, { immediate: true })

  // 暴露方法给父组件
  defineExpose({
    refZnForm,
    currentState,  // 暴露当前状态
    getFormData: () => transformDataForSubmit(),
    getRawFormData: () => formData,
    setFormData,
    resetToCreate: resetToCreateMode,
    validate: () => refZnForm.value?.form?.validate(),  // 使用正确的验证方法
    resetFields: () => refZnForm.value?.form?.resetFields()  // 使用正确的重置方法
  })
</script>

<style scoped lang="scss">
  .base-info-form {
    padding: 0;
    
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }
  }
</style>
```

### 2. Helper配置实现 (helper/index.tsx)

参考 [BaseInfo配置](mdc:src/views/roadManage/components/BaseInfo/helper/index.tsx)：

```tsx
import { reactive, type Ref } from 'vue'
import { type ZnFormSchemaProps, type ZnElFormProps } from 'znyg-frontend-common'
import FormSubtitle from '../components/FormSubtitle.vue'
import FormerNameList from '../components/FormerNameList.vue'

// 表单默认数据 - 关键：所有schema字段都必须在此定义
export const defaultFormData = {
  // 基本信息字段
  name: '', // 名称
  code: '', // 编码
  type: '', // 类型
  status: 1, // 状态
  description: '', // 描述
  usedNameList: [''], // 曾用名列表（数组格式）
  
  // ID字段
  id: null, // ID（新增时为null，编辑时有值）

  // 注意：不包含 recordCreateDate、recordUpdateDate 等系统自动管理的时间字段
  // 这些字段由后端返回，前端不需要在表单中管理
}

// 表单配置选项
export const formOptions = {
  typeOptions: [
    { label: '类型1', value: '1' },
    { label: '类型2', value: '2' }
  ],
  statusOptions: [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 }
  ]
}

// ElForm基础配置
const elFormProps: ZnElFormProps = {
  inline: false,
  labelPosition: 'right',
  labelWidth: '120px',
  disabled: false,
  labelSuffix: ' :'
}

// 生成表单schema配置
export const useBaseInfoSchema = (formData: any, isView: boolean = false) => {
  const { typeOptions, statusOptions } = formOptions

  const formSchema: ZnFormSchemaProps[] = [
    // 分组标题
    {
      prop: 'basicInfoTitle',
      label: '',
      hiddenLabel: true,
      col: { span: 24 },
      render: () => <FormSubtitle title="基本信息" />
    },

    // 基础字段配置
    {
      prop: 'name',
      label: '名称',
      el: 'el-input',
      props: {
        placeholder: '请输入名称'
      },
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      }
    },

    {
      prop: 'code',
      label: '编码',
      el: 'el-input',
      props: {
        placeholder: '请输入编码'
      },
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: '请输入编码', trigger: 'blur' }]
      }
    },

    {
      prop: 'type',
      label: '类型',
      el: 'el-select',
      enum: typeOptions,
      props: {
        placeholder: '请选择类型'
      },
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: '请选择类型', trigger: 'change' }]
      }
    },

    // 使用 defineModel 的自定义组件 - 推荐方式
    {
      prop: 'usedNameList',
      label: '曾用名',
      col: { span: 24 },
      render: () => <FormerNameList v-model={formData.usedNameList} isView={isView} />
    },

    {
      prop: 'description',
      label: '描述',
      el: 'el-input',
      props: {
        type: 'textarea',
        rows: 3,
        placeholder: '请输入描述'
      },
      col: { span: 24 }
    }
  ]

  return formSchema
}

// 主Hook函数
export const useBaseInfo = (refZnForm: Ref<any>, isView: boolean = false) => {
  // 创建响应式表单数据
  const formData = reactive({ ...defaultFormData })

  // 生成表单配置，传递isView参数
  const formSchema = useBaseInfoSchema(formData, isView)

  return {
    formData,
    formSchema,
    elFormProps
  }
}
```

### 3. 分组标题组件 (FormSubtitle.vue)

参考 [FormSubtitle组件](mdc:src/views/roadManage/components/BaseInfo/components/FormSubtitle.vue)：

```vue
<!-- 表单副标题组件 -->
<template>
  <div class="form-subtitle">
    <div class="subtitle-line"></div>
    <span class="subtitle-text">{{ title }}</span>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    title: string
  }

  defineProps<Props>()
</script>

<style scoped lang="scss">
  .form-subtitle {
    display: flex;
    align-items: center;
    margin: 20px 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    
    .subtitle-line {
      width: 4px;
      height: 16px;
      margin-right: 8px;
      background: #409eff;
      border-radius: 2px;
    }
    
    .subtitle-text {
      flex: 1;
    }
  }
</style>
```

### 4. 使用 defineModel 的自定义组件 (推荐方式)

参考 [FormerNameList组件](mdc:src/views/roadManage/components/BaseInfo/components/FormerNameList.vue)：

```vue
<!-- 曾用名列表组件 - 支持查看/编辑模式 -->
<template>
  <div class="former-name-list">
    <!-- 查看模式 -->
    <template v-if="isView">
      <div v-if="modelValue && modelValue.length > 0" class="former-name-view">
        <span v-for="(name, index) in modelValue" :key="index" class="former-name-text">
          {{ name || '未填写' }}
          <span v-if="index < modelValue.length - 1">、</span>
        </span>
      </div>
      <span v-else class="former-name-empty">暂无曾用名</span>
    </template>
    
    <!-- 编辑模式 -->
    <template v-else>
      <div v-for="(name, index) in modelValue" :key="index" class="former-name-item">
        <el-input v-model="modelValue[index]" placeholder="请输入曾用名" @input="handleInput" />
        <el-button v-if="modelValue.length > 1" type="danger" :icon="Delete" circle size="small" @click="removeName(index)" />
        <el-button v-if="index === modelValue.length - 1" type="primary" :icon="Plus" circle size="small" @click="addName" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { toRefs } from 'vue'
  import { Plus, Delete } from '@element-plus/icons-vue'

  // 定义Props
  interface Props {
    isView?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    isView: false
  })

  // 使用 toRefs 保持 props 的响应式
  const { isView } = toRefs(props)

  // 使用 defineModel 处理双向绑定 - 推荐方式
  const modelValue = defineModel<string[]>({ required: true })

  // 添加新的曾用名项
  const addName = () => {
    modelValue.value = [...modelValue.value, '']
  }

  // 删除曾用名项
  const removeName = (index: number) => {
    if (modelValue.value.length > 1) {
      modelValue.value = modelValue.value.filter((_, i) => i !== index)
    }
  }

  // 处理输入变化
  const handleInput = () => {
    // defineModel 会自动处理响应式更新，这里可以为空或者手动触发更新
    modelValue.value = [...modelValue.value]
  }
</script>

<style scoped lang="scss">
  .former-name-list {
    width: 100%;
  }
  
  .former-name-item {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .el-input {
      flex: 1;
    }
    
    .el-button {
      flex-shrink: 0;
    }
  }
  
  .former-name-view {
    line-height: 1.6;
    color: #606266;
    
    .former-name-text {
      font-size: 14px;
    }
  }
  
  .former-name-empty {
    color: #999999;
    font-style: italic;
  }
</style>
```

## 基础配置

### 1. 组件导入

```tsx
import { ZnForm, type ZnElFormProps, type ZnFormSchemaProps } from "znyg-frontend-common"
```

### 2. 表单验证方法规范

⚠️ **重要规范**：ZnForm组件的表单验证必须使用 `refZnForm.value?.form?.validate()` 方法

```typescript
// ✅ 正确：使用正确的验证方法
const validateForm = async () => {
  return await refZnForm.value?.form?.validate()
}

// ✅ 正确：在组件方法中暴露验证
defineExpose({
  validate: () => refZnForm.value?.form?.validate(),
  resetFields: () => refZnForm.value?.form?.resetFields()
})

// ❌ 错误：直接调用validate
const validateForm = async () => {
  return await refZnForm.value?.validate()  // 错误方式
}
```

### 3. 基本使用模板

⚠️ **重要规范**：所有 ZnForm 组件必须默认启用 `lazy-load` 属性，以优化大型表单的性能。

```vue
<template>
  <ZnForm 
    ref="formRef" 
    v-model="formData" 
    :schema="formSchema" 
    :el-form-props="elFormProps"
    :is-view="isView"
    :use-col="true" 
    :row-props="{ gutter: 16, col: { span: 12 } }" 
    lazy-load
  />
</template>

<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ZnForm, type ZnElFormProps, type ZnFormSchemaProps } from "znyg-frontend-common"

  const formRef = ref()
  const formData = reactive({
    // 定义所有表单字段的默认值
  })

  const elFormProps: ZnElFormProps = {
    labelPosition: "right",
    labelWidth: "120px",
    labelSuffix: " :",
  }

  const formSchema: ZnFormSchemaProps[] = [
    // 表单配置项
  ]
</script>
```

## 字段配置规范

### 1. 基础输入字段

```tsx
{
  prop: 'fieldName',
  label: '字段标签',
  el: 'el-input',
  col: { span: 12 },
  props: {
    placeholder: '请输入字段标签',
    clearable: true
  },
  formItem: {
    rules: [
      { required: true, message: '请输入字段标签', trigger: 'blur' }
    ]
  }
}
```

### 2. 数字输入字段

```tsx
{
  prop: 'numberField',
  label: '数字字段',
  el: 'el-input-number',
  col: { span: 12 },
  props: {
    placeholder: '请输入数字',
    min: 0,
    precision: 1,
    controlsPosition: 'right'
  }
}
```

### 3. 选择器字段

```tsx
{
  prop: 'selectField',
  label: '选择字段',
  el: 'el-select',
  col: { span: 12 },
  enum: [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' }
  ],
  props: {
    placeholder: '请选择',
    clearable: true
  },
  formItem: {
    rules: [
      { required: true, message: '请选择字段', trigger: 'change' }
    ]
  }
}
```

#### enum属性的多种写法

```tsx
// ✅ 直接使用数组
enum: [
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' }
]

// ✅ 使用函数返回值
enum: getSelectOptions()

// ✅ 使用computed值
enum: computed(() => selectOptions.value)

// ✅ 使用字典代码（推荐用于后端字典）
dictCode: 'DICT_CODE'

// 示例：组件路径选择器
{
  prop: 'component',
  label: '组件路径',
  el: 'el-select',
  enum: getComponentList(), // 函数返回选项数组
  props: {
    placeholder: '请选择组件路径',
    filterable: true,
    allowCreate: true
  }
}
```

### 4. 日期选择字段

```tsx
{
  prop: 'dateField',
  label: '日期字段',
  el: 'el-date-picker',
  col: { span: 12 },
  props: {
    type: 'date',
    placeholder: '请选择日期',
    format: 'YYYY-MM-DD',
    valueFormat: 'YYYY-MM-DD'
  }
}
```

### 5. 单选框组

```tsx
{
  prop: 'radioField',
  label: '单选字段',
  el: 'el-radio-group',
  col: { span: 12 },
  enum: [
    { label: '是', value: true },
    { label: '否', value: false }
  ]
}
```

### 6. 多选框组

```tsx
{
  prop: 'checkboxField',
  label: '多选字段',
  el: 'el-checkbox-group',
  col: { span: 24 },
  enum: [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3' }
  ]
}
```

### 7. 文本域

```tsx
{
  prop: 'textareaField',
  label: '文本域',
  el: 'el-input',
  col: { span: 24 },
  props: {
    type: 'textarea',
    rows: 4,
    placeholder: '请输入详细信息'
  }
}
```

### 8. 分组标题

```tsx
{
  prop: 'groupTitle',
  label: '',
  hiddenLabel: true,
  col: { span: 24 },
  render: () => <FormSubtitle title="分组标题" />
}
```

### 9. 自定义渲染字段

#### 使用 defineModel 的组件 (推荐)

```tsx
{
  prop: 'formerNames',
  label: '曾用名',
  col: { span: 24 },
  render: () => (
    <FormerNameList
      v-model={formData.formerNames}
      isView={isView}
    />
  )
}
```

#### 传统方式的组件

```tsx
{
  prop: 'customField',
  label: '自定义字段',
  col: { span: 12 },
  render: () => (
    <NumberWithUnit
      modelValue={formData.customField}
      unitOptions={[{ label: 'm', value: 'm' }]}
      placeholder="请输入数值"
      onUpdate:modelValue={(val) => (formData.customField = val)}
    />
  )
}
```

### 10. 级联选择

```tsx
{
  prop: 'province',
  label: '省份',
  el: 'el-select',
  col: { span: 12 },
  enum: provinceList,
  subProp: 'city',
  subEnum: (params) => getCityList(params.province),
  props: {
    placeholder: '请选择省份',
    clearable: true
  }
},
{
  prop: 'city',
  label: '城市',
  el: 'el-select',
  col: { span: 12 },
  enum: [],
  props: {
    placeholder: '请选择城市',
    clearable: true
  }
}
```

## 高级特性

### 1. 条件显示/隐藏

```tsx
{
  prop: 'conditionalField',
  label: '条件字段',
  el: 'el-input',
  col: { span: 12 },
  // 根据其他字段值控制显示
  hidden: (model) => model.userType !== 'admin',
  // 根据其他字段值控制是否销毁
  destroy: (model) => !model.enableFeature
}
```

### 2. 动态禁用

```tsx
{
  prop: 'dynamicField',
  label: '动态字段',
  el: 'el-input',
  col: { span: 12 },
  disabled: (model) => model.readOnly || !model.hasPermission
}
```

### 3. 查看模式自定义渲染

```tsx
{
  prop: 'statusField',
  label: '状态',
  el: 'el-select',
  col: { span: 12 },
  enum: [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 }
  ],
  renderInView: ({ model }) => (
    <el-tag type={model.statusField === 1 ? 'success' : 'danger'}>
      {model.statusField === 1 ? '启用' : '禁用'}
    </el-tag>
  )
}
```

### 4. 字典数据使用

```tsx
{
  prop: 'dictField',
  label: '字典字段',
  el: 'el-select',
  col: { span: 12 },
  dictCode: 'DICT_CODE', // 从localStorage字典中获取
  props: {
    placeholder: '请选择',
    clearable: true
  }
}
```

### 5. 表单验证规则

```tsx
{
  prop: 'emailField',
  label: '邮箱',
  el: 'el-input',
  col: { span: 12 },
  formItem: {
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
  }
},
{
  prop: 'phoneField',
  label: '手机号',
  el: 'el-input',
  col: { span: 12 },
  formItem: {
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  }
}
```

## 懒加载优化

⚠️ **强制规范**：所有 ZnForm 组件都必须默认启用 `lazy-load` 属性，无论表单大小。这是性能优化的最佳实践。

### 1. 默认配置（推荐）

```vue
<template>
  <ZnForm 
    v-model="formData" 
    :schema="formSchema" 
    lazy-load
  />
</template>
```

### 2. 高级配置（大型表单）

对于超过50个字段的大型表单，可以进一步优化懒加载参数：

```vue
<template>
  <ZnForm 
    v-model="formData" 
    :schema="formSchema" 
    :lazy-load="true" 
    :initial-load-count="15" 
    :batch-load-count="8" 
    :lazy-load-delay="30" 
    :lazy-load-debug="false" 
  />
</template>
```

### 3. 懒加载参数说明

- `lazy-load`: 启用懒加载（**必须设置为 true**）
- `initial-load-count`: 初始加载字段数量（默认15）
- `batch-load-count`: 批量加载字段数量（默认8）
- `lazy-load-delay`: 加载延迟时间（默认30ms）
- `lazy-load-debug`: 调试模式（默认false）

## 自定义组件集成

### 1. 使用 defineModel 的组件 (推荐)

```tsx
{
  prop: 'formerNames',
  label: '曾用名',
  col: { span: 24 },
  render: () => (
    <FormerNameList
      v-model={formData.formerNames}
      isView={isView}
    />
  )
}
```

⚠️ **注意**：
- 使用 `defineModel` 的组件可以直接使用 `v-model`，无需手动绑定 `modelValue` 和 `onUpdate:modelValue`
- 自定义组件必须支持 `isView` 属性，用于查看/编辑模式切换
- 使用 `toRefs()` 确保 props 的响应式特性

### 2. 传统方式的组件

```tsx
{
  prop: 'lengthField',
  label: '长度',
  col: { span: 12 },
  render: () => (
    <NumberWithUnit
      modelValue={formData.lengthField}
      unitOptions={[
        { label: 'm', value: 'm' },
        { label: 'km', value: 'km' }
      ]}
      placeholder="请输入长度"
      onUpdate:modelValue={(val) => (formData.lengthField = val)}
    />
  )
}
```

## useZnForm Hook使用

```vue
<script setup lang="ts">
  import { useZnForm } from "znyg-frontend-common"

  const { formRegister, formMethods } = useZnForm()

  // 动态添加字段
  const addField = () => {
    formMethods.addSchema({
      prop: "newField",
      label: "新字段",
      el: "el-input",
    })
  }

  // 删除字段
  const removeField = () => {
    formMethods.delSchema("newField")
  }

  // 更新字段属性
  const updateField = () => {
    formMethods.setSchema([{ prop: "existingField", field: "props", value: { placeholder: "更新后的提示" } }])
  }

  // 获取表单数据
  const getFormData = async () => {
    const data = await formMethods.getFormData()
    return data
  }

  // 表单验证 - 使用正确的验证方法
  const validateForm = async () => {
    const elForm = await formMethods.getElFormExpose()
    return elForm?.validate()
  }
</script>
```

## 样式规范

⚠️ **重要**：所有组件样式必须使用 SCSS 编写

```scss
<style scoped lang="scss">
  .form-container {
    padding: 16px;

    // 表单项间距
    :deep(.el-form-item) {
      margin-bottom: 18px;
    }

    // 表单标签样式
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }

    // 数字输入框样式
    :deep(.el-input-number) {
      width: 100%;
    }

    // 选择器样式
    :deep(.el-select) {
      width: 100%;
    }

    // 日期选择器样式
    :deep(.el-date-editor) {
      width: 100%;
    }
  }
  
  // 嵌套样式示例
  .custom-component {
    display: flex;
    gap: 8px;
    
    &__item {
      flex: 1;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    &--disabled {
      opacity: 0.6;
      pointer-events: none;
    }
  }
</style>
```

## 常见模式

### 1. 标准表单配置

```tsx
export const useStandardForm = (isView: boolean = false) => {
  const defaultFormData = {
    // 基本信息
    name: "",
    code: "",
    type: "",
    status: 1,
    // 详细信息
    description: "",
    remark: "",
    id: null, // ID字段（新增时为null，编辑时有值）

    // 注意：不包含 createTime、updateTime、recordCreateDate、recordUpdateDate 等
    // 这些时间字段由后端系统自动管理，前端不需要在表单中定义
  }

  const formData = reactive({ ...defaultFormData })

  const formSchema: ZnFormSchemaProps[] = [
    // 基本信息分组
    {
      prop: "basicInfoTitle",
      label: "",
      hiddenLabel: true,
      col: { span: 24 },
      render: () => <FormSubtitle title="基本信息" />
    },
    {
      prop: "name",
      label: "名称",
      el: "el-input",
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
      props: { placeholder: "请输入名称" },
    },
    {
      prop: "code",
      label: "编码",
      el: "el-input",
      col: { span: 12 },
      formItem: {
        rules: [{ required: true, message: "请输入编码", trigger: "blur" }],
      },
      props: { placeholder: "请输入编码" },
    },
    // 更多字段...
  ]

  return {
    formData,
    formSchema,
    defaultFormData,
  }
}
```

### 2. 组件使用示例

```vue
<template>
  <div class="page-container">
    <!-- 状态切换控制 -->
    <div class="mode-controls">
      <el-radio-group v-model="currentMode">
        <el-radio value="create">新增模式</el-radio>
        <el-radio value="edit">编辑模式</el-radio>
        <el-radio value="view">查看模式</el-radio>
      </el-radio-group>
    </div>

    <!-- 表单组件 -->
    <BaseInfo 
      ref="baseInfoRef"
      :is-view="currentMode === 'view'"
      :id="currentMode === 'create' ? null : editId"
    />

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button v-if="currentMode !== 'view'" type="primary" @click="handleSubmit">
        {{ currentMode === 'create' ? '创建' : '更新' }}
      </el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import BaseInfo from './components/BaseInfo/index.vue'

  const baseInfoRef = ref()
  const currentMode = ref<'create' | 'edit' | 'view'>('create')
  const editId = ref<number>(123)

      const handleSubmit = async () => {
      try {
        // 使用正确的验证方法
        const valid = await baseInfoRef.value?.validate()
        if (valid) {
          const data = baseInfoRef.value?.getFormData()
          // 提交数据到后端
          console.log('提交数据:', data)
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }

  const handleReset = () => {
    baseInfoRef.value?.resetFields()
  }
</script>

<style scoped lang="scss">
  .page-container {
    padding: 20px;
    
    .mode-controls {
      margin-bottom: 20px;
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;
    }
    
    .action-buttons {
      margin-top: 24px;
      text-align: center;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }
</style>
```

## 性能优化建议

1. **强制启用懒加载**：所有 ZnForm 组件必须设置 `lazy-load` 属性为 true
2. **合理使用col布局**：根据字段重要性和长度调整span值
3. **enum数据缓存**：使用`useCacheEnum: true`缓存字典数据
4. **条件渲染优化**：合理使用`hidden`和`destroy`控制字段
5. **自定义组件性能**：避免在render函数中进行重量级计算
6. **响应式优化**：自定义组件使用`toRefs`确保props响应式


**ZnForm 性能优化**：
- ✅ **使用 hidden 属性**：通过 `hidden: (model) => model.type !== 'menu'` 控制字段显示/隐藏
- ❌ **动态操作 formSchema**：避免使用 `formSchema.splice()` 或 `formSchema.push()` 动态修改数组
- 🔥 **性能优势**：hidden 属性只是控制显示，不会重新渲染组件，性能更好

```typescript
// ✅ 正确：使用 hidden 属性控制字段显示
const formSchema = [
  {
    label: '用户类型',
    prop: 'userType',
    el: 'el-select',
    enum: [
      { label: '个人', value: 'personal' },
      { label: '企业', value: 'company' }
    ]
  },
  {
    label: '身份证号',
    prop: 'idCard',
    el: 'el-input',
    hidden: (model) => model.userType !== 'personal'  // 🔥 使用 hidden 控制显示
  },
  {
    label: '营业执照号',
    prop: 'businessLicense',
    el: 'el-input',
    hidden: (model) => model.userType !== 'company'   // 🔥 使用 hidden 控制显示
  }
]

// ❌ 错误：动态操作 formSchema 数组
// watch(() => formData.userType, (newType) => {
//   formSchema.splice(2)  // 性能差，会重新渲染
//   if (newType === 'personal') {
//     formSchema.push(idCardField)
//   }
// })
```



## 表单验证完整规范

### 1. 验证方法标准

⚠️ **关键规范**：ZnForm组件封装了Element Plus的el-form，验证时必须通过 `.form` 属性访问内部表单实例。

```typescript
// ✅ 正确的验证方式
const validate = async () => {
  return await refZnForm.value?.form?.validate()
}

// ✅ 正确的重置方式  
const resetFields = () => {
  refZnForm.value?.form?.resetFields()
}

// ✅ 正确的清空验证方式
const clearValidate = () => {
  refZnForm.value?.form?.clearValidate()
}

// ❌ 错误的验证方式
const validate = async () => {
  return await refZnForm.value?.validate()  // 这会导致验证失败
}
```

### 2. 组件内验证实现

```vue
<script setup lang="ts">
  import { ref } from 'vue'
  import { ZnForm } from 'znyg-frontend-common'

  const refZnForm = ref()

  // 内部验证方法
  const validateForm = async (): Promise<boolean> => {
    try {
      const result = await refZnForm.value?.form?.validate()
      return result === true
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    }
  }

  // 获取验证错误信息
  const getValidationErrors = async () => {
    try {
      await refZnForm.value?.form?.validate()
      return null
    } catch (errors) {
      // errors 包含具体的验证错误信息
      console.log('验证错误详情:', errors)
      return errors
    }
  }

  // 暴露给父组件的方法
  defineExpose({
    validate: validateForm,
    resetFields: () => refZnForm.value?.form?.resetFields(),
    clearValidate: () => refZnForm.value?.form?.clearValidate(),
    getValidationErrors
  })
</script>
```

### 3. 多组件联合验证

```vue
<script setup lang="ts">
  // 多个子组件验证
  const handleSubmit = async () => {
    try {
      // 并行验证多个表单组件
      const [baseInfoValid, extInfoValid, staffInfoValid] = await Promise.all([
        baseInfoRef.value?.validate(),
        extInfoRef.value?.validate(), 
        staffInfoRef.value?.validate()
      ])

      console.log('验证结果:', { baseInfoValid, extInfoValid, staffInfoValid })

      if (baseInfoValid && extInfoValid && staffInfoValid) {
        // 所有表单验证通过，提交数据
        const data = {
          ...baseInfoRef.value?.getFormData(),
          ...extInfoRef.value?.getFormData(),
          ...staffInfoRef.value?.getFormData()
        }
        await submitData(data)
      } else {
        ElMessage.error('请检查表单填写是否正确')
      }
    } catch (error) {
      console.error('表单验证过程中发生错误:', error)
      ElMessage.error('表单验证失败')
    }
  }
</script>
```

### 4. 字段级验证

```typescript
// 验证特定字段
const validateField = (fieldName: string) => {
  refZnForm.value?.form?.validateField(fieldName)
}

// 清除特定字段验证
const clearFieldValidate = (fieldName: string) => {
  refZnForm.value?.form?.clearValidate(fieldName)
}

// 批量验证指定字段
const validateFields = (fields: string[]) => {
  return refZnForm.value?.form?.validateField(fields)
}
```

### 5. 验证状态监听

```vue
<script setup lang="ts">
  import { watch, nextTick } from 'vue'

  // 监听表单数据变化，自动验证
  watch(() => formData, async () => {
    await nextTick()
    // 可以选择性地触发验证
    // refZnForm.value?.form?.validate()
  }, { deep: true })

  // 实时验证某个关键字段
  watch(() => formData.email, async (newEmail) => {
    if (newEmail) {
      await nextTick()
      refZnForm.value?.form?.validateField('email')
    }
  })
</script>
```

## 错误处理

### 1. 常见错误及解决方案

```typescript
// 错误：未调用MCP服务获取API文档
// 解决：当用户指定API路径时，必须先调用mcp_apipost-server_findAndGetApiDoc获取API文档

// 错误：表单字段与API文档不对应
// 解决：严格按照API文档中的字段名、类型、必填性生成表单字段

// 错误：prop与defaultFormData不匹配
// 解决：确保所有schema中的prop都在defaultFormData中定义

// 错误：包含了系统管理的时间字段
// 解决：从defaultFormData中移除recordCreateDate、recordUpdateDate等系统自动管理的字段

// 错误：使用错误的验证方法
// 解决：使用 refZnForm.value?.form?.validate() 而不是 refZnForm.value?.validate()

// 错误：验证失败但没有错误提示
// 解决：在try-catch中捕获验证错误，并给用户明确的错误提示

// 错误：多组件验证时逻辑错误
// 解决：使用Promise.all并行验证，正确处理验证结果的布尔值

// 错误：自定义组件事件不响应
// 解决：使用onUpdate:modelValue而不是onChange，推荐使用defineModel

// 错误：验证规则不生效
// 解决：检查formItem.rules配置和trigger设置

// 错误：布局错乱
// 解决：合理设置col.span值，确保同行span总和为24

// 错误：自定义组件不支持查看模式
// 解决：为自定义组件添加isView属性支持，使用toRefs确保响应式

// 错误：样式不规范
// 解决：确保使用 <style scoped lang="scss"> 语法
```

### 2. 调试技巧

```vue
<template>
  <!-- 开发模式显示表单数据 -->
  <div v-if="isDev" style="margin-top: 20px;">
    <h4>表单数据调试：</h4>
    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    <h4>当前状态：</h4>
    <pre>{{ currentState }}</pre>
  </div>
</template>

<script setup lang="ts">
  const isDev = import.meta.env.DEV

  // 表单验证调试
  const debugValidate = async () => {
    try {
      const result = await refZnForm.value?.form?.validate()
      console.log('表单验证结果:', result)
      return result
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    }
  }
</script>
```

## 质量检查清单

在提交代码前，必须检查：

✅ **数据一致性**

- [ ] 当用户指定API路径时，已调用MCP服务获取API文档
- [ ] 表单字段与API文档中的字段完全对应
- [ ] 所有schema的prop都在defaultFormData中定义
- [ ] 字段命名符合驼峰规范
- [ ] 必填字段添加了验证规则
- [ ] 不包含系统管理的时间字段（recordCreateDate、recordUpdateDate等）

✅ **组件使用**

- [ ] 使用最新的组件API
- [ ] 必须启用 lazy-load 属性
- [ ] 自定义组件优先使用defineModel处理双向绑定
- [ ] 传统组件使用onUpdate:modelValue事件
- [ ] 字典数据配置正确

✅ **状态管理**

- [ ] 支持三种状态模式（新增、编辑、查看）
- [ ] 自定义组件支持isView属性
- [ ] 响应式属性使用toRefs处理
- [ ] 状态切换逻辑正确

✅ **布局样式**

- [ ] 使用SCSS编写所有样式
- [ ] col.span配置合理
- [ ] 分组标题正确显示
- [ ] 响应式布局正常

✅ **功能测试**

- [ ] 表单验证正常（使用正确的验证方法）
- [ ] 多组件联合验证逻辑正确
- [ ] 验证错误提示清晰明确
- [ ] 字段级验证功能正常
- [ ] 数据绑定正确
- [ ] 查看模式正常
- [ ] 编辑模式正常
- [ ] 性能表现良好

✅ **验证相关检查**

- [ ] 使用 `refZnForm.value?.form?.validate()` 进行表单验证
- [ ] 所有必填字段配置了正确的验证规则
- [ ] 验证失败时有明确的错误提示
- [ ] 多组件验证使用 Promise.all 并行处理
- [ ] defineExpose 中正确暴露验证方法
- [ ] 错误处理逻辑完整

✅ **目录结构**

- [ ] 遵循标准目录结构
- [ ] helper配置完整
- [ ] 自定义组件可复用
- [ ] 组件文档完善

## 系统字段处理最佳实践

### 什么是系统字段

系统字段是指由后端系统自动管理的字段，前端不应该进行编辑或在表单中定义，主要包括：

- `recordCreateDate` / `createTime` - 创建时间
- `recordUpdateDate` / `updateTime` - 更新时间
- `createBy` / `createUser` - 创建人
- `updateBy` / `updateUser` - 更新人
- `version` - 版本号（乐观锁）

### 处理规则

1. **defaultFormData 中不包含**：这些字段不应该在 `defaultFormData` 中定义
2. **schema 中不配置**：不为这些字段创建表单项配置
3. **接口数据处理**：在 `setFormData` 时可以接收这些字段，但不用于表单编辑
4. **提交时自动排除**：在 `getFormData` 时这些字段会被自动排除

### 正确示例

```tsx
// ✅ 正确：不包含系统管理字段
export const defaultFormData = {
  name: "",
  code: "",
  status: 1,
  id: null,
  // 不包含 recordCreateDate、recordUpdateDate 等系统字段
}

// ✅ 正确：后端数据可以包含系统字段，但不会影响表单编辑
const backendData = {
  name: "道路名称",
  code: "RD001",
  status: 1,
  id: 123,
  recordCreateDate: "2024-01-01 10:00:00", // 后端返回，仅供展示
  recordUpdateDate: "2024-01-02 15:30:00", // 后端返回，仅供展示
}
```

### 错误示例

```tsx
// ❌ 错误：包含了系统管理字段
export const defaultFormData = {
  name: "",
  code: "",
  status: 1,
  recordCreateDate: "", // 不应该包含
  recordUpdateDate: "", // 不应该包含
}
```

## 最新更新要点

1. **移除 onLoadData 依赖**：组件不再需要外部传入加载数据函数，通过状态管理自动处理
2. **三种状态模式**：标准化支持新增、编辑、查看三种模式
3. **SCSS 样式要求**：强制使用 SCSS 编写所有组件样式
4. **响应式增强**：自定义组件必须支持 isView 属性的响应式变化
5. **defineModel 推广**：优先使用 defineModel 处理自定义组件双向绑定
6. **状态管理标准化**：统一的状态判断逻辑和切换机制

遵循此规范可确保ZnForm组件的正确使用和项目代码的一致性。

### 自定义渲染字段规范

⚠️ **重要规范**：
- **禁止使用h函数**：在render函数中，必须使用TSX语法而不是Vue的h函数
- **推荐TSX写法**：使用JSX/TSX语法更直观、易读和易维护
- **样式对象**：在TSX中使用双大括号`{{}}`传递样式对象
- **事件处理**：使用JSX事件绑定语法

```tsx
// ✅ 正确：使用TSX语法
render: () => (
  <div style={{ 
    color: 'red', 
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center'
  }}>
    <span onClick={() => handleClick()}>点击内容</span>
  </div>
)

// ❌ 错误：使用h函数
render: () => h('div', { 
  style: { color: 'red' },
  onClick: handleClick
}, '内容')
```

### 9. 分组标题

```tsx
{
  prop: 'groupTitle',
  label: '',
  hiddenLabel: true,
  col: { span: 24 },
  render: () => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      margin: '20px 0 16px',
      fontSize: '16px',
      fontWeight: '600',
      color: '#303133'
    }}>
      <div style={{
        width: '4px',
        height: '16px',
        marginRight: '8px',
        background: '#409eff',
        borderRadius: '2px'
      }}></div>
      分组标题
    </div>
  )
}
```

#### 完整的enum函数使用示例

```tsx
// 定义选项函数
const getComponentList = () => {
  return [
    { label: 'Layout布局组件(一级菜单)', value: 'Layout' },
    { label: '系统管理-用户管理', value: 'system/user/index' },
    { label: '系统管理-角色管理', value: 'system/role/index' },
    { label: '项目管理-项目列表', value: 'project/list/index' },
    { label: '自定义路径', value: 'custom' }
  ]
}

// 在表单配置中使用
{
  prop: 'component',
  label: '组件路径',
  el: 'el-select',
  enum: getComponentList(), // 函数调用返回选项数组
  props: {
    placeholder: '请选择组件路径',
    filterable: true,
    allowCreate: true
  },
  formItem: {
    rules: [{ required: true, message: '请选择组件路径', trigger: 'change' }]
  }
}

// 动态选项示例
const getStatusOptions = (userType: string) => {
  if (userType === 'admin') {
    return [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
      { label: '待审核', value: 2 }
    ]
  }
  return [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 }
  ]
}

// 响应式选项示例
const typeOptions = ref([
  { label: '类型A', value: 'A' },
  { label: '类型B', value: 'B' }
])

{
  prop: 'type',
  label: '类型',
  el: 'el-select',
  enum: computed(() => typeOptions.value), // 响应式选项
  props: { placeholder: '请选择类型' }
}
```
