define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4"],(function(e,t,i,a,o){"use strict";function r(e){this._ellipsoid=t.defaultValue(e,o.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(r.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),r.mercatorAngleToGeodeticLatitude=function(e){return a.CesiumMath.PI_OVER_TWO-2*Math.atan(Math.exp(-e))},r.geodeticLatitudeToMercatorAngle=function(e){e>r.MaximumLatitude?e=r.MaximumLatitude:e<-r.MaximumLatitude&&(e=-r.MaximumLatitude);var t=Math.sin(e);return.5*Math.log((1+t)/(1-t))},r.MaximumLatitude=r.mercatorAngleToGeodeticLatitude(Math.PI),r.prototype.project=function(e,i){var o=this._semimajorAxis,n=e.longitude*o,u=r.geodeticLatitudeToMercatorAngle(e.latitude)*o,d=e.height;return t.defined(i)?(i.x=n,i.y=u,i.z=d,i):new a.Cartesian3(n,u,d)},r.prototype.unproject=function(e,a){if(!t.defined(e))throw new i.DeveloperError("cartesian is required");var n=this._oneOverSemimajorAxis,u=e.x*n,d=r.mercatorAngleToGeodeticLatitude(e.y*n),s=e.z;return t.defined(a)?(a.longitude=u,a.latitude=d,a.height=s,a):new o.Cartographic(u,d,s)},e.WebMercatorProjection=r}));