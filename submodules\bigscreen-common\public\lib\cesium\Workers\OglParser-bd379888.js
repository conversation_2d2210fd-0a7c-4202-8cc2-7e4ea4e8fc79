/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Geometry-a94d02e6","./ComponentDatatype-dad47320","./defined-3b3eb2ba"],(function(t,e,n,r){"use strict";function o(t){this.buffer=t,this.int32Array=new Uint32Array(t),this.offset=0}o.prototype.getUint32=function(){const t=this.int32Array[this.offset/4];return this.offset+=4,t},o.prototype.getUint16Array=function(t){const e=new Uint16Array(this.buffer,this.offset,t);return this.offset+=Uint16Array.BYTES_PER_ELEMENT*t,e},o.prototype.getUint32Array=function(t){const e=new Uint32Array(this.buffer,this.offset,t);return this.offset+=Uint32Array.BYTES_PER_ELEMENT*t,e},o.prototype.getFloat32Array=function(t){const e=new Float32Array(this.buffer,this.offset,t);return this.offset+=Float32Array.BYTES_PER_ELEMENT*t,e};const i={};function s(t,e,n){let r=0;r+=4,r+=4,r+=3*t*4,r+=4,r+=3*t*4,r+=4,n&&(r+=2*t*4);let o=!1;return t<65536&&(o=!0),r+=4,r+=4,r+=o?2*e:4*e,o&&e%2==1&&(r+=2),r+=4,r+=4,r+=4,r+=4,new ArrayBuffer(r)}function a(t,e,n,r,o,i){let s=-1;i=i||{};const a=e.start,u=e.count;let f;for(let e=0;e<u;e++)if(f=t[a+e],void 0===i[f]){i[f]=++s;for(let t=0;t<o;t++)r[s*o+t]=n[f*o+t]}}function u(t,e,n,o,i){const s=t.groups[e],u=s.start,f=s.materialIndex,p=t.attributes,c=t.indices,l=new Uint32Array(i);let y=0;y+=4,l[y/4]=3*n,y+=4;const A={};let g=new Float32Array(i,y,3*n);a(c,s,p.position.values,g,3,A),y+=3*n*4,l[y/4]=3*n,y+=4,g=new Float32Array(i,y,3*n),a(c,s,p.normal.values,g,3),y+=3*n*4,l[y/4]=r.defined(p.st)?2*n:0,y+=4,r.defined(p.st)&&(g=new Float32Array(i,y,2*n),a(c,s,p.st.values,g,2),y+=2*n*4);let d=!1;l[y/4]=1,n<65536&&(l[y/4]=0,d=!0),y+=4,l[y/4]=o,y+=4,g=d?new Uint16Array(i,y,o):new Uint32Array(i,y,o);for(let t=0;t<o;t++)g[t]=A[c[u+t]];return y+=d?2*o:4*o,d&&o%2==1&&(y+=2),l[y/4]=1,y+=4,l[y/4]=0,y+=4,l[y/4]=o,y+=4,l[y/4]=f,y+=4,i}i.parseGeometry=function(t){const r={attributes:{position:void 0,normal:void 0,st:void 0},indices:void 0,groups:[],primitiveType:e.PrimitiveType.TRIANGLES},i=r.attributes,s=new o(t);s.getUint32(),i.position=new e.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:s.getFloat32Array(s.getUint32())}),i.normal=new e.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:s.getFloat32Array(s.getUint32())});const a=s.getUint32();a&&(i.st=new e.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:s.getFloat32Array(a)}));const u=0===s.getUint32(),f=s.getUint32();let p;p=u?s.getUint16Array(f):s.getUint32Array(f),r.indices=p,u&&f%2&&(s.offset+=2);const c=r.groups;for(let t=0,e=s.getUint32();t<e;t++)c.push({start:s.getUint32(),count:s.getUint32(),materialIndex:s.getUint32()});return r},i.writeOglBuffer=function(t){const e=t.attributes.position,n=e.values.length/e.componentsPerAttribute,o=t.indices.length;let i=s(n,o,r.defined(t.attributes.st));return i=u(t,0,n,o,i),i},i.spliceBuffer=function(t){const e=[],n=i.parseGeometry(t),o=n.groups,a=o.length,f=n.attributes;if(1===a)return e.push(t),e;const p=i.getVertexCountByGroup(t),c=r.defined(f.st);for(let t=0;t<a;t++){const r=p[t],i=o[t].count;let a=s(r,i,c);a=u(n,t,r,i,a),e.push(a)}return e},i.getVertexCountByGroup=function(t){const e=[],n=i.parseGeometry(t),r=n.indices,o=n.groups,s=o.length;let a,u,f,p,c,l={};for(let t=0;t<s;t++){a=o[t],u=a.start,f=a.count,p=0,l={};for(let t=0;t<f;t++)c=r[u+t],void 0===l[c]&&(l[c]=++p);e.push(p)}return e};var f=i;t.OglParser=f}));
