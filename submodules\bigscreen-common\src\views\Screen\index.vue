<!--
 * @Description: 大屏主页面
 * @Autor: qian
 * @Date: 2023-06-19 17:04:41
 * @LastEditors: wangjialing
 * @LastEditTime: 2023-08-23 16:42:34
-->
<template>
  <div class="Screen">
    首页
    <card-title title="我是标题"></card-title>
  </div>
</template>

<script>
  export default {
    name: 'Screen'
  }
</script>
<script setup>
import cardTitle from '../../components/popCom/cardTitle.vue';
</script>
<style lang="scss" scoped>
  .Screen {
    position: relative;
    width: 1920px;
    height: 100vh;
    overflow: hidden;
    background: #eee;
  }
</style>
