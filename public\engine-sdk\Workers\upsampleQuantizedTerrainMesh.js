define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./AttributeCompression-a01059cd","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./createTaskProcessorWorker","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./OrientedBoundingBox-d0a49c02","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./materem-d090bcd4","./EmWrapperManager-1ae94128","./TerrainEncoding-308a828e","./EmLBDeal-04aa737f"],(function(e,n,r,t,i,f,a,u,o,s,h,l,d,c,g,B,p,m,v,C,b,w,y,x,E,A){"use strict";var M=32767,P=M/2|0,T=[],O=new t.Cartographic,I=new r.Cartesian3,S=[],W=[],R=[],H=[],k=[],D=new r.Cartesian3,L=new i.BoundingSphere,N=new C.OrientedBoundingBox,z=new r.Cartesian2,U=new r.Cartesian3;function V(n,r,t,i,f){var a=n.uBuffer.length,u=0,o=t[i]-t[f];Math.abs(o)>1e-9&&(u=t[i]/(t[i]-t[f]));var s=n.uBuffer[r[i]],h=n.vBuffer[r[i]],l=n.hBuffer[r[i]],d=n.uBuffer[r[f]],c=n.vBuffer[r[f]],g=n.hBuffer[r[f]];if(d=s+u*(d-s),c=h+u*(c-h),g=l+u*(g-l),n.uBuffer.push(d),n.vBuffer.push(c),n.hBuffer.push(g),e.defined(n.nBuffer)){var B=n.nBuffer[2*r[i]],p=n.nBuffer[2*r[i]+1],m=n.nBuffer[2*r[f]],v=n.nBuffer[2*r[f]+1];m=B+u*(m-B),v=p+u*(v-p),n.nBuffer.push(m),n.nBuffer.push(v)}return a}function j(e,n,r,t,i){var f=0,a=0,u=0,o=[];o.length=32;var s=function(){o[f]=V(e,n,r,u,a),f++},h=function(){o[f]=n[a],f++};if(t>0){a=t,u=a-1,s();while(a<i&&r[a]>=0)h(),a++;a<i?u=a-1:(a=0,u=i-1),s()}else{a=0;while(a<i&&r[a]>=0)h(),a++;u=a-1,s();while(a<i&&r[a]<0)a++;if(a<i){u=a-1,s();while(a<i&&r[a]>=0)h(),a++}else a=0,u=i-1,s()}i=f;for(let l=0;l<f;l++)n[l]=o[l];return i}function F(e,n,r,t){var i=0,f=0,a=-1,u=[];u.length=32;for(let o=0;o<r;o++){const r=n.uBuffer[t[o]],s=n.vBuffer[t[o]],h=n.hBuffer[t[o]];u[o]=r*e.x+s*e.y+h*e.z+e.w,u[o]>1e-9?(i++,a<0&&(a=o)):u[o]<-1e-9&&f++}return 0===i&&f>0?(r=0,r):i>=0&&0===f?r:j(n,t,u,a,r)}function G(e,n,r,t,i,f,u){var o=[new a.Cartesian4(1,0,0,-e),new a.Cartesian4(-1,0,0,r),new a.Cartesian4(0,1,0,-n),new a.Cartesian4(0,-1,0,t)];for(let a=0;a<4;a++)if(u=F(o[a],i,u,f),u<3)return u;return u}function X(n,r,t,i,f,a,u,o){var s=a.length/3,h=[];for(let e=0;e<s;++e){let u=3,o=[];if(o.length=32,o[0]=a[3*e],o[1]=a[3*e+1],o[2]=a[3*e+2],u=G(n,r,t,i,f,o,u),u>=3){let e=u-2;for(let n=1;n<=e;++n)h.push(o[0]),h.push(o[n]),h.push(o[n+1])}}var l=[],d=[];l.length=f.uBuffer.length,d.length=l.length;for(let e=0;e<l.length;e++)l[e]=!0,d[e]=-1;for(let e=0;e<h.length;e++)l[h[e]]=!1;for(let g=0,B=0;g<l.length;++g)l[g]||(d[g]=B,B++,u.uBuffer.push(f.uBuffer[g]),u.vBuffer.push(f.vBuffer[g]),u.hBuffer.push(f.hBuffer[g]),e.defined(f.nBuffer)&&(u.nBuffer.push(f.nBuffer[2*g]),u.nBuffer.push(f.nBuffer[2*g+1])));let c=h.length;for(let e=0;e<c;++e)o.push(d[h[e]])}function Z(n,f){var a=n.isEastChild,u=n.isNorthChild,o=a?P:0,s=a?M:P,h=u?P:0,l=u?M:P,d=S,g=W,B=R,p=k;d.length=0,g.length=0,B.length=0,p.length=0;var m=H;m.length=0;var v=n.vertices,b=n.indices;b=b.subarray(0,n.indexCountWithoutSkirts);var w,y,x,V,j,F=E.TerrainEncoding.clone(n.encoding),G=F.hasVertexNormals,Z=n.exaggeration,_=n.vertexCountWithoutSkirts,Y=n.minimumHeight,q=n.maximumHeight,J=new Array(_),K=new Array(_),Q=new Array(_),$=G?new Array(2*_):void 0;for(y=0,x=0;y<_;++y,x+=2){var ee=F.decodeTextureCoordinates(v,y,z);if(w=F.decodeHeight(v,y)/Z,V=r.CesiumMath.clamp(ee.x*M|0,0,M),j=r.CesiumMath.clamp(ee.y*M|0,0,M),Q[y]=r.CesiumMath.clamp((w-Y)/(q-Y)*M|0,0,M),J[y]=V,K[y]=j,G){var ne=F.getOctEncodedNormal(v,y,U);$[x]=ne.x,$[x+1]=ne.y}}var re={uBuffer:J,vBuffer:K,hBuffer:Q,nBuffer:$},te={uBuffer:d,vBuffer:g,hBuffer:B,nBuffer:p};X(o,h,s,l,re,b,te,m);var ie=a?-M:0,fe=u?-M:0,ae=[],ue=[],oe=[],se=[],he=Number.MAX_VALUE,le=-he,de=T;de.length=0;var ce,ge=t.Ellipsoid.clone(n.ellipsoid),Be=t.Rectangle.clone(n.childRectangle),pe=Be.north,me=Be.south,ve=Be.east,Ce=Be.west;if(ve<Ce&&(ve+=r.CesiumMath.TWO_PI),n.isPlaneMode){var be=n.projectionString,we=n.dCenX,ye=n.dCenY,xe=n.dCenZ;ce=new A.EmLBDeal;var Ee=ce.init(be,new r.Cartesian3(we,ye,xe));Ee||(ce.destroy(),ce=void 0)}for(y=0;y<d.length;++y)V=Math.round(d[y]),V<=o?(ae.push(y),V=0):V>=s?(oe.push(y),V=M):V=2*V+ie,d[y]=V,j=Math.round(g[y]),j<=h?(ue.push(y),j=0):j>=l?(se.push(y),j=M):j=2*j+fe,g[y]=j,w=r.CesiumMath.lerp(Y,q,B[y]/M),w<he&&(he=w),w>le&&(le=w),B[y]=w,O.longitude=r.CesiumMath.lerp(Ce,ve,V/M),O.latitude=r.CesiumMath.lerp(me,pe,j/M),O.height=w,ge.cartographicToCartesian(O,I),e.defined(ce)&&ce.computeCartesianToProj(I,!1,void 0,I),de.push(I.x),de.push(I.y),de.push(I.z);var Ae,Me,Pe=i.BoundingSphere.fromVertices(de,r.Cartesian3.ZERO,3,L);if(e.defined(ce))Ae=C.OrientedBoundingBox.fromPoints(Pe.getCornerAry()),Me=new r.Cartesian3;else{Ae=C.OrientedBoundingBox.fromRectangle(Be,he,le,ge,N);var Te=new E.EllipsoidalOccluder(ge);Me=Te.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(Pe.center,de,3,Pe.center,he,D)}var Oe=le-he,Ie=new Uint16Array(d.length+g.length+B.length);for(y=0;y<d.length;++y)Ie[y]=d[y];var Se=d.length;for(y=0;y<g.length;++y)Ie[Se+y]=g[y];for(Se+=g.length,y=0;y<B.length;++y)Ie[Se+y]=M*(B[y]-he)/Oe;var We,Re=c.IndexDatatype.createTypedArray(d.length,m);if(G){var He=new Uint8Array(p);f.push(Ie.buffer,Re.buffer,He.buffer),We=He.buffer}else f.push(Ie.buffer,Re.buffer);return e.defined(ce)&&ce.destroy(),{vertices:Ie.buffer,encodedNormals:We,indices:Re.buffer,minimumHeight:he,maximumHeight:le,westIndices:ae,southIndices:ue,eastIndices:oe,northIndices:se,boundingSphere:Pe,orientedBoundingBox:Ae,horizonOcclusionPoint:Me}}function _(n){var r=n.data,t=r.webAssemblyConfig;e.defined(t)&&x.EmWrapperManager.initWebAssembly(t.wasmBinaryFileES6).then((function(){self.onmessage=p(Z),self.postMessage(!0)}))}return _}));