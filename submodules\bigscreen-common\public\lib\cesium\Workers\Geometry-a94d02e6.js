/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./defined-3b3eb2ba","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./WebGLConstants-433debbf","./Transforms-42ed7720"],(function(t,e,n,a,i,r){"use strict";var s=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});const o={POINTS:i.WebGLConstants.POINTS,LINES:i.WebGLConstants.LINES,LINE_LOOP:i.WebGLConstants.LINE_LOOP,LINE_STRIP:i.WebGLConstants.LINE_STRIP,TRIANGLES:i.WebGLConstants.TRIANGLES,TRIANGLE_STRIP:i.WebGLConstants.TRIANGLE_STRIP,TRIANGLE_FAN:i.WebGLConstants.TRIANGLE_FAN,isLines:function(t){return t===o.LINES||t===o.LINE_LOOP||t===o.LINE_STRIP},isTriangles:function(t){return t===o.TRIANGLES||t===o.TRIANGLE_STRIP||t===o.TRIANGLE_FAN},validate:function(t){return t===o.POINTS||t===o.LINES||t===o.LINE_LOOP||t===o.LINE_STRIP||t===o.TRIANGLES||t===o.TRIANGLE_STRIP||t===o.TRIANGLE_FAN}};var u=Object.freeze(o);function I(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT),this.attributes=t.attributes,this.indices=t.indices,this.primitiveType=e.defaultValue(t.primitiveType,u.TRIANGLES),this.boundingSphere=t.boundingSphere,this.geometryType=e.defaultValue(t.geometryType,s.NONE),this.boundingSphereCV=t.boundingSphereCV,this.offsetAttribute=t.offsetAttribute}I.computeNumberOfVertices=function(t){let n=-1;for(const a in t.attributes)if(t.attributes.hasOwnProperty(a)&&e.defined(t.attributes[a])&&e.defined(t.attributes[a].values)){const e=t.attributes[a];n=e.values.length/e.componentsPerAttribute}return n};const N=new a.Cartographic,T=new n.Cartesian3,c=new a.Matrix4,l=[new a.Cartographic,new a.Cartographic,new a.Cartographic],L=[new n.Cartesian2,new n.Cartesian2,new n.Cartesian2],b=[new n.Cartesian2,new n.Cartesian2,new n.Cartesian2],f=new n.Cartesian3,E=new r.Quaternion,p=new a.Matrix4,m=new a.Matrix2;I._textureCoordinateRotationPoints=function(t,e,i,s){let o;const u=a.Rectangle.center(s,N),I=a.Cartographic.toCartesian(u,i,T),y=r.Transforms.eastNorthUpToFixedFrame(I,i,c),C=a.Matrix4.inverse(y,c),h=L,d=l;d[0].longitude=s.west,d[0].latitude=s.south,d[1].longitude=s.west,d[1].latitude=s.north,d[2].longitude=s.east,d[2].latitude=s.south;let A=f;for(o=0;o<3;o++)a.Cartographic.toCartesian(d[o],i,A),A=a.Matrix4.multiplyByPointAsVector(C,A,A),h[o].x=A.x,h[o].y=A.y;const S=r.Quaternion.fromAxisAngle(n.Cartesian3.UNIT_Z,-e,E),x=a.Matrix3.fromQuaternion(S,p),P=t.length;let G=Number.POSITIVE_INFINITY,R=Number.POSITIVE_INFINITY,_=Number.NEGATIVE_INFINITY,O=Number.NEGATIVE_INFINITY;for(o=0;o<P;o++)A=a.Matrix4.multiplyByPointAsVector(C,t[o],A),A=a.Matrix3.multiplyByVector(x,A,A),G=Math.min(G,A.x),R=Math.min(R,A.y),_=Math.max(_,A.x),O=Math.max(O,A.y);const g=a.Matrix2.fromRotation(e,m),w=b;w[0].x=G,w[0].y=R,w[1].x=G,w[1].y=O,w[2].x=_,w[2].y=R;const V=h[0],M=h[2].x-V.x,v=h[1].y-V.y;for(o=0;o<3;o++){const t=w[o];a.Matrix2.multiplyByVector(g,t,t),t.x=(t.x-V.x)/M,t.y=(t.y-V.y)/v}const F=w[0],W=w[1],Y=w[2],B=new Array(6);return n.Cartesian2.pack(F,B),n.Cartesian2.pack(W,B,2),n.Cartesian2.pack(Y,B,4),B},t.Geometry=I,t.GeometryAttribute=function(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT),this.componentDatatype=t.componentDatatype,this.componentsPerAttribute=t.componentsPerAttribute,this.normalize=e.defaultValue(t.normalize,!1),this.values=t.values},t.GeometryType=s,t.PrimitiveType=u}));
