<template>
  <div class="h-1400 w-2200 flex">
    <div class="position-absolute left-20 top-20 h-200 w-200 cursor-move bg-indigo" v-drag>可以随意拖动</div>

    <div class="parent-box position-absolute left-200 top-200 z-1 h-600 w-1200 border border-1 border-blue border-solid bg-white" shadow="~  hover:blue">
      <div
        shadow="~  hover:blue"
        class="position-absolute left-10 top-100 z-10 h-400 w-300 bg-amber"
        :style="style"
        v-drag-fixed-range="{ draggableClass: 'draggable-area', parentClass: 'parent-box' }">
        <div class="draggable-area h-50 w-full cursor-move bg-cyan">拖这里</div>
        <div>只能在框里拖动，黄色区域不能拖动</div>
      </div>
    </div>

    <div id="targetDiv" class="position-absolute left-400 top-1000 h-400 w-300 cursor-move" border="5 solid amber" v-drag>目标盒子</div>

    <div class="absolute right-0 h-1400 w-500 bg-fuchsia">
      子盒子原来的位置，将挂在目标盒子左右
      <div class="absolute h-300 w-200 bg-red" shadow="~ hover:blue" v-close-to="{ targetId: 'targetDiv', left: 20, top: 10 }">
        目标盒子在左,设置left,如果设置top，则当前设置的top+设置的值
      </div>

      <div class="absolute h-300 w-200 bg-blueGray text-right" shadow="~ hover:blue" v-close-to="{ targetId: 'targetDiv', right: 20, top: 0 }">
        目标盒子在右，设置right
      </div>
    </div>
  </div>
</template>

<script setup>
  import vCloseTo from '@Common/directives/closeTo/index.js'
  import vDrag from '@Common/directives/drag/index.js'
  import vDragFixedRange from '@Common/directives/dragFixedRange/index.js'
  import { useTitle } from '@vueuse/core'
  const title = useTitle()
  title.value = '公共库-指令Demo'
</script>

<style lang="scss" scoped>
  .custom-style {
    @apply flex flex-content-center flex-1;
  }
</style>
