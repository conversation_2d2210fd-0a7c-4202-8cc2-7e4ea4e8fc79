<template>
  <div class="treetable-container">
    <el-table
      ref="treeTableStructure"
      @selection-change="handleSelect"
      @row-click="hanedleRow"
      :header-cell-style="{
        color: '#DAEBFF',
        borderColor: '#4979b8',
        height: '32px'
      }"
      :header-cell-class-name="headCellClass"
      :tree-props="{ children: 'thisChildList', hasChildren: 'hasChildren' }"
      :cell-style="cellStyle"
      :data="treeData"
      lazy
      type="selection"
      height="188px"
      :row-style="getRowStyle"
      :load="load"
      row-key="id">
      <!-- :header-cell-class-name="cellClass" -->
      <!-- :tree-props="{ children: 'thisChildList' }" -->

      <el-table-column type="selection" width="35" :selectable="judgeSelect"></el-table-column>

      <el-table-column prop="name" :label="props.tableHeaderList[0]" />

      <el-table-column prop="count" :label="props.tableHeaderList[1]" width="95" />
      <el-table-column prop="bugCount" :label="props.tableHeaderList[2]" width="95" />
    </el-table>
    <!-- <el-button type="primary" @click="handleClick">测试</el-button> -->
  </div>
</template>
<script setup>
  import { onUnmounted, reactive, ref, watch } from 'vue'
  import { useDebounceFn } from '@vueuse/core'
  const { appContext } = getCurrentInstance()
  import rowBg from '@/assets/ScreenRight/Perception/Device/row.png'
  import { toUe5 } from '@/hooks/useUE/tools.js'
  import lib from '@/utils/lib'
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    tableHeaderList: {
      type: Array,
      default: () => ['设备类型', '设备数量', '故障数量']
    },
    width: {
      type: String,
      default: '464px'
    },
    type: {
      type: String,
      default: 'device' // device|structure
    }
  })
  let treeData = []
  const treeTableStructure = ref(null) // 表单
  const iconMap = {
    供配电系统: 'powerIcon',
    火灾报警和消防系统: 'fireAlarmIcon',
    排水系统: 'waterIcon',
    通风系统: 'windIcon',
    通信系统: 'infoIcon',
    综合监控系统: 'monitorIcon',
    照明系统: 'lightIcon'
  }
  let selectIDList = reactive([]) //   传递的id列表
  const typeIds = lib.typeIdMap
  let typeId = 0
  // 当选择项发生变化时会触发该事件
  const handleSelect = useDebounceFn(async (selection, row) => {
    // 确保只保留最后一个选中项
    if (selection.length > 1) {
      const lastSelected = selection[selection.length - 1]
      treeTableStructure.value.clearSelection()
      treeTableStructure.value.toggleRowSelection(lastSelected, true)
      selection = [lastSelected]
    }

    lib._engineController.clearCzml()
    lib._engineController.clearSelect()
    if (selection.length == 0) return
    selectIDList = selection.filter((item) => item.code !== undefined)

    await lib._engineController.getBimInfoByCodeList(
      selectIDList.map((_) => _.code),
      lib.codeBimInfoMap
    )

    selectIDList.forEach(async (_) => {
      const bimInfo = await lib.utils.getBimInfoByCode(_.code)
      lib._engineController.flyToBimId(bimInfo.bimId, -15)

      if (bimInfo) {
        const cartesian = bimInfo.position
        const icon = await lib.utils.convertImageToBase64(`images/${iconMap[_.deviceParentParentSystemName] || 'defaultIcon'}.png`)
        const czml = {
          id: _.code,
          name: lib.utils.getRandomString(5),
          billboard: {
            // 图片
            image: icon,
            scale: 0.5,
            disableDepthTestDistance: 999999,
            horizontalOrigin: 'CENTER',
            verticalOrigin: 'BOTTOM'
          },
          position: {
            cartesian: [0, 0, 0]
          },
          onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
            console.log('点击了图标', position, tagInstance, clickClinetX, clickClinetY)
            let y = clickClinetY > 1000 ? 1000 : clickClinetY
            lib.popWindow.removeDialog('deviceWindow')
            lib._engineController.flyToBimId(bimInfo.bimId, -15)
            lib.popWindow.createPopWindow(
              './Components/DeviceFile/index.vue',
              {
                left: clickClinetX,
                top: y,
                tag: 'deviceWindow',
                draggable: true,
                appContext,
                appendParent: 'player',
                // followPoint: { typeId: obj.typeId, id },
                closeFunc: () => {}
              },
              _
            )
          }
        }
        lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.设备撒点)
        // lib._engineController.flyToBimId(bimInfo.bimId)
      }
    })
  }, 100)
  //   点击表格的一行 触发的函数
  const hanedleRow = (row, column, event) => {
    // console.log('点击表格的一行 触发的函数row', row, judgeSelect(row))
    // console.log('点击表格的一行 触发的函数column', column)
    if (judgeSelect(row)) {
      // 如果点击的是3级数据 会和这一行的多选框联动
      //   treeTable.value.toggleRowSelection(row,true) 这样是不会切换的 只能选中
      treeTableStructure.value.toggleRowSelection(row)
    } else {
      // 如果点击的是1、2级数据，就折叠孩子
      // treeTableStructure.value.toggleRowExpansion(row)
      // console.log('点击量这一行，但是1、2级不允许点击')
    }
  }

  //  判断是否可以点击
  const judgeSelect = (row, index) => {
    //  判断是否可以点击，1级和2级,3级都禁止点击
    if (props.type === 'structure') {
      return true
    }
    if (row.treeLevel == 1 || row.treeLevel == 2) {
      return false // 禁止点击
    } else if (row.treeLevel == 3) {
      if (row.thisChildList) {
        return row.thisChildList.length < 0
      }
      return true
    } else {
      return true
    }
  }
  //   把表头的全选框隐藏
  // 将表头的全选隐藏
  const headCellClass = (row) => {
    if (row.columnIndex === 0) {
      return 'disabledCheck'
    }
  }
  // 定义生成行样式的方法
  const getRowStyle = () => {
    return {
      background: `url(${rowBg}) no-repeat`,
      backgroundSize: '100% 35px',
      height: '37px'
    }
  }
  const highlightCell = [
    '照明系统',
    '照明子系统',
    '主照明灯具',
    '加强照明灯具',
    '综合监控系统',
    '综合监控子系统',
    '摄像机',
    '交通监控子系统',
    '车道指示器',
    '火灾报警和消防系统',
    '火灾报警子系统',
    '手动报警按钮',
    '消防子系统',
    '消防喷淋泵组',
    '消火栓泵组',
    '消防泡沫泵组',
    '通风系统',
    '通风子系统',
    '射流风机',
    '排水系统',
    '排水子系统',
    '排水泵'
  ]
  const cellStyle = (row) => {
    if (judgeSelect(row.row)) {
      return {
        height: '36px',
        color: '#DAEBFF',
        borderColor: '#0e3f85e2',
        cursor: 'pointer'
      }
    } else if (highlightCell.includes(row.row.name)) {
      return {
        height: '36px',
        color: '#00b0f0',
        borderColor: '#0e3f85e2',
        cursor: 'pointer'
      }
    } else {
      return {
        height: '36px',
        color: '#DAEBFF',
        borderColor: '#0e3f85e2',
        cursor: 'default'
      }
    }
  }

  const findNodeById = (data, id) => {
    function traverse(node) {
      if (node.id === id) {
        return node
      }
      if (node.thisChildList && node.thisChildList.length > 0) {
        for (const child of node.thisChildList) {
          const found = traverse(child)
          if (found) {
            return found
          }
        }
      }
      return null
    }

    for (const node of data) {
      const found = traverse(node)
      if (found) {
        return found
      }
    }
    return null
  }
  const load = (tree, treeNode, resolve) => {
    console.log(tree, treeNode, resolve)
    const childNode = findNodeById(props.data, tree.id)
    console.log('子节点', childNode)
    const list = childNode.thisChildList
    list.forEach((item) => {
      item.hasChildren = item.thisChildList && item.thisChildList.length > 0
    })

    resolve(list)
  }
  lib.bus.busTreeTableStructure.on((type) => {
    if (type === 'clear') {
      treeTableStructure.value.clearSelection()
    }
  })
  watch(
    () => props.data,
    () => {
      if (props.data) {
        console.log('【watch】__表单树形!!!!!!!!!!props.data', props.data)
        treeData = props.data.map((_) => {
          return {
            bugCount: _.bugCount,
            count: _.count,
            goodRate: _.goodRate,
            id: _.id,
            name: _.name,
            treeLevel: _.treeLevel,
            hasChildren: _.thisChildList.length > 0
          }
        })
      }
    }
  )
  // const clickModel = lib.provideTools.clickModel.inject()
  // watch(
  //   () => clickModel?.data,
  //   (val) => {
  //     const obj = val?.object
  //     if (obj && obj.typeId === typeId) {
  //       const id = obj.id
  //       toUe5('openCustomPOIWindow', {
  //         id: id,
  //         isOpen: true
  //       })
  //       const data = JSON.parse(obj.window?.jsonParameter)
  //       lib.popWindow.createPopWindow(
  //         './Components/DeviceFile/index.vue',
  //         {
  //           // left: 1420,
  //           // top: 580,
  //           tag: 'deviceWindow',
  //           appContext,
  //           appendParent: 'player',
  //           followPoint: { typeId: obj.typeId, id },
  //           closeFunc: () => {
  //             toUe5('openCustomPOIWindow', {
  //               id: id,
  //               isOpen: false
  //             })
  //           }
  //         },
  //         data
  //       )
  //     }
  //   },
  //   {
  //     immediate: true,
  //     deep: true
  //   }
  // )
  onUnmounted(() => {
    // const obj = {
    //   isOn: false, // 是否开启标签
    //   typeId: typeId // 设施设备类型ID（为0时指全部类型）
    // }
    // toUe5('customPOIList', obj)
    lib.popWindow.removeDialog('deviceWindow')
    lib.bus.busTreeTableStructure.reset()
  })
</script>

<style lang="scss" scoped>
  :deep(.el-icon svg) {
    color: #ffffff;
    opacity: 1 !important;
  }
  :deep(.el-table) {
    /* width: 32px; */
    height: 22px;
    font-family: Alibaba-PuHuiTi;
    font-size: 16px;
    font-weight: normal;
    line-height: 12px;
    background: transparent;

    --el-table-border: none;
    --el-table-border-color: #06244d;

    // --el-bg-color: linear-gradient(270deg, #1133731a 0%, #003574cc 100%);
    --el-bg-color: transparent;
    --el-fill-color-light: linear-gradient(270deg, #0d366e33 0%, #0d366e99 50%, #0d366e33 100%);
    tr {
      background-color: transparent !important;
    }

    /* color: #4979b8; */

    /* background-color: red !important; */
  }
  :deep(.el-table__header-wrapper) {
    width: 100%;
    height: 36px;
    margin-bottom: 3px;

    // background: url('@/assets/ScreenRight/Perception/Device/title.png') no-repeat;
    // background-size: 100% 45px;
    background: linear-gradient(90deg, rgb(0 40 85 / 73%) 0%, rgb(4 86 117 / 94%) 53%, rgb(0 79 129 / 56%) 100%);
    border-radius: 7px;
  }
  :deep(.is-disabled .el-checkbox__inner) {
    background-color: #224a87;
    border-color: #3975d2 !important;
  }

  /* 禁止选择的1、2级选中了的样式 */
  :deep(.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner) {
    background-color: grey;
  }
  :deep(.el-checkbox__inner) {
    background-color: #0a6fad;
    border: 1px solid #00c2ff;
  }
  :deep(.el-table__empty-block) {
    background-color: #06244d;
  }
  :deep(.el-checkbox__inner::after) {
    height: 6px;
  }
  :deep(.el-table .disabledCheck .cell .el-checkbox__inner) {
    display: none;
  }
</style>
