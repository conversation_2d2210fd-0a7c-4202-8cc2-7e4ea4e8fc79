/*
 * @Description: cesium 控制
 * @Autor: qian
 * @Date: 2023-09-11 10:17:41
 * @LastEditors: qian
 * @LastEditTime: 2024-08-26 17:06:38
 */
import { markRaw } from 'vue'

import gaugePointImg from '@Common/assets/gaugePoint.svg'

import useStore from '@Common/store'
import { createPopWindow, removeDialogById } from '@Common/views/Screen/InfoWindow/PopWindow.js'
let controller = null
// 存储全部实例
const storageContainer = {
  entities: new Map(),
  primitives: new Map(),
  billboards: new Map(),
  dataSources: new Map(),
  flowPolylines: new Map()
}
const CesiumController = function (info) {
  if (controller) {
    return controller
  }
  info && this.setConstructor(info)
}

CesiumController.prototype.setConstructor = function (info) {
  // 挂载到window
  this.ref = info.ref
  this.viewer = info.viewer
  this.entity = this.ref.createEntity()
  this.outline = this.ref.outline()
  this.name = info._cesium ? info._cesium : '__Cesium'
  controller = this
  window[info._cesium ? info._cesium : '__Cesium'] = this

  this.viewer.camera.percentageChanged = 0.1
}

/**
 * @description: 设置视口
 * @param {Object} camera // 视口对象
 * @param {Number} duration // 动画时长
 */
CesiumController.prototype.setCamera = function (camera, duration = 3) {
  this.ref.flyTo(Object.assign({}, camera, { duration: duration || 0 }))
}

/**
 * @description: 根据经纬度拉近视角
 * @param {Object} position // [lng, lat]
 * @param {Number} height // 视角高度
 * @param {Number} duration // 动画时长
 */
CesiumController.prototype.setCameraByLngAndLat = function (position, height = 100000, duration = 2) {
  // 创建目标位置
  var destination = Cesium.Cartesian3.fromDegrees(position[0], position[1], height)
  // 设置相机视角
  this.ref.flyTo({
    destination,
    duration // 持续时间，单位为秒
  })
}

/**
 * @description: 渲染和清除标签
 * @param {*} data 配置数据 注释部分非必传，position和image必传
   [{
        position: { longitude: item.Lon_bd, latitude: item.Lat_bd }, // height不传默认为0
        image: item.image,
        // scale: 0.5, // 标签缩放 默认为1
        // pixelOffset: {x: 0, y: 40}, // 标签在屏幕空间中的偏移，x指向右侧，y指向下方
        // scaleByDistance: {near: 0, nearScale: 0, far: 3000, farScale: 1 } // 标签大小根据同相机的距离进行缩放,在near和far之间进行插值
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(1000, 100000), // 控制在距离范围内可见
        // clickFunction: () => {} // 点击事件
    }]
 * @param {*} isShow 渲染和清除 默认为true渲染
 */
CesiumController.prototype.setBillboard = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const examples = []
    data.forEach((item, index) => {
      // //   是否有默认高度 没有传高度，默认为0
      //   item.position = { longitude: item.position.longitude, latitude: item.position.latitude, height: !item.position.height ? 0 : item.position.height }
      item.id = item.id ? item.id : 'image_' + Math.random().toString(16).slice(2) + '_' + index
      item.content = !item.content ? {} : item.content
      item.content.clickFunction = item.clickFunction ? item.clickFunction : null
      item.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined
      item.eyeOffset = new Cesium.Cartesian3(0, 0, -10) // 设置标签相对于位置的偏移量
      const example = this.ref.addBillboard(item)
      examples.push(example)
      storageContainer.billboards.set(item.id, example)
    })
    return examples
  } else {
    data.forEach((item) => {
      if (!item) return
      this.ref.removeBillboardById(typeof item.id === 'string' ? item.id : item.id.id)
      storageContainer.billboards.delete(typeof item.id === 'string' ? item.id : item.id.id)
    })
  }
}

/**
 * @description: 渲染和清除线段
 * @param {*} data
 * {
    positions: [longitude, latitude，longitude, latitude, ...]
    width: 6, // 宽度
    material: '#47B8FF',
    // clickFunction: () => {} // 点击事件
 * }
 * @param {*} isShow
 * @author: qian
 */

CesiumController.prototype.setPolyline = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const polylines = []
    data.forEach((item, index) => {
      let positions = Cesium.Cartesian3.fromDegreesArray(item.positions)
      // 高度
      if (item.height) {
        const twoDimensionalArray = convertTo2DArray(item.positions)
        positions = twoDimensionalArray.map((position) => {
          return Cesium.Cartesian3.fromDegrees(position[0], position[1], item.height)
        })
      }
      const polyline = new Cesium.GroundPolylinePrimitive({
        geometryInstances: new Cesium.GeometryInstance({
          // geometry: Cesium.PolylineGeometry.createGeometry(
          //   new Cesium.PolylineGeometry({
          //     positions: positions,
          //     width: item.width || 3
          //   })
          geometry: new Cesium.GroundPolylineGeometry({
            positions: positions,
            width: item.width || 3
          }),
          attributes: {
            // eslint-disable-next-line new-cap
            color: Cesium.ColorGeometryInstanceAttribute.fromColor(new Cesium.Color.fromCssColorString(item.color || item.material || '#15dc23'))
          }
        }),
        appearance: new Cesium.PolylineColorAppearance()
        // asynchronous: false
      })

      polyline.id = 'polyline_' + Math.random().toString(16).slice(2) + '_' + index + 1
      // polyline.content = {}
      polyline.content = !item.content ? {} : item.content

      polyline.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件
      polyline.content.clickFunction = item.clickFunction ? item.clickFunction : null
      const primitive = this.viewer.scene.primitives.add(polyline)
      polylines.push(primitive)
      storageContainer.primitives.set(polyline.id, primitive)
    })
    return polylines
  } else {
    data.forEach((item) => {
      this.viewer.scene.primitives.remove(storageContainer.primitives.get(item.id))
      storageContainer.primitives.delete(item.id)
    })
  }
}
function convertTo2DArray(arr) {
  const result = []
  for (let i = 0; i < arr.length; i += 2) {
    result.push([arr[i], arr[i + 1]])
  }
  return result
}
CesiumController.prototype.setPolygon = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const examples = []
    data.forEach((item, index) => {
      const polygon = {
        id: 'polygon_' + Math.random().toString(16).slice(2) + '_' + index + 1,
        polygon: {
          hierarchy: Cesium.Cartesian3.fromDegreesArray(item.positions),
          fill: true,
          material: Cesium.Color.fromAlpha(Cesium.Color.fromCssColorString(item.color), item.alpha),
          classificationType: Cesium.ClassificationType.TERRAIN
        },
        content: {}
      }
      polygon.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件 // ! 面层点击事件错乱
      // examples.content.clickFunction = item.clickFunction ? item.clickFunction : null

      const example = this.viewer.entities.add(polygon)
      examples.push(example)
      storageContainer.entities.set(polygon.id, example)
    })
    return examples
  } else {
    data.forEach((item) => {
      this.viewer.entities.remove(item)
      storageContainer.entities.delete(item.id)
    })
  }
}

// 全部清除
CesiumController.prototype.removeAllExamples = function () {
  storageContainer.entities.forEach((item) => {
    if (item.content.isAllowAllDeletion) {
      this.entity.removeEntityById(item.id)
      storageContainer.entities.delete(item.id)
    }
  })
  storageContainer.billboards.forEach((item) => {
    if (!item) return
    const val = typeof item.id === 'string' ? item : item.id
     if (val.content.isAllowAllDeletion) {
      this.ref.removeBillboardById(val.id)
      storageContainer.billboards.delete(val.id)
    }
  })

  storageContainer.primitives.forEach((item) => {
    if (item.content.isAllowAllDeletion) {
      this.viewer.scene.primitives.remove(storageContainer.primitives.get(item.id))
      storageContainer.primitives.delete(item.id)
    }
  })

  storageContainer.dataSources.forEach((item) => {
    __Cesium.viewer.dataSources.remove(storageContainer.dataSources.get(item.name))
    storageContainer.dataSources.delete(item.name)
  })

  storageContainer.flowPolylines.forEach((item) => {
    if (item.isAllowAllDeletion) {
      this.viewer.scene.primitives.remove(item)
      storageContainer.flowPolylines.delete(item.name)
    }
  })
}

// 全部清除 此方法在销毁cesium时使用
CesiumController.prototype.removeAll = function () {
  this.ref.removeAllBillboards()
  this.ref.removeAllLabels()
  // this.ref.removeLotsLabel()
  // this.viewer.scene.primitives.removeAll() // ! 此方法会清除白模
  this.entity.removeAllEntity()
  this.outline.removeOutline()
  // this.viewer.dataSources.removeAll() // 路网
  controller = null
}

// 注册鼠标事件
CesiumController.prototype.onMouseEvents = function () {
  // 监听鼠标左键点击事件
  this.viewer.screenSpaceEventHandler.setInputAction((movement) => {
    const feature = this.viewer.scene.pick(movement.position) // 点击的物体
    const cartesian = this.viewer.camera.pickEllipsoid(movement.position, this.viewer.scene.ellipsoid)
    let position = {}
    if (cartesian) {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
      position = {
        longitude: Cesium.Math.toDegrees(cartographic.longitude),
        latitude: Cesium.Math.toDegrees(cartographic.latitude)
      }
      // console.log(Cesium.Math.toDegrees(cartographic.longitude).toFixed(5), Cesium.Math.toDegrees(cartographic.latitude).toFixed(5))
    }
    const { contextMenu, storeScreenPopWindowData } = useStore()
    if (contextMenu.isGaugePoint) {
      // 如果开始标记点则每次点击地图都撒一个点
      const { longitude, latitude } = position
      const examples = this.setBillboard([
        {
          position, // height不传默认为0
          image: gaugePointImg,
          content: {}
        }
      ])
      contextMenu.pointList.push(markRaw({ id: 'gaugePoint' + Math.ceil(Math.random() * 1000000), longitude, latitude, examples }))
      // 判断弹窗数组里是否有对应id的弹窗
      const bool = storeScreenPopWindowData.dialogNodes.some((_) => _.id === 'commonMenuInfo')
      if (!bool) createPopWindow('MenuInfo', { id: 'commonMenuInfo', left: '50%', top: '10.4167vw', draggableClass: '', appendParent: 'body' })
    }
    if (feature) {
      if (feature.id) {
        // 点击非模型
        if (feature.id.content && feature.id.content.clickFunction && typeof feature.id.content.clickFunction === 'function') {
          feature.id.content.clickFunction(feature, movement.position) // 对象
        }
      } else if (feature.primitive.content && feature.primitive.content.clickFunction && typeof feature.primitive.content.clickFunction === 'function') {
        // todo Polyline更新渲染方法
        feature.primitive.content.clickFunction(feature, movement.position) // 对象
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

// 注册右键菜单事件
CesiumController.prototype.onContextMenu = function () {
  // 绑定鼠标右键事件
  this.viewer.scene.canvas.addEventListener(
    'contextmenu',
    (e) => {
      // 显示菜单
      createPopWindow('ContextMenu', { id: 'commonMenu', left: e.clientX, top: e.clientY, appendParent: 'body' }, e)
    },
    false
  )

  // 鼠标点击文档移除菜单
  document.addEventListener('click', () => {
    removeDialogById('commonMenu')
  })
}

/**
 * @description: 根据Wgs84获取屏幕位置
 * @param {Array} data 数组 [Wgs84.lng, Wgs84.lat]
 */
CesiumController.prototype.getWindowPositionByWgs84 = function (data) {
  return Cesium.SceneTransforms.wgs84ToWindowCoordinates(this.viewer.scene, Cesium.Cartesian3.fromDegrees(data[0], data[1], 0))
}
/**
 * @description: 根据屏幕位置获取Wgs84
 * @param {*} x
 * @param {*} y
 */
CesiumController.prototype.getWgs84ByWindowPosition = function (x, y) {
  const cartographic = Cesium.Cartographic.fromCartesian(this.viewer.scene.camera.pickEllipsoid({ x, y }))
  return [(cartographic.longitude * 180.0) / Math.PI, (cartographic.latitude * 180.0) / Math.PI]
}

/** // !此方法已废弃 推荐使用 onCameraChanged()
 * @description: 通过相机高度计算层级
 */
CesiumController.prototype.getZoomtByHeight = function (height) {
  height = height || this.getCameraHeight() // this.viewer.camera.positionCartographic.height
  // 获取zoom层
  var A = 40487.57
  var B = 0.00007096758
  var C = 91610.74
  var D = -40467.74
  console.log('获取zoom层级' + Math.round(D + (A - D) / (1 + Math.pow(height / C, B))))
  this.zoom = Math.round(D + (A - D) / (1 + Math.pow(height / C, B)))
  return this.zoom
}

/**
 * @description: 获取相机高度
 */
CesiumController.prototype.getCameraHeight = function () {
  return this.viewer.camera.positionCartographic.height
}

/**
 * @description: 注册相机变化监听
 */
CesiumController.prototype.onCameraChanged = function () {
  // this.viewer.camera.percentageChanged = 0
  this.viewer.camera.changed.addEventListener((e) => {
    // cesium_variable_Data.heading = Cesium.Math.toDegrees(this.viewer.camera.heading)
    // cesium_variable_Data.pitch = Cesium.Math.toDegrees(this.viewer.camera.pitch)
    // cesium_variable_Data.roll = Cesium.Math.toDegrees(this.viewer.camera.roll)
    if (tileRender && tileRender.length > 0) {
      this.level = this.viewer.scene._globe._surface._tilesToRender[0]._level
    }
  })
}
// 获取当前层级
CesiumController.prototype.getLevel = function () {
  // return this.viewer.scene._globe._surface._tilesToRender[0]._level
  // const tileRender = this.viewer.scene._globe._surface._tilesToRender
  // if (tileRender && tileRender.length > 0) {
  //   return this.viewer.scene._globe._surface._tilesToRender[0]._level
  // }

  const height = this.getCameraHeight() // this.viewer.camera.positionCartographic.height
  // 获取zoom层
  var A = 40487.57
  var B = 0.00007096758
  var C = 91610.74
  var D = -40467.74
  // console.log('获取zoom层级' + Math.round(D + (A - D) / (1 + Math.pow(height / C, B))))
  this.zoom = Math.round(D + (A - D) / (1 + Math.pow(height / C, B)))
  return this.zoom
}

CesiumController.prototype.loadImages = function (imgList) {
  const promiseAll = []
  for (let i = 0; i < imgList.length; i++) {
    promiseAll[i] = new Promise((resolve, reject) => {
      const img = new Image()
      img.src = imgList[i]
      img.onload = function () {
        resolve(img)
      }
    })
  }
  return Promise.all(promiseAll)
}
CesiumController.prototype.getAssetsFile = function (url) {
  return new URL(`../../../public/${url}`, import.meta.url).href
}

// 流动线段 => 从开始绘制到结束，再循环流动
CesiumController.prototype.setFlowPolyline = function (data, isShow = true, isAllowAllDeletion = true) {
  if (!data || !data.length) return
  if (isShow) {
    const collection = this.viewer.scene.primitives.add(new Cesium.PolylineCollection({ uniformSpeed: true }))
    // 加载图片
    this.loadImages([this.getAssetsFile(data[0].image || 'images/Arrow11.png')]).then((imgArr) => {
      data.forEach((ele, index) => {
        const material = Cesium.Material.fromType('PolylineFirstFlow', {
          image: new Cesium.Texture({
            context: this.viewer.scene.frameState.context,
            source: imgArr[0]
          }),
          // eslint-disable-next-line new-cap
          color: new Cesium.Color.fromCssColorString(ele.color || '#15dc23'),
          speed: ele.speed || 1,
          onlyFirstFlow: ele.onlyFirstFlow || false
        })

        this.viewer.scene.preRender.addEventListener(() => {
          material.uniforms.currentFlowTime++
        })
        // const coordinates = ele.lineArray.flat()
        const polyline = {
          positions: Cesium.Cartesian3.fromDegreesArray(ele.lineArray),
          width: ele.width || 10,
          material: material
        }
        collection.add(polyline)
      })
    })
    collection.name = 'flowPolyline_' + Math.random().toString(16).slice(2)
    collection.isAllowAllDeletion = isAllowAllDeletion
    // polyline.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

    storageContainer.flowPolylines.set(collection.name, collection)
    return collection
  } else {
    this.viewer.scene.primitives.remove(storageContainer.flowPolylines.get(data.name))
    storageContainer.flowPolylines.delete(data.name)
  }
}
// 光斑流线 适用于区域范围线路流动
CesiumController.prototype.setFaculaFlowPolyline = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const polylines = []
    this.loadImages([this.getAssetsFile('images/Arrow.png')]).then((imgArr) => {
      data.forEach((item, index) => {
        const polyline = {
          positions: Cesium.Cartesian3.fromDegreesArray(item.positions),
          clampToGround: true,
          width: item.width || 3,
          material: new Cesium.PolylineFaculaFlowMaterialProperty({
            // eslint-disable-next-line new-cap
            color: new Cesium.Color.fromCssColorString(item.color || item.material || '#15dc23'),
            // color: Cesium.Color.CYAN,
            duration: item.duration || 5000,
            trailImage: imgArr[0]
            // trailImage: './images/Arrow.png'
          })
        }

        polyline.id = 'faculaFlowPolyline_' + Math.random().toString(16).slice(2) + '_' + index + 1
        polyline.content = {}
        polyline.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

        // 点击事件
        polyline.content.clickFunction = item.clickFunction ? item.clickFunction : null
        const entity = this.viewer.entities.add({ polyline })
        polylines.push(entity)
        storageContainer.entities.set(polyline.id, entity)
      })
    })
    return polylines
  } else {
    data.forEach((item) => {
      this.viewer.entities.remove(item)
      storageContainer.entities.delete(item.id)
    })
  }
}
CesiumController.prototype.setCustomDataSource = function (data) {
  if (!data) {
    const id = 'dataSource_' + Math.random().toString(16).slice(2)
    const dataSource = new Cesium.CustomDataSource(id)
    __Cesium.viewer.dataSources.add(dataSource)
    storageContainer.dataSources.set(id, dataSource)
    return {
      id,
      dataSource
    }
  } else {
    __Cesium.viewer.dataSources.remove(storageContainer.dataSources.get(data.id))
    storageContainer.dataSources.delete(data.id)
  }
}
// 虚线
CesiumController.prototype.setPolylineDash = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const entities = []

    data.forEach((item, index) => {
      item.positions = Cesium.Cartesian3.fromDegreesArray(item.positions)
      const dashedMaterial = new Cesium.PolylineDashMaterialProperty({
        // color: item.color || Cesium.Color.CYAN
        // eslint-disable-next-line new-cap
        color: new Cesium.Color.fromCssColorString(item.color || item.material || '#15dc23')
      })
      const line = {
        id: 'polylineDash_' + Math.random().toString(16).slice(2) + '_' + index + 1,
        polyline: {
          positions: item.positions,
          width: item.width || 2,
          material: dashedMaterial,
          clampToGround: true
        },
        content: {}
      }
      line.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件
      line.content.clickFunction = item.clickFunction ? item.clickFunction : null
      // 创建虚线实例
      const entity = this.viewer.entities.add(line)
      entities.push(entity)
      storageContainer.entities.set(line.id, entity)
    })
    return entities
  } else {
    data.forEach((item) => {
      this.viewer.entities.remove(item)
      storageContainer.entities.delete(item.id)
    })
  }
}
// 新版流动线 => 图片循环流动
CesiumController.prototype.setFlowPolylineNew = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const entities = []

    data.forEach((item, index) => {
      const line = {
        id: 'newFlowLine_' + Math.random().toString(16).slice(2) + '_' + index + 1,
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(item.positions),
          width: item.width || 10,
          material: new Cesium.PolylineFlowMaterialProperty({
            color: Cesium.Color.fromCssColorString('rgba(0, 255, 0, 0)'),
            image: item.image || this.getAssetsFile('images/style3.png'),
            repeat: new Cesium.Cartesian2(1, 1),
            // repeat: new Cesium.Cartesian2(2, 1),
            speed: item.speed || 3,
            reverse: false
          }),
          zIndex: 10,
          clampToGround: true
        },
        content: {}
      }
      line.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件
      line.content.clickFunction = item.clickFunction ? item.clickFunction : null
      // 创建虚线实例
      const entity = this.viewer.entities.add(line)
      entities.push(entity)
      storageContainer.entities.set(line.id, entity)
    })
    return entities
  } else {
    data.forEach((item) => {
      this.viewer.entities.remove(item)
      storageContainer.entities.delete(item.id)
    })
  }
}

// 波纹图
/*
[
  {
    position: [116.391275, 39.90923], // * [经度, 纬度] 必传
    semiMinorAxis: 50000, // 波纹半径
    semiMajorAxis: 50000, // 波纹半径
    height: 0,
    color: '#15dc23', // 波纹颜色
    duration: 4000, // 波纹持续时间
    gradient: 0.5, // 波纹渐变
    count: 2 // 波纹数量
  },
  ...
]
*/
CesiumController.prototype.setCircleWave = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const examples = []
    data.forEach((item, index) => {
      // eslint-disable-next-line new-cap
      var color = new Cesium.Color.fromCssColorString(item.color || '#15dc23')
      var convertedColor = new Cesium.Color(color.red, color.green, color.blue, color.alpha)
      const entity = {
        id: 'CircleWave_' + Math.random().toString(16).slice(2) + '_' + index + 1,
        name: item.name || '',
        content: {},
        position: Cesium.Cartesian3.fromDegrees(item.position[0], item.position[1], 0),
        ellipse: {
          height: item.height || 0,
          semiMinorAxis: item.semiMinorAxis || 50000,
          semiMajorAxis: item.semiMajorAxis || 50000,
          material: new Cesium.CircleWaveMaterialProperty({
            duration: item.duration || 4000,
            gradient: item.gradient || 0.5,
            // color: new Cesium.Color(66 / 255, 126 / 255, 216 / 255),
            color: convertedColor,
            count: item.count || 2
          })
        }
      }
      entity.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件
      // examples.content.clickFunction = item.clickFunction ? item.clickFunction : null
      const example = this.viewer.entities.add(entity)
      examples.push(example)
      storageContainer.entities.set(entity.id, example)
    })
    return examples
  } else {
    data.forEach((item) => {
      this.viewer.entities.remove(item)
      storageContainer.entities.delete(item.id)
    })
  }
}
/**
 * @description: 渲染热力图 渐变效果
 * @param {Object} data // 数据 // ! 如果第二个参数是false。第一个参数传此方法的返回值
 * [{
 *  positions: [121.44985195456954, 31.175514441949417, 1, 121.43700850785393, 31.168036544040305, 1], // 经纬度 + 高
 *  colors: ['#38ac18', '#34aca0'], // 颜色
 *  width: 3
 * }]
 * @param {BodyInit} isShow // 是否展示
 */
CesiumController.prototype.setHeatmap = function (data, isShow = true) {
  if (!data || !data.length) return
  if (isShow) {
    const polylines = []
    data.forEach((item, index) => {
      // eslint-disable-next-line new-cap
      item.colors = item.colors.map((color) => new Cesium.Color.fromCssColorString(color))
      var geometry = new Cesium.GeometryInstance({
        geometry: new Cesium.PolylineGeometry({
          positions: Cesium.Cartesian3.fromDegreesArrayHeights(item.positions),
          width: item.width || 3,
          colors: item.colors,
          colorsPerVertex: true
        })
      })

      const polyline = new Cesium.Primitive({
        geometryInstances: geometry,
        appearance: new Cesium.PolylineColorAppearance({
          translucent: false
        })
      })

      polyline.id = 'heatmap_' + Math.random().toString(16).slice(2) + '_' + index + 1
      polyline.content = {}
      polyline.content.isAllowAllDeletion = item.isAllowAllDeletion === undefined

      // 点击事件
      polyline.content.clickFunction = item.clickFunction ? item.clickFunction : null
      const primitive = this.viewer.scene.primitives.add(polyline)
      polylines.push(primitive)
      storageContainer.primitives.set(polyline.id, primitive)
    })
    return polylines
  } else {
    data.forEach((item) => {
      this.viewer.scene.primitives.remove(storageContainer.primitives.get(item.id))
      storageContainer.primitives.delete(item.id)
    })
  }
}

// 将经纬度转换为笛卡尔坐标
CesiumController.prototype.getCartesian3 = function (lng, lat, height = 0) {
  const cartesian3 = Cesium.Cartesian3.fromDegrees(lng, lat, height)
  return cartesian3
}

// 根据id更新billboard标签位置
// position: [lng, lat]
CesiumController.prototype.updateBillboardPositionById = function (id, position, height = 0) {
  storageContainer.billboards.get(id).position = Cesium.Cartesian3.fromDegrees(position[0], position[1], height)
}

// 根据id更新billboards标签图片
// image: 图片地址
CesiumController.prototype.updateBillboardImageById = function (id, image) {
  storageContainer.billboards.get(id).image = image
}

export default CesiumController
