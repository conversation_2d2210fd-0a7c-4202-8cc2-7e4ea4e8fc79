/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Math-b5f4d889"],(function(t,a,i,n,e){"use strict";function s(t,a,i,n,e,s,o){const r=function(t,a){return t*a*(4+t*(4-3*a))/16}(t,i);return(1-r)*t*a*(n+r*e*(o+r*s*(2*o*o-1)))}const o=new a.Cartesian3,r=new a.Cartesian3;function h(t,n,h,d){a.Cartesian3.normalize(d.cartographicToCartesian(n,r),o),a.Cartesian3.normalize(d.cartographicToCartesian(h,r),r),function(t,a,i,n,o,r,h){const d=(a-i)/a,c=r-n,u=Math.atan((1-d)*Math.tan(o)),l=Math.atan((1-d)*Math.tan(h)),M=Math.cos(u),g=Math.sin(u),_=Math.cos(l),p=Math.sin(l),f=M*_,m=M*p,C=g*p,H=g*_;let b,v,O,S,q,U=c,A=e.CesiumMath.TWO_PI,R=Math.cos(U),w=Math.sin(U);do{R=Math.cos(U),w=Math.sin(U);const t=m-H*R;let a;O=Math.sqrt(_*_*w*w+t*t),v=C+f*R,b=Math.atan2(O,v),0===O?(a=0,S=1):(a=f*w/O,S=1-a*a),A=U,q=v-2*C/S,isFinite(q)||(q=0),U=c+s(d,a,S,b,O,v,q)}while(Math.abs(U-A)>e.CesiumMath.EPSILON12);const y=S*(a*a-i*i)/(i*i),E=y*(256+y*(y*(74-47*y)-128))/1024,P=q*q,x=i*(1+y*(4096+y*(y*(320-175*y)-768))/16384)*(b-E*O*(q+E*(v*(2*P-1)-E*q*(4*O*O-3)*(4*P-3)/6)/4)),D=Math.atan2(_*w,m-H*R),T=Math.atan2(M*w,m*R-H);t._distance=x,t._startHeading=D,t._endHeading=T,t._uSquared=y}(t,d.maximumRadius,d.minimumRadius,n.longitude,n.latitude,h.longitude,h.latitude),t._start=i.Cartographic.clone(n,t._start),t._end=i.Cartographic.clone(h,t._end),t._start.height=0,t._end.height=0,function(t){const a=t._uSquared,i=t._ellipsoid.maximumRadius,n=t._ellipsoid.minimumRadius,e=(i-n)/i,s=Math.cos(t._startHeading),o=Math.sin(t._startHeading),r=(1-e)*Math.tan(t._start.latitude),h=1/Math.sqrt(1+r*r),d=h*r,c=Math.atan2(r,s),u=h*o,l=u*u,M=1-l,g=Math.sqrt(M),_=a/4,p=_*_,f=p*_,m=p*p,C=1+_-3*p/4+5*f/4-175*m/64,H=1-_+15*p/8-35*f/8,b=1-3*_+35*p/4,v=1-5*_,O=C*c-H*Math.sin(2*c)*_/2-b*Math.sin(4*c)*p/16-v*Math.sin(6*c)*f/48-5*Math.sin(8*c)*m/512,S=t._constants;S.a=i,S.b=n,S.f=e,S.cosineHeading=s,S.sineHeading=o,S.tanU=r,S.cosineU=h,S.sineU=d,S.sigma=c,S.sineAlpha=u,S.sineSquaredAlpha=l,S.cosineSquaredAlpha=M,S.cosineAlpha=g,S.u2Over4=_,S.u4Over16=p,S.u6Over64=f,S.u8Over256=m,S.a0=C,S.a1=H,S.a2=b,S.a3=v,S.distanceRatio=O}(t)}function d(t,a,e){const s=n.defaultValue(e,i.Ellipsoid.WGS84);this._ellipsoid=s,this._start=new i.Cartographic,this._end=new i.Cartographic,this._constants={},this._startHeading=void 0,this._endHeading=void 0,this._distance=void 0,this._uSquared=void 0,n.defined(t)&&n.defined(a)&&h(this,t,a,s)}Object.defineProperties(d.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},startHeading:{get:function(){return this._startHeading}},endHeading:{get:function(){return this._endHeading}}}),d.prototype.setEndPoints=function(t,a){h(this,t,a,this._ellipsoid)},d.prototype.interpolateUsingFraction=function(t,a){return this.interpolateUsingSurfaceDistance(this._distance*t,a)},d.prototype.interpolateUsingSurfaceDistance=function(t,a){const e=this._constants,o=e.distanceRatio+t/e.b,r=Math.cos(2*o),h=Math.cos(4*o),d=Math.cos(6*o),c=Math.sin(2*o),u=Math.sin(4*o),l=Math.sin(6*o),M=Math.sin(8*o),g=o*o,_=o*g,p=e.u8Over256,f=e.u2Over4,m=e.u6Over64,C=e.u4Over16;let H=2*_*p*r/3+o*(1-f+7*C/4-15*m/4+579*p/64-(C-15*m/4+187*p/16)*r-(5*m/4-115*p/16)*h-29*p*d/16)+(f/2-C+71*m/32-85*p/16)*c+(5*C/16-5*m/4+383*p/96)*u-g*((m-11*p/2)*c+5*p*u/2)+(29*m/96-29*p/16)*l+539*p*M/1536;const b=Math.asin(Math.sin(H)*e.cosineAlpha),v=Math.atan(e.a/e.b*Math.tan(b));H-=e.sigma;const O=Math.cos(2*e.sigma+H),S=Math.sin(H),q=Math.cos(H),U=e.cosineU*q,A=e.sineU*S,R=Math.atan2(S*e.sineHeading,U-A*e.cosineHeading)-s(e.f,e.sineAlpha,e.cosineSquaredAlpha,H,S,q,O);return n.defined(a)?(a.longitude=this._start.longitude+R,a.latitude=v,a.height=0,a):new i.Cartographic(this._start.longitude+R,v,0)},t.EllipsoidGeodesic=d}));
