define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./WebGLConstants-0cf30984","./AttributeCompression-a01059cd","./IndexDatatype-e1c63859","./createTaskProcessorWorker"],(function(a,e,r,n,t,i,s,c){"use strict";var u=32767,o=new n.Cartographic,f=new r.Cartesian3;function p(a,e,t,s,c){var p=a.length/3,C=a.subarray(0,p),d=a.subarray(p,2*p),b=a.subarray(2*p,3*p);i.AttributeCompression.zigZagDeltaDecode(C,d,b);for(var l=new Float64Array(a.length),w=0;w<p;++w){var h=C[w],v=d[w],y=b[w],k=r.CesiumMath.lerp(e.west,e.east,h/u),g=r.CesiumMath.lerp(e.south,e.north,v/u),A=r.CesiumMath.lerp(t,s,y/u),m=n.Cartographic.fromRadians(k,g,A,o),x=c.cartographicToCartesian(m,f);r.Cartesian3.pack(x,l,3*w)}return l}var C=new n.Rectangle,d=new n.Ellipsoid,b=new r.Cartesian3,l={min:void 0,max:void 0};function w(a){a=new Float64Array(a);var e=0;l.min=a[e++],l.max=a[e++],n.Rectangle.unpack(a,e,C),e+=n.Rectangle.packedLength,n.Ellipsoid.unpack(a,e,d),e+=n.Ellipsoid.packedLength,r.Cartesian3.unpack(a,e,b)}var h=new r.Cartesian3,v=new r.Cartesian3,y=new r.Cartesian3,k=new r.Cartesian3,g=new r.Cartesian3;function A(a,e){var n=new Uint16Array(a.positions),t=new Uint16Array(a.widths),i=new Uint32Array(a.counts),c=new Uint16Array(a.batchIds);w(a.packedBuffer);var u,o=C,f=d,A=b,m=l.min,x=l.max,E=p(n,o,m,x,f),D=E.length/3,I=4*D-4,R=new Float32Array(3*I),T=new Float32Array(3*I),U=new Float32Array(3*I),F=new Float32Array(2*I),N=new Uint16Array(I),P=0,L=0,M=0,S=0,_=i.length;for(u=0;u<_;++u){for(var G=i[u],W=t[u],B=c[u],z=0;z<G;++z){var H;if(0===z){var O=r.Cartesian3.unpack(E,3*S,h),Y=r.Cartesian3.unpack(E,3*(S+1),v);H=r.Cartesian3.subtract(O,Y,y),r.Cartesian3.add(O,H,H)}else H=r.Cartesian3.unpack(E,3*(S+z-1),y);var Z,j=r.Cartesian3.unpack(E,3*(S+z),k);if(z===G-1){var q=r.Cartesian3.unpack(E,3*(S+G-1),h),J=r.Cartesian3.unpack(E,3*(S+G-2),v);Z=r.Cartesian3.subtract(q,J,g),r.Cartesian3.add(q,Z,Z)}else Z=r.Cartesian3.unpack(E,3*(S+z+1),g);r.Cartesian3.subtract(H,A,H),r.Cartesian3.subtract(j,A,j),r.Cartesian3.subtract(Z,A,Z);for(var K=0===z?2:0,Q=z===G-1?2:4,V=K;V<Q;++V){r.Cartesian3.pack(j,R,P),r.Cartesian3.pack(H,T,P),r.Cartesian3.pack(Z,U,P),P+=3;var X=V-2<0?-1:1;F[L++]=V%2*2-1,F[L++]=X*W,N[M++]=B}}S+=G}var $=s.IndexDatatype.createTypedArray(I,6*D-6),aa=0,ea=0;for(_=D-1,u=0;u<_;++u)$[ea++]=aa,$[ea++]=aa+2,$[ea++]=aa+1,$[ea++]=aa+1,$[ea++]=aa+2,$[ea++]=aa+3,aa+=4;return e.push(R.buffer,T.buffer,U.buffer),e.push(F.buffer,N.buffer,$.buffer),{indexDatatype:2===$.BYTES_PER_ELEMENT?s.IndexDatatype.UNSIGNED_SHORT:s.IndexDatatype.UNSIGNED_INT,currentPositions:R.buffer,previousPositions:T.buffer,nextPositions:U.buffer,expandAndWidth:F.buffer,batchIds:N.buffer,indices:$.buffer}}var m=c(A);return m}));