import EngineController from '../index'
import { OverlayOp, TagItem } from '../interface/index'
import type { ModTagInstance } from 'BimEngine'

const BimEngine = window.BimEngine

/**
 * 添加标签,点，线，面  点线面示例：http://139.224.111.10:7000/codebox/#/?route=%E6%A0%87%E6%B3%A8-%E5%87%A0%E4%BD%95  图片标注示例：http://139.224.111.10:7000/codebox/#/?route=%E5%9B%BE%E7%89%87%E6%A0%87%E6%B3%A8
 */
export const addBillboard = async (_this: EngineController, option: OverlayOp) => {
  const billboard = await _this.markCollection?.create(option)
  console.log(option)
  _this.overlayList.billboard.push(billboard)
  return Promise.resolve(billboard)
}

export const addHtml = async (_this: EngineController, log, lat, height, html, newCzml?: any) => {
  const cartesian = _this.geoToWorld(log, lat, height)
  if (!cartesian) {
    console.log('转换世界坐标失败')
    return
  }

  const czml = [
    {
      id: 'document',
      name: 'Basic CZML billboard and label',
      version: '1.0'
    },
    {
      id: 'some-unique-id',
      name: 'xx',
      billboard: {
        // 图片
        image: 'https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png',
        // 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAEvUlEQVRYw+2Y22scVRzHP78zM3uZyWY3abKJl6I2VkLAS4qCCFVRURSh9T/QB1EffPFBBBXf9U189EEQ0Qd9sC8WpXgjIlrwgmirhcam1mQT3Sa72ezszDk/H3biGi8tdrf6sl8YznBmzu98z+/yPWcGhhhiiCHOifp7d83r+7f7FzreXGyCRu0lK51c/kLHy6AJqaoBvMy2FRHbjz0ZMLnC2tpaADAxMVHs9rZ8CDdEpPmfE9xcfPUSv1i5I1e9/y0g7PZuddvYdNu86RKN21vkSydFJPk3c/h9eMsnroWYsAyNCEq6tbUVFYsobJNLQ8gpaTvCkwppo7q8vPzF9PT05sA9qKpBdhsA1Ov1YGxszAeCOD5dyjd/vAXiq0E8TPgTpT1fpxScT+ZBsUG89Pasv+vGRa9UfEfkqvbFJBhuE6X2zmOath9HtLLDsJgG/ugblOYPIeKB677vmWKaJqtBoXp40AQrXWItwAvjOJb8L+++4FznwDknMMVPZWL/S4jvOdeJDLKdVoelUP1hIDrYPvXiTK8IvBAg+PXIg66zceC8C+vUb9aznx90rhP9wS0hxt02EKGuL9x3Q+fMh89AMgnJJEkakbpJtlYe1s5ZNO5d2ASc7ujTzlnc5ql7jaQlI1pGk0lcEjlrZ7RZm+67iiu3vLbYXv74lT/ELGT9y72atiZ3hHLkaqR6Vzdr6kfR+ue9h8lm5JpLM2bkipVtDxpcgNeaA5b7lJn8SGHXnS3SuAKQYgOTNKpqsyLMWlPZh0g3IDJ+E+nKeztz0W6OobYJLkTBOQJjzJUDyEETEbhu/okLfdGQXOkvYmvjtb+97zHMK5rZQUMjLsTa6gCEOs1jXYRzAWhoAcKrNkgPtQHUuQKAW3qTdOpuMDmoHcHrrG/rjQUwxak26iqgoBomPy9UvV3Xn+ifYJIIRkLIdNDFBU/8fOqPfEvanPvdQfEqcuq13jiv0HNerryMCXKZXoW29XPJNmvjtnF4qn+CzrQgiXAaIBp64vsAZmz+M3v67XnVf5AX+/tG4ZnJ/Yu4NBNyLXrFKuy+dd3zw4/gyT5zsGVrQIho2Js9jUzlhlXyE8fPm8GFas0rzzay1WY2bOTlSjnEO9p3kcj4+DpqN5xq2amtoMkkaIgQensf/QCX1rXT1bs/X6ht+7sPfg+uAukkuKjbaoi4JRmdPT6QnQQ1R7VTe0KTX+ez+EVObQQQ7H3kiPgj9b8sLCi1g5mHviE32kuj7Sp2LkL05YHuxTZee9q49uXbm76zXYImPnOlpo1xu7KwxzZO+KB4pT3Wn9q/QlBOCSoboEXNikzAR/UzGb32gcEeFlaPlYiKzyIy7tRGODBGi9ppjolrldG0kJH3VDFi/G75BOUEQNFAXCeynXakncadwWX3fDLwE7U2T07j8RxOLwUXoBqCAhoqiqiGCIqzEZIpRBZWVQKETRmZm7LNEw/4o7OHLsqRX/VkxW2mTxm4eUdlbueX2mzXwe8twIWoLJDog+y6viEiGxf9m0Qbx+5A5GHUXqeqgRjxUReABqgGKAGigTp3TESfl/K+1/+Xrzrd+O4ai73VgznETePwEVdH5Ts8+76M7Ptq+PtiiCGGGOLC8RtKLDgkovpfDQAAAABJRU5ErkJggg==',
        scale: 1,
        disableDepthTestDistance: 999999,
        horizontalOrigin: 'CENTER',
        verticalOrigin: 'BOTTOM'
      },
      // label: {
      //   // 这个是文字 UE 建议使用图片，文字的渲染有些许问题
      //   disableDepthTestDistance: 9999,
      //   fillColor: {
      //     rgba: [255, 255, 255, 255]
      //   },
      //   font: '12pt Lucida Console',
      //   horizontalOrigin: 'LEFT',
      //   pixelOffset: {
      //     cartesian2: [8, 0]
      //   },
      //   style: 'FILL',
      //   text: '这个是文字',
      //   showBackground: true,
      //   backgroundColor: {
      //     rgba: [112, 89, 57, 200]
      //   }
      // },
      position: {
        cartesian: [0, 0, 0]
      }
    }
  ]
  if (newCzml) {
    czml[1] = newCzml
  }
  const tag: ModTagInstance = new BimEngine.ModTagInstance()
  tag.isVirMod = true
  tag.setTag(czml)
  const imgBaseUrl = await BimEngine.BimEngineCore.renderHTMLToImage(html)
  czml[1].billboard.image = imgBaseUrl

  // // tag.projectId = projId
  // tag.position = pickPosition
  // tag.updatePosition()

  // await tag.html2Img(html)
  tag.setPosition(cartesian)
  _this.currentProject.project.addMod(tag)
  // ue 渲染标签
  _this.viewer.ueViewer.renderTag(tag)
}

export const addCzml = async (_this: EngineController, log, lat, height, czml, tagStr: string = 'TagPoint', clearable: boolean = true, depth?: number) => {
  const cartesian = _this.geoToWorld(log, lat, height)
  console.log(cartesian)
  addCzmlByCartesian(_this, cartesian, czml, tagStr, clearable, depth)
}

export const addCzmlByCartesian = async (_this: EngineController, cartesian, czml, tagStr: string = 'TagPoint', clearable: boolean = true, depth?: number) => {
  const newCzml = [
    {
      id: 'document',
      name: 'Basic CZML billboard and label',
      version: '1.0'
    }
    // {
    //   id: 'some-unique-id',
    //   name: 'xx',
    //   billboard: {
    //     // 图片
    //     image: 'https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png',
    //     // 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAEvUlEQVRYw+2Y22scVRzHP78zM3uZyWY3abKJl6I2VkLAS4qCCFVRURSh9T/QB1EffPFBBBXf9U189EEQ0Qd9sC8WpXgjIlrwgmirhcam1mQT3Sa72ezszDk/H3biGi8tdrf6sl8YznBmzu98z+/yPWcGhhhiiCHOifp7d83r+7f7FzreXGyCRu0lK51c/kLHy6AJqaoBvMy2FRHbjz0ZMLnC2tpaADAxMVHs9rZ8CDdEpPmfE9xcfPUSv1i5I1e9/y0g7PZuddvYdNu86RKN21vkSydFJPk3c/h9eMsnroWYsAyNCEq6tbUVFYsobJNLQ8gpaTvCkwppo7q8vPzF9PT05sA9qKpBdhsA1Ov1YGxszAeCOD5dyjd/vAXiq0E8TPgTpT1fpxScT+ZBsUG89Pasv+vGRa9UfEfkqvbFJBhuE6X2zmOath9HtLLDsJgG/ugblOYPIeKB677vmWKaJqtBoXp40AQrXWItwAvjOJb8L+++4FznwDknMMVPZWL/S4jvOdeJDLKdVoelUP1hIDrYPvXiTK8IvBAg+PXIg66zceC8C+vUb9aznx90rhP9wS0hxt02EKGuL9x3Q+fMh89AMgnJJEkakbpJtlYe1s5ZNO5d2ASc7ujTzlnc5ql7jaQlI1pGk0lcEjlrZ7RZm+67iiu3vLbYXv74lT/ELGT9y72atiZ3hHLkaqR6Vzdr6kfR+ue9h8lm5JpLM2bkipVtDxpcgNeaA5b7lJn8SGHXnS3SuAKQYgOTNKpqsyLMWlPZh0g3IDJ+E+nKeztz0W6OobYJLkTBOQJjzJUDyEETEbhu/okLfdGQXOkvYmvjtb+97zHMK5rZQUMjLsTa6gCEOs1jXYRzAWhoAcKrNkgPtQHUuQKAW3qTdOpuMDmoHcHrrG/rjQUwxak26iqgoBomPy9UvV3Xn+ifYJIIRkLIdNDFBU/8fOqPfEvanPvdQfEqcuq13jiv0HNerryMCXKZXoW29XPJNmvjtnF4qn+CzrQgiXAaIBp64vsAZmz+M3v67XnVf5AX+/tG4ZnJ/Yu4NBNyLXrFKuy+dd3zw4/gyT5zsGVrQIho2Js9jUzlhlXyE8fPm8GFas0rzzay1WY2bOTlSjnEO9p3kcj4+DpqN5xq2amtoMkkaIgQensf/QCX1rXT1bs/X6ht+7sPfg+uAukkuKjbaoi4JRmdPT6QnQQ1R7VTe0KTX+ez+EVObQQQ7H3kiPgj9b8sLCi1g5mHviE32kuj7Sp2LkL05YHuxTZee9q49uXbm76zXYImPnOlpo1xu7KwxzZO+KB4pT3Wn9q/QlBOCSoboEXNikzAR/UzGb32gcEeFlaPlYiKzyIy7tRGODBGi9ppjolrldG0kJH3VDFi/G75BOUEQNFAXCeynXakncadwWX3fDLwE7U2T07j8RxOLwUXoBqCAhoqiqiGCIqzEZIpRBZWVQKETRmZm7LNEw/4o7OHLsqRX/VkxW2mTxm4eUdlbueX2mzXwe8twIWoLJDog+y6viEiGxf9m0Qbx+5A5GHUXqeqgRjxUReABqgGKAGigTp3TESfl/K+1/+Xrzrd+O4ai73VgznETePwEVdH5Ts8+76M7Ptq+PtiiCGGGOLC8RtKLDgkovpfDQAAAABJRU5ErkJggg==',
    //     scale: 1,
    //     disableDepthTestDistance: 999999,
    //     horizontalOrigin: 'CENTER',
    //     verticalOrigin: 'BOTTOM'
    //   }
    //   position: {
    //     cartesian: [0, 0, 0]
    //   }
    // }
  ]
  newCzml.push(czml)
  const tag: ModTagInstance = new BimEngine.ModTagInstance()
  tag.isVirMod = true
  tag.setTag(newCzml)
  tag.setPosition(cartesian)
  if (depth !== undefined) {
    tag.setDepth(depth)
  }
  _this.currentProject.project.addMod(tag)

  // ue 渲染标签
  _this.viewer.ueViewer.renderTag(tag)

  const tagItem: TagItem = {
    tagInstance: tag,
    tag: tagStr,
    clearable
  }
  _this.overlayList.czmlList.push(tagItem)
}

export const removeCzmlByTagstr = (_this: EngineController, tagStr: string) => {
  const list = _this.overlayList.czmlList.filter((_) => _.tag === tagStr)
  list.forEach((item) => {
    const tagItem = item as TagItem
    _this.viewer.ueViewer.deleteMod(tagItem.tagInstance)
  })
  _this.overlayList.czmlList = _this.overlayList.czmlList.filter((_) => _.tag !== tagStr)
}

export const clearCzml = (_this: EngineController) => {
  _this.overlayList.czmlList
    .filter((_) => _.clearable)
    .forEach((item) => {
      const tagItem = item as TagItem
      _this.viewer.ueViewer.deleteMod(tagItem.tagInstance)
    })
  _this.overlayList.czmlList = _this.overlayList.czmlList.filter((_) => !_.clearable)
}
