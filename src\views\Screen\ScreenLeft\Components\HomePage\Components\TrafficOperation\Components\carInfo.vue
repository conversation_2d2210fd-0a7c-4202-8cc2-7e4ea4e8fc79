<template>
  <div class="car-info-container">
    <div class="car-info-item">
      <div class="line1">
        <div class="number-container">
          <span class="number">{{ sum }}</span>
          万辆
          <span class="red">
            {{ data?.pro || '-' }}%
            <img src="@/assets/ScreenLeft/TrafficSituation/up.png" />
          </span>
        </div>
      </div>
      <div class="line2">
        总车流量
        <span>环比率</span>
      </div>
    </div>
    <div class="car-info-item">
      <div class="line1">
        <div class="number-container">
          <span class="number">{{ data?.avgspeed ?? '-' }}</span>
          万辆
        </div>
      </div>
      <div class="line2">平均车速</div>
    </div>
    <div class="car-info-item">
      <div class="line1">
        <div class="number-container">
          <span class="number">{{ data?.avg ?? '-' }}</span>
          万辆
        </div>
      </div>
      <div class="line2">时段流量</div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })

  const sum = computed(() => {
    if (props.data?.sum) {
      return (props.data.sum / 10000).toFixed(2)
    }
    return '-'
  })
</script>

<style lang="scss" scoped>
  .car-info-container {
    display: flex;
    justify-content: space-between;
    width: 480px;
    height: 68px;
    .car-info-item {
      width: 149px;
      height: 68px;
      padding: 8px 10px;
      background: url('@/assets/ScreenLeft/TrafficSituation/bg.png');
      .line1 {
        width: 100%;
        font-family: Alibaba-PuHuiTi;
        font-size: 12px;
        font-weight: normal;
        line-height: 23px;
        color: #ffffff;
        text-align: center;
        .number {
          font-size: 21px;
          font-weight: bold;
        }
      }
      .red {
        color: red;
      }
      .line2 {
        width: 100%;
        font-family: Alibaba-PuHuiTi;
        font-size: 16px;
        font-weight: normal;
        line-height: 30px;
        color: #a8d6ff;
        text-align: center;
        span {
          margin-left: 8px;
          font-size: 12px;
        }
      }
    }
  }
</style>
