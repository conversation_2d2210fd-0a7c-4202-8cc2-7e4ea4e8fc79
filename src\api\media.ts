import requestVideo from '@/utils/requestVideo'

export const mediaApi = {
  start: (params) =>
    requestVideo({
      url: 'rest/media/start',
      method: 'post',
      data: params
    }),
  stop: (params) =>
    requestVideo({
      url: 'rest/media/stop',
      method: 'post',
      data: params
    }),
  keepalive: (params) =>
    requestVideo({
      url: 'rest/media/keepalive',
      method: 'post',
      data: params
    })
}
