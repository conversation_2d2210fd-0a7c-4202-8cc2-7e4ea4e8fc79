define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./RectangleGeometryLibrary-a7195bb5"],(function(e,t,i,r,a,n,o,l,u,s,d,p,c,f,g,h,y,b){"use strict";var m=new a.BoundingSphere,v=new a.BoundingSphere,_=new i.Cartesian3,E=new r.Rectangle;function A(e,t){var i=e._ellipsoid,r=t.height,a=t.width,n=t.northCap,o=t.southCap,l=r,u=2,g=0,h=4;n&&(u-=1,l-=1,g+=1,h-=2),o&&(u-=1,l-=1,g+=1,h-=2),g+=u*a+2*l-h;var y,m=new Float64Array(3*g),v=0,E=0,A=_;if(n)b.RectangleGeometryLibrary.computePosition(t,i,!1,E,0,A),m[v++]=A.x,m[v++]=A.y,m[v++]=A.z;else for(y=0;y<a;y++)b.RectangleGeometryLibrary.computePosition(t,i,!1,E,y,A),m[v++]=A.x,m[v++]=A.y,m[v++]=A.z;for(y=a-1,E=1;E<r;E++)b.RectangleGeometryLibrary.computePosition(t,i,!1,E,y,A),m[v++]=A.x,m[v++]=A.y,m[v++]=A.z;if(E=r-1,!o)for(y=a-2;y>=0;y--)b.RectangleGeometryLibrary.computePosition(t,i,!1,E,y,A),m[v++]=A.x,m[v++]=A.y,m[v++]=A.z;for(y=0,E=r-2;E>0;E--)b.RectangleGeometryLibrary.computePosition(t,i,!1,E,y,A),m[v++]=A.x,m[v++]=A.y,m[v++]=A.z;for(var w=m.length/3*2,G=f.IndexDatatype.createTypedArray(m.length/3,w),R=0,P=0;P<m.length/3-1;P++)G[R++]=P,G[R++]=P+1;G[R++]=m.length/3-1,G[R++]=0;var D=new d.Geometry({attributes:new c.GeometryAttributes,primitiveType:p.PrimitiveType.LINES});return D.attributes.position=new d.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m}),D.indices=G,D}function w(e,t){var i=e._surfaceHeight,r=e._extrudedHeight,a=e._ellipsoid,n=r,o=i,l=A(e,t),u=t.height,s=t.width,d=y.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,o,a,!1),p=d.length,c=new Float64Array(2*p);c.set(d);var g=y.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,n,a);c.set(g,p),l.attributes.position.values=c;var h=t.northCap,b=t.southCap,m=4;h&&(m-=1),b&&(m-=1);var v=2*(c.length/3+m),_=f.IndexDatatype.createTypedArray(c.length/3,v);p=c.length/6;for(var E,w=0,G=0;G<p-1;G++)_[w++]=G,_[w++]=G+1,_[w++]=G+p,_[w++]=G+p+1;if(_[w++]=p-1,_[w++]=0,_[w++]=p+p-1,_[w++]=p,_[w++]=0,_[w++]=p,h)E=u-1;else{var R=s-1;_[w++]=R,_[w++]=R+p,E=s+u-2}if(_[w++]=E,_[w++]=E+p,!b){var P=s+E-1;_[w++]=P,_[w]=P+p}return l.indices=_,l}function G(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var n=a.rectangle,o=e.defaultValue(a.granularity,i.CesiumMath.RADIANS_PER_DEGREE),l=e.defaultValue(a.ellipsoid,r.Ellipsoid.WGS84),u=e.defaultValue(a.rotation,0);if(!e.defined(n))throw new t.DeveloperError("rectangle is required.");if(r.Rectangle.validate(n),n.north<n.south)throw new t.DeveloperError("options.rectangle.north must be greater than options.rectangle.south");var s=e.defaultValue(a.height,0),d=e.defaultValue(a.extrudedHeight,s);this._rectangle=r.Rectangle.clone(n),this._granularity=o,this._ellipsoid=l,this._surfaceHeight=Math.max(s,d),this._rotation=u,this._extrudedHeight=Math.min(s,d),this._offsetAttribute=a.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}G.packedLength=r.Rectangle.packedLength+r.Ellipsoid.packedLength+5,G.pack=function(i,a,n){if(!e.defined(i))throw new t.DeveloperError("value is required");if(!e.defined(a))throw new t.DeveloperError("array is required");return n=e.defaultValue(n,0),r.Rectangle.pack(i._rectangle,a,n),n+=r.Rectangle.packedLength,r.Ellipsoid.pack(i._ellipsoid,a,n),n+=r.Ellipsoid.packedLength,a[n++]=i._granularity,a[n++]=i._surfaceHeight,a[n++]=i._rotation,a[n++]=i._extrudedHeight,a[n]=e.defaultValue(i._offsetAttribute,-1),a};var R=new r.Rectangle,P=r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),D={rectangle:R,ellipsoid:P,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};G.unpack=function(i,a,n){if(!e.defined(i))throw new t.DeveloperError("array is required");a=e.defaultValue(a,0);var o=r.Rectangle.unpack(i,a,R);a+=r.Rectangle.packedLength;var l=r.Ellipsoid.unpack(i,a,P);a+=r.Ellipsoid.packedLength;var u=i[a++],s=i[a++],d=i[a++],p=i[a++],c=i[a];return e.defined(n)?(n._rectangle=r.Rectangle.clone(o,n._rectangle),n._ellipsoid=r.Ellipsoid.clone(l,n._ellipsoid),n._surfaceHeight=s,n._rotation=d,n._extrudedHeight=p,n._offsetAttribute=-1===c?void 0:c,n):(D.granularity=u,D.height=s,D.rotation=d,D.extrudedHeight=p,D.offsetAttribute=-1===c?void 0:c,new G(D))};var L=new r.Cartographic;function C(t,i){return e.defined(i)&&(t=G.unpack(t,i)),t._ellipsoid=r.Ellipsoid.clone(t._ellipsoid),t._rectangle=r.Rectangle.clone(t._rectangle),G.createGeometry(t)}return G.createGeometry=function(t){var r,n,o=t._rectangle,l=t._ellipsoid,u=b.RectangleGeometryLibrary.computeOptions(o,t._granularity,t._rotation,0,E,L);if(!i.CesiumMath.equalsEpsilon(o.north,o.south,i.CesiumMath.EPSILON10)&&!i.CesiumMath.equalsEpsilon(o.east,o.west,i.CesiumMath.EPSILON10)){var c,f=t._surfaceHeight,h=t._extrudedHeight,_=!i.CesiumMath.equalsEpsilon(f,h,0,i.CesiumMath.EPSILON2);if(_){if(r=w(t,u),e.defined(t._offsetAttribute)){var G=r.attributes.position.values.length/3,R=new Uint8Array(G);t._offsetAttribute===g.GeometryOffsetAttribute.TOP?R=g.arrayFill(R,1,0,G/2):(c=t._offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1,R=g.arrayFill(R,c)),r.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:R})}var P=a.BoundingSphere.fromRectangle3D(o,l,f,v),D=a.BoundingSphere.fromRectangle3D(o,l,h,m);n=a.BoundingSphere.union(P,D)}else{if(r=A(t,u),r.attributes.position.values=y.PolygonPipeline.scaleToGeodeticHeight(r.attributes.position.values,f,l,!1),e.defined(t._offsetAttribute)){var C=r.attributes.position.values.length,x=new Uint8Array(C/3);c=t._offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1,g.arrayFill(x,c),r.attributes.applyOffset=new d.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:x})}n=a.BoundingSphere.fromRectangle3D(o,l,f)}return new d.Geometry({attributes:r.attributes,indices:r.indices,primitiveType:p.PrimitiveType.LINES,boundingSphere:n,offsetAttribute:t._offsetAttribute})}},C}));