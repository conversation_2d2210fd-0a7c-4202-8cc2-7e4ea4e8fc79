<template>
  <div class="ScreenTop">
    <slot></slot>
    <Duty></Duty>
    <Right></Right>
  </div>
</template>

<script setup>
  import Duty from './Components/duty.vue'
  import Right from './Components/right.vue'

  defineOptions({
    name: 'ScreenTop'
  })
</script>
<style lang="scss" scoped>
  .ScreenTop {
    position: absolute;
    z-index: 999;
    width: $screen-width;
    height: $screen-top-height;
    background: url('../../../assets/ScreenTop/topBg.png');
    background-size: cover;
    .title {
      position: absolute;
      top: 22px;
      left: 50%;
      width: 772px;
      height: 40px;
      margin-left: -386px;
      font-family: PangMenZhengDao;
      font-size: 32px;
      color: #ffffff;

      //text-shadow: 0px 4px 6px #001437;
      //background: linear-gradient(180deg, #FFFFFF 0%, #B3D5FF 100%);
      //background-clip: text;
      //-webkit-text-fill-color: transparent;
    }
  }
</style>
