define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./AttributeCompression-a01059cd","./createTaskProcessorWorker"],(function(a,e,r,t,n,i){"use strict";var s=32767,o=new t.Cartographic,c=new r.Cartesian3,u=new t.Rectangle,p=new t.Ellipsoid,l={min:void 0,max:void 0};function f(a){a=new Float64Array(a);var e=0;l.min=a[e++],l.max=a[e++],t.Rectangle.unpack(a,e,u),e+=t.Rectangle.packedLength,t.Ellipsoid.unpack(a,e,p)}function h(a,e){var i=new Uint16Array(a.positions);f(a.packed<PERSON>uffer);var h=u,d=p,g=l.min,m=l.max,C=i.length/3,b=i.subarray(0,C),v=i.subarray(C,2*C),w=i.subarray(2*C,3*C);n.AttributeCompression.zigZagDeltaDecode(b,v,w);for(var k=new Float64Array(i.length),y=0;y<C;++y){var A=b[y],R=v[y],x=w[y],M=r.CesiumMath.lerp(h.west,h.east,A/s),D=r.CesiumMath.lerp(h.south,h.north,R/s),E=r.CesiumMath.lerp(g,m,x/s),F=t.Cartographic.fromRadians(M,D,E,o),T=d.cartographicToCartesian(F,c);r.Cartesian3.pack(T,k,3*y)}return e.push(k.buffer),{positions:k.buffer}}var d=i(h);return d}));