<template>
  <div class="traffic-operation-container">
    <HeadLine title-bg="ScreenLeft/TrafficOperation/<EMAIL>">
      <DatePicker v-model="date" @change="handleDateChange" :disabled-date="disabledDate" style="top: -58px; right: 43px"></DatePicker>
      <!-- <template #title>交通态势</template> -->
      <div class="content">
        <CarSummary></CarSummary>
        <!-- <CarInfo :data="state.data"></CarInfo> -->
        <div class="divider-container"></div>
        <CarDetails :data="state.data"></CarDetails>
        <div class="divider-container"></div>
        <CarFlowChat style="margin-top: -10px" :data="state.data?.data"></CarFlowChat>
      </div>
    </HeadLine>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import moment from 'moment'

  import CarDetails from './Components/carDetails.vue'
  import CarFlowChat from './Components/carFlowChat.vue'
  import CarInfo from './Components/carInfo.vue'
  import CarSummary from './Components/carSummary.vue'
  import DatePicker from '@/components/DatePicker/index.vue'
  import HeadLine from '@/components/HeadLine/index.vue'

  import { trafficDataApi } from '@/api/index'

  const state = reactive({
    data: {}
  })
  const date = ref('')
  onMounted(() => {
    date.value = moment().format('YYYY-MM-DD')
    getData()
  })

  const getData = () => {
    trafficDataApi.getDayData({ day: date.value }).then((res) => {
      console.log(res, '1111111111111111')
      if (res.success) {
        state.data = res.result
      }
    })
  }

  const handleDateChange = (val) => {
    getData()
  }

  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
</script>

<style lang="scss" scoped>
  .traffic-operation-container {
    .content {
      width: 480px;
      height: 325px;
      .divider-container {
        width: 480px;
        height: 1px;
        margin-top: 12px;
        margin-bottom: 12px;
        background: url('@/assets/ScreenLeft/TrafficSituation/divider.png');
      }
    }
  }
</style>
