<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 10:42:26
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 13:57:05
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\EmergencyResources\team.vue
 * @Description:
 *
-->
<template>
  <div class="team-container">
    <div class="head">队伍</div>
    <div class="team-content">
      <div class="content-item" v-for="(item, index) in list" :key="index">
        <img class="icon" :src="getAssetsFile(`ScreenLeft/EventManage/${item.icon}.png`)" alt="" />
        <div class="name">{{ item.name }}</div>
        <span class="num">{{ item.num }}</span>
        <span class="unit">{{ item.unit }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import SmallHeadLine from '@/components/SmallHeadLine/index.vue'

  import { getAssetsFile } from '@/utils'
  const list = ref([
    { name: '应急队伍', icon: 'team', num: 4, unit: '队' },
    { name: '队伍人数', icon: 'num', num: 24, unit: '人' }
  ])
</script>

<style lang="scss" scoped>
  .team-container {
    display: flex;
    gap: 47px;
    align-items: center;
    width: 637px;
    height: 67px;
    padding: 16px 54px 8px 25px;
    background: url('@/assets/ScreenLeft/EventManage/teamBg.png') no-repeat;
    background-size: 100% 100%;
    .head {
      width: 48px;
      height: 40px;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/ScreenLeft/EventManage/titleLine.png') no-repeat;
      background-position: 100% 100%;
    }
    .team-content {
      display: flex;
      justify-content: space-between;
      width: 464px;
      height: 47px;
      .content-item {
        display: flex;
        gap: 10px;
        align-items: center;
        width: 220px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-weight: 400;
        color: #c3edff;
        .icon {
          width: 53px;
          height: 48px;
        }
        .name {
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          font-weight: 400;
          line-height: 22px;
          color: #d8f3ff;
        }
        .num {
          margin-right: 3px;
          font-family: PangMenZhengDao;
          font-size: 28px;
          font-weight: 400;
          color: #48e6ff;
        }
        .unit {
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
        }
      }
    }
  }
</style>
