<template>
  <!-- v-loading="isLoading" element-loading-background="rgba(122, 122, 122, 0)" -->
  <div class="work-control-container">
    <SubTitle :tabs="['养护运营', '节点任务']" @tab-change="changeTab" />
    <OpenPage style="top: -12px; right: 210px" :path="curPath" :params="{ date }" />
    <div>
      <TimeRangePicker style="top: -14px; right: -20px" v-model="date" @update:model-value="handleDateChange" @update:selectedTab="handleSelectedTabChange" />
    </div>
    <div class="tab">
      <div
        class="tab-item"
        :class="{
          active: item.active,
          'cursor-pointer': true
        }"
        v-for="(item, index) in tabList"
        :key="index"
        @click="handlerTab(item, index)">
        <div class="content">
          <img :src="getAssetsFile(`ScreenLeft/DynamicMaintenance/${item.icon}.png`)" />
        </div>
        <div class="text">
          <span>{{ item.name }}</span>
          <div class="num" :style="{ color: item.color }">{{ item.num }}</div>
        </div>
      </div>
    </div>
    <div class="img-box">
      <div class="prev" @click="prev"></div>
      <div class="next" @click="next"></div>
      <el-carousel ref="refCarousel" class="imgs" height="283px" indicator-position="none" arrow="never" :autoplay="false">
        <!-- 循环生成 -->
        <el-carousel-item v-if="tabCur === 0" v-for="(group, pageIndex) in groupedImgList" :key="pageIndex">
          <div class="image-group">
            <!-- 直接遍历分组后的图片列表 -->
            <div class="image" v-for="(item, index) in group" :key="index">
              <el-image :src="item.url" @click="handleImg(item)">
                <!-- 可以添加加载中的占位图 -->
                <template #placeholder>
                  <div class="c-#fff text-14 lh-130 text-center">加载中...</div>
                </template>
                <!-- 可以添加加载失败的占位图 -->
                <template #error>
                  <div class="flex items-center justify-center w-237 h-132 c-#ccc">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <!-- <img width="237" height="132" :src="item.url" alt="" @click="handleImg(item.url)" /> -->
              <ElTooltip effect="dark" :content="item.name" placement="top">
                <div class="title">{{ lib.utils.getMiddleEllipsis(item.name, 15) }}</div>
              </ElTooltip>
            </div>
          </div>
        </el-carousel-item>
        <el-carousel-item v-if="tabCur === 1" v-for="(group, groupIndex) in groupedDocIds" :key="groupIndex">
          <div class="image-group">
            <!-- 每页显示 4 个图片 -->
            <div class="image" v-for="(item, index) in group" :key="index">
              <ImgCard :name="item.name" :doc-id="item.docId || 544" width="237" height="132" only-one />
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, watch, computed, getCurrentInstance } from 'vue'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'
  import SubTitle from '@/components/SubTitle/index.vue'
  import OpenPage from '@/components/OpenPage/index.vue'
  import ImgCard from '@/components/ImgCard/index.vue'
  import { getAssetsFile } from '@/utils'
  import lib from '@/utils/lib'
  import TimeRangePicker from '@/components/TimeRangePicker/index.vue'
  import { ElTooltip } from 'element-plus'
  import MissionList from '@/views/Screen/MapPopupWindow/Components/MissionList/index.vue'
  // 定义常量
  const PAGE_SIZE = 4
  // 响应式数据
  const tabCur = ref(0) // 主tab
  const activeIndex = ref(0) // 子tab
  const curPath = ref('/inventory/operations')
  const tabList1 = ref([
    {
      name: '应完成总量',
      num: 0,
      icon: 'contentNum',
      active: true,
      color: '#48E6FF'
    },
    {
      name: '已完成',
      titleName: '养护作业列表',
      num: 0,
      icon: 'unfinishNum',
      active: false,
      color: '#FFD448'
    }
  ])
  const tabList2 = ref([
    {
      name: '节点任务数量',
      titleName: '任务列表',
      num: 0,
      icon: 'contentNum',
      active: true,
      color: '#48E6FF'
    },
    {
      name: '未完成数量',
      titleName: '未完成列表',
      num: 0,
      icon: 'unfinishNum',
      active: false,
      color: '#FFD448'
    }
  ])
  const tabList = ref(tabList1.value)
  const isLoading = ref(false)
  const type = ref('month')
  const queryParams = ref({
    startDate: moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    endDate: moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
  })
  const date = ref(moment().format('YYYY-MM'))
  const handleDate = ref(date.value)
  const refCarousel = ref(null)
  const docIds = ref([])
  const intervalTime = ref(null)
  const imgList = ref([]) // 图片列表
  const planList = ref([]) // 养护作业列表
  const missionData = ref([]) // 节点任务列表数据
  const shouldFinishList = ref([]) // 应完成总量列表

  const { appContext } = getCurrentInstance()

  const handleSelectedTabChange = (val) => {
    type.value = val
  }
  // 计算属性
  const groupedDocIds = computed(() => {
    const chunked = []
    for (let i = 0; i < docIds.value.length; i += PAGE_SIZE) {
      chunked.push(docIds.value.slice(i, i + PAGE_SIZE))
    }
    return chunked
  })
  const handleImg = (item) => {
    lib.utils.openImageZoom([item.url], item.name)
  }
  // 工具函数
  const getQuarterStartAndEnd = (year: number, quarter: number) => {
    const startMonth = (quarter - 1) * 3
    const endMonth = startMonth + 2

    const startDate = moment([year, startMonth, 1]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    const endDate = moment([year, endMonth, 1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')

    return { startDate, endDate }
  }

  const getMonthStartAndEnd = (year: number, month: number) => {
    const startDate = moment([year, month - 1])
      .startOf('month')
      .format('YYYY-MM-DD HH:mm:ss')
    const endDate = moment([year, month - 1])
      .endOf('month')
      .format('YYYY-MM-DD HH:mm:ss')

    return { startDate, endDate }
  }

  // 计算属性，将 imgList 按每页 4 个元素进行分组
  const groupedImgList = computed(() => {
    const result = []
    const size = 4
    for (let i = 0; i < imgList.value.length; i += size) {
      result.push(imgList.value.slice(i, i + size))
    }
    return result
  })

  // 处理日期变化
  const handleDateChange = (val: string) => {
    handleDate.value = val
  }

  // 切换轮播图
  const prev = () => {
    refCarousel.value?.prev()
    triggerLazyLoad()
  }

  const next = () => {
    refCarousel.value?.next()
    triggerLazyLoad()
  }
  const triggerLazyLoad = () => {
    // 模拟滚动事件，触发懒加载
    setTimeout(() => {
      window.dispatchEvent(new Event('scroll'))
    }, 100)
  }
  // 养护运营接口
  const getData = async () => {
    try {
      isLoading.value = true
      const objParams = {
        start: queryParams.value.startDate,
        end: queryParams.value.endDate,
        period: type.value
      }
      const res: any = await lib.api.bigscreenApi.workOrderStatistics(objParams)
      if (res.success) {
        tabList.value[0].num = res.result?.planTotal
        tabList.value[1].num = res.result?.finished
      }
    } catch (error) {
      console.error('养护运营数据获取失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  const getWorkImgList = async () => {
    // imgList.value = []
    const arr = []
    try {
      // 请求工作订单图片列表
      const res: any = await lib.api.getWorkControlApi.workOrderPicList({
        startDate: queryParams.value.startDate,
        endDate: queryParams.value.endDate
      })

      // 检查接口返回结果
      if (res.success && Array.isArray(res.result)) {
        // 添加基本数据到 imgList
        const allDllDocIds = []
        res.result.forEach((item) => {
          const url = item.url || '' // 确保 URL 有效
          arr.push({
            ...item,
            url // 使用上面定义的 URL
          })

          // 收集所有的 dllDocIds
          if (item.dllDocIds) {
            allDllDocIds.push(item.dllDocIds)
          }
        })
        imgList.value = arr
        // 如果有 dllDocIds，进行第二个 API 请求
        if (allDllDocIds.length > 0) {
          await fetchAndAddDocUrls(allDllDocIds)
        }
        console.log('imgList', imgList.value)
      }
    } catch (error) {
      console.error('养护运营图片列表获取失败:', error)
    }
  }

  // 新增函数：根据 dllDocIds 请求文档 URL
  const fetchAndAddDocUrls = async (dllDocIds) => {
    try {
      const docsRes: any = await lib.api.docDiyDiyApi.getDocs({ dllIds: dllDocIds })
      if (docsRes.success && Array.isArray(docsRes.result)) {
        // 遍历 imgList
        imgList.value.forEach((imgItem) => {
          if (imgItem.dllDocIds) {
            // 查找 docsRes.result 中与当前 imgItem 的 dllDocId 匹配的项
            const matchingDoc = docsRes.result.find((docItem) => docItem.dllDocId == imgItem.dllDocIds)
            if (matchingDoc && matchingDoc.fileName) {
              const newUrl = import.meta.env.VITE_DOC_URL + 'img/' + matchingDoc.fileName
              // 更新 imgList 中对应图片的 url
              imgItem.url = newUrl
            }
          }
        })
      }
    } catch (error) {
      console.error('获取文档信息失败:', error)
    }
  }

  // 节点任务接口
  const getNodeData = async () => {
    try {
      isLoading.value = true
      const objParams = {
        startDate: queryParams.value.startDate,
        endDate: queryParams.value.endDate,
        status: activeIndex.value === 1 ? [0, 1] : []
      }
      const res: any = await lib.api.bigscreenApi.pointTask(objParams)
      if (res.success) {
        tabList.value[0].num = res.result?.total
        tabList.value[1].num = res.result?.unfinished
        missionData.value = res.result?.list
      }
    } catch (error) {
      console.error('节点任务数据获取失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  const getNodeImgList = async () => {
    docIds.value = []
    try {
      const res: any = await lib.api.getWorkControlApi.taskPicList({
        startDate: queryParams.value.startDate,
        endDate: queryParams.value.endDate
      })
      if (res.success && Array.isArray(res.result)) {
        // 优化后的代码
        docIds.value = res.result.map((item) => ({
          docId: item?.docId || null,
          name: item?.name
        }))
      }
    } catch (error) {
      console.error('节点任务图片列表获取失败:', error)
    }
  }

  // 获取养护作业列表数据
  const getPlanList = async () => {
    try {
      const params = {
        period: 'month',
        startDate: queryParams.value.startDate,
        endDate: queryParams.value.endDate,
        // projectId: 28,
        ...(activeIndex.value === 1 ? { status: 'final' } : {})
      }
      const res: any = await lib.api.bigscreenApi.workOrderList(params)
      if (res.success) {
        planList.value = res?.result?.list
      }
    } catch (error) {
      console.error('养护作业列表数据获取失败:', error)
    }
  }
  // 获取应完成总量列表
  const getShouldFinishList = async () => {
    try {
      const params = {
        period: 'month',
        start: queryParams.value.startDate,
        end: queryParams.value.endDate
      }
      const res: any = await lib.api.bigscreenApi.workPlanShouldFinishList(params)
      if (res.success) {
        const list = res.result
        list.forEach((item) => {
          item.frequencyName = (item.frequency || '-') + '次/' + lib.utils.getDictNameByCode(item.frequencyUnit, 'cur_frequency_unit')

          item.workOrderTypeNName = lib.utils.getDictNameByCode(item.workOrderType, 'advqjsd_work_order_type')
        })
        shouldFinishList.value = list
      }
    } catch (error) {
      console.error('养护作业应完成总量列表数据获取失败:', error)
    }
  }
  // 更新参数并获取数据
  const updateParams = async () => {
    const year = moment(handleDate.value).year()
    const month = moment(handleDate.value).month() + 1
    const season = moment(handleDate.value).quarter()

    switch (type.value) {
      case 'year':
        queryParams.value.startDate = `${year}-01-01 00:00:00`
        queryParams.value.endDate = `${year}-12-31 23:59:59`
        break
      case 'quarter':
        const { startDate, endDate } = getQuarterStartAndEnd(year, season)
        queryParams.value.startDate = startDate
        queryParams.value.endDate = endDate
        break
      case 'month':
        const monthRange = getMonthStartAndEnd(year, month)
        queryParams.value.startDate = monthRange.startDate
        queryParams.value.endDate = monthRange.endDate
        break
    }

    if (tabCur.value === 0) {
      await getData()
      await getWorkImgList()
      await getPlanList()
      await getShouldFinishList()
    } else {
      await getNodeData()
      await getNodeImgList()
    }

    if (!intervalTime.value) {
      intervalTime.value = useIntervalFn(async () => {
        if (tabCur.value === 0) {
          await getData()
          await getWorkImgList()
          await getPlanList()
          await getShouldFinishList()
        } else {
          await getNodeData()
          await getNodeImgList()
        }
      }, 1000 * 60 * 2)
    }
  }

  // 监听日期和类型变化
  watch(
    () => [handleDate.value, type.value],
    () => {
      updateParams()
    },
    { immediate: true, deep: true }
  )

  // 切换主 tab
  const tabIndex = ref(0)
  const changeTab = (index: number) => {
    tabIndex.value = index
    curPath.value = index === 0 ? '/inventory/operations' : '/inventory/task'
    tabCur.value = index
    tabList.value = index === 0 ? tabList1.value : tabList2.value
    tabList.value[0].active = true
    tabList.value[1].active = false
    activeIndex.value = 0

    if (tabCur.value === 0) {
      getData()
      getPlanList()
      getShouldFinishList()
      getWorkImgList()
    } else {
      getNodeData()
      getNodeImgList()
    }
  }

  // 切换子 tab
  const handlerTab = async (item: any, index: number) => {
    tabList.value.forEach((data) => {
      data.active = data.name === item.name
    })
    activeIndex.value = index
    if (tabCur.value === 0) {
      if (planList.value.length === 0) {
        await getPlanList()
      }
      if (shouldFinishList.value.length === 0) {
        await getShouldFinishList()
      }
    } else {
      await getNodeData()
    }
    const currentTitle =
      tabCur.value === 0 ? (item.name === '应完成总量' ? '养护作业列表' : item.titleName) : item.name === '养护内容数量' ? '任务列表' : item.titleName
    lib.popWindow.removeDialog('MissionList')
    // debugger
    // if (item.name === '应完成总量') {
    //   return
    // }
    if (item.active) {
      lib.popWindow.createPopWindow(
        './Components/MissionList/index.vue',
        {
          left: 1596,
          top: 205,
          tag: 'MissionList',
          appContext,
          appendParent: 'player',
          closeFunc: () => {}
        },
        {
          title: currentTitle,
          list: tabCur.value === 0 ? (item.name === '应完成总量' ? shouldFinishList.value : planList.value) : missionData.value,
          item
        }
      )
    }
  }
</script>

<style lang="scss" scoped>
  .work-control-container {
    width: 617px;
    height: 439px;
    .tab {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .tab-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 328px;
        height: 99px;

        // cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/taskTab.png');
        background-size: 100% 100%;
        &.active {
          background: url('@/assets/ScreenLeft/DynamicMaintenance/taskTabSelected.png');
          background-size: 100% 100%;
        }
      }
      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-left: 44px;
      }
      .text {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span {
          margin-top: 9px;
          font-family: 'Source Han Sans CN';
          font-size: 18px;
          font-weight: 400;
          line-height: 24px;
          color: #ffffff;
        }
        .num {
          font-family: PangMenZhengDao;
          font-size: 32px;
          font-weight: 400;
          line-height: 34px;
          color: #ffffff;
          text-align: center;
        }
      }
    }
    .img-box {
      position: relative;
      margin-top: 17px;
      .prev {
        position: absolute;
        left: 0;
        width: 39px;
        height: 247px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/prev.png') no-repeat;
        background-size: 100% 100%;
      }
      .next {
        position: absolute;
        right: 0;
        width: 39px;
        height: 247px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/next.png') no-repeat;
        background-size: 100% 100%;
      }
      .imgs {
        position: absolute;
        top: -15px;
        left: 62px;
        width: 507px;
        .image-group {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          justify-content: space-between;
        }
        .image {
          position: relative;
          width: 245px;
          height: 141px;
          background: url('@/assets/ScreenLeft/DynamicMaintenance/imgbg.png') no-repeat;
          :deep(.el-image__inner) {
            width: 237px;
            height: 132px;
            border-radius: 10px;
          }
          .mask {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            width: 100%;
            height: 100%;
            cursor: pointer;
          }
          .title {
            position: absolute;
            bottom: 7px;
            left: 0;
            width: 237px;
            height: 21px;
            overflow: hidden;
            font-family: 'Source Han Sans CN';
            font-size: 14px;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
            background: rgb(1 52 78 / 68%);
            border-radius: 0 0 5px 5px;
          }
        }
      }
    }
  }
</style>
