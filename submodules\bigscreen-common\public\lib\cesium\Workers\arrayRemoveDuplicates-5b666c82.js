/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./defined-3b3eb2ba","./Math-b5f4d889"],(function(e,n,d){"use strict";const i=d.CesiumMath.EPSILON10;e.arrayRemoveDuplicates=function(e,d,t,f){if(!n.defined(e))return;t=n.defaultValue(t,!1);const s=n.defined(f),u=e.length;if(u<2)return e;let r,l,c,a=e[0],h=0,o=-1;for(r=1;r<u;++r)l=e[r],d(a,l,i)?(n.defined(c)||(c=e.slice(0,r),h=r-1,o=0),s&&f.push(r)):(n.defined(c)&&(c.push(l),h=r,s&&(o=f.length)),a=l);return t&&d(e[0],e[u-1],i)&&(s&&(n.defined(c)?f.splice(o,0,h):f.push(u-1)),n.defined(c)?c.length-=1:c=e.slice(0,-1)),n.defined(c)?c:e}}));
