<!--
 * @Author: tianmengxue <EMAIL>
 * @Date: 2023-06-28 10:42:58
 * @LastEditors: tianmengxue <EMAIL>
 * @LastEditTime: 2023-06-28 12:26:31
-->
<template>
  <div class="timer-car-title">
    <span class="title">{{ title }}</span>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.timer-car-title{
  height: 30px;
  border-bottom:1px solid #2C5289;
  margin-bottom: 10px;
  margin-top:10px;
  .title{
    font-size: 16px;
    font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
    font-weight: normal;
    color: #FFFFFF;
    line-height: 18px;
  }
}
</style>
