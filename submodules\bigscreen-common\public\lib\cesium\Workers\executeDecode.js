/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./createTaskProcessorWorker","./Cartesian3-bb0e6278","./defined-3b3eb2ba","./Math-b5f4d889"],(function(e,n,t,s){"use strict";return e((function(e,t){return function(e,t){let s,a,o;if(!s){s=[],a=[],o=[];const r=new Float32Array(e.positions),c=new Float32Array(e.normals),i=new Uint16Array(e.indices);for(let e=0;e<i.length;e+=3){const t=new n.Cartesian3(i[e],i[e+1],i[e+2]);o.push(t)}for(let e=0;e<r.length;e+=3)s.push(r[e]),s.push(r[e+1]),s.push(r[e+2]);for(let e=0;e<c.length;e+=2){const t=new n.Cartesian2(c[e],c[e+1]),s=new n.Cartesian3(t.x,t.y,1-Math.abs(t.x)-Math.abs(t.y));s.z<0&&(s.x=(1-Math.abs(s.x))*(s.x>=0?1:-1),s.y=(1-Math.abs(s.y))*(s.y>=0?1:-1)),a.push(s)}const h=function(e){const t={},s=[],a=[],o=[],r={};let c;const i=4,h=Math.pow(10,i);let l,u,f,p,b;const d=[];for(l=0;l<e.positions.length/3;l++){const n=e.positions[3*l],i=e.positions[3*l+1],u=e.positions[3*l+2],f=e.normals[l];c=`${Math.round(n*h)}_${Math.round(i*h)}_${Math.round(u*h)}`,void 0===t[c]?(t[c]=l,s.push(n),s.push(i),s.push(u),o.push(f),a[l]=s.length/3-1,r[s.length/3-1]||(r[s.length/3-1]=[l]),r[s.length/3-1].push(l)):(a[l]=a[t[c]],r[a[t[c]]]||(r[a[t[c]]]=[]&&console("bucunzai")),r[a[t[c]]].push(l))}const g=[],m=[],w=[],y=[];for(l=0,p=0,u=e.indices.length;l<u;l++,p++){f={a:e.indices[l].x,b:e.indices[l].y,c:e.indices[l].z},f.a=a[f.a],f.b=a[f.b],f.c=a[f.c];const n=[f.a,f.b,f.c];for(let e=0;e<3;e++)if(n[e]===n[(e+1)%3]){w.push(l);break}y.push(f)}for(l=w.length-1;l>=0;l--){const e=w[l];for(y.splice(e,1),p=0,b=d.length;p<b;p++)d[p].splice(e,1)}const M=[];for(let e=0;e<y.length;e++)M.push(y[e]);for(let e=0;e<s.length/3;e++){const n=s[3*e],t=s[3*e+1],a=s[3*e+2],r=o[e];m.push(n),m.push(t),m.push(a),g.push(r)}e._normals=g,e._indices=M,e._positions=m,e.changes=a,e.changesPos=r,Math.DEG2RAD=Math.PI/180;const C=50,x=Math.cos(Math.DEG2RAD*C);e.faceNormals=[];const A=[0,0],N={};let $,_;c=null;const v=["a","b","c"],z=[];for(let t=0;t<M.length;t++){const s=M[t],a=new n.Cartesian3(m[3*s.a],m[3*s.a+1],m[3*s.a+2]),o=new n.Cartesian3(m[3*s.b],m[3*s.b+1],m[3*s.b+2]),r=new n.Cartesian3(m[3*s.c],m[3*s.c+1],m[3*s.c+2]),c=new n.Cartesian3,i=new n.Cartesian3;n.Cartesian3.subtract(r,o,c),n.Cartesian3.subtract(a,o,i),n.Cartesian3.cross(c,i,c);try{n.Cartesian3.normalize(c,c)}catch(e){}e.faceNormals.push(c.clone());const h={a:s.a,b:s.b,c:s.c,faceNormal:c.clone()};z.push(h)}for(let e=0;e<z.length;e++){const n=z[e];for(let t=0;t<3;t++)$=n[v[t]],_=n[v[(t+1)%3]],A[0]=Math.min($,_),A[1]=Math.max($,_),c=`${A[0]},${A[1]}`,void 0===N[c]?N[c]={index1:A[0],index2:A[1],face1:e,face2:void 0}:N[c].face2=e}const D=[];for(c in N)if(N.hasOwnProperty(c)){const e=N[c];(void 0===e.face2||n.Cartesian3.dot(z[e.face1].faceNormal,z[e.face2].faceNormal)<=x)&&(D.push(e.index1),D.push(e.index2))}const P=new Uint16Array(D).buffer,k=new Float32Array(m).buffer;return e.edge=P,e.mergeVertices=k,e}({positions:s,normals:a,indices:o}),l=h.edge,u=h.mergeVertices;t.push.apply(t,l),t.push.apply(t,u);return{edge:l,mergeVertices:u}}}(e,t)}))}));
