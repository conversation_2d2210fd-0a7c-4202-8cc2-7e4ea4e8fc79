<template>
  <div class="device-info-container relative">
    <div class="title popup-title cursor-move">设备信息</div>
    <div class="close-btn" @click="handleClose"><img src="@/assets/StructureDiagram/robot/close.png" w12 h12 /></div>
    <div class="subtitle"><span class="position-absolute -top-3">设备信息</span></div>
    <div class="device-info-box">
      <div class="key-value-line">
        <div class="key">设备名称</div>
        <div class="value">{{ data.name }}</div>
      </div>
      <div class="key-value-line">
        <div class="key">安装位置</div>
        <div class="value">{{ mileage }}</div>
      </div>
    </div>
    <div class="subtitle"><span class="position-absolute -top-3">数据监测</span></div>
    <div class="device-monitor-data-container">
      <el-date-picker
        value-format="YYYY-MM-DD HH:mm:ss"
        v-model="dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        @change="getChartData" />
      <div class="device-monitor-channel mt-12">
        <tabs v-model="activeName" :list="channels"></tabs>
      </div>
      <chart ref="cpChart"></chart>
    </div>
  </div>
</template>

<script setup>
  import moment from 'moment'

  import chart from './Components/chart.vue'
  import tabs from './Components/tabs.vue'

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    data: { type: Object, default: () => ({}) }
  })
  const cpChart = ref(null)

  const handleClose = lib.provideTools.handleClose.inject()
  const dateRange = ref([moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD HH:mm:ss')])
  const deviceExt = ref({})
  const deviceTypeName = ref('')
  const channels = ref([])
  const activeName = ref('')
  const type = ref('') // measure 遥测量   status 遥信量
  const mileage = ref('')

  const getLastDateTime = (deviceId) => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      const res = await lib.api.getDLLData('rest/data/deviceLastData', { deviceId })
      if (res.success) {
        const keys = Object.keys(res.result)
        if (keys.length > 0) {
          const channelItem = res.result[keys[0]]
          if (channelItem.time) {
            const end = moment(channelItem.time).add(1, 'minute').format('YYYY-MM-DD HH:mm:ss')
            const start = moment(channelItem.time).add(-1, 'days').format('YYYY-MM-DD 00:00:00')
            dateRange.value = [start, end]
          }
        }
      } else {
        dateRange.value = [moment().format('YYYY-MM-DD 00:00:00'), moment().format('YYYY-MM-DD HH:mm:ss')]
      }
      resolve()
    })
  }

  const getChannels = (id) => {
    return new Promise((resolve) => {
      lib.api.getDLLData('rest/deviceManage/channels', { deviceId: id }).then((res) => {
        if (res.success) {
          if (res.result && res.result.list) {
            // 只显示每个测点配置为显示的变量
            resolve(res.result.list.filter((item) => item.state === 1))
          }
        }
      })
    })
  }

  const getChartData = async () => {
    const channelId = activeName.value
    const currentChanel = channels.value.find((_) => _.id === activeName.value)
    const start = dateRange.value[0]
    const end = dateRange.value[1]
    // const lastDataResponse = await lib.api.getDLLData('rest/data/deviceLastData', { deviceId: currentChanel.deviceId })

    // if (lastDataResponse && lastDataResponse.success) {
    //   // 如果最后一次有值  则往前取一天
    //   const keys = Object.keys(lastDataResponse.result)
    //   if (keys.length > 0) {
    //     const channelItem = lastDataResponse.result[keys[0]]
    //     if (channelItem.time) {
    //       end = moment(channelItem.time).add(1, 'minute').format('YYYY-MM-DD HH:mm:ss')
    //       start = moment(channelItem.time).add(-1, 'days').format('YYYY-MM-DD 00:00:00')
    //     }
    //   }
    // }

    lib.api
      .getDLLData('rest/data/channelData', {
        start,
        end,
        id: channelId
      })
      .then(async (res) => {
        if (res.success) {
          const dataYList = []
          for (let i = 0; i < channels.value.length; i += 1) {
            if (channels.value[i].id === activeName.value) {
              // 获取y轴翻译
              await lib.api
                .getDLLData('rest/channelExt/getChannelExtDto', {
                  projectFlag: 2,
                  relationalId: channels.value[i].measurePointId
                })
                .then((resy) => {
                  if (resy.result.measurePointExt && resy.result.measurePointExt.extChildValue) {
                    dataYList.push(...JSON.parse(resy.result.measurePointExt.extChildValue))
                  }
                  if (resy.success) {
                    type.value = resy.result.type
                  }
                })
            }
          }

          let dataX = res.result[channelId].map((_) => moment(_.time).format('MM-DD HH:mm'))
          let list = res.result[channelId].map((_) => _.value)
          if (list && list.length > 0) {
            cpChart.value.initChart(dataX, list, deviceExt.value, dataYList, type.value)
          } else {
            // 遥信量 则获取最后一次的数据
            const lastDataRes = await lib.api.getDLLData('rest/data/deviceLastData', { deviceId: currentChanel.deviceId })
            if (lastDataRes && lastDataRes.success) {
              const lastData = lastDataRes.result[currentChanel.code] || null
              console.log(lastData)
              if (lastData) {
                const lastValue = lastData[currentChanel.code]
                dataX = [moment(start).format('MM-DD HH:mm'), moment(end).format('MM-DD HH:mm')]
                dataX = [moment(start).format('MM-DD HH:mm'), moment(end).format('MM-DD HH:mm')]
                list = [lastValue, lastValue]
                cpChart.value.initChart(dataX, list, deviceExt.value, dataYList, type.value)
              }
            }
          }
        }
      })
  }

  const getDeviceData = () => {
    lib.api.getDLLData('rest/deviceManage/getDevice', { id: props.data.id }).then((res) => {
      console.log('设备信息', res.result)
      if (res.success) {
        deviceTypeName.value = res.result.deviceTypeName
        mileage.value = res.result.mileage
      }
    })
  }

  watch(
    () => props.data,
    async (val) => {
      if (val && val.deviceTypeId) {
        deviceExt.value = null
        if (val.deviceTypeId === 203) {
          // 液位计   需要读取第一起泵点和第二起泵点数据
          const extRet = await lib.api.getDLLData('rest/deviceManage/getDeviceExtData', { id: val.id })

          if (extRet.success && extRet.result.deviceExt) {
            deviceExt.value = extRet.result.deviceExt
          }
        }

        await getLastDateTime(val.id)
        channels.value = await getChannels(val.id)
        activeName.value = channels.value[0].id

        getDeviceData()
      }
    },
    { immediate: true, deep: true }
  )
  watch(activeName, (val) => {
    // console.log('点击了', activeName.value, val)
    getChartData()
  })
</script>

<style lang="scss" scoped>
  .device-info-container {
    width: 421px;
    height: 603px;
    background: url('@/assets/StructureDiagram/device/bg.png');
    background-size: cover;
    .title {
      width: 100%;
      height: 40px;
      padding-left: 17px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      line-height: 40px;
      color: #ffffff;
    }
    .close-btn {
      position: absolute;
      top: 8px;
      right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      cursor: pointer;
    }
    .subtitle {
      position: relative;
      width: 396px;
      height: 24px;
      padding-left: 28px;
      margin: 25px auto 0; /* 顶部 25px, 水平自动，底部 0 */
      font-family: PangMenZhengDao-Regular, PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      line-height: 21px;
      color: #ffffff;
      background: url('@/assets/StructureDiagram/device/titleBg.png');
      background-size: cover;
    }
    .device-info-box {
      width: 100%;
      height: 80px;
      padding: 0 14px;
      .key-value-line {
        display: flex;
        width: 100%;
        height: 40px;
        line-height: 40px;
        .key {
          width: 160px;
          height: 40px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-weight: 400;
          color: #ffffff;
        }
        .value {
          flex: 1;
          font-family: 'Alibaba PuHuiTi';
          font-size: 16px;
          font-weight: 400;
          color: #5bb5ff;
        }
      }
    }
    .device-monitor-data-container {
      width: 100%;
      height: 400px;
      padding: 10px 14px;
      :deep(.el-select) {
        --el-fill-color-blank: transparent !important;
        --el-select-input-focus-border-color: #47b7ff;
        --el-border-color: #47b7ff;
      }
      :deep(.el-select__wrapper) {
        min-height: 20px;
        color: #8dd8ff;
      }
      :deep(.el-input__wrapper) {
        background-color: transparent !important;
        border: 1px solid #47b7ff;
        box-shadow: none;
      }
      :deep(.el-select__placeholder) {
        color: #8dd8ff;
      }

      & {
        --el-text-color-regular: #ffffff;
      }
      // --el-text-color-regular: #ffffff;
      .device-monitor-channel {
        display: flex;
        justify-content: center;
        width: 100%;
        height: 30px;
      }
    }
  }
</style>
