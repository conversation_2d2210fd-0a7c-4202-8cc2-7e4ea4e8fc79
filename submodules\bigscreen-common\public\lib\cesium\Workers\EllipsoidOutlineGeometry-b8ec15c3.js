/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889"],(function(t,i,e,n,a,o,r,s,m,u,f){"use strict";const l=new e.Cartesian3(1,1,1),d=Math.cos,c=Math.sin;function C(t){t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT);const i=a.defaultValue(t.radii,l),n=a.defaultValue(t.innerRadii,i),o=a.defaultValue(t.minimumClock,0),r=a.defaultValue(t.maximumClock,f.CesiumMath.TWO_PI),s=a.defaultValue(t.minimumCone,0),m=a.defaultValue(t.maximumCone,f.CesiumMath.PI),u=Math.round(a.defaultValue(t.stackPartitions,10)),d=Math.round(a.defaultValue(t.slicePartitions,8)),c=Math.round(a.defaultValue(t.subdivisions,128));this._radii=e.Cartesian3.clone(i),this._innerRadii=e.Cartesian3.clone(n),this._minimumClock=o,this._maximumClock=r,this._minimumCone=s,this._maximumCone=m,this._stackPartitions=u,this._slicePartitions=d,this._subdivisions=c,this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}C.packedLength=2*e.Cartesian3.packedLength+8,C.pack=function(t,i,n){return n=a.defaultValue(n,0),e.Cartesian3.pack(t._radii,i,n),n+=e.Cartesian3.packedLength,e.Cartesian3.pack(t._innerRadii,i,n),n+=e.Cartesian3.packedLength,i[n++]=t._minimumClock,i[n++]=t._maximumClock,i[n++]=t._minimumCone,i[n++]=t._maximumCone,i[n++]=t._stackPartitions,i[n++]=t._slicePartitions,i[n++]=t._subdivisions,i[n]=a.defaultValue(t._offsetAttribute,-1),i};const _=new e.Cartesian3,p=new e.Cartesian3,b={radii:_,innerRadii:p,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};C.unpack=function(t,i,n){i=a.defaultValue(i,0);const o=e.Cartesian3.unpack(t,i,_);i+=e.Cartesian3.packedLength;const r=e.Cartesian3.unpack(t,i,p);i+=e.Cartesian3.packedLength;const s=t[i++],m=t[i++],u=t[i++],f=t[i++],l=t[i++],d=t[i++],c=t[i++],h=t[i];return a.defined(n)?(n._radii=e.Cartesian3.clone(o,n._radii),n._innerRadii=e.Cartesian3.clone(r,n._innerRadii),n._minimumClock=s,n._maximumClock=m,n._minimumCone=u,n._maximumCone=f,n._stackPartitions=l,n._slicePartitions=d,n._subdivisions=c,n._offsetAttribute=-1===h?void 0:h,n):(b.minimumClock=s,b.maximumClock=m,b.minimumCone=u,b.maximumCone=f,b.stackPartitions=l,b.slicePartitions=d,b.subdivisions=c,b.offsetAttribute=-1===h?void 0:h,new C(b))},C.createGeometry=function(t){const e=t._radii;if(e.x<=0||e.y<=0||e.z<=0)return;const l=t._innerRadii;if(l.x<=0||l.y<=0||l.z<=0)return;const C=t._minimumClock,_=t._maximumClock,p=t._minimumCone,b=t._maximumCone,h=t._subdivisions,y=o.Ellipsoid.fromCartesian3(e);let k=t._slicePartitions+1,x=t._stackPartitions+1;k=Math.round(k*Math.abs(_-C)/f.CesiumMath.TWO_PI),x=Math.round(x*Math.abs(b-p)/f.CesiumMath.PI),k<2&&(k=2),x<2&&(x=2);let A=0,P=1;const v=l.x!==e.x||l.y!==e.y||l.z!==e.z;let M=!1,w=!1;v&&(P=2,p>0&&(M=!0,A+=k),b<Math.PI&&(w=!0,A+=k));const g=h*P*(x+k),V=new Float64Array(3*g),G=2*(g+A-(k+x)*P),E=u.IndexDatatype.createTypedArray(g,G);let O,D,I,T,z=0;const L=new Array(x),R=new Array(x);for(O=0;O<x;O++)T=p+O*(b-p)/(x-1),L[O]=c(T),R[O]=d(T);const N=new Array(h),B=new Array(h);for(O=0;O<h;O++)I=C+O*(_-C)/(h-1),N[O]=c(I),B[O]=d(I);for(O=0;O<x;O++)for(D=0;D<h;D++)V[z++]=e.x*L[O]*B[D],V[z++]=e.y*L[O]*N[D],V[z++]=e.z*R[O];if(v)for(O=0;O<x;O++)for(D=0;D<h;D++)V[z++]=l.x*L[O]*B[D],V[z++]=l.y*L[O]*N[D],V[z++]=l.z*R[O];for(L.length=h,R.length=h,O=0;O<h;O++)T=p+O*(b-p)/(h-1),L[O]=c(T),R[O]=d(T);for(N.length=k,B.length=k,O=0;O<k;O++)I=C+O*(_-C)/(k-1),N[O]=c(I),B[O]=d(I);for(O=0;O<h;O++)for(D=0;D<k;D++)V[z++]=e.x*L[O]*B[D],V[z++]=e.y*L[O]*N[D],V[z++]=e.z*R[O];if(v)for(O=0;O<h;O++)for(D=0;D<k;D++)V[z++]=l.x*L[O]*B[D],V[z++]=l.y*L[O]*N[D],V[z++]=l.z*R[O];for(z=0,O=0;O<x*P;O++){const t=O*h;for(D=0;D<h-1;D++)E[z++]=t+D,E[z++]=t+D+1}let S=x*h*P;for(O=0;O<k;O++)for(D=0;D<h-1;D++)E[z++]=S+O+D*k,E[z++]=S+O+(D+1)*k;if(v)for(S=x*h*P+k*h,O=0;O<k;O++)for(D=0;D<h-1;D++)E[z++]=S+O+D*k,E[z++]=S+O+(D+1)*k;if(v){let t=x*h*P,i=t+h*k;if(M)for(O=0;O<k;O++)E[z++]=t+O,E[z++]=i+O;if(w)for(t+=h*k-k,i+=h*k-k,O=0;O<k;O++)E[z++]=t+O,E[z++]=i+O}const U=new s.GeometryAttributes({position:new r.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:V})});if(a.defined(t._offsetAttribute)){const i=V.length,e=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(i/3).fill(e);U.applyOffset=new r.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new r.Geometry({attributes:U,indices:E,primitiveType:r.PrimitiveType.LINES,boundingSphere:i.BoundingSphere.fromEllipsoid(y),offsetAttribute:t._offsetAttribute})},t.EllipsoidOutlineGeometry=C}));
