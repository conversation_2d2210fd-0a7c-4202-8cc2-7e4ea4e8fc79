/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./EllipseGeometryLibrary-07d021fe","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889"],(function(e,t,i,r,n,a,o,s,l,u,d,c){"use strict";const f=new i.Cartesian3;let p=new i.Cartesian3;const m=new t.BoundingSphere,h=new t.BoundingSphere;function b(e){const t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).center,r=n.defaultValue(e.ellipsoid,o.Ellipsoid.WGS84),a=e.semiMajorAxis,s=e.semiMinorAxis,l=n.defaultValue(e.granularity,c.CesiumMath.RADIANS_PER_DEGREE),u=n.defaultValue(e.height,0),d=n.defaultValue(e.extrudedHeight,u);this._center=i.Cartesian3.clone(t),this._semiMajorAxis=a,this._semiMinorAxis=s,this._ellipsoid=o.Ellipsoid.clone(r),this._rotation=n.defaultValue(e.rotation,0),this._height=Math.max(d,u),this._granularity=l,this._extrudedHeight=Math.min(d,u),this._numberOfVerticalLines=Math.max(n.defaultValue(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}b.packedLength=i.Cartesian3.packedLength+o.Ellipsoid.packedLength+8,b.pack=function(e,t,r){return r=n.defaultValue(r,0),i.Cartesian3.pack(e._center,t,r),r+=i.Cartesian3.packedLength,o.Ellipsoid.pack(e._ellipsoid,t,r),r+=o.Ellipsoid.packedLength,t[r++]=e._semiMajorAxis,t[r++]=e._semiMinorAxis,t[r++]=e._rotation,t[r++]=e._height,t[r++]=e._granularity,t[r++]=e._extrudedHeight,t[r++]=e._numberOfVerticalLines,t[r]=n.defaultValue(e._offsetAttribute,-1),t};const y=new i.Cartesian3,_=new o.Ellipsoid,A={center:y,ellipsoid:_,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};b.unpack=function(e,t,r){t=n.defaultValue(t,0);const a=i.Cartesian3.unpack(e,t,y);t+=i.Cartesian3.packedLength;const s=o.Ellipsoid.unpack(e,t,_);t+=o.Ellipsoid.packedLength;const l=e[t++],u=e[t++],d=e[t++],c=e[t++],f=e[t++],p=e[t++],m=e[t++],h=e[t];return n.defined(r)?(r._center=i.Cartesian3.clone(a,r._center),r._ellipsoid=o.Ellipsoid.clone(s,r._ellipsoid),r._semiMajorAxis=l,r._semiMinorAxis=u,r._rotation=d,r._height=c,r._granularity=f,r._extrudedHeight=p,r._numberOfVerticalLines=m,r._offsetAttribute=-1===h?void 0:h,r):(A.height=c,A.extrudedHeight=p,A.granularity=f,A.rotation=d,A.semiMajorAxis=l,A.semiMinorAxis=u,A.numberOfVerticalLines=m,A.offsetAttribute=-1===h?void 0:h,new b(A))},b.createGeometry=function(e){if(e._semiMajorAxis<=0||e._semiMinorAxis<=0)return;const o=e._height,b=e._extrudedHeight,y=!c.CesiumMath.equalsEpsilon(o,b,0,c.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);const _={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:o,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines};let A;if(y)_.extrudedHeight=b,_.offsetAttribute=e._offsetAttribute,A=function(e){const o=e.center,p=e.ellipsoid,b=e.semiMajorAxis;let y=i.Cartesian3.multiplyByScalar(p.geodeticSurfaceNormal(o,f),e.height,f);m.center=i.Cartesian3.add(o,y,m.center),m.radius=b,y=i.Cartesian3.multiplyByScalar(p.geodeticSurfaceNormal(o,y),e.extrudedHeight,y),h.center=i.Cartesian3.add(o,y,h.center),h.radius=b;let _=a.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions;const A=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:a.EllipseGeometryLibrary.raisePositionsToHeight(_,e,!0)})});_=A.position.values;const g=t.BoundingSphere.union(m,h);let x=_.length/3;if(n.defined(e.offsetAttribute)){let t=new Uint8Array(x);if(e.offsetAttribute===u.GeometryOffsetAttribute.TOP)t=t.fill(1,0,x/2);else{const i=e.offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1;t=t.fill(i)}A.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:t})}let E=n.defaultValue(e.numberOfVerticalLines,16);E=c.CesiumMath.clamp(E,0,x/2);const M=d.IndexDatatype.createTypedArray(x,2*x+2*E);x/=2;let C,G,L=0;for(C=0;C<x;++C)M[L++]=C,M[L++]=(C+1)%x,M[L++]=C+x,M[L++]=(C+1)%x+x;if(E>0){const e=Math.min(E,x);G=Math.round(x/e);const t=Math.min(G*E,x);for(C=0;C<t;C+=G)M[L++]=C,M[L++]=C+x}return{boundingSphere:g,attributes:A,indices:M}}(_);else if(A=function(e){const n=e.center;p=i.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(n,p),e.height,p),p=i.Cartesian3.add(n,p,p);const o=new t.BoundingSphere(p,e.semiMajorAxis),u=a.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,c=new l.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:a.EllipseGeometryLibrary.raisePositionsToHeight(u,e,!1)})}),f=u.length/3,m=d.IndexDatatype.createTypedArray(f,2*f);let h=0;for(let e=0;e<f;++e)m[h++]=e,m[h++]=(e+1)%f;return{boundingSphere:o,attributes:c,indices:m}}(_),n.defined(e._offsetAttribute)){const t=A.attributes.position.values.length,i=e._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(t/3).fill(i);A.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}return new s.Geometry({attributes:A.attributes,indices:A.indices,primitiveType:s.PrimitiveType.LINES,boundingSphere:A.boundingSphere,offsetAttribute:e._offsetAttribute})},e.EllipseOutlineGeometry=b}));
