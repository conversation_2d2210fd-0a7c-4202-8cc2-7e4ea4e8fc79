/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./createTaskProcessorWorker","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryPipeline-9dbd2054","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./Cartesian3-bb0e6278","./OrientedBoundingBox-e47c7a90","./RuntimeError-592f0d41","./OglParser-bd379888","./WebGLConstants-433debbf","./Transforms-42ed7720","./Math-b5f4d889","./Resource-41d99fe7","./combine-0bec9016","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d"],(function(t,e,n,o,a,r,i,s,u,c,m,p,l,d,f,b,y,h,A,v,M,x){"use strict";const D=[];function g(t,e,o){const a={indicesCount:e,indicesOffset:0,indicesStart:0,attributes:{},indices:null,vertexStart:0,vertexOffset:0,vertexCount:t};return function(t,e){const o=t.vertexCount,a=t.indicesCount,i=t.attributes;i.position=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:new Float32Array(3*o)}),i.batchId=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(o)}),i.normal=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:new Float32Array(3*o)}),e&&(i.st=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(2*o)}));t.indices=o<65536?new Uint16Array(a):new Uint32Array(a)}(a,o),a}function C(t){return t.values.length/t.componentsPerAttribute}function I(t){return r.ComponentDatatype.getSizeInBytes(t.componentDatatype)*t.componentsPerAttribute}function w(t,e,o,s){const u=t.attributes,c=u.position.values,m=u.batchId.values,p=function(t,e,n){const o=function(t,e){let n,o;if(e)return n=[Math.ceil(e.maximum.x)+1,Math.ceil(e.maximum.y)+1,Math.ceil(e.maximum.z)+1],o=[Math.floor(e.minimum.x)-1,Math.floor(e.minimum.y)-1,Math.floor(e.minimum.z)-1],{min:o,max:n};o=[],n=[],o[0]=o[1]=o[2]=Number.MAX_VALUE,n[0]=n[1]=n[2]=-Number.MAX_VALUE;for(let e=0;e<t.length;e+=3)o[0]=Math.min(o[0],t[e+0]),o[1]=Math.min(o[1],t[e+1]),o[2]=Math.min(o[2],t[e+2]),n[0]=Math.max(n[0],t[e+0]),n[1]=Math.max(n[1],t[e+1]),n[2]=Math.max(n[2],t[e+2]);return{min:o,max:n}}(t,n),r=o.max,s=o.min,u=new Float32Array([r[0]!==s[0]?e/(r[0]-s[0]):0,r[1]!==s[1]?e/(r[1]-s[1]):0,r[2]!==s[2]?e/(r[2]-s[2]):0]),c=a.Matrix4.fromScale(new i.Cartesian3((r[0]-s[0])/e,(r[1]-s[1])/e,(r[2]-s[2])/e));return c[12]=s[0],c[13]=s[1],c[14]=s[2],{max:r,min:s,multiplier:u,decodeMat:c}}(c,65535,e),l=p.min,d=p.multiplier,f=p.decodeMat,b=t.vertexCount;u.compressedPositionAndBatchid=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_SHORT,componentsPerAttribute:4,values:new Uint16Array(4*b)});const y=u.compressedPositionAndBatchid.values;let h=0;for(let t=0,e=c.length;t<e;t+=3)y[4*h]=Math.floor((c[t+0]-l[0])*d[0]),y[4*h+1]=Math.floor((c[t+1]-l[1])*d[1]),y[4*h+2]=Math.floor((c[t+2]-l[2])*d[2]),y[4*h+3]=Math.floor(m[h]),h++;if(u.position.values=void 0,delete u.position,u.batchId.values=void 0,delete u.batchId,t.decodeMat=f,o){const e=u.st.values;u.compressedSt=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_SHORT,componentsPerAttribute:2,values:new Uint16Array(2*b)}),t.decodeSt=function(t,e,n,o){const r=function(t,e){let n,o,a,r;if(e)return{max:e.max,min:e.min};o=[],n=[],o[0]=o[1]=Number.MAX_VALUE,n[0]=n[1]=-Number.MAX_VALUE;for(let e=0;e<t.length;e+=2)a=t[e],r=t[e+1],o[0]=Math.min(o[0],a),n[0]=Math.max(n[0],a),o[1]=Math.min(o[1],r),n[1]=Math.max(n[1],r);return{min:o,max:n}}(t,o),i=r.max,s=r.min,u=new Float32Array([i[0]!==s[0]?e/(i[0]-s[0]):0,i[1]!==s[1]?e/(i[1]-s[1]):0]),c=new a.Cartesian4((i[0]-s[0])/e,(i[1]-s[1])/e,s[0],s[1]);let m,p,l=0;for(let e=0,o=t.length;e<o;e+=2)m=t[e],p=t[e+1],n[2*l]=Math.floor((m-s[0])*u[0]),n[2*l+1]=Math.floor((p-s[1])*u[1]),l++;return c}(e,65535,u.compressedSt.values,s),u.st.values=void 0,delete u.st}const A=u.normal.values;return u.compressedNormal=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.SHORT,componentsPerAttribute:2,values:new Int16Array(2*b)}),function(t,e){const n=t.length;let o,a,r,i,s,u=0;for(let c=0;c<n;c+=3){if(o=t[c],a=t[c+1],r=t[c+2],i=o/(Math.abs(o)+Math.abs(a)+Math.abs(r)),s=a/(Math.abs(o)+Math.abs(a)+Math.abs(r)),r<0){i=(1-Math.abs(s))*(i>=0?1:-1),s=(1-Math.abs(i))*(s>=0?1:-1);let t=1-Math.abs(i)-Math.abs(s);t>0&&(t+=.001,i+=i>0?t/2:-t/2,s+=s>0?t/2:-t/2)}e[2*u]=Math.round(32767*i+(i<0?1:0)),e[2*u+1]=Math.round(32767*s+(s<0?1:0)),u++}}(A,u.compressedNormal.values),u.normal.values=void 0,delete u.normal,t}function P(t,n){const m=t.oglBufferArray,p=t.taskId,l=t.hasUv;let d;!function(t){t.forEach((function(t){t.geometryData=c.OglParser.parseOglData(t.buffer)}))}(m),l&&(d=function(t){let e,n,o,a,r;const i=[],s=[];s[0]=s[1]=Number.MAX_VALUE,i[0]=i[1]=-Number.MAX_VALUE;const u=Math.max,c=Math.min;let m,p,l;for(let d=0,f=t.length;d<f;d++){e=t[d],n=e.geometryData,o=n.groups[e.groupIndex],a=n.uv,r=n.indices;for(let t=o.start,e=o.start+o.count;t<e;t++)m=r[t],p=a[2*m],l=a[2*m+1],s[0]=c(p,s[0]),i[0]=u(p,i[0]),s[1]=c(l,s[1]),i[1]=u(l,i[1])}return{min:s,max:i}}(m));const f=function(t,e,n){let o,a,r,i,s,u,c,m,p,l,d,f,b,y,h,A,v,M,x,g,C;const I=e.attributes,w=I.position.values,P=I.normal.values,B=n?I.st.values:void 0,O=I.batchId.values,S=e.indices;let T=0,E=0,L=0;for(let e=0,I=t.length;e<I;e++){D.length=0,L=0,o=t[e],C=o.batchId,a=o.geometryData,r=a.groups[o.groupIndex],i=o.vertexCount;const I=o.matrix,G=a.indices,U=a.position,F=a.normal,N=a.uv;let k,z,_,R,V,X,$;s=I[0],u=I[4],c=I[8],m=I[12],p=I[1],l=I[5],d=I[9],f=I[13],b=I[2],y=I[6],h=I[10],A=I[14],v=I[3],M=I[7],x=I[11],g=I[15];for(let t=0,e=r.count;t<e;t++){const e=G[t+r.start];if(void 0===D[e]){D[e]=L,L++,k=U[3*e],z=U[3*e+1],_=U[3*e+2],R=k*s+z*u+_*c+m,V=k*p+z*l+_*d+f,X=k*b+z*y+_*h+A,$=k*v+z*M+_*x+g;const t=T+D[e];w[3*t]=R/$,w[3*t+1]=V/$,w[3*t+2]=X/$,k=F[3*e],z=F[3*e+1],_=F[3*e+2],R=k*s+z*u+_*c,V=k*p+z*l+_*d,X=k*b+z*y+_*h,P[3*t]=R,P[3*t+1]=V,P[3*t+2]=X,n&&(B[2*t]=N[2*e],B[2*t+1]=N[2*e+1]),O[t]=C}S[E+t]=T+D[e]}T+=i,E+=r.count}return e}(m,g(t.vertexCount,t.indicesCount,l),l);f.obb=function(t){const e=i.Cartesian3.unpackArray(t,[]);return s.OrientedBoundingBox.fromPoints(e)}(f.attributes.position.values);const b=w(f,undefined,l,d),y=b.decodeMat,h=o.GeometryPipeline.createAttributeLocations(b),A=function(t){let n,o,a;const i=[];for(o in t)t.hasOwnProperty(o)&&e.defined(t[o])&&e.defined(t[o].values)&&(i.push(o),t[o].componentDatatype===r.ComponentDatatype.DOUBLE&&(t[o].componentDatatype=r.ComponentDatatype.FLOAT,t[o].values=r.ComponentDatatype.createTypedArray(r.ComponentDatatype.FLOAT,t[o].values)));let s;const c=i.length;if(c>0)for(s=C(t[i[0]]),n=1;n<c;++n){const e=C(t[i[n]]);if(e!==s)throw new u.RuntimeError(`Each attribute list must have the same number of vertices.  Attribute ${i[n]} has a different number of vertices (${e.toString()}) than attribute ${i[0]} (${s.toString()}).`)}i.sort((function(e,n){return r.ComponentDatatype.getSizeInBytes(t[n].componentDatatype)-r.ComponentDatatype.getSizeInBytes(t[e].componentDatatype)}));let m=0;const p={};for(n=0;n<c;++n)o=i[n],a=t[o],p[o]=m,m+=I(a);if(m>0){const e=r.ComponentDatatype.getSizeInBytes(t[i[0]].componentDatatype),u=m%e;0!==u&&(m+=e-u);const l=new ArrayBuffer(s*m),d={};for(n=0;n<c;++n){o=i[n];const e=r.ComponentDatatype.getSizeInBytes(t[o].componentDatatype);d[o]={pointer:r.ComponentDatatype.createTypedArray(t[o].componentDatatype,l),index:p[o]/e,strideInComponentType:m/e}}for(n=0;n<s;++n)for(let e=0;e<c;++e){o=i[e],a=t[o];const r=a.values,s=d[o],u=s.pointer,c=a.componentsPerAttribute;for(let t=0;t<c;++t)u[s.index+t]=r[n*c+t];s.index+=s.strideInComponentType}return{buffer:l,offsetsInBytes:p,vertexSizeInBytes:m}}}(b.attributes);A.attributeLocations=h,function(t){const e=t.attributes;for(const t in e)e.hasOwnProperty(t)&&(e[t].values=void 0)}(f);const v={attributes:A,indices:b.indices,obb:s.OrientedBoundingBox.pack(b.obb,[]),materialId:m[0].materialId};n.push(A.buffer),n.push(b.indices.buffer);const M=[];m.forEach((function(t){var e,o;M.push({geometryId:t.geometryId,buffer:t.buffer}),e=n,o=t.buffer,-1===e.indexOf(o)&&e.push(o)}));const x={mainThreadBuffers:M,batchedGeometry:v,taskId:p,decodeMat:a.Matrix4.pack(y,[])};return l&&(x.decodeSt=a.Cartesian4.pack(b.decodeSt,[])),x}return t((function(t,e){return P(t,e)}))}));
