<template>
  <div>
    <div ref="ref1" class="ml-100 c-amber" h-200 w-300 bg-indigo @click="isClickOutside = false">onClickOutside: 在外部点击：{{ isClickOutside }}</div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  import { onClickOutside } from '@vueuse/core'
  const ref1 = ref(null)

  const isClickOutside = ref(false)
  onClickOutside(ref1, (event) => {
    console.log(event)
    isClickOutside.value = true
  })
</script>

<style lang="scss" scoped></style>
