/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Cartesian3-bb0e6278","./combine-0bec9016","./AttributeCompression-d661357e","./Rectangle-9bffefe4","./Math-b5f4d889","./IndexDatatype-00859b8b","./createTaskProcessorWorker","./defined-3b3eb2ba","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./RuntimeError-592f0d41"],(function(e,t,a,n,r,s,i,o,c,f,u){"use strict";const d=32767,b=new n.Cartographic,p=new e.Cartesian3;const l=new n.Rectangle,C=new n.Ellipsoid,w=new e.Cartesian3,h={min:void 0,max:void 0};const y=new e.Cartesian3,k=new e.Cartesian3,g=new e.Cartesian3,m=new e.Cartesian3,A=new e.Cartesian3;return i((function(i,o){const c=new Uint16Array(i.positions),f=new Uint16Array(i.widths),u=new Uint32Array(i.counts),D=new Uint16Array(i.batchIds);!function(t){t=new Float64Array(t);let a=0;h.min=t[a++],h.max=t[a++],n.Rectangle.unpack(t,a,l),a+=n.Rectangle.packedLength,n.Ellipsoid.unpack(t,a,C),a+=n.Ellipsoid.packedLength,e.Cartesian3.unpack(t,a,w)}(i.packedBuffer);const E=C,x=w,I=function(t,s,i,o,c){const f=t.length/3,u=t.subarray(0,f),l=t.subarray(f,2*f),C=t.subarray(2*f,3*f);a.AttributeCompression.zigZagDeltaDecode(u,l,C);const w=new Float64Array(t.length);for(let t=0;t<f;++t){const a=u[t],f=l[t],h=C[t],y=r.CesiumMath.lerp(s.west,s.east,a/d),k=r.CesiumMath.lerp(s.south,s.north,f/d),g=r.CesiumMath.lerp(i,o,h/d),m=n.Cartographic.fromRadians(y,k,g,b),A=c.cartographicToCartesian(m,p);e.Cartesian3.pack(A,w,3*t)}return w}(c,l,h.min,h.max,E),P=I.length/3,R=4*P-4,U=new Float32Array(3*R),T=new Float32Array(3*R),F=new Float32Array(3*R),N=new Float32Array(2*R),M=new Uint16Array(R);let L,S=0,_=0,v=0,G=0,W=u.length;for(L=0;L<W;++L){const t=u[L],a=f[L],n=D[L];for(let r=0;r<t;++r){let s;if(0===r){const t=e.Cartesian3.unpack(I,3*G,y),a=e.Cartesian3.unpack(I,3*(G+1),k);s=e.Cartesian3.subtract(t,a,g),e.Cartesian3.add(t,s,s)}else s=e.Cartesian3.unpack(I,3*(G+r-1),g);const i=e.Cartesian3.unpack(I,3*(G+r),m);let o;if(r===t-1){const a=e.Cartesian3.unpack(I,3*(G+t-1),y),n=e.Cartesian3.unpack(I,3*(G+t-2),k);o=e.Cartesian3.subtract(a,n,A),e.Cartesian3.add(a,o,o)}else o=e.Cartesian3.unpack(I,3*(G+r+1),A);e.Cartesian3.subtract(s,x,s),e.Cartesian3.subtract(i,x,i),e.Cartesian3.subtract(o,x,o);const c=r===t-1?2:4;for(let t=0===r?2:0;t<c;++t){e.Cartesian3.pack(i,U,S),e.Cartesian3.pack(s,T,S),e.Cartesian3.pack(o,F,S),S+=3;const r=t-2<0?-1:1;N[_++]=t%2*2-1,N[_++]=r*a,M[v++]=n}}G+=t}const B=s.IndexDatatype.createTypedArray(R,6*P-6);let O=0,z=0;for(W=P-1,L=0;L<W;++L)B[z++]=O,B[z++]=O+2,B[z++]=O+1,B[z++]=O+1,B[z++]=O+2,B[z++]=O+3,O+=4;o.push(U.buffer,T.buffer,F.buffer),o.push(N.buffer,M.buffer,B.buffer);let H={indexDatatype:2===B.BYTES_PER_ELEMENT?s.IndexDatatype.UNSIGNED_SHORT:s.IndexDatatype.UNSIGNED_INT,currentPositions:U.buffer,previousPositions:T.buffer,nextPositions:F.buffer,expandAndWidth:N.buffer,batchIds:M.buffer,indices:B.buffer};if(i.keepDecodedPositions){const e=function(e){const t=e.length,a=new Uint32Array(t+1);let n=0;for(let r=0;r<t;++r)a[r]=n,n+=e[r];return a[t]=n,a}(u);o.push(I.buffer,e.buffer),H=t.combine(H,{decodedPositions:I.buffer,decodedPositionOffsets:e.buffer})}return H}))}));
