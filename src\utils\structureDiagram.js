import lib from '@/utils/lib.ts'
import { fabric } from 'fabric'

class StructureDiagram {
  static canvas // 地图实例
  static mouseDownFunc
  static code
  #jsonData // 整个画布的Json数据
  // #imageServer = 'http://sd.suog.cn:11000/stec-platform-doc/img/'
  #imageServer = 'http://***********:10000/stec-platform-doc/img/'
  constructor(div) {
    if (div === undefined) return
    this.canvas = new fabric.Canvas(div, {
      isDrawingMode: false,
      skipTargetFind: false,
      selectable: true,
      selection: false,
      hoverCursor: 'pointer'
    })

    // 初始化气泡元素
    this.tooltip = null

    this.canvas.on({
      'mouse:down': (e) => {
        if (typeof this.mouseDownFunc === 'function') {
          this.mouseDownFunc(e.target, e)
        }
      },
      'mouse:over': (e) => {
        const isQJSDZHJK = this.code === 'QJSDZHJK' //判断是否是钱江项目的综合监控系统
        if (e.target?.itemData && isQJSDZHJK) {
          console.log('mouse:over', e.target.itemData)
          const name = e.target.itemData.name.split('|')[1]
          if (name) {
            this.showTooltip(name, e.e, e.target)
          }
        }
      },
      'mouse:out': (e) => {
        if (e.target?.itemData) {
          // 鼠标移出时隐藏气泡
          this.hideTooltip()
        }
      }
    })

    // const rect = new fabric.Rect({
    //   left: 100,
    //   top: 100,
    //   fill: 'red',
    //   width: 200,
    //   height: 200
    // })
    // this.canvas.add(rect)
  }

  /**
   * @description: 获取实例
   */
  getInstance() {
    return this.canvas
  }

  /**
   * @description: 获取数据
   * @param {*} code
   */
  async getData(code) {
    this.code = code
    this.clear()
    // lib.api.presetStructureApi.get({ code }).then((res) => {
    const res = await lib.api.bigscreenApi.presetStructureGet({ code })
    // lib.api.presetStructureApi.get({ projectId: 16, code: 'DP_JDJC_ZHJK_up' }).then((res) => {
    if (res.success && res.result.configScript) {
      this.#jsonData = JSON.parse(res.result.configScript)
      this.#init()
      setTimeout(() => {
        this.#initDeviceState()
      }, 1000)
    }
  }

  /**
   * @description: 画布中添加元素
   * @param {*} ele Cavnas元素
   */
  add(ele) {
    this.canvas.add(ele)
  }

  /**
   * @description: 设置缩放比例
   * @param {*} zoom 比例
   */
  setZoom(zoom) {
    this.canvas.setZoom(zoom)
  }

  /**
   * @description: 根据Json字符串进行画布初始化
   */
  #init() {
    const isQJSDZHJK = this.code === 'QJSDZHJK' //判断是否是钱江项目的综合监控系统

    this.#jsonData.components.forEach((item, index) => {
      const left = item.style.x
      const top = item.style.y
      const width = item.style.w
      const height = item.style.h
      if (item.type === 'img') {
        const imgUrl = `${this.#imageServer}${item.imgSrc}`
        fabric.Image.fromURL(imgUrl, (oImg) => {
          oImg.set({
            left,
            top,
            scaleX: width / oImg.width,
            scaleY: height / oImg.height,
            selectable: false,
            objType: 'bgImg'
          })
          this.add(oImg)
          // if (item.name === '背景图片') {
          if (index === 0) {
            // 背景图片
            oImg.hoverCursor = 'default'
            this.canvas.sendToBack(oImg)
          }
          // console.log(imgUrl, item.name, item.style.w, item.style.h, oImg.width, oImg.height);
        })
      } else if (['deviceImage', 'deviceImageByGroup', 'deviceGroupImage', 'deviceDynamicImg'].includes(item.type)) {
        // 设备图标
        let imgUrl = `${this.#imageServer}${item.imgSrc}`
        if (item.type === 'deviceGroupImage') {
          imgUrl = `${this.#imageServer}${item.imgSrcList[1]}`
        }
        // if (canvasCurrentCode.value === 'DP_JGJC_BXFJC') {
        //   item.deviceGroupCode = item.deviceGroup && item.deviceGroup[0] ? item.deviceGroup[0].code : ''
        // }

        fabric.Image.fromURL(
          imgUrl,
          (oImg) => {
            oImg.set({
              left,
              top,
              scaleX: width / oImg.width,
              scaleY: height / oImg.height,
              objType: item.type === 'deviceGroupImage' ? 'deviceGroupImage' : 'deviceImage',
              selectable: isQJSDZHJK,
              hasControls: false,
              itemData: item,
              opacity: 1,
              lockMovementX: true, // 禁止X轴移动
              lockMovementY: true // 禁止Y轴移动
            })
            oImg.moveTo(5)
            this.canvas.add(oImg)
            if (item.type === 'deviceGroupImage') {
              oImg.visible = false
              oImg.moveTo(1)
            }
            // if (highlightId && highlightId === item.id) {
            //   // 初始化高亮
            //   highlightSelectObject(oImg)
            // }
          },
          { crossOrigin: 'anonymous' }
        )
      } else if (item.type === 'text') {
        const text = new fabric.Text(item.name, {
          left,
          top: top + 10,
          fontSize: 12,
          textAlign: 'center',
          selectable: false
          // backgroundColor: 'red'
          // textBackgroundColor: 'green',
        })
        text.width = width
        this.add(text)
      }
    })
  }

  /**
   * @description: 获取所有设备状态
   */
  #initDeviceState() {
    console.log(this.#jsonData)
  }

  getJSONData() {
    return this.#jsonData
  }

  /**
   * @description: 设置鼠标按下的事件
   * @param {*} fun
   */
  setMouseDownFunc(fun) {
    this.mouseDownFunc = fun
  }

  /**
   * @description: 清除画布
   */
  clear() {
    if (this.canvas) {
      this.canvas.clear()
    }
  }

  /**
   * 销毁画布
   */
  dispose() {
    closePopWindowByTag('videoWall')

    // 清理气泡提示
    this.hideTooltip()

    if (this.canvas) {
      this.canvas.dispose()
      this.canvas = null
    }
  }

  /**
   * @description: 显示气泡提示
   * @param {string} name 要显示的名称
   * @param {Event} event 鼠标事件
   * @param {Object} target fabric对象
   */
  showTooltip(name, event, target) {
    // 如果气泡已存在，先隐藏
    this.hideTooltip()

    // 创建气泡元素
    this.tooltip = document.createElement('div')
    this.tooltip.className = 'canvas-tooltip'
    this.tooltip.textContent = name
    this.tooltip.style.cssText = `
      position: absolute;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 9999;
      pointer-events: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      text-align: center;
    `

    // 添加到页面
    document.getElementById('structureDiagram').appendChild(this.tooltip)

    // 保存target引用
    this.tooltipTarget = target

    // 设置位置
    this.updateTooltipPosition(event, target)
  }

  /**
   * @description: 隐藏气泡提示
   */
  hideTooltip() {
    if (this.tooltip) {
      document.getElementById('structureDiagram').removeChild(this.tooltip)
      this.tooltip = null
      this.tooltipTarget = null
    }
  }

  /**
   * @description: 更新气泡位置
   * @param {Event} event 鼠标事件
   * @param {Object} target fabric对象
   */
  updateTooltipPosition(event, target) {
    if (this.tooltip) {
      if (target) {
        // 获取target元素的边界框
        const boundingRect = target.getBoundingRect()
        const canvasElement = this.canvas.getElement()
        const canvasRect = canvasElement.getBoundingClientRect()

        // 计算target在页面中的实际位置
        const targetCenterX = boundingRect.left + boundingRect.width / 2
        const targetTopY = boundingRect.top

        // 获取气泡的宽度以便居中显示
        const tooltipRect = this.tooltip.getBoundingClientRect()
        const tooltipWidth = tooltipRect.width

        // 设置气泡位置：水平居中，垂直在target上方
        this.tooltip.style.left = targetCenterX - tooltipWidth / 2 + 'px'
        this.tooltip.style.top = targetTopY - 35 + 'px' // 在target上方35px
      } else if (event) {
        // 如果没有target，使用鼠标位置
        const x = event.pageX
        const y = event.pageY
        this.tooltip.style.left = x + 10 + 'px'
        this.tooltip.style.top = y - 30 + 'px'
      }
    }
  }
}

export default StructureDiagram
