import ComponentDatatype from"../../Core/ComponentDatatype.js";function getComponentReader(t){switch(t){case ComponentDatatype.BYTE:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getInt8(n+r*o)};case ComponentDatatype.UNSIGNED_BYTE:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getUint8(n+r*o)};case ComponentDatatype.SHORT:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getInt16(n+r*o,!0)};case ComponentDatatype.UNSIGNED_SHORT:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getUint16(n+r*o,!0)};case ComponentDatatype.INT:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getInt32(n+r*o,!0)};case ComponentDatatype.UNSIGNED_INT:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getUint32(n+r*o,!0)};case ComponentDatatype.FLOAT:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getFloat32(n+r*o,!0)};case ComponentDatatype.DOUBLE:return function(t,n,e,o,a){for(var r=0;r<e;++r)a[r]=t.getFloat64(n+r*o,!0)}}}export default getComponentReader;