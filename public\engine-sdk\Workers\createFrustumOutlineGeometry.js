define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./Plane-1b1689fd","./VertexFormat-9b18d410","./FrustumGeometry-79a0a462"],(function(e,t,r,n,a,i,u,o,c,s,p,m,f,h,d,g){"use strict";var k=0,y=1;function _(n){t.Check.typeOf.object("options",n),t.Check.typeOf.object("options.frustum",n.frustum),t.Check.typeOf.object("options.origin",n.origin),t.Check.typeOf.object("options.orientation",n.orientation);var a,u,o=n.frustum,c=n.orientation,s=n.origin,p=e.defaultValue(n._drawNearPlane,!0);o instanceof g.PerspectiveFrustum?(a=k,u=g.PerspectiveFrustum.packedLength):o instanceof g.OrthographicFrustum&&(a=y,u=g.OrthographicFrustum.packedLength),this._frustumType=a,this._frustum=o.clone(),this._origin=r.Cartesian3.clone(s),this._orientation=i.Quaternion.clone(c),this._drawNearPlane=p,this._workerName="createFrustumOutlineGeometry",this.packedLength=2+u+r.Cartesian3.packedLength+i.Quaternion.packedLength}_.pack=function(n,a,u){t.Check.typeOf.object("value",n),t.Check.defined("array",a),u=e.defaultValue(u,0);var o=n._frustumType,c=n._frustum;return a[u++]=o,o===k?(g.PerspectiveFrustum.pack(c,a,u),u+=g.PerspectiveFrustum.packedLength):(g.OrthographicFrustum.pack(c,a,u),u+=g.OrthographicFrustum.packedLength),r.Cartesian3.pack(n._origin,a,u),u+=r.Cartesian3.packedLength,i.Quaternion.pack(n._orientation,a,u),u+=i.Quaternion.packedLength,a[u]=n._drawNearPlane?1:0,a};var l=new g.PerspectiveFrustum,v=new g.OrthographicFrustum,b=new i.Quaternion,C=new r.Cartesian3;function F(t,r){return e.defined(r)&&(t=_.unpack(t,r)),_.createGeometry(t)}return _.unpack=function(n,a,u){t.Check.defined("array",n),a=e.defaultValue(a,0);var o,c=n[a++];c===k?(o=g.PerspectiveFrustum.unpack(n,a,l),a+=g.PerspectiveFrustum.packedLength):(o=g.OrthographicFrustum.unpack(n,a,v),a+=g.OrthographicFrustum.packedLength);var s=r.Cartesian3.unpack(n,a,C);a+=r.Cartesian3.packedLength;var p=i.Quaternion.unpack(n,a,b);a+=i.Quaternion.packedLength;var m=1===n[a];if(!e.defined(u))return new _({frustum:o,origin:s,orientation:p,_drawNearPlane:m});var f=c===u._frustumType?u._frustum:void 0;return u._frustum=o.clone(f),u._frustumType=c,u._origin=r.Cartesian3.clone(s,u._origin),u._orientation=i.Quaternion.clone(p,u._orientation),u._drawNearPlane=m,u},_.createGeometry=function(e){var t=e._frustumType,r=e._frustum,n=e._origin,i=e._orientation,u=e._drawNearPlane,o=new Float64Array(24);g.FrustumGeometry._computeNearFarPlanes(n,i,t,r,o);for(var c,h,d=new f.GeometryAttributes({position:new p.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:o})}),k=u?2:1,y=new Uint16Array(8*(k+1)),_=u?0:1;_<2;++_)c=u?8*_:0,h=4*_,y[c]=h,y[c+1]=h+1,y[c+2]=h+1,y[c+3]=h+2,y[c+4]=h+2,y[c+5]=h+3,y[c+6]=h+3,y[c+7]=h;for(_=0;_<2;++_)c=8*(k+_),h=4*_,y[c]=h,y[c+1]=h+4,y[c+2]=h+1,y[c+3]=h+5,y[c+4]=h+2,y[c+5]=h+6,y[c+6]=h+3,y[c+7]=h+7;return new p.Geometry({attributes:d,indices:y,primitiveType:m.PrimitiveType.LINES,boundingSphere:a.BoundingSphere.fromVertices(o)})},F}));