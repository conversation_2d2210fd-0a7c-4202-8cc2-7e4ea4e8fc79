import moment from 'moment'

import { bigscreenApi } from '@/api/index.js'
import { toUe5 } from '@/hooks/useUE/tools.js'
import { useEventBus } from '@vueuse/core'
import { defineStore } from 'pinia'

const useScreenPageStore = defineStore('screenData', {
  state: () => ({
    filterTree: [], // 底部
    mileageList: [], // 缩略图中里程号列表   [ { name: 'NK0+200',modelCode:'',prefix:'NK', evaluate: '优级', list: [{ name: '服务器' }] }]
    /*
    示例
    [{"name":"北线","mileage":[{"name":"NK0+000","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+100","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+200","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+300","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+400","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+500","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+600","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+700","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+800","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK0+900","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+000","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+100","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+200","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+300","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+400","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"NK1+500","evaluate":"中","modelCode":"","value":0,"list":[]}]},{"name":"南线","mileage":[{"name":"SK0+000","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+100","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+200","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+300","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+400","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+500","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+600","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+700","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+800","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK0+900","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+000","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+100","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+200","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+300","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+400","evaluate":"中","modelCode":"","value":0,"list":[]},{"name":"SK1+500","evaluate":"中","modelCode":"","value":0,"list":[]}]},{"name":"银城中路出口","mileage":[{"name":"YCZNK0+000","evaluate":"中","modelCode":"","value":0,"list":[]}]},{"name":"荣成路进口","mileage":[{"name":"RCZNK0+000","evaluate":"中","modelCode":"","value":0,"list":[]}]}]
    */
    transformData: {
      jobControl: {
        date: moment().format('YYYY-MM-DD'),
        currentStatus: ''
      },
      diseaseTracking: {
        date: moment().format('YYYY-MM-DD'),
        currentStatus: '发现'
      }
    }, // 页面之间传值
    /**是否展示Ai问题模式 */
    showAIQuestion: false,
    /**是否开启车辆孪生 */
    vehicleTwinOpen: false,
    /**是否开启问题模式 */
    isQuestionType: false
  }),
  getters: {},
  actions: {
    async getModeCodeMileageRelation() {
      this.mileageList = []
      const res = await bigscreenApi.getMileageList({})
      if (res.success) {
        res.result.forEach((item) => {
          const mileageItem = {
            name: item.name,
            mileage: item.mileage.map((_) => {
              return {
                name: _,
                evaluate: '',
                modelCode: '',
                modelCodeList: [],
                value: 0,
                list: []
              }
            })
          }
          this.mileageList.push(mileageItem)
        })
      }

      const resModelCode = await bigscreenApi.getModeCodeMileageRelation({ structure_type: 4 })
      if (resModelCode.success) {
        const codeMileageList = resModelCode.result
        this.mileageList.forEach((_line) => {
          _line.mileage.forEach((_) => {
            const code = codeMileageList.filter((_code) => _code.mileage === _.name)
            if (code.length > 0) {
              _.modelCode = code[0].modelCode
              _.modelCodeList = code.map((_) => _.modelCode)
              _.value = code[0].mileageValue
            }
          })
        })
      }
    },
    getDeviceTreeType() {
      bigscreenApi.getDeviceTreeType({}).then((res) => {
        if (res.success) {
          this.filterTree = res.result
        }
      })
    },
    clearPoint() {
      this.mileageList.forEach((item) => {
        item.mileage.forEach((_) => {
          _.list = []
        })
      })
      const busRadar = useEventBus('radar')
      const busVirtualPanel = useEventBus('virtualPanel')

      busRadar.emit(false)
      busVirtualPanel.emit(false)

      // 清除交通指示
      const obj = {
        isOn: false, // 是否开启标签
        // typeId: 1000123 // 设施设备类型ID（为0时指全部类型）
        typeId: 0 // 设施设备类型ID（为0时指全部类型）
      }
      toUe5('customPOIList', obj)
    },
    clearPointByPointType(pointType) {
      this.mileageList.forEach((item) => {
        item.mileage.forEach((_) => {
          _.list = _.list.filter((_item) => _item.pointType !== pointType)
        })
      })
    },

    /**
     * @description: 更新路面评价数据
     * @param {*} list    如：[
{
"modelCode": "SHSPDD16SLMLLM0005",
"cnt": 1,
"status": "中"
}]
     */
    refreshEvaluateLine(list) {
      this.mileageList.forEach((item) => {
        item.mileage.forEach((_) => {
          const evaluateItemList = list.filter((_item) => _.modelCodeList.includes(_item.modelCode))
          evaluateItemList.forEach((evaluateItem) => {
            _.evaluate = evaluateItem.status
            _[`lane${evaluateItem.lane}`] = evaluateItem.status
          })
        })
      })
    },
    /**
     * @description: 清除路面评价数据
     */
    clearEvaluateLine() {
      this.mileageList.forEach((item) => {
        item.mileage.forEach((_) => {
          _.evaluate = ''
          _.lane1 = ''
          _.lane2 = ''
        })
      })
    },
    /**
     * @description: 添加撒点数据
     * @param {*} list  [{name:'排水泵',mileage:'NK0+000'}]
     * @return {*}
     */
    addPoints(list) {
      const lineNameMap = {
        NK: '北线',
        SK: '南线',
        YC: '银城中路出口',
        RC: '荣成路进口'
      }

      list.forEach((item) => {
        const { mileage } = item
        const mileageNO = mileage.substring(0, mileage.length - 2) + '00'
        const linePrifix = mileage.substring(0, 2)
        if (['YC', 'RC'].includes(linePrifix)) {
          this.mileageList.find((_) => _.name === lineNameMap[linePrifix])?.mileage[0].list.push(item)
        } else {
          this.mileageList
            .find((_) => _.name === lineNameMap[linePrifix])
            ?.mileage.find((_) => _.name === mileageNO)
            ?.list.push(item)
        }
      })
    }
  }
})

export default useScreenPageStore
