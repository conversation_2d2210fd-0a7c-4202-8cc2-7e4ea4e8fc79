<template>
  <div class="device-defect-list-container">
    <PopupBg title="缺陷列表" width="800px" height="490px" @close="handlerClose">
      <tableList list-width="780px" list-data-height="360px" :head-list="tableHead" :list="tableData" @click-item="tableClick($event)"></tableList>
    </PopupBg>
  </div>
</template>

<script setup>
  import { onUnmounted, ref } from 'vue'
  import PopupBg from '@/components/PopupBg/index.vue'
  import tableList from '@/components/TableList/index.vue'
  import moment from 'moment'

  const { appContext } = getCurrentInstance()

  import lib from '@/utils/lib.ts'
  const emit = defineEmits(['close'])
  const handlerClose = () => {
    lib.popWindow.removeDialog('maintenanceWindow')
    lib.popWindow.removeDialog('WorkList')
    emit('close')
  }
  const tableHead = ref([
    { label: '缺陷类型', prop: 'defectTypeName' },
    { label: '缺陷等级', prop: 'level' },
    { label: '发现时间', prop: 'findTime' },
    { label: '处理状态', prop: 'status' }
  ])
  const tableData = ref([])

  onMounted(() => {
    lib.api.bigscreenApi.deviceDefectList({}).then((res) => {
      const list = res.result
      list.forEach((item) => {
        item.findTime = moment(item.discoveryDate).format('YYYY-MM-DD HH:mm:ss')
      })
      tableData.value = list
    })
  })
  const isSelectedItem = ref(false)

  lib.bus.busTreeTableStructure.on((type) => {
    if (type === 'clear') {
      tableData.value.forEach((_) => {
        _.isOn = false
      })
    }
  })
  const tableClick = (item) => {
    console.log('列表点击-----', item)
    lib.popWindow.removeDialog('maintenanceWindow')
    item.isOn = !item.isOn
    isSelectedItem.value = true

    lib._engineController.clearCzml()
    lib.popWindow.createPopWindow(
      './Components/DefectDetails/index.vue',
      {
        left: 1700,
        top: 700,
        tag: 'maintenanceWindow',
        appContext,
        appendParent: 'player'
      },
      item
    )

    lib.api.bigscreenApi.geDefectOrderDetails({ id: item.id }).then(async (res) => {
      console.log('缺陷详情', res)
      if (res.success && (res.result?.deviceCode || res.result?.structureCode)) {
        let code = res.result.deviceCode || res.result.structureCode
        const bimInfo = await lib._engineController.getBimInfoByCode(code)
        if (bimInfo) {
          lib._engineController.setColorByList([{ bimId: bimInfo.bimId, color: 'rgb(0,176,80)' }])
          lib._engineController.flyToBimId(bimInfo.bimId, -15)

          const icon = await lib.utils.convertImageToBase64(`images/defectIcon.png`)
          const cartesian = bimInfo.position

          const czml = {
            id: lib.utils.getRandomString(10),
            name: lib.utils.getRandomString(5),
            billboard: {
              // 图片
              image: icon,
              scale: 0.5,
              disableDepthTestDistance: 999999,
              horizontalOrigin: 'CENTER',
              verticalOrigin: 'BOTTOM'
            },
            position: {
              cartesian: [0, 0, 0]
            },
            onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
              lib.popWindow.createPopWindow(
                './Components/DefectDetails/index.vue',
                {
                  left: clickClinetX,
                  top: clickClinetY,
                  tag: 'maintenanceWindow',
                  appContext,
                  appendParent: 'player',
                  zIndex: 999,
                  // followPoint: { typeId: obj.typeId, id },
                  closeFunc: () => {
                    // toUe5('openCustomPOIWindow', {
                    //   id: id,
                    //   isOpen: false
                    // })
                  }
                },
                item
              )
            }
          }
          lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
        }
        console.log('bimInfo-------------', bimInfo)
      }
    })

    // // 缺陷列表有里程号
    // if (!item.detailPosition) {
    //   item.mileage = 'EK53+825'
    //   lib._engineController.clearSelect()

    //   // ! 没有里程号，直接弹窗
    //   // lib.popWindow.createPopWindow(
    //   //   './Components/DefectDetails/index.vue',
    //   //   {
    //   //     left: 1700,
    //   //     top: 700,
    //   //     tag: 'maintenanceWindow',
    //   //     appContext,
    //   //     appendParent: 'player'
    //   //   },
    //   //   item
    //   // )
    //   return
    // } else {
    //   if (!item.mileage) {
    //     item.mileage = item.detailPosition
    //   }
    // }

    // lib.api.getPointCode
    //   .getModeCodeFromMileage({
    //     mileage: item.mileage,
    //     structureType: 4 // 4代表路面，默认查4
    //   })
    //   .then(async (res) => {
    //     if (res.success) {
    //       item.modelCode = res.result.code

    //       const bimInfo = await lib._engineController.getBimInfoByCode(item.modelCode)
    //       if (bimInfo) {
    //         const icon = await lib.utils.convertImageToBase64(`images/defectIcon.png`)
    //         const cartesian = bimInfo.position

    //         const czml = {
    //           id: lib.utils.getRandomString(10),
    //           name: lib.utils.getRandomString(5),
    //           billboard: {
    //             // 图片
    //             image: icon,
    //             scale: 0.5,
    //             disableDepthTestDistance: 999999,
    //             horizontalOrigin: 'CENTER',
    //             verticalOrigin: 'BOTTOM'
    //           },
    //           position: {
    //             cartesian: [0, 0, 0]
    //           },
    //           onClick: (position, tagInstance, clickClinetX, clickClinetY) => {
    //             lib.popWindow.createPopWindow(
    //               './Components/DefectDetails/index.vue',
    //               {
    //                 left: clickClinetX,
    //                 top: clickClinetY,
    //                 tag: 'maintenanceWindow',
    //                 appContext,
    //                 appendParent: 'player',
    //                 zIndex: 999,
    //                 closeFunc: () => {}
    //               },
    //               item
    //             )
    //           }
    //         }
    //         lib._engineController.addCzmlByCartesian(cartesian, czml, lib.enumsList.point.突发事件撒点)
    //         lib._engineController.flyToBimId(bimInfo.bimId)
    //       }
    //     }
    //   })
  }
  onUnmounted(() => {
    isSelectedItem.value = false

    lib.popWindow.removeDialog('maintenanceWindow')
  })
</script>

<style lang="scss" scoped>
  .device-defect-list-container {
    width: 435px;
    height: 280px;
    background: url('@/assets/CommonPopup/popupBg.png');
    background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 11px 14px 0 45px;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .content {
      width: 407px;
      height: 200px;
      margin: 13px 21px 0 17px;
    }
  }
</style>
