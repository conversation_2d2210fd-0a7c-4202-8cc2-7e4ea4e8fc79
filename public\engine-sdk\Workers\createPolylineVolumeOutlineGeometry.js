define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./arrayRemoveDuplicates-7c710eac","./BoundingRectangle-34bbdfa6","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./PolylineVolumeGeometryLibrary-b0c96b85","./EllipsoidGeodesic-5ac97652","./PolylinePipeline-6bd12257"],(function(e,i,r,n,t,o,a,l,s,p,d,u,c,y,f,h,g,v,m,E,P,b,_,k,C){"use strict";function D(e,i){var r=new c.GeometryAttributes;r.position=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});var n,o,a=i.length,l=r.position.values.length/3,s=e.length/3,f=s/a,h=y.IndexDatatype.createTypedArray(l,2*a*(f+1)),g=0;n=0;var v=n*a;for(o=0;o<a-1;o++)h[g++]=o+v,h[g++]=o+v+1;for(h[g++]=a-1+v,h[g++]=v,n=f-1,v=n*a,o=0;o<a-1;o++)h[g++]=o+v,h[g++]=o+v+1;for(h[g++]=a-1+v,h[g++]=v,n=0;n<f-1;n++){var m=a*n,E=m+a;for(o=0;o<a;o++)h[g++]=o+m,h[g++]=o+E}var P=new d.Geometry({attributes:r,indices:y.IndexDatatype.createTypedArray(l,h),boundingSphere:t.BoundingSphere.fromVertices(e),primitiveType:u.PrimitiveType.LINES});return P}function w(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT);var o=t.polylinePositions,a=t.shapePositions;if(!e.defined(o))throw new i.DeveloperError("options.polylinePositions is required.");if(!e.defined(a))throw new i.DeveloperError("options.shapePositions is required.");this._positions=o,this._shape=a,this._ellipsoid=n.Ellipsoid.clone(e.defaultValue(t.ellipsoid,n.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(t.cornerType,_.CornerType.ROUNDED),this._granularity=e.defaultValue(t.granularity,r.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";var l=1+o.length*r.Cartesian3.packedLength;l+=1+a.length*r.Cartesian2.packedLength,this.packedLength=l+n.Ellipsoid.packedLength+2}w.pack=function(t,o,a){if(!e.defined(t))throw new i.DeveloperError("value is required");if(!e.defined(o))throw new i.DeveloperError("array is required");var l;a=e.defaultValue(a,0);var s=t._positions,p=s.length;for(o[a++]=p,l=0;l<p;++l,a+=r.Cartesian3.packedLength)r.Cartesian3.pack(s[l],o,a);var d=t._shape;for(p=d.length,o[a++]=p,l=0;l<p;++l,a+=r.Cartesian2.packedLength)r.Cartesian2.pack(d[l],o,a);return n.Ellipsoid.pack(t._ellipsoid,o,a),a+=n.Ellipsoid.packedLength,o[a++]=t._cornerType,o[a]=t._granularity,o};var L=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE),T={polylinePositions:void 0,shapePositions:void 0,ellipsoid:L,height:void 0,cornerType:void 0,granularity:void 0};w.unpack=function(t,o,a){if(!e.defined(t))throw new i.DeveloperError("array is required");var l;o=e.defaultValue(o,0);var s=t[o++],p=new Array(s);for(l=0;l<s;++l,o+=r.Cartesian3.packedLength)p[l]=r.Cartesian3.unpack(t,o);s=t[o++];var d=new Array(s);for(l=0;l<s;++l,o+=r.Cartesian2.packedLength)d[l]=r.Cartesian2.unpack(t,o);var u=n.Ellipsoid.unpack(t,o,L);o+=n.Ellipsoid.packedLength;var c=t[o++],y=t[o];return e.defined(a)?(a._positions=p,a._shape=d,a._ellipsoid=n.Ellipsoid.clone(u,a._ellipsoid),a._cornerType=c,a._granularity=y,a):(T.polylinePositions=p,T.shapePositions=d,T.cornerType=c,T.granularity=y,new w(T))};var G=new v.BoundingRectangle;function A(i,r){return e.defined(r)&&(i=w.unpack(i,r)),i._ellipsoid=n.Ellipsoid.clone(i._ellipsoid),w.createGeometry(i)}return w.createGeometry=function(e){var i=e._positions,n=g.arrayRemoveDuplicates(i,r.Cartesian3.equalsEpsilon),t=e._shape;if(t=_.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(t),!(n.length<2||t.length<3)){b.PolygonPipeline.computeWindingOrder2D(t)===b.WindingOrder.CLOCKWISE&&t.reverse();var o=v.BoundingRectangle.fromPoints(t,G),a=_.PolylineVolumeGeometryLibrary.computePositions(n,t,o,e,!1);return D(a,t)}},A}));