import { getToken } from '@/utils/auth'
import axios from 'axios'
import { PROJECT_ID } from '@/config/index.ts'
import router from '@/router/index.js'
// create an axios instance
const service = axios.create({
  timeout: 0 // request timeout
  // baseURL: process.env.ADMIN_SERVER
})

// 请求前拦截
service.interceptors.request.use(
  (config) => {
    config.baseURL = import.meta.env.VITE_VIDEO_URL
    const token = getToken()
    // const token = '5adeb796-9b8a-4213-81e7-fe6875342d26'
    token && (config.headers['x-auth-token'] = token)
    if (config.data && config.data.projectId === undefined) {
      // config.data.projectId = 28
      config.data.projectId = PROJECT_ID
    }
    return config
  },
  (error) => {
    // Do something with request error
    // console.log(error) // for debug
    Promise.reject(error)
  }
)

// 去请求后拦截
service.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.status === 1001) {
      router.push({ path: '/login' })
    }
    return res
  },
  (error) => {
    // console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)
export default service
