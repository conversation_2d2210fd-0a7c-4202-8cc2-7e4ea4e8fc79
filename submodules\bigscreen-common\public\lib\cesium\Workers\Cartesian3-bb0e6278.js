/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./defined-3b3eb2ba","./Math-b5f4d889"],(function(n,e,t){"use strict";function r(n,t){this.x=e.defaultValue(n,0),this.y=e.defaultValue(t,0)}r.fromElements=function(n,t,u){return e.defined(u)?(u.x=n,u.y=t,u):new r(n,t)},r.clone=function(n,t){if(e.defined(n))return e.defined(t)?(t.x=n.x,t.y=n.y,t):new r(n.x,n.y)},r.fromCartesian3=r.clone,r.fromCartesian4=r.clone,r.packedLength=2,r.pack=function(n,t,r){return r=e.defaultValue(r,0),t[r++]=n.x,t[r]=n.y,t},r.unpack=function(n,t,u){return t=e.defaultValue(t,0),e.defined(u)||(u=new r),u.x=n[t++],u.y=n[t],u},r.packArray=function(n,t){const u=n.length,a=2*u;e.defined(t)?(Array.isArray(t)||t.length===a)&&t.length!==a&&(t.length=a):t=new Array(a);for(let e=0;e<u;++e)r.pack(n[e],t,2*e);return t},r.unpackArray=function(n,t){const u=n.length;e.defined(t)?t.length=u/2:t=new Array(u/2);for(let e=0;e<u;e+=2){const u=e/2;t[u]=r.unpack(n,e,t[u])}return t},r.fromArray=r.unpack,r.maximumComponent=function(n){return Math.max(n.x,n.y)},r.minimumComponent=function(n){return Math.min(n.x,n.y)},r.minimumByComponent=function(n,e,t){return t.x=Math.min(n.x,e.x),t.y=Math.min(n.y,e.y),t},r.maximumByComponent=function(n,e,t){return t.x=Math.max(n.x,e.x),t.y=Math.max(n.y,e.y),t},r.clamp=function(n,e,r,u){const a=t.CesiumMath.clamp(n.x,e.x,r.x),o=t.CesiumMath.clamp(n.y,e.y,r.y);return u.x=a,u.y=o,u},r.magnitudeSquared=function(n){return n.x*n.x+n.y*n.y},r.magnitude=function(n){return Math.sqrt(r.magnitudeSquared(n))};const u=new r;r.distance=function(n,e){return r.subtract(n,e,u),r.magnitude(u)},r.distanceSquared=function(n,e){return r.subtract(n,e,u),r.magnitudeSquared(u)},r.normalize=function(n,e){const t=r.magnitude(n);return e.x=n.x/t,e.y=n.y/t,e},r.dot=function(n,e){return n.x*e.x+n.y*e.y},r.cross=function(n,e){return n.x*e.y-n.y*e.x},r.multiplyComponents=function(n,e,t){return t.x=n.x*e.x,t.y=n.y*e.y,t},r.divideComponents=function(n,e,t){return t.x=n.x/e.x,t.y=n.y/e.y,t},r.add=function(n,e,t){return t.x=n.x+e.x,t.y=n.y+e.y,t},r.subtract=function(n,e,t){return t.x=n.x-e.x,t.y=n.y-e.y,t},r.multiplyByScalar=function(n,e,t){return t.x=n.x*e,t.y=n.y*e,t},r.divideByScalar=function(n,e,t){return t.x=n.x/e,t.y=n.y/e,t},r.negate=function(n,e){return e.x=-n.x,e.y=-n.y,e},r.abs=function(n,e){return e.x=Math.abs(n.x),e.y=Math.abs(n.y),e};const a=new r;r.lerp=function(n,e,t,u){return r.multiplyByScalar(e,t,a),u=r.multiplyByScalar(n,1-t,u),r.add(a,u,u)};const o=new r,i=new r;r.angleBetween=function(n,e){return r.normalize(n,o),r.normalize(e,i),t.CesiumMath.acosClamped(r.dot(o,i))};const c=new r;function y(n,t,r){this.x=e.defaultValue(n,0),this.y=e.defaultValue(t,0),this.z=e.defaultValue(r,0)}r.mostOrthogonalAxis=function(n,e){const t=r.normalize(n,c);return r.abs(t,t),e=t.x<=t.y?r.clone(r.UNIT_X,e):r.clone(r.UNIT_Y,e)},r.equals=function(n,t){return n===t||e.defined(n)&&e.defined(t)&&n.x===t.x&&n.y===t.y},r.equalsArray=function(n,e,t){return n.x===e[t]&&n.y===e[t+1]},r.equalsEpsilon=function(n,r,u,a){return n===r||e.defined(n)&&e.defined(r)&&t.CesiumMath.equalsEpsilon(n.x,r.x,u,a)&&t.CesiumMath.equalsEpsilon(n.y,r.y,u,a)},r.ZERO=Object.freeze(new r(0,0)),r.ONE=Object.freeze(new r(1,1)),r.UNIT_X=Object.freeze(new r(1,0)),r.UNIT_Y=Object.freeze(new r(0,1)),r.prototype.clone=function(n){return r.clone(this,n)},r.prototype.equals=function(n){return r.equals(this,n)},r.prototype.equalsEpsilon=function(n,e,t){return r.equalsEpsilon(this,n,e,t)},r.prototype.toString=function(){return`(${this.x}, ${this.y})`},y.fromSpherical=function(n,t){e.defined(t)||(t=new y);const r=n.clock,u=n.cone,a=e.defaultValue(n.magnitude,1),o=a*Math.sin(u);return t.x=o*Math.cos(r),t.y=o*Math.sin(r),t.z=a*Math.cos(u),t},y.fromElements=function(n,t,r,u){return e.defined(u)?(u.x=n,u.y=t,u.z=r,u):new y(n,t,r)},y.clone=function(n,t){if(e.defined(n))return e.defined(t)?(t.x=n.x,t.y=n.y,t.z=n.z,t):new y(n.x,n.y,n.z)},y.fromCartesian4=y.clone,y.packedLength=3,y.pack=function(n,t,r){return r=e.defaultValue(r,0),t[r++]=n.x,t[r++]=n.y,t[r]=n.z,t},y.unpack=function(n,t,r){return t=e.defaultValue(t,0),e.defined(r)||(r=new y),r.x=n[t++],r.y=n[t++],r.z=n[t],r},y.packArray=function(n,t){const r=n.length,u=3*r;e.defined(t)?(Array.isArray(t)||t.length===u)&&t.length!==u&&(t.length=u):t=new Array(u);for(let e=0;e<r;++e)y.pack(n[e],t,3*e);return t},y.unpackArray=function(n,t){const r=n.length;e.defined(t)?t.length=r/3:t=new Array(r/3);for(let e=0;e<r;e+=3){const r=e/3;t[r]=y.unpack(n,e,t[r])}return t},y.fromArray=y.unpack,y.maximumComponent=function(n){return Math.max(n.x,n.y,n.z)},y.minimumComponent=function(n){return Math.min(n.x,n.y,n.z)},y.minimumByComponent=function(n,e,t){return t.x=Math.min(n.x,e.x),t.y=Math.min(n.y,e.y),t.z=Math.min(n.z,e.z),t},y.maximumByComponent=function(n,e,t){return t.x=Math.max(n.x,e.x),t.y=Math.max(n.y,e.y),t.z=Math.max(n.z,e.z),t},y.clamp=function(n,e,r,u){const a=t.CesiumMath.clamp(n.x,e.x,r.x),o=t.CesiumMath.clamp(n.y,e.y,r.y),i=t.CesiumMath.clamp(n.z,e.z,r.z);return u.x=a,u.y=o,u.z=i,u},y.magnitudeSquared=function(n){return n.x*n.x+n.y*n.y+n.z*n.z},y.magnitude=function(n){return Math.sqrt(y.magnitudeSquared(n))};const s=new y;y.distance=function(n,e){return y.subtract(n,e,s),y.magnitude(s)},y.distanceSquared=function(n,e){return y.subtract(n,e,s),y.magnitudeSquared(s)},y.normalize=function(n,e){const t=y.magnitude(n);return e.x=n.x/t,e.y=n.y/t,e.z=n.z/t,e},y.dot=function(n,e){return n.x*e.x+n.y*e.y+n.z*e.z},y.multiplyComponents=function(n,e,t){return t.x=n.x*e.x,t.y=n.y*e.y,t.z=n.z*e.z,t},y.divideComponents=function(n,e,t){return t.x=n.x/e.x,t.y=n.y/e.y,t.z=n.z/e.z,t},y.add=function(n,e,t){return t.x=n.x+e.x,t.y=n.y+e.y,t.z=n.z+e.z,t},y.subtract=function(n,e,t){return t.x=n.x-e.x,t.y=n.y-e.y,t.z=n.z-e.z,t},y.multiplyByScalar=function(n,e,t){return t.x=n.x*e,t.y=n.y*e,t.z=n.z*e,t},y.divideByScalar=function(n,e,t){return t.x=n.x/e,t.y=n.y/e,t.z=n.z/e,t},y.negate=function(n,e){return e.x=-n.x,e.y=-n.y,e.z=-n.z,e},y.abs=function(n,e){return e.x=Math.abs(n.x),e.y=Math.abs(n.y),e.z=Math.abs(n.z),e};const f=new y;y.lerp=function(n,e,t,r){return y.multiplyByScalar(e,t,f),r=y.multiplyByScalar(n,1-t,r),y.add(f,r,r)};const l=new y,d=new y;y.angleBetween=function(n,e){y.normalize(n,l),y.normalize(e,d);const t=y.dot(l,d),r=y.magnitude(y.cross(l,d,l));return Math.atan2(r,t)};const m=new y;y.mostOrthogonalAxis=function(n,e){const t=y.normalize(n,m);return y.abs(t,t),e=t.x<=t.y?t.x<=t.z?y.clone(y.UNIT_X,e):y.clone(y.UNIT_Z,e):t.y<=t.z?y.clone(y.UNIT_Y,e):y.clone(y.UNIT_Z,e)},y.projectVector=function(n,e,t){const r=y.dot(n,e)/y.dot(e,e);return y.multiplyByScalar(e,r,t)},y.equals=function(n,t){return n===t||e.defined(n)&&e.defined(t)&&n.x===t.x&&n.y===t.y&&n.z===t.z},y.equalsArray=function(n,e,t){return n.x===e[t]&&n.y===e[t+1]&&n.z===e[t+2]},y.equalsEpsilon=function(n,r,u,a){return n===r||e.defined(n)&&e.defined(r)&&t.CesiumMath.equalsEpsilon(n.x,r.x,u,a)&&t.CesiumMath.equalsEpsilon(n.y,r.y,u,a)&&t.CesiumMath.equalsEpsilon(n.z,r.z,u,a)},y.cross=function(n,e,t){const r=n.x,u=n.y,a=n.z,o=e.x,i=e.y,c=e.z,y=u*c-a*i,s=a*o-r*c,f=r*i-u*o;return t.x=y,t.y=s,t.z=f,t},y.midpoint=function(n,e,t){return t.x=.5*(n.x+e.x),t.y=.5*(n.y+e.y),t.z=.5*(n.z+e.z),t},y.fromDegrees=function(n,e,r,u,a){return n=t.CesiumMath.toRadians(n),e=t.CesiumMath.toRadians(e),y.fromRadians(n,e,r,u,a)};let x=new y,h=new y;const p=new y(40680631590769,40680631590769,40408299984661.445);y.fromRadians=function(n,t,r,u,a){r=e.defaultValue(r,0);const o=e.defined(u)?u.radiiSquared:p,i=Math.cos(t);x.x=i*Math.cos(n),x.y=i*Math.sin(n),x.z=Math.sin(t),x=y.normalize(x,x),y.multiplyComponents(o,x,h);const c=Math.sqrt(y.dot(x,h));return h=y.divideByScalar(h,c,h),x=y.multiplyByScalar(x,r,x),e.defined(a)||(a=new y),y.add(h,x,a)},y.fromDegreesArray=function(n,t,r){const u=n.length;e.defined(r)?r.length=u/2:r=new Array(u/2);for(let e=0;e<u;e+=2){const u=n[e],a=n[e+1],o=e/2;r[o]=y.fromDegrees(u,a,0,t,r[o])}return r},y.fromRadiansArray=function(n,t,r){const u=n.length;e.defined(r)?r.length=u/2:r=new Array(u/2);for(let e=0;e<u;e+=2){const u=n[e],a=n[e+1],o=e/2;r[o]=y.fromRadians(u,a,0,t,r[o])}return r},y.fromDegreesArrayHeights=function(n,t,r){const u=n.length;e.defined(r)?r.length=u/3:r=new Array(u/3);for(let e=0;e<u;e+=3){const u=n[e],a=n[e+1],o=n[e+2],i=e/3;r[i]=y.fromDegrees(u,a,o,t,r[i])}return r},y.fromRadiansArrayHeights=function(n,t,r){const u=n.length;e.defined(r)?r.length=u/3:r=new Array(u/3);for(let e=0;e<u;e+=3){const u=n[e],a=n[e+1],o=n[e+2],i=e/3;r[i]=y.fromRadians(u,a,o,t,r[i])}return r},y.ZERO=Object.freeze(new y(0,0,0)),y.ONE=Object.freeze(new y(1,1,1)),y.UNIT_X=Object.freeze(new y(1,0,0)),y.UNIT_Y=Object.freeze(new y(0,1,0)),y.UNIT_Z=Object.freeze(new y(0,0,1)),y.prototype.clone=function(n){return y.clone(this,n)},y.prototype.equals=function(n){return y.equals(this,n)},y.prototype.equalsEpsilon=function(n,e,t){return y.equalsEpsilon(this,n,e,t)},y.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z})`},n.Cartesian2=r,n.Cartesian3=y}));
