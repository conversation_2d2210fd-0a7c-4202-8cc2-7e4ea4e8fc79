<template>
  <PopupBg width="1573px" height="828px" :title="imgName || '图片预览'">
    <div class="relative">
      <img src="@/assets/ScreenRight/Perception/goPre.png" class="w28 h45 absolute top-280 left-12 cursor-pointer z-99" @click="gotoPre" />
      <img src="@/assets/ScreenRight/Perception/goNext.png" class="w28 h45 absolute top-280 right-12 cursor-pointer z-99" @click="gotoNext" />
      <el-carousel height="760px" arrow="never" ref="refCarousel">
        <el-carousel-item v-for="(url, ind) in picList" :key="ind">
          <div class="flex justify-between ml-50">
            <img width="1440" height="726" :src="url" alt="" />
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </PopupBg>
</template>

<script setup lang="ts">
  import PopupBg from '@/components/PopupBg/index.vue'
  import { ElCarousel, ElCarouselItem } from 'element-plus'
  defineProps({
    picList: {
      type: Array,
      default: () => []
    },
    imgName: {
      type: String,
      default: ''
    }
  })
  const refCarousel = ref()
  const gotoPre = () => {
    refCarousel.value.prev()
  }
  const gotoNext = () => {
    refCarousel.value.next()
  }
</script>

<style scoped></style>
