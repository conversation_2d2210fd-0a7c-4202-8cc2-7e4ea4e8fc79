define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./VertexFormat-9b18d410","./GeometryInstance-d4317835","./arrayRemoveDuplicates-7c710eac","./BoundingRectangle-34bbdfa6","./AxisAlignedBoundingBox-9ebc7d89","./EllipsoidTangentPlane-265ae537","./OrientedBoundingBox-d0a49c02","./CoplanarPolygonGeometryLibrary-b66dad27","./ArcType-10662e8b","./EllipsoidRhumbLine-f49ff2c9","./PolygonPipeline-cdeb6a0b","./PolygonGeometryLibrary-4b76b18a"],(function(e,t,a,n,r,o,i,s,l,p,c,y,d,u,m,g,b,f,h,v,C,x,P,A,w,F,G,L,E,T,k){"use strict";var D=new a.Cartesian3,_=new P.BoundingRectangle,V=new a.Cartesian2,R=new a.Cartesian2,B=new a.Cartesian3,I=new a.Cartesian3,M=new a.Cartesian3,H=new a.Cartesian3,O=new a.Cartesian3,S=new a.Cartesian3,z=new o.Quaternion,N=new i.Matrix3,Q=new i.Matrix3,j=new a.Cartesian3;function U(e,t,n,r,s,l,u,m){var g=e.positions,f=T.PolygonPipeline.triangulate(e.positions2D,e.holes);f.length<3&&(f=[0,1,2]);var h=b.IndexDatatype.createTypedArray(g.length,f.length);h.set(f);var v=N;if(0!==r){var C=o.Quaternion.fromAxisAngle(l,r,z);if(v=i.Matrix3.fromQuaternion(C,v),t.tangent||t.bitangent){C=o.Quaternion.fromAxisAngle(l,-r,z);var x=i.Matrix3.fromQuaternion(C,Q);u=a.Cartesian3.normalize(i.Matrix3.multiplyByVector(x,u,u),u),t.bitangent&&(m=a.Cartesian3.normalize(a.Cartesian3.cross(l,u,m),m))}}else v=i.Matrix3.clone(i.Matrix3.IDENTITY,v);var P=R;t.st&&(P.x=n.x,P.y=n.y);for(var A=g.length,w=3*A,F=new Float64Array(w),G=t.normal?new Float32Array(w):void 0,L=t.tangent?new Float32Array(w):void 0,E=t.bitangent?new Float32Array(w):void 0,k=t.st?new Float32Array(2*A):void 0,_=0,B=0,I=0,M=0,H=0,O=0;O<A;O++){var S=g[O];if(F[_++]=S.x,F[_++]=S.y,F[_++]=S.z,t.st){var j=i.Matrix3.multiplyByVector(v,S,D),U=s(j,V);a.Cartesian2.subtract(U,P,U);var Y=a.CesiumMath.clamp(U.x/n.width,0,1),q=a.CesiumMath.clamp(U.y/n.height,0,1);k[H++]=Y,k[H++]=q}t.normal&&(G[B++]=l.x,G[B++]=l.y,G[B++]=l.z),t.tangent&&(L[M++]=u.x,L[M++]=u.y,L[M++]=u.z),t.bitangent&&(E[I++]=m.x,E[I++]=m.y,E[I++]=m.z)}var J=new d.GeometryAttributes;return t.position&&(J.position=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:F})),t.normal&&(J.normal=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G})),t.tangent&&(J.tangent=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),t.bitangent&&(J.bitangent=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.st&&(J.st=new c.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:k})),new c.Geometry({attributes:J,indices:h,primitiveType:y.PrimitiveType.TRIANGLES})}function Y(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var r=a.polygonHierarchy;t.Check.defined("options.polygonHierarchy",r);var o=e.defaultValue(a.vertexFormat,v.VertexFormat.DEFAULT);this._vertexFormat=v.VertexFormat.clone(o),this._polygonHierarchy=r,this._stRotation=e.defaultValue(a.stRotation,0),this._ellipsoid=n.Ellipsoid.clone(e.defaultValue(a.ellipsoid,n.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=k.PolygonGeometryLibrary.computeHierarchyPackedLength(r)+v.VertexFormat.packedLength+n.Ellipsoid.packedLength+2}Y.fromPositions=function(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT),t.Check.defined("options.positions",a.positions);var n={polygonHierarchy:{positions:a.positions},vertexFormat:a.vertexFormat,stRotation:a.stRotation,ellipsoid:a.ellipsoid};return new Y(n)},Y.pack=function(a,r,o){return t.Check.typeOf.object("value",a),t.Check.defined("array",r),o=e.defaultValue(o,0),o=k.PolygonGeometryLibrary.packPolygonHierarchy(a._polygonHierarchy,r,o),n.Ellipsoid.pack(a._ellipsoid,r,o),o+=n.Ellipsoid.packedLength,v.VertexFormat.pack(a._vertexFormat,r,o),o+=v.VertexFormat.packedLength,r[o++]=a._stRotation,r[o]=a.packedLength,r};var q=n.Ellipsoid.clone(n.Ellipsoid.UNIT_SPHERE),J=new v.VertexFormat,W={polygonHierarchy:{}};function Z(t,a){return e.defined(a)&&(t=Y.unpack(t,a)),Y.createGeometry(t)}return Y.unpack=function(a,r,o){t.Check.defined("array",a),r=e.defaultValue(r,0);var i=k.PolygonGeometryLibrary.unpackPolygonHierarchy(a,r);r=i.startingIndex,delete i.startingIndex;var s=n.Ellipsoid.unpack(a,r,q);r+=n.Ellipsoid.packedLength;var l=v.VertexFormat.unpack(a,r,J);r+=v.VertexFormat.packedLength;var p=a[r++],c=a[r];return e.defined(o)||(o=new Y(W)),o._polygonHierarchy=i,o._ellipsoid=n.Ellipsoid.clone(s,o._ellipsoid),o._vertexFormat=v.VertexFormat.clone(l,o._vertexFormat),o._stRotation=p,o.packedLength=c,o},Y.createGeometry=function(e){var t=e._vertexFormat,n=e._polygonHierarchy,o=e._stRotation,i=n.positions;if(i=x.arrayRemoveDuplicates(i,a.Cartesian3.equalsEpsilon,!0),!(i.length<3)){var s=B,l=I,p=M,y=O,d=S,u=G.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(i,H,y,d);if(u){if(s=a.Cartesian3.cross(y,d,s),s=a.Cartesian3.normalize(s,s),!a.Cartesian3.equalsEpsilon(H,a.Cartesian3.ZERO,a.CesiumMath.EPSILON6)){var g=e._ellipsoid.geodeticSurfaceNormal(H,j);a.Cartesian3.dot(s,g)<0&&(s=a.Cartesian3.negate(s,s),y=a.Cartesian3.negate(y,y))}var f=G.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(H,y,d),h=G.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(H,y,d);t.tangent&&(l=a.Cartesian3.clone(y,l)),t.bitangent&&(p=a.Cartesian3.clone(d,p));var v=k.PolygonGeometryLibrary.polygonsFromHierarchy(n,f,!1),P=v.hierarchy,A=v.polygons;if(0!==P.length){i=P[0].outerRing;for(var w=r.BoundingSphere.fromPoints(i),F=k.PolygonGeometryLibrary.computeBoundingRectangle(s,h,i,o,_),L=[],E=0;E<A.length;E++){var T=new C.GeometryInstance({geometry:U(A[E],t,F,o,h,s,l,p)});L.push(T)}var D=m.GeometryPipeline.combineInstances(L)[0];D.attributes.position.values=new Float64Array(D.attributes.position.values),D.indices=b.IndexDatatype.createTypedArray(D.attributes.position.values.length/3,D.indices);var V=D.attributes;return t.position||delete V.position,new c.Geometry({attributes:V,indices:D.indices,primitiveType:D.primitiveType,boundingSphere:w})}}}},Z}));