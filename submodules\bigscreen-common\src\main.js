import { createApp } from 'vue'

import App from './App.vue'

import 'element-plus/dist/index.css'
import 'suit-datav/lib/styles/index.css'
import './permission.js'
import 'animate.css'
import 'virtual:uno.css'
import 'echarts-liquidfill'
import directives from './directives'
import router from './router'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import BinDatav from 'suit-datav'

const app = createApp(App)

// console.log(import.meta.env.VITE_BASE_URL, process.env.NODE_ENV)

// if (process.env.NODE_ENV !== 'development') {
//   // 只在开发环境打印到控制台
//   console.log = function() {}
//   console.error = function() {}
// }

app.use(ElementPlus)
app.use(router)
app.use(createPinia())
app.use(BinDatav)
app.use(directives)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
