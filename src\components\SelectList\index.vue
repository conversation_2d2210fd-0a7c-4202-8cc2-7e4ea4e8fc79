<template>
  <div class="select-list-com">
    <!-- :suffix-icon="IconDropDown" -->
    <el-select
      :clearable="clearable"
      popper-class="select-list-com-popper"
      v-model="selectValue"
      :placeholder="placeholder"
      size="mini"
      v-bind="$attrs"
      @change="selectValueChange"
      :style="{ width: width, height: height }">
      <el-option
        :disabled="item?.disabled"
        v-for="item in optionList"
        :key="item[propertyName?.id]"
        :label="item[propertyName?.name]"
        :value="item[propertyName?.id]">
        <span :style="`padding-left: ${item?.level * 10}px;`">
          {{ item[propertyName?.name] }}
        </span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: ''
  }
</script>
<script setup>
  import { onBeforeMount, ref, toRefs } from 'vue'

  // import IconDropDown from '../IconDropDown/index.vue'
  const props = defineProps({
    width: {
      type: String,
      default: '192px'
    },
    height: {
      type: String,
      default: '36px'
    },
    propertyName: {
      type: Object,
      default: () => {
        return {
          id: 'id',
          name: 'name'
        }
      }
    },
    optionList: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    // value: {
    //   type: String,
    //   Array,
    //   default: ''
    // },
    clearable: {
      type: Boolean,
      default: true
    }
  })
  // const emit = defineEmits(['update:value'])
  const { width, placeholder } = toRefs(props)
  const selectValue = defineModel('selectValue')

  // const isShowRightIcon = ref(0)
  const selectValueChange = (val) => {
    // if (!value) {
    //   isShowRightIcon.value = 0
    // }
    // emit('update:value', val)
  }
</script>
<style lang="scss">
  // .select-list-com,
  // .btn-wrap {
  //   .el-select,
  //   .el-select--large,
  //   .btn-wrap {
  //     .el-input,
  //     .el-input--large,
  //     .el-input--suffix {
  //       .el-input__wrapper {
  //         color: #ffffff;
  //         background-color: #004d84;
  //         border: 1px solid #00b2ff;
  //         .el-input__inner {
  //           padding: 0;
  //         }
  //       }
  //     }
  //   }
  // }
</style>
<style scoped lang="scss">
  .select-list-com {
    // --el-fill-color-blank: transparent;
    :deep(.el-select) {
      --el-select-input-focus-border-color: #47b7ff;
      --el-border-color: #47b7ff;
    }
    :deep(.el-select__wrapper),
    :deep(.el-input__wrapper) {
      height: 36px;
      min-height: 36px;
      color: #8dd8ff;
      background: url('@/assets/CommonPopup/selectList.png') no-repeat !important;
      background-size: 100% 36px;
      box-shadow: none;
    }
    :deep(.el-select__placeholder) {
      color: #8dd8ff;
    }
    :deep(.el-input__inner) {
      height: 36px;
      font-size: 16px;
      color: #8dd8ff;
      background-color: transparent !important;
      border: none;
      box-shadow: none;
    }

    // display: inline-block;

    // // height: 50%;
    // :deep(.el-select) {
    //   .el-select__wrapper {
    //     min-height: 56px;

    //     // line-height: 56px;
    //     background-color: #004d84 !important;
    //     border: 1px solid #00b2ff;
    //     box-shadow: none;
    //     .el-select__selection {
    //       .el-select__placeholder {
    //         font-family: 'Alibaba PuHuiTi';

    //         // font-size: 24px;
    //         font-style: normal;
    //         font-weight: 400;
    //         line-height: 24px;
    //         color: #ffffff;
    //         text-align: left;
    //         text-transform: none;
    //       }

    //       // 没有数据的情况下
    //       .is-transparent {
    //         font-family: 'Alibaba PuHuiTi';

    //         // font-size: 24px;
    //         color: #ffffff;
    //       }
    //       .el-select__selected-item {
    //         .el-tag.el-tag--info {
    //           background: rgb(255 255 255 / 30%);
    //           .el-tag__content {
    //             font-size: 20px;
    //             color: #ffffff;
    //           }
    //           .el-tag__close {
    //             color: #ffffff;
    //           }
    //         }
    //       }
    //     }
    //     .el-select__suffix {
    //       .el-icon,
    //       .el-select__caret {
    //         // font-size: 24px;
    //         color: #ffffff;
    //       }
    //     }
    //   }
    // }
  }
</style>
<style>
  .select-list-com-popper {
    background: #093161;

    --el-fill-color-blank: transparent;
    --el-fill-color-light: transparent;
    --el-text-color-regular: #ffffff;
    --el-border-color-light: #47b7ff;
  }
</style>
