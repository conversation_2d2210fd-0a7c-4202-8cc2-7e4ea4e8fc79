<template>
  <div class="drag-handler-example">
    <h1>CanvasDragHandler 使用示例</h1>

    <div class="demo-content">
      <!-- Canvas 区域 -->
      <div class="canvas-section">
        <h2>Canvas 区域</h2>
        <canvas id="example-canvas" ref="canvasRef" width="400" height="300"></canvas>
        <div class="canvas-info">
          <p>使用封装的 CanvasDragHandler 类</p>
          <p>只有 isCamera=true 的元素可以拖拽</p>
        </div>
      </div>

      <!-- 目标区域 -->
      <div class="drop-section">
        <h2>放置目标区域</h2>

        <!-- 目标区域使用 data-zone-id 属性 -->
        <div class="drop-zone" data-zone-id="project-zone" :class="{ active: activeZone === 'project-zone' }">
          <div class="zone-header">项目区域</div>
          <div class="zone-content">
            <div v-if="projectItems.length" class="dropped-items">
              <div v-for="(item, index) in projectItems" :key="index" class="dropped-item">
                <span class="item-type">{{ item.object?.type }}</span>
                <span class="item-data">{{ item.object?.id }}</span>
              </div>
            </div>
            <div v-else class="empty-hint">拖拽项目元素到这里</div>
          </div>
        </div>

        <!-- 目标区域使用 class 名称 -->
        <div class="drop-zone workspace-zone" data-zone-id="workspace-zone" :class="{ active: activeZone === 'workspace-zone' }">
          <div class="zone-header">工作区域</div>
          <div class="zone-content">
            <div v-if="workspaceItems.length" class="dropped-items">
              <div v-for="(item, index) in workspaceItems" :key="index" class="dropped-item">
                <span class="item-type">{{ item.object?.type }}</span>
                <span class="item-data">{{ item.object?.id }}</span>
              </div>
            </div>
            <div v-else class="empty-hint">拖拽工作元素到这里</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="addCameraElement" class="btn">添加相机元素</button>
      <button @click="addNormalElement" class="btn">添加普通元素</button>
      <button @click="clearAll" class="btn">清空所有</button>
    </div>

    <!-- 状态显示 -->
    <div class="status-info">
      <h3>状态信息</h3>
      <p>当前活动区域: {{ activeZone || '无' }}</p>
      <p>项目区域元素: {{ projectItems.length }}</p>
      <p>工作区域元素: {{ workspaceItems.length }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue'
  import { fabric } from 'fabric'
  import { CanvasDragHandler, DragData, injectDragStyles } from '@/utils/CanvasDragHandler/CanvasDragHandler'

  // 响应式数据
  const canvasRef = ref<HTMLCanvasElement>()
  const activeZone = ref('')
  const projectItems = ref<any[]>([])
  const workspaceItems = ref<any[]>([])

  let fabricCanvas: fabric.Canvas | null = null
  let dragHandler: CanvasDragHandler | null = null

  // 初始化Canvas和拖拽处理器
  const initCanvas = () => {
    if (!canvasRef.value) return

    // 创建Fabric.js Canvas
    fabricCanvas = new fabric.Canvas(canvasRef.value, {
      backgroundColor: '#f5f5f5',
      isDrawingMode: false,
      selection: true,
      preserveObjectStacking: true
    })

    // 添加一些初始元素
    addInitialElements()

    // 创建拖拽处理器
    dragHandler = new CanvasDragHandler({
      canvas: fabricCanvas,
      dropZoneSelector: '.drop-zone', // 目标区域选择器

      // 自定义拖拽条件：只有isCamera=true的元素可以拖拽
      canDrag: (obj: fabric.Object) => {
        const isCamera = (obj as any).isCamera
        console.log(`检查元素 ${obj.type} 是否可拖拽: ${isCamera}`)
        return !!isCamera
      },

      // 自定义数据提取
      getCustomData: (obj: fabric.Object) => {
        return (
          (obj as any).customData || {
            id: `element-${Date.now()}`,
            type: obj.type,
            isCamera: (obj as any).isCamera
          }
        )
      },

      // 拖拽成功回调
      onDrop: (data: DragData, zoneId: string, zoneElement: Element) => {
        console.log('元素放置成功:', { data, zoneId })

        const dropItem = {
          ...data,
          droppedAt: new Date().toISOString(),
          zoneId
        }

        // 根据zone ID分配到不同数组
        if (zoneId === 'project-zone') {
          projectItems.value.push(dropItem)
        } else if (zoneId === 'workspace-zone') {
          workspaceItems.value.push(dropItem)
        }

        // 清除活动区域状态
        activeZone.value = ''
      },

      // 拖拽开始回调
      onDragStart: (obj: fabric.Object, data: DragData) => {
        console.log('拖拽开始:', { obj: obj.type, data })
      },

      // 拖拽结束回调
      onDragEnd: (success: boolean, data?: DragData, zoneId?: string) => {
        console.log('拖拽结束:', { success, zoneId })
        activeZone.value = ''
      },

      // 区域变化回调（实时更新activeZone）
      onZoneChange: (zoneId: string | null) => {
        activeZone.value = zoneId || ''
      },

      // 自定义预览样式
      previewStyle: {
        borderRadius: '12px',
        border: '3px solid #007bff',
        fontSize: '14px'
      },

      // 启用调试日志
      debug: true
    })

    // 注入拖拽样式
    injectDragStyles()
  }

  // 添加初始元素
  const addInitialElements = () => {
    if (!fabricCanvas) return

    // 相机元素（可拖拽）
    const camera = new fabric.Rect({
      left: 50,
      top: 50,
      width: 80,
      height: 60,
      fill: '#4CAF50',
      stroke: '#2E7D32',
      strokeWidth: 2,
      rx: 8,
      ry: 8,
      hasControls: false,
      moveCursor: 'grab',
      hoverCursor: 'grab',
      lockMovementX: true, // 禁止X轴移动
      lockMovementY: true // 禁止Y轴移动
    })

    camera.set('customData', {
      id: 'camera-001',
      type: 'camera',
      name: '主摄像头',
      status: 'active'
    })
    camera.set('isCamera', true)

    // 普通元素（不可拖拽）
    const decoration = new fabric.Circle({
      left: 200,
      top: 80,
      radius: 30,
      fill: '#FF9800',
      stroke: '#F57C00',
      strokeWidth: 2
    })

    decoration.set('customData', {
      id: 'decoration-001',
      type: 'decoration',
      name: '装饰元素'
    })
    decoration.set('isCamera', false)

    fabricCanvas.add(camera, decoration)
    fabricCanvas.renderAll()
  }

  // 添加相机元素
  const addCameraElement = () => {
    if (!fabricCanvas) return

    const colors = ['#4CAF50', '#2196F3', '#FF5722', '#9C27B0']
    const color = colors[Math.floor(Math.random() * colors.length)]

    const camera = new fabric.Rect({
      left: Math.random() * 300,
      top: Math.random() * 200,
      width: 60 + Math.random() * 40,
      height: 40 + Math.random() * 30,
      fill: color,
      stroke: '#333',
      strokeWidth: 2,
      rx: 6,
      ry: 6
    })

    camera.set('customData', {
      id: `camera-${Date.now()}`,
      type: 'camera',
      name: `摄像头-${Date.now()}`,
      status: 'active'
    })
    camera.set('isCamera', true)

    fabricCanvas.add(camera)
    fabricCanvas.renderAll()
  }

  // 添加普通元素
  const addNormalElement = () => {
    if (!fabricCanvas) return

    const element = new fabric.Triangle({
      left: Math.random() * 300,
      top: Math.random() * 200,
      width: 40 + Math.random() * 30,
      height: 40 + Math.random() * 30,
      fill: '#FFC107',
      stroke: '#FF8F00',
      strokeWidth: 2
    })

    element.set('customData', {
      id: `element-${Date.now()}`,
      type: 'normal',
      name: '普通元素'
    })
    element.set('isCamera', false)

    fabricCanvas.add(element)
    fabricCanvas.renderAll()
  }

  // 清空所有
  const clearAll = () => {
    if (fabricCanvas) {
      fabricCanvas.clear()
      fabricCanvas.backgroundColor = '#f5f5f5'
      fabricCanvas.renderAll()
    }
    projectItems.value = []
    workspaceItems.value = []
    activeZone.value = ''
  }

  // 组件生命周期
  onMounted(() => {
    initCanvas()
  })

  onBeforeUnmount(() => {
    if (dragHandler) {
      dragHandler.destroy()
    }
  })
</script>

<style lang="scss" scoped>
  .drag-handler-example {
    max-width: 1200px;
    padding: 20px;
    margin: 0 auto;
    font-family: Arial, sans-serif;
    h1 {
      margin-bottom: 30px;
      font-size: 28px;
      color: #333333;
      text-align: center;
    }
    .demo-content {
      display: flex;
      gap: 30px;
      margin-bottom: 30px;

      @media (width <= 768px) {
        flex-direction: column;
      }
    }
    .canvas-section {
      flex: 1;
      h2 {
        margin-bottom: 15px;
        font-size: 20px;
        color: #555555;
      }
      canvas {
        cursor: grab;
        border: 2px solid #dddddd;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgb(0 0 0 / 10%);
      }
      .canvas-info {
        padding: 10px;
        margin-top: 10px;
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        border-radius: 4px;
        p {
          margin: 5px 0;
          font-size: 14px;
          color: #666666;
        }
      }
    }
    .drop-section {
      flex: 1;
      h2 {
        margin-bottom: 15px;
        font-size: 20px;
        color: #555555;
      }
    }
    .drop-zone {
      min-height: 120px;
      padding: 20px;
      margin-bottom: 20px;
      background-color: #fafafa;
      border: 3px dashed #cccccc;
      border-radius: 8px;
      transition: all 0.3s ease;
      &.active {
        background-color: #e3f2fd;
        border-color: #007bff;
        transform: scale(1.02);
      }
      &[data-zone-id='project-zone'] {
        border-left-color: #4caf50;
        &.active {
          background-color: #e8f5e8;
          border-color: #4caf50;
        }
      }
      &.workspace-zone {
        border-left-color: #ff9800;
        &.active {
          background-color: #fff3e0;
          border-color: #ff9800;
        }
      }
      .zone-header {
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: bold;
        color: #333333;
      }
      .zone-content {
        .empty-hint {
          padding: 20px;
          font-style: italic;
          color: #999999;
          text-align: center;
        }
        .dropped-items {
          .dropped-item {
            padding: 12px;
            margin-bottom: 8px;
            background-color: #ffffff;
            border: 1px solid #eeeeee;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
            .item-type {
              display: inline-block;
              padding: 2px 8px;
              margin-right: 8px;
              font-size: 12px;
              font-weight: bold;
              color: white;
              background-color: #007bff;
              border-radius: 12px;
            }
            .item-data {
              font-size: 12px;
              color: #666666;
            }
          }
        }
      }
    }
    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      justify-content: center;
      margin: 30px 0;
      .btn {
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background-color: #007bff;
        border: none;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
        transition: all 0.3s ease;
        &:hover {
          background-color: #0056b3;
          transform: translateY(-2px);
        }
        &.disabled {
          background-color: #dc3545;
        }
      }
    }
    .status-info {
      padding: 20px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      h3 {
        margin-top: 0;
        font-size: 18px;
        color: #495057;
      }
      p {
        margin: 5px 0;
        font-size: 14px;
        color: #666666;
      }
    }
  }
</style>
