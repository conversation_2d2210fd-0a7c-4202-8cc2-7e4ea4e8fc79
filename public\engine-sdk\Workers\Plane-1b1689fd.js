define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Matrix4-a50b021f"],(function(e,n,t,a,r){"use strict";function i(e,n){if(t.Check.typeOf.object("normal",e),!a.CesiumMath.equalsEpsilon(a.Cartesian3.magnitude(e),1,a.<PERSON>siumMath.EPSILON6))throw new t.Dev<PERSON>rror("normal must be normalized.");t.Check.typeOf.number("distance",n),this.normal=a.Cartesian3.clone(e),this.distance=n}i.computePositionClockWise=function(e){let n=new a.Cartesian3;e.forEach((e=>{a.Cartesian3.add(e,n,n)})),a.Car<PERSON>3.multiplyByScalar(n,1/e.length,n);let t=a.Cartesian3.UNIT_Y,r=new Map,i=[],o=new a.Cartesian3;e.forEach((e=>{a.Cartesian3.subtract(e,n,o),a.Cartesian3.normalize(o,o);let s=a.Cartesian3.dot(o,t);a.Cartesian3.dot(a.Cartesian3.cross(t,o,o),a.Cartesian3.UNIT_Z)<0&&(s=-s,s-=2),i.push(s),r.set(s,e)})),i.sort(((e,n)=>e-n));let s=[];i.forEach((e=>{s.push(r.get(e))}))},i.fromPointNormal=function(e,r,o){if(t.Check.typeOf.object("point",e),t.Check.typeOf.object("normal",r),!a.CesiumMath.equalsEpsilon(a.Cartesian3.magnitude(r),1,a.CesiumMath.EPSILON6))throw new t.DeveloperError("normal must be normalized.");var s=-a.Cartesian3.dot(r,e);return n.defined(o)?(a.Cartesian3.clone(r,o.normal),o.distance=s,o):new i(r,s)};var o=new a.Cartesian3;i.fromCartesian4=function(e,t){var r=a.Cartesian3.fromCartesian4(e,o),s=e.w;return n.defined(t)?(a.Cartesian3.clone(r,t.normal),t.distance=s,t):new i(r,s)},i.getIntersectWithLineSegment=function(e,n,t){let r=a.Cartesian3.dot(e.normal,n)-e.distance,i=a.Cartesian3.dot(e.normal,t)-e.distance;if(r*i<=0){a.Cartesian3.subtract(n,t,o),a.Cartesian3.normalize(o,o);let i=a.Cartesian3.dot(o,e.normal);return a.Cartesian3.multiplyByScalar(o,-r/i,o),a.Cartesian3.add(o,n,new a.Cartesian3)}},i.getPointDistance=function(e,n){return a.Cartesian3.dot(e.normal,n)+e.distance};var s=new a.Cartesian3;i.projectPointOntoPlane=function(e,t,r){n.defined(r)||(r=new a.Cartesian3);var o=i.getPointDistance(e,t),l=a.Cartesian3.multiplyByScalar(e.normal,o,s);return a.Cartesian3.subtract(t,l,r)},i.projectPointsOntoPlane=function(e,t,a){n.defined(a)||(a=[]);for(let n=0;n<t.length;n++)a[n]=i.projectPointOntoPlane(e,t[n],a[n]);return a};var l=new a.Cartesian3;i.transform=function(e,n,t){return r.Matrix4.multiplyByPointAsVector(n,e.normal,o),a.Cartesian3.normalize(o,o),a.Cartesian3.multiplyByScalar(e.normal,-e.distance,l),r.Matrix4.multiplyByPoint(n,l,l),i.fromPointNormal(l,o,t)},i.clone=function(e,t){return n.defined(t)?(a.Cartesian3.clone(e.normal,t.normal),t.distance=e.distance,t):new i(e.normal,e.distance)},i.equals=function(e,n){return e.distance===n.distance&&a.Cartesian3.equals(e.normal,n.normal)},i.ORIGIN_XY_PLANE=Object.freeze(new i(a.Cartesian3.UNIT_Z,0)),i.ORIGIN_YZ_PLANE=Object.freeze(new i(a.Cartesian3.UNIT_X,0)),i.ORIGIN_ZX_PLANE=Object.freeze(new i(a.Cartesian3.UNIT_Y,0)),i.prototype.projectPointsOntoPlane=function(e,n){return i.projectPointsOntoPlane(this,e,n)},i.fromPositions=function(e){if(e.length<3)throw new Error("\u8bf7\u8f93\u5165\u81f3\u5c11\u4e09\u4e2a\u70b9");let n=e[0],t=e[1],r=e[2],o=a.Cartesian3.subtract(n,t,new a.Cartesian3),s=a.Cartesian3.subtract(n,r,new a.Cartesian3),l=a.Cartesian3.cross(o,s,s);return i.fromPointNormal(a.Cartesian3.clone(n,o),l)},e.Plane=i}));