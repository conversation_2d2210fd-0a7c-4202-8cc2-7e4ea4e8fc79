import Check from"../Core/Check.js";import defined from"../Core/defined.js";var InspectorShared={createCheckbox:function(e,t,c){Check.typeOf.string("labelText",e),Check.typeOf.string("checkedBinding",t);var n=document.createElement("div"),i=document.createElement("label"),d=document.createElement("input");d.type="checkbox";var a="checked: "+t;return defined(c)&&(a+=", enable: "+c),d.setAttribute("data-bind",a),i.appendChild(d),i.appendChild(document.createTextNode(e)),n.appendChild(i),n},createSection:function(e,t,c,n){Check.defined("panel",e),Check.typeOf.string("headerText",t),Check.typeOf.string("sectionVisibleBinding",c),Check.typeOf.string("toggleSectionVisibilityBinding",n);var i=document.createElement("div");i.className="cesium-cesiumInspector-section",i.setAttribute("data-bind",'css: { "cesium-cesiumInspector-section-collapsed": !'+c+" }"),e.appendChild(i);var d=document.createElement("h3");d.className="cesium-cesiumInspector-sectionHeader",d.appendChild(document.createTextNode(t)),d.setAttribute("data-bind","click: "+n),i.appendChild(d);var a=document.createElement("div");return a.className="cesium-cesiumInspector-sectionContent",i.appendChild(a),a}};export default InspectorShared;