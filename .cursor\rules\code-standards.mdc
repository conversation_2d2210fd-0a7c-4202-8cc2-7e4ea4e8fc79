---
description: 
globs: 
alwaysApply: true
---
# 代码规范规则

## 总体原则

本项目使用ESLint、Prettier、StyleLint和UnoCSS进行代码规范管理。**当ESLint和Prettier规则冲突时，以ESLint规则为准。**

**代码简洁性原则**：优先使用现代JavaScript/TypeScript语法特性，如可选链操作符(`?.`)、空值合并操作符(`??`)等，使代码更简洁清晰。

## JavaScript/TypeScript 规范

### 基础语法规范

#### 变量声明
```javascript
// ✅ 正确：使用 let 或 const，禁用 var
const name = 'John'
let age = 25

// ❌ 错误：使用 var
var oldStyle = 'bad'
```

#### 引号使用
```javascript
// ✅ 正确：使用单引号，允许模板字符串
const message = 'Hello World'
const template = `Hello ${name}`

// ❌ 错误：使用双引号
const message = "Hello World"
```

#### 分号使用
```javascript
// ✅ 正确：不使用分号
const result = getValue()
console.log(result)

// ❌ 错误：使用分号
const result = getValue();
console.log(result);
```

#### 逗号规范
```javascript
// ✅ 正确：不使用尾随逗号
const obj = {
  name: 'John',
  age: 25
}

// ❌ 错误：使用尾随逗号
const obj = {
  name: 'John',
  age: 25,
}
```

### TypeScript 特定规范

#### 类型声明
```typescript
// ✅ 正确：允许使用 any 类型（项目配置）
const data: any = fetchData()

// ✅ 正确：允许使用 @ts-ignore（有说明时）
// @ts-ignore: 临时忽略类型检查
const result = unsafeOperation()

// ✅ 正确：允许显式类型声明
const count: number = 10
```

#### 命名空间
```typescript
// ✅ 正确：允许使用命名空间
namespace Utils {
  export function format(str: string): string {
    return str.trim()
  }
}
```

### 代码格式化

#### 缩进和空格
```javascript
// ✅ 正确：使用2空格缩进，箭头函数前后有空格
const transform = (data) => {
  return data.map(item => item.value)
}

// ✅ 正确：对象大括号内有空格
const config = { host: 'localhost', port: 3000 }

// ✅ 正确：数组方括号内无空格
const items = [1, 2, 3, 4]
```

#### 行长度和换行
```javascript
// ✅ 正确：单行长度不超过160字符
const veryLongVariableName = 'This is a very long string that demonstrates the line length limit of 160 characters which is quite generous for readability'

// ✅ 正确：三元运算符的换行格式
const result = condition
  ? 'true value'
  : 'false value'
```

### 导入排序规范

基于 `simple-import-sort` 插件配置，导入语句应按以下顺序排列：

```javascript
// 1. Vue核心库
import Vue from 'vue'
import { createRouter } from 'vue-router'
import { Button } from 'ant-design-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import { ref } from '@vueuse/core'

// 2. Vue组件
import MyComponent from './MyComponent.vue'
import AnotherComponent from '../AnotherComponent.vue'

// 3. 资源文件
import logo from '@/assets/logo.png'
import '@/assets/styles.css'

// 4. 其他第三方库
import axios from 'axios'
import lodash from 'lodash'

// 5. 类型导入
import type { User } from './types'
import type { Config } from '../config'
```

## Vue 规范

### 组件命名
```vue
<!-- ✅ 正确：允许单词组件名 -->
<template>
  <Card>
    <Button />
  </Card>
</template>
```

### 模板语法
```vue
<!-- ✅ 正确：属性可以在同一行 -->
<el-button type="primary" size="large" @click="handleClick">
  Click me
</el-button>

<!-- ✅ 正确：允许 v-html -->
<div v-html="htmlContent"></div>

<!-- ✅ 正确：不要求默认属性 -->
<MyComponent :optional-prop="value" />
```

### 脚本和样式缩进
```vue
<template>
  <div>Content</div>
</template>

<script setup lang="ts">
  // ✅ 正确：script标签内容有缩进
  import { ref } from 'vue'
  
  const count = ref(0)
</script>

<style scoped>
  /* ✅ 正确：style标签内容有缩进 */
  .container {
    padding: 20px;
  }
</style>
```

## CSS/SCSS 规范

### 基础规则
```css
/* ✅ 正确：URL使用引号 */
background-image: url('./image.png');

/* ✅ 正确：16进制颜色使用完整格式 */
color: #ffffff;
background: #000000;

/* ✅ 正确：规则之间不要空行 */
.class1 {
  color: red;
}
.class2 {
  color: blue;
}
```

### SCSS特定规则
```scss
// ✅ 正确：允许使用@import引入scss文件
@import 'variables';
@import 'mixins';

// ✅ 正确：允许vendor-prefix（多行省略等场景）
.ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// ✅ 正确：Vue中使用v-bind可以使用大写
.dynamic {
  color: v-bind(primaryColor);
}
```

### 选择器规范
```scss
// ✅ 正确：允许使用Vue深度选择器
.container {
  :global(.ant-btn) {
    margin: 10px;
  }
  
  :v-deep(.inner-class) {
    padding: 5px;
  }
  
  :deep(.nested-component) {
    border: 1px solid #ccc;
  }
}

// ✅ 正确：class命名无强制格式要求
.myCustomClass {}
.my-kebab-case {}
.my_snake_case {}
```

## UnoCSS 工具类规范

### 预设快捷类
基于 [uno.config.ts](mdc:uno.config.ts) 配置，优先使用预定义的快捷类：

```vue
<template>
  <!-- ✅ 正确：使用预设快捷类 -->
  <div class="flex-center">居中内容</div>
  <div class="flex-x-between">两端对齐</div>
  <div class="wh-full">全宽高</div>
  <div class="absolute-lt">左上角绝对定位</div>
  
  <!-- ✅ 正确：使用主题色变量 -->
  <div class="text-primary bg-primary_dark">主题色文本</div>
</template>
```

### UnoCSS命名规范
```vue
<template>
  <!-- ✅ 正确：基础工具类 -->
  <div class="p-4 m-2 text-16 bg-blue-500">
    基础样式
  </div>
  
  <!-- ✅ 正确：响应式设计 -->
  <div class="w-full md:w-1/2 lg:w-1/3">
    响应式宽度
  </div>
  
  <!-- ✅ 正确：状态变体 -->
  <button class="bg-blue-500 hover:bg-blue-600 active:bg-blue-700">
    交互按钮
  </button>
</template>
```

## 文件和目录规范

### 文件命名
```
// ✅ 正确的文件命名
src/
├── components/
│   ├── UserCard.vue          # PascalCase for components
│   └── common/
│       └── BaseButton.vue
├── views/
│   ├── userManage/           # camelCase for directories
│   │   ├── index.vue
│   │   └── helper/
│   │       └── index.tsx
├── utils/
│   ├── request.ts            # camelCase for utilities
│   └── format.ts
└── types/
    └── user.ts               # camelCase for types
```

### 导入路径
```javascript
// ✅ 正确：使用别名导入
import { http } from '@/utils/http'
import UserCard from '@/components/UserCard.vue'

// ✅ 正确：相对路径导入
import { useTableConfig } from './helper'
import type { UserInfo } from '../types'
```

## 代码质量规范

### 控制台输出
```javascript
// ✅ 正确：允许使用console（开发阶段）
console.log('Debug info:', data)
console.error('Error occurred:', error)

// ✅ 正确：允许使用debugger（开发阶段）
debugger // 用于调试
```

### 函数和变量
```javascript
// ✅ 正确：允许空函数
const noop = () => {}

// ✅ 正确：允许未使用的变量（TypeScript）
const handleClick = (event: Event, _unused: string) => {
  console.log(event)
}

// ✅ 正确：允许在定义前使用（函数提升）
sayHello()
function sayHello() {
  console.log('Hello')
}
```

### 条件判断
```javascript
// ✅ 正确：允许使用 == 和 != 
if (value == null) {
  // 处理 null 或 undefined
}

// ✅ 正确：也可以使用严格相等
if (value === null || value === undefined) {
  // 严格判断
}
```

### 可选链操作符和空值合并
```javascript
// ✅ 正确：优先使用可选链操作符，代码更简洁清晰
instance?.clearData?.()
user?.profile?.name
config?.api?.baseUrl

// ✅ 正确：可选链与函数调用
callback?.()
instance?.method?.(param1, param2)

// ✅ 正确：结合空值合并操作符使用
const name = user?.profile?.name ?? 'Unknown'
const timeout = config?.timeout ?? 5000
const message = response?.message ?? '操作失败'

// ✅ 正确：可选链与数组访问
const firstItem = items?.[0]
const dynamicProp = obj?.[propName]

// ❌ 错误：冗长的条件判断
if (instance && typeof instance.clearData === 'function') {
  instance.clearData()
}

// ❌ 错误：多层嵌套判断
if (user && user.profile && user.profile.name) {
  console.log(user.profile.name)
}

// ❌ 错误：使用 || 代替 ??（可能有副作用）
const value = someValue || defaultValue // 当 someValue 为 0、''、false 时也会使用默认值
const value = someValue ?? defaultValue // 只有当 someValue 为 null 或 undefined 时才使用默认值
```

## 项目特定规范

### ZnTable组件使用
参考 [zntable-usage规则](mdc:.cursor/rules/zntable-usage.mdc)，确保：
- 调用MCP服务获取最新文档
- 使用标准Mock数据格式
- 正确配置dataCallback

### API响应处理规范

项目API已经在请求拦截器中处理了响应数据，直接返回`response.data`，因此在业务代码中处理API响应时应遵循以下规范：

```javascript
// ✅ 正确：使用 response.success 判断请求是否成功
const response = await apiCall()
if (response.success) {
  // 请求成功
  console.log('操作成功')
  
  // 直接访问 response.result，不需要 .data
  if (response.result?.id) {
    formData.id = response.result.id
  }
} else {
  // 请求失败，使用 response.message 获取错误信息
  const errorMsg = response.message || '操作失败'
  ElMessage.error(errorMsg)
}

// ✅ 正确：处理列表数据
const listResponse = await apiCall()
if (listResponse.success) {
  const data = listResponse.result || []
  setTableData(data)
}

// ✅ 正确：处理布尔值返回（如重复检查）
const checkResponse = await nameCheckApi({ name: 'test' })
if (checkResponse.result === false) {
  ElMessage.error('名称已存在')
  return
}

// ❌ 错误：不要使用 response.data.xxx
if (response.data.status === 1) { // 错误
  // ...
}

// ❌ 错误：不要使用 response.data.result
const result = response.data.result // 错误

// ❌ 错误：不要使用 response.data.success
if (response.data.success) { // 错误
  // ...
}
```

#### 响应数据结构说明

项目API响应统一结构：
```typescript
interface ApiResponse<T = any> {
  success: boolean      // 请求是否成功
  result: T            // 响应数据
  message?: string     // 错误信息或提示信息
  status?: number      // 状态码（可选）
}
```

#### 常见场景处理

```javascript
// ✅ 保存操作
const saveData = async () => {
  const response = await saveApi(formData)
  if (response.success) {
    ElMessage.success('保存成功')
    // 更新ID（如果返回）
    if (response.result?.id) {
      formData.id = response.result.id
    }
  } else {
    ElMessage.error(response.message || '保存失败')
  }
}

// ✅ 数据查询
const fetchData = async () => {
  const response = await queryApi(params)
  if (response.success) {
    tableData.value = response.result || []
  } else {
    ElMessage.error(response.message || '查询失败')
    tableData.value = []
  }
}

// ✅ 删除操作
const deleteItem = async (id: number) => {
  const response = await deleteApi({ id })
  if (response.success) {
    ElMessage.success('删除成功')
    refreshTable()
  } else {
    ElMessage.error(response.message || '删除失败')
  }
}

// ✅ 名称重复检查
const checkName = async (name: string) => {
  const response = await checkNameApi({ name })
  // result为false表示名称重复
  if (response.result === false) {
    ElMessage.error('名称已存在，请修改后重试')
    return false
  }
  return true
}
```

### 错误处理
```javascript
// ✅ 正确：允许抛出字面量
throw 'Custom error message'
throw { code: 500, message: 'Server error' }

// ✅ 正确：标准错误处理
try {
  await apiCall()
} catch (error) {
  console.error('API调用失败:', error)
  throw new Error('操作失败')
}
```

## 配置文件说明

- [.eslintrc.cjs](mdc:.eslintrc.cjs) - ESLint配置，JavaScript/TypeScript/Vue代码检查
- [.prettierrc.cjs](mdc:.prettierrc.cjs) - Prettier配置，代码格式化
- [.stylelintrc.cjs](mdc:.stylelintrc.cjs) - StyleLint配置，CSS/SCSS样式检查
- [uno.config.ts](mdc:uno.config.ts) - UnoCSS配置，原子化CSS框架

## 优先级说明

当规则冲突时，优先级顺序：
1. **ESLint规则** - 最高优先级
2. StyleLint规则 - CSS/SCSS相关
3. Prettier规则 - 格式化相关
4. UnoCSS规则 - 工具类相关

请严格遵循此代码规范，确保项目代码的一致性和可维护性。

