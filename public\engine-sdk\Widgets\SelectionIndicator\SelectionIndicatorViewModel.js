import Cartesian2 from"../../Core/Cartesian2.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import DeveloperError from"../../Core/DeveloperError.js";import EasingFunction from"../../Core/EasingFunction.js";import SceneTransforms from"../../Scene/SceneTransforms.js";import knockout from"../../ThirdParty/knockout.js";var screenSpacePos=new Cartesian2,offScreen="-1000px";function SelectionIndicatorViewModel(e,t,i){if(!defined(e))throw new DeveloperError("scene is required.");if(!defined(t))throw new DeveloperError("selectionIndicatorElement is required.");if(!defined(i))throw new DeveloperError("container is required.");this._scene=e,this._screenPositionX=offScreen,this._screenPositionY=offScreen,this._tweens=e.tweens,this._container=defaultValue(i,document.body),this._selectionIndicatorElement=t,this._scale=1,this.position=void 0,this.showSelection=!1,knockout.track(this,["position","_screenPositionX","_screenPositionY","_scale","showSelection"]),this.isVisible=void 0,knockout.defineProperty(this,"isVisible",{get:function(){return this.showSelection&&defined(this.position)}}),knockout.defineProperty(this,"_transform",{get:function(){return"scale("+this._scale+")"}}),this.computeScreenSpacePosition=function(t,i){return SceneTransforms.wgs84ToWindowCoordinates(e,t,i)}}SelectionIndicatorViewModel.prototype.update=function(){if(this.showSelection&&defined(this.position)){var e=this.computeScreenSpacePosition(this.position,screenSpacePos);if(defined(e)){var t=this._container,i=t.parentNode.clientWidth,o=t.parentNode.clientHeight,n=this._selectionIndicatorElement.clientWidth,r=.5*n;e.x=Math.min(Math.max(e.x,-n),i+n)-r,e.y=Math.min(Math.max(e.y,-n),o+n)-r,this._screenPositionX=Math.floor(e.x+.25)+"px",this._screenPositionY=Math.floor(e.y+.25)+"px"}else this._screenPositionX=offScreen,this._screenPositionY=offScreen}},SelectionIndicatorViewModel.prototype.animateAppear=function(){this._tweens.addProperty({object:this,property:"_scale",startValue:2,stopValue:1,duration:.8,easingFunction:EasingFunction.EXPONENTIAL_OUT})},SelectionIndicatorViewModel.prototype.animateDepart=function(){this._tweens.addProperty({object:this,property:"_scale",startValue:this._scale,stopValue:1.5,duration:.8,easingFunction:EasingFunction.EXPONENTIAL_OUT})},Object.defineProperties(SelectionIndicatorViewModel.prototype,{container:{get:function(){return this._container}},selectionIndicatorElement:{get:function(){return this._selectionIndicatorElement}},scene:{get:function(){return this._scene}}});export default SelectionIndicatorViewModel;