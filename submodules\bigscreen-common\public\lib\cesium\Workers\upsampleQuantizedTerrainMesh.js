/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AttributeCompression-d661357e","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./TerrainEncoding-389ca311","./IndexDatatype-00859b8b","./Math-b5f4d889","./OrientedBoundingBox-e47c7a90","./createTaskProcessorWorker","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,n,i,s,r,h,o,u,d,p,l,a,f,c,g,m,x,w){"use strict";const C={clipTriangleAtAxisAlignedThreshold:function(e,t,n,i,r,h){let o,u,d;s.defined(h)?h.length=0:h=[],t?(o=n<e,u=i<e,d=r<e):(o=n>e,u=i>e,d=r>e);const p=o+u+d;let l,a,f,c,g,m;return 1===p?o?(l=(e-n)/(i-n),a=(e-n)/(r-n),h.push(1),h.push(2),1!==a&&(h.push(-1),h.push(0),h.push(2),h.push(a)),1!==l&&(h.push(-1),h.push(0),h.push(1),h.push(l))):u?(f=(e-i)/(r-i),c=(e-i)/(n-i),h.push(2),h.push(0),1!==c&&(h.push(-1),h.push(1),h.push(0),h.push(c)),1!==f&&(h.push(-1),h.push(1),h.push(2),h.push(f))):d&&(g=(e-r)/(n-r),m=(e-r)/(i-r),h.push(0),h.push(1),1!==m&&(h.push(-1),h.push(2),h.push(1),h.push(m)),1!==g&&(h.push(-1),h.push(2),h.push(0),h.push(g))):2===p?o||n===e?u||i===e?d||r===e||(a=(e-n)/(r-n),f=(e-i)/(r-i),h.push(2),h.push(-1),h.push(0),h.push(2),h.push(a),h.push(-1),h.push(1),h.push(2),h.push(f)):(m=(e-r)/(i-r),l=(e-n)/(i-n),h.push(1),h.push(-1),h.push(2),h.push(1),h.push(m),h.push(-1),h.push(0),h.push(1),h.push(l)):(c=(e-i)/(n-i),g=(e-r)/(n-r),h.push(0),h.push(-1),h.push(1),h.push(0),h.push(c),h.push(-1),h.push(2),h.push(0),h.push(g)):3!==p&&(h.push(0),h.push(1),h.push(2)),h},computeBarycentricCoordinates:function(e,t,i,r,h,o,u,d,p){const l=i-u,a=u-h,f=o-d,c=r-d,g=1/(f*l+a*c),m=t-d,x=e-u,w=(f*x+a*m)*g,C=(-c*x+l*m)*g,B=1-w-C;return s.defined(p)?(p.x=w,p.y=C,p.z=B,p):new n.Cartesian3(w,C,B)},computeLineSegmentLineSegmentIntersection:function(e,t,i,r,h,o,u,d,p){const l=(d-o)*(i-e)-(u-h)*(r-t);if(0===l)return;const a=((u-h)*(t-o)-(d-o)*(e-h))/l,f=((i-e)*(t-o)-(r-t)*(e-h))/l;return a>=0&&a<=1&&f>=0&&f<=1?(s.defined(p)||(p=new n.Cartesian2),p.x=e+a*(i-e),p.y=t+a*(r-t),p):void 0}};var B=C;const y=32767,b=16383,I=[],A=[],v=[],T=new i.Cartographic;let z=new n.Cartesian3;const M=[],N=[],V=[],R=[],E=[],H=new n.Cartesian3,O=new t.BoundingSphere,S=new u.OrientedBoundingBox,U=new n.Cartesian2,F=new n.Cartesian3;function P(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}P.prototype.clone=function(e){return s.defined(e)||(e=new P),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},P.prototype.initializeIndexed=function(e,t,n,i,s){this.uBuffer=e,this.vBuffer=t,this.heightBuffer=n,this.normalBuffer=i,this.index=s,this.first=void 0,this.second=void 0,this.ratio=void 0},P.prototype.initializeFromClipResult=function(e,t,n){let i=t+1;return-1!==e[t]?n[e[t]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=n[e[i]],++i,this.second=n[e[i]],++i,this.ratio=e[i],++i),i},P.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},P.prototype.isIndexed=function(){return s.defined(this.index)},P.prototype.getH=function(){return s.defined(this.index)?this.heightBuffer[this.index]:o.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},P.prototype.getU=function(){return s.defined(this.index)?this.uBuffer[this.index]:o.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},P.prototype.getV=function(){return s.defined(this.index)?this.vBuffer[this.index]:o.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};let D=new n.Cartesian2,W=-1;const X=[new n.Cartesian3,new n.Cartesian3],k=[new n.Cartesian3,new n.Cartesian3];function K(t,i){++W;let s=X[W],r=k[W];return s=e.AttributeCompression.octDecode(t.first.getNormalX(),t.first.getNormalY(),s),r=e.AttributeCompression.octDecode(t.second.getNormalX(),t.second.getNormalY(),r),z=n.Cartesian3.lerp(s,r,t.ratio,z),n.Cartesian3.normalize(z,z),e.AttributeCompression.octEncode(z,i),--W,i}P.prototype.getNormalX=function(){return s.defined(this.index)?this.normalBuffer[2*this.index]:(D=K(this,D),D.x)},P.prototype.getNormalY=function(){return s.defined(this.index)?this.normalBuffer[2*this.index+1]:(D=K(this,D),D.y)};const L=[];function Y(e,t,n,i,r,h,o,u,d){if(0===o.length)return;let p=0,l=0;for(;l<o.length;)l=L[p++].initializeFromClipResult(o,l,u);for(let r=0;r<p;++r){const o=L[r];if(o.isIndexed())o.newIndex=h[o.index],o.uBuffer=e,o.vBuffer=t,o.heightBuffer=n,d&&(o.normalBuffer=i);else{const r=o.getKey();if(s.defined(h[r]))o.newIndex=h[r];else{const s=e.length;e.push(o.getU()),t.push(o.getV()),n.push(o.getH()),d&&(i.push(o.getNormalX()),i.push(o.getNormalY())),o.newIndex=s,h[r]=s}}}3===p?(r.push(L[0].newIndex),r.push(L[1].newIndex),r.push(L[2].newIndex)):4===p&&(r.push(L[0].newIndex),r.push(L[1].newIndex),r.push(L[2].newIndex),r.push(L[0].newIndex),r.push(L[2].newIndex),r.push(L[3].newIndex))}return L.push(new P),L.push(new P),L.push(new P),L.push(new P),d((function(e,s){const d=e.isEastChild,p=e.isNorthChild,l=d?b:0,a=d?y:b,f=p?b:0,c=p?y:b,g=M,m=N,x=V,w=E;g.length=0,m.length=0,x.length=0,w.length=0;const C=R;C.length=0;const D={},W=e.vertices;let X=e.indices;X=X.subarray(0,e.indexCountWithoutSkirts);const k=r.TerrainEncoding.clone(e.encoding),K=k.hasVertexNormals;let L=0;const _=e.vertexCountWithoutSkirts,G=e.minimumHeight,J=e.maximumHeight,Z=new Array(_),j=new Array(_),q=new Array(_),Q=K?new Array(2*_):void 0;let $,ee,te,ne,ie;for(ee=0,te=0;ee<_;++ee,te+=2){const e=k.decodeTextureCoordinates(W,ee,U);if($=k.decodeHeight(W,ee),ne=o.CesiumMath.clamp(e.x*y|0,0,y),ie=o.CesiumMath.clamp(e.y*y|0,0,y),q[ee]=o.CesiumMath.clamp(($-G)/(J-G)*y|0,0,y),ne<20&&(ne=0),ie<20&&(ie=0),y-ne<20&&(ne=y),y-ie<20&&(ie=y),Z[ee]=ne,j[ee]=ie,K){const e=k.getOctEncodedNormal(W,ee,F);Q[te]=e.x,Q[te+1]=e.y}(d&&ne>=b||!d&&ne<=b)&&(p&&ie>=b||!p&&ie<=b)&&(D[ee]=L,g.push(ne),m.push(ie),x.push(q[ee]),K&&(w.push(Q[te]),w.push(Q[te+1])),++L)}const se=[];se.push(new P),se.push(new P),se.push(new P);const re=[];let he,oe;for(re.push(new P),re.push(new P),re.push(new P),ee=0;ee<X.length;ee+=3){const e=X[ee],t=X[ee+1],n=X[ee+2],i=Z[e],s=Z[t],r=Z[n];se[0].initializeIndexed(Z,j,q,Q,e),se[1].initializeIndexed(Z,j,q,Q,t),se[2].initializeIndexed(Z,j,q,Q,n);const h=B.clipTriangleAtAxisAlignedThreshold(b,d,i,s,r,I);he=0,he>=h.length||(he=re[0].initializeFromClipResult(h,he,se),he>=h.length||(he=re[1].initializeFromClipResult(h,he,se),he>=h.length||(he=re[2].initializeFromClipResult(h,he,se),oe=B.clipTriangleAtAxisAlignedThreshold(b,p,re[0].getV(),re[1].getV(),re[2].getV(),A),Y(g,m,x,w,C,D,oe,re,K),he<h.length&&(re[2].clone(re[1]),re[2].initializeFromClipResult(h,he,se),oe=B.clipTriangleAtAxisAlignedThreshold(b,p,re[0].getV(),re[1].getV(),re[2].getV(),A),Y(g,m,x,w,C,D,oe,re,K)))))}const ue=d?-32767:0,de=p?-32767:0,pe=[],le=[],ae=[],fe=[];let ce=Number.MAX_VALUE,ge=-ce;const me=v;me.length=0;const xe=i.Ellipsoid.clone(e.ellipsoid),we=i.Rectangle.clone(e.childRectangle),Ce=we.north,Be=we.south;let ye=we.east;const be=we.west;for(ye<be&&(ye+=o.CesiumMath.TWO_PI),ee=0;ee<g.length;++ee)ne=Math.round(g[ee]),ne<=l?(pe.push(ee),ne=0):ne>=a?(ae.push(ee),ne=y):ne=2*ne+ue,g[ee]=ne,ie=Math.round(m[ee]),ie<=f?(le.push(ee),ie=0):ie>=c?(fe.push(ee),ie=y):ie=2*ie+de,m[ee]=ie,$=o.CesiumMath.lerp(G,J,x[ee]/y),$<ce&&(ce=$),$>ge&&(ge=$),x[ee]=$,T.longitude=o.CesiumMath.lerp(be,ye,ne/y),T.latitude=o.CesiumMath.lerp(Be,Ce,ie/y),T.height=$,xe.cartographicToCartesian(T,z),me.push(z.x),me.push(z.y),me.push(z.z);const Ie=t.BoundingSphere.fromVertices(me,n.Cartesian3.ZERO,3,O),Ae=u.OrientedBoundingBox.fromRectangle(we,ce,ge,xe,S),ve=new r.EllipsoidalOccluder(xe).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(Ie.center,me,3,Ie.center,ce,H),Te=ge-ce,ze=new Uint16Array(g.length+m.length+x.length);for(ee=0;ee<g.length;++ee)ze[ee]=g[ee];let Me=g.length;for(ee=0;ee<m.length;++ee)ze[Me+ee]=m[ee];for(Me+=m.length,ee=0;ee<x.length;++ee)ze[Me+ee]=y*(x[ee]-ce)/Te;const Ne=h.IndexDatatype.createTypedArray(g.length,C);let Ve;if(K){const e=new Uint8Array(w);s.push(ze.buffer,Ne.buffer,e.buffer),Ve=e.buffer}else s.push(ze.buffer,Ne.buffer);return{vertices:ze.buffer,encodedNormals:Ve,indices:Ne.buffer,minimumHeight:ce,maximumHeight:ge,westIndices:pe,southIndices:le,eastIndices:ae,northIndices:fe,boundingSphere:Ie,orientedBoundingBox:Ae,horizonOcclusionPoint:ve}}))}));
