/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./FrustumGeometry-44cfafc1","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./Rectangle-9bffefe4","./Math-b5f4d889","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./Plane-a268aa11","./VertexFormat-86c096b8"],(function(e,t,r,n,a,i,u,o,s,c,p,m,d,f,h){"use strict";const g=0,l=1;function _(n){const i=n.frustum,u=n.orientation,o=n.origin,s=e.defaultValue(n._drawNearPlane,!0);let c,p;i instanceof a.PerspectiveFrustum?(c=g,p=a.PerspectiveFrustum.packedLength):i instanceof a.OrthographicFrustum&&(c=l,p=a.OrthographicFrustum.packedLength),this._frustumType=c,this._frustum=i.clone(),this._origin=r.Cartesian3.clone(o),this._orientation=t.Quaternion.clone(u),this._drawNearPlane=s,this._workerName="createFrustumOutlineGeometry",this.packedLength=2+p+r.Cartesian3.packedLength+t.Quaternion.packedLength}_.pack=function(n,i,u){u=e.defaultValue(u,0);const o=n._frustumType,s=n._frustum;return i[u++]=o,o===g?(a.PerspectiveFrustum.pack(s,i,u),u+=a.PerspectiveFrustum.packedLength):(a.OrthographicFrustum.pack(s,i,u),u+=a.OrthographicFrustum.packedLength),r.Cartesian3.pack(n._origin,i,u),u+=r.Cartesian3.packedLength,t.Quaternion.pack(n._orientation,i,u),i[u+=t.Quaternion.packedLength]=n._drawNearPlane?1:0,i};const k=new a.PerspectiveFrustum,y=new a.OrthographicFrustum,b=new t.Quaternion,F=new r.Cartesian3;return _.unpack=function(n,i,u){i=e.defaultValue(i,0);const o=n[i++];let s;o===g?(s=a.PerspectiveFrustum.unpack(n,i,k),i+=a.PerspectiveFrustum.packedLength):(s=a.OrthographicFrustum.unpack(n,i,y),i+=a.OrthographicFrustum.packedLength);const c=r.Cartesian3.unpack(n,i,F);i+=r.Cartesian3.packedLength;const p=t.Quaternion.unpack(n,i,b),m=1===n[i+=t.Quaternion.packedLength];if(!e.defined(u))return new _({frustum:s,origin:c,orientation:p,_drawNearPlane:m});const d=o===u._frustumType?u._frustum:void 0;return u._frustum=s.clone(d),u._frustumType=o,u._origin=r.Cartesian3.clone(c,u._origin),u._orientation=t.Quaternion.clone(p,u._orientation),u._drawNearPlane=m,u},_.createGeometry=function(e){const r=e._frustumType,o=e._frustum,s=e._origin,c=e._orientation,p=e._drawNearPlane,m=new Float64Array(24);a.FrustumGeometry._computeNearFarPlanes(s,c,r,o,m);const d=new u.GeometryAttributes({position:new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m})});let f,h;const g=p?2:1,l=new Uint16Array(8*(g+1));let _=p?0:1;for(;_<2;++_)f=p?8*_:0,h=4*_,l[f]=h,l[f+1]=h+1,l[f+2]=h+1,l[f+3]=h+2,l[f+4]=h+2,l[f+5]=h+3,l[f+6]=h+3,l[f+7]=h;for(_=0;_<2;++_)f=8*(g+_),h=4*_,l[f]=h,l[f+1]=h+4,l[f+2]=h+1,l[f+3]=h+5,l[f+4]=h+2,l[f+5]=h+6,l[f+6]=h+3,l[f+7]=h+7;return new i.Geometry({attributes:d,indices:l,primitiveType:i.PrimitiveType.LINES,boundingSphere:t.BoundingSphere.fromVertices(m)})},function(t,r){return e.defined(r)&&(t=_.unpack(t,r)),_.createGeometry(t)}}));
