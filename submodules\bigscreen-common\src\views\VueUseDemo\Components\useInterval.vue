<template>
  <div>
    <div class="ml-100 h-200 w-300 bg-blue">
      useInterval:
      {{ count }}
      <el-button @click="pause">暂停</el-button>
      <el-button @click="resume">恢复</el-button>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'

  import { useIntervalFn } from '@vueuse/core'
  const count = ref(0)
  const { pause, resume } = useIntervalFn(
    () => {
      count.value++
      console.log(count.value)
    },
    1000,
    { immediateCallback: true }
  )

  // let flag = ''
  // setInterval(() => {
  //   flag += 'a'
  //   console.log(flag)
  // }, 1000)
</script>

<style lang="scss" scoped></style>
