<!--
 * @Description: 大屏左侧组件
 * @Autor: qian
 * @Date: 2023-06-19 17:04:41
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-22 11:08:56
-->
<template>
  <div class="ScreenLeft" :class="[activePage]">
    <component :is="activePage"></component>
  </div>
</template>

<script>
  import HomePage from './Components/HomePage/index.vue'
  export default {
    name: 'ScreenLeft',
    components: { HomePage }
  }
</script>
<script setup>
  import { computed, getCurrentInstance } from 'vue'

  import useStore from '@/store'
  import { setComponents } from '@/utils'
  const { screen } = useStore()
  const modulesFiles = import.meta.glob('./components/*/index.vue', { eager: true })
  setComponents(modulesFiles, getCurrentInstance())

  const activePage = computed(() => {
    if (screen.activePage === 'Monitor') {
      return ''
    }
    return screen.activePage
  })

  // const width = computed(() => {
  //   if (activePage.value === 'Emergency') {
  //     return '556px'
  //   }
  //   return '628px'
  // })
</script>
<style lang="scss" scoped>
  .ScreenLeft {
    z-index: 10;
    width: $screen-left-width;
    height: $screen-left-height;
    padding: 31px 41px 22px 21px;

    // margin-top: 23px;

    // margin-left: -$screen-left-width;
    font-size: 50px;
    pointer-events: auto;

    // background: linear-gradient(270deg, rgb(0 53 116 / 10%) 0%, rgb(0 53 116 / 80%) 100%);
    background: url('../../../assets/ScreenLeft/bg.png');

    //&.HomePage {
    //  width: 750px;
    //  margin-left: -775px;
    //}
  }
</style>
