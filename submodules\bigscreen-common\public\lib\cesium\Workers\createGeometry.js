/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./PrimitivePipeline-5c787aa8","./createTaskProcessorWorker","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Math-b5f4d889","./Rectangle-9bffefe4","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryPipeline-9dbd2054","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11","./WebMercatorProjection-ce967e48"],(function(e,t,r,n,o,i,s,a,c,f,d,b,u,m,l,p,y,P,k,C,G){"use strict";const W={};function R(t){let r=W[t];return e.defined(r)||("object"==typeof exports?W[r]=r=require(`Workers/${t}`):require([`Workers/${t}`],(function(e){r=e,W[r]=e}))),r}return r((function(r,n){const o=r.subTasks,i=o.length,s=new Array(i);for(let t=0;t<i;t++){const r=o[t],n=r.geometry,i=r.moduleName;if(e.defined(i)){const e=R(i);s[t]=e(n,r.offset)}else s[t]=n}return Promise.all(s).then((function(e){return t.PrimitivePipeline.packCreateGeometryResults(e,n)}))}))}));
