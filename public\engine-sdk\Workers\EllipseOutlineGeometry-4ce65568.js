define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./EllipseGeometryLibrary-98898859"],(function(e,t,i,r,a,n,o,s,l,u,d,f,p){"use strict";var c=new r.Cartesian3,m=new r.Cartesian3;function h(e){var t=e.center;m=r.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,m),e.height,m),m=r.Cartesian3.add(t,m,m);for(var i=new n.BoundingSphere(m,e.semiMajorAxis),a=p.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,l=new u.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p.EllipseGeometryLibrary.raisePositionsToHeight(a,e,!1)})}),f=a.length/3,c=d.IndexDatatype.createTypedArray(f,2*f),h=0,y=0;y<f;++y)c[h++]=y,c[h++]=(y+1)%f;return{boundingSphere:i,attributes:l,indices:c}}var y=new n.BoundingSphere,b=new n.BoundingSphere;function A(e){var i=e.center,a=e.ellipsoid,l=e.semiMajorAxis,m=r.Cartesian3.multiplyByScalar(a.geodeticSurfaceNormal(i,c),e.height,c);y.center=r.Cartesian3.add(i,m,y.center),y.radius=l,m=r.Cartesian3.multiplyByScalar(a.geodeticSurfaceNormal(i,m),e.extrudedHeight,m),b.center=r.Cartesian3.add(i,m,b.center),b.radius=l;var h=p.EllipseGeometryLibrary.computeEllipsePositions(e,!1,!0).outerPositions,A=new u.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p.EllipseGeometryLibrary.raisePositionsToHeight(h,e,!0)})});h=A.position.values;var _=n.BoundingSphere.union(y,b),g=h.length/3;if(t.defined(e.offsetAttribute)){var v=new Uint8Array(g);if(e.offsetAttribute===f.GeometryOffsetAttribute.TOP)v=f.arrayFill(v,1,0,g/2);else{var E=e.offsetAttribute===f.GeometryOffsetAttribute.NONE?0:1;v=f.arrayFill(v,E)}A.applyOffset=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:v})}var x=t.defaultValue(e.numberOfVerticalLines,16);x=r.CesiumMath.clamp(x,0,g/2);var w=d.IndexDatatype.createTypedArray(g,2*g+2*x);g/=2;var M,C,D=0;for(M=0;M<g;++M)w[D++]=M,w[D++]=(M+1)%g,w[D++]=M+g,w[D++]=(M+1)%g+g;if(x>0){var G=Math.min(x,g);C=Math.round(g/G);var L=Math.min(C*x,g);for(M=0;M<L;M+=C)w[D++]=M,w[D++]=M+g}return{boundingSphere:_,attributes:A,indices:w}}function _(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var n=e.center,o=t.defaultValue(e.ellipsoid,a.Ellipsoid.WGS84),s=e.semiMajorAxis,l=e.semiMinorAxis,u=t.defaultValue(e.granularity,r.CesiumMath.RADIANS_PER_DEGREE);if(!t.defined(n))throw new i.DeveloperError("center is required.");if(!t.defined(s))throw new i.DeveloperError("semiMajorAxis is required.");if(!t.defined(l))throw new i.DeveloperError("semiMinorAxis is required.");if(s<l)throw new i.DeveloperError("semiMajorAxis must be greater than or equal to the semiMinorAxis.");if(u<=0)throw new i.DeveloperError("granularity must be greater than zero.");var d=t.defaultValue(e.height,0),f=t.defaultValue(e.extrudedHeight,d);this._center=r.Cartesian3.clone(n),this._semiMajorAxis=s,this._semiMinorAxis=l,this._ellipsoid=a.Ellipsoid.clone(o),this._rotation=t.defaultValue(e.rotation,0),this._height=Math.max(f,d),this._granularity=u,this._extrudedHeight=Math.min(f,d),this._numberOfVerticalLines=Math.max(t.defaultValue(e.numberOfVerticalLines,16),0),this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipseOutlineGeometry"}_.packedLength=r.Cartesian3.packedLength+a.Ellipsoid.packedLength+8,_.pack=function(e,n,o){if(!t.defined(e))throw new i.DeveloperError("value is required");if(!t.defined(n))throw new i.DeveloperError("array is required");return o=t.defaultValue(o,0),r.Cartesian3.pack(e._center,n,o),o+=r.Cartesian3.packedLength,a.Ellipsoid.pack(e._ellipsoid,n,o),o+=a.Ellipsoid.packedLength,n[o++]=e._semiMajorAxis,n[o++]=e._semiMinorAxis,n[o++]=e._rotation,n[o++]=e._height,n[o++]=e._granularity,n[o++]=e._extrudedHeight,n[o++]=e._numberOfVerticalLines,n[o]=t.defaultValue(e._offsetAttribute,-1),n};var g=new r.Cartesian3,v=new a.Ellipsoid,E={center:g,ellipsoid:v,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,numberOfVerticalLines:void 0,offsetAttribute:void 0};_.unpack=function(e,n,o){if(!t.defined(e))throw new i.DeveloperError("array is required");n=t.defaultValue(n,0);var s=r.Cartesian3.unpack(e,n,g);n+=r.Cartesian3.packedLength;var l=a.Ellipsoid.unpack(e,n,v);n+=a.Ellipsoid.packedLength;var u=e[n++],d=e[n++],f=e[n++],p=e[n++],c=e[n++],m=e[n++],h=e[n++],y=e[n];return t.defined(o)?(o._center=r.Cartesian3.clone(s,o._center),o._ellipsoid=a.Ellipsoid.clone(l,o._ellipsoid),o._semiMajorAxis=u,o._semiMinorAxis=d,o._rotation=f,o._height=p,o._granularity=c,o._extrudedHeight=m,o._numberOfVerticalLines=h,o._offsetAttribute=-1===y?void 0:y,o):(E.height=p,E.extrudedHeight=m,E.granularity=c,E.rotation=f,E.semiMajorAxis=u,E.semiMinorAxis=d,E.numberOfVerticalLines=h,E.offsetAttribute=-1===y?void 0:y,new _(E))},_.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var i=e._height,a=e._extrudedHeight,n=!r.CesiumMath.equalsEpsilon(i,a,0,r.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var u,d={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:i,granularity:e._granularity,numberOfVerticalLines:e._numberOfVerticalLines};if(n)d.extrudedHeight=a,d.offsetAttribute=e._offsetAttribute,u=A(d);else if(u=h(d),t.defined(e._offsetAttribute)){var p=u.attributes.position.values.length,c=new Uint8Array(p/3),m=e._offsetAttribute===f.GeometryOffsetAttribute.NONE?0:1;f.arrayFill(c,m),u.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:c})}return new s.Geometry({attributes:u.attributes,indices:u.indices,primitiveType:l.PrimitiveType.LINES,boundingSphere:u.boundingSphere,offsetAttribute:e._offsetAttribute})}},e.EllipseOutlineGeometry=_}));