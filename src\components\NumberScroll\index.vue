<template>
  <div class="number-scroll">
    <span 
      v-for="(digit, index) in displayDigits" 
      :key="`${digitsKey}-${index}`"
      class="number-item"
    >
      <span class="number-wrapper">
        <i 
          class="number-roll"
          :style="{ 
            transform: `translate(-50%, -${digit * 10}%)`,
            transition: `transform ${animationDuration}ms ease-in-out`
          }"
        >
          0123456789
        </i>
      </span>
    </span>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick, computed } from 'vue'

const props = defineProps({
  start: {
    type: Number,
    default: 0
  },
  end: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 1200
  },
  autoPlay: {
    type: Boolean,
    default: true
  },
  decimals: {
    type: Number,
    default: 0
  },
  useEasing: {
    type: Boolean,
    default: true
  },
  refreshTrigger: {
    default: 0
  },
  dataSource: {
    type: String,
    default: 'default'
  }
})

const displayDigits = ref([])
const previousNumber = ref(props.start)
const digitsKey = ref(Date.now())
const isAnimating = ref(false)

// 计算动画时长
const animationDuration = computed(() => {
  return props.useEasing ? props.duration : Math.max(props.duration * 0.6, 300)
})

// 将数字转换为数字数组
const numberToDigits = (num) => {
  return Math.abs(Math.floor(num)).toString().split('').map(Number)
}

// 执行带动画的数字切换
const animateToNumber = (fromNum, toNum) => {
  if (isAnimating.value) return
  
  const fromDigits = numberToDigits(fromNum)
  const toDigits = numberToDigits(toNum)
  
  isAnimating.value = true
  
  // 判断位数变化情况
  if (fromDigits.length === toDigits.length) {
    // 位数相同，直接滚动
    displayDigits.value = toDigits
    previousNumber.value = toNum
    
    setTimeout(() => {
      isAnimating.value = false
    }, animationDuration.value + 50)
    
  } else if (fromDigits.length < toDigits.length) {
    // 位数增加：少位 → 多位
    const maxLength = toDigits.length
    const paddedFromDigits = [...fromDigits]
    
    // 在前面补0到目标位数
    while (paddedFromDigits.length < maxLength) {
      paddedFromDigits.unshift(0)
    }
    
    // 先设置补0后的起始位置
    displayDigits.value = paddedFromDigits
    
    nextTick(() => {
      // 滚动到目标数字
      displayDigits.value = toDigits
      previousNumber.value = toNum
      
      setTimeout(() => {
        isAnimating.value = false
      }, animationDuration.value + 50)
    })
    
  } else {
    // 位数减少：多位 → 少位（优化重点）
    const targetLength = toDigits.length
    
    // 策略：先瞬间切换到目标位数，然后执行滚动动画
    // 从原数字的后几位开始滚动到目标数字
    const fromLastDigits = fromDigits.slice(-targetLength)
    
    // 立即切换到目标位数结构，但显示起始数字的后几位
    digitsKey.value = Date.now()
    displayDigits.value = fromLastDigits
    
    nextTick(() => {
      // 滚动到目标数字
      displayDigits.value = toDigits
      previousNumber.value = toNum
      
      setTimeout(() => {
        isAnimating.value = false
      }, animationDuration.value + 50)
    })
  }
}

// 直接设置数字（无动画）
const setNumber = (num) => {
  const digits = numberToDigits(num)
  displayDigits.value = digits
  previousNumber.value = num
  isAnimating.value = false
}

// 监听end值变化
watch(() => props.end, (newVal) => {
  if (props.autoPlay && newVal !== previousNumber.value) {
    animateToNumber(previousNumber.value, newVal)
  } else if (!props.autoPlay) {
    setNumber(newVal)
  }
}, { immediate: true })

// 监听start值变化
watch(() => props.start, (newVal) => {
  if (!props.autoPlay) {
    setNumber(newVal)
  }
})

// 监听刷新触发器（强制动画）
watch(() => props.refreshTrigger, () => {
  const targetNumber = props.end || props.start
  if (targetNumber !== previousNumber.value) {
    animateToNumber(previousNumber.value, targetNumber)
  }
})

// 监听数据源变化（强制动画）
watch(() => props.dataSource, () => {
  const targetNumber = props.end || props.start
  if (targetNumber !== previousNumber.value) {
    animateToNumber(previousNumber.value, targetNumber)
  }
})

// 组件初始化
onMounted(() => {
  const initialNumber = props.end || props.start
  setNumber(initialNumber)
})

// 暴露方法给父组件
defineExpose({
  forceRefresh: () => {
    const targetNumber = props.end || props.start
    animateToNumber(previousNumber.value, targetNumber)
  }
})
</script>

<style lang="scss" scoped>
.number-scroll {
  display: inline-flex;
  align-items: center;
  font-size: inherit;
  color: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  
  .number-item {
    display: inline-block;
    width: 0.8em;
    height: 0.9em;
    overflow: hidden;
    position: relative;
    
    .number-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      writing-mode: vertical-lr;
      text-orientation: upright;
      overflow: hidden;
      
      .number-roll {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, 0);
        letter-spacing: 0;
        font-style: normal;
        line-height: 0.8;
        font-size: inherit;
        color: inherit;
        font-family: inherit;
        font-weight: inherit;
      }
    }
  }
}
</style> 