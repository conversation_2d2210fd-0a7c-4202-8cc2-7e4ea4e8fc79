import { viewpoints } from './data/viewpoints'
// import { emitUIInteraction } from '@/utils/webRtcVideo.js'

/**
 * 每隔1秒执行
 * @param {*} func
 * @param  {...any} args
 * @returns
 */
function executeWithDelay(func, ...args) {
  const queue = []
  let isProcessing = false

  function processQueue() {
    if (isProcessing || queue.length === 0) {
      return
    }

    isProcessing = true
    const task = queue.shift()
    task.func
      .apply(null, task.args)
      .then(() => {
        isProcessing = false
        setTimeout(processQueue, 100)
      })
      .catch((err) => {
        isProcessing = false
        console.error('Error executing function:', err)
      })
  }

  return function (...args) {
    queue.push({ func, args })
    processQueue()
  }
}

/**
 * @description: 转发给UE引擎统一入口
 * @param {*} keyword
 * @param {*} object
 * @return {*}
 */
export const toUe5 = executeWithDelay((keyword, object) => {
  const obj = {}
  obj[keyword] = JSON.stringify(object)
  console.log(new Date(), '发送给UE5', keyword, object, obj)

  // emitUIInteraction(obj)
  window['$stream']?.emitUIInteraction(obj)

  return new Promise((resolve) => resolve())
})
export const toUe5Viewpoint = (name) => {
  const find = viewpoints.find((it) => it.name === name)
  if (!find) {
    console.error(`未找到名称为${name}的视口！`)
    return
  }
  toUe5('viewPoint', { camera: find.camera, id: find.id })
}
