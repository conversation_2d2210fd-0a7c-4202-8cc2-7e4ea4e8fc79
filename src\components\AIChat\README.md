# AIChat 组件 - 引用文件列表优化

## 功能概述

本次优化主要针对AIChat组件中的引用文件列表进行了以下改进：

### 1. 文件合并功能
- **相同文件名合并**：将具有相同文件名的引用文件进行合并显示
- **计数显示**：合并后的文件会显示引用次数，格式为 `(#数量)`
- **智能排序**：按相关度（score）对原始引用进行排序

### 2. 鼠标悬停详情弹窗
- **触发条件**：所有引用文件都显示弹窗，包括单个引用
- **详细信息**：显示每个引用片段的序号、相关度和内容预览
- **交互功能**：点击弹窗中的任意片段可直接打开对应文件
- **正确实现**：参考[Element Plus Popover文档](https://element-plus.org/zh-CN/component/popover.html)正确配置

### 3. 数据结构优化
```javascript
// 合并后的数据结构
{
  id: "文件ID",
  fileName: "文件名",
  url: "文件链接",
  count: 引用次数,
  showPopover: 弹窗显示状态,
  originalQuotes: [原始引用数组]
}
```

## 使用方法

### 基本使用
```vue
<template>
  <div class="quote-list" v-if="item?.quoteList?.length > 0">
    <div 
      class="quote-item" 
      v-for="(mergedQuote, index) in getMergedQuoteList(item.quoteList)" 
      :key="index">
      <!-- 弹窗组件 -->
      <el-popover
        :visible="mergedQuote.showPopover"
        placement="top"
        :width="400"
        trigger="manual"
        popper-class="quote-detail-popover">
        <template #reference>
          <div 
            class="quote-item-wrapper"
            @mouseenter="handleQuoteMouseEnter(mergedQuote, $event)"
            @mouseleave="handleQuoteMouseLeave(mergedQuote)">
            <!-- 完整的引用项内容 -->
          </div>
        </template>
        <!-- 弹窗内容 -->
      </el-popover>
    </div>
  </div>
</template>
```

### 核心函数

#### getMergedQuoteList(quoteList)
合并引用文件列表的主要函数：
- 按文件名分组
- 计算引用次数
- 保存原始引用数据
- 按相关度排序

#### handleQuoteMouseEnter(mergedQuote, event)
处理鼠标进入事件：
- 所有引用都显示弹窗
- 立即显示弹窗

#### handleQuoteMouseLeave(mergedQuote)
处理鼠标离开事件：
- 所有引用都隐藏弹窗
- 立即隐藏弹窗

## 样式特性

### 引用项样式
- 渐变背景和毛玻璃效果
- 悬停时的动画效果
- 引用计数标签

### 弹窗样式
- 毛玻璃背景效果
- 圆角设计和阴影
- 响应式布局
- 滚动条美化

## 修复说明

### 问题分析
原始实现中 `el-popover` 的配置存在问题：
1. `trigger="manual"` 需要配合 `:visible` 属性使用
2. 弹窗的触发元素结构不正确
3. 鼠标事件绑定位置错误
4. 只有多个引用才显示弹窗的限制不合理

### 修复方案
1. **正确的弹窗配置**：
   ```vue
   <el-popover
     :visible="mergedQuote.showPopover"
     trigger="manual"
     placement="top"
     :width="400">
   ```

2. **完整的触发元素**：
   ```vue
   <template #reference>
     <div class="quote-item-wrapper"
       @mouseenter="handleQuoteMouseEnter(mergedQuote, $event)"
       @mouseleave="handleQuoteMouseLeave(mergedQuote)">
       <!-- 包含复选框、图标、文件名的完整引用项 -->
     </div>
   </template>
   ```

3. **所有引用都显示弹窗**：
   ```javascript
   const handleQuoteMouseEnter = (mergedQuote: any) => {
     // 所有引用都显示弹窗，不再限制只有多个引用才显示
     mergedQuote.showPopover = true
   }
   ```

4. **新增包装器样式**：
   ```css
   .quote-item-wrapper {
     display: flex;
     gap: 12px;
     align-items: flex-start;
     width: 100%;
   }
   ```

## 数据格式示例

### 输入数据格式
```javascript
[
  {
    "position": 1,
    "document_id": "e1210be9-ed52-48ce-a3de-eb2d23db390c",
    "document_name": "边坡喷播绿化工程技术标准CJJT292-2018.pdf.md",
    "fileName": "边坡喷播绿化工程技术标准CJJT292-2018.pdf",
    "score": 0.24256428,
    "content": "前言\n根据住房和城乡建设部...",
    "url": "http://***********:10000/knowledge/35/边坡喷播绿化工程技术标准CJJT292-2018.pdf"
  },
  // ... 更多引用
]
```

### 输出数据格式
```javascript
[
  {
    "id": "e1210be9-ed52-48ce-a3de-eb2d23db390c",
    "fileName": "边坡喷播绿化工程技术标准CJJT292-2018.pdf",
    "url": "http://***********:10000/knowledge/35/边坡喷播绿化工程技术标准CJJT292-2018.pdf",
    "count": 6,
    "showPopover": false,
    "originalQuotes": [
      // 原始引用数组，按score降序排列
    ]
  }
]
```

## 兼容性说明

- 支持Vue 3 + TypeScript
- 使用Element Plus组件库
- 兼容现有的文件选择功能
- 保持原有的点击打开文件功能

## 注意事项

1. 弹窗显示和隐藏都是立即响应，提供更好的用户体验
2. **所有引用都能显示弹窗**，包括单个引用
3. 弹窗内容限制为100字符，超出部分显示省略号
4. 参考[Element Plus Popover文档](https://element-plus.org/zh-CN/component/popover.html)确保正确配置
5. 整个引用项作为弹窗触发元素，提供更大的悬停区域