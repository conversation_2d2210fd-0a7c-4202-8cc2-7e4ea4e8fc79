/**
 * Web client for interacting with the Dify API from browsers
 * This implementation uses the fetch API and is designed for frontend use
 */
import type { IOnCompleted, IOnData, IOnError, IOnFile, IOnMessageEnd, IOnMessageReplace, IOnNodeFinished, IOnNodeStarted, IOnThought, IOnWorkflowFinished, IOnWorkflowStarted, IOtherOptions } from './base';
export type StreamChatParams = {
    /** 输入参数 */
    inputs: Record<string, any>;
    /** 用户消息 */
    query: string;
    /** 会话ID */
    conversation_id?: string;
    /** 用户标识 */
    user?: string;
    /** 附带的文件 */
    files?: ChatSendFile[];
};
export type StreamChatCallbacks = {
    onData: IOnData;
    onCompleted: IOnCompleted;
    onFile: IOnFile;
    onThought: IOnThought;
    onMessageEnd: IOnMessageEnd;
    onMessageReplace: IOnMessageReplace;
    onError: IOnError;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted: IOnWorkflowStarted;
    onNodeStarted: IOnNodeStarted;
    onNodeFinished: IOnNodeFinished;
    onWorkflowFinished: IOnWorkflowFinished;
};
export type ChatSendFile = {
    type: string;
    transfer_method: string;
    url: string;
    upload_file_id: string;
};
export type MessageFile = {
    id: string;
    filename: string;
    type: string;
    url: string;
    mime_type: string;
    size: number;
    transfer_method: string;
    belongs_to: string;
};
export type ConversationMessage = {
    id: string;
    conversation_id: string;
    parent_message_id: string;
    inputs: {
        [key: string]: any;
    };
    query: string;
    answer: string;
    message_files: MessageFile[];
    feedback: any;
    retriever_resources: any[];
    created_at: number;
    agent_thoughts: any[];
    status: string;
    error: any;
};
export declare const routes: {
    application: {
        method: string;
        url: () => string;
    };
    feedback: {
        method: string;
        url: (message_id: string) => string;
    };
    createCompletionMessage: {
        method: string;
        url: () => string;
    };
    createChatMessage: {
        method: string;
        url: () => string;
    };
    stopChatMessage: {
        method: string;
        url: (task_id: string) => string;
    };
    getConversationMessages: {
        method: string;
        url: () => string;
    };
    getConversations: {
        method: string;
        url: () => string;
    };
    renameConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    deleteConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    fileUpload: {
        method: string;
        url: () => string;
    };
    runWorkflow: {
        method: string;
        url: () => string;
    };
    audioToText: {
        method: string;
        url: () => string;
    };
    datasets: {
        method: string;
        url: () => string;
    };
    createDocumentByFile: {
        method: string;
        url: (dataset_id: string) => string;
    };
    getDocumentIndexingStatus: {
        method: string;
        url: (dataset_id: string, batch: string) => string;
    };
    getDatasetDocuments: {
        method: string;
        url: (dataset_id: string) => string;
    };
    getDatasetDocument: {
        method: string;
        url: (dataset_id: string, document_id: string) => string;
    };
    deleteDatasetDocument: {
        method: string;
        url: (dataset_id: string, document_id: string) => string;
    };
};
/**
 * Base web client for interacting with the Dify API from browsers
 * This implementation is suitable for browser environments where
 * API authentication can be handled by the server
 */
export declare class DifyClientWeb {
    _enableWebSearch: boolean;
    protected baseUrl: string;
    protected appKey: string;
    protected sessionId?: string;
    protected inputs: Record<string, any>;
    /**
     * Create a new Dify web client
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     */
    constructor(baseUrl?: string, appKey?: string);
    get enableWebSearch(): boolean;
    set enableWebSearch(value: boolean);
    /**
     * Get the singleton instance of DifyClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): DifyClientWeb;
    /**
     * Set or update the app key
     *
     * @param appKey - The key to identify which application configuration to use
     */
    setAppKey(appKey: string): void;
    /**
     * Set inputs object (overwrites existing inputs)
     *
     * @param inputs - Input parameters to set
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * set conversion sessionId
     * @param sessionId
     */
    setSessionId(sessionId: string): void;
    /**
     * Add inputs to existing inputs object (merges with existing)
     *
     * @param inputs - Input parameters to add
     */
    addInputs(inputs: Record<string, any>): void;
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param headerParams - Additional headers to include in the request
     * @returns Promise with the response data
     */
    sendRequest(method: string, endpoint: string, data?: any, params?: any, headerParams?: Record<string, string>, otherOptions?: IOtherOptions): Promise<any>;
    /**
     * Get application parameters
     *
     * @returns Application parameters from the Dify API
     */
    getApplicationParameters(): Promise<any>;
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user_id - Optional user identifier
     * @returns Response from the Dify API
     */
    messageFeedback(message_id: string, rating: 'like' | 'dislike', user_id?: string): Promise<any>;
    /**
     * Upload a file to the Dify API
     *
     * @param file - File to upload
     * @returns Promise that resolves with the uploaded file ID
     */
    fileUpload(file: File): Promise<{
        id: string;
        name: string;
        mime_type: string;
        size: number;
        extension: string;
    }>;
    /**
     * Convert audio to text
     *
     * @param audioFile - Audio file to transcribe
     * @param audioType - MIME type of the audio file
     * @returns Transcription response from the Dify API
     */
    audioToText(audioFile: File, audioType?: string): Promise<any>;
    getDatasets(): Promise<any>;
    createDocumentByFile(dataset_id: string, data: FormData): Promise<any>;
    getDocumentIndexingStatus(dataset_id: string, batch: string): Promise<any>;
    getDatasetDocuments(dataset_id: string, keyword?: string, page?: number, limit?: number): Promise<any>;
    deleteDatasetDocument(dataset_id: string, document_id: string): Promise<any>;
    getDatasetDocument(dataset_id: string, document_id: string): Promise<any>;
    /**
     * Get session ID from localStorage as fallback when cookies fail
     */
    private getSessionFromStorage;
    /**
     * Save session ID to localStorage as fallback
     */
    private saveSessionToStorage;
    /**
     * Extract session ID from API response and save it
     */
    private handleSessionFromResponse;
}
/**
 * Completion client for web browsers
 */
export declare class CompletionClientWeb extends DifyClientWeb {
    /**
     * Get the singleton instance of CompletionClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): CompletionClientWeb;
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with completion response
     */
    createCompletionMessage(inputs: Record<string, any>, user?: string, files?: string[]): Promise<any>;
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with workflow response
     */
    runWorkflow(inputs: Record<string, any>, files?: string[]): Promise<any>;
    /**
     * Create a streaming completion message
     * This uses the ssePost method from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    streamCompletionMessage(inputs: Record<string, any>, user?: string, files?: string[], callbacks?: {
        onData: IOnData;
        onCompleted: IOnCompleted;
        onFile: IOnFile;
        onThought: IOnThought;
        onMessageEnd: IOnMessageEnd;
        onMessageReplace: IOnMessageReplace;
        onError: IOnError;
        getAbortController?: (abortController: AbortController) => void;
        onWorkflowStarted: IOnWorkflowStarted;
        onNodeStarted: IOnNodeStarted;
        onNodeFinished: IOnNodeFinished;
        onWorkflowFinished: IOnWorkflowFinished;
    }): void;
}
/**
 * Chat client for web browsers
 */
export declare class ChatClientWeb extends DifyClientWeb {
    message_files: MessageFile[];
    /**
     * Get the singleton instance of ChatClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    static getInstance(baseUrl?: string, appKey?: string): ChatClientWeb;
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @returns Promise with chat message response
     */
    createChatMessage(inputs: Record<string, any>, query: string, response_mode?: string, conversation_id?: string, user?: string, files?: string[]): Promise<any>;
    /**
     * Create a streaming chat message
     * This uses ssePost from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    streamChatMessage({ inputs, query, conversation_id, user, files, }: StreamChatParams, callbacks?: StreamChatCallbacks): void;
    /**
     * Get messages from a conversation
     *
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Promise with conversation messages
     */
    getConversationMessages(conversation_id: string, first_id?: string, limit?: number): Promise<any>;
    /**
     * Get a list of conversations
     *
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns Promise with list of conversations
     */
    getConversations(first_id?: string, limit?: number, pinned?: boolean): Promise<any>;
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param auto_generate - Whether to auto-generate the name
     * @returns Promise with rename response
     */
    renameConversation(conversation_id: string, name: string, auto_generate?: boolean): Promise<any>;
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @returns Promise with delete response
     */
    deleteConversation(conversation_id: string): Promise<any>;
    /**
     * Stop a chat message task
     *
     * @param task_id - ID of the task to stop
     * @param user - User identifier
     * @returns Promise with stop response
     */
    stopChatMessage(task_id: string, user?: string): Promise<any>;
}
