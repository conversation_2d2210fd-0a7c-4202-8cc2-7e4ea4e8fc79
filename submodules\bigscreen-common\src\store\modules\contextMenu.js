/*
 * @Description:右键菜单数据
 * @Autor: wangjialing
 * @Date: 2023-06-28 09:30:13
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2023-10-11 09:34:38
 */

import { defineStore } from 'pinia'

const useContextMenuStore = defineStore('contextMenu', {
  state: () => ({
    pointList: [],// 标记的点数组
    isGaugePoint: false // 是否开始标记点
  }),
  getters: {},
  actions: {}
})

export default useContextMenuStore
