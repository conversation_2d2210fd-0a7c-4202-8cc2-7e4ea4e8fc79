/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./createTaskProcessorWorker","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryPipeline-9dbd2054","./ComponentDatatype-dad47320","./Cartesian3-bb0e6278","./OrientedBoundingBox-e47c7a90","./RuntimeError-592f0d41","./OglParser-bd379888","./Rectangle-9bffefe4","./Math-b5f4d889","./WebGLConstants-433debbf","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IndexDatatype-00859b8b","./IntersectionTests-25cff68e","./Plane-a268aa11","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d"],(function(t,e,n,o,r,a,i,s,u,c,p,f,d,m,y,b,l,A,D,g,v,h){"use strict";const C=[];function I(t,e,o){const a={indicesCount:e,indicesOffset:0,indicesStart:0,attributes:{},indices:null,vertexStart:0,vertexOffset:0,vertexCount:t};return function(t,e){const o=t.vertexCount,a=t.indicesCount,i=t.attributes;i.position=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:new Float32Array(3*o)}),i.batchId=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(o)}),i.normal=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:new Float32Array(3*o)}),e&&(i.st=new n.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(2*o)}));t.indices=o<65536?new Uint16Array(a):new Uint32Array(a)}(a,o),a}function x(t){return t.values.length/t.componentsPerAttribute}function B(t){return r.ComponentDatatype.getSizeInBytes(t.componentDatatype)*t.componentsPerAttribute}function O(t,n){const c=t.oglBufferArray,p=t.taskId,f=t.hasUv;!function(t){t.forEach((function(t){t.geometryData=u.OglParser.parseOglData(t.buffer)}))}(c),f&&function(t){let e,n,o,r,a;const i=[],s=[];s[0]=s[1]=Number.MAX_VALUE,i[0]=i[1]=-Number.MAX_VALUE;const u=Math.max,c=Math.min;let p,f,d;for(let m=0,y=t.length;m<y;m++){e=t[m],n=e.geometryData,o=n.groups[e.groupIndex],r=n.uv,a=n.indices;for(let t=o.start,e=o.start+o.count;t<e;t++)p=a[t],f=r[2*p],d=r[2*p+1],s[0]=c(f,s[0]),i[0]=u(f,i[0]),s[1]=c(d,s[1]),i[1]=u(d,i[1])}}(c);const d=function(t,e,n){let o,r,a,i,s,u,c,p,f,d,m,y,b,l,A,D,g,v,h,I,x;const B=e.attributes,O=B.position.values,P=B.normal.values,w=n?B.st.values:void 0,T=B.batchId.values,L=e.indices;let E=0,F=0,S=0;for(let e=0,B=t.length;e<B;e++){C.length=0,S=0,o=t[e],x=o.batchId,r=o.geometryData,a=r.groups[o.groupIndex],i=o.vertexCount;const B=o.matrix,G=r.indices,k=r.position,z=r.normal,U=r.uv;let M,R,$,N,V,W,X;s=B[0],u=B[4],c=B[8],p=B[12],f=B[1],d=B[5],m=B[9],y=B[13],b=B[2],l=B[6],A=B[10],D=B[14],g=B[3],v=B[7],h=B[11],I=B[15];for(let t=0,e=a.count;t<e;t++){const e=G[t+a.start];if(void 0===C[e]){C[e]=S,S++,M=k[3*e],R=k[3*e+1],$=k[3*e+2],N=M*s+R*u+$*c+p,V=M*f+R*d+$*m+y,W=M*b+R*l+$*A+D,X=M*g+R*v+$*h+I;const t=E+C[e];O[3*t]=N/X,O[3*t+1]=V/X,O[3*t+2]=W/X,M=z[3*e],R=z[3*e+1],$=z[3*e+2],N=M*s+R*u+$*c,V=M*f+R*d+$*m,W=M*b+R*l+$*A,P[3*t]=N,P[3*t+1]=V,P[3*t+2]=W,n&&(w[2*t]=U[2*e],w[2*t+1]=U[2*e+1]),T[t]=x}L[F+t]=E+C[e]}E+=i,F+=a.count}return e}(c,I(t.vertexCount,t.indicesCount,f),f);d.obb=function(t){const e=a.Cartesian3.unpackArray(t,[]);return i.OrientedBoundingBox.fromPoints(e)}(d.attributes.position.values);const m=o.GeometryPipeline.createAttributeLocations(d),y=function(t){let n,o,a;const i=[];for(o in t)t.hasOwnProperty(o)&&e.defined(t[o])&&e.defined(t[o].values)&&(i.push(o),t[o].componentDatatype===r.ComponentDatatype.DOUBLE&&(t[o].componentDatatype=r.ComponentDatatype.FLOAT,t[o].values=r.ComponentDatatype.createTypedArray(r.ComponentDatatype.FLOAT,t[o].values)));let u;const c=i.length;if(c>0)for(u=x(t[i[0]]),n=1;n<c;++n){const e=x(t[i[n]]);if(e!==u)throw new s.RuntimeError(`Each attribute list must have the same number of vertices.  Attribute ${i[n]} has a different number of vertices (${e.toString()}) than attribute ${i[0]} (${u.toString()}).`)}i.sort((function(e,n){return r.ComponentDatatype.getSizeInBytes(t[n].componentDatatype)-r.ComponentDatatype.getSizeInBytes(t[e].componentDatatype)}));let p=0;const f={};for(n=0;n<c;++n)o=i[n],a=t[o],f[o]=p,p+=B(a);if(p>0){const e=r.ComponentDatatype.getSizeInBytes(t[i[0]].componentDatatype),s=p%e;0!==s&&(p+=e-s);const d=new ArrayBuffer(u*p),m={};for(n=0;n<c;++n){o=i[n];const e=r.ComponentDatatype.getSizeInBytes(t[o].componentDatatype);m[o]={pointer:r.ComponentDatatype.createTypedArray(t[o].componentDatatype,d),index:f[o]/e,strideInComponentType:p/e}}for(n=0;n<u;++n)for(let e=0;e<c;++e){o=i[e],a=t[o];const r=a.values,s=m[o],u=s.pointer,c=a.componentsPerAttribute;for(let t=0;t<c;++t)u[s.index+t]=r[n*c+t];s.index+=s.strideInComponentType}return{buffer:d,offsetsInBytes:f,vertexSizeInBytes:p}}}(d.attributes);y.attributeLocations=m,function(t){const e=t.attributes;for(const t in e)e.hasOwnProperty(t)&&(e[t].values=void 0)}(d);const b={attributes:y,indices:d.indices,obb:i.OrientedBoundingBox.pack(d.obb,[]),materialId:c[0].materialId};n.push(y.buffer),n.push(d.indices.buffer);const l=[];c.forEach((function(t){var e,o;l.push({geometryId:t.geometryId,buffer:t.buffer}),e=n,o=t.buffer,-1===e.indexOf(o)&&e.push(o)}));return{mainThreadBuffers:l,batchedGeometry:b,taskId:p}}return t((function(t,e){return O(t,e)}))}));
