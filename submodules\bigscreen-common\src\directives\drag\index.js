/**
 * @description: 拖动指令
 * @param {String} draggableClass 能拖动的子class，如果不传，则默认为整个父级都可以拖动, 'none' 没有拖动
 */
const vDrag = {
  mounted: (el, binding, vnode) => {
    const { draggableClass } = binding.value || {}
    if (draggableClass == 'none') return
    let draggableEl = el

    if (draggableClass) {
      draggableEl = el.querySelector('.' + draggableClass)
    }
    if (draggableEl) {
      draggableEl.onmousedown = (eDown) => {
        const divx = eDown.pageX - el.offsetLeft
        const divy = eDown.pageY - el.offsetTop
        document.onmousemove = (e) => {
          const left = e.pageX - divx
          const top = e.pageY - divy
          el.style.right = 'unset'
          el.style.left = left + 'px'
          el.style.top = top + 'px'
        }
        document.onmouseup = function () {
          document.onmousemove = null
          document.onmouseup = null
        }
      }
    }
  }
}
export default vDrag
