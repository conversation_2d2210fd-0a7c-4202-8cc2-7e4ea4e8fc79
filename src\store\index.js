import useDictionary from './modules/dictionary.js'
import useScreenComWindowStore from './modules/screenComWindow.js'
import useScreenDataStore from './modules/screenData.js'
import useScreenPageStore from './modules/screenPage.js'
import useScreenPopWindowStore from './modules/screenPopWindow.js'
import useStoreDemo from './modules/storeDemo.js'
import useUe from './modules/ue.js'
import useScreenWaterPageStore from './modules/waterPage.js'

const useStore = () => ({
  /**
   * @description: 存储弹出窗口数据
   */
  storeScreenPopWindowData: useScreenPopWindowStore(),
  storeScreenComWindowData: useScreenComWindowStore(),
  /**
   * @description:
   * ```
   * 页面数据存储
   * state: activePage(HomePage,Traffic,Monitor,Emergency,Inspect)
   * actions:setActivePage
   * ```
   */
  screen: useScreenPageStore(),
  storeDemo: useStoreDemo(),
  storeDictionary: useDictionary(),
  /**
   * @description: 存储UE数据
   */
  storeUe: useUe(),
  storeWaterData: useScreenWaterPageStore(),
  storeScreenData: useScreenDataStore()
})
export default useStore
