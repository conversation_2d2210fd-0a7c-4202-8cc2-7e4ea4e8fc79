import ForEach from"./ForEach.js";import defined from"../../Core/defined.js";function addPipelineExtras(e){return ForEach.shader(e,(function(e){addExtras(e)})),ForEach.buffer(e,(function(e){addExtras(e)})),ForEach.image(e,(function(e){addExtras(e),ForEach.compressedImage(e,(function(e){addExtras(e)}))})),addExtras(e),e}function addExtras(e){e.extras=defined(e.extras)?e.extras:{},e.extras._pipeline=defined(e.extras._pipeline)?e.extras._pipeline:{}}export default addPipelineExtras;