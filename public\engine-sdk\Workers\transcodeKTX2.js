define(["./when-1807bd8d","./Check-1951f41f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./createTaskProcessorWorker","./PixelFormat-a042b7a2"],(function(_,R,A,t,e,O){"use strict";const T={VK_FORMAT_UNDEFINED:0,VK_FORMAT_R4G4_UNORM_PACK8:1,VK_FORMAT_R4G4B4A4_UNORM_PACK16:2,VK_FORMAT_B4G4R4A4_UNORM_PACK16:3,VK_FORMAT_R5G6B5_UNORM_PACK16:4,VK_FORMAT_B5G6R5_UNORM_PACK16:5,VK_FORMAT_R5G5B5A1_UNORM_PACK16:6,VK_FORMAT_B5G5R5A1_UNORM_PACK16:7,VK_FORMAT_A1R5G5B5_UNORM_PACK16:8,VK_FORMAT_R8_UNORM:9,VK_FORMAT_R8_SNORM:10,VK_FORMAT_R8_USCALED:11,VK_FORMAT_R8_SSCALED:12,VK_FORMAT_R8_UINT:13,VK_FORMAT_R8_SINT:14,VK_FORMAT_R8_SRGB:15,VK_FORMAT_R8G8_UNORM:16,VK_FORMAT_R8G8_SNORM:17,VK_FORMAT_R8G8_USCALED:18,VK_FORMAT_R8G8_SSCALED:19,VK_FORMAT_R8G8_UINT:20,VK_FORMAT_R8G8_SINT:21,VK_FORMAT_R8G8_SRGB:22,VK_FORMAT_R8G8B8_UNORM:23,VK_FORMAT_R8G8B8_SNORM:24,VK_FORMAT_R8G8B8_USCALED:25,VK_FORMAT_R8G8B8_SSCALED:26,VK_FORMAT_R8G8B8_UINT:27,VK_FORMAT_R8G8B8_SINT:28,VK_FORMAT_R8G8B8_SRGB:29,VK_FORMAT_B8G8R8_UNORM:30,VK_FORMAT_B8G8R8_SNORM:31,VK_FORMAT_B8G8R8_USCALED:32,VK_FORMAT_B8G8R8_SSCALED:33,VK_FORMAT_B8G8R8_UINT:34,VK_FORMAT_B8G8R8_SINT:35,VK_FORMAT_B8G8R8_SRGB:36,VK_FORMAT_R8G8B8A8_UNORM:37,VK_FORMAT_R8G8B8A8_SNORM:38,VK_FORMAT_R8G8B8A8_USCALED:39,VK_FORMAT_R8G8B8A8_SSCALED:40,VK_FORMAT_R8G8B8A8_UINT:41,VK_FORMAT_R8G8B8A8_SINT:42,VK_FORMAT_R8G8B8A8_SRGB:43,VK_FORMAT_B8G8R8A8_UNORM:44,VK_FORMAT_B8G8R8A8_SNORM:45,VK_FORMAT_B8G8R8A8_USCALED:46,VK_FORMAT_B8G8R8A8_SSCALED:47,VK_FORMAT_B8G8R8A8_UINT:48,VK_FORMAT_B8G8R8A8_SINT:49,VK_FORMAT_B8G8R8A8_SRGB:50,VK_FORMAT_A8B8G8R8_UNORM_PACK32:51,VK_FORMAT_A8B8G8R8_SNORM_PACK32:52,VK_FORMAT_A8B8G8R8_USCALED_PACK32:53,VK_FORMAT_A8B8G8R8_SSCALED_PACK32:54,VK_FORMAT_A8B8G8R8_UINT_PACK32:55,VK_FORMAT_A8B8G8R8_SINT_PACK32:56,VK_FORMAT_A8B8G8R8_SRGB_PACK32:57,VK_FORMAT_A2R10G10B10_UNORM_PACK32:58,VK_FORMAT_A2R10G10B10_SNORM_PACK32:59,VK_FORMAT_A2R10G10B10_USCALED_PACK32:60,VK_FORMAT_A2R10G10B10_SSCALED_PACK32:61,VK_FORMAT_A2R10G10B10_UINT_PACK32:62,VK_FORMAT_A2R10G10B10_SINT_PACK32:63,VK_FORMAT_A2B10G10R10_UNORM_PACK32:64,VK_FORMAT_A2B10G10R10_SNORM_PACK32:65,VK_FORMAT_A2B10G10R10_USCALED_PACK32:66,VK_FORMAT_A2B10G10R10_SSCALED_PACK32:67,VK_FORMAT_A2B10G10R10_UINT_PACK32:68,VK_FORMAT_A2B10G10R10_SINT_PACK32:69,VK_FORMAT_R16_UNORM:70,VK_FORMAT_R16_SNORM:71,VK_FORMAT_R16_USCALED:72,VK_FORMAT_R16_SSCALED:73,VK_FORMAT_R16_UINT:74,VK_FORMAT_R16_SINT:75,VK_FORMAT_R16_SFLOAT:76,VK_FORMAT_R16G16_UNORM:77,VK_FORMAT_R16G16_SNORM:78,VK_FORMAT_R16G16_USCALED:79,VK_FORMAT_R16G16_SSCALED:80,VK_FORMAT_R16G16_UINT:81,VK_FORMAT_R16G16_SINT:82,VK_FORMAT_R16G16_SFLOAT:83,VK_FORMAT_R16G16B16_UNORM:84,VK_FORMAT_R16G16B16_SNORM:85,VK_FORMAT_R16G16B16_USCALED:86,VK_FORMAT_R16G16B16_SSCALED:87,VK_FORMAT_R16G16B16_UINT:88,VK_FORMAT_R16G16B16_SINT:89,VK_FORMAT_R16G16B16_SFLOAT:90,VK_FORMAT_R16G16B16A16_UNORM:91,VK_FORMAT_R16G16B16A16_SNORM:92,VK_FORMAT_R16G16B16A16_USCALED:93,VK_FORMAT_R16G16B16A16_SSCALED:94,VK_FORMAT_R16G16B16A16_UINT:95,VK_FORMAT_R16G16B16A16_SINT:96,VK_FORMAT_R16G16B16A16_SFLOAT:97,VK_FORMAT_R32_UINT:98,VK_FORMAT_R32_SINT:99,VK_FORMAT_R32_SFLOAT:100,VK_FORMAT_R32G32_UINT:101,VK_FORMAT_R32G32_SINT:102,VK_FORMAT_R32G32_SFLOAT:103,VK_FORMAT_R32G32B32_UINT:104,VK_FORMAT_R32G32B32_SINT:105,VK_FORMAT_R32G32B32_SFLOAT:106,VK_FORMAT_R32G32B32A32_UINT:107,VK_FORMAT_R32G32B32A32_SINT:108,VK_FORMAT_R32G32B32A32_SFLOAT:109,VK_FORMAT_R64_UINT:110,VK_FORMAT_R64_SINT:111,VK_FORMAT_R64_SFLOAT:112,VK_FORMAT_R64G64_UINT:113,VK_FORMAT_R64G64_SINT:114,VK_FORMAT_R64G64_SFLOAT:115,VK_FORMAT_R64G64B64_UINT:116,VK_FORMAT_R64G64B64_SINT:117,VK_FORMAT_R64G64B64_SFLOAT:118,VK_FORMAT_R64G64B64A64_UINT:119,VK_FORMAT_R64G64B64A64_SINT:120,VK_FORMAT_R64G64B64A64_SFLOAT:121,VK_FORMAT_B10G11R11_UFLOAT_PACK32:122,VK_FORMAT_E5B9G9R9_UFLOAT_PACK32:123,VK_FORMAT_D16_UNORM:124,VK_FORMAT_X8_D24_UNORM_PACK32:125,VK_FORMAT_D32_SFLOAT:126,VK_FORMAT_S8_UINT:127,VK_FORMAT_D16_UNORM_S8_UINT:128,VK_FORMAT_D24_UNORM_S8_UINT:129,VK_FORMAT_D32_SFLOAT_S8_UINT:130,VK_FORMAT_BC1_RGB_UNORM_BLOCK:131,VK_FORMAT_BC1_RGB_SRGB_BLOCK:132,VK_FORMAT_BC1_RGBA_UNORM_BLOCK:133,VK_FORMAT_BC1_RGBA_SRGB_BLOCK:134,VK_FORMAT_BC2_UNORM_BLOCK:135,VK_FORMAT_BC2_SRGB_BLOCK:136,VK_FORMAT_BC3_UNORM_BLOCK:137,VK_FORMAT_BC3_SRGB_BLOCK:138,VK_FORMAT_BC4_UNORM_BLOCK:139,VK_FORMAT_BC4_SNORM_BLOCK:140,VK_FORMAT_BC5_UNORM_BLOCK:141,VK_FORMAT_BC5_SNORM_BLOCK:142,VK_FORMAT_BC6H_UFLOAT_BLOCK:143,VK_FORMAT_BC6H_SFLOAT_BLOCK:144,VK_FORMAT_BC7_UNORM_BLOCK:145,VK_FORMAT_BC7_SRGB_BLOCK:146,VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK:147,VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK:148,VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK:149,VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK:150,VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK:151,VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK:152,VK_FORMAT_EAC_R11_UNORM_BLOCK:153,VK_FORMAT_EAC_R11_SNORM_BLOCK:154,VK_FORMAT_EAC_R11G11_UNORM_BLOCK:155,VK_FORMAT_EAC_R11G11_SNORM_BLOCK:156,VK_FORMAT_ASTC_4x4_UNORM_BLOCK:157,VK_FORMAT_ASTC_4x4_SRGB_BLOCK:158,VK_FORMAT_ASTC_5x4_UNORM_BLOCK:159,VK_FORMAT_ASTC_5x4_SRGB_BLOCK:160,VK_FORMAT_ASTC_5x5_UNORM_BLOCK:161,VK_FORMAT_ASTC_5x5_SRGB_BLOCK:162,VK_FORMAT_ASTC_6x5_UNORM_BLOCK:163,VK_FORMAT_ASTC_6x5_SRGB_BLOCK:164,VK_FORMAT_ASTC_6x6_UNORM_BLOCK:165,VK_FORMAT_ASTC_6x6_SRGB_BLOCK:166,VK_FORMAT_ASTC_8x5_UNORM_BLOCK:167,VK_FORMAT_ASTC_8x5_SRGB_BLOCK:168,VK_FORMAT_ASTC_8x6_UNORM_BLOCK:169,VK_FORMAT_ASTC_8x6_SRGB_BLOCK:170,VK_FORMAT_ASTC_8x8_UNORM_BLOCK:171,VK_FORMAT_ASTC_8x8_SRGB_BLOCK:172,VK_FORMAT_ASTC_10x5_UNORM_BLOCK:173,VK_FORMAT_ASTC_10x5_SRGB_BLOCK:174,VK_FORMAT_ASTC_10x6_UNORM_BLOCK:175,VK_FORMAT_ASTC_10x6_SRGB_BLOCK:176,VK_FORMAT_ASTC_10x8_UNORM_BLOCK:177,VK_FORMAT_ASTC_10x8_SRGB_BLOCK:178,VK_FORMAT_ASTC_10x10_UNORM_BLOCK:179,VK_FORMAT_ASTC_10x10_SRGB_BLOCK:180,VK_FORMAT_ASTC_12x10_UNORM_BLOCK:181,VK_FORMAT_ASTC_12x10_SRGB_BLOCK:182,VK_FORMAT_ASTC_12x12_UNORM_BLOCK:183,VK_FORMAT_ASTC_12x12_SRGB_BLOCK:184,VK_FORMAT_G8B8G8R8_422_UNORM:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM:1000156006,VK_FORMAT_R10X6_UNORM_PACK16:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16:1000156016,VK_FORMAT_R12X4_UNORM_PACK16:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM:1000156033,VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG:1000054e3,VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG:1000054001,VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG:1000054002,VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG:1000054003,VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG:1000054004,VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG:1000054005,VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG:1000054006,VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG:1000054007,VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT:1000066e3,VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT:1000066001,VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT:1000066002,VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT:1000066003,VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT:1000066004,VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT:1000066005,VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT:1000066006,VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT:1000066007,VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT:1000066008,VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT:1000066009,VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT:1000066010,VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT:1000066011,VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT:1000066012,VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT:1000066013,VK_FORMAT_G8B8G8R8_422_UNORM_KHR:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM_KHR:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR:1000156006,VK_FORMAT_R10X6_UNORM_PACK16_KHR:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR:1000156016,VK_FORMAT_R12X4_UNORM_PACK16_KHR:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM_KHR:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM_KHR:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR:1000156033};var K=Object.freeze(T);const M=0,F=0,n=0,B=2,i=0,V=0,C=2,G=1,r=64,s=0;class o{constructor(){this.vkFormat=s,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=M,this.levels=[],this.dataFormatDescriptor=[{vendorId:n,descriptorType:F,descriptorBlockSize:0,versionNumber:B,colorModel:i,colorPrimaries:G,transferFunction:C,flags:V,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],this.keyValue={},this.globalData=null}}class U{constructor(_,R,A,t){this._dataView=void 0,this._littleEndian=void 0,this._offset=void 0,this._dataView=new DataView(_.buffer,_.byteOffset+R,A),this._littleEndian=t,this._offset=0}_nextUint8(){const _=this._dataView.getUint8(this._offset);return this._offset+=1,_}_nextUint16(){const _=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,_}_nextUint32(){const _=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,_}_nextUint64(){const _=this._dataView.getUint32(this._offset,this._littleEndian),R=this._dataView.getUint32(this._offset+4,this._littleEndian),A=_+2**32*R;return this._offset+=8,A}_nextInt32(){const _=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,_}_skip(_){return this._offset+=_,this}_scan(_,R){void 0===R&&(R=0);const A=this._offset;let t=0;while(this._dataView.getUint8(this._offset)!==R&&t<_)t++,this._offset++;return t<_&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+A,t)}}const N=[171,75,84,88,32,50,48,187,13,10,26,10];function a(_){return"undefined"!==typeof TextDecoder?(new TextDecoder).decode(_):Buffer.from(_).toString("utf8")}function S(_){const R=new Uint8Array(_.buffer,_.byteOffset,N.length);if(R[0]!==N[0]||R[1]!==N[1]||R[2]!==N[2]||R[3]!==N[3]||R[4]!==N[4]||R[5]!==N[5]||R[6]!==N[6]||R[7]!==N[7]||R[8]!==N[8]||R[9]!==N[9]||R[10]!==N[10]||R[11]!==N[11])throw new Error("Missing KTX 2.0 identifier.");const A=new o,t=17*Uint32Array.BYTES_PER_ELEMENT,e=new U(_,N.length,t,!0);A.vkFormat=e._nextUint32(),A.typeSize=e._nextUint32(),A.pixelWidth=e._nextUint32(),A.pixelHeight=e._nextUint32(),A.pixelDepth=e._nextUint32(),A.layerCount=e._nextUint32(),A.faceCount=e._nextUint32();const O=e._nextUint32();A.supercompressionScheme=e._nextUint32();const T=e._nextUint32(),K=e._nextUint32(),M=e._nextUint32(),F=e._nextUint32(),n=e._nextUint64(),B=e._nextUint64(),i=3*O*8,V=new U(_,N.length+t,i,!0);for(let r=0;r<O;r++)A.levels.push({levelData:new Uint8Array(_.buffer,_.byteOffset+V._nextUint64(),V._nextUint64()),uncompressedByteLength:V._nextUint64()});const C=new U(_,T,K,!0),G={vendorId:C._skip(4)._nextUint16(),descriptorType:C._nextUint16(),versionNumber:C._nextUint16(),descriptorBlockSize:C._nextUint16(),colorModel:C._nextUint8(),colorPrimaries:C._nextUint8(),transferFunction:C._nextUint8(),flags:C._nextUint8(),texelBlockDimension:[C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8()],bytesPlane:[C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8()],samples:[]},s=6,S=4,l=(G.descriptorBlockSize/4-s)/S;for(let o=0;o<l;o++){const _={bitOffset:C._nextUint16(),bitLength:C._nextUint8(),channelType:C._nextUint8(),samplePosition:[C._nextUint8(),C._nextUint8(),C._nextUint8(),C._nextUint8()],sampleLower:-1/0,sampleUpper:1/0};_.channelType&r?(_.sampleLower=C._nextInt32(),_.sampleUpper=C._nextInt32()):(_.sampleLower=C._nextUint32(),_.sampleUpper=C._nextUint32()),G.samples[o]=_}A.dataFormatDescriptor.length=0,A.dataFormatDescriptor.push(G);const L=new U(_,M,F,!0);while(L._offset<F){const _=L._nextUint32(),R=L._scan(_),t=a(R),e=L._scan(_-R.byteLength);A.keyValue[t]=t.match(/^ktx/i)?a(e):e,L._offset%4&&L._skip(4-L._offset%4)}if(B<=0)return A;const P=new U(_,n,B,!0),f=P._nextUint16(),x=P._nextUint16(),c=P._nextUint32(),X=P._nextUint32(),E=P._nextUint32(),h=P._nextUint32(),d=[];for(let r=0;r<O;r++)d.push({imageFlags:P._nextUint32(),rgbSliceByteOffset:P._nextUint32(),rgbSliceByteLength:P._nextUint32(),alphaSliceByteOffset:P._nextUint32(),alphaSliceByteLength:P._nextUint32()});const u=n+P._offset,p=u+c,m=p+X,I=m+E,D=new Uint8Array(_.buffer,_.byteOffset+u,c),w=new Uint8Array(_.buffer,_.byteOffset+p,X),g=new Uint8Array(_.buffer,_.byteOffset+m,E),y=new Uint8Array(_.buffer,_.byteOffset+I,h);return A.globalData={endpointCount:f,selectorCount:x,imageDescs:d,endpointsData:D,selectorsData:w,tablesData:g,extendedData:y},A}const l=["positiveX","negativeX","positiveY","negativeY","positiveZ","negativeZ"],L=163,P=166;let f;function x(_,t){R.Check.typeOf.object("transcoderModule",f);const e=_.ktx2Buffer,O=_.supportedTargetFormats;let T;try{T=S(e)}catch(F){throw new A.RuntimeError("Invalid KTX2 file.")}if(0!==T.layerCount)throw new A.RuntimeError("KTX2 texture arrays are not supported.");if(0!==T.pixelDepth)throw new A.RuntimeError("KTX2 3D textures are unsupported.");const K=T.dataFormatDescriptor[0],M=new Array(T.levelCount);return 0!==T.vkFormat||K.colorModel!==L&&K.colorModel!==P?(t.push(e.buffer),c(T,M)):X(e,T,O,f,t,M),M}function c(R,A){const t=R.vkFormat===K.VK_FORMAT_R8G8B8_SRGB?O.PixelFormat.RGB:O.PixelFormat.RGBA;let e;R.vkFormat===K.VK_FORMAT_R8G8B8A8_UNORM?e=O.PixelDatatype.UNSIGNED_BYTE:R.vkFormat===K.VK_FORMAT_R16G16B16A16_SFLOAT?e=O.PixelDatatype.HALF_FLOAT:R.vkFormat===K.VK_FORMAT_R32G32B32A32_SFLOAT&&(e=O.PixelDatatype.FLOAT);for(let T=0;T<R.levels.length;++T){const K={};A[T]=K;const M=R.levels[T].levelData,F=R.pixelWidth>>T,n=R.pixelHeight>>T,B=F*n*O.PixelFormat.componentsLength(t);for(let A=0;A<R.faceCount;++A){const T=M.byteOffset+B*R.typeSize*A;let i;i=_.defined(e)&&1!==O.PixelDatatype.sizeInBytes(e)?2===O.PixelDatatype.sizeInBytes(e)?new Uint16Array(M.buffer,T,B):new Float32Array(M.buffer,T,B):new Uint8Array(M.buffer,T,B),K[l[A]]={internalFormat:t,datatype:e,width:F,height:n,levelBuffer:i}}}}function X(R,t,e,T,K,M){const F=new T.KTX2File(R);let n=F.getWidth(),B=F.getHeight();const i=F.getLevels(),V=F.getHasAlpha();if(!(n>0)||!(B>0)||!(i>0))throw F.close(),F.delete(),new A.RuntimeError("Invalid KTX2 file");let C,G;const r=t.dataFormatDescriptor[0],s=T.transcoder_texture_format;if(r.colorModel===L)if(e.etc)C=V?O.PixelFormat.RGBA8_ETC2_EAC:O.PixelFormat.RGB8_ETC2,G=V?s.cTFETC2_RGBA:s.cTFETC1_RGB;else if(e.etc1&&!V)C=O.PixelFormat.RGB_ETC1,G=s.cTFETC1_RGB;else if(e.s3tc)C=V?O.PixelFormat.RGBA_DXT5:O.PixelFormat.RGB_DXT1,G=V?s.cTFBC3_RGBA:s.cTFBC1_RGB;else if(e.pvrtc)C=V?O.PixelFormat.RGBA_PVRTC_4BPPV1:O.PixelFormat.RGB_PVRTC_4BPPV1,G=V?s.cTFPVRTC1_4_RGBA:s.cTFPVRTC1_4_RGB;else if(e.astc)C=O.PixelFormat.RGBA_ASTC,G=s.cTFASTC_4x4_RGBA;else{if(!e.bc7)throw new A.RuntimeError("No transcoding format target available for ETC1S compressed ktx2.");C=O.PixelFormat.RGBA_BC7,G=s.cTFBC7_RGBA}else if(r.colorModel===P)if(e.astc)C=O.PixelFormat.RGBA_ASTC,G=s.cTFASTC_4x4_RGBA;else if(e.bc7)C=O.PixelFormat.RGBA_BC7,G=s.cTFBC7_RGBA;else if(e.s3tc)C=V?O.PixelFormat.RGBA_DXT5:O.PixelFormat.RGB_DXT1,G=V?s.cTFBC3_RGBA:s.cTFBC1_RGB;else if(e.etc)C=V?O.PixelFormat.RGBA8_ETC2_EAC:O.PixelFormat.RGB8_ETC2,G=V?s.cTFETC2_RGBA:s.cTFETC1_RGB;else if(e.etc1&&!V)C=O.PixelFormat.RGB_ETC1,G=s.cTFETC1_RGB;else{if(!e.pvrtc)throw new A.RuntimeError("No transcoding format target available for UASTC compressed ktx2.");C=V?O.PixelFormat.RGBA_PVRTC_4BPPV1:O.PixelFormat.RGB_PVRTC_4BPPV1,G=V?s.cTFPVRTC1_4_RGBA:s.cTFPVRTC1_4_RGB}if(!F.startTranscoding())throw F.close(),F.delete(),new A.RuntimeError("startTranscoding() failed");for(let O=0;O<t.levels.length;++O){const R={};M[O]=R,n=t.pixelWidth>>O,B=t.pixelHeight>>O;const e=F.getImageTranscodedSizeInBytes(O,0,0,G.value),T=new Uint8Array(e),i=F.transcodeImage(T,O,0,0,G.value,0,-1,-1);if(!_.defined(i))throw new A.RuntimeError("transcodeImage() failed.");K.push(T.buffer),R[l[0]]={internalFormat:C,width:n,height:B,levelBuffer:T}}return F.close(),F.delete(),M}function E(_){f=_,f.initializeBasis(),self.onmessage=e(x),self.postMessage(!0)}function h(R){const A=R.data,t=A.webAssemblyConfig;if(_.defined(t))return require([t.modulePath],(function(R){if(!_.defined(t.wasmBinaryFile))return R().then((function(_){E(_)}));_.defined(R)||(R=self.MSC_TRANSCODER),R(t).then((function(_){E(_)}))}))}return h}));