define(["exports","./Check-1951f41f","./Cartesian2-d051b2ac","./Matrix4-a50b021f","./OrientedBoundingBox-d0a49c02"],(function(n,e,t,a,i){"use strict";var r={},o=new t.Cartesian3,u=new t.Cartesian3,s=new t.Cartesian3,C=new t.Cartesian3,d=new i.OrientedBoundingBox;function c(n,e,a,i,r){var u=t.Cartesian3.subtract(n,e,o),s=t.Cartesian3.dot(a,u),C=t.Cartesian3.dot(i,u);return t.Cartesian2.fromElements(s,C,r)}r.validOutline=function(n){e.Check.defined("positions",n);var r=i.OrientedBoundingBox.fromPoints(n,d),o=r.halfAxes,c=a.Matrix3.getColumn(o,0,u),f=a.Matrix3.getColumn(o,1,s),l=a.Matrix3.getColumn(o,2,C),m=t.Cartesian3.magnitude(c),g=t.Cartesian3.magnitude(f),x=t.Cartesian3.magnitude(l);return!(0===m&&(0===g||0===x)||0===g&&0===x)},r.computeProjectTo2DArguments=function(n,r,o,c){e.Check.defined("positions",n),e.Check.defined("centerResult",r),e.Check.defined("planeAxis1Result",o),e.Check.defined("planeAxis2Result",c);var f,l,m=i.OrientedBoundingBox.fromPoints(n,d),g=m.halfAxes,x=a.Matrix3.getColumn(g,0,u),h=a.Matrix3.getColumn(g,1,s),B=a.Matrix3.getColumn(g,2,C),M=t.Cartesian3.magnitude(x),P=t.Cartesian3.magnitude(h),p=t.Cartesian3.magnitude(B),k=Math.min(M,P,p);return(0!==M||0!==P&&0!==p)&&(0!==P||0!==p)&&(k!==P&&k!==p||(f=x),k===M?f=h:k===p&&(l=h),k!==M&&k!==P||(l=B),t.Cartesian3.normalize(f,o),t.Cartesian3.normalize(l,c),t.Cartesian3.clone(m.center,r),!0)},r.createProjectPointsTo2DFunction=function(n,e,t){return function(a){for(var i=new Array(a.length),r=0;r<a.length;r++)i[r]=c(a[r],n,e,t);return i}},r.createProjectPointTo2DFunction=function(n,e,t){return function(a,i){return c(a,n,e,t,i)}},n.CoplanarPolygonGeometryLibrary=r}));