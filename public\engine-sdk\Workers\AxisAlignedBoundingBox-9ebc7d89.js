define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Transforms-3ef1852a","./Matrix4-a50b021f","./Plane-1b1689fd"],(function(i,t,n,m,e,a,r){"use strict";function s(i,n,e){this.minimum=m.Cartesian3.clone(t.defaultValue(i,m.Cartesian3.ZERO)),this.maximum=m.Cartesian3.clone(t.defaultValue(n,m.Cartesian3.ZERO)),e=t.defined(e)?m.Cartesian3.clone(e):m.Cartesian3.midpoint(this.minimum,this.maximum,new m.Cartesian3),this.center=e}s.fromPoints=function(i,n){if(t.defined(n)||(n=new s),!t.defined(i)||0===i.length)return n.minimum=m.Cartesian3.clone(m.Cartesian3.ZERO,n.minimum),n.maximum=m.Cartesian3.clone(m.Cartesian3.ZERO,n.maximum),n.center=m.Cartesian3.clone(m.Cartesian3.ZERO,n.center),n;for(var e=i[0].x,a=i[0].y,r=i[0].z,u=i[0].x,o=i[0].y,c=i[0].z,h=i.length,x=1;x<h;x++){var f=i[x],l=f.x,d=f.y,y=f.z;e=Math.min(l,e),u=Math.max(l,u),a=Math.min(d,a),o=Math.max(d,o),r=Math.min(y,r),c=Math.max(y,c)}var C=n.minimum;C.x=e,C.y=a,C.z=r;var p=n.maximum;return p.x=u,p.y=o,p.z=c,n.center=m.Cartesian3.midpoint(C,p,n.center),n},s.clone=function(i,n){if(t.defined(i))return t.defined(n)?(n.minimum=m.Cartesian3.clone(i.minimum,n.minimum),n.maximum=m.Cartesian3.clone(i.maximum,n.maximum),n.center=m.Cartesian3.clone(i.center,n.center),n):new s(i.minimum,i.maximum,i.center)},s.equals=function(i,n){return i===n||t.defined(i)&&t.defined(n)&&m.Cartesian3.equals(i.center,n.center)&&m.Cartesian3.equals(i.minimum,n.minimum)&&m.Cartesian3.equals(i.maximum,n.maximum)};var u=new m.Cartesian3;s.intersectPlane=function(i,t){n.Check.defined("box",i),n.Check.defined("plane",t),u=m.Cartesian3.subtract(i.maximum,i.minimum,u);var a=m.Cartesian3.multiplyByScalar(u,.5,u),r=t.normal,s=a.x*Math.abs(r.x)+a.y*Math.abs(r.y)+a.z*Math.abs(r.z),o=m.Cartesian3.dot(i.center,r)+t.distance;return o-s>0?e.Intersect.INSIDE:o+s<0?e.Intersect.OUTSIDE:e.Intersect.INTERSECTING},s.prototype.clone=function(i){return s.clone(this,i)},s.prototype.intersectPlane=function(i){return s.intersectPlane(this,i)},s.prototype.getTopPlane=function(){var i=this._topPlane;return t.defined(i)||(i=new r.Plane(m.Cartesian3.UNIT_Z,-this.maximum.z),this._topPlane=i),i},s.prototype.equals=function(i){return s.equals(this,i)},s.prototype.transformBy=function(i){const t=this.getCornerAry();return t.forEach((t=>{a.Matrix4.multiplyByPoint(i,t,t)})),s.fromPoints(t,this),this},s.prototype.getLength=function(){const i=(this.maximum.x-this.minimum.x)*(this.maximum.x-this.minimum.x),t=(this.maximum.y-this.minimum.y)*(this.maximum.y-this.minimum.y);return Math.sqrt(i+t)},s.prototype.getCornerAry=function(){const i=[];for(let t=0;t<2;++t)for(let n=0;n<2;++n)for(let e=0;e<2;++e){const a=0!==t?this.maximum.x:this.minimum.x,r=0!==n?this.maximum.y:this.minimum.y,s=0!==e?this.maximum.z:this.minimum.z,u=new m.Cartesian3(a,r,s);i.push(u)}return i},s.prototype.addBox=function(i){const t=[this.minimum,this.maximum,i.minimum,i.maximum];s.fromPoints(t,this)},s.prototype.addPt=function(i){const t=[this.minimum,this.maximum,i];s.fromPoints(t,this)},s.prototype.isOverlap=function(i){return!(i.minimum.x-this.maximum.x>1e-6||this.minimum.x-i.maximum.x>1e-6)&&(!(i.minimum.y-this.maximum.y>1e-6||this.minimum.y-i.maximum.y>1e-6)&&!(i.minimum.z-this.maximum.z>1e-6||this.minimum.z-i.maximum.z>1e-6))},s.fromBoundingSphere=function(i,t){let n=new m.Cartesian3(i.x-t,i.y-t,i.z-t),e=new m.Cartesian3(i.x+t,i.y+t,i.z+t);return new s(n,e)},i.AxisAlignedBoundingBox=s}));