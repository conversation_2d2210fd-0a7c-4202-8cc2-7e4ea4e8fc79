/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./Math-b5f4d889","./Rectangle-9bffefe4"],(function(t,e,n,o,a,r){"use strict";const c={SCALAR:"SCALAR",VEC2:"VEC2",VEC3:"VEC3",VEC4:"VEC4",MAT2:"MAT2",MAT3:"MAT3",MAT4:"MAT4",getMathType:function(t){switch(t){case c.SCALAR:return Number;case c.VEC2:return e.Cartesian2;case c.VEC3:return e.Cartesian3;case c.VEC4:return r.Cartesian4;case c.MAT2:return r.Matrix2;case c.MAT3:return r.Matrix3;case c.MAT4:return r.Matrix4}},getNumberOfComponents:function(t){switch(t){case c.SCALAR:return 1;case c.VEC2:return 2;case c.VEC3:return 3;case c.VEC4:case c.MAT2:return 4;case c.MAT3:return 9;case c.MAT4:return 16}},fromNumberOfComponents:function(t){switch(t){case 1:return c.SCALAR;case 2:return c.VEC2;case 3:return c.VEC3;case 4:return c.VEC4;case 9:return c.MAT3;case 16:return c.MAT4}},getAttributeLocationCount:function(t){switch(t){case c.SCALAR:case c.VEC2:case c.VEC3:case c.VEC4:return 1;case c.MAT2:return 2;case c.MAT3:return 3;case c.MAT4:return 4}},getGlslType:function(t){switch(t){case c.SCALAR:return"float";case c.VEC2:return"vec2";case c.VEC3:return"vec3";case c.VEC4:return"vec4";case c.MAT2:return"mat2";case c.MAT3:return"mat3";case c.MAT4:return"mat4"}}};var s=Object.freeze(c);const u=1/256,i={octEncodeInRange:function(t,e,n){if(n.x=t.x/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),n.y=t.y/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),t.z<0){const t=n.x,e=n.y;n.x=(1-Math.abs(e))*a.CesiumMath.signNotZero(t),n.y=(1-Math.abs(t))*a.CesiumMath.signNotZero(e)}return n.x=a.CesiumMath.toSNorm(n.x,e),n.y=a.CesiumMath.toSNorm(n.y,e),n},octEncode:function(t,e){return i.octEncodeInRange(t,255,e)}},C=new e.Cartesian2,f=new Uint8Array(1);function M(t){return f[0]=t,f[0]}i.octEncodeToCartesian4=function(t,e){return i.octEncodeInRange(t,65535,C),e.x=M(C.x*u),e.y=M(C.x),e.z=M(C.y*u),e.w=M(C.y),e},i.octDecodeInRange=function(t,n,o,r){if(r.x=a.CesiumMath.fromSNorm(t,o),r.y=a.CesiumMath.fromSNorm(n,o),r.z=1-(Math.abs(r.x)+Math.abs(r.y)),r.z<0){const t=r.x;r.x=(1-Math.abs(r.y))*a.CesiumMath.signNotZero(t),r.y=(1-Math.abs(t))*a.CesiumMath.signNotZero(r.y)}return e.Cartesian3.normalize(r,r)},i.octDecode=function(t,e,n){return i.octDecodeInRange(t,e,255,n)},i.octDecodeFromCartesian4=function(t,e){const n=256*t.x+t.y,o=256*t.z+t.w;return i.octDecodeInRange(n,o,65535,e)},i.octPackFloat=function(t){return 256*t.x+t.y};const A=new e.Cartesian2;function m(t){return t>>1^-(1&t)}i.octEncodeFloat=function(t){return i.octEncode(t,A),i.octPackFloat(A)},i.octDecodeFloat=function(t,e){const n=t/256,o=Math.floor(n),a=256*(n-o);return i.octDecode(o,a,e)},i.octPack=function(t,e,n,o){const a=i.octEncodeFloat(t),r=i.octEncodeFloat(e),c=i.octEncode(n,A);return o.x=65536*c.x+a,o.y=65536*c.y+r,o},i.octUnpack=function(t,e,n,o){let a=t.x/65536;const r=Math.floor(a),c=65536*(a-r);a=t.y/65536;const s=Math.floor(a),u=65536*(a-s);i.octDecodeFloat(c,e),i.octDecodeFloat(u,n),i.octDecode(r,s,o)},i.compressTextureCoordinates=function(t){return 4096*(4095*t.x|0)+(4095*t.y|0)},i.decompressTextureCoordinates=function(t,e){const n=t/4096,o=Math.floor(n);return e.x=o/4095,e.y=(t-4096*o)/4095,e},i.zigZagDeltaDecode=function(t,e,n){const a=t.length;let r=0,c=0,s=0;for(let u=0;u<a;++u)r+=m(t[u]),c+=m(e[u]),t[u]=r,e[u]=c,o.defined(n)&&(s+=m(n[u]),n[u]=s)},i.dequantize=function(t,e,o,a){const r=s.getNumberOfComponents(o);let c;switch(e){case n.ComponentDatatype.BYTE:c=127;break;case n.ComponentDatatype.UNSIGNED_BYTE:c=255;break;case n.ComponentDatatype.SHORT:c=32767;break;case n.ComponentDatatype.UNSIGNED_SHORT:c=65535;break;case n.ComponentDatatype.INT:c=2147483647;break;case n.ComponentDatatype.UNSIGNED_INT:c=4294967295}const u=new Float32Array(a*r);for(let e=0;e<a;e++)for(let n=0;n<r;n++){const o=e*r+n;u[o]=Math.max(t[o]/c,-1)}return u},i.decodeRGB565=function(t,e){const n=t.length;o.defined(e)||(e=new Float32Array(3*n));const a=1/31;for(let o=0;o<n;o++){const n=t[o],r=n>>11,c=n>>5&63,s=31&n,u=3*o;e[u]=r*a,e[u+1]=.015873015873015872*c,e[u+2]=s*a}return e};var d=i;t.AttributeCompression=d,t.AttributeType=s}));
