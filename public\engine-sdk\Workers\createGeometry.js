define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./AttributeCompression-a01059cd","./GeometryPipeline-f727231c","./EncodedCartesian3-16f34df4","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./PrimitivePipeline-d98b7ff1","./WebMercatorProjection-ab2cf572","./createTaskProcessorWorker"],(function(e,r,t,n,i,a,o,f,s,c,d,u,b,m,p,l,y,v,P,k,C,h){"use strict";var G={};function T(r){var t=G[r];return e.defined(t)||("object"===typeof exports?G[t]=t=require("Workers/"+r):require(["Workers/"+r],(function(e){t=e,G[t]=e}))),t}function W(r,t){for(var n=r.subTasks,i=n.length,a=new Array(i),o=0;o<i;o++){var f=n[o],s=f.geometry,c=f.moduleName;if(e.defined(c)){var d=T(c);a[o]=d(s,f.offset)}else a[o]=s}return e.when.all(a,(function(e){return k.PrimitivePipeline.packCreateGeometryResults(e,t)}))}var g=h(W);return g}));