<!--
 * @Description: 登录页面
 * @Autor: qian
 * @Date: 2023-06-12 17:40:14
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-15 15:02:36
-->
<template>
  <div class="login">
    <!-- 动态背景粒子 -->
    <div class="particles">
      <div class="particle" v-for="i in 30" :key="i"></div>
    </div>
    
    <!-- 背景网格 -->
    <div class="grid-bg"></div>
    
    <div class="container">
      <!-- 标题部分 -->
      <div class="title-section">
        <h1 class="login-title">系统登录</h1>
        <div class="title-line"></div>
      </div>
      
      <!-- 登录表单 -->
      <div class="form-wrapper">
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username" class="form-item">
            <div class="input-wrapper">
              <el-input 
                v-model="loginForm.username" 
                :prefix-icon="User" 
                placeholder="请输入用户名"
                class="cyber-input"
              />
            </div>
          </el-form-item>
          
          <el-form-item prop="password" class="form-item">
            <div class="input-wrapper">
              <el-input 
                v-model="loginForm.password" 
                type="password" 
                show-password 
                :prefix-icon="Lock" 
                placeholder="请输入密码"
                class="cyber-input"
              />
            </div>
          </el-form-item>
          
          <el-form-item class="form-item">
            <el-button 
              type="primary" 
              :loading="loading" 
              @click="handleLogin()"
              class="cyber-button"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
  import { reactive, ref, toRefs } from 'vue'
  import { useRouter } from 'vue-router'

  import { login } from '@/api/login.js'
  import { setToken } from '@/utils/auth'
  import { Lock, User } from '@element-plus/icons-vue'
  import { ElForm, ElMessage } from 'element-plus'

  export default {
    name: 'Login'
  }
</script>
<script setup>
  const router = useRouter()

  const loginFormRef = ref(ElForm)

  const state = reactive({
    loginForm: {
      username: '',
      password: ''
    },
    loginRules: {
      username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
      password: [{ required: true, trigger: 'blur', validator: validatePassword }]
    },
    loading: false
  })
  const { loginForm, loginRules, loading } = toRefs(state)

  // 密码校验
  function validatePassword(rule, value, callback) {
    if (value.length < 6) {
      callback(new Error('密码长度不低于六位'))
    } else if (value.length > 30) {
      callback(new Error('密码长度不高于三十位'))
    } else {
      callback()
    }
  }
  // 点击登录
  function handleLogin() {
    loginFormRef.value.validate((valid) => {
      if (valid) {
        loading.value = true
        login()
          .loginByUsername(state.loginForm)
          .then((res) => {
            if (res.success) {
              debugger
              setToken(res.result)
              router.push({ path: '/' })
              // this.$store.dispatch('setDictMap')
              // this.$store.dispatch('setFileUrl')
            }
            ElMessage({
              message: res.message,
              type: res.success ? 'success' : 'warning'
            })
            loading.value = false
          })
      }
    })
  }
</script>
<style lang="scss" scoped>
  .login {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0c0c0c 100%);
  }

  // 动态背景粒子
  .particles {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ffff;
    border-radius: 50%;
    box-shadow: 0 0 6px #00ffff;
    animation: float 6s infinite ease-in-out;
  }
  .particle:nth-child(1) { top: 23%; left: 67%; animation-duration: 4s; animation-delay: 0s; }
  .particle:nth-child(2) { top: 78%; left: 12%; animation-duration: 5s; animation-delay: 1s; }
  .particle:nth-child(3) { top: 45%; left: 89%; animation-duration: 3s; animation-delay: 2s; }
  .particle:nth-child(4) { top: 90%; left: 34%; animation-duration: 6s; animation-delay: 0.5s; }
  .particle:nth-child(5) { top: 15%; left: 56%; animation-duration: 4s; animation-delay: 1.5s; }
  .particle:nth-child(6) { top: 67%; left: 78%; animation-duration: 5s; animation-delay: 2.5s; }
  .particle:nth-child(7) { top: 34%; left: 23%; animation-duration: 3s; animation-delay: 0.2s; }
  .particle:nth-child(8) { top: 89%; left: 45%; animation-duration: 7s; animation-delay: 1.2s; }
  .particle:nth-child(9) { top: 12%; left: 90%; animation-duration: 4s; animation-delay: 2.2s; }
  .particle:nth-child(10) { top: 56%; left: 15%; animation-duration: 5s; animation-delay: 0.8s; }
  .particle:nth-child(11) { top: 78%; left: 67%; animation-duration: 6s; animation-delay: 1.8s; }
  .particle:nth-child(12) { top: 23%; left: 34%; animation-duration: 3s; animation-delay: 2.8s; }
  .particle:nth-child(13) { top: 45%; left: 89%; animation-duration: 4s; animation-delay: 0.3s; }
  .particle:nth-child(14) { top: 90%; left: 12%; animation-duration: 5s; animation-delay: 1.3s; }
  .particle:nth-child(15) { top: 15%; left: 56%; animation-duration: 7s; animation-delay: 2.3s; }
  .particle:nth-child(16) { top: 67%; left: 78%; animation-duration: 3s; animation-delay: 0.7s; }
  .particle:nth-child(17) { top: 34%; left: 23%; animation-duration: 4s; animation-delay: 1.7s; }
  .particle:nth-child(18) { top: 89%; left: 45%; animation-duration: 6s; animation-delay: 2.7s; }
  .particle:nth-child(19) { top: 12%; left: 90%; animation-duration: 5s; animation-delay: 0.4s; }
  .particle:nth-child(20) { top: 56%; left: 15%; animation-duration: 3s; animation-delay: 1.4s; }
  .particle:nth-child(21) { top: 78%; left: 67%; animation-duration: 7s; animation-delay: 2.4s; }
  .particle:nth-child(22) { top: 23%; left: 34%; animation-duration: 4s; animation-delay: 0.6s; }
  .particle:nth-child(23) { top: 45%; left: 89%; animation-duration: 5s; animation-delay: 1.6s; }
  .particle:nth-child(24) { top: 90%; left: 12%; animation-duration: 6s; animation-delay: 2.6s; }
  .particle:nth-child(25) { top: 15%; left: 56%; animation-duration: 3s; animation-delay: 0.9s; }
  .particle:nth-child(26) { top: 67%; left: 78%; animation-duration: 4s; animation-delay: 1.9s; }
  .particle:nth-child(27) { top: 34%; left: 23%; animation-duration: 7s; animation-delay: 2.9s; }
  .particle:nth-child(28) { top: 89%; left: 45%; animation-duration: 5s; animation-delay: 0.1s; }
  .particle:nth-child(29) { top: 12%; left: 90%; animation-duration: 6s; animation-delay: 1.1s; }
  .particle:nth-child(30) { top: 56%; left: 15%; animation-duration: 4s; animation-delay: 2.1s; }

  @keyframes float {
    0%, 100% {
      opacity: 0.3;
      transform: translateY(0) translateX(0);
    }
    25% {
      opacity: 0.8;
      transform: translateY(-20px) translateX(10px);
    }
    50% {
      opacity: 1;
      transform: translateY(-10px) translateX(-10px);
    }
    75% {
      opacity: 0.6;
      transform: translateY(-30px) translateX(20px);
    }
  }

  // 背景网格
  .grid-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgb(0 255 255 / 30%) 1px, transparent 1px),
      linear-gradient(90deg, rgb(0 255 255 / 30%) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.1;
    animation: grid-move 20s linear infinite;
  }

  @keyframes grid-move {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(50px, 50px);
    }
  }
  .container {
    position: relative;
    z-index: 10;
    width: 650px;
    padding: 60px;
    background: rgb(10 10 30 / 85%);
    backdrop-filter: blur(20px);
    border: 2px solid rgb(0 255 255 / 30%);
    border-radius: 25px;
    box-shadow: 
      0 12px 48px rgb(0 0 0 / 80%),
      inset 0 2px 0 rgb(255 255 255 / 10%),
      0 0 70px rgb(0 255 255 / 20%);
    animation: container-glow 2s ease-in-out infinite alternate;
  }

  @keyframes container-glow {
    0% {
      box-shadow: 
        0 8px 32px rgb(0 0 0 / 80%),
        inset 0 1px 0 rgb(255 255 255 / 10%),
        0 0 50px rgb(0 255 255 / 20%);
    }
    100% {
      box-shadow: 
        0 8px 32px rgb(0 0 0 / 80%),
        inset 0 1px 0 rgb(255 255 255 / 10%),
        0 0 60px rgb(0 255 255 / 40%);
    }
  }
  .title-section {
    margin-bottom: 50px;
    text-align: center;
  }
  .login-title {
    margin: 0 0 20px;
    font-size: 48px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 25px rgb(0 255 255 / 60%);
    background: linear-gradient(45deg, #ffffff, #00ffff);
    background-clip: text;
    animation: title-pulse 3s ease-in-out infinite;
    -webkit-text-fill-color: transparent;
  } 

  @keyframes title-pulse {
    0%, 100% {
      text-shadow: 0 0 20px rgb(0 255 255 / 60%);
    }
    50% {
      text-shadow: 0 0 30px rgb(0 255 255 / 90%);
    }
  }
  .title-line {
    position: relative;
    width: 150px;
    height: 3px;
    margin: 0 auto;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      content: '';
      background: #00ffff;
      border-radius: 50%;
      box-shadow: 0 0 15px #00ffff;
      transform: translate(-50%, -50%);
    }
  }
  .form-wrapper {
    margin-top: 40px;
  }
  .login-form {
    .form-item {
      margin-bottom: 35px;
    }
  }
  .input-wrapper {
    position: relative;
    &::before {
      position: absolute;
      inset: 0;
      z-index: -1;
      content: '';
      background: linear-gradient(45deg, transparent, rgb(0 255 255 / 10%), transparent);
      border-radius: 8px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    &:hover::before {
      opacity: 1;
    }
  }
  :deep(.cyber-input) {
    .el-input__wrapper {
      background: rgb(15 15 40 / 80%);
      border: 1px solid rgb(0 255 255 / 30%);
      border-radius: 8px;
      box-shadow: inset 0 2px 10px rgb(0 0 0 / 30%);
      transition: all 0.3s ease;
      &:hover {
        border-color: rgb(0 255 255 / 60%);
        box-shadow: 
          inset 0 2px 10px rgb(0 0 0 / 30%),
          0 0 15px rgb(0 255 255 / 30%);
      }
      &.is-focus {
        border-color: #00ffff;
        box-shadow: 
          inset 0 2px 10px rgb(0 0 0 / 30%),
          0 0 20px rgb(0 255 255 / 50%);
      }
    }
    .el-input__inner {
      height: 55px;
      font-size: 18px;
      line-height: 55px;
      color: #ffffff;
      &::placeholder {
        color: rgb(255 255 255 / 50%);
      }
    }
    .el-input__prefix-inner {
      color: #00ffff;
    }
    .el-input__suffix-inner {
      color: #00ffff;
    }
  }
  :deep(.cyber-button) {
    position: relative;
    width: 100%;
    height: 60px;
    overflow: hidden;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    letter-spacing: 1px;
    background: linear-gradient(135deg, rgb(0 100 200 / 80%), rgb(0 255 255 / 60%));
    border: 2px solid #00ffff;
    border-radius: 10px;
    transition: all 0.3s ease;
    &::before {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      content: '';
      background: linear-gradient(90deg, transparent, rgb(255 255 255 / 30%), transparent);
      transition: left 0.5s ease;
    }
    &:hover {
      box-shadow: 
        0 8px 25px rgb(0 255 255 / 40%),
        inset 0 1px 0 rgb(255 255 255 / 20%);
      transform: translateY(-2px);
      &::before {
        left: 100%;
      }
    }
    &:active {
      transform: translateY(0);
    }
    &.is-loading {
      background: linear-gradient(135deg, rgb(100 100 100 / 80%), rgb(150 150 150 / 60%));
      border-color: rgb(255 255 255 / 30%);
    }
  }

  // 响应式设计
  @media (width <= 768px) {
    .container {
      width: 90%;
      max-width: 500px;
      padding: 40px 30px;
    }
    .login-title {
      font-size: 36px;
    }
  }
</style>
