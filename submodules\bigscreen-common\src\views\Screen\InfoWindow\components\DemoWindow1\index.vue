<!--
 * @Description:
 * @Autor: wangjialing
 * @Date: 2023-06-28 13:59:33
 * @LastEditors: wangjialing
 * @LastEditTime: 2023-06-28 18:03:13
-->
<template>
  <div>
    弹框2   <el-button type='primary'>测试</el-button>{{ storeScreenPopWindowData.testNum }}
  </div>
</template>

<script setup>
  import useStore from '@Common/store'
  import { ElButton } from 'element-plus'
  const { storeScreenPopWindowData } = useStore()
</script>

<style lang="scss" scoped>

</style>
