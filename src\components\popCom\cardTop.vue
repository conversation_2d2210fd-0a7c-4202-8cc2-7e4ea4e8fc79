<template>
  <div class="event-card-top">
    <div class="card-top">
      <div class="card-top-left">
        <span class="top-left-img"></span>
        <span class="top-left-text">
          {{ title }}
        </span>
        <span
          v-if="leftBtnText"
          class="top-left-btn"
          :style="{ background: leftBtnColor, border: '1px solid ' + leftBtnBorderColor, color: leftBtnBorderColor }">
          <span class="btn-text">{{ leftBtnText }}</span>
          <!-- <span class="btn-icon"></span> -->
        </span>
      </div>
      <span v-if="isClose" class="close-btn" @click="handleCloseClick">
        <img src="@/assets/images/pop/closePop.png" />
      </span>
      <div v-show="btnText" :class="`card-top-right ${btnType}`">
        {{ btnText }}
      </div>
    </div>
    <img class="bg1" src="@/assets/images/pop/titleBg.png" />
  </div>
</template>

<script setup>
  import { provideTools } from '@/utils/provideMap.js'
  defineProps({
    title: {
      // 标题
      type: String,
      default: ''
    },
    leftBtnColor: {
      type: String,
      default: 'rgba(0, 180, 255, 0.15)'
    },
    leftBtnBorderColor: {
      type: String,
      default: '#00beff'
    },
    btnText: {
      // 右侧按钮文字
      type: String,
      default: ''
    },
    btnType: {
      // success warning
      type: String,
      default: 'success'
    },
    leftBtnText: {
      // 左侧按钮
      type: String,
      default: ''
    },
    isClose: {
      type: Boolean,
      default: false
    }
  })
  const emits = defineEmits(['handleClose'])
  // const handleClose = inject('handleClose')
  const handleClose = provideTools.handleClose.inject()

  const handleCloseClick = () => {
    emits('handleClose')
    handleClose()
  }
</script>

<style lang="scss" scoped>
  .event-card-top {
    // width: 240px;
    position: relative;
    width: 100%;
    height: 40px;
    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      padding: 0 12px 0 16px;
      background: linear-gradient(90deg, rgb(0 141 255 / 25%) 0%, rgb(0 141 255 / 15%) 100%);
    }
    .bg1 {
      position: absolute;
      right: 5px;
      bottom: 0;
    }
    .card-top-left {
      > span {
        display: inline-block;
        vertical-align: middle;
      }
      .top-left-img {
        width: 9px;
        height: 17px;

        // border: 1px solid red;
        // background: #00BEFF;
        // box-shadow: 2px 3px 5px 0px #000E2C;
        background: url('../../assets/images/pop/arrowRight.png') no-repeat center;
      }
      .top-left-text {
        padding-left: 8px;
        font-family: Alibaba-PuHuiTi-M, Alibaba-PuHuiTi;
        font-size: 18px;
        font-weight: normal;
        line-height: 25px;
        color: #ffffff;
      }
      .top-left-btn {
        height: 26px;
        margin-left: 30px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-size: 16px;
        font-weight: normal;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
        border-radius: 12px;
        .btn-text {
          padding: 0 5px;
        }
        .btn-icon {
          display: inline-block;
          width: 14px;
          height: 11px;
          border: 1px solid red;
        }
      }
    }
    .card-top-right {
      &.warning {
        width: 57px;
        height: 16px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-size: 12px;
        font-weight: normal;
        line-height: 14px;
        color: #ffb500;
        text-align: center;
        text-shadow: 0 1px 2px #053873;
        background: rgb(255 181 0 / 20%);
        border: 1px solid #ffb500;
        border-radius: 8px;
      }
      &.success {
        width: 57px;
        height: 16px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-size: 12px;
        font-weight: normal;
        line-height: 14px;
        color: #00ff00;
        text-align: center;
        text-shadow: 0 1px 2px #053873;
        background: rgb(0 255 0 / 20%);
        border: 1px solid #00ff00;
        border-radius: 8px;
      }
    }

    // 关闭
    .close-btn {
      z-index: 999;
      width: 16px;
      height: 16px;

      // background: linear-gradient(180deg, #4574bc 0%, #193452 100%);
      // box-shadow: 0px 2px 4px 0px #000c2f;
      // border-radius: 2px;
      // border: 1px solid;
      // border-image: linear-gradient(180deg, rgba(139, 184, 255, 1), rgba(84, 144, 255, 1)) 1 1;
      cursor: pointer;
    }
  }
</style>
