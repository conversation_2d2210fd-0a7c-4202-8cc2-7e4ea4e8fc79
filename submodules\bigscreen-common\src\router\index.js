import { createRouter, createWebHashHistory } from 'vue-router'

import CesiumControllerDemo from '@Common/views/CesiumDemo/CesiumControllerDemo/index.vue'
import DirectiveDemo from '@Common/views/DirectiveDemo/index.vue'
import DirectiveTest from '@Common/views/DirectiveTest/index.vue'
import EchartsDemo from '@Common/views/EchartsDemo/index.vue'
import Screen from '@Common/views/Screen/index.vue'
import Temp from '@Common/views/Temp/index.vue'
import VueUseDemo from '@Common/views/VueUseDemo/index.vue'

const routes = [
  {
    path: '/',
    name: 'Screen',
    component: Screen
  },
  {
    path: '/EchartsDemo',
    name: 'EchartsDemo',
    component: EchartsDemo
  },
  {
    path: '/VueUseDemo',
    name: 'VueUseDemo',
    component: VueUseDemo
  },
  {
    path: '/DirectiveDemo',
    name: 'DirectiveDemo',
    component: DirectiveDemo
  },
  {
    path: '/DirectiveTest',
    name: 'DirectiveTest',
    component: DirectiveTest
  },
  {
    path: '/Temp',
    name: 'Temp',
    component: Temp
  },
  {
    path: '/CesiumDemo/CesiumControllerDemo',
    name: 'CesiumControllerDemo',
    component: CesiumControllerDemo
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
