import lib from '@/utils/lib'
import { getAllPopWindows, closePopWindowByTag, PopWindowProps, usePopWindow } from 'znyg-frontend-common'

const { showPopWindow } = usePopWindow()
import ImageZoom from '@/views/Screen/PopupWindow/ImageZoom/index.vue'
/**
 * @description: 获取某个文件夹下的所有图片
 * @param {Objcect} iconModules 异步迭代器对象
 * @return {Objcect} 图片路径对象
 */
export function getIconData(iconModules) {
  const iconData = {}
  const keys = Object.keys(iconModules)
  for (const key of keys) {
    const iconName = key
      .split('/')
      .pop()
      .replace(/\.\w+$/, '')
    iconData[iconName] = iconModules[key].default
  }
  return iconData
}

/**
 * @description: 导入某个文件夹下的所有组件
 * @param {Objcect} modulesFiles 异步迭代器对象
 * @param {Objcect} app 当前组件的vue实例
 */
export function setComponents(modulesFiles, app) {
  const components = {}
  const keys = Object.keys(modulesFiles)
  for (const key of keys) {
    const splitArr = key.split('/')
    const componentName = splitArr[splitArr.length - 2]
    components[componentName] = modulesFiles[key].default
  }
  app.components = { ...app.components, ...components }
}

/**
 * @description: 获取assets静态资源
 * @param {string} assets文件夹下的路径
 */
export const getAssetsFile = (url) => {
  return new URL(`../assets/${url}`, import.meta.url).href
}

// 生成从minNum到maxNum的随机数
export function randomNum(minNum: number, maxNum?: number) {
  switch (arguments.length) {
    case 1:
      return parseInt((Math.random() * minNum + 1).toString(), 10)
      break
    case 2:
      return parseInt((Math.random() * (maxNum! - minNum + 1) + minNum).toString(), 10)
      break
    default:
      return 0
      break
  }
}

/**
 * @description: 对数组进行分组
 * @param {*} array 原数组
 * @param {*} groupSize 每组几个
 * @return {*} 分好组的数组
 */
export function groupArray(array, groupSize) {
  const groups = []
  for (let i = 0; i < array.length; i += groupSize) {
    groups.push(array.slice(i, i + groupSize))
  }
  return groups
}

/**
 * @description: 当小数位数为0时，显示整数位  否则保留两位小数
 */
export function formatNumber(number) {
  const roundedNumber = Math.round(number * 100) / 100 // 保留两位小数并四舍五入

  if (Number.isInteger(roundedNumber)) {
    return roundedNumber.toString() // 如果是整数，直接返回整数位
  } else {
    return roundedNumber.toFixed(2) // 如果有小数位，保留两位小数
  }
}
export const formatNumberText = (number: number) => {
  if (number >= 10000) {
    return (number / 10000).toFixed(1) + '万'
  }
  return number.toString()
}

/**
 * 把 assets 下的图片文件转换成 Base64，图片路径从 assets 开始
 */
export const convertImageToBase64 = (relativePath) => {
  return new Promise((resolve, reject) => {
    try {
      // 使用 import 动态导入图片
      const imageUrl = new URL(`../assets/${relativePath}`, import.meta.url).href

      // 创建 Image 对象
      const img = new Image()
      img.src = imageUrl

      img.onload = () => {
        // 创建 Canvas 元素
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        // 设置 Canvas 尺寸
        canvas.width = img.width
        canvas.height = img.height

        // 将图像绘制到 Canvas 上
        ctx.drawImage(img, 0, 0)

        // 获取 Base64 数据并解析为 PNG
        const base64Data = canvas.toDataURL('image/png')
        resolve(base64Data)
      }

      img.onerror = (error) => {
        reject(new Error('Image loading failed: ' + error))
      }
    } catch (error) {
      console.error('Error converting image to Base64:', error)
      reject(error)
    }
  })
}

/**
 * 获取随机字符串
 * @param {*} length 最大为10
 * @returns string
 */
export function getRandomString(length) {
  return Math.random()
    .toString(36)
    .slice(2, 2 + length)
}

/** 通过code获取BIM信息 */
export const getBimInfoByCode = async (code) => {
  let bimInfo = null
  if (lib.codeBimInfoMap.has(code)) {
    bimInfo = lib.codeBimInfoMap.get(code)
  } else {
    bimInfo = await lib._engineController.getBimInfoByCode(code)
    lib.codeBimInfoMap.set(code, bimInfo)
  }
  log.success(code, bimInfo)
  return bimInfo
}

export const getBimInfoByCodeList = async (codeList) => {
  const bimInfoList = await lib._engineController.getBimInfoByCodeList(codeList, lib.codeBimInfoMap)
  return bimInfoList
}

/**给AI窗口传问题 */
export const sendQuestionToAIChat = (moduleName) => {
  // const allWindows = getAllPopWindows()
  // console.log(allWindows)
  // if (allWindows.some((_) => _.tag === lib.enumsList.widows.Ai对话框)) {
  //   lib.bus.busAIChat.emit(question)
  // } else {
  //   //还没打开Ai窗口，先打开
  //   lib.bus.busAIButtons.emit(true)
  //   // await nextTick()
  //   lib.bus.busAIChat.emit(question)
  // }
  const question = `${moduleName}模块应该怎么用，结合说明知识库，帮我进行详细的介绍和功能分析`
  lib.bus.busAIChat.emit(question)
}
// 放大图片弹窗
export const openImageZoom = (imageUrls, imgName = '') => {
  closePopWindowByTag(lib.enumMap.PopWindowTag.图片弹窗)
  // 图片弹窗
  const op: PopWindowProps = {
    left: 1816,
    top: 330,
    tag: lib.enumMap.PopWindowTag.图片弹窗,
    // zIndex: 999,
    draggable: true
  }
  showPopWindow(op, ImageZoom, { picList: imageUrls, imgName })
}

export const executeCode = async (code, BimEngine, viewer, project, lib) => {
  // const BimEngine = window.BimEngine
  // const viewer = lib._engineController.viewer
  // const project = lib._engineController.currentProject
  try {
    // 处理换行符
    let wrappedCode = code.replace(/\\\\n/g, '\n').replace(/\\n/g, '\n')

    console.log('准备执行的代码:', wrappedCode)

    // 检查代码是否包含await，如果包含则用async函数包装
    if (wrappedCode.includes('await')) {
      // 创建异步函数来执行包含await的代码
      const asyncFunc = new Function(
        'BimEngine',
        'viewer',
        'project',
        'lib',
        `
        return (async () => {
          ${wrappedCode}
        })();
      `
      )

      // 执行异步函数
      await asyncFunc(BimEngine, viewer, project, lib)
    } else {
      // 对于不包含await的代码，使用eval直接执行
      const syncFunc = new Function('BimEngine', 'viewer', 'project', 'lib', wrappedCode)
      syncFunc(BimEngine, viewer, project, lib)
    }

    console.log('✅ 代码执行成功')
  } catch (error) {
    console.error('❌ 代码执行失败:', error.message)
    throw error
  }
}
// 自定义函数，实现中间省略
/**
 * @description: 实现字符串中间省略功能，当字符串长度超过指定最大长度时，在字符串中间添加省略号。
 * @param {string} text 需要处理的原始字符串。
 * @param {number} maxLength 允许的最大字符串长度。
 * @return {string} 处理后的字符串，如果原始字符串长度不超过最大长度，则返回原始字符串；否则返回中间添加省略号的字符串。
 */
export const getMiddleEllipsis = (text: string, maxLength: number) => {
  // 如果字符串长度小于等于最大长度，直接返回原始字符串
  if (text.length <= maxLength) {
    return text
  }
  // 计算需要保留的字符串起始部分的长度
  const start = Math.ceil((maxLength - 3) / 2)
  // 计算需要保留的字符串结束部分的起始索引
  const end = text.length - Math.floor((maxLength - 3) / 2)
  // 拼接起始部分、省略号和结束部分并返回
  return text.slice(0, start) + '......' + text.slice(end)
}

export const getDictNameByCode = (code, parentCode) => {
  const { storeDictionary } = lib.store()
  return storeDictionary.dictMapQJ[parentCode].find((_) => _.code === code)?.name || ''
}
