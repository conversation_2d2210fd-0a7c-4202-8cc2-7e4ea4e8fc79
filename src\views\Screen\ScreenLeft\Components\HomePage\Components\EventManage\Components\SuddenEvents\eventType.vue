<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 15:15:27
 * @LastEditors: wmf '<EMAIL>'
 * @LastEditTime: 2025-06-12 15:14:41
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\SuddenEvents\eventType.vue
 * @Description:
 *
-->
<template>
  <div class="eventType-container">
    <SubHeadLine>事件类型</SubHeadLine>
    <!-- <pieChart :chartData="props.eventTypeList" :chartTitle="'类型'" :chartUnit="'%'" type="eventType"></pieChart> -->
    <ThreePie :data="props.eventTypeList" :chartTitle="'类型'" :chartUnit="'%'" type="eventType"></ThreePie>
  </div>
</template>

<script setup>
  import pieChart from '../EmergencyResources/pieChart.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  import ThreePie from '../EmergencyResources/threePie.vue'
  const props = defineProps({
    eventTypeList: {
      type: Array,
      default: () => []
    }
  })

  // const chartData = ref([
  //   { value: 25, name: '单车事故' },
  //   { value: 45, name: '单车抛锚' },
  //   { value: 20, name: '两车事故' },
  //   { value: 10, name: '其他' }
  // ])
</script>

<style lang="scss" scoped>
  .eventType-container {
    width: 288px;
    height: 180px;
  }
</style>
