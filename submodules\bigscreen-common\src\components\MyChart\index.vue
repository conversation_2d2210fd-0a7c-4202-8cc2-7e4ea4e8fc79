<template>
  <div class="my-chart-container">
    <div ref="chartRef" class="chart-div" :style="{ width: props.width, height: props.height }"></div>
  </div>
</template>

<script>
  export default {
    name: 'My<PERSON><PERSON>'
  }
</script>

<script setup>
  import { markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue'
  import * as echarts from 'echarts'

  const props = defineProps({
    option: {
      type: Object,
      default: () => {
        return {
          xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              data: [150, 230, 224, 218, 135, 147, 260],
              type: 'line'
            }
          ]
        }
      }
    },
    width: {
      type: String,
      default: '500px'
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  })

  const chartRef = ref(null)
  const chartInstance = ref()

  // 绘制
  const draw = () => {
    if (chartInstance.value && props.option) {
      chartInstance.value.setOption(props.option, true)
    }
  }

  const init = () => {
    if (!chartRef.value) return

    // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
    chartInstance.value = echarts.getInstanceByDom(chartRef.value)
    if (!chartInstance.value) {
      chartInstance.value = markRaw(echarts.init(chartRef.value))
      draw()
    }
  }

  // 对父组件暴露获取Echarts的实例
  defineExpose({
    getInstance: () => chartInstance.value,
    draw
  })

  watch(
    () => props.option,
    () => {
      draw()
    },
    {
      deep: true,
      immediate: true
    }
  )
  watch(
    () => props.loading,
    (loading) => {
      if (loading) {
        chartInstance.value.showLoading()
      } else {
        chartInstance.value.hideLoading()
      }
    }
  )

  onMounted(() => {
    init()
  })

  onBeforeUnmount(() => {
    // 容器被销毁之后，销毁实例，避免内存泄漏
    chartInstance.value?.dispose()
  })
</script>

<style lang="scss" scoped>
  .my-chart-container {
    .chart-div {
    }
  }
</style>
