define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4"],(function(t,i,e,a,n){"use strict";function s(t,i,e){if(0===t)return i*e;var a=t*t,n=a*a,s=n*a,h=s*a,u=h*a,r=u*a,d=e,o=Math.sin(2*d),l=Math.sin(4*d),c=Math.sin(6*d),M=Math.sin(8*d),m=Math.sin(10*d),_=Math.sin(12*d);return i*((1-a/4-3*n/64-5*s/256-175*h/16384-441*u/65536-4851*r/1048576)*d-(3*a/8+3*n/32+45*s/1024+105*h/4096+2205*u/131072+6237*r/524288)*o+(15*n/256+45*s/1024+525*h/16384+1575*u/65536+155925*r/8388608)*l-(35*s/3072+175*h/12288+3675*u/262144+13475*r/1048576)*c+(315*h/131072+2205*u/524288+43659*r/8388608)*M-(693*u/1310720+6237*r/5242880)*m+1001*r/8388608*_)}function h(t,i,e){var a=t/e;if(0===i)return a;var n=a*a,s=n*a,h=s*a,u=i,r=u*u,d=r*r,o=d*r,l=o*r,c=l*r,M=c*r,m=Math.sin(2*a),_=Math.cos(2*a),g=Math.sin(4*a),f=Math.cos(4*a),p=Math.sin(6*a),C=Math.cos(6*a),v=Math.sin(8*a),P=Math.cos(8*a),O=Math.sin(10*a),E=Math.cos(10*a),I=Math.sin(12*a);return a+a*r/4+7*a*d/64+15*a*o/256+579*a*l/16384+1515*a*c/65536+16837*a*M/1048576+(3*a*d/16+45*a*o/256-a*(32*n-561)*l/4096-a*(232*n-1677)*c/16384+a*(399985-90560*n+512*h)*M/5242880)*_+(21*a*o/256+483*a*l/4096-a*(224*n-1969)*c/16384-a*(33152*n-112599)*M/1048576)*f+(151*a*l/4096+4681*a*c/65536+1479*a*M/16384-453*s*M/32768)*C+(1097*a*c/65536+42783*a*M/1048576)*P+8011*a*M/1048576*E+(3*r/8+3*d/16+213*o/2048-3*n*o/64+255*l/4096-33*n*l/512+20861*c/524288-33*n*c/512+h*c/1024+28273*M/1048576-471*n*M/8192+9*h*M/4096)*m+(21*d/256+21*o/256+533*l/8192-21*n*l/512+197*c/4096-315*n*c/4096+584039*M/16777216-12517*n*M/131072+7*h*M/2048)*g+(151*o/6144+151*l/4096+5019*c/131072-453*n*c/16384+26965*M/786432-8607*n*M/131072)*p+(1097*l/131072+1097*c/65536+225797*M/10485760-1097*n*M/65536)*v+(8011*c/2621440+8011*M/1048576)*O+293393*M/251658240*I}function u(t,i){if(0===t)return Math.log(Math.tan(.5*(a.CesiumMath.PI_OVER_TWO+i)));var e=t*Math.sin(i);return Math.log(Math.tan(.5*(a.CesiumMath.PI_OVER_TWO+i)))-t/2*Math.log((1+e)/(1-e))}function r(t,i,e,n,s){var h=u(t._ellipticity,e),r=u(t._ellipticity,s);return Math.atan2(a.CesiumMath.negativePiToPi(n-i),r-h)}function d(t,i,e,n,h,u,r){var d=t._heading,o=u-n,l=0;if(a.CesiumMath.equalsEpsilon(Math.abs(d),a.CesiumMath.PI_OVER_TWO,a.CesiumMath.EPSILON8))if(i===e)l=i*Math.cos(h)*a.CesiumMath.negativePiToPi(o);else{var c=Math.sin(h);l=i*Math.cos(h)*a.CesiumMath.negativePiToPi(o)/Math.sqrt(1-t._ellipticitySquared*c*c)}else{var M=s(t._ellipticity,i,h),m=s(t._ellipticity,i,r);l=(m-M)/Math.cos(d)}return Math.abs(l)}var o=new a.Cartesian3,l=new a.Cartesian3;function c(t,i,s,h){var u=a.Cartesian3.normalize(h.cartographicToCartesian(i,l),o),c=a.Cartesian3.normalize(h.cartographicToCartesian(s,l),l);e.Check.typeOf.number.greaterThanOrEquals("value",Math.abs(Math.abs(a.Cartesian3.angleBetween(u,c))-Math.PI),.0125);var M=h.maximumRadius,m=h.minimumRadius,_=M*M,g=m*m;t._ellipticitySquared=(_-g)/_,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=n.Cartographic.clone(i,t._start),t._start.height=0,t._end=n.Cartographic.clone(s,t._end),t._end.height=0,t._heading=r(t,i.longitude,i.latitude,s.longitude,s.latitude),t._distance=d(t,h.maximumRadius,h.minimumRadius,i.longitude,i.latitude,s.longitude,s.latitude)}function M(t,e,r,d,o,l){var c,M,m,_=o*o;if(Math.abs(a.CesiumMath.PI_OVER_TWO-Math.abs(e))>a.CesiumMath.EPSILON8){var g=s(o,d,t.latitude),f=r*Math.cos(e),p=g+f;M=h(p,o,d);var C=u(o,t.latitude),v=u(o,M);m=Math.tan(e)*(v-C),c=a.CesiumMath.negativePiToPi(t.longitude+m)}else{var P;if(M=t.latitude,0===o)P=d*Math.cos(t.latitude);else{var O=Math.sin(t.latitude);P=d*Math.cos(t.latitude)/Math.sqrt(1-_*O*O)}m=r/P,c=e>0?a.CesiumMath.negativePiToPi(t.longitude+m):a.CesiumMath.negativePiToPi(t.longitude-m)}return i.defined(l)?(l.longitude=c,l.latitude=M,l.height=0,l):new n.Cartographic(c,M,0)}function m(t,e,a){var s=i.defaultValue(a,n.Ellipsoid.WGS84);this._ellipsoid=s,this._start=new n.Cartographic,this._end=new n.Cartographic,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,i.defined(t)&&i.defined(e)&&c(this,t,e,s)}Object.defineProperties(m.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return e.Check.defined("distance",this._distance),this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return e.Check.defined("distance",this._distance),this._heading}}}),m.fromStartHeadingDistance=function(t,s,h,u,r){e.Check.defined("start",t),e.Check.defined("heading",s),e.Check.defined("distance",h),e.Check.typeOf.number.greaterThan("distance",h,0);var d=i.defaultValue(u,n.Ellipsoid.WGS84),o=d.maximumRadius,l=d.minimumRadius,c=o*o,_=l*l,g=Math.sqrt((c-_)/c);s=a.CesiumMath.negativePiToPi(s);var f=M(t,s,h,d.maximumRadius,g);return!i.defined(r)||i.defined(u)&&!u.equals(r.ellipsoid)?new m(t,f,d):(r.setEndPoints(t,f),r)},m.prototype.setEndPoints=function(t,i){e.Check.defined("start",t),e.Check.defined("end",i),c(this,t,i,this._ellipsoid)},m.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},m.prototype.interpolateUsingSurfaceDistance=function(t,a){if(e.Check.typeOf.number("distance",t),!i.defined(this._distance)||0===this._distance)throw new e.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");return M(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,a)},m.prototype.findIntersectionWithLongitude=function(t,s){if(e.Check.typeOf.number("intersectionLongitude",t),!i.defined(this._distance)||0===this._distance)throw new e.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");var h=this._ellipticity,u=this._heading,r=Math.abs(u),d=this._start;if(t=a.CesiumMath.negativePiToPi(t),a.CesiumMath.equalsEpsilon(Math.abs(t),Math.PI,a.CesiumMath.EPSILON14)&&(t=a.CesiumMath.sign(d.longitude)*Math.PI),i.defined(s)||(s=new n.Cartographic),Math.abs(a.CesiumMath.PI_OVER_TWO-r)<=a.CesiumMath.EPSILON8)return s.longitude=t,s.latitude=d.latitude,s.height=0,s;if(a.CesiumMath.equalsEpsilon(Math.abs(a.CesiumMath.PI_OVER_TWO-r),a.CesiumMath.PI_OVER_TWO,a.CesiumMath.EPSILON8)){if(a.CesiumMath.equalsEpsilon(t,d.longitude,a.CesiumMath.EPSILON12))return;return s.longitude=t,s.latitude=a.CesiumMath.PI_OVER_TWO*a.CesiumMath.sign(a.CesiumMath.PI_OVER_TWO-u),s.height=0,s}var o,l=d.latitude,c=h*Math.sin(l),M=Math.tan(.5*(a.CesiumMath.PI_OVER_TWO+l))*Math.exp((t-d.longitude)/Math.tan(u)),m=(1+c)/(1-c),_=d.latitude;do{o=_;var g=h*Math.sin(o),f=(1+g)/(1-g);_=2*Math.atan(M*Math.pow(f/m,h/2))-a.CesiumMath.PI_OVER_TWO}while(!a.CesiumMath.equalsEpsilon(_,o,a.CesiumMath.EPSILON12));return s.longitude=t,s.latitude=_,s.height=0,s},m.prototype.findIntersectionWithLatitude=function(t,s){if(e.Check.typeOf.number("intersectionLatitude",t),!i.defined(this._distance)||0===this._distance)throw new e.DeveloperError("EllipsoidRhumbLine must have distinct start and end set.");var h=this._ellipticity,r=this._heading,d=this._start;if(!a.CesiumMath.equalsEpsilon(Math.abs(r),a.CesiumMath.PI_OVER_TWO,a.CesiumMath.EPSILON8)){var o=u(h,d.latitude),l=u(h,t),c=Math.tan(r)*(l-o),M=a.CesiumMath.negativePiToPi(d.longitude+c);return i.defined(s)?(s.longitude=M,s.latitude=t,s.height=0,s):new n.Cartographic(M,t,0)}},t.EllipsoidRhumbLine=m}));