define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./IntersectionTests-5ad222fe","./Plane-1b1689fd","./VertexFormat-9b18d410","./arrayRemoveDuplicates-7c710eac","./ArcType-10662e8b","./EllipsoidRhumbLine-f49ff2c9","./EllipsoidGeodesic-5ac97652","./PolylinePipeline-6bd12257","./Color-eff039bb"],(function(e,t,r,o,a,n,i,l,s,p,d,c,u,y,f,h,m,v,C,g,w,E,b){"use strict";var _=[];function A(e,t,r,o,a){var n,i=_;i.length=a;var l=r.red,s=r.green,p=r.blue,d=r.alpha,c=o.red,u=o.green,y=o.blue,f=o.alpha;if(b.Color.equals(r,o)){for(n=0;n<a;n++)i[n]=b.Color.clone(r);return i}var h=(c-l)/a,m=(u-s)/a,v=(y-p)/a,C=(f-d)/a;for(n=0;n<a;n++)i[n]=new b.Color(l+n*h,s+n*m,p+n*v,d+n*C);return i}function P(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var n=a.positions,i=a.colors,l=e.defaultValue(a.width,1),s=e.defaultValue(a.colorsPerVertex,!1);if(!e.defined(n)||n.length<2)throw new t.DeveloperError("At least two positions are required.");if("number"!==typeof l)throw new t.DeveloperError("width must be a number");if(e.defined(i)&&(s&&i.length<n.length||!s&&i.length<n.length-1))throw new t.DeveloperError("colors has an invalid length.");this._positions=n,this._colors=i,this._width=l,this._colorsPerVertex=s,this._vertexFormat=m.VertexFormat.clone(e.defaultValue(a.vertexFormat,m.VertexFormat.DEFAULT)),this._arcType=e.defaultValue(a.arcType,C.ArcType.GEODESIC),this._granularity=e.defaultValue(a.granularity,r.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=o.Ellipsoid.clone(e.defaultValue(a.ellipsoid,o.Ellipsoid.WGS84)),this._workerName="createPolylineGeometry";var p=1+n.length*r.Cartesian3.packedLength;p+=e.defined(i)?1+i.length*b.Color.packedLength:1,this.packedLength=p+o.Ellipsoid.packedLength+m.VertexFormat.packedLength+4}P.pack=function(a,n,i){if(!e.defined(a))throw new t.DeveloperError("value is required");if(!e.defined(n))throw new t.DeveloperError("array is required");var l;i=e.defaultValue(i,0);var s=a._positions,p=s.length;for(n[i++]=p,l=0;l<p;++l,i+=r.Cartesian3.packedLength)r.Cartesian3.pack(s[l],n,i);var d=a._colors;for(p=e.defined(d)?d.length:0,n[i++]=p,l=0;l<p;++l,i+=b.Color.packedLength)b.Color.pack(d[l],n,i);return o.Ellipsoid.pack(a._ellipsoid,n,i),i+=o.Ellipsoid.packedLength,m.VertexFormat.pack(a._vertexFormat,n,i),i+=m.VertexFormat.packedLength,n[i++]=a._width,n[i++]=a._colorsPerVertex?1:0,n[i++]=a._arcType,n[i]=a._granularity,n};var D=o.Ellipsoid.clone(o.Ellipsoid.UNIT_SPHERE),T=new m.VertexFormat,x={positions:void 0,colors:void 0,ellipsoid:D,vertexFormat:T,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};P.unpack=function(a,n,i){if(!e.defined(a))throw new t.DeveloperError("array is required");var l;n=e.defaultValue(n,0);var s=a[n++],p=new Array(s);for(l=0;l<s;++l,n+=r.Cartesian3.packedLength)p[l]=r.Cartesian3.unpack(a,n);s=a[n++];var d=s>0?new Array(s):void 0;for(l=0;l<s;++l,n+=b.Color.packedLength)d[l]=b.Color.unpack(a,n);var c=o.Ellipsoid.unpack(a,n,D);n+=o.Ellipsoid.packedLength;var u=m.VertexFormat.unpack(a,n,T);n+=m.VertexFormat.packedLength;var y=a[n++],f=1===a[n++],h=a[n++],v=a[n];return e.defined(i)?(i._positions=p,i._colors=d,i._ellipsoid=o.Ellipsoid.clone(c,i._ellipsoid),i._vertexFormat=m.VertexFormat.clone(u,i._vertexFormat),i._width=y,i._colorsPerVertex=f,i._arcType=h,i._granularity=v,i):(x.positions=p,x.colors=d,x.width=y,x.colorsPerVertex=f,x.arcType=h,x.granularity=v,new P(x))};var k=new r.Cartesian3,V=new r.Cartesian3,L=new r.Cartesian3,F=new r.Cartesian3;function G(t,r){return e.defined(r)&&(t=P.unpack(t,r)),t._ellipsoid=o.Ellipsoid.clone(t._ellipsoid),P.createGeometry(t)}return P.createGeometry=function(t){var o,n,i,l=t._width,s=t._vertexFormat,f=t._colors,h=t._colorsPerVertex,m=t._arcType,g=t._granularity,w=t._ellipsoid,P=v.arrayRemoveDuplicates(t._positions,r.Cartesian3.equalsEpsilon),D=P.length;if(!(D<2||l<=0)){if(m===C.ArcType.GEODESIC||m===C.ArcType.RHUMB){var T,x;m===C.ArcType.GEODESIC?(T=r.CesiumMath.chordLength(g,w.maximumRadius),x=E.PolylinePipeline.numberOfPoints):(T=g,x=E.PolylinePipeline.numberOfPointsRhumbLine);var G=E.PolylinePipeline.extractHeights(P,w);if(e.defined(f)){var R=1;for(o=0;o<D-1;++o)R+=x(P[o],P[o+1],T);var O=new Array(R),S=0;for(o=0;o<D-1;++o){var B=P[o],I=P[o+1],U=f[o],N=x(B,I,T);if(h&&o<R){var q=f[o+1],M=A(B,I,U,q,N),H=M.length;for(n=0;n<H;++n)O[S++]=M[n]}else for(n=0;n<N;++n)O[S++]=b.Color.clone(U)}O[S]=b.Color.clone(f[f.length-1]),f=O,_.length=0}P=m===C.ArcType.GEODESIC?E.PolylinePipeline.generateCartesianArc({positions:P,minDistance:T,ellipsoid:w,height:G}):E.PolylinePipeline.generateCartesianRhumbArc({positions:P,granularity:T,ellipsoid:w,height:G})}D=P.length;var W,Y=4*D-4,z=new Float64Array(3*Y),J=new Float64Array(3*Y),j=new Float64Array(3*Y),K=new Float32Array(2*Y),Q=s.st?new Float32Array(2*Y):void 0,X=e.defined(f)?new Uint8Array(4*Y):void 0,Z=0,$=0,ee=0,te=0;for(n=0;n<D;++n){var re,oe;0===n?(W=k,r.Cartesian3.subtract(P[0],P[1],W),r.Cartesian3.add(P[0],W,W)):W=P[n-1],r.Cartesian3.clone(W,L),r.Cartesian3.clone(P[n],V),n===D-1?(W=k,r.Cartesian3.subtract(P[D-1],P[D-2],W),r.Cartesian3.add(P[D-1],W,W)):W=P[n+1],r.Cartesian3.clone(W,F),e.defined(X)&&(re=0===n||h?f[n]:f[n-1],n!==D-1&&(oe=f[n]));var ae=0===n?2:0,ne=n===D-1?2:4;for(i=ae;i<ne;++i){r.Cartesian3.pack(V,z,Z),r.Cartesian3.pack(L,J,Z),r.Cartesian3.pack(F,j,Z),Z+=3;var ie=i-2<0?-1:1;if(K[$++]=i%2*2-1,K[$++]=ie*l,s.st&&(Q[ee++]=n/(D-1),Q[ee++]=Math.max(K[$-2],0)),e.defined(X)){var le=i<2?re:oe;X[te++]=b.Color.floatToByte(le.red),X[te++]=b.Color.floatToByte(le.green),X[te++]=b.Color.floatToByte(le.blue),X[te++]=b.Color.floatToByte(le.alpha)}}}var se=new u.GeometryAttributes;se.position=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:z}),se.prevPosition=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:J}),se.nextPosition=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:j}),se.expandAndWidth=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:K}),s.st&&(se.st=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:Q})),e.defined(X)&&(se.color=new d.GeometryAttribute({componentDatatype:p.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:X,normalize:!0}));var pe=y.IndexDatatype.createTypedArray(Y,6*D-6),de=0,ce=0,ue=D-1;for(n=0;n<ue;++n)pe[ce++]=de,pe[ce++]=de+2,pe[ce++]=de+1,pe[ce++]=de+1,pe[ce++]=de+2,pe[ce++]=de+3,de+=4;return new d.Geometry({attributes:se,indices:pe,primitiveType:c.PrimitiveType.TRIANGLES,boundingSphere:a.BoundingSphere.fromPoints(P),geometryType:d.GeometryType.POLYLINES})}},G}));