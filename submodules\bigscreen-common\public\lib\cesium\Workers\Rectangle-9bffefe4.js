/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./defined-3b3eb2ba","./Math-b5f4d889","./RuntimeError-592f0d41"],(function(t,e,n,a,i){"use strict";function r(t,e,a,i){this.x=n.defaultValue(t,0),this.y=n.defaultValue(e,0),this.z=n.defaultValue(a,0),this.w=n.defaultValue(i,0)}r.fromElements=function(t,e,a,i,u){return n.defined(u)?(u.x=t,u.y=e,u.z=a,u.w=i,u):new r(t,e,a,i)},r.fromColor=function(t,e){return n.defined(e)?(e.x=t.red,e.y=t.green,e.z=t.blue,e.w=t.alpha,e):new r(t.red,t.green,t.blue,t.alpha)},r.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e):new r(t.x,t.y,t.z,t.w)},r.packedLength=4,r.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t.x,e[a++]=t.y,e[a++]=t.z,e[a]=t.w,e},r.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new r),a.x=t[e++],a.y=t[e++],a.z=t[e++],a.w=t[e],a},r.packArray=function(t,e){const a=t.length,i=4*a;n.defined(e)?(Array.isArray(e)||e.length===i)&&e.length!==i&&(e.length=i):e=new Array(i);for(let n=0;n<a;++n)r.pack(t[n],e,4*n);return e},r.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/4:e=new Array(a/4);for(let n=0;n<a;n+=4){const a=n/4;e[a]=r.unpack(t,n,e[a])}return e},r.fromArray=r.unpack,r.maximumComponent=function(t){return Math.max(t.x,t.y,t.z,t.w)},r.minimumComponent=function(t){return Math.min(t.x,t.y,t.z,t.w)},r.minimumByComponent=function(t,e,n){return n.x=Math.min(t.x,e.x),n.y=Math.min(t.y,e.y),n.z=Math.min(t.z,e.z),n.w=Math.min(t.w,e.w),n},r.maximumByComponent=function(t,e,n){return n.x=Math.max(t.x,e.x),n.y=Math.max(t.y,e.y),n.z=Math.max(t.z,e.z),n.w=Math.max(t.w,e.w),n},r.clamp=function(t,e,n,i){const r=a.CesiumMath.clamp(t.x,e.x,n.x),u=a.CesiumMath.clamp(t.y,e.y,n.y),o=a.CesiumMath.clamp(t.z,e.z,n.z),s=a.CesiumMath.clamp(t.w,e.w,n.w);return i.x=r,i.y=u,i.z=o,i.w=s,i},r.magnitudeSquared=function(t){return t.x*t.x+t.y*t.y+t.z*t.z+t.w*t.w},r.magnitude=function(t){return Math.sqrt(r.magnitudeSquared(t))};const u=new r;r.distance=function(t,e){return r.subtract(t,e,u),r.magnitude(u)},r.distanceSquared=function(t,e){return r.subtract(t,e,u),r.magnitudeSquared(u)},r.normalize=function(t,e){const n=r.magnitude(t);return e.x=t.x/n,e.y=t.y/n,e.z=t.z/n,e.w=t.w/n,e},r.dot=function(t,e){return t.x*e.x+t.y*e.y+t.z*e.z+t.w*e.w},r.multiplyComponents=function(t,e,n){return n.x=t.x*e.x,n.y=t.y*e.y,n.z=t.z*e.z,n.w=t.w*e.w,n},r.divideComponents=function(t,e,n){return n.x=t.x/e.x,n.y=t.y/e.y,n.z=t.z/e.z,n.w=t.w/e.w,n},r.add=function(t,e,n){return n.x=t.x+e.x,n.y=t.y+e.y,n.z=t.z+e.z,n.w=t.w+e.w,n},r.subtract=function(t,e,n){return n.x=t.x-e.x,n.y=t.y-e.y,n.z=t.z-e.z,n.w=t.w-e.w,n},r.multiplyByScalar=function(t,e,n){return n.x=t.x*e,n.y=t.y*e,n.z=t.z*e,n.w=t.w*e,n},r.divideByScalar=function(t,e,n){return n.x=t.x/e,n.y=t.y/e,n.z=t.z/e,n.w=t.w/e,n},r.negate=function(t,e){return e.x=-t.x,e.y=-t.y,e.z=-t.z,e.w=-t.w,e},r.abs=function(t,e){return e.x=Math.abs(t.x),e.y=Math.abs(t.y),e.z=Math.abs(t.z),e.w=Math.abs(t.w),e};const o=new r;r.lerp=function(t,e,n,a){return r.multiplyByScalar(e,n,o),a=r.multiplyByScalar(t,1-n,a),r.add(o,a,a)};const s=new r;r.mostOrthogonalAxis=function(t,e){const n=r.normalize(t,s);return r.abs(n,n),e=n.x<=n.y?n.x<=n.z?n.x<=n.w?r.clone(r.UNIT_X,e):r.clone(r.UNIT_W,e):n.z<=n.w?r.clone(r.UNIT_Z,e):r.clone(r.UNIT_W,e):n.y<=n.z?n.y<=n.w?r.clone(r.UNIT_Y,e):r.clone(r.UNIT_W,e):n.z<=n.w?r.clone(r.UNIT_Z,e):r.clone(r.UNIT_W,e)},r.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.x===e.x&&t.y===e.y&&t.z===e.z&&t.w===e.w},r.equalsArray=function(t,e,n){return t.x===e[n]&&t.y===e[n+1]&&t.z===e[n+2]&&t.w===e[n+3]},r.equalsEpsilon=function(t,e,i,r){return t===e||n.defined(t)&&n.defined(e)&&a.CesiumMath.equalsEpsilon(t.x,e.x,i,r)&&a.CesiumMath.equalsEpsilon(t.y,e.y,i,r)&&a.CesiumMath.equalsEpsilon(t.z,e.z,i,r)&&a.CesiumMath.equalsEpsilon(t.w,e.w,i,r)},r.ZERO=Object.freeze(new r(0,0,0,0)),r.ONE=Object.freeze(new r(1,1,1,1)),r.UNIT_X=Object.freeze(new r(1,0,0,0)),r.UNIT_Y=Object.freeze(new r(0,1,0,0)),r.UNIT_Z=Object.freeze(new r(0,0,1,0)),r.UNIT_W=Object.freeze(new r(0,0,0,1)),r.prototype.clone=function(t){return r.clone(this,t)},r.prototype.equals=function(t){return r.equals(this,t)},r.prototype.equalsEpsilon=function(t,e,n){return r.equalsEpsilon(this,t,e,n)},r.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};const c=new Float32Array(1),l=new Uint8Array(c.buffer),f=new Uint32Array([287454020]),d=68===new Uint8Array(f.buffer)[0];function h(t,e,a,i){this[0]=n.defaultValue(t,0),this[1]=n.defaultValue(a,0),this[2]=n.defaultValue(e,0),this[3]=n.defaultValue(i,0)}r.packFloat=function(t,e){return n.defined(e)||(e=new r),c[0]=t,d?(e.x=l[0],e.y=l[1],e.z=l[2],e.w=l[3]):(e.x=l[3],e.y=l[2],e.z=l[1],e.w=l[0]),e},r.unpackFloat=function(t){return d?(l[0]=t.x,l[1]=t.y,l[2]=t.z,l[3]=t.w):(l[0]=t.w,l[1]=t.z,l[2]=t.y,l[3]=t.x),c[0]},h.packedLength=4,h.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t[0],e[a++]=t[1],e[a++]=t[2],e[a++]=t[3],e},h.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new h),a[0]=t[e++],a[1]=t[e++],a[2]=t[e++],a[3]=t[e++],a},h.packArray=function(t,e){const a=t.length,i=4*a;n.defined(e)?(Array.isArray(e)||e.length===i)&&e.length!==i&&(e.length=i):e=new Array(i);for(let n=0;n<a;++n)h.pack(t[n],e,4*n);return e},h.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/4:e=new Array(a/4);for(let n=0;n<a;n+=4){const a=n/4;e[a]=h.unpack(t,n,e[a])}return e},h.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):new h(t[0],t[2],t[1],t[3])},h.fromArray=h.unpack,h.fromColumnMajorArray=function(t,e){return h.clone(t,e)},h.fromRowMajorArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[2],e[2]=t[1],e[3]=t[3],e):new h(t[0],t[1],t[2],t[3])},h.fromScale=function(t,e){return n.defined(e)?(e[0]=t.x,e[1]=0,e[2]=0,e[3]=t.y,e):new h(t.x,0,0,t.y)},h.fromUniformScale=function(t,e){return n.defined(e)?(e[0]=t,e[1]=0,e[2]=0,e[3]=t,e):new h(t,0,0,t)},h.fromRotation=function(t,e){const a=Math.cos(t),i=Math.sin(t);return n.defined(e)?(e[0]=a,e[1]=i,e[2]=-i,e[3]=a,e):new h(a,-i,i,a)},h.toArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):[t[0],t[1],t[2],t[3]]},h.getElementIndex=function(t,e){return 2*t+e},h.getColumn=function(t,e,n){const a=2*e,i=t[a],r=t[a+1];return n.x=i,n.y=r,n},h.setColumn=function(t,e,n,a){const i=2*e;return(a=h.clone(t,a))[i]=n.x,a[i+1]=n.y,a},h.getRow=function(t,e,n){const a=t[e],i=t[e+2];return n.x=a,n.y=i,n},h.setRow=function(t,e,n,a){return(a=h.clone(t,a))[e]=n.x,a[e+2]=n.y,a};const m=new e.Cartesian2;h.setScale=function(t,e,n){const a=h.getScale(t,m),i=e.x/a.x,r=e.y/a.y;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*r,n[3]=t[3]*r,n};const y=new e.Cartesian2;h.setUniformScale=function(t,e,n){const a=h.getScale(t,y),i=e/a.x,r=e/a.y;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*r,n[3]=t[3]*r,n};const w=new e.Cartesian2;h.getScale=function(t,n){return n.x=e.Cartesian2.magnitude(e.Cartesian2.fromElements(t[0],t[1],w)),n.y=e.Cartesian2.magnitude(e.Cartesian2.fromElements(t[2],t[3],w)),n};const M=new e.Cartesian2;h.getMaximumScale=function(t){return h.getScale(t,M),e.Cartesian2.maximumComponent(M)};const p=new e.Cartesian2;h.setRotation=function(t,e,n){const a=h.getScale(t,p);return n[0]=e[0]*a.x,n[1]=e[1]*a.x,n[2]=e[2]*a.y,n[3]=e[3]*a.y,n};const C=new e.Cartesian2;function g(t,e,a,i,r,u,o,s,c){this[0]=n.defaultValue(t,0),this[1]=n.defaultValue(i,0),this[2]=n.defaultValue(o,0),this[3]=n.defaultValue(e,0),this[4]=n.defaultValue(r,0),this[5]=n.defaultValue(s,0),this[6]=n.defaultValue(a,0),this[7]=n.defaultValue(u,0),this[8]=n.defaultValue(c,0)}h.getRotation=function(t,e){const n=h.getScale(t,C);return e[0]=t[0]/n.x,e[1]=t[1]/n.x,e[2]=t[2]/n.y,e[3]=t[3]/n.y,e},h.multiply=function(t,e,n){const a=t[0]*e[0]+t[2]*e[1],i=t[0]*e[2]+t[2]*e[3],r=t[1]*e[0]+t[3]*e[1],u=t[1]*e[2]+t[3]*e[3];return n[0]=a,n[1]=r,n[2]=i,n[3]=u,n},h.add=function(t,e,n){return n[0]=t[0]+e[0],n[1]=t[1]+e[1],n[2]=t[2]+e[2],n[3]=t[3]+e[3],n},h.subtract=function(t,e,n){return n[0]=t[0]-e[0],n[1]=t[1]-e[1],n[2]=t[2]-e[2],n[3]=t[3]-e[3],n},h.multiplyByVector=function(t,e,n){const a=t[0]*e.x+t[2]*e.y,i=t[1]*e.x+t[3]*e.y;return n.x=a,n.y=i,n},h.multiplyByScalar=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n},h.multiplyByScale=function(t,e,n){return n[0]=t[0]*e.x,n[1]=t[1]*e.x,n[2]=t[2]*e.y,n[3]=t[3]*e.y,n},h.multiplyByUniformScale=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n},h.negate=function(t,e){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e},h.transpose=function(t,e){const n=t[0],a=t[2],i=t[1],r=t[3];return e[0]=n,e[1]=a,e[2]=i,e[3]=r,e},h.abs=function(t,e){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e[3]=Math.abs(t[3]),e},h.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]},h.equalsArray=function(t,e,n){return t[0]===e[n]&&t[1]===e[n+1]&&t[2]===e[n+2]&&t[3]===e[n+3]},h.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t[0]-e[0])<=a&&Math.abs(t[1]-e[1])<=a&&Math.abs(t[2]-e[2])<=a&&Math.abs(t[3]-e[3])<=a},h.IDENTITY=Object.freeze(new h(1,0,0,1)),h.ZERO=Object.freeze(new h(0,0,0,0)),h.COLUMN0ROW0=0,h.COLUMN0ROW1=1,h.COLUMN1ROW0=2,h.COLUMN1ROW1=3,Object.defineProperties(h.prototype,{length:{get:function(){return h.packedLength}}}),h.prototype.clone=function(t){return h.clone(this,t)},h.prototype.equals=function(t){return h.equals(this,t)},h.prototype.equalsEpsilon=function(t,e){return h.equalsEpsilon(this,t,e)},h.prototype.toString=function(){return`(${this[0]}, ${this[2]})\n(${this[1]}, ${this[3]})`},g.packedLength=9,g.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t[0],e[a++]=t[1],e[a++]=t[2],e[a++]=t[3],e[a++]=t[4],e[a++]=t[5],e[a++]=t[6],e[a++]=t[7],e[a++]=t[8],e},g.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new g),a[0]=t[e++],a[1]=t[e++],a[2]=t[e++],a[3]=t[e++],a[4]=t[e++],a[5]=t[e++],a[6]=t[e++],a[7]=t[e++],a[8]=t[e++],a},g.packArray=function(t,e){const a=t.length,i=9*a;n.defined(e)?(Array.isArray(e)||e.length===i)&&e.length!==i&&(e.length=i):e=new Array(i);for(let n=0;n<a;++n)g.pack(t[n],e,9*n);return e},g.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/9:e=new Array(a/9);for(let n=0;n<a;n+=9){const a=n/9;e[a]=g.unpack(t,n,e[a])}return e},g.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e):new g(t[0],t[3],t[6],t[1],t[4],t[7],t[2],t[5],t[8])},g.fromArray=g.unpack,g.fromColumnMajorArray=function(t,e){return g.clone(t,e)},g.fromRowMajorArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[3],e[2]=t[6],e[3]=t[1],e[4]=t[4],e[5]=t[7],e[6]=t[2],e[7]=t[5],e[8]=t[8],e):new g(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])},g.fromQuaternion=function(t,e){const a=t.x*t.x,i=t.x*t.y,r=t.x*t.z,u=t.x*t.w,o=t.y*t.y,s=t.y*t.z,c=t.y*t.w,l=t.z*t.z,f=t.z*t.w,d=t.w*t.w,h=a-o-l+d,m=2*(i-f),y=2*(r+c),w=2*(i+f),M=-a+o-l+d,p=2*(s-u),C=2*(r-c),x=2*(s+u),z=-a-o+l+d;return n.defined(e)?(e[0]=h,e[1]=w,e[2]=C,e[3]=m,e[4]=M,e[5]=x,e[6]=y,e[7]=p,e[8]=z,e):new g(h,m,y,w,M,p,C,x,z)},g.fromHeadingPitchRoll=function(t,e){const a=Math.cos(-t.pitch),i=Math.cos(-t.heading),r=Math.cos(t.roll),u=Math.sin(-t.pitch),o=Math.sin(-t.heading),s=Math.sin(t.roll),c=a*i,l=-r*o+s*u*i,f=s*o+r*u*i,d=a*o,h=r*i+s*u*o,m=-s*i+r*u*o,y=-u,w=s*a,M=r*a;return n.defined(e)?(e[0]=c,e[1]=d,e[2]=y,e[3]=l,e[4]=h,e[5]=w,e[6]=f,e[7]=m,e[8]=M,e):new g(c,l,f,d,h,m,y,w,M)},g.fromScale=function(t,e){return n.defined(e)?(e[0]=t.x,e[1]=0,e[2]=0,e[3]=0,e[4]=t.y,e[5]=0,e[6]=0,e[7]=0,e[8]=t.z,e):new g(t.x,0,0,0,t.y,0,0,0,t.z)},g.fromUniformScale=function(t,e){return n.defined(e)?(e[0]=t,e[1]=0,e[2]=0,e[3]=0,e[4]=t,e[5]=0,e[6]=0,e[7]=0,e[8]=t,e):new g(t,0,0,0,t,0,0,0,t)},g.fromCrossProduct=function(t,e){return n.defined(e)?(e[0]=0,e[1]=t.z,e[2]=-t.y,e[3]=-t.z,e[4]=0,e[5]=t.x,e[6]=t.y,e[7]=-t.x,e[8]=0,e):new g(0,-t.z,t.y,t.z,0,-t.x,-t.y,t.x,0)},g.fromRotationX=function(t,e){const a=Math.cos(t),i=Math.sin(t);return n.defined(e)?(e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=a,e[5]=i,e[6]=0,e[7]=-i,e[8]=a,e):new g(1,0,0,0,a,-i,0,i,a)},g.fromRotationY=function(t,e){const a=Math.cos(t),i=Math.sin(t);return n.defined(e)?(e[0]=a,e[1]=0,e[2]=-i,e[3]=0,e[4]=1,e[5]=0,e[6]=i,e[7]=0,e[8]=a,e):new g(a,0,i,0,1,0,-i,0,a)},g.fromRotationZ=function(t,e){const a=Math.cos(t),i=Math.sin(t);return n.defined(e)?(e[0]=a,e[1]=i,e[2]=0,e[3]=-i,e[4]=a,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e):new g(a,-i,0,i,a,0,0,0,1)},g.toArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e):[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8]]},g.getElementIndex=function(t,e){return 3*t+e},g.getColumn=function(t,e,n){const a=3*e,i=t[a],r=t[a+1],u=t[a+2];return n.x=i,n.y=r,n.z=u,n},g.setColumn=function(t,e,n,a){const i=3*e;return(a=g.clone(t,a))[i]=n.x,a[i+1]=n.y,a[i+2]=n.z,a},g.getRow=function(t,e,n){const a=t[e],i=t[e+3],r=t[e+6];return n.x=a,n.y=i,n.z=r,n},g.setRow=function(t,e,n,a){return(a=g.clone(t,a))[e]=n.x,a[e+3]=n.y,a[e+6]=n.z,a};const x=new e.Cartesian3;g.setScale=function(t,e,n){const a=g.getScale(t,x),i=e.x/a.x,r=e.y/a.y,u=e.z/a.z;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*i,n[3]=t[3]*r,n[4]=t[4]*r,n[5]=t[5]*r,n[6]=t[6]*u,n[7]=t[7]*u,n[8]=t[8]*u,n};const z=new e.Cartesian3;g.setUniformScale=function(t,e,n){const a=g.getScale(t,z),i=e/a.x,r=e/a.y,u=e/a.z;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*i,n[3]=t[3]*r,n[4]=t[4]*r,n[5]=t[5]*r,n[6]=t[6]*u,n[7]=t[7]*u,n[8]=t[8]*u,n};const O=new e.Cartesian3;g.getScale=function(t,n){return n.x=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[0],t[1],t[2],O)),n.y=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[3],t[4],t[5],O)),n.z=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[6],t[7],t[8],O)),n};const b=new e.Cartesian3;g.getMaximumScale=function(t){return g.getScale(t,b),e.Cartesian3.maximumComponent(b)};const S=new e.Cartesian3;g.setRotation=function(t,e,n){const a=g.getScale(t,S);return n[0]=e[0]*a.x,n[1]=e[1]*a.x,n[2]=e[2]*a.x,n[3]=e[3]*a.y,n[4]=e[4]*a.y,n[5]=e[5]*a.y,n[6]=e[6]*a.z,n[7]=e[7]*a.z,n[8]=e[8]*a.z,n};const R=new e.Cartesian3;g.getRotation=function(t,e){const n=g.getScale(t,R);return e[0]=t[0]/n.x,e[1]=t[1]/n.x,e[2]=t[2]/n.x,e[3]=t[3]/n.y,e[4]=t[4]/n.y,e[5]=t[5]/n.y,e[6]=t[6]/n.z,e[7]=t[7]/n.z,e[8]=t[8]/n.z,e},g.multiply=function(t,e,n){const a=t[0]*e[0]+t[3]*e[1]+t[6]*e[2],i=t[1]*e[0]+t[4]*e[1]+t[7]*e[2],r=t[2]*e[0]+t[5]*e[1]+t[8]*e[2],u=t[0]*e[3]+t[3]*e[4]+t[6]*e[5],o=t[1]*e[3]+t[4]*e[4]+t[7]*e[5],s=t[2]*e[3]+t[5]*e[4]+t[8]*e[5],c=t[0]*e[6]+t[3]*e[7]+t[6]*e[8],l=t[1]*e[6]+t[4]*e[7]+t[7]*e[8],f=t[2]*e[6]+t[5]*e[7]+t[8]*e[8];return n[0]=a,n[1]=i,n[2]=r,n[3]=u,n[4]=o,n[5]=s,n[6]=c,n[7]=l,n[8]=f,n},g.add=function(t,e,n){return n[0]=t[0]+e[0],n[1]=t[1]+e[1],n[2]=t[2]+e[2],n[3]=t[3]+e[3],n[4]=t[4]+e[4],n[5]=t[5]+e[5],n[6]=t[6]+e[6],n[7]=t[7]+e[7],n[8]=t[8]+e[8],n},g.subtract=function(t,e,n){return n[0]=t[0]-e[0],n[1]=t[1]-e[1],n[2]=t[2]-e[2],n[3]=t[3]-e[3],n[4]=t[4]-e[4],n[5]=t[5]-e[5],n[6]=t[6]-e[6],n[7]=t[7]-e[7],n[8]=t[8]-e[8],n},g.multiplyByVector=function(t,e,n){const a=e.x,i=e.y,r=e.z,u=t[0]*a+t[3]*i+t[6]*r,o=t[1]*a+t[4]*i+t[7]*r,s=t[2]*a+t[5]*i+t[8]*r;return n.x=u,n.y=o,n.z=s,n},g.multiplyByScalar=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7]*e,n[8]=t[8]*e,n},g.multiplyByScale=function(t,e,n){return n[0]=t[0]*e.x,n[1]=t[1]*e.x,n[2]=t[2]*e.x,n[3]=t[3]*e.y,n[4]=t[4]*e.y,n[5]=t[5]*e.y,n[6]=t[6]*e.z,n[7]=t[7]*e.z,n[8]=t[8]*e.z,n},g.multiplyByUniformScale=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7]*e,n[8]=t[8]*e,n},g.negate=function(t,e){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e[4]=-t[4],e[5]=-t[5],e[6]=-t[6],e[7]=-t[7],e[8]=-t[8],e},g.transpose=function(t,e){const n=t[0],a=t[3],i=t[6],r=t[1],u=t[4],o=t[7],s=t[2],c=t[5],l=t[8];return e[0]=n,e[1]=a,e[2]=i,e[3]=r,e[4]=u,e[5]=o,e[6]=s,e[7]=c,e[8]=l,e};const _=[1,0,0],T=[2,2,1];function V(t){let e=0;for(let n=0;n<3;++n){const a=t[g.getElementIndex(T[n],_[n])];e+=2*a*a}return Math.sqrt(e)}function q(t,e){const n=a.CesiumMath.EPSILON15;let i=0,r=1;for(let e=0;e<3;++e){const n=Math.abs(t[g.getElementIndex(T[e],_[e])]);n>i&&(r=e,i=n)}let u=1,o=0;const s=_[r],c=T[r];if(Math.abs(t[g.getElementIndex(c,s)])>n){const e=(t[g.getElementIndex(c,c)]-t[g.getElementIndex(s,s)])/2/t[g.getElementIndex(c,s)];let n;n=e<0?-1/(-e+Math.sqrt(1+e*e)):1/(e+Math.sqrt(1+e*e)),u=1/Math.sqrt(1+n*n),o=n*u}return(e=g.clone(g.IDENTITY,e))[g.getElementIndex(s,s)]=e[g.getElementIndex(c,c)]=u,e[g.getElementIndex(c,s)]=o,e[g.getElementIndex(s,c)]=-o,e}const E=new g,A=new g;g.computeEigenDecomposition=function(t,e){const i=a.CesiumMath.EPSILON20;let r=0,u=0;n.defined(e)||(e={});const o=e.unitary=g.clone(g.IDENTITY,e.unitary),s=e.diagonal=g.clone(t,e.diagonal),c=i*function(t){let e=0;for(let n=0;n<9;++n){const a=t[n];e+=a*a}return Math.sqrt(e)}(s);for(;u<10&&V(s)>c;)q(s,E),g.transpose(E,A),g.multiply(s,E,s),g.multiply(A,s,s),g.multiply(o,E,o),++r>2&&(++u,r=0);return e},g.abs=function(t,e){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e[3]=Math.abs(t[3]),e[4]=Math.abs(t[4]),e[5]=Math.abs(t[5]),e[6]=Math.abs(t[6]),e[7]=Math.abs(t[7]),e[8]=Math.abs(t[8]),e},g.determinant=function(t){const e=t[0],n=t[3],a=t[6],i=t[1],r=t[4],u=t[7],o=t[2],s=t[5],c=t[8];return e*(r*c-s*u)+i*(s*a-n*c)+o*(n*u-r*a)},g.inverse=function(t,e){const n=t[0],a=t[1],i=t[2],r=t[3],u=t[4],o=t[5],s=t[6],c=t[7],l=t[8],f=g.determinant(t);e[0]=u*l-c*o,e[1]=c*i-a*l,e[2]=a*o-u*i,e[3]=s*o-r*l,e[4]=n*l-s*i,e[5]=r*i-n*o,e[6]=r*c-s*u,e[7]=s*a-n*c,e[8]=n*u-r*a;const d=1/f;return g.multiplyByScalar(e,d,e)};const I=new g;function N(t,e,a,i,r,u,o,s,c,l,f,d,h,m,y,w){this[0]=n.defaultValue(t,0),this[1]=n.defaultValue(r,0),this[2]=n.defaultValue(c,0),this[3]=n.defaultValue(h,0),this[4]=n.defaultValue(e,0),this[5]=n.defaultValue(u,0),this[6]=n.defaultValue(l,0),this[7]=n.defaultValue(m,0),this[8]=n.defaultValue(a,0),this[9]=n.defaultValue(o,0),this[10]=n.defaultValue(f,0),this[11]=n.defaultValue(y,0),this[12]=n.defaultValue(i,0),this[13]=n.defaultValue(s,0),this[14]=n.defaultValue(d,0),this[15]=n.defaultValue(w,0)}g.inverseTranspose=function(t,e){return g.inverse(g.transpose(t,I),e)},g.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[7]===e[7]&&t[8]===e[8]},g.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t[0]-e[0])<=a&&Math.abs(t[1]-e[1])<=a&&Math.abs(t[2]-e[2])<=a&&Math.abs(t[3]-e[3])<=a&&Math.abs(t[4]-e[4])<=a&&Math.abs(t[5]-e[5])<=a&&Math.abs(t[6]-e[6])<=a&&Math.abs(t[7]-e[7])<=a&&Math.abs(t[8]-e[8])<=a},g.IDENTITY=Object.freeze(new g(1,0,0,0,1,0,0,0,1)),g.ZERO=Object.freeze(new g(0,0,0,0,0,0,0,0,0)),g.COLUMN0ROW0=0,g.COLUMN0ROW1=1,g.COLUMN0ROW2=2,g.COLUMN1ROW0=3,g.COLUMN1ROW1=4,g.COLUMN1ROW2=5,g.COLUMN2ROW0=6,g.COLUMN2ROW1=7,g.COLUMN2ROW2=8,Object.defineProperties(g.prototype,{length:{get:function(){return g.packedLength}}}),g.prototype.clone=function(t){return g.clone(this,t)},g.prototype.equals=function(t){return g.equals(this,t)},g.equalsArray=function(t,e,n){return t[0]===e[n]&&t[1]===e[n+1]&&t[2]===e[n+2]&&t[3]===e[n+3]&&t[4]===e[n+4]&&t[5]===e[n+5]&&t[6]===e[n+6]&&t[7]===e[n+7]&&t[8]===e[n+8]},g.prototype.equalsEpsilon=function(t,e){return g.equalsEpsilon(this,t,e)},g.prototype.toString=function(){return`(${this[0]}, ${this[3]}, ${this[6]})\n(${this[1]}, ${this[4]}, ${this[7]})\n(${this[2]}, ${this[5]}, ${this[8]})`},N.packedLength=16,N.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t[0],e[a++]=t[1],e[a++]=t[2],e[a++]=t[3],e[a++]=t[4],e[a++]=t[5],e[a++]=t[6],e[a++]=t[7],e[a++]=t[8],e[a++]=t[9],e[a++]=t[10],e[a++]=t[11],e[a++]=t[12],e[a++]=t[13],e[a++]=t[14],e[a]=t[15],e},N.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new N),a[0]=t[e++],a[1]=t[e++],a[2]=t[e++],a[3]=t[e++],a[4]=t[e++],a[5]=t[e++],a[6]=t[e++],a[7]=t[e++],a[8]=t[e++],a[9]=t[e++],a[10]=t[e++],a[11]=t[e++],a[12]=t[e++],a[13]=t[e++],a[14]=t[e++],a[15]=t[e],a},N.packArray=function(t,e){const a=t.length,i=16*a;n.defined(e)?(Array.isArray(e)||e.length===i)&&e.length!==i&&(e.length=i):e=new Array(i);for(let n=0;n<a;++n)N.pack(t[n],e,16*n);return e},N.unpackArray=function(t,e){const a=t.length;n.defined(e)?e.length=a/16:e=new Array(a/16);for(let n=0;n<a;n+=16){const a=n/16;e[a]=N.unpack(t,n,e[a])}return e},N.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e):new N(t[0],t[4],t[8],t[12],t[1],t[5],t[9],t[13],t[2],t[6],t[10],t[14],t[3],t[7],t[11],t[15])},N.fromArray=N.unpack,N.fromColumnMajorArray=function(t,e){return N.clone(t,e)},N.fromRowMajorArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[4],e[2]=t[8],e[3]=t[12],e[4]=t[1],e[5]=t[5],e[6]=t[9],e[7]=t[13],e[8]=t[2],e[9]=t[6],e[10]=t[10],e[11]=t[14],e[12]=t[3],e[13]=t[7],e[14]=t[11],e[15]=t[15],e):new N(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},N.fromRotationTranslation=function(t,a,i){return a=n.defaultValue(a,e.Cartesian3.ZERO),n.defined(i)?(i[0]=t[0],i[1]=t[1],i[2]=t[2],i[3]=0,i[4]=t[3],i[5]=t[4],i[6]=t[5],i[7]=0,i[8]=t[6],i[9]=t[7],i[10]=t[8],i[11]=0,i[12]=a.x,i[13]=a.y,i[14]=a.z,i[15]=1,i):new N(t[0],t[3],t[6],a.x,t[1],t[4],t[7],a.y,t[2],t[5],t[8],a.z,0,0,0,1)},N.fromTranslationQuaternionRotationScale=function(t,e,a,i){n.defined(i)||(i=new N);const r=a.x,u=a.y,o=a.z,s=e.x*e.x,c=e.x*e.y,l=e.x*e.z,f=e.x*e.w,d=e.y*e.y,h=e.y*e.z,m=e.y*e.w,y=e.z*e.z,w=e.z*e.w,M=e.w*e.w,p=s-d-y+M,C=2*(c-w),g=2*(l+m),x=2*(c+w),z=-s+d-y+M,O=2*(h-f),b=2*(l-m),S=2*(h+f),R=-s-d+y+M;return i[0]=p*r,i[1]=x*r,i[2]=b*r,i[3]=0,i[4]=C*u,i[5]=z*u,i[6]=S*u,i[7]=0,i[8]=g*o,i[9]=O*o,i[10]=R*o,i[11]=0,i[12]=t.x,i[13]=t.y,i[14]=t.z,i[15]=1,i},N.fromTranslationRotationScale=function(t,e){return N.fromTranslationQuaternionRotationScale(t.translation,t.rotation,t.scale,e)},N.fromTranslation=function(t,e){return N.fromRotationTranslation(g.IDENTITY,t,e)},N.fromScale=function(t,e){return n.defined(e)?(e[0]=t.x,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=t.y,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=t.z,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e):new N(t.x,0,0,0,0,t.y,0,0,0,0,t.z,0,0,0,0,1)},N.fromUniformScale=function(t,e){return n.defined(e)?(e[0]=t,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=t,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=t,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e):new N(t,0,0,0,0,t,0,0,0,0,t,0,0,0,0,1)},N.fromRotation=function(t,e){return n.defined(e)||(e=new N),e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=0,e[4]=t[3],e[5]=t[4],e[6]=t[5],e[7]=0,e[8]=t[6],e[9]=t[7],e[10]=t[8],e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e};const U=new e.Cartesian3,P=new e.Cartesian3,L=new e.Cartesian3;N.fromCamera=function(t,a){const i=t.position,r=t.direction,u=t.up;e.Cartesian3.normalize(r,U),e.Cartesian3.normalize(e.Cartesian3.cross(U,u,P),P),e.Cartesian3.normalize(e.Cartesian3.cross(P,U,L),L);const o=P.x,s=P.y,c=P.z,l=U.x,f=U.y,d=U.z,h=L.x,m=L.y,y=L.z,w=i.x,M=i.y,p=i.z,C=o*-w+s*-M+c*-p,g=h*-w+m*-M+y*-p,x=l*w+f*M+d*p;return n.defined(a)?(a[0]=o,a[1]=h,a[2]=-l,a[3]=0,a[4]=s,a[5]=m,a[6]=-f,a[7]=0,a[8]=c,a[9]=y,a[10]=-d,a[11]=0,a[12]=C,a[13]=g,a[14]=x,a[15]=1,a):new N(o,s,c,C,h,m,y,g,-l,-f,-d,x,0,0,0,1)},N.computePerspectiveFieldOfView=function(t,e,n,a,i){const r=1/Math.tan(.5*t),u=r/e,o=(a+n)/(n-a),s=2*a*n/(n-a);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=r,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=o,i[11]=-1,i[12]=0,i[13]=0,i[14]=s,i[15]=0,i},N.computeOrthographicOffCenter=function(t,e,n,a,i,r,u){let o=1/(e-t),s=1/(a-n),c=1/(r-i);const l=-(e+t)*o,f=-(a+n)*s,d=-(r+i)*c;return o*=2,s*=2,c*=-2,u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=c,u[11]=0,u[12]=l,u[13]=f,u[14]=d,u[15]=1,u},N.computePerspectiveOffCenter=function(t,e,n,a,i,r,u){const o=2*i/(e-t),s=2*i/(a-n),c=(e+t)/(e-t),l=(a+n)/(a-n),f=-(r+i)/(r-i),d=-2*r*i/(r-i);return u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=c,u[9]=l,u[10]=f,u[11]=-1,u[12]=0,u[13]=0,u[14]=d,u[15]=0,u},N.computeInfinitePerspectiveOffCenter=function(t,e,n,a,i,r){const u=2*i/(e-t),o=2*i/(a-n),s=(e+t)/(e-t),c=(a+n)/(a-n),l=-2*i;return r[0]=u,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=o,r[6]=0,r[7]=0,r[8]=s,r[9]=c,r[10]=-1,r[11]=-1,r[12]=0,r[13]=0,r[14]=l,r[15]=0,r},N.computeViewportTransformation=function(t,e,a,i){n.defined(i)||(i=new N),t=n.defaultValue(t,n.defaultValue.EMPTY_OBJECT);const r=n.defaultValue(t.x,0),u=n.defaultValue(t.y,0),o=n.defaultValue(t.width,0),s=n.defaultValue(t.height,0);e=n.defaultValue(e,0);const c=.5*o,l=.5*s,f=.5*((a=n.defaultValue(a,1))-e),d=c,h=l,m=f,y=r+c,w=u+l,M=e+f;return i[0]=d,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=h,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=m,i[11]=0,i[12]=y,i[13]=w,i[14]=M,i[15]=1,i},N.computeView=function(t,n,a,i,r){return r[0]=i.x,r[1]=a.x,r[2]=-n.x,r[3]=0,r[4]=i.y,r[5]=a.y,r[6]=-n.y,r[7]=0,r[8]=i.z,r[9]=a.z,r[10]=-n.z,r[11]=0,r[12]=-e.Cartesian3.dot(i,t),r[13]=-e.Cartesian3.dot(a,t),r[14]=e.Cartesian3.dot(n,t),r[15]=1,r},N.toArray=function(t,e){return n.defined(e)?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e):[t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]]},N.getElementIndex=function(t,e){return 4*t+e},N.getColumn=function(t,e,n){const a=4*e,i=t[a],r=t[a+1],u=t[a+2],o=t[a+3];return n.x=i,n.y=r,n.z=u,n.w=o,n},N.setColumn=function(t,e,n,a){const i=4*e;return(a=N.clone(t,a))[i]=n.x,a[i+1]=n.y,a[i+2]=n.z,a[i+3]=n.w,a},N.getRow=function(t,e,n){const a=t[e],i=t[e+4],r=t[e+8],u=t[e+12];return n.x=a,n.y=i,n.z=r,n.w=u,n},N.setRow=function(t,e,n,a){return(a=N.clone(t,a))[e]=n.x,a[e+4]=n.y,a[e+8]=n.z,a[e+12]=n.w,a},N.setTranslation=function(t,e,n){return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=e.x,n[13]=e.y,n[14]=e.z,n[15]=t[15],n};const W=new e.Cartesian3;N.setScale=function(t,e,n){const a=N.getScale(t,W),i=e.x/a.x,r=e.y/a.y,u=e.z/a.z;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*i,n[3]=t[3],n[4]=t[4]*r,n[5]=t[5]*r,n[6]=t[6]*r,n[7]=t[7],n[8]=t[8]*u,n[9]=t[9]*u,n[10]=t[10]*u,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const v=new e.Cartesian3;N.setUniformScale=function(t,e,n){const a=N.getScale(t,v),i=e/a.x,r=e/a.y,u=e/a.z;return n[0]=t[0]*i,n[1]=t[1]*i,n[2]=t[2]*i,n[3]=t[3],n[4]=t[4]*r,n[5]=t[5]*r,n[6]=t[6]*r,n[7]=t[7],n[8]=t[8]*u,n[9]=t[9]*u,n[10]=t[10]*u,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const k=new e.Cartesian3;N.getScale=function(t,n){return n.x=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[0],t[1],t[2],k)),n.y=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[4],t[5],t[6],k)),n.z=e.Cartesian3.magnitude(e.Cartesian3.fromElements(t[8],t[9],t[10],k)),n};const $=new e.Cartesian3;N.getMaximumScale=function(t){return N.getScale(t,$),e.Cartesian3.maximumComponent($)};const j=new e.Cartesian3;N.setRotation=function(t,e,n){const a=N.getScale(t,j);return n[0]=e[0]*a.x,n[1]=e[1]*a.x,n[2]=e[2]*a.x,n[3]=t[3],n[4]=e[3]*a.y,n[5]=e[4]*a.y,n[6]=e[5]*a.y,n[7]=t[7],n[8]=e[6]*a.z,n[9]=e[7]*a.z,n[10]=e[8]*a.z,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n};const B=new e.Cartesian3;N.getRotation=function(t,e){const n=N.getScale(t,B);return e[0]=t[0]/n.x,e[1]=t[1]/n.x,e[2]=t[2]/n.x,e[3]=t[4]/n.y,e[4]=t[5]/n.y,e[5]=t[6]/n.y,e[6]=t[8]/n.z,e[7]=t[9]/n.z,e[8]=t[10]/n.z,e},N.multiply=function(t,e,n){const a=t[0],i=t[1],r=t[2],u=t[3],o=t[4],s=t[5],c=t[6],l=t[7],f=t[8],d=t[9],h=t[10],m=t[11],y=t[12],w=t[13],M=t[14],p=t[15],C=e[0],g=e[1],x=e[2],z=e[3],O=e[4],b=e[5],S=e[6],R=e[7],_=e[8],T=e[9],V=e[10],q=e[11],E=e[12],A=e[13],I=e[14],N=e[15],U=a*C+o*g+f*x+y*z,P=i*C+s*g+d*x+w*z,L=r*C+c*g+h*x+M*z,W=u*C+l*g+m*x+p*z,v=a*O+o*b+f*S+y*R,k=i*O+s*b+d*S+w*R,$=r*O+c*b+h*S+M*R,j=u*O+l*b+m*S+p*R,B=a*_+o*T+f*V+y*q,X=i*_+s*T+d*V+w*q,Z=r*_+c*T+h*V+M*q,D=u*_+l*T+m*V+p*q,F=a*E+o*A+f*I+y*N,Y=i*E+s*A+d*I+w*N,G=r*E+c*A+h*I+M*N,H=u*E+l*A+m*I+p*N;return n[0]=U,n[1]=P,n[2]=L,n[3]=W,n[4]=v,n[5]=k,n[6]=$,n[7]=j,n[8]=B,n[9]=X,n[10]=Z,n[11]=D,n[12]=F,n[13]=Y,n[14]=G,n[15]=H,n},N.add=function(t,e,n){return n[0]=t[0]+e[0],n[1]=t[1]+e[1],n[2]=t[2]+e[2],n[3]=t[3]+e[3],n[4]=t[4]+e[4],n[5]=t[5]+e[5],n[6]=t[6]+e[6],n[7]=t[7]+e[7],n[8]=t[8]+e[8],n[9]=t[9]+e[9],n[10]=t[10]+e[10],n[11]=t[11]+e[11],n[12]=t[12]+e[12],n[13]=t[13]+e[13],n[14]=t[14]+e[14],n[15]=t[15]+e[15],n},N.subtract=function(t,e,n){return n[0]=t[0]-e[0],n[1]=t[1]-e[1],n[2]=t[2]-e[2],n[3]=t[3]-e[3],n[4]=t[4]-e[4],n[5]=t[5]-e[5],n[6]=t[6]-e[6],n[7]=t[7]-e[7],n[8]=t[8]-e[8],n[9]=t[9]-e[9],n[10]=t[10]-e[10],n[11]=t[11]-e[11],n[12]=t[12]-e[12],n[13]=t[13]-e[13],n[14]=t[14]-e[14],n[15]=t[15]-e[15],n},N.multiplyTransformation=function(t,e,n){const a=t[0],i=t[1],r=t[2],u=t[4],o=t[5],s=t[6],c=t[8],l=t[9],f=t[10],d=t[12],h=t[13],m=t[14],y=e[0],w=e[1],M=e[2],p=e[4],C=e[5],g=e[6],x=e[8],z=e[9],O=e[10],b=e[12],S=e[13],R=e[14],_=a*y+u*w+c*M,T=i*y+o*w+l*M,V=r*y+s*w+f*M,q=a*p+u*C+c*g,E=i*p+o*C+l*g,A=r*p+s*C+f*g,I=a*x+u*z+c*O,N=i*x+o*z+l*O,U=r*x+s*z+f*O,P=a*b+u*S+c*R+d,L=i*b+o*S+l*R+h,W=r*b+s*S+f*R+m;return n[0]=_,n[1]=T,n[2]=V,n[3]=0,n[4]=q,n[5]=E,n[6]=A,n[7]=0,n[8]=I,n[9]=N,n[10]=U,n[11]=0,n[12]=P,n[13]=L,n[14]=W,n[15]=1,n},N.multiplyByMatrix3=function(t,e,n){const a=t[0],i=t[1],r=t[2],u=t[4],o=t[5],s=t[6],c=t[8],l=t[9],f=t[10],d=e[0],h=e[1],m=e[2],y=e[3],w=e[4],M=e[5],p=e[6],C=e[7],g=e[8],x=a*d+u*h+c*m,z=i*d+o*h+l*m,O=r*d+s*h+f*m,b=a*y+u*w+c*M,S=i*y+o*w+l*M,R=r*y+s*w+f*M,_=a*p+u*C+c*g,T=i*p+o*C+l*g,V=r*p+s*C+f*g;return n[0]=x,n[1]=z,n[2]=O,n[3]=0,n[4]=b,n[5]=S,n[6]=R,n[7]=0,n[8]=_,n[9]=T,n[10]=V,n[11]=0,n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},N.multiplyByTranslation=function(t,e,n){const a=e.x,i=e.y,r=e.z,u=a*t[0]+i*t[4]+r*t[8]+t[12],o=a*t[1]+i*t[5]+r*t[9]+t[13],s=a*t[2]+i*t[6]+r*t[10]+t[14];return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=u,n[13]=o,n[14]=s,n[15]=t[15],n},N.multiplyByScale=function(t,e,n){const a=e.x,i=e.y,r=e.z;return 1===a&&1===i&&1===r?N.clone(t,n):(n[0]=a*t[0],n[1]=a*t[1],n[2]=a*t[2],n[3]=t[3],n[4]=i*t[4],n[5]=i*t[5],n[6]=i*t[6],n[7]=t[7],n[8]=r*t[8],n[9]=r*t[9],n[10]=r*t[10],n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n)},N.multiplyByUniformScale=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3],n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7],n[8]=t[8]*e,n[9]=t[9]*e,n[10]=t[10]*e,n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},N.multiplyByVector=function(t,e,n){const a=e.x,i=e.y,r=e.z,u=e.w,o=t[0]*a+t[4]*i+t[8]*r+t[12]*u,s=t[1]*a+t[5]*i+t[9]*r+t[13]*u,c=t[2]*a+t[6]*i+t[10]*r+t[14]*u,l=t[3]*a+t[7]*i+t[11]*r+t[15]*u;return n.x=o,n.y=s,n.z=c,n.w=l,n},N.multiplyByPointAsVector=function(t,e,n){const a=e.x,i=e.y,r=e.z,u=t[0]*a+t[4]*i+t[8]*r,o=t[1]*a+t[5]*i+t[9]*r,s=t[2]*a+t[6]*i+t[10]*r;return n.x=u,n.y=o,n.z=s,n},N.multiplyByPoint=function(t,e,n){const a=e.x,i=e.y,r=e.z,u=t[0]*a+t[4]*i+t[8]*r+t[12],o=t[1]*a+t[5]*i+t[9]*r+t[13],s=t[2]*a+t[6]*i+t[10]*r+t[14];return n.x=u,n.y=o,n.z=s,n},N.multiplyByScalar=function(t,e,n){return n[0]=t[0]*e,n[1]=t[1]*e,n[2]=t[2]*e,n[3]=t[3]*e,n[4]=t[4]*e,n[5]=t[5]*e,n[6]=t[6]*e,n[7]=t[7]*e,n[8]=t[8]*e,n[9]=t[9]*e,n[10]=t[10]*e,n[11]=t[11]*e,n[12]=t[12]*e,n[13]=t[13]*e,n[14]=t[14]*e,n[15]=t[15]*e,n},N.negate=function(t,e){return e[0]=-t[0],e[1]=-t[1],e[2]=-t[2],e[3]=-t[3],e[4]=-t[4],e[5]=-t[5],e[6]=-t[6],e[7]=-t[7],e[8]=-t[8],e[9]=-t[9],e[10]=-t[10],e[11]=-t[11],e[12]=-t[12],e[13]=-t[13],e[14]=-t[14],e[15]=-t[15],e},N.transpose=function(t,e){const n=t[1],a=t[2],i=t[3],r=t[6],u=t[7],o=t[11];return e[0]=t[0],e[1]=t[4],e[2]=t[8],e[3]=t[12],e[4]=n,e[5]=t[5],e[6]=t[9],e[7]=t[13],e[8]=a,e[9]=r,e[10]=t[10],e[11]=t[14],e[12]=i,e[13]=u,e[14]=o,e[15]=t[15],e},N.abs=function(t,e){return e[0]=Math.abs(t[0]),e[1]=Math.abs(t[1]),e[2]=Math.abs(t[2]),e[3]=Math.abs(t[3]),e[4]=Math.abs(t[4]),e[5]=Math.abs(t[5]),e[6]=Math.abs(t[6]),e[7]=Math.abs(t[7]),e[8]=Math.abs(t[8]),e[9]=Math.abs(t[9]),e[10]=Math.abs(t[10]),e[11]=Math.abs(t[11]),e[12]=Math.abs(t[12]),e[13]=Math.abs(t[13]),e[14]=Math.abs(t[14]),e[15]=Math.abs(t[15]),e},N.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t[12]===e[12]&&t[13]===e[13]&&t[14]===e[14]&&t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[8]===e[8]&&t[9]===e[9]&&t[10]===e[10]&&t[3]===e[3]&&t[7]===e[7]&&t[11]===e[11]&&t[15]===e[15]},N.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t[0]-e[0])<=a&&Math.abs(t[1]-e[1])<=a&&Math.abs(t[2]-e[2])<=a&&Math.abs(t[3]-e[3])<=a&&Math.abs(t[4]-e[4])<=a&&Math.abs(t[5]-e[5])<=a&&Math.abs(t[6]-e[6])<=a&&Math.abs(t[7]-e[7])<=a&&Math.abs(t[8]-e[8])<=a&&Math.abs(t[9]-e[9])<=a&&Math.abs(t[10]-e[10])<=a&&Math.abs(t[11]-e[11])<=a&&Math.abs(t[12]-e[12])<=a&&Math.abs(t[13]-e[13])<=a&&Math.abs(t[14]-e[14])<=a&&Math.abs(t[15]-e[15])<=a},N.getTranslation=function(t,e){return e.x=t[12],e.y=t[13],e.z=t[14],e},N.getMatrix3=function(t,e){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[4],e[4]=t[5],e[5]=t[6],e[6]=t[8],e[7]=t[9],e[8]=t[10],e};const X=new g,Z=new g,D=new r,F=new r(0,0,0,1);N.inverse=function(t,e){const n=t[0],u=t[4],o=t[8],s=t[12],c=t[1],l=t[5],f=t[9],d=t[13],h=t[2],m=t[6],y=t[10],w=t[14],M=t[3],p=t[7],C=t[11],x=t[15];let z=y*x,O=w*C,b=m*x,S=w*p,R=m*C,_=y*p,T=h*x,V=w*M,q=h*C,E=y*M,A=h*p,I=m*M;const U=z*l+S*f+R*d-(O*l+b*f+_*d),P=O*c+T*f+E*d-(z*c+V*f+q*d),L=b*c+V*l+A*d-(S*c+T*l+I*d),W=_*c+q*l+I*f-(R*c+E*l+A*f),v=O*u+b*o+_*s-(z*u+S*o+R*s),k=z*n+V*o+q*s-(O*n+T*o+E*s),$=S*n+T*u+I*s-(b*n+V*u+A*s),j=R*n+E*u+A*o-(_*n+q*u+I*o);z=o*d,O=s*f,b=u*d,S=s*l,R=u*f,_=o*l,T=n*d,V=s*c,q=n*f,E=o*c,A=n*l,I=u*c;const B=z*p+S*C+R*x-(O*p+b*C+_*x),Y=O*M+T*C+E*x-(z*M+V*C+q*x),G=b*M+V*p+A*x-(S*M+T*p+I*x),H=_*M+q*p+I*C-(R*M+E*p+A*C),Q=b*y+_*w+O*m-(R*w+z*m+S*y),J=q*w+z*h+V*y-(T*y+E*w+O*h),K=T*m+I*w+S*h-(A*w+b*h+V*m),tt=A*y+R*h+E*m-(q*m+I*y+_*h);let et=n*U+u*P+o*L+s*W;if(Math.abs(et)<a.CesiumMath.EPSILON21){if(g.equalsEpsilon(N.getMatrix3(t,X),Z,a.CesiumMath.EPSILON7)&&r.equals(N.getRow(t,3,D),F))return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=0,e[11]=0,e[12]=-t[12],e[13]=-t[13],e[14]=-t[14],e[15]=1,e;throw new i.RuntimeError("matrix is not invertible because its determinate is zero.")}return et=1/et,e[0]=U*et,e[1]=P*et,e[2]=L*et,e[3]=W*et,e[4]=v*et,e[5]=k*et,e[6]=$*et,e[7]=j*et,e[8]=B*et,e[9]=Y*et,e[10]=G*et,e[11]=H*et,e[12]=Q*et,e[13]=J*et,e[14]=K*et,e[15]=tt*et,e},N.inverseTransformation=function(t,e){const n=t[0],a=t[1],i=t[2],r=t[4],u=t[5],o=t[6],s=t[8],c=t[9],l=t[10],f=t[12],d=t[13],h=t[14],m=-n*f-a*d-i*h,y=-r*f-u*d-o*h,w=-s*f-c*d-l*h;return e[0]=n,e[1]=r,e[2]=s,e[3]=0,e[4]=a,e[5]=u,e[6]=c,e[7]=0,e[8]=i,e[9]=o,e[10]=l,e[11]=0,e[12]=m,e[13]=y,e[14]=w,e[15]=1,e};const Y=new N;N.inverseTranspose=function(t,e){return N.inverse(N.transpose(t,Y),e)},N.IDENTITY=Object.freeze(new N(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),N.ZERO=Object.freeze(new N(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),N.COLUMN0ROW0=0,N.COLUMN0ROW1=1,N.COLUMN0ROW2=2,N.COLUMN0ROW3=3,N.COLUMN1ROW0=4,N.COLUMN1ROW1=5,N.COLUMN1ROW2=6,N.COLUMN1ROW3=7,N.COLUMN2ROW0=8,N.COLUMN2ROW1=9,N.COLUMN2ROW2=10,N.COLUMN2ROW3=11,N.COLUMN3ROW0=12,N.COLUMN3ROW1=13,N.COLUMN3ROW2=14,N.COLUMN3ROW3=15,Object.defineProperties(N.prototype,{length:{get:function(){return N.packedLength}}}),N.prototype.clone=function(t){return N.clone(this,t)},N.prototype.equals=function(t){return N.equals(this,t)},N.equalsArray=function(t,e,n){return t[0]===e[n]&&t[1]===e[n+1]&&t[2]===e[n+2]&&t[3]===e[n+3]&&t[4]===e[n+4]&&t[5]===e[n+5]&&t[6]===e[n+6]&&t[7]===e[n+7]&&t[8]===e[n+8]&&t[9]===e[n+9]&&t[10]===e[n+10]&&t[11]===e[n+11]&&t[12]===e[n+12]&&t[13]===e[n+13]&&t[14]===e[n+14]&&t[15]===e[n+15]},N.prototype.equalsEpsilon=function(t,e){return N.equalsEpsilon(this,t,e)},N.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})\n(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})\n(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})\n(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};const G=new e.Cartesian3,H=new e.Cartesian3;function Q(t,i,r,u,o){const s=t.x,c=t.y,l=t.z,f=i.x,d=i.y,h=i.z,m=s*s*f*f,y=c*c*d*d,w=l*l*h*h,M=m+y+w,p=Math.sqrt(1/M),C=e.Cartesian3.multiplyByScalar(t,p,G);if(M<u)return isFinite(p)?e.Cartesian3.clone(C,o):void 0;const g=r.x,x=r.y,z=r.z,O=H;O.x=C.x*g*2,O.y=C.y*x*2,O.z=C.z*z*2;let b,S,R,_,T,V,q,E,A,I,N,U=(1-p)*e.Cartesian3.magnitude(t)/(.5*e.Cartesian3.magnitude(O)),P=0;do{U-=P,R=1/(1+U*g),_=1/(1+U*x),T=1/(1+U*z),V=R*R,q=_*_,E=T*T,A=V*R,I=q*_,N=E*T,b=m*V+y*q+w*E-1,S=m*A*g+y*I*x+w*N*z;P=b/(-2*S)}while(Math.abs(b)>a.CesiumMath.EPSILON12);return n.defined(o)?(o.x=s*R,o.y=c*_,o.z=l*T,o):new e.Cartesian3(s*R,c*_,l*T)}function J(t,e,a){this.longitude=n.defaultValue(t,0),this.latitude=n.defaultValue(e,0),this.height=n.defaultValue(a,0)}J.fromRadians=function(t,e,a,i){return a=n.defaultValue(a,0),n.defined(i)?(i.longitude=t,i.latitude=e,i.height=a,i):new J(t,e,a)},J.fromDegrees=function(t,e,n,i){return t=a.CesiumMath.toRadians(t),e=a.CesiumMath.toRadians(e),J.fromRadians(t,e,n,i)};const K=new e.Cartesian3,tt=new e.Cartesian3,et=new e.Cartesian3,nt=new e.Cartesian3(1/6378137,1/6378137,1/6356752.314245179),at=new e.Cartesian3(1/40680631590769,1/40680631590769,1/40408299984661.445),it=a.CesiumMath.EPSILON1;function rt(t,i,r,u){i=n.defaultValue(i,0),r=n.defaultValue(r,0),u=n.defaultValue(u,0),t._radii=new e.Cartesian3(i,r,u),t._radiiSquared=new e.Cartesian3(i*i,r*r,u*u),t._radiiToTheFourth=new e.Cartesian3(i*i*i*i,r*r*r*r,u*u*u*u),t._oneOverRadii=new e.Cartesian3(0===i?0:1/i,0===r?0:1/r,0===u?0:1/u),t._oneOverRadiiSquared=new e.Cartesian3(0===i?0:1/(i*i),0===r?0:1/(r*r),0===u?0:1/(u*u)),t._minimumRadius=Math.min(i,r,u),t._maximumRadius=Math.max(i,r,u),t._centerToleranceSquared=a.CesiumMath.EPSILON1,0!==t._radiiSquared.z&&(t._squaredXOverSquaredZ=t._radiiSquared.x/t._radiiSquared.z)}function ut(t,e,n){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,rt(this,t,e,n)}J.fromCartesian=function(t,i,r){const u=n.defined(i)?i.oneOverRadii:nt,o=n.defined(i)?i.oneOverRadiiSquared:at,s=Q(t,u,o,n.defined(i)?i._centerToleranceSquared:it,tt);if(!n.defined(s))return;let c=e.Cartesian3.multiplyComponents(s,o,K);c=e.Cartesian3.normalize(c,c);const l=e.Cartesian3.subtract(t,s,et),f=Math.atan2(c.y,c.x),d=Math.asin(c.z),h=a.CesiumMath.sign(e.Cartesian3.dot(l,t))*e.Cartesian3.magnitude(l);return n.defined(r)?(r.longitude=f,r.latitude=d,r.height=h,r):new J(f,d,h)},J.toCartesian=function(t,n,a){return e.Cartesian3.fromRadians(t.longitude,t.latitude,t.height,n,a)},J.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.longitude=t.longitude,e.latitude=t.latitude,e.height=t.height,e):new J(t.longitude,t.latitude,t.height)},J.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.longitude===e.longitude&&t.latitude===e.latitude&&t.height===e.height},J.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t.longitude-e.longitude)<=a&&Math.abs(t.latitude-e.latitude)<=a&&Math.abs(t.height-e.height)<=a},J.ZERO=Object.freeze(new J(0,0,0)),J.prototype.clone=function(t){return J.clone(this,t)},J.prototype.equals=function(t){return J.equals(this,t)},J.prototype.equalsEpsilon=function(t,e){return J.equalsEpsilon(this,t,e)},J.prototype.toString=function(){return`(${this.longitude}, ${this.latitude}, ${this.height})`},Object.defineProperties(ut.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),ut.clone=function(t,a){if(!n.defined(t))return;const i=t._radii;return n.defined(a)?(e.Cartesian3.clone(i,a._radii),e.Cartesian3.clone(t._radiiSquared,a._radiiSquared),e.Cartesian3.clone(t._radiiToTheFourth,a._radiiToTheFourth),e.Cartesian3.clone(t._oneOverRadii,a._oneOverRadii),e.Cartesian3.clone(t._oneOverRadiiSquared,a._oneOverRadiiSquared),a._minimumRadius=t._minimumRadius,a._maximumRadius=t._maximumRadius,a._centerToleranceSquared=t._centerToleranceSquared,a):new ut(i.x,i.y,i.z)},ut.fromCartesian3=function(t,e){return n.defined(e)||(e=new ut),n.defined(t)?(rt(e,t.x,t.y,t.z),e):e},ut.WGS84=Object.freeze(new ut(6378137,6378137,6356752.314245179)),ut.UNIT_SPHERE=Object.freeze(new ut(1,1,1)),ut.MOON=Object.freeze(new ut(a.CesiumMath.LUNAR_RADIUS,a.CesiumMath.LUNAR_RADIUS,a.CesiumMath.LUNAR_RADIUS)),ut.prototype.clone=function(t){return ut.clone(this,t)},ut.packedLength=e.Cartesian3.packedLength,ut.pack=function(t,a,i){return i=n.defaultValue(i,0),e.Cartesian3.pack(t._radii,a,i),a},ut.unpack=function(t,a,i){a=n.defaultValue(a,0);const r=e.Cartesian3.unpack(t,a);return ut.fromCartesian3(r,i)},ut.prototype.geocentricSurfaceNormal=e.Cartesian3.normalize,ut.prototype.geodeticSurfaceNormalCartographic=function(t,a){const i=t.longitude,r=t.latitude,u=Math.cos(r),o=u*Math.cos(i),s=u*Math.sin(i),c=Math.sin(r);return n.defined(a)||(a=new e.Cartesian3),a.x=o,a.y=s,a.z=c,e.Cartesian3.normalize(a,a)},ut.prototype.geodeticSurfaceNormal=function(t,i){if(!e.Cartesian3.equalsEpsilon(t,e.Cartesian3.ZERO,a.CesiumMath.EPSILON14))return n.defined(i)||(i=new e.Cartesian3),i=e.Cartesian3.multiplyComponents(t,this._oneOverRadiiSquared,i),e.Cartesian3.normalize(i,i)};const ot=new e.Cartesian3,st=new e.Cartesian3;ut.prototype.cartographicToCartesian=function(t,a){const i=ot,r=st;this.geodeticSurfaceNormalCartographic(t,i),e.Cartesian3.multiplyComponents(this._radiiSquared,i,r);const u=Math.sqrt(e.Cartesian3.dot(i,r));return e.Cartesian3.divideByScalar(r,u,r),e.Cartesian3.multiplyByScalar(i,t.height,i),n.defined(a)||(a=new e.Cartesian3),e.Cartesian3.add(r,i,a)},ut.prototype.cartographicArrayToCartesianArray=function(t,e){const a=t.length;n.defined(e)?e.length=a:e=new Array(a);for(let n=0;n<a;n++)e[n]=this.cartographicToCartesian(t[n],e[n]);return e};const ct=new e.Cartesian3,lt=new e.Cartesian3,ft=new e.Cartesian3;ut.prototype.cartesianToCartographic=function(t,i){const r=this.scaleToGeodeticSurface(t,lt);if(!n.defined(r))return;const u=this.geodeticSurfaceNormal(r,ct),o=e.Cartesian3.subtract(t,r,ft),s=Math.atan2(u.y,u.x),c=Math.asin(u.z),l=a.CesiumMath.sign(e.Cartesian3.dot(o,t))*e.Cartesian3.magnitude(o);return n.defined(i)?(i.longitude=s,i.latitude=c,i.height=l,i):new J(s,c,l)},ut.prototype.cartesianArrayToCartographicArray=function(t,e){const a=t.length;n.defined(e)?e.length=a:e=new Array(a);for(let n=0;n<a;++n)e[n]=this.cartesianToCartographic(t[n],e[n]);return e},ut.prototype.scaleToGeodeticSurface=function(t,e){return Q(t,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,e)},ut.prototype.scaleToGeocentricSurface=function(t,a){n.defined(a)||(a=new e.Cartesian3);const i=t.x,r=t.y,u=t.z,o=this._oneOverRadiiSquared,s=1/Math.sqrt(i*i*o.x+r*r*o.y+u*u*o.z);return e.Cartesian3.multiplyByScalar(t,s,a)},ut.prototype.transformPositionToScaledSpace=function(t,a){return n.defined(a)||(a=new e.Cartesian3),e.Cartesian3.multiplyComponents(t,this._oneOverRadii,a)},ut.prototype.transformPositionFromScaledSpace=function(t,a){return n.defined(a)||(a=new e.Cartesian3),e.Cartesian3.multiplyComponents(t,this._radii,a)},ut.prototype.equals=function(t){return this===t||n.defined(t)&&e.Cartesian3.equals(this._radii,t._radii)},ut.prototype.toString=function(){return this._radii.toString()},ut.prototype.getSurfaceNormalIntersectionWithZAxis=function(t,a,i){a=n.defaultValue(a,0);const r=this._squaredXOverSquaredZ;if(n.defined(i)||(i=new e.Cartesian3),i.x=0,i.y=0,i.z=t.z*(1-r),!(Math.abs(i.z)>=this._radii.z-a))return i};const dt=[.14887433898163,.43339539412925,.67940956829902,.86506336668898,.97390652851717,0],ht=[.29552422471475,.26926671930999,.21908636251598,.14945134915058,.066671344308684,0];function mt(t,e,n){const a=.5*(e+t),i=.5*(e-t);let r=0;for(let t=0;t<5;t++){const e=i*dt[t];r+=ht[t]*(n(a+e)+n(a-e))}return r*=i,r}function yt(t,e,a,i){this.west=n.defaultValue(t,0),this.south=n.defaultValue(e,0),this.east=n.defaultValue(a,0),this.north=n.defaultValue(i,0)}ut.prototype.surfaceArea=function(t){const e=t.west;let n=t.east;const i=t.south,r=t.north;for(;n<e;)n+=a.CesiumMath.TWO_PI;const u=this._radiiSquared,o=u.x,s=u.y,c=u.z,l=o*s;return mt(i,r,(function(t){const a=Math.cos(t),i=Math.sin(t);return Math.cos(t)*mt(e,n,(function(t){const e=Math.cos(t),n=Math.sin(t);return Math.sqrt(l*i*i+c*(s*e*e+o*n*n)*a*a)}))}))},Object.defineProperties(yt.prototype,{width:{get:function(){return yt.computeWidth(this)}},height:{get:function(){return yt.computeHeight(this)}}}),yt.packedLength=4,yt.pack=function(t,e,a){return a=n.defaultValue(a,0),e[a++]=t.west,e[a++]=t.south,e[a++]=t.east,e[a]=t.north,e},yt.unpack=function(t,e,a){return e=n.defaultValue(e,0),n.defined(a)||(a=new yt),a.west=t[e++],a.south=t[e++],a.east=t[e++],a.north=t[e],a},yt.computeWidth=function(t){let e=t.east;const n=t.west;return e<n&&(e+=a.CesiumMath.TWO_PI),e-n},yt.computeHeight=function(t){return t.north-t.south},yt.fromDegrees=function(t,e,i,r,u){return t=a.CesiumMath.toRadians(n.defaultValue(t,0)),e=a.CesiumMath.toRadians(n.defaultValue(e,0)),i=a.CesiumMath.toRadians(n.defaultValue(i,0)),r=a.CesiumMath.toRadians(n.defaultValue(r,0)),n.defined(u)?(u.west=t,u.south=e,u.east=i,u.north=r,u):new yt(t,e,i,r)},yt.fromRadians=function(t,e,a,i,r){return n.defined(r)?(r.west=n.defaultValue(t,0),r.south=n.defaultValue(e,0),r.east=n.defaultValue(a,0),r.north=n.defaultValue(i,0),r):new yt(t,e,a,i)},yt.fromCartographicArray=function(t,e){let i=Number.MAX_VALUE,r=-Number.MAX_VALUE,u=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,c=-Number.MAX_VALUE;for(let e=0,n=t.length;e<n;e++){const n=t[e];i=Math.min(i,n.longitude),r=Math.max(r,n.longitude),s=Math.min(s,n.latitude),c=Math.max(c,n.latitude);const l=n.longitude>=0?n.longitude:n.longitude+a.CesiumMath.TWO_PI;u=Math.min(u,l),o=Math.max(o,l)}return r-i>o-u&&(i=u,r=o,r>a.CesiumMath.PI&&(r-=a.CesiumMath.TWO_PI),i>a.CesiumMath.PI&&(i-=a.CesiumMath.TWO_PI)),n.defined(e)?(e.west=i,e.south=s,e.east=r,e.north=c,e):new yt(i,s,r,c)},yt.fromCartesianArray=function(t,e,i){e=n.defaultValue(e,ut.WGS84);let r=Number.MAX_VALUE,u=-Number.MAX_VALUE,o=Number.MAX_VALUE,s=-Number.MAX_VALUE,c=Number.MAX_VALUE,l=-Number.MAX_VALUE;for(let n=0,i=t.length;n<i;n++){const i=e.cartesianToCartographic(t[n]);r=Math.min(r,i.longitude),u=Math.max(u,i.longitude),c=Math.min(c,i.latitude),l=Math.max(l,i.latitude);const f=i.longitude>=0?i.longitude:i.longitude+a.CesiumMath.TWO_PI;o=Math.min(o,f),s=Math.max(s,f)}return u-r>s-o&&(r=o,u=s,u>a.CesiumMath.PI&&(u-=a.CesiumMath.TWO_PI),r>a.CesiumMath.PI&&(r-=a.CesiumMath.TWO_PI)),n.defined(i)?(i.west=r,i.south=c,i.east=u,i.north=l,i):new yt(r,c,u,l)},yt.clone=function(t,e){if(n.defined(t))return n.defined(e)?(e.west=t.west,e.south=t.south,e.east=t.east,e.north=t.north,e):new yt(t.west,t.south,t.east,t.north)},yt.equalsEpsilon=function(t,e,a){return a=n.defaultValue(a,0),t===e||n.defined(t)&&n.defined(e)&&Math.abs(t.west-e.west)<=a&&Math.abs(t.south-e.south)<=a&&Math.abs(t.east-e.east)<=a&&Math.abs(t.north-e.north)<=a},yt.prototype.clone=function(t){return yt.clone(this,t)},yt.prototype.equals=function(t){return yt.equals(this,t)},yt.equals=function(t,e){return t===e||n.defined(t)&&n.defined(e)&&t.west===e.west&&t.south===e.south&&t.east===e.east&&t.north===e.north},yt.prototype.equalsEpsilon=function(t,e){return yt.equalsEpsilon(this,t,e)},yt.validate=function(t){},yt.southwest=function(t,e){return n.defined(e)?(e.longitude=t.west,e.latitude=t.south,e.height=0,e):new J(t.west,t.south)},yt.northwest=function(t,e){return n.defined(e)?(e.longitude=t.west,e.latitude=t.north,e.height=0,e):new J(t.west,t.north)},yt.northeast=function(t,e){return n.defined(e)?(e.longitude=t.east,e.latitude=t.north,e.height=0,e):new J(t.east,t.north)},yt.southeast=function(t,e){return n.defined(e)?(e.longitude=t.east,e.latitude=t.south,e.height=0,e):new J(t.east,t.south)},yt.center=function(t,e){let i=t.east;const r=t.west;i<r&&(i+=a.CesiumMath.TWO_PI);const u=a.CesiumMath.negativePiToPi(.5*(r+i)),o=.5*(t.south+t.north);return n.defined(e)?(e.longitude=u,e.latitude=o,e.height=0,e):new J(u,o)},yt.intersection=function(t,e,i){let r=t.east,u=t.west,o=e.east,s=e.west;r<u&&o>0?r+=a.CesiumMath.TWO_PI:o<s&&r>0&&(o+=a.CesiumMath.TWO_PI),r<u&&s<0?s+=a.CesiumMath.TWO_PI:o<s&&u<0&&(u+=a.CesiumMath.TWO_PI);const c=a.CesiumMath.negativePiToPi(Math.max(u,s)),l=a.CesiumMath.negativePiToPi(Math.min(r,o));if((t.west<t.east||e.west<e.east)&&l<=c)return;const f=Math.max(t.south,e.south),d=Math.min(t.north,e.north);return f>=d?void 0:n.defined(i)?(i.west=c,i.south=f,i.east=l,i.north=d,i):new yt(c,f,l,d)},yt.simpleIntersection=function(t,e,a){const i=Math.max(t.west,e.west),r=Math.max(t.south,e.south),u=Math.min(t.east,e.east),o=Math.min(t.north,e.north);if(!(r>=o||i>=u))return n.defined(a)?(a.west=i,a.south=r,a.east=u,a.north=o,a):new yt(i,r,u,o)},yt.union=function(t,e,i){n.defined(i)||(i=new yt);let r=t.east,u=t.west,o=e.east,s=e.west;r<u&&o>0?r+=a.CesiumMath.TWO_PI:o<s&&r>0&&(o+=a.CesiumMath.TWO_PI),r<u&&s<0?s+=a.CesiumMath.TWO_PI:o<s&&u<0&&(u+=a.CesiumMath.TWO_PI);const c=a.CesiumMath.negativePiToPi(Math.min(u,s)),l=a.CesiumMath.negativePiToPi(Math.max(r,o));return i.west=c,i.south=Math.min(t.south,e.south),i.east=l,i.north=Math.max(t.north,e.north),i},yt.expand=function(t,e,a){return n.defined(a)||(a=new yt),a.west=Math.min(t.west,e.longitude),a.south=Math.min(t.south,e.latitude),a.east=Math.max(t.east,e.longitude),a.north=Math.max(t.north,e.latitude),a},yt.contains=function(t,e){let n=e.longitude;const i=e.latitude,r=t.west;let u=t.east;return u<r&&(u+=a.CesiumMath.TWO_PI,n<0&&(n+=a.CesiumMath.TWO_PI)),(n>r||a.CesiumMath.equalsEpsilon(n,r,a.CesiumMath.EPSILON14))&&(n<u||a.CesiumMath.equalsEpsilon(n,u,a.CesiumMath.EPSILON14))&&i>=t.south&&i<=t.north};const wt=new J;yt.subsample=function(t,e,i,r){e=n.defaultValue(e,ut.WGS84),i=n.defaultValue(i,0),n.defined(r)||(r=[]);let u=0;const o=t.north,s=t.south,c=t.east,l=t.west,f=wt;f.height=i,f.longitude=l,f.latitude=o,r[u]=e.cartographicToCartesian(f,r[u]),u++,f.longitude=c,r[u]=e.cartographicToCartesian(f,r[u]),u++,f.latitude=s,r[u]=e.cartographicToCartesian(f,r[u]),u++,f.longitude=l,r[u]=e.cartographicToCartesian(f,r[u]),u++,f.latitude=o<0?o:s>0?s:0;for(let n=1;n<8;++n)f.longitude=-Math.PI+n*a.CesiumMath.PI_OVER_TWO,yt.contains(t,f)&&(r[u]=e.cartographicToCartesian(f,r[u]),u++);return 0===f.latitude&&(f.longitude=l,r[u]=e.cartographicToCartesian(f,r[u]),u++,f.longitude=c,r[u]=e.cartographicToCartesian(f,r[u]),u++),r.length=u,r},yt.subsection=function(t,e,i,r,u,o){if(n.defined(o)||(o=new yt),t.west<=t.east){const n=t.east-t.west;o.west=t.west+e*n,o.east=t.west+r*n}else{const n=a.CesiumMath.TWO_PI+t.east-t.west;o.west=a.CesiumMath.negativePiToPi(t.west+e*n),o.east=a.CesiumMath.negativePiToPi(t.west+r*n)}const s=t.north-t.south;return o.south=t.south+i*s,o.north=t.south+u*s,1===e&&(o.west=t.east),1===r&&(o.east=t.east),1===i&&(o.south=t.north),1===u&&(o.north=t.north),o},yt.MAX_VALUE=Object.freeze(new yt(-Math.PI,-a.CesiumMath.PI_OVER_TWO,Math.PI,a.CesiumMath.PI_OVER_TWO)),t.Cartesian4=r,t.Cartographic=J,t.Ellipsoid=ut,t.Matrix2=h,t.Matrix3=g,t.Matrix4=N,t.Rectangle=yt}));
