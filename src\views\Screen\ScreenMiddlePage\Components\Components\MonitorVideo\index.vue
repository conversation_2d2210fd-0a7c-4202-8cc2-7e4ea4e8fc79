<template>
  <div class="monitor-video-container" v-drag="{ draggableClass: 'title', onmousedown: onMouseDown, onmouseup: onMouseUp }">
    <div class="title">监控视频</div>
    <div class="cancelIcon" @click="handleCloseClick"></div>

    <div class="video-container" id="videoBox">
      <div class="video-loading-container"></div>
    </div>
    <div class="top-video-container" v-if="isShowVideo"></div>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted } from 'vue'

  import { toUe5 } from '@/hooks/useUE/tools'

  import { provideTools } from '@/utils/provideMap.js'

  import vDrag from '@Common/directives/drag/index.js'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const isShowVideo = ref(true)
  const onMouseDown = (e) => {
    removeVideo()
  }
  const onMouseUp = (e, data) => {
    console.log(e, data)
    nextTick(() => {
      addVideo(parseInt(data.left), parseInt(data.top))
    })
  }
  // const handleClose = inject('handleClose')
  const handleClose = provideTools.handleClose.inject()

  const handleCloseClick = () => {
    handleClose()
    removeVideo()
  }

  onMounted(() => {
    nextTick(() => {
      addVideo()
    })
  })

  onUnmounted(() => {
    removeVideo()
  })
  const state = reactive({
    left: 0,
    top: 0,
    right: 0,
    bottom: 0
  })

  const addVideo = (offsetX = 0, offsetY = 0) => {
    const screenWidth = 1920
    const screenHeight = 1080
    const width = 496
    const height = 320
    const left = 633 + offsetX + 23 - 5
    const top = 0 + 89 + offsetY + 98 - 16
    const right = left + width
    const bottom = top + height
    state.left = (left / screenWidth) * 100 + '%'
    state.top = (top / screenHeight) * 100 + '%'
    state.right = (1 - right / screenWidth) * 100 + '%'
    state.bottom = (1 - bottom / screenHeight) * 100 + '%'
    console.log(left, top, right, bottom, state)
    isShowVideo.value = true
    const obj = {
      videoList: [
        // 使用列表形式可兼容多个视频同时传递的情况
        {
          id: 111, // 视频id，用于整个界面有多个视频时的控制，由前端决定
          state: 1, // 视频界面状态：0：关闭，1：显示（为0即关闭界面时可以不传下列参数
          isAutoPlay: true, // 是否自动开始播放
          // url: 'http://172.22.49.1:10000/hls/test2.m3u8', // 视频播放地址
          url: props?.data?.videoUrl, // 视频播放地址
          sizeOffset: [left / screenWidth, top / screenHeight, right / screenWidth, bottom / screenHeight] // 视频在界面中的位置，[left, top, right, bottom]
          // sizeOffset: [left / screenWidth, top / screenHeight, 0.7, 0.8] // 视频在界面中的位置，[left, top, right, bottom]
        }
      ]
    }
    toUe5('playVideo', obj)
  }

  const removeVideo = () => {
    isShowVideo.value = false
    const obj = {
      videoList: [
        {
          id: 111, // 视频id，用于整个界面有多个视频时的控制，由前端决定
          state: 0 // 视频界面状态：0：关闭，1：显示（为0即关闭界面时可以不传下列参数
        }
      ]
    }
    toUe5('playVideo', obj)
  }
</script>

<style lang="scss" scoped>
  .monitor-video-container {
    position: absolute;
    width: 544px;
    height: 442px;
    padding: 20px;
    background: url('@/assets/<EMAIL>');
    background-size: cover;
    .title {
      width: 100%;
      height: 30px;
      font-size: 21px;
      line-height: 30px;
      color: #ffffff;
      cursor: move;
    }
    .video-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 502px;
      height: 333px;
      margin-top: 24px;
      background: url('@/assets/<EMAIL>');
      background-size: cover;
      .video-loading-container {
        width: 478px;
        height: 307px;
        background: url('@/assets/ScreenMiddle/Traffic/<EMAIL>');
        background-size: cover;
      }
    }
    .top-video-container {
      position: fixed;

      // top: v-bind('state.top');
      // right: v-bind('state.right');
      // /* stylelint-disable-next-line declaration-block-no-redundant-longhand-properties */
      // bottom: v-bind('state.bottom');
      // left: v-bind('state.left'); /* stylelint-disable-line */
      // background: red;
    }
    .cancelIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 44px;
      height: 44px;
      cursor: pointer;
      background: url('@/assets/ScreenMiddle/Emergency/close-icon.png') no-repeat center;
      background-size: 22px 22px;
    }
  }
</style>
