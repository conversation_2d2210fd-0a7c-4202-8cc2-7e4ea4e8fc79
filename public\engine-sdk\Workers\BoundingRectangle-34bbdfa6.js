define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a"],(function(e,t,n,i,h,r,a){"use strict";function c(e,n,i,h){this.x=t.defaultValue(e,0),this.y=t.defaultValue(n,0),this.width=t.defaultValue(i,0),this.height=t.defaultValue(h,0)}c.packedLength=4,c.pack=function(e,i,h){return n.Check.typeOf.object("value",e),n.Check.defined("array",i),h=t.defaultValue(h,0),i[h++]=e.x,i[h++]=e.y,i[h++]=e.width,i[h]=e.height,i},c.unpack=function(e,i,h){return n.Check.defined("array",e),i=t.defaultValue(i,0),t.defined(h)||(h=new c),h.x=e[i++],h.y=e[i++],h.width=e[i++],h.height=e[i],h},c.fromPoints=function(e,n){if(t.defined(n)||(n=new c),!t.defined(e)||0===e.length)return n.x=0,n.y=0,n.width=0,n.height=0,n;for(var i=e.length,h=e[0].x,r=e[0].y,a=e[0].x,d=e[0].y,f=1;f<i;f++){var u=e[f],o=u.x,y=u.y;h=Math.min(o,h),a=Math.max(o,a),r=Math.min(y,r),d=Math.max(y,d)}return n.x=h,n.y=r,n.width=a-h,n.height=d-r,n};var d=new r.GeographicProjection,f=new h.Cartographic,u=new h.Cartographic;c.fromRectangle=function(e,n,r){if(t.defined(r)||(r=new c),!t.defined(e))return r.x=0,r.y=0,r.width=0,r.height=0,r;n=t.defaultValue(n,d);var a=n.project(h.Rectangle.southwest(e,f)),o=n.project(h.Rectangle.northeast(e,u));return i.Cartesian2.subtract(o,a,o),r.x=a.x,r.y=a.y,r.width=o.x,r.height=o.y,r},c.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n.x=e.x,n.y=e.y,n.width=e.width,n.height=e.height,n):new c(e.x,e.y,e.width,e.height)},c.union=function(e,i,h){n.Check.typeOf.object("left",e),n.Check.typeOf.object("right",i),t.defined(h)||(h=new c);var r=Math.min(e.x,i.x),a=Math.min(e.y,i.y),d=Math.max(e.x+e.width,i.x+i.width),f=Math.max(e.y+e.height,i.y+i.height);return h.x=r,h.y=a,h.width=d-r,h.height=f-a,h},c.expand=function(e,t,i){n.Check.typeOf.object("rectangle",e),n.Check.typeOf.object("point",t),i=c.clone(e,i);var h=t.x-i.x,r=t.y-i.y;return h>i.width?i.width=h:h<0&&(i.width-=h,i.x=t.x),r>i.height?i.height=r:r<0&&(i.height-=r,i.y=t.y),i},c.intersect=function(e,t){n.Check.typeOf.object("left",e),n.Check.typeOf.object("right",t);var i=e.x,h=e.y,r=t.x,c=t.y;return i>r+t.width||i+e.width<r||h+e.height<c||h>c+t.height?a.Intersect.OUTSIDE:a.Intersect.INTERSECTING},c.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height},c.prototype.clone=function(e){return c.clone(this,e)},c.prototype.intersect=function(e){return c.intersect(this,e)},c.prototype.equals=function(e){return c.equals(this,e)},e.BoundingRectangle=c}));