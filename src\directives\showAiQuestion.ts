import type { Directive } from 'vue'
import { watch } from 'vue'
import lib from '@/utils/lib'

const vShowAiQuestion: Directive = {
  mounted(el: HTMLElement) {
    // 使用watch而不是watchEffect，更明确地监听store变化
    el._unwatch = watch(
      () => lib.store().storeScreenData.showAIQuestion,
      (showAIQuestion) => {
        el.classList.toggle('cursor-help', showAIQuestion)
      },
      { immediate: true } // 立即执行一次
    )
  },

  unmounted(el: HTMLElement) {
    // 清理监听器
    if (el._unwatch) {
      el._unwatch()
      delete el._unwatch
    }
  }
}

export default vShowAiQuestion
