define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./EllipsoidGeometry-b5925121"],(function(e,t,r,i,a,o,n,s,c,d,l,m,p,u,f,y,G){"use strict";function k(t){var i=e.defaultValue(t.radius,1),a=new r.Cartesian3(i,i,i),o={radii:a,stackPartitions:t.stackPartitions,slicePartitions:t.slicePartitions,vertexFormat:t.vertexFormat};this._ellipsoidGeometry=new G.EllipsoidGeometry(o),this._workerName="createSphereGeometry"}k.packedLength=G.EllipsoidGeometry.packedLength,k.pack=function(e,r,i){return t.Check.typeOf.object("value",e),G.EllipsoidGeometry.pack(e._ellipsoidGeometry,r,i)};var v=new G.EllipsoidGeometry,b={radius:void 0,radii:new r.Cartesian3,vertexFormat:new y.VertexFormat,stackPartitions:void 0,slicePartitions:void 0};function x(t,r){return e.defined(r)&&(t=k.unpack(t,r)),k.createGeometry(t)}return k.unpack=function(t,i,a){var o=G.EllipsoidGeometry.unpack(t,i,v);return b.vertexFormat=y.VertexFormat.clone(o._vertexFormat,b.vertexFormat),b.stackPartitions=o._stackPartitions,b.slicePartitions=o._slicePartitions,e.defined(a)?(r.Cartesian3.clone(o._radii,b.radii),a._ellipsoidGeometry=new G.EllipsoidGeometry(b),a):(b.radius=o._radii.x,new k(b))},k.createGeometry=function(e){return G.EllipsoidGeometry.createGeometry(e._ellipsoidGeometry)},x}));