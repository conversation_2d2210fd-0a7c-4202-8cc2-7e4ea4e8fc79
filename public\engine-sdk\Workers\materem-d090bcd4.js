define(["exports"],(function(t){"use strict";var e=function(){var t="undefined"!==typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!==typeof __filename&&(t=t||__filename),function(e){e=e||{};var r;e="undefined"!==typeof e?e:{};e["ready"]=new Promise((function(t,e){r=t}));var n=!1,p=!1;e["onRuntimeInitialized"]=function(){n=!0,p&&"function"===typeof e["onModuleLoaded"]&&e["onModuleLoaded"](e)},e["onModuleParsed"]=function(){p=!0,n&&"function"===typeof e["onModuleLoaded"]&&e["onModuleLoaded"](e)};var i,o={};for(i in e)e.hasOwnProperty(i)&&(o[i]=e[i]);var _="./this.program",a=function(t,e){throw e},c=!1,u=!1,l=!1,s=!1;c="object"===typeof window,u="function"===typeof importScripts,l="object"===typeof process&&"object"===typeof process.versions&&"string"===typeof process.versions.node,s=!c&&!l&&!u;var y,d,m,f,b="";function S(t){return e["locateFile"]?e["locateFile"](t,b):b+t}l?(b=u?require("path").dirname(b)+"/":__dirname+"/",y=function(t,e){return m||(m=require("fs")),f||(f=require("path")),t=f["normalize"](t),m["readFileSync"](t,e?null:"utf8")},d=function(t){var e=y(t,!0);return e.buffer||(e=new Uint8Array(e)),C(e.buffer),e},process["argv"].length>1&&(_=process["argv"][1].replace(/\\/g,"/")),process["argv"].slice(2),process["on"]("uncaughtException",(function(t){if(!(t instanceof ao))throw t})),process["on"]("unhandledRejection",dt),a=function(t){process["exit"](t)},e["inspect"]=function(){return"[Emscripten Module object]"}):s?("undefined"!=typeof read&&(y=function(t){return read(t)}),d=function(t){var e;return"function"===typeof readbuffer?new Uint8Array(readbuffer(t)):(e=read(t,"binary"),C("object"===typeof e),e)},"undefined"!=typeof scriptArgs?scriptArgs:"undefined"!=typeof arguments&&arguments,"function"===typeof quit&&(a=function(t){quit(t)}),"undefined"!==typeof print&&("undefined"===typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!==typeof printErr?printErr:print)):(c||u)&&(u?b=self.location.href:document.currentScript&&(b=document.currentScript.src),t&&(b=t),b=0!==b.indexOf("blob:")?b.substr(0,b.lastIndexOf("/")+1):"",y=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},u&&(d=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}));var v=e["print"]||console.log.bind(console),h=e["printErr"]||console.warn.bind(console);for(i in o)o.hasOwnProperty(i)&&(e[i]=o[i]);o=null,e["arguments"]&&e["arguments"],e["thisProgram"]&&(_=e["thisProgram"]),e["quit"]&&(a=e["quit"]);var B,g,L,P=0,I=function(t){P=t},j=function(){return P};e["wasmBinary"]&&(B=e["wasmBinary"]),e["noExitRuntime"]&&(g=e["noExitRuntime"]),"object"!==typeof WebAssembly&&h("no native wasm support detected");var G=new WebAssembly.Table({initial:1611,maximum:1611,element:"anyfunc"}),x=!1;function C(t,e){t||dt("Assertion failed: "+e)}function R(t){var r=e["_"+t];return C(r,"Cannot call unknown function "+t+", make sure it is exported"),r}function M(t,e,r,n,p){var i={string:function(t){var e=0;if(null!==t&&void 0!==t&&0!==t){var r=1+(t.length<<2);e=Di(r),E(t,e,r)}return e},array:function(t){var e=Di(t.length);return O(t,e),e}};function o(t){return"string"===e?D(t):"boolean"===e?Boolean(t):t}var _=R(t),a=[],c=0;if(n)for(var u=0;u<n.length;u++){var l=i[r[u]];l?(0===c&&(c=Ti()),a[u]=l(n[u])):a[u]=n[u]}var s=_.apply(null,a);return s=o(s),0!==c&&Ai(c),s}function V(t,e,r,n){r=r||[];var p=r.every((function(t){return"number"===t})),i="string"!==e;return i&&p&&!n?R(t):function(){return M(t,e,r,arguments)}}var w="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;function T(t,e,r){var n=e+r,p=e;while(t[p]&&!(p>=n))++p;if(p-e>16&&t.subarray&&w)return w.decode(t.subarray(e,p));var i="";while(e<p){var o=t[e++];if(128&o){var _=63&t[e++];if(192!=(224&o)){var a=63&t[e++];if(o=224==(240&o)?(15&o)<<12|_<<6|a:(7&o)<<18|_<<12|a<<6|63&t[e++],o<65536)i+=String.fromCharCode(o);else{var c=o-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&o)<<6|_)}else i+=String.fromCharCode(o)}return i}function D(t,e){return t?T(W,t,e):""}function A(t,e,r,n){if(!(n>0))return 0;for(var p=r,i=r+n-1,o=0;o<t.length;++o){var _=t.charCodeAt(o);if(_>=55296&&_<=57343){var a=t.charCodeAt(++o);_=65536+((1023&_)<<10)|1023&a}if(_<=127){if(r>=i)break;e[r++]=_}else if(_<=2047){if(r+1>=i)break;e[r++]=192|_>>6,e[r++]=128|63&_}else if(_<=65535){if(r+2>=i)break;e[r++]=224|_>>12,e[r++]=128|_>>6&63,e[r++]=128|63&_}else{if(r+3>=i)break;e[r++]=240|_>>18,e[r++]=128|_>>12&63,e[r++]=128|_>>6&63,e[r++]=128|63&_}}return e[r]=0,r-p}function E(t,e,r){return A(t,W,e,r)}function N(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&t.charCodeAt(++r)),n<=127?++e:e+=n<=2047?2:n<=65535?3:4}return e}function O(t,e){F.set(t,e)}function k(t,e,r){for(var n=0;n<t.length;++n)F[e++>>0]=t.charCodeAt(n);r||(F[e>>0]=0)}var z,F,W,H,U,Y,q=65536;function X(t,e){return t%e>0&&(t+=e-t%e),t}function J(t){z=t,e["HEAP8"]=F=new Int8Array(t),e["HEAP16"]=new Int16Array(t),e["HEAP32"]=H=new Int32Array(t),e["HEAPU8"]=W=new Uint8Array(t),e["HEAPU16"]=new Uint16Array(t),e["HEAPU32"]=new Uint32Array(t),e["HEAPF32"]=U=new Float32Array(t),e["HEAPF64"]=Y=new Float64Array(t)}var Z=5328992,K=85936,Q=e["INITIAL_MEMORY"]||33554432;function $(t){while(t.length>0){var r=t.shift();if("function"!=typeof r){var n=r.func;"number"===typeof n?void 0===r.arg?e["dynCall_v"](n):e["dynCall_vi"](n,r.arg):n(void 0===r.arg?null:r.arg)}else r(e)}}L=e["wasmMemory"]?e["wasmMemory"]:new WebAssembly.Memory({initial:Q/q,maximum:2147483648/q}),L&&(z=L.buffer),Q=z.byteLength,J(z),H[K>>2]=Z;var tt=[],et=[],rt=[],nt=[];function pt(){if(e["preRun"]){"function"==typeof e["preRun"]&&(e["preRun"]=[e["preRun"]]);while(e["preRun"].length)at(e["preRun"].shift())}$(tt)}function it(){$(et)}function ot(){$(rt)}function _t(){if(e["postRun"]){"function"==typeof e["postRun"]&&(e["postRun"]=[e["postRun"]]);while(e["postRun"].length)ct(e["postRun"].shift())}$(nt)}function at(t){tt.unshift(t)}function ct(t){nt.unshift(t)}var ut=0,lt=null;function st(t){ut++,e["monitorRunDependencies"]&&e["monitorRunDependencies"](ut)}function yt(t){if(ut--,e["monitorRunDependencies"]&&e["monitorRunDependencies"](ut),0==ut&&lt){var r=lt;lt=null,r()}}function dt(t){throw e["onAbort"]&&e["onAbort"](t),t+="",v(t),h(t),x=!0,t="abort("+t+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(t)}function mt(t,e){return String.prototype.startsWith?t.startsWith(e):0===t.indexOf(e)}e["preloadedImages"]={},e["preloadedAudios"]={};var ft="data:application/octet-stream;base64,";function bt(t){return mt(t,ft)}var St="file://";function vt(t){return mt(t,St)}var ht="materem.wasm";function Bt(){try{if(B)return new Uint8Array(B);if(d)return d(ht);throw"both async and sync fetching of the wasm failed"}catch(h){dt(h)}}function gt(){return B||!c&&!u||"function"!==typeof fetch||vt(ht)?new Promise((function(t,e){t(Bt())})):fetch(ht,{credentials:"same-origin"}).then((function(t){if(!t["ok"])throw"failed to load wasm binary file at '"+ht+"'";return t["arrayBuffer"]()})).catch((function(){return Bt()}))}function Lt(){var t={a:be};function r(t,r){var n=t.exports;e["asm"]=n,yt()}function n(t){r(t["instance"])}function p(e){return gt().then((function(e){return WebAssembly.instantiate(e,t)})).then(e,(function(t){h("failed to asynchronously prepare wasm: "+t),dt(t)}))}function i(){if(B||"function"!==typeof WebAssembly.instantiateStreaming||bt(ht)||vt(ht)||"function"!==typeof fetch)return p(n);fetch(ht,{credentials:"same-origin"}).then((function(e){var r=WebAssembly.instantiateStreaming(e,t);return r.then(n,(function(t){h("wasm streaming compile failed: "+t),h("falling back to ArrayBuffer instantiation"),p(n)}))}))}if(st(),e["instantiateWasm"])try{var o=e["instantiateWasm"](t,r);return o}catch(_){return h("Module.instantiateWasm callback failed with error: "+_),!1}return i(),{}}function Pt(t){return ni(t)}bt(ht)||(ht=S(ht)),et.push({func:function(){he()}});var It={},jt=[];function Gt(t){if(t){var e=It[t];e.refcount++}}function xt(t){if(!t||It[t])return t;for(var e in It)for(var r=+e,n=It[r].adjusted,p=n.length,i=0;i<p;i++)if(n[i]===t)return r;return t}function Ct(t){var e=It[t];return e&&!e.caught&&(e.caught=!0,oi.uncaught_exceptions--),e&&(e.rethrown=!1),jt.push(t),Gt(xt(t)),t}var Rt=0;function Mt(t){return ri(t)}function Vt(t){if(t){var r=It[t];r.refcount--,0!==r.refcount||r.rethrown||(r.destructor&&e["dynCall_ii"](r.destructor,t),delete It[t],Mt(t))}}function wt(){ii(0);var t=jt.pop();t&&(Vt(xt(t)),Rt=0)}function Tt(){var t=Rt;if(!t)return 0|(I(0),0);var e=It[t],r=e.type;if(!r)return 0|(I(0),t);var n=Array.prototype.slice.call(arguments),p=(ai(r),86096);H[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&_i(n[i],r,t))return t=H[t>>2],e.adjusted.push(t),0|(I(n[i]),t);return t=H[t>>2],0|(I(r),t)}function Dt(){var t=Rt;if(!t)return 0|(I(0),0);var e=It[t],r=e.type;if(!r)return 0|(I(0),t);var n=Array.prototype.slice.call(arguments),p=(ai(r),86096);H[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&_i(n[i],r,t))return t=H[t>>2],e.adjusted.push(t),0|(I(n[i]),t);return t=H[t>>2],0|(I(r),t)}function At(){var t=jt.pop();throw t=xt(t),It[t].rethrown||(jt.push(t),It[t].rethrown=!0),Rt=t,t}function Et(t,e,r){throw It[t]={ptr:t,adjusted:[t],type:e,destructor:r,refcount:0,caught:!1,rethrown:!1},Rt=t,"uncaught_exception"in oi?oi.uncaught_exceptions++:oi.uncaught_exceptions=1,t}function Nt(){return oi.uncaught_exceptions}function Ot(t){return H[pi()>>2]=t,t}function kt(t,e){return Ot(63),-1}function zt(t){throw Rt||(Rt=t),t}var Ft={mappings:{},buffers:[null,[],[]],printChar:function(t,e){var r=Ft.buffers[t];0===e||10===e?((1===t?v:h)(T(r,0)),r.length=0):r.push(e)},varargs:void 0,get:function(){Ft.varargs+=4;var t=H[Ft.varargs-4>>2];return t},getStr:function(t){var e=D(t);return e},get64:function(t,e){return t}};function Wt(t,e){if(-1===(0|t)||0===e)return-28;var r=Ft.mappings[t];return r?(e===r.len&&(Ft.mappings[t]=null,r.allocated&&ri(r.malloc)),0):0}function Ht(t,e){return Wt(t,e)}function Ut(){dt()}function Yt(t,e,r){W.copyWithin(t,e,e+r)}function qt(){return W.length}function Xt(t){try{return L.grow(t-z.byteLength+65535>>>16),J(L.buffer),1}catch(e){}}function Jt(t){t>>>=0;var e=qt(),r=65536,n=2147483648;if(t>n)return!1;for(var p=16777216,i=1;i<=4;i*=2){var o=e*(1+.2/i);o=Math.min(o,t+100663296);var _=Math.min(n,X(Math.max(p,t,o),r)),a=Xt(_);if(a)return!0}return!1}var Zt={};function Kt(){return _||"./this.program"}function Qt(){if(!Qt.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"===typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:Kt()};for(var e in Zt)t[e]=Zt[e];var r=[];for(var e in t)r.push(e+"="+t[e]);Qt.strings=r}return Qt.strings}function $t(t,e){var r=0;return Qt().forEach((function(n,p){var i=e+r;H[t+4*p>>2]=i,k(n,i),r+=n.length+1})),0}function te(t,e){var r=Qt();H[t>>2]=r.length;var n=0;return r.forEach((function(t){n+=t.length+1})),H[e>>2]=n,0}function ee(t){uo(t)}function re(t){return 0}function ne(t,e,r,n){var p=Ft.getStreamFromFD(t),i=Ft.doReadv(p,e,r);return H[n>>2]=i,0}function pe(t,e,r,n,p){}function ie(t,e,r,n){for(var p=0,i=0;i<r;i++){for(var o=H[e+8*i>>2],_=H[e+(8*i+4)>>2],a=0;a<_;a++)Ft.printChar(t,W[o+a]);p+=_}return H[n>>2]=p,0}function oe(){return 0|j()}function _e(t){return t}function ae(t){return t%4===0&&(t%100!==0||t%400===0)}function ce(t,e){for(var r=0,n=0;n<=e;r+=t[n++]);return r}var ue=[31,29,31,30,31,30,31,31,30,31,30,31],le=[31,28,31,30,31,30,31,31,30,31,30,31];function se(t,e){var r=new Date(t.getTime());while(e>0){var n=ae(r.getFullYear()),p=r.getMonth(),i=(n?ue:le)[p];if(!(e>i-r.getDate()))return r.setDate(r.getDate()+e),r;e-=i-r.getDate()+1,r.setDate(1),p<11?r.setMonth(p+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1))}return r}function ye(t,e,r,n){var p=H[n+40>>2],i={tm_sec:H[n>>2],tm_min:H[n+4>>2],tm_hour:H[n+8>>2],tm_mday:H[n+12>>2],tm_mon:H[n+16>>2],tm_year:H[n+20>>2],tm_wday:H[n+24>>2],tm_yday:H[n+28>>2],tm_isdst:H[n+32>>2],tm_gmtoff:H[n+36>>2],tm_zone:p?D(p):""},o=D(r),_={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var a in _)o=o.replace(new RegExp(a,"g"),_[a]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],u=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(t,e,r){var n="number"===typeof t?t.toString():t||"";while(n.length<e)n=r[0]+n;return n}function s(t,e){return l(t,e,"0")}function y(t,e){function r(t){return t<0?-1:t>0?1:0}var n;return 0===(n=r(t.getFullYear()-e.getFullYear()))&&0===(n=r(t.getMonth()-e.getMonth()))&&(n=r(t.getDate()-e.getDate())),n}function d(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function m(t){var e=se(new Date(t.tm_year+1900,0,1),t.tm_yday),r=new Date(e.getFullYear(),0,4),n=new Date(e.getFullYear()+1,0,4),p=d(r),i=d(n);return y(p,e)<=0?y(i,e)<=0?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var f={"%a":function(t){return c[t.tm_wday].substring(0,3)},"%A":function(t){return c[t.tm_wday]},"%b":function(t){return u[t.tm_mon].substring(0,3)},"%B":function(t){return u[t.tm_mon]},"%C":function(t){var e=t.tm_year+1900;return s(e/100|0,2)},"%d":function(t){return s(t.tm_mday,2)},"%e":function(t){return l(t.tm_mday,2," ")},"%g":function(t){return m(t).toString().substring(2)},"%G":function(t){return m(t)},"%H":function(t){return s(t.tm_hour,2)},"%I":function(t){var e=t.tm_hour;return 0==e?e=12:e>12&&(e-=12),s(e,2)},"%j":function(t){return s(t.tm_mday+ce(ae(t.tm_year+1900)?ue:le,t.tm_mon-1),3)},"%m":function(t){return s(t.tm_mon+1,2)},"%M":function(t){return s(t.tm_min,2)},"%n":function(){return"\n"},"%p":function(t){return t.tm_hour>=0&&t.tm_hour<12?"AM":"PM"},"%S":function(t){return s(t.tm_sec,2)},"%t":function(){return"\t"},"%u":function(t){return t.tm_wday||7},"%U":function(t){var e=new Date(t.tm_year+1900,0,1),r=0===e.getDay()?e:se(e,7-e.getDay()),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=ce(ae(n.getFullYear())?ue:le,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%V":function(t){var e,r=new Date(t.tm_year+1900,0,4),n=new Date(t.tm_year+1901,0,4),p=d(r),i=d(n),o=se(new Date(t.tm_year+1900,0,1),t.tm_yday);return y(o,p)<0?"53":y(i,o)<=0?"01":(e=p.getFullYear()<t.tm_year+1900?t.tm_yday+32-p.getDate():t.tm_yday+1-p.getDate(),s(Math.ceil(e/7),2))},"%w":function(t){return t.tm_wday},"%W":function(t){var e=new Date(t.tm_year,0,1),r=1===e.getDay()?e:se(e,0===e.getDay()?1:7-e.getDay()+1),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=ce(ae(n.getFullYear())?ue:le,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%y":function(t){return(t.tm_year+1900).toString().substring(2)},"%Y":function(t){return t.tm_year+1900},"%z":function(t){var e=t.tm_gmtoff,r=e>=0;return e=Math.abs(e)/60,e=e/60*100+e%60,(r?"+":"-")+String("0000"+e).slice(-4)},"%Z":function(t){return t.tm_zone},"%%":function(){return"%"}};for(var a in f)o.indexOf(a)>=0&&(o=o.replace(new RegExp(a,"g"),f[a](i)));var b=fe(o,!1);return b.length>e?0:(O(b,t),b.length-1)}function de(t,e,r,n){return ye(t,e,r,n)}function me(t){var e=Date.now()/1e3|0;return t&&(H[t>>2]=e),e}function fe(t,e,r){var n=r>0?r:N(t)+1,p=new Array(n),i=A(t,p,0,p.length);return e&&(p.length=i),p}var be={m:Pt,q:Ct,t:wt,b:Tt,l:Dt,n:Mt,G:At,u:Et,V:Nt,T:kt,f:zt,S:Ht,J:Ut,Q:Yt,v:Jt,W:$t,X:te,C:ee,Y:re,U:ne,K:pe,F:ie,a:oe,x:Ki,L:_o,c:ki,E:Qi,d:Ei,g:Fi,I:Ji,o:Ui,M:eo,y:Yi,p:qi,H:ro,O:Zi,B:po,i:Ni,h:Oi,e:zi,D:to,k:Wi,j:Hi,s:Xi,r:no,w:io,N:$i,A:oo,P:_e,memory:L,R:de,table:G,z:me},Se=Lt();e["asm"]=Se;var ve,he=e["___wasm_call_ctors"]=function(){return(he=e["___wasm_call_ctors"]=e["asm"]["Z"]).apply(null,arguments)},Be=(e["___em_js__array_bounds_check_error"]=function(){return(e["___em_js__array_bounds_check_error"]=e["asm"]["_"]).apply(null,arguments)},e["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=function(){return(Be=e["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=e["asm"]["$"]).apply(null,arguments)}),ge=e["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=function(){return(ge=e["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=e["asm"]["aa"]).apply(null,arguments)},Le=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=function(){return(Le=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=e["asm"]["ba"]).apply(null,arguments)},Pe=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=function(){return(Pe=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=e["asm"]["ca"]).apply(null,arguments)},Ie=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=function(){return(Ie=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=e["asm"]["da"]).apply(null,arguments)},je=e["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=function(){return(je=e["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=e["asm"]["ea"]).apply(null,arguments)},Ge=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=function(){return(Ge=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=e["asm"]["fa"]).apply(null,arguments)},xe=e["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=function(){return(xe=e["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=e["asm"]["ga"]).apply(null,arguments)},Ce=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=function(){return(Ce=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=e["asm"]["ha"]).apply(null,arguments)},Re=e["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=function(){return(Re=e["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=e["asm"]["ia"]).apply(null,arguments)},Me=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=function(){return(Me=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=e["asm"]["ja"]).apply(null,arguments)},Ve=e["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=function(){return(Ve=e["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=e["asm"]["ka"]).apply(null,arguments)},we=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=function(){return(we=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=e["asm"]["la"]).apply(null,arguments)},Te=e["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=function(){return(Te=e["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=e["asm"]["ma"]).apply(null,arguments)},De=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=function(){return(De=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=e["asm"]["na"]).apply(null,arguments)},Ae=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=function(){return(Ae=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=e["asm"]["oa"]).apply(null,arguments)},Ee=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=function(){return(Ee=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=e["asm"]["pa"]).apply(null,arguments)},Ne=e["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=function(){return(Ne=e["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=e["asm"]["qa"]).apply(null,arguments)},Oe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=function(){return(Oe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=e["asm"]["ra"]).apply(null,arguments)},ke=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=function(){return(ke=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=e["asm"]["sa"]).apply(null,arguments)},ze=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=function(){return(ze=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=e["asm"]["ta"]).apply(null,arguments)},Fe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=function(){return(Fe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=e["asm"]["ua"]).apply(null,arguments)},We=e["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=function(){return(We=e["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=e["asm"]["va"]).apply(null,arguments)},He=e["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=function(){return(He=e["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=e["asm"]["wa"]).apply(null,arguments)},Ue=e["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=function(){return(Ue=e["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=e["asm"]["xa"]).apply(null,arguments)},Ye=e["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=function(){return(Ye=e["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=e["asm"]["ya"]).apply(null,arguments)},qe=e["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=function(){return(qe=e["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=e["asm"]["za"]).apply(null,arguments)},Xe=e["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=function(){return(Xe=e["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=e["asm"]["Aa"]).apply(null,arguments)},Je=e["_emscripten_bind_LBPlanishAry___destroy___0"]=function(){return(Je=e["_emscripten_bind_LBPlanishAry___destroy___0"]=e["asm"]["Ba"]).apply(null,arguments)},Ze=e["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=function(){return(Ze=e["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=e["asm"]["Ca"]).apply(null,arguments)},Ke=e["_emscripten_bind_LBProj4Wrapper_Init_2"]=function(){return(Ke=e["_emscripten_bind_LBProj4Wrapper_Init_2"]=e["asm"]["Da"]).apply(null,arguments)},Qe=e["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=function(){return(Qe=e["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=e["asm"]["Ea"]).apply(null,arguments)},$e=e["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=function(){return($e=e["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=e["asm"]["Fa"]).apply(null,arguments)},tr=e["_emscripten_bind_LBProj4Wrapper___destroy___0"]=function(){return(tr=e["_emscripten_bind_LBProj4Wrapper___destroy___0"]=e["asm"]["Ga"]).apply(null,arguments)},er=e["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=function(){return(er=e["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=e["asm"]["Ha"]).apply(null,arguments)},rr=e["_emscripten_bind_LBSpaBody_Init_2"]=function(){return(rr=e["_emscripten_bind_LBSpaBody_Init_2"]=e["asm"]["Ia"]).apply(null,arguments)},nr=e["_emscripten_bind_LBSpaBody_GetTriangle_2"]=function(){return(nr=e["_emscripten_bind_LBSpaBody_GetTriangle_2"]=e["asm"]["Ja"]).apply(null,arguments)},pr=e["_emscripten_bind_LBSpaBody_CheckReference_1"]=function(){return(pr=e["_emscripten_bind_LBSpaBody_CheckReference_1"]=e["asm"]["Ka"]).apply(null,arguments)},ir=e["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=function(){return(ir=e["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=e["asm"]["La"]).apply(null,arguments)},or=e["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=function(){return(or=e["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=e["asm"]["Ma"]).apply(null,arguments)},_r=e["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=function(){return(_r=e["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=e["asm"]["Na"]).apply(null,arguments)},ar=e["_emscripten_bind_LBSpaBody_GetVolume_0"]=function(){return(ar=e["_emscripten_bind_LBSpaBody_GetVolume_0"]=e["asm"]["Oa"]).apply(null,arguments)},cr=e["_emscripten_bind_LBSpaBody___destroy___0"]=function(){return(cr=e["_emscripten_bind_LBSpaBody___destroy___0"]=e["asm"]["Pa"]).apply(null,arguments)},ur=e["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=function(){return(ur=e["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=e["asm"]["Qa"]).apply(null,arguments)},lr=e["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=function(){return(lr=e["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=e["asm"]["Ra"]).apply(null,arguments)},sr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=function(){return(sr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=e["asm"]["Sa"]).apply(null,arguments)},yr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=function(){return(yr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=e["asm"]["Ta"]).apply(null,arguments)},dr=e["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=function(){return(dr=e["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=e["asm"]["Ua"]).apply(null,arguments)},mr=e["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=function(){return(mr=e["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=e["asm"]["Va"]).apply(null,arguments)},fr=e["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=function(){return(fr=e["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=e["asm"]["Wa"]).apply(null,arguments)},br=e["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=function(){return(br=e["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=e["asm"]["Xa"]).apply(null,arguments)},Sr=e["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=function(){return(Sr=e["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=e["asm"]["Ya"]).apply(null,arguments)},vr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=function(){return(vr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=e["asm"]["Za"]).apply(null,arguments)},hr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=function(){return(hr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=e["asm"]["_a"]).apply(null,arguments)},Br=e["_emscripten_bind_LBEdgeFormer___destroy___0"]=function(){return(Br=e["_emscripten_bind_LBEdgeFormer___destroy___0"]=e["asm"]["$a"]).apply(null,arguments)},gr=e["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(gr=e["_emscripten_bind_VoidPtr___destroy___0"]=e["asm"]["ab"]).apply(null,arguments)},Lr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=function(){return(Lr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=e["asm"]["bb"]).apply(null,arguments)},Pr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=function(){return(Pr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=e["asm"]["cb"]).apply(null,arguments)},Ir=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=function(){return(Ir=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=e["asm"]["db"]).apply(null,arguments)},jr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=function(){return(jr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=e["asm"]["eb"]).apply(null,arguments)},Gr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=function(){return(Gr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=e["asm"]["fb"]).apply(null,arguments)},xr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=function(){return(xr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=e["asm"]["gb"]).apply(null,arguments)},Cr=e["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=function(){return(Cr=e["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=e["asm"]["hb"]).apply(null,arguments)},Rr=e["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=function(){return(Rr=e["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=e["asm"]["ib"]).apply(null,arguments)},Mr=e["_emscripten_bind_LBSpaVec2_get_x_0"]=function(){return(Mr=e["_emscripten_bind_LBSpaVec2_get_x_0"]=e["asm"]["jb"]).apply(null,arguments)},Vr=e["_emscripten_bind_LBSpaVec2_set_x_1"]=function(){return(Vr=e["_emscripten_bind_LBSpaVec2_set_x_1"]=e["asm"]["kb"]).apply(null,arguments)},wr=e["_emscripten_bind_LBSpaVec2_get_y_0"]=function(){return(wr=e["_emscripten_bind_LBSpaVec2_get_y_0"]=e["asm"]["lb"]).apply(null,arguments)},Tr=e["_emscripten_bind_LBSpaVec2_set_y_1"]=function(){return(Tr=e["_emscripten_bind_LBSpaVec2_set_y_1"]=e["asm"]["mb"]).apply(null,arguments)},Dr=e["_emscripten_bind_LBSpaVec2___destroy___0"]=function(){return(Dr=e["_emscripten_bind_LBSpaVec2___destroy___0"]=e["asm"]["nb"]).apply(null,arguments)},Ar=e["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=function(){return(Ar=e["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=e["asm"]["ob"]).apply(null,arguments)},Er=e["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=function(){return(Er=e["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=e["asm"]["pb"]).apply(null,arguments)},Nr=e["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=function(){return(Nr=e["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=e["asm"]["qb"]).apply(null,arguments)},Or=e["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=function(){return(Or=e["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=e["asm"]["rb"]).apply(null,arguments)},kr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=function(){return(kr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=e["asm"]["sb"]).apply(null,arguments)},zr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=function(){return(zr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=e["asm"]["tb"]).apply(null,arguments)},Fr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=function(){return(Fr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=e["asm"]["ub"]).apply(null,arguments)},Wr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=function(){return(Wr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=e["asm"]["vb"]).apply(null,arguments)},Hr=e["_emscripten_bind_LBSpaSelectResult___destroy___0"]=function(){return(Hr=e["_emscripten_bind_LBSpaSelectResult___destroy___0"]=e["asm"]["wb"]).apply(null,arguments)},Ur=e["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=function(){return(Ur=e["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=e["asm"]["xb"]).apply(null,arguments)},Yr=e["_emscripten_bind_LBSpaVec4_get_x_0"]=function(){return(Yr=e["_emscripten_bind_LBSpaVec4_get_x_0"]=e["asm"]["yb"]).apply(null,arguments)},qr=e["_emscripten_bind_LBSpaVec4_set_x_1"]=function(){return(qr=e["_emscripten_bind_LBSpaVec4_set_x_1"]=e["asm"]["zb"]).apply(null,arguments)},Xr=e["_emscripten_bind_LBSpaVec4_get_y_0"]=function(){return(Xr=e["_emscripten_bind_LBSpaVec4_get_y_0"]=e["asm"]["Ab"]).apply(null,arguments)},Jr=e["_emscripten_bind_LBSpaVec4_set_y_1"]=function(){return(Jr=e["_emscripten_bind_LBSpaVec4_set_y_1"]=e["asm"]["Bb"]).apply(null,arguments)},Zr=e["_emscripten_bind_LBSpaVec4_get_z_0"]=function(){return(Zr=e["_emscripten_bind_LBSpaVec4_get_z_0"]=e["asm"]["Cb"]).apply(null,arguments)},Kr=e["_emscripten_bind_LBSpaVec4_set_z_1"]=function(){return(Kr=e["_emscripten_bind_LBSpaVec4_set_z_1"]=e["asm"]["Db"]).apply(null,arguments)},Qr=e["_emscripten_bind_LBSpaVec4_get_w_0"]=function(){return(Qr=e["_emscripten_bind_LBSpaVec4_get_w_0"]=e["asm"]["Eb"]).apply(null,arguments)},$r=e["_emscripten_bind_LBSpaVec4_set_w_1"]=function(){return($r=e["_emscripten_bind_LBSpaVec4_set_w_1"]=e["asm"]["Fb"]).apply(null,arguments)},tn=e["_emscripten_bind_LBSpaVec4___destroy___0"]=function(){return(tn=e["_emscripten_bind_LBSpaVec4___destroy___0"]=e["asm"]["Gb"]).apply(null,arguments)},en=e["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=function(){return(en=e["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=e["asm"]["Hb"]).apply(null,arguments)},rn=e["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=function(){return(rn=e["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=e["asm"]["Ib"]).apply(null,arguments)},nn=e["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=function(){return(nn=e["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=e["asm"]["Jb"]).apply(null,arguments)},pn=e["_emscripten_bind_LBSpaGeoTool___destroy___0"]=function(){return(pn=e["_emscripten_bind_LBSpaGeoTool___destroy___0"]=e["asm"]["Kb"]).apply(null,arguments)},on=e["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=function(){return(on=e["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=e["asm"]["Lb"]).apply(null,arguments)},_n=e["_emscripten_bind_LBSpaMat_At_1"]=function(){return(_n=e["_emscripten_bind_LBSpaMat_At_1"]=e["asm"]["Mb"]).apply(null,arguments)},an=e["_emscripten_bind_LBSpaMat___destroy___0"]=function(){return(an=e["_emscripten_bind_LBSpaMat___destroy___0"]=e["asm"]["Nb"]).apply(null,arguments)},cn=e["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=function(){return(cn=e["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=e["asm"]["Ob"]).apply(null,arguments)},un=e["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=function(){return(un=e["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=e["asm"]["Pb"]).apply(null,arguments)},ln=e["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=function(){return(ln=e["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=e["asm"]["Qb"]).apply(null,arguments)},sn=e["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=function(){return(sn=e["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=e["asm"]["Rb"]).apply(null,arguments)},yn=e["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=function(){return(yn=e["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=e["asm"]["Sb"]).apply(null,arguments)},dn=e["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=function(){return(dn=e["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=e["asm"]["Tb"]).apply(null,arguments)},mn=e["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=function(){return(mn=e["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=e["asm"]["Ub"]).apply(null,arguments)},fn=e["_emscripten_bind_LBSpaSerial___destroy___0"]=function(){return(fn=e["_emscripten_bind_LBSpaSerial___destroy___0"]=e["asm"]["Vb"]).apply(null,arguments)},bn=e["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=function(){return(bn=e["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=e["asm"]["Wb"]).apply(null,arguments)},Sn=e["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=function(){return(Sn=e["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=e["asm"]["Xb"]).apply(null,arguments)},vn=e["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=function(){return(vn=e["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=e["asm"]["Yb"]).apply(null,arguments)},hn=e["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=function(){return(hn=e["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=e["asm"]["Zb"]).apply(null,arguments)},Bn=e["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=function(){return(Bn=e["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=e["asm"]["_b"]).apply(null,arguments)},gn=e["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=function(){return(gn=e["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=e["asm"]["$b"]).apply(null,arguments)},Ln=e["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=function(){return(Ln=e["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=e["asm"]["ac"]).apply(null,arguments)},Pn=e["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=function(){return(Pn=e["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=e["asm"]["bc"]).apply(null,arguments)},In=e["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=function(){return(In=e["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=e["asm"]["cc"]).apply(null,arguments)},jn=e["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=function(){return(jn=e["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=e["asm"]["dc"]).apply(null,arguments)},Gn=e["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=function(){return(Gn=e["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=e["asm"]["ec"]).apply(null,arguments)},xn=e["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=function(){return(xn=e["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=e["asm"]["fc"]).apply(null,arguments)},Cn=e["_emscripten_bind_LBSpaMgr_Select_2"]=function(){return(Cn=e["_emscripten_bind_LBSpaMgr_Select_2"]=e["asm"]["gc"]).apply(null,arguments)},Rn=e["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=function(){return(Rn=e["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=e["asm"]["hc"]).apply(null,arguments)},Mn=e["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=function(){return(Mn=e["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=e["asm"]["ic"]).apply(null,arguments)},Vn=e["_emscripten_bind_LBSpaMgr___destroy___0"]=function(){return(Vn=e["_emscripten_bind_LBSpaMgr___destroy___0"]=e["asm"]["jc"]).apply(null,arguments)},wn=e["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=function(){return(wn=e["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=e["asm"]["kc"]).apply(null,arguments)},Tn=e["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=function(){return(Tn=e["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=e["asm"]["lc"]).apply(null,arguments)},Dn=e["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=function(){return(Dn=e["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=e["asm"]["mc"]).apply(null,arguments)},An=e["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=function(){return(An=e["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=e["asm"]["nc"]).apply(null,arguments)},En=e["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=function(){return(En=e["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=e["asm"]["oc"]).apply(null,arguments)},Nn=e["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=function(){return(Nn=e["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=e["asm"]["pc"]).apply(null,arguments)},On=e["_emscripten_bind_LBDeal_LBDeal_0"]=function(){return(On=e["_emscripten_bind_LBDeal_LBDeal_0"]=e["asm"]["qc"]).apply(null,arguments)},kn=e["_emscripten_bind_LBDeal_Init_4"]=function(){return(kn=e["_emscripten_bind_LBDeal_Init_4"]=e["asm"]["rc"]).apply(null,arguments)},zn=e["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=function(){return(zn=e["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=e["asm"]["sc"]).apply(null,arguments)},Fn=e["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=function(){return(Fn=e["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=e["asm"]["tc"]).apply(null,arguments)},Wn=e["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=function(){return(Wn=e["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=e["asm"]["uc"]).apply(null,arguments)},Hn=e["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=function(){return(Hn=e["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=e["asm"]["vc"]).apply(null,arguments)},Un=e["_emscripten_bind_LBDeal___destroy___0"]=function(){return(Un=e["_emscripten_bind_LBDeal___destroy___0"]=e["asm"]["wc"]).apply(null,arguments)},Yn=e["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=function(){return(Yn=e["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=e["asm"]["xc"]).apply(null,arguments)},qn=e["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=function(){return(qn=e["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=e["asm"]["yc"]).apply(null,arguments)},Xn=e["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=function(){return(Xn=e["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=e["asm"]["zc"]).apply(null,arguments)},Jn=e["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=function(){return(Jn=e["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=e["asm"]["Ac"]).apply(null,arguments)},Zn=e["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=function(){return(Zn=e["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=e["asm"]["Bc"]).apply(null,arguments)},Kn=e["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=function(){return(Kn=e["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=e["asm"]["Cc"]).apply(null,arguments)},Qn=e["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=function(){return(Qn=e["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=e["asm"]["Dc"]).apply(null,arguments)},$n=e["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=function(){return($n=e["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=e["asm"]["Ec"]).apply(null,arguments)},tp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=function(){return(tp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=e["asm"]["Fc"]).apply(null,arguments)},ep=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=function(){return(ep=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=e["asm"]["Gc"]).apply(null,arguments)},rp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=function(){return(rp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=e["asm"]["Hc"]).apply(null,arguments)},np=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=function(){return(np=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=e["asm"]["Ic"]).apply(null,arguments)},pp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=function(){return(pp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=e["asm"]["Jc"]).apply(null,arguments)},ip=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=function(){return(ip=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=e["asm"]["Kc"]).apply(null,arguments)},op=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=function(){return(op=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=e["asm"]["Lc"]).apply(null,arguments)},_p=e["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=function(){return(_p=e["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=e["asm"]["Mc"]).apply(null,arguments)},ap=e["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=function(){return(ap=e["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=e["asm"]["Nc"]).apply(null,arguments)},cp=e["_emscripten_bind_LBSpaVec_get_x_0"]=function(){return(cp=e["_emscripten_bind_LBSpaVec_get_x_0"]=e["asm"]["Oc"]).apply(null,arguments)},up=e["_emscripten_bind_LBSpaVec_set_x_1"]=function(){return(up=e["_emscripten_bind_LBSpaVec_set_x_1"]=e["asm"]["Pc"]).apply(null,arguments)},lp=e["_emscripten_bind_LBSpaVec_get_y_0"]=function(){return(lp=e["_emscripten_bind_LBSpaVec_get_y_0"]=e["asm"]["Qc"]).apply(null,arguments)},sp=e["_emscripten_bind_LBSpaVec_set_y_1"]=function(){return(sp=e["_emscripten_bind_LBSpaVec_set_y_1"]=e["asm"]["Rc"]).apply(null,arguments)},yp=e["_emscripten_bind_LBSpaVec_get_z_0"]=function(){return(yp=e["_emscripten_bind_LBSpaVec_get_z_0"]=e["asm"]["Sc"]).apply(null,arguments)},dp=e["_emscripten_bind_LBSpaVec_set_z_1"]=function(){return(dp=e["_emscripten_bind_LBSpaVec_set_z_1"]=e["asm"]["Tc"]).apply(null,arguments)},mp=e["_emscripten_bind_LBSpaVec___destroy___0"]=function(){return(mp=e["_emscripten_bind_LBSpaVec___destroy___0"]=e["asm"]["Uc"]).apply(null,arguments)},fp=e["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=function(){return(fp=e["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=e["asm"]["Vc"]).apply(null,arguments)},bp=e["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=function(){return(bp=e["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=e["asm"]["Wc"]).apply(null,arguments)},Sp=e["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=function(){return(Sp=e["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=e["asm"]["Xc"]).apply(null,arguments)},vp=e["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=function(){return(vp=e["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=e["asm"]["Yc"]).apply(null,arguments)},hp=e["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=function(){return(hp=e["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=e["asm"]["Zc"]).apply(null,arguments)},Bp=e["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=function(){return(Bp=e["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=e["asm"]["_c"]).apply(null,arguments)},gp=e["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=function(){return(gp=e["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=e["asm"]["$c"]).apply(null,arguments)},Lp=e["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=function(){return(Lp=e["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=e["asm"]["ad"]).apply(null,arguments)},Pp=e["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=function(){return(Pp=e["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=e["asm"]["bd"]).apply(null,arguments)},Ip=e["_emscripten_bind_LBSpaTriangle_GetPt_1"]=function(){return(Ip=e["_emscripten_bind_LBSpaTriangle_GetPt_1"]=e["asm"]["cd"]).apply(null,arguments)},jp=e["_emscripten_bind_LBSpaTriangle_GetUV_1"]=function(){return(jp=e["_emscripten_bind_LBSpaTriangle_GetUV_1"]=e["asm"]["dd"]).apply(null,arguments)},Gp=e["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=function(){return(Gp=e["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=e["asm"]["ed"]).apply(null,arguments)},xp=e["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=function(){return(xp=e["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=e["asm"]["fd"]).apply(null,arguments)},Cp=e["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=function(){return(Cp=e["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=e["asm"]["gd"]).apply(null,arguments)},Rp=e["_emscripten_bind_LBSpaTriangle___destroy___0"]=function(){return(Rp=e["_emscripten_bind_LBSpaTriangle___destroy___0"]=e["asm"]["hd"]).apply(null,arguments)},Mp=e["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=function(){return(Mp=e["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=e["asm"]["id"]).apply(null,arguments)},Vp=e["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=function(){return(Vp=e["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=e["asm"]["jd"]).apply(null,arguments)},wp=e["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=function(){return(wp=e["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=e["asm"]["kd"]).apply(null,arguments)},Tp=e["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=function(){return(Tp=e["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=e["asm"]["ld"]).apply(null,arguments)},Dp=e["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=function(){return(Dp=e["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=e["asm"]["md"]).apply(null,arguments)},Ap=e["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=function(){return(Ap=e["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=e["asm"]["nd"]).apply(null,arguments)},Ep=e["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=function(){return(Ep=e["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=e["asm"]["od"]).apply(null,arguments)},Np=e["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=function(){return(Np=e["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=e["asm"]["pd"]).apply(null,arguments)},Op=e["_emscripten_bind_LBSpaPrimitive___destroy___0"]=function(){return(Op=e["_emscripten_bind_LBSpaPrimitive___destroy___0"]=e["asm"]["qd"]).apply(null,arguments)},kp=e["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=function(){return(kp=e["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=e["asm"]["rd"]).apply(null,arguments)},zp=e["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=function(){return(zp=e["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=e["asm"]["sd"]).apply(null,arguments)},Fp=e["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=function(){return(Fp=e["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=e["asm"]["td"]).apply(null,arguments)},Wp=e["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=function(){return(Wp=e["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=e["asm"]["ud"]).apply(null,arguments)},Hp=e["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=function(){return(Hp=e["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=e["asm"]["vd"]).apply(null,arguments)},Up=e["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=function(){return(Up=e["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=e["asm"]["wd"]).apply(null,arguments)},Yp=e["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=function(){return(Yp=e["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=e["asm"]["xd"]).apply(null,arguments)},qp=e["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=function(){return(qp=e["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=e["asm"]["yd"]).apply(null,arguments)},Xp=e["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=function(){return(Xp=e["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=e["asm"]["zd"]).apply(null,arguments)},Jp=e["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=function(){return(Jp=e["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=e["asm"]["Ad"]).apply(null,arguments)},Zp=e["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=function(){return(Zp=e["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=e["asm"]["Bd"]).apply(null,arguments)},Kp=e["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=function(){return(Kp=e["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=e["asm"]["Cd"]).apply(null,arguments)},Qp=e["_emscripten_bind_LBSpaBoxMgr_Select_0"]=function(){return(Qp=e["_emscripten_bind_LBSpaBoxMgr_Select_0"]=e["asm"]["Dd"]).apply(null,arguments)},$p=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=function(){return($p=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=e["asm"]["Ed"]).apply(null,arguments)},ti=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=function(){return(ti=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=e["asm"]["Fd"]).apply(null,arguments)},ei=e["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=function(){return(ei=e["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=e["asm"]["Gd"]).apply(null,arguments)},ri=e["_free"]=function(){return(ri=e["_free"]=e["asm"]["Hd"]).apply(null,arguments)},ni=e["_malloc"]=function(){return(ni=e["_malloc"]=e["asm"]["Id"]).apply(null,arguments)},pi=e["___errno_location"]=function(){return(pi=e["___errno_location"]=e["asm"]["Jd"]).apply(null,arguments)},ii=e["_setThrew"]=function(){return(ii=e["_setThrew"]=e["asm"]["Kd"]).apply(null,arguments)},oi=e["__ZSt18uncaught_exceptionv"]=function(){return(oi=e["__ZSt18uncaught_exceptionv"]=e["asm"]["Ld"]).apply(null,arguments)},_i=e["___cxa_can_catch"]=function(){return(_i=e["___cxa_can_catch"]=e["asm"]["Md"]).apply(null,arguments)},ai=e["___cxa_is_pointer_type"]=function(){return(ai=e["___cxa_is_pointer_type"]=e["asm"]["Nd"]).apply(null,arguments)},ci=e["dynCall_v"]=function(){return(ci=e["dynCall_v"]=e["asm"]["Od"]).apply(null,arguments)},ui=e["dynCall_vi"]=function(){return(ui=e["dynCall_vi"]=e["asm"]["Pd"]).apply(null,arguments)},li=e["dynCall_vii"]=function(){return(li=e["dynCall_vii"]=e["asm"]["Qd"]).apply(null,arguments)},si=e["dynCall_viii"]=function(){return(si=e["dynCall_viii"]=e["asm"]["Rd"]).apply(null,arguments)},yi=e["dynCall_viiii"]=function(){return(yi=e["dynCall_viiii"]=e["asm"]["Sd"]).apply(null,arguments)},di=e["dynCall_viiiii"]=function(){return(di=e["dynCall_viiiii"]=e["asm"]["Td"]).apply(null,arguments)},mi=e["dynCall_viiiiiii"]=function(){return(mi=e["dynCall_viiiiiii"]=e["asm"]["Ud"]).apply(null,arguments)},fi=e["dynCall_viiiiiiiiii"]=function(){return(fi=e["dynCall_viiiiiiiiii"]=e["asm"]["Vd"]).apply(null,arguments)},bi=e["dynCall_viiiiiiiiiii"]=function(){return(bi=e["dynCall_viiiiiiiiiii"]=e["asm"]["Wd"]).apply(null,arguments)},Si=e["dynCall_viiiiiiiiiiiiiii"]=function(){return(Si=e["dynCall_viiiiiiiiiiiiiii"]=e["asm"]["Xd"]).apply(null,arguments)},vi=e["dynCall_viidd"]=function(){return(vi=e["dynCall_viidd"]=e["asm"]["Yd"]).apply(null,arguments)},hi=e["dynCall_i"]=function(){return(hi=e["dynCall_i"]=e["asm"]["Zd"]).apply(null,arguments)},Bi=e["dynCall_ii"]=function(){return(Bi=e["dynCall_ii"]=e["asm"]["_d"]).apply(null,arguments)},gi=e["dynCall_iii"]=function(){return(gi=e["dynCall_iii"]=e["asm"]["$d"]).apply(null,arguments)},Li=e["dynCall_iiii"]=function(){return(Li=e["dynCall_iiii"]=e["asm"]["ae"]).apply(null,arguments)},Pi=e["dynCall_iiiii"]=function(){return(Pi=e["dynCall_iiiii"]=e["asm"]["be"]).apply(null,arguments)},Ii=e["dynCall_iiiiii"]=function(){return(Ii=e["dynCall_iiiiii"]=e["asm"]["ce"]).apply(null,arguments)},ji=e["dynCall_iiiiiii"]=function(){return(ji=e["dynCall_iiiiiii"]=e["asm"]["de"]).apply(null,arguments)},Gi=e["dynCall_iiiiiiii"]=function(){return(Gi=e["dynCall_iiiiiiii"]=e["asm"]["ee"]).apply(null,arguments)},xi=e["dynCall_iiiiiiiii"]=function(){return(xi=e["dynCall_iiiiiiiii"]=e["asm"]["fe"]).apply(null,arguments)},Ci=e["dynCall_iiiiiiiiiiii"]=function(){return(Ci=e["dynCall_iiiiiiiiiiii"]=e["asm"]["ge"]).apply(null,arguments)},Ri=e["dynCall_iiiiid"]=function(){return(Ri=e["dynCall_iiiiid"]=e["asm"]["he"]).apply(null,arguments)},Mi=e["dynCall_iiiidiii"]=function(){return(Mi=e["dynCall_iiiidiii"]=e["asm"]["ie"]).apply(null,arguments)},Vi=e["dynCall_iidd"]=function(){return(Vi=e["dynCall_iidd"]=e["asm"]["je"]).apply(null,arguments)},wi=e["dynCall_diii"]=function(){return(wi=e["dynCall_diii"]=e["asm"]["ke"]).apply(null,arguments)},Ti=e["stackSave"]=function(){return(Ti=e["stackSave"]=e["asm"]["le"]).apply(null,arguments)},Di=e["stackAlloc"]=function(){return(Di=e["stackAlloc"]=e["asm"]["me"]).apply(null,arguments)},Ai=e["stackRestore"]=function(){return(Ai=e["stackRestore"]=e["asm"]["ne"]).apply(null,arguments)};function Ei(t,e,r){var n=Ti();try{return gi(t,e,r)}catch(p){if(Ai(n),p!==p+0&&"longjmp"!==p)throw p;ii(1,0)}}function Ni(t){var e=Ti();try{ci(t)}catch(r){if(Ai(e),r!==r+0&&"longjmp"!==r)throw r;ii(1,0)}}function Oi(t,e){var r=Ti();try{ui(t,e)}catch(n){if(Ai(r),n!==n+0&&"longjmp"!==n)throw n;ii(1,0)}}function ki(t,e){var r=Ti();try{return Bi(t,e)}catch(n){if(Ai(r),n!==n+0&&"longjmp"!==n)throw n;ii(1,0)}}function zi(t,e,r){var n=Ti();try{li(t,e,r)}catch(p){if(Ai(n),p!==p+0&&"longjmp"!==p)throw p;ii(1,0)}}function Fi(t,e,r,n){var p=Ti();try{return Li(t,e,r,n)}catch(i){if(Ai(p),i!==i+0&&"longjmp"!==i)throw i;ii(1,0)}}function Wi(t,e,r,n){var p=Ti();try{si(t,e,r,n)}catch(i){if(Ai(p),i!==i+0&&"longjmp"!==i)throw i;ii(1,0)}}function Hi(t,e,r,n,p){var i=Ti();try{yi(t,e,r,n,p)}catch(o){if(Ai(i),o!==o+0&&"longjmp"!==o)throw o;ii(1,0)}}function Ui(t,e,r,n,p){var i=Ti();try{return Pi(t,e,r,n,p)}catch(o){if(Ai(i),o!==o+0&&"longjmp"!==o)throw o;ii(1,0)}}function Yi(t,e,r,n,p,i){var o=Ti();try{return Ii(t,e,r,n,p,i)}catch(_){if(Ai(o),_!==_+0&&"longjmp"!==_)throw _;ii(1,0)}}function qi(t,e,r,n,p,i,o){var _=Ti();try{return ji(t,e,r,n,p,i,o)}catch(a){if(Ai(_),a!==a+0&&"longjmp"!==a)throw a;ii(1,0)}}function Xi(t,e,r,n,p,i){var o=Ti();try{di(t,e,r,n,p,i)}catch(_){if(Ai(o),_!==_+0&&"longjmp"!==_)throw _;ii(1,0)}}function Ji(t,e,r,n,p,i,o,_){var a=Ti();try{return Mi(t,e,r,n,p,i,o,_)}catch(c){if(Ai(a),c!==c+0&&"longjmp"!==c)throw c;ii(1,0)}}function Zi(t,e,r,n,p,i,o,_,a){var c=Ti();try{return xi(t,e,r,n,p,i,o,_,a)}catch(u){if(Ai(c),u!==u+0&&"longjmp"!==u)throw u;ii(1,0)}}function Ki(t,e,r,n){var p=Ti();try{return wi(t,e,r,n)}catch(i){if(Ai(p),i!==i+0&&"longjmp"!==i)throw i;ii(1,0)}}function Qi(t,e,r,n){var p=Ti();try{return Vi(t,e,r,n)}catch(i){if(Ai(p),i!==i+0&&"longjmp"!==i)throw i;ii(1,0)}}function $i(t,e,r,n,p,i,o,_,a,c,u,l){var s=Ti();try{bi(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Ai(s),y!==y+0&&"longjmp"!==y)throw y;ii(1,0)}}function to(t,e,r,n,p){var i=Ti();try{vi(t,e,r,n,p)}catch(o){if(Ai(i),o!==o+0&&"longjmp"!==o)throw o;ii(1,0)}}function eo(t,e,r,n,p,i){var o=Ti();try{return Ri(t,e,r,n,p,i)}catch(_){if(Ai(o),_!==_+0&&"longjmp"!==_)throw _;ii(1,0)}}function ro(t,e,r,n,p,i,o,_){var a=Ti();try{return Gi(t,e,r,n,p,i,o,_)}catch(c){if(Ai(a),c!==c+0&&"longjmp"!==c)throw c;ii(1,0)}}function no(t,e,r,n,p,i,o,_){var a=Ti();try{mi(t,e,r,n,p,i,o,_)}catch(c){if(Ai(a),c!==c+0&&"longjmp"!==c)throw c;ii(1,0)}}function po(t,e,r,n,p,i,o,_,a,c,u,l){var s=Ti();try{return Ci(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Ai(s),y!==y+0&&"longjmp"!==y)throw y;ii(1,0)}}function io(t,e,r,n,p,i,o,_,a,c,u){var l=Ti();try{fi(t,e,r,n,p,i,o,_,a,c,u)}catch(s){if(Ai(l),s!==s+0&&"longjmp"!==s)throw s;ii(1,0)}}function oo(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m){var f=Ti();try{Si(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m)}catch(b){if(Ai(f),b!==b+0&&"longjmp"!==b)throw b;ii(1,0)}}function _o(t){var e=Ti();try{return hi(t)}catch(r){if(Ai(e),r!==r+0&&"longjmp"!==r)throw r;ii(1,0)}}function ao(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function co(t){function n(){ve||(ve=!0,e["calledRun"]=!0,x||(it(),ot(),r(e),e["onRuntimeInitialized"]&&e["onRuntimeInitialized"](),_t()))}ut>0||(pt(),ut>0||(e["setStatus"]?(e["setStatus"]("Running..."),setTimeout((function(){setTimeout((function(){e["setStatus"]("")}),1),n()}),1)):n()))}function uo(t,r){r&&g&&0===t||(g||(x=!0,e["onExit"]&&e["onExit"](t)),a(t,new ao(t)))}if(e["asm"]=Se,e["ccall"]=M,e["cwrap"]=V,lt=function t(){ve||co(),ve||(lt=t)},e["run"]=co,e["preInit"]){"function"==typeof e["preInit"]&&(e["preInit"]=[e["preInit"]]);while(e["preInit"].length>0)e["preInit"].pop()()}function lo(){}function so(t){return(t||lo).__cache__}function yo(t,e){var r=so(e),n=r[t];return n||(n=Object.create((e||lo).prototype),n.ptr=t,r[t]=n)}function mo(t,e){return yo(t.ptr,e)}function fo(t){if(!t["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";t["__destroy__"](),delete so(t.__class__)[t.ptr]}function bo(t,e){return t.ptr===e.ptr}function So(t){return t.ptr}function vo(t){return t.__class__}g=!0,co(),lo.prototype=Object.create(lo.prototype),lo.prototype.constructor=lo,lo.prototype.__class__=lo,lo.__cache__={},e["WrapperObject"]=lo,e["getCache"]=so,e["wrapPointer"]=yo,e["castObject"]=mo,e["NULL"]=yo(0),e["destroy"]=fo,e["compare"]=bo,e["getPointer"]=So,e["getClass"]=vo;var ho={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ho.needed){for(var t=0;t<ho.temps.length;t++)e["_free"](ho.temps[t]);ho.temps.length=0,e["_free"](ho.buffer),ho.buffer=0,ho.size+=ho.needed,ho.needed=0}ho.buffer||(ho.size+=128,ho.buffer=e["_malloc"](ho.size),C(ho.buffer)),ho.pos=0},alloc:function(t,r){C(ho.buffer);var n,p=r.BYTES_PER_ELEMENT,i=t.length*p;return i=i+7&-8,ho.pos+i>=ho.size?(C(i>0),ho.needed+=i,n=e["_malloc"](i),ho.temps.push(n)):(n=ho.buffer+ho.pos,ho.pos+=i),n},copy:function(t,e,r){r>>>=0;var n=e.BYTES_PER_ELEMENT;switch(n){case 2:r>>>=1;break;case 4:r>>>=2;break;case 8:r>>>=3;break}for(var p=0;p<t.length;p++)e[r+p]=t[p]}};function Bo(t){if("string"===typeof t){var e=fe(t),r=ho.alloc(e,F);return ho.copy(e,F,r),r}return t}function go(t){if("object"===typeof t){var e=ho.alloc(t,F);return ho.copy(t,F,e),e}return t}function Lo(t){if("object"===typeof t){var e=ho.alloc(t,H);return ho.copy(t,H,e),e}return t}function Po(t){if("object"===typeof t){var e=ho.alloc(t,U);return ho.copy(t,U,e),e}return t}function Io(t){if("object"===typeof t){var e=ho.alloc(t,Y);return ho.copy(t,Y,e),e}return t}function jo(){this.ptr=Be(),so(jo)[this.ptr]=this}function Go(){this.ptr=He(),so(Go)[this.ptr]=this}function xo(){this.ptr=Ze(),so(xo)[this.ptr]=this}function Co(){this.ptr=er(),so(Co)[this.ptr]=this}function Ro(){throw"cannot construct a LBSpaPrimitiveCluster, no constructor in IDL"}function Mo(){this.ptr=mr(),so(Mo)[this.ptr]=this}function Vo(){throw"cannot construct a VoidPtr, no constructor in IDL"}function wo(){throw"cannot construct a LBSpaSelectSegResultItem, no constructor in IDL"}function To(){this.ptr=Rr(),so(To)[this.ptr]=this}function Do(){this.ptr=Ar(),so(Do)[this.ptr]=this}function Ao(){this.ptr=Ur(),so(Ao)[this.ptr]=this}function Eo(){this.ptr=en(),so(Eo)[this.ptr]=this}function No(){this.ptr=on(),so(No)[this.ptr]=this}function Oo(){this.ptr=cn(),so(Oo)[this.ptr]=this}function ko(){this.ptr=bn(),so(ko)[this.ptr]=this}function zo(){this.ptr=wn(),so(zo)[this.ptr]=this}function Fo(){this.ptr=On(),so(Fo)[this.ptr]=this}function Wo(){throw"cannot construct a LBSpaSelectResultId, no constructor in IDL"}function Ho(){throw"cannot construct a LBSpaPrimitiveSpatial, no constructor in IDL"}function Uo(){throw"cannot construct a LBSpaSelectTriResultItem, no constructor in IDL"}function Yo(){this.ptr=ap(),so(Yo)[this.ptr]=this}function qo(){this.ptr=fp(),so(qo)[this.ptr]=this}function Xo(){this.ptr=Mp(),so(Xo)[this.ptr]=this}function Jo(){this.ptr=kp(),so(Jo)[this.ptr]=this}function Zo(){this.ptr=Xp(),so(Zo)[this.ptr]=this}return jo.prototype=Object.create(lo.prototype),jo.prototype.constructor=jo,jo.prototype.__class__=jo,jo.__cache__={},e["MaterPrimitiveDecoder"]=jo,jo.prototype["Decode"]=jo.prototype.Decode=function(t,e){var r=this.ptr;return ho.prepare(),"object"==typeof t&&(t=go(t)),e&&"object"===typeof e&&(e=e.ptr),!!ge(r,t,e)},jo.prototype["GetPtNum"]=jo.prototype.GetPtNum=function(){var t=this.ptr;return Le(t)},jo.prototype["GetPtVal"]=jo.prototype.GetPtVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Pe(r,t,e)},jo.prototype["IsHaveUV"]=jo.prototype.IsHaveUV=function(){var t=this.ptr;return!!Ie(t)},jo.prototype["GetUVVal"]=jo.prototype.GetUVVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),je(r,t,e)},jo.prototype["IsHaveNormal"]=jo.prototype.IsHaveNormal=function(){var t=this.ptr;return!!Ge(t)},jo.prototype["GetNormalVal"]=jo.prototype.GetNormalVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),xe(r,t,e)},jo.prototype["IsHaveBatchId"]=jo.prototype.IsHaveBatchId=function(){var t=this.ptr;return!!Ce(t)},jo.prototype["GetBatchId"]=jo.prototype.GetBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Re(e,t)},jo.prototype["IsHaveMaterialId"]=jo.prototype.IsHaveMaterialId=function(){var t=this.ptr;return!!Me(t)},jo.prototype["GetMaterialId"]=jo.prototype.GetMaterialId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ve(e,t)},jo.prototype["IsHaveOutlineCoord"]=jo.prototype.IsHaveOutlineCoord=function(){var t=this.ptr;return!!we(t)},jo.prototype["GetOutlineCoord"]=jo.prototype.GetOutlineCoord=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Te(e,t)},jo.prototype["GetIndexNum"]=jo.prototype.GetIndexNum=function(){var t=this.ptr;return De(t)},jo.prototype["GetIndex"]=jo.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ae(e,t)},jo.prototype["IsHaveEdgeCheck"]=jo.prototype.IsHaveEdgeCheck=function(){var t=this.ptr;return!!Ee(t)},jo.prototype["GetEdgeCheck"]=jo.prototype.GetEdgeCheck=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ne(e,t)},jo.prototype["GetInstanceNum"]=jo.prototype.GetInstanceNum=function(){var t=this.ptr;return Oe(t)},jo.prototype["GetInstanceMatVal"]=jo.prototype.GetInstanceMatVal=function(t,e,r){var n=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),ke(n,t,e,r)},jo.prototype["GetInstanceBatchNum"]=jo.prototype.GetInstanceBatchNum=function(){var t=this.ptr;return ze(t)},jo.prototype["GetInstanceBatchId"]=jo.prototype.GetInstanceBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Fe(e,t)},jo.prototype["__destroy__"]=jo.prototype.__destroy__=function(){var t=this.ptr;We(t)},Go.prototype=Object.create(lo.prototype),Go.prototype.constructor=Go,Go.prototype.__class__=Go,Go.__cache__={},e["LBPlanishAry"]=Go,Go.prototype["SetPlanishNum"]=Go.prototype.SetPlanishNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Ue(e,t)},Go.prototype["SetPlanishPtNum"]=Go.prototype.SetPlanishPtNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ye(r,t,e)},Go.prototype["SetPlanishPtVal"]=Go.prototype.SetPlanishPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),qe(p,t,e,r,n)},Go.prototype["SetPlanishBot"]=Go.prototype.SetPlanishBot=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Xe(r,t,e)},Go.prototype["__destroy__"]=Go.prototype.__destroy__=function(){var t=this.ptr;Je(t)},xo.prototype=Object.create(lo.prototype),xo.prototype.constructor=xo,xo.prototype.__class__=xo,xo.__cache__={},e["LBProj4Wrapper"]=xo,xo.prototype["Init"]=xo.prototype.Init=function(t,e){var r=this.ptr;return ho.prepare(),t=t&&"object"===typeof t?t.ptr:Bo(t),e=e&&"object"===typeof e?e.ptr:Bo(e),!!Ke(r,t,e)},xo.prototype["TranformAry"]=xo.prototype.TranformAry=function(t,e){var r=this.ptr;ho.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),Qe(r,t,e)},xo.prototype["InverseTranformAry"]=xo.prototype.InverseTranformAry=function(t,e){var r=this.ptr;ho.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),$e(r,t,e)},xo.prototype["__destroy__"]=xo.prototype.__destroy__=function(){var t=this.ptr;tr(t)},Co.prototype=Object.create(lo.prototype),Co.prototype.constructor=Co,Co.prototype.__class__=Co,Co.__cache__={},e["LBSpaBody"]=Co,Co.prototype["Init"]=Co.prototype.Init=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!rr(r,t,e)},Co.prototype["GetTriangle"]=Co.prototype.GetTriangle=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!nr(r,t,e)},Co.prototype["CheckReference"]=Co.prototype.CheckReference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!pr(e,t)},Co.prototype["ComputeUnion"]=Co.prototype.ComputeUnion=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!ir(e,t)},Co.prototype["ComputeIntersection"]=Co.prototype.ComputeIntersection=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!or(e,t)},Co.prototype["ComputeDifference"]=Co.prototype.ComputeDifference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!_r(e,t)},Co.prototype["GetVolume"]=Co.prototype.GetVolume=function(){var t=this.ptr;return ar(t)},Co.prototype["__destroy__"]=Co.prototype.__destroy__=function(){var t=this.ptr;cr(t)},Ro.prototype=Object.create(lo.prototype),Ro.prototype.constructor=Ro,Ro.prototype.__class__=Ro,Ro.__cache__={},e["LBSpaPrimitiveCluster"]=Ro,Ro.prototype["SetIndexMatrix"]=Ro.prototype.SetIndexMatrix=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),ur(r,t,e)},Ro.prototype["RemoveAllMatrix"]=Ro.prototype.RemoveAllMatrix=function(){var t=this.ptr;lr(t)},Ro.prototype["EnableIndexSelected"]=Ro.prototype.EnableIndexSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),sr(r,t,e)},Ro.prototype["EnableAllIndexSelected"]=Ro.prototype.EnableAllIndexSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),yr(e,t)},Ro.prototype["__destroy__"]=Ro.prototype.__destroy__=function(){var t=this.ptr;dr(t)},Mo.prototype=Object.create(lo.prototype),Mo.prototype.constructor=Mo,Mo.prototype.__class__=Mo,Mo.__cache__={},e["LBEdgeFormer"]=Mo,Mo.prototype["SetPtAry"]=Mo.prototype.SetPtAry=function(t,e){var r=this.ptr;ho.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),fr(r,t,e)},Mo.prototype["SetIndexAry"]=Mo.prototype.SetIndexAry=function(t,e){var r=this.ptr;ho.prepare(),"object"==typeof t&&(t=Lo(t)),e&&"object"===typeof e&&(e=e.ptr),br(r,t,e)},Mo.prototype["FormEdge"]=Mo.prototype.FormEdge=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Sr(e,t)},Mo.prototype["GetEdgeIndexArySize"]=Mo.prototype.GetEdgeIndexArySize=function(){var t=this.ptr;return vr(t)},Mo.prototype["GetEdgeIndexAryVal"]=Mo.prototype.GetEdgeIndexAryVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),hr(e,t)},Mo.prototype["__destroy__"]=Mo.prototype.__destroy__=function(){var t=this.ptr;Br(t)},Vo.prototype=Object.create(lo.prototype),Vo.prototype.constructor=Vo,Vo.prototype.__class__=Vo,Vo.__cache__={},e["VoidPtr"]=Vo,Vo.prototype["__destroy__"]=Vo.prototype.__destroy__=function(){var t=this.ptr;gr(t)},wo.prototype=Object.create(lo.prototype),wo.prototype.constructor=wo,wo.prototype.__class__=wo,wo.__cache__={},e["LBSpaSelectSegResultItem"]=wo,wo.prototype["GetResultId"]=wo.prototype.GetResultId=function(){var t=this.ptr;return yo(Lr(t),Wo)},wo.prototype["GetPt0"]=wo.prototype.GetPt0=function(){var t=this.ptr;return yo(Pr(t),Yo)},wo.prototype["GetPt1"]=wo.prototype.GetPt1=function(){var t=this.ptr;return yo(Ir(t),Yo)},wo.prototype["GetSegPt"]=wo.prototype.GetSegPt=function(){var t=this.ptr;return yo(jr(t),Yo)},wo.prototype["GetPickPt"]=wo.prototype.GetPickPt=function(){var t=this.ptr;return yo(Gr(t),Yo)},wo.prototype["GetPickDist"]=wo.prototype.GetPickDist=function(){var t=this.ptr;return xr(t)},wo.prototype["__destroy__"]=wo.prototype.__destroy__=function(){var t=this.ptr;Cr(t)},To.prototype=Object.create(lo.prototype),To.prototype.constructor=To,To.prototype.__class__=To,To.__cache__={},e["LBSpaVec2"]=To,To.prototype["get_x"]=To.prototype.get_x=function(){var t=this.ptr;return Mr(t)},To.prototype["set_x"]=To.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Vr(e,t)},Object.defineProperty(To.prototype,"x",{get:To.prototype.get_x,set:To.prototype.set_x}),To.prototype["get_y"]=To.prototype.get_y=function(){var t=this.ptr;return wr(t)},To.prototype["set_y"]=To.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Tr(e,t)},Object.defineProperty(To.prototype,"y",{get:To.prototype.get_y,set:To.prototype.set_y}),To.prototype["__destroy__"]=To.prototype.__destroy__=function(){var t=this.ptr;Dr(t)},Do.prototype=Object.create(lo.prototype),Do.prototype.constructor=Do,Do.prototype.__class__=Do,Do.__cache__={},e["LBSpaSelectResult"]=Do,Do.prototype["ClearAll"]=Do.prototype.ClearAll=function(){var t=this.ptr;Er(t)},Do.prototype["GetResultIdSize"]=Do.prototype.GetResultIdSize=function(){var t=this.ptr;return Nr(t)},Do.prototype["GetResultId"]=Do.prototype.GetResultId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Or(e,t),Wo)},Do.prototype["GetTriResultElemSize"]=Do.prototype.GetTriResultElemSize=function(){var t=this.ptr;return kr(t)},Do.prototype["GetTriResultElem"]=Do.prototype.GetTriResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(zr(e,t),Uo)},Do.prototype["GetSegResultElemSize"]=Do.prototype.GetSegResultElemSize=function(){var t=this.ptr;return Fr(t)},Do.prototype["GetSegResultElem"]=Do.prototype.GetSegResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Wr(e,t),wo)},Do.prototype["__destroy__"]=Do.prototype.__destroy__=function(){var t=this.ptr;Hr(t)},Ao.prototype=Object.create(lo.prototype),Ao.prototype.constructor=Ao,Ao.prototype.__class__=Ao,Ao.__cache__={},e["LBSpaVec4"]=Ao,Ao.prototype["get_x"]=Ao.prototype.get_x=function(){var t=this.ptr;return Yr(t)},Ao.prototype["set_x"]=Ao.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),qr(e,t)},Object.defineProperty(Ao.prototype,"x",{get:Ao.prototype.get_x,set:Ao.prototype.set_x}),Ao.prototype["get_y"]=Ao.prototype.get_y=function(){var t=this.ptr;return Xr(t)},Ao.prototype["set_y"]=Ao.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Jr(e,t)},Object.defineProperty(Ao.prototype,"y",{get:Ao.prototype.get_y,set:Ao.prototype.set_y}),Ao.prototype["get_z"]=Ao.prototype.get_z=function(){var t=this.ptr;return Zr(t)},Ao.prototype["set_z"]=Ao.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Kr(e,t)},Object.defineProperty(Ao.prototype,"z",{get:Ao.prototype.get_z,set:Ao.prototype.set_z}),Ao.prototype["get_w"]=Ao.prototype.get_w=function(){var t=this.ptr;return Qr(t)},Ao.prototype["set_w"]=Ao.prototype.set_w=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),$r(e,t)},Object.defineProperty(Ao.prototype,"w",{get:Ao.prototype.get_w,set:Ao.prototype.set_w}),Ao.prototype["__destroy__"]=Ao.prototype.__destroy__=function(){var t=this.ptr;tn(t)},Eo.prototype=Object.create(lo.prototype),Eo.prototype.constructor=Eo,Eo.prototype.__class__=Eo,Eo.__cache__={},e["LBSpaGeoTool"]=Eo,Eo.prototype["GetRaySegIntersection"]=Eo.prototype.GetRaySegIntersection=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),rn(o,t,e,r,n,p,i)},Eo.prototype["GetTwoSegIntersection"]=Eo.prototype.GetTwoSegIntersection=function(t,e,r,n,p){var i=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),!!nn(i,t,e,r,n,p)},Eo.prototype["__destroy__"]=Eo.prototype.__destroy__=function(){var t=this.ptr;pn(t)},No.prototype=Object.create(lo.prototype),No.prototype.constructor=No,No.prototype.__class__=No,No.__cache__={},e["LBSpaMat"]=No,No.prototype["At"]=No.prototype.At=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(_n(e,t),Ao)},No.prototype["__destroy__"]=No.prototype.__destroy__=function(){var t=this.ptr;an(t)},Oo.prototype=Object.create(lo.prototype),Oo.prototype.constructor=Oo,Oo.prototype.__class__=Oo,Oo.__cache__={},e["LBSpaSerial"]=Oo,Oo.prototype["WriteSpatial"]=Oo.prototype.WriteSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),un(e,t)},Oo.prototype["WriteTriangle"]=Oo.prototype.WriteTriangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),ln(e,t)},Oo.prototype["GetBufferSize"]=Oo.prototype.GetBufferSize=function(){var t=this.ptr;return sn(t)},Oo.prototype["GetBufferVal"]=Oo.prototype.GetBufferVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yn(e,t)},Oo.prototype["ReadSpatial"]=Oo.prototype.ReadSpatial=function(t,e){var r=this.ptr;return ho.prepare(),"object"==typeof t&&(t=go(t)),e&&"object"===typeof e&&(e=e.ptr),yo(dn(r,t,e),Ho)},Oo.prototype["ReadTriangle"]=Oo.prototype.ReadTriangle=function(t,e){var r=this.ptr;return ho.prepare(),"object"==typeof t&&(t=go(t)),e&&"object"===typeof e&&(e=e.ptr),yo(mn(r,t,e),qo)},Oo.prototype["__destroy__"]=Oo.prototype.__destroy__=function(){var t=this.ptr;fn(t)},ko.prototype=Object.create(lo.prototype),ko.prototype.constructor=ko,ko.prototype.__class__=ko,ko.__cache__={},e["LBSpaMgr"]=ko,ko.prototype["CreateTriangleSpatial"]=ko.prototype.CreateTriangleSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Sn(e,t),Ho)},ko.prototype["CreateStepLineSpatial"]=ko.prototype.CreateStepLineSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(vn(e,t),Ho)},ko.prototype["CreatePrimitiveCluster"]=ko.prototype.CreatePrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(hn(e,t),Ro)},ko.prototype["AddPrimitiveSpatial"]=ko.prototype.AddPrimitiveSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Bn(e,t)},ko.prototype["GetPrimitiveCluster"]=ko.prototype.GetPrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(gn(e,t),Ro)},ko.prototype["EnablePrimitiveSelected"]=ko.prototype.EnablePrimitiveSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ln(r,t,e)},ko.prototype["IsPrimitiveSelected"]=ko.prototype.IsPrimitiveSelected=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Pn(e,t)},ko.prototype["EnableAllPrimitiveSelected"]=ko.prototype.EnableAllPrimitiveSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),In(e,t)},ko.prototype["SetPrimitiveSpatialMat"]=ko.prototype.SetPrimitiveSpatialMat=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!jn(r,t,e)},ko.prototype["RemovePrimitiveSpatial"]=ko.prototype.RemovePrimitiveSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Gn(e,t)},ko.prototype["RemoveAllPrimitiveSpatial"]=ko.prototype.RemoveAllPrimitiveSpatial=function(){var t=this.ptr;xn(t)},ko.prototype["Select"]=ko.prototype.Select=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!Cn(r,t,e)},ko.prototype["GetNumOfRemoveItems"]=ko.prototype.GetNumOfRemoveItems=function(){var t=this.ptr;return Rn(t)},ko.prototype["GetRemoveItemAt"]=ko.prototype.GetRemoveItemAt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Mn(e,t),Wo)},ko.prototype["__destroy__"]=ko.prototype.__destroy__=function(){var t=this.ptr;Vn(t)},zo.prototype=Object.create(lo.prototype),zo.prototype.constructor=zo,zo.prototype.__class__=zo,zo.__cache__={},e["LBSpaSkirtInfo"]=zo,zo.prototype["get_iPtSectIndex"]=zo.prototype.get_iPtSectIndex=function(){var t=this.ptr;return Tn(t)},zo.prototype["set_iPtSectIndex"]=zo.prototype.set_iPtSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Dn(e,t)},Object.defineProperty(zo.prototype,"iPtSectIndex",{get:zo.prototype.get_iPtSectIndex,set:zo.prototype.set_iPtSectIndex}),zo.prototype["get_iIndexSectIndex"]=zo.prototype.get_iIndexSectIndex=function(){var t=this.ptr;return An(t)},zo.prototype["set_iIndexSectIndex"]=zo.prototype.set_iIndexSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),En(e,t)},Object.defineProperty(zo.prototype,"iIndexSectIndex",{get:zo.prototype.get_iIndexSectIndex,set:zo.prototype.set_iIndexSectIndex}),zo.prototype["__destroy__"]=zo.prototype.__destroy__=function(){var t=this.ptr;Nn(t)},Fo.prototype=Object.create(lo.prototype),Fo.prototype.constructor=Fo,Fo.prototype.__class__=Fo,Fo.__cache__={},e["LBDeal"]=Fo,Fo.prototype["Init"]=Fo.prototype.Init=function(t,e,r,n){var p=this.ptr;return ho.prepare(),t=t&&"object"===typeof t?t.ptr:Bo(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),!!kn(p,t,e,r,n)},Fo.prototype["ComputeProjToCartesian"]=Fo.prototype.ComputeProjToCartesian=function(t,e,r,n){var p=this.ptr;ho.prepare(),"object"==typeof t&&(t=Po(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=Io(r)),n&&"object"===typeof n&&(n=n.ptr),zn(p,t,e,r,n)},Fo.prototype["ComputeCartesianToProj"]=Fo.prototype.ComputeCartesianToProj=function(t,e,r,n,p){var i=this.ptr;ho.prepare(),"object"==typeof t&&(t=Po(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=Io(r)),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),Fn(i,t,e,r,n,p)},Fo.prototype["TranformDegreeToProj"]=Fo.prototype.TranformDegreeToProj=function(t,e,r){var n=this.ptr;ho.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Wn(n,t,e,r)},Fo.prototype["TranformProjToDegree"]=Fo.prototype.TranformProjToDegree=function(t,e,r){var n=this.ptr;ho.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Hn(n,t,e,r)},Fo.prototype["__destroy__"]=Fo.prototype.__destroy__=function(){var t=this.ptr;Un(t)},Wo.prototype=Object.create(lo.prototype),Wo.prototype.constructor=Wo,Wo.prototype.__class__=Wo,Wo.__cache__={},e["LBSpaSelectResultId"]=Wo,Wo.prototype["get_iPrimitiveId"]=Wo.prototype.get_iPrimitiveId=function(){var t=this.ptr;return Yn(t)},Wo.prototype["set_iPrimitiveId"]=Wo.prototype.set_iPrimitiveId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),qn(e,t)},Object.defineProperty(Wo.prototype,"iPrimitiveId",{get:Wo.prototype.get_iPrimitiveId,set:Wo.prototype.set_iPrimitiveId}),Wo.prototype["get_bCluster"]=Wo.prototype.get_bCluster=function(){var t=this.ptr;return!!Xn(t)},Wo.prototype["set_bCluster"]=Wo.prototype.set_bCluster=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Jn(e,t)},Object.defineProperty(Wo.prototype,"bCluster",{get:Wo.prototype.get_bCluster,set:Wo.prototype.set_bCluster}),Wo.prototype["get_iBatchId"]=Wo.prototype.get_iBatchId=function(){var t=this.ptr;return Zn(t)},Wo.prototype["set_iBatchId"]=Wo.prototype.set_iBatchId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Kn(e,t)},Object.defineProperty(Wo.prototype,"iBatchId",{get:Wo.prototype.get_iBatchId,set:Wo.prototype.set_iBatchId}),Wo.prototype["__destroy__"]=Wo.prototype.__destroy__=function(){var t=this.ptr;Qn(t)},Ho.prototype=Object.create(lo.prototype),Ho.prototype.constructor=Ho,Ho.prototype.__class__=Ho,Ho.__cache__={},e["LBSpaPrimitiveSpatial"]=Ho,Ho.prototype["__destroy__"]=Ho.prototype.__destroy__=function(){var t=this.ptr;$n(t)},Uo.prototype=Object.create(lo.prototype),Uo.prototype.constructor=Uo,Uo.prototype.__class__=Uo,Uo.__cache__={},e["LBSpaSelectTriResultItem"]=Uo,Uo.prototype["GetResultId"]=Uo.prototype.GetResultId=function(){var t=this.ptr;return yo(tp(t),Wo)},Uo.prototype["GetPt0"]=Uo.prototype.GetPt0=function(){var t=this.ptr;return yo(ep(t),Yo)},Uo.prototype["GetPt1"]=Uo.prototype.GetPt1=function(){var t=this.ptr;return yo(rp(t),Yo)},Uo.prototype["GetPt2"]=Uo.prototype.GetPt2=function(){var t=this.ptr;return yo(np(t),Yo)},Uo.prototype["GetPickPt"]=Uo.prototype.GetPickPt=function(){var t=this.ptr;return yo(pp(t),Yo)},Uo.prototype["GetPickNormal"]=Uo.prototype.GetPickNormal=function(){var t=this.ptr;return yo(ip(t),Yo)},Uo.prototype["GetPickDist"]=Uo.prototype.GetPickDist=function(){var t=this.ptr;return op(t)},Uo.prototype["__destroy__"]=Uo.prototype.__destroy__=function(){var t=this.ptr;_p(t)},Yo.prototype=Object.create(lo.prototype),Yo.prototype.constructor=Yo,Yo.prototype.__class__=Yo,Yo.__cache__={},e["LBSpaVec"]=Yo,Yo.prototype["get_x"]=Yo.prototype.get_x=function(){var t=this.ptr;return cp(t)},Yo.prototype["set_x"]=Yo.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),up(e,t)},Object.defineProperty(Yo.prototype,"x",{get:Yo.prototype.get_x,set:Yo.prototype.set_x}),Yo.prototype["get_y"]=Yo.prototype.get_y=function(){var t=this.ptr;return lp(t)},Yo.prototype["set_y"]=Yo.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),sp(e,t)},Object.defineProperty(Yo.prototype,"y",{get:Yo.prototype.get_y,set:Yo.prototype.set_y}),Yo.prototype["get_z"]=Yo.prototype.get_z=function(){var t=this.ptr;return yp(t)},Yo.prototype["set_z"]=Yo.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),dp(e,t)},Object.defineProperty(Yo.prototype,"z",{get:Yo.prototype.get_z,set:Yo.prototype.set_z}),Yo.prototype["__destroy__"]=Yo.prototype.__destroy__=function(){var t=this.ptr;mp(t)},qo.prototype=Object.create(lo.prototype),qo.prototype.constructor=qo,qo.prototype.__class__=qo,qo.__cache__={},e["LBSpaTriangle"]=qo,qo.prototype["SetPtNum"]=qo.prototype.SetPtNum=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),bp(n,t,e,r)},qo.prototype["SetPtVal"]=qo.prototype.SetPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),Sp(p,t,e,r,n)},qo.prototype["SetUVVal"]=qo.prototype.SetUVVal=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),vp(n,t,e,r)},qo.prototype["SetNormVal"]=qo.prototype.SetNormVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),hp(p,t,e,r,n)},qo.prototype["SetIndexNum"]=qo.prototype.SetIndexNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Bp(e,t)},qo.prototype["SetIndexVal"]=qo.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),gp(r,t,e)},qo.prototype["AddTrangle"]=qo.prototype.AddTrangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Lp(e,t)},qo.prototype["GetPtNum"]=qo.prototype.GetPtNum=function(){var t=this.ptr;return Pp(t)},qo.prototype["GetPt"]=qo.prototype.GetPt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Ip(e,t),Yo)},qo.prototype["GetUV"]=qo.prototype.GetUV=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(jp(e,t),To)},qo.prototype["GetNorm"]=qo.prototype.GetNorm=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),yo(Gp(e,t),Yo)},qo.prototype["GetIndexNum"]=qo.prototype.GetIndexNum=function(){var t=this.ptr;return xp(t)},qo.prototype["GetIndex"]=qo.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Cp(e,t)},qo.prototype["__destroy__"]=qo.prototype.__destroy__=function(){var t=this.ptr;Rp(t)},Xo.prototype=Object.create(lo.prototype),Xo.prototype.constructor=Xo,Xo.prototype.__class__=Xo,Xo.__cache__={},e["LBSpaPrimitive"]=Xo,Xo.prototype["SetPtValNum"]=Xo.prototype.SetPtValNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Vp(r,t,e)},Xo.prototype["SetPtValVal"]=Xo.prototype.SetPtValVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),wp(r,t,e)},Xo.prototype["SetBatchIdVal"]=Xo.prototype.SetBatchIdVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Tp(r,t,e)},Xo.prototype["SetIndexNum"]=Xo.prototype.SetIndexNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Dp(r,t,e)},Xo.prototype["SetIndexVal"]=Xo.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ap(r,t,e)},Xo.prototype["SetEdgeCheckVal"]=Xo.prototype.SetEdgeCheckVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ep(r,t,e)},Xo.prototype["InitIndexByPt"]=Xo.prototype.InitIndexByPt=function(){var t=this.ptr;Np(t)},Xo.prototype["__destroy__"]=Xo.prototype.__destroy__=function(){var t=this.ptr;Op(t)},Jo.prototype=Object.create(lo.prototype),Jo.prototype.constructor=Jo,Jo.prototype.__class__=Jo,Jo.__cache__={},e["LBSpaSelectCondition"]=Jo,Jo.prototype["SetBox"]=Jo.prototype.SetBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),zp(o,t,e,r,n,p,i)},Jo.prototype["SetRay"]=Jo.prototype.SetRay=function(t,e,r,n,p,i,o,_,a){var c=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),Fp(c,t,e,r,n,p,i,o,_,a)},Jo.prototype["SetWedge"]=Jo.prototype.SetWedge=function(t,e,r,n,p,i,o,_,a,c){var u=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),c&&"object"===typeof c&&(c=c.ptr),Wp(u,t,e,r,n,p,i,o,_,a,c)},Jo.prototype["SetWedgeByBufferedPoints"]=Jo.prototype.SetWedgeByBufferedPoints=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Hp(n,t,e,r)},Jo.prototype["ClearBuffer"]=Jo.prototype.ClearBuffer=function(){var t=this.ptr;Up(t)},Jo.prototype["AddBuffer"]=Jo.prototype.AddBuffer=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Yp(n,t,e,r)},Jo.prototype["__destroy__"]=Jo.prototype.__destroy__=function(){var t=this.ptr;qp(t)},Zo.prototype=Object.create(lo.prototype),Zo.prototype.constructor=Zo,Zo.prototype.__class__=Zo,Zo.__cache__={},e["LBSpaBoxMgr"]=Zo,Zo.prototype["InsertBox"]=Zo.prototype.InsertBox=function(t,e,r,n,p,i,o){var _=this.ptr;ho.prepare(),t=t&&"object"===typeof t?t.ptr:Bo(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),Jp(_,t,e,r,n,p,i,o)},Zo.prototype["RemoveBox"]=Zo.prototype.RemoveBox=function(t){var e=this.ptr;ho.prepare(),t=t&&"object"===typeof t?t.ptr:Bo(t),Zp(e,t)},Zo.prototype["SetSelectBox"]=Zo.prototype.SetSelectBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),Kp(o,t,e,r,n,p,i)},Zo.prototype["Select"]=Zo.prototype.Select=function(){var t=this.ptr;Qp(t)},Zo.prototype["GetSelectedIdSize"]=Zo.prototype.GetSelectedIdSize=function(){var t=this.ptr;return $p(t)},Zo.prototype["GetSelectedId"]=Zo.prototype.GetSelectedId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),D(ti(e,t))},Zo.prototype["__destroy__"]=Zo.prototype.__destroy__=function(){var t=this.ptr;ei(t)},"function"===typeof e["onModuleParsed"]&&e["onModuleParsed"](),e.ready}}();t.materem=e}));