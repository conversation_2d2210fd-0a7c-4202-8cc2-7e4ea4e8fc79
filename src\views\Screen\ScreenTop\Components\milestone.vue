<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-23 11:10:31
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-24 16:52:08
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenTop\Components\milestone.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="milestone-container">
    <div class="close-btn" @click="handleClose">
      <img src="@/assets/CommonPopup/closeIcon.png" />
    </div>
  </div>
</template>

<script setup>
  import lib from '@/utils/lib.ts'
  const handleClose = lib.provideTools.handleClose.inject()
</script>

<style lang="scss" scoped>
  .milestone-container {
    position: absolute;
    z-index: 999;
    width: 1573px;
    height: 544px;
    background-image: url('@/assets/ScreenTop/milestone.png');
    background-size: cover;
    .close-btn {
      position: absolute;
      top: 15px;
      right: 10px;
      width: 50px;
      height: 50px;
      cursor: pointer;
    }
  }
</style>
