<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-06-20 11:23:59
 * @LastEditors: wmf '<EMAIL>'
 * @LastEditTime: 2025-06-09 16:23:29
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\ComprehensiveEvaluation\Components\evaluationItem.vue
 * @Description: 
 * 
-->
<template>
  <div class="content">
    <div class="content-item">
      <SubHeadLine>
        <div class="mt-6">{{ item.name }}</div>
        <div class="icon-group">
          <!-- 提示框 -->
          <el-popover placement="top" trigger="hover" popper-class="popper-box" effect="customized" width="280px">
            <template #reference>
              <img class="icon" :src="getAssetsFile(`ScreenLeft/ComprehensiveEvaluation/icon.png`)" />
            </template>
            <div class="tip-box">
              <div class="tip-title">{{ item.tooltipTitle }}</div>
              <div class="tip-content" v-html="item.tooltipContent"></div>
            </div>
          </el-popover>
          <!-- 图片按钮 -->
          <template v-if="showImageTooltip">
            <el-popover placement="top" trigger="hover" popper-class="image-popover" effect="customized">
              <template #reference>
                <el-icon :size="tooltipIconSize" :style="{ color: tooltipIconColor }">
                  <component :is="tooltipIcon" />
                </el-icon>
              </template>
              <div class="image-box">
                <slot name="image-content">
                  <img :src="imageUrl" class="preview-image" :style="{ width: imageWidth, height: imageHeight }" />
                </slot>
              </div>
            </el-popover>
          </template>
        </div>
      </SubHeadLine>
      <div class="main">
        <!-- <div class="left">
          <div class="value" :style="{ color: item.color }">
            <span style="font-size: 33px">{{ item.value || '一' }}</span>
            <span v-if="item.value" style="font-size: 20px">分</span>
          </div>
          <div class="describe">{{ item.describe }}</div>
        </div>
        <div class="right"> -->
        <!-- <Mychart :option="item.chartData" width="110px" height="100px"></Mychart> -->
        <ThreePie :data="item.chartData" :chartTitle="item.describe" :value="processVal"></ThreePie>
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import Mychart from '@Common/components/MyChart/index.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  import ThreePie from './threePie.vue'
  import { getAssetsFile } from '@/utils'
  import { Picture } from '@element-plus/icons-vue'

  const props = defineProps({
    item: {
      type: Array,
      default: () => []
    },
    showImageTooltip: {
      type: Boolean,
      default: false
    },
    tooltipIcon: {
      type: [String, Object],
      default: Picture
    },
    tooltipIconSize: {
      type: [String, Number],
      default: '20px'
    },
    tooltipIconColor: {
      type: String,
      default: '#00a3ff'
    },
    imageUrl: {
      type: String,
      default: ''
    },
    imageWidth: {
      type: String,
      default: '300px'
    },
    imageHeight: {
      type: String,
      default: '200px'
    }
  })
  watch(
    () => props.item,
    (newVal) => {
      console.log(newVal)
    },
    { deep: true, immediate: true }
  )
  const processVal = computed(() => {
    return props.item.chartData[0]?.value
  })
</script>

<style lang="scss" scoped>
  .content {
    .content-item {
      width: 280px;
      height: 150px;

      // background: url('@/assets/ScreenLeft/ComprehensiveEvaluation/bg.png');
      // background-size: cover;
      .icon-group {
        position: absolute;
        top: -2px;
        right: 85px;
        display: flex;
        gap: 8px;
        align-items: center;
        .icon {
          width: 16px;
          height: 16px;
          cursor: pointer;
          transition: transform 0.2s;
          &:hover {
            transform: scale(1.1);
          }
        }
      }
      .main {
        display: flex;
        justify-content: space-between;
        padding: 0 18px 26px 41px;
        .left {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .value {
            font-family: 'Alibaba PuHuiTi-Bold';
          }
          .describe {
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            font-weight: 400;
            line-height: 18px;
            color: #dbefff;
          }
        }
        .right {
          width: 106px;
          height: 83px;
          background: url('@/assets/ScreenLeft/ComprehensiveEvaluation/chartBg.png');
          background-size: cover;
        }
      }
    }
  }
</style>
<style>
  .el-popover {
    font-family: 'Alibaba PuHuiTi';

    --el-text-color-regular: #ffffff;
  }
  .el-popper.is-customized {
    padding: 0;
    background: rgb(2 17 27 / 71%);
    border: 1px solid #0080b6;
  }
  .el-popper.is-customized .el-popper__arrow::before {
    right: 0;
    background: rgb(2 17 27 / 71%);
  }
  .el-popover__popper {
    white-space: pre-line; /* 指定换行显示 */
  }
  .el-popover__title {
    color: #43ddff;
  }
  .popper-box {
    .tip-box {
      padding: 8px;
    }
    .tip-title {
      font-size: 14px;
      color: #43ddff;
    }
    .tip-content {
      margin-top: 7px;
      font-size: 12px;
    }
  }
  .custom-tip-box {
    position: absolute;
    top: -40px;
    left: -25px;
    width: 178px;
    height: 50px;
    padding-left: 8px;
    font-family: 'Alibaba PuHuiTi';
    font-weight: 400;
    line-height: 14px;
    color: #ffffff;
    background: url('@/assets/ScreenLeft/ComprehensiveEvaluation/tipBg.png');
    background-size: cover;
  }
  .image-popover {
    .image-box {
      width: 392px;
      height: 154px;
      .preview-image {
        width: 100%;
        height: 100%;

        /* object-fit: contain; */
      }
    }
  }
</style>
