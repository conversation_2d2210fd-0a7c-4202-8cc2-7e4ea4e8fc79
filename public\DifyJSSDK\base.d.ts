export declare const API_PREFIX = "/api";
export type WorkflowStartedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        workflow_id: string;
        sequence_number: number;
        created_at: number;
    };
};
export type WorkflowFinishedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        workflow_id: string;
        status: string;
        outputs: any;
        error: string;
        elapsed_time: number;
        total_tokens: number;
        total_steps: number;
        created_at: number;
        finished_at: number;
    };
};
export type NodeStartedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        node_id: string;
        node_type: string;
        index: number;
        predecessor_node_id?: string;
        inputs: any;
        created_at: number;
        extras?: any;
    };
};
export type NodeFinishedResponse = {
    task_id: string;
    workflow_run_id: string;
    event: string;
    data: {
        id: string;
        node_id: string;
        node_type: string;
        index: number;
        predecessor_node_id?: string;
        inputs: any;
        process_data: any;
        outputs: any;
        status: string;
        error: string;
        elapsed_time: number;
        execution_metadata: {
            total_tokens: number;
            total_price: number;
            currency: string;
        };
        created_at: number;
    };
};
export type IOnDataMoreInfo = {
    conversationId?: string;
    taskId?: string;
    messageId: string;
    errorMessage?: string;
    errorCode?: string;
};
export type IOnData = (message: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => void;
export type IOnThought = (thought: ThoughtItem) => void;
export type IOnFile = (file: VisionFile) => void;
export type IOnMessageEnd = (messageEnd: MessageEnd) => void;
export type IOnMessageReplace = (messageReplace: MessageReplace) => void;
export type IOnAnnotationReply = (messageReplace: AnnotationReply) => void;
export type IOnCompleted = (hasError?: boolean) => void;
export type IOnError = (msg: string, code?: string) => void;
export type IOnWorkflowStarted = (workflowStarted: WorkflowStartedResponse) => void;
export type IOnWorkflowFinished = (workflowFinished: WorkflowFinishedResponse) => void;
export type IOnNodeStarted = (nodeStarted: NodeStartedResponse) => void;
export type IOnNodeFinished = (nodeFinished: NodeFinishedResponse) => void;
export type ThoughtItem = {
    id: string;
    thought: string;
    message_id: string;
    conversation_id: string;
};
export type VisionFile = {
    id: string;
    url: string;
    type: string;
    belongs_to: string;
    mime_type: string;
    name: string;
};
export type MessageEnd = {
    id: string;
    task_id: string;
    message_id: string;
    conversation_id: string;
    metadata: {
        usage: {
            completion_tokens: number;
            prompt_tokens: number;
            total_tokens: number;
        };
    };
};
export type MessageReplace = {
    id: string;
    message_id: string;
    conversation_id: string;
    answer: string;
};
export type AnnotationReply = {
    id: string;
    annotation_id: string;
    message_id: string;
    conversation_id: string;
    answer: string;
    error?: string;
};
export type Feedbacktype = {
    rating: 'like' | 'dislike';
    user_id?: string;
};
export type IOtherOptions = {
    isPublicAPI?: boolean;
    bodyStringify?: boolean;
    needAllResponseContent?: boolean;
    deleteContentType?: boolean;
    onData?: IOnData;
    onThought?: IOnThought;
    onFile?: IOnFile;
    onMessageEnd?: IOnMessageEnd;
    onMessageReplace?: IOnMessageReplace;
    onError?: IOnError;
    onCompleted?: IOnCompleted;
    getAbortController?: (abortController: AbortController) => void;
    onWorkflowStarted?: IOnWorkflowStarted;
    onWorkflowFinished?: IOnWorkflowFinished;
    onNodeStarted?: IOnNodeStarted;
    onNodeFinished?: IOnNodeFinished;
    apiBaseUrl?: string;
};
export declare const upload: (fetchOptions: any, options: IOtherOptions) => Promise<any>;
export declare const ssePost: (url: string, fetchOptions: any, options: IOtherOptions) => Promise<void>;
export declare const request: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const get: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const post: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const put: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const del: (url: string, options?: any, otherOptions?: IOtherOptions) => Promise<any>;
export declare const postFormData: (url: string, formData: FormData, otherOptions?: IOtherOptions) => Promise<any>;
