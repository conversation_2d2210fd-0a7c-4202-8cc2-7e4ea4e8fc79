define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac"],(function(e,t,a,i){"use strict";var n=new i.Cartesian3,r=new i.Cartesian3;function u(e,u,o,s,d){if(!t.defined(e))throw new a.DeveloperError("cartesian is required.");if(!t.defined(u))throw new a.DeveloperError("oneOverRadii is required.");if(!t.defined(o))throw new a.DeveloperError("oneOverRadiiSquared is required.");if(!t.defined(s))throw new a.DeveloperError("centerToleranceSquared is required.");var h=e.x,l=e.y,c=e.z,f=u.x,m=u.y,C=u.z,p=h*h*f*f,g=l*l*m*m,M=c*c*C*C,_=p+g+M,w=Math.sqrt(1/_),O=i.Cartesian3.multiplyByScalar(e,w,n);if(_<s)return isFinite(w)?i.Cartesian3.clone(O,d):void 0;var v=o.x,y=o.y,T=o.z,q=r;q.x=O.x*v*2,q.y=O.y*y*2,q.z=O.z*T*2;var S,R,E,P,b,V,I,x,A,k,z,W=(1-w)*i.Cartesian3.magnitude(e)/(.5*i.Cartesian3.magnitude(q)),N=0;do{W-=N,E=1/(1+W*v),P=1/(1+W*y),b=1/(1+W*T),V=E*E,I=P*P,x=b*b,A=V*E,k=I*P,z=x*b,S=p*V+g*I+M*x-1,R=p*A*v+g*k*y+M*z*T;var L=-2*R;N=S/L}while(Math.abs(S)>i.CesiumMath.EPSILON12);return t.defined(d)?(d.x=h*E,d.y=l*P,d.z=c*b,d):new i.Cartesian3(h*E,l*P,c*b)}function o(e,a,i){this.longitude=t.defaultValue(e,0),this.latitude=t.defaultValue(a,0),this.height=t.defaultValue(i,0)}o.fromRadians=function(e,a,i,n){return i=t.defaultValue(i,0),t.defined(n)?(n.longitude=e,n.latitude=a,n.height=i,n):new o(e,a,i)},o.fromDegrees=function(e,t,a,n){return e=i.CesiumMath.toRadians(e),t=i.CesiumMath.toRadians(t),o.fromRadians(e,t,a,n)};var s=new i.Cartesian3,d=new i.Cartesian3,h=new i.Cartesian3,l=new i.Cartesian3(1/6378137,1/6378137,1/6356752.314245179),c=new i.Cartesian3(1/40680631590769,1/40680631590769,1/40408299984661.445),f=i.CesiumMath.EPSILON1;function m(e,n,r,u){n=t.defaultValue(n,0),r=t.defaultValue(r,0),u=t.defaultValue(u,0),a.Check.typeOf.number.greaterThanOrEquals("x",n,0),a.Check.typeOf.number.greaterThanOrEquals("y",r,0),a.Check.typeOf.number.greaterThanOrEquals("z",u,0),e._radii=new i.Cartesian3(n,r,u),e._radiiSquared=new i.Cartesian3(n*n,r*r,u*u),e._radiiToTheFourth=new i.Cartesian3(n*n*n*n,r*r*r*r,u*u*u*u),e._oneOverRadii=new i.Cartesian3(0===n?0:1/n,0===r?0:1/r,0===u?0:1/u),e._oneOverRadiiSquared=new i.Cartesian3(0===n?0:1/(n*n),0===r?0:1/(r*r),0===u?0:1/(u*u)),e._minimumRadius=Math.min(n,r,u),e._maximumRadius=Math.max(n,r,u),e._centerToleranceSquared=i.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function C(e,t,a){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,m(this,e,t,a)}o.fromCartesian=function(e,a,n){var r=t.defined(a)?a.oneOverRadii:l,m=t.defined(a)?a.oneOverRadiiSquared:c,C=t.defined(a)?a._centerToleranceSquared:f,p=u(e,r,m,C,d);if(t.defined(p)){var g=i.Cartesian3.multiplyComponents(p,m,s);g=i.Cartesian3.normalize(g,g);var M=i.Cartesian3.subtract(e,p,h),_=Math.atan2(g.y,g.x),w=Math.asin(g.z),O=i.CesiumMath.sign(i.Cartesian3.dot(M,e))*i.Cartesian3.magnitude(M);return t.defined(n)?(n.longitude=_,n.latitude=w,n.height=O,n):new o(_,w,O)}},o.toCartesian=function(e,t,a){return i.Cartesian3.fromRadians(e.longitude,e.latitude,e.height,t,a)},o.clone=function(e,a){if(t.defined(e))return t.defined(a)?(a.longitude=e.longitude,a.latitude=e.latitude,a.height=e.height,a):new o(e.longitude,e.latitude,e.height)},o.equals=function(e,a){return e===a||t.defined(e)&&t.defined(a)&&e.longitude===a.longitude&&e.latitude===a.latitude&&e.height===a.height},o.equalsEpsilon=function(e,a,i){return i=t.defaultValue(i,0),e===a||t.defined(e)&&t.defined(a)&&Math.abs(e.longitude-a.longitude)<=i&&Math.abs(e.latitude-a.latitude)<=i&&Math.abs(e.height-a.height)<=i},o.ZERO=Object.freeze(new o(0,0,0)),o.prototype.clone=function(e){return o.clone(this,e)},o.prototype.equals=function(e){return o.equals(this,e)},o.prototype.equalsEpsilon=function(e,t){return o.equalsEpsilon(this,e,t)},o.prototype.toString=function(){return"("+this.longitude+", "+this.latitude+", "+this.height+")"},Object.defineProperties(C.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),C.clone=function(e,a){if(t.defined(e)){var n=e._radii;return t.defined(a)?(i.Cartesian3.clone(n,a._radii),i.Cartesian3.clone(e._radiiSquared,a._radiiSquared),i.Cartesian3.clone(e._radiiToTheFourth,a._radiiToTheFourth),i.Cartesian3.clone(e._oneOverRadii,a._oneOverRadii),i.Cartesian3.clone(e._oneOverRadiiSquared,a._oneOverRadiiSquared),a._minimumRadius=e._minimumRadius,a._maximumRadius=e._maximumRadius,a._centerToleranceSquared=e._centerToleranceSquared,a):new C(n.x,n.y,n.z)}},C.fromCartesian3=function(e,a){return t.defined(a)||(a=new C),t.defined(e)?(m(a,e.x,e.y,e.z),a):a},C.WGS84=Object.freeze(new C(6378137,6378137,6356752.314245179)),C.UNIT_SPHERE=Object.freeze(new C(1,1,1)),C.MOON=Object.freeze(new C(i.CesiumMath.LUNAR_RADIUS,i.CesiumMath.LUNAR_RADIUS,i.CesiumMath.LUNAR_RADIUS)),C.prototype.clone=function(e){return C.clone(this,e)},C.packedLength=i.Cartesian3.packedLength,C.pack=function(e,n,r){return a.Check.typeOf.object("value",e),a.Check.defined("array",n),r=t.defaultValue(r,0),i.Cartesian3.pack(e._radii,n,r),n},C.unpack=function(e,n,r){a.Check.defined("array",e),n=t.defaultValue(n,0);var u=i.Cartesian3.unpack(e,n);return C.fromCartesian3(u,r)},C.prototype.geocentricSurfaceNormal=i.Cartesian3.normalize,C.prototype.geodeticSurfaceNormalCartographic=function(e,n){a.Check.typeOf.object("cartographic",e);var r=e.longitude,u=e.latitude,o=Math.cos(u),s=o*Math.cos(r),d=o*Math.sin(r),h=Math.sin(u);return t.defined(n)||(n=new i.Cartesian3),n.x=s,n.y=d,n.z=h,i.Cartesian3.normalize(n,n)},C.prototype.geodeticSurfaceNormal=function(e,a){return t.defined(a)||(a=new i.Cartesian3),a=i.Cartesian3.multiplyComponents(e,this._oneOverRadiiSquared,a),i.Cartesian3.normalize(a,a)};var p=new i.Cartesian3,g=new i.Cartesian3;C.prototype.cartographicToCartesian=function(e,a){var n=p,r=g;this.geodeticSurfaceNormalCartographic(e,n),i.Cartesian3.multiplyComponents(this._radiiSquared,n,r);var u=Math.sqrt(i.Cartesian3.dot(n,r));return i.Cartesian3.divideByScalar(r,u,r),i.Cartesian3.multiplyByScalar(n,e.height,n),t.defined(a)||(a=new i.Cartesian3),i.Cartesian3.add(r,n,a)},C.prototype.cartographicArrayToCartesianArray=function(e,i){a.Check.defined("cartographics",e);var n=e.length;t.defined(i)?i.length=n:i=new Array(n);for(var r=0;r<n;r++)i[r]=this.cartographicToCartesian(e[r],i[r]);return i};var M=new i.Cartesian3,_=new i.Cartesian3,w=new i.Cartesian3;function O(e,a,i,n){this.west=t.defaultValue(e,0),this.south=t.defaultValue(a,0),this.east=t.defaultValue(i,0),this.north=t.defaultValue(n,0)}C.prototype.cartesianToCartographic=function(e,a){var n=this.scaleToGeodeticSurface(e,_);if(t.defined(n)){var r=this.geodeticSurfaceNormal(n,M),u=i.Cartesian3.subtract(e,n,w),s=Math.atan2(r.y,r.x),d=Math.asin(r.z),h=i.CesiumMath.sign(i.Cartesian3.dot(u,e))*i.Cartesian3.magnitude(u);return t.defined(a)?(a.longitude=s,a.latitude=d,a.height=h,a):new o(s,d,h)}},C.prototype.cartesianArrayToCartographicArray=function(e,i){a.Check.defined("cartesians",e);var n=e.length;t.defined(i)?i.length=n:i=new Array(n);for(var r=0;r<n;++r)i[r]=this.cartesianToCartographic(e[r],i[r]);return i},C.prototype.scaleToGeodeticSurface=function(e,t){return u(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,t)},C.prototype.scaleToGeocentricSurface=function(e,n){a.Check.typeOf.object("cartesian",e),t.defined(n)||(n=new i.Cartesian3);var r=e.x,u=e.y,o=e.z,s=this._oneOverRadiiSquared,d=1/Math.sqrt(r*r*s.x+u*u*s.y+o*o*s.z);return i.Cartesian3.multiplyByScalar(e,d,n)},C.prototype.transformPositionToScaledSpace=function(e,a){return t.defined(a)||(a=new i.Cartesian3),i.Cartesian3.multiplyComponents(e,this._oneOverRadii,a)},C.prototype.transformPositionFromScaledSpace=function(e,a){return t.defined(a)||(a=new i.Cartesian3),i.Cartesian3.multiplyComponents(e,this._radii,a)},C.prototype.equals=function(e){return this===e||t.defined(e)&&i.Cartesian3.equals(this._radii,e._radii)},C.prototype.toString=function(){return this._radii.toString()},C.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,n,r){if(a.Check.typeOf.object("position",e),!i.CesiumMath.equalsEpsilon(this._radii.x,this._radii.y,i.CesiumMath.EPSILON15))throw new a.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");a.Check.typeOf.number.greaterThan("Ellipsoid.radii.z",this._radii.z,0),n=t.defaultValue(n,0);var u=this._squaredXOverSquaredZ;if(t.defined(r)||(r=new i.Cartesian3),r.x=0,r.y=0,r.z=e.z*(1-u),!(Math.abs(r.z)>=this._radii.z-n))return r},Object.defineProperties(O.prototype,{width:{get:function(){return O.computeWidth(this)}},height:{get:function(){return O.computeHeight(this)}}}),O.packedLength=4,O.pack=function(e,a,i){return i=t.defaultValue(i,0),a[i++]=e.west,a[i++]=e.south,a[i++]=e.east,a[i]=e.north,a},O.unpack=function(e,a,i){return a=t.defaultValue(a,0),t.defined(i)||(i=new O),i.west=e[a++],i.south=e[a++],i.east=e[a++],i.north=e[a],i},O.computeWidth=function(e){var t=e.east,a=e.west;return t<a&&(t+=i.CesiumMath.TWO_PI),t-a},O.computeHeight=function(e){return e.north-e.south},O.fromDegrees=function(e,a,n,r,u){return e=i.CesiumMath.toRadians(t.defaultValue(e,0)),a=i.CesiumMath.toRadians(t.defaultValue(a,0)),n=i.CesiumMath.toRadians(t.defaultValue(n,0)),r=i.CesiumMath.toRadians(t.defaultValue(r,0)),t.defined(u)?(u.west=e,u.south=a,u.east=n,u.north=r,u):new O(e,a,n,r)},O.fromRadians=function(e,a,i,n,r){return t.defined(r)?(r.west=t.defaultValue(e,0),r.south=t.defaultValue(a,0),r.east=t.defaultValue(i,0),r.north=t.defaultValue(n,0),r):new O(e,a,i,n)},O.fromCartographicArray=function(e,a){for(var n=Number.MAX_VALUE,r=-Number.MAX_VALUE,u=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,d=-Number.MAX_VALUE,h=0,l=e.length;h<l;h++){var c=e[h];n=Math.min(n,c.longitude),r=Math.max(r,c.longitude),s=Math.min(s,c.latitude),d=Math.max(d,c.latitude);var f=c.longitude>=0?c.longitude:c.longitude+i.CesiumMath.TWO_PI;u=Math.min(u,f),o=Math.max(o,f)}return r-n>o-u&&(n=u,r=o,r>i.CesiumMath.PI&&(r-=i.CesiumMath.TWO_PI),n>i.CesiumMath.PI&&(n-=i.CesiumMath.TWO_PI)),t.defined(a)?(a.west=n,a.south=s,a.east=r,a.north=d,a):new O(n,s,r,d)},O.fromCartesianArray=function(e,a,n){a=t.defaultValue(a,C.WGS84);for(var r=Number.MAX_VALUE,u=-Number.MAX_VALUE,o=Number.MAX_VALUE,s=-Number.MAX_VALUE,d=Number.MAX_VALUE,h=-Number.MAX_VALUE,l=0,c=e.length;l<c;l++){var f=a.cartesianToCartographic(e[l]);r=Math.min(r,f.longitude),u=Math.max(u,f.longitude),d=Math.min(d,f.latitude),h=Math.max(h,f.latitude);var m=f.longitude>=0?f.longitude:f.longitude+i.CesiumMath.TWO_PI;o=Math.min(o,m),s=Math.max(s,m)}return u-r>s-o&&(r=o,u=s,u>i.CesiumMath.PI&&(u-=i.CesiumMath.TWO_PI),r>i.CesiumMath.PI&&(r-=i.CesiumMath.TWO_PI)),t.defined(n)?(n.west=r,n.south=d,n.east=u,n.north=h,n):new O(r,d,u,h)},O.clone=function(e,a){if(t.defined(e))return t.defined(a)?(a.west=e.west,a.south=e.south,a.east=e.east,a.north=e.north,a):new O(e.west,e.south,e.east,e.north)},O.equalsEpsilon=function(e,a,i){return i=t.defaultValue(i,0),e===a||t.defined(e)&&t.defined(a)&&Math.abs(e.west-a.west)<=i&&Math.abs(e.south-a.south)<=i&&Math.abs(e.east-a.east)<=i&&Math.abs(e.north-a.north)<=i},O.prototype.clone=function(e){return O.clone(this,e)},O.prototype.equals=function(e){return O.equals(this,e)},O.equals=function(e,a){return e===a||t.defined(e)&&t.defined(a)&&e.west===a.west&&e.south===a.south&&e.east===a.east&&e.north===a.north},O.prototype.equalsEpsilon=function(e,a){return a=t.defaultValue(a,0),O.equalsEpsilon(this,e,a)},O.validate=function(e){a.Check.typeOf.object("rectangle",e);var t=e.north;a.Check.typeOf.number.greaterThanOrEquals("north",t,-i.CesiumMath.PI_OVER_TWO),a.Check.typeOf.number.lessThanOrEquals("north",t,i.CesiumMath.PI_OVER_TWO);var n=e.south;a.Check.typeOf.number.greaterThanOrEquals("south",n,-i.CesiumMath.PI_OVER_TWO),a.Check.typeOf.number.lessThanOrEquals("south",n,i.CesiumMath.PI_OVER_TWO);var r=e.west;a.Check.typeOf.number.greaterThanOrEquals("west",r,-Math.PI),a.Check.typeOf.number.lessThanOrEquals("west",r,Math.PI);var u=e.east;a.Check.typeOf.number.greaterThanOrEquals("east",u,-Math.PI),a.Check.typeOf.number.lessThanOrEquals("east",u,Math.PI)},O.southwest=function(e,a){return t.defined(a)?(a.longitude=e.west,a.latitude=e.south,a.height=0,a):new o(e.west,e.south)},O.northwest=function(e,a){return t.defined(a)?(a.longitude=e.west,a.latitude=e.north,a.height=0,a):new o(e.west,e.north)},O.northeast=function(e,a){return t.defined(a)?(a.longitude=e.east,a.latitude=e.north,a.height=0,a):new o(e.east,e.north)},O.southeast=function(e,a){return t.defined(a)?(a.longitude=e.east,a.latitude=e.south,a.height=0,a):new o(e.east,e.south)},O.center=function(e,a){var n=e.east,r=e.west;n<r&&(n+=i.CesiumMath.TWO_PI);var u=i.CesiumMath.negativePiToPi(.5*(r+n)),s=.5*(e.south+e.north);return t.defined(a)?(a.longitude=u,a.latitude=s,a.height=0,a):new o(u,s)},O.intersection=function(e,a,n){var r=e.east,u=e.west,o=a.east,s=a.west;r<u&&o>0?r+=i.CesiumMath.TWO_PI:o<s&&r>0&&(o+=i.CesiumMath.TWO_PI),r<u&&s<0?s+=i.CesiumMath.TWO_PI:o<s&&u<0&&(u+=i.CesiumMath.TWO_PI);var d=i.CesiumMath.negativePiToPi(Math.max(u,s)),h=i.CesiumMath.negativePiToPi(Math.min(r,o));if(!((e.west<e.east||a.west<a.east)&&h<=d)){var l=Math.max(e.south,a.south),c=Math.min(e.north,a.north);if(!(l>=c))return t.defined(n)?(n.west=d,n.south=l,n.east=h,n.north=c,n):new O(d,l,h,c)}},O.simpleIntersection=function(e,a,i){var n=Math.max(e.west,a.west),r=Math.max(e.south,a.south),u=Math.min(e.east,a.east),o=Math.min(e.north,a.north);if(!(r>=o||n>=u))return t.defined(i)?(i.west=n,i.south=r,i.east=u,i.north=o,i):new O(n,r,u,o)},O.union=function(e,a,n){t.defined(n)||(n=new O);var r=e.east,u=e.west,o=a.east,s=a.west;r<u&&o>0?r+=i.CesiumMath.TWO_PI:o<s&&r>0&&(o+=i.CesiumMath.TWO_PI),r<u&&s<0?s+=i.CesiumMath.TWO_PI:o<s&&u<0&&(u+=i.CesiumMath.TWO_PI);var d=i.CesiumMath.convertLongitudeRange(Math.min(u,s)),h=i.CesiumMath.convertLongitudeRange(Math.max(r,o));return n.west=d,n.south=Math.min(e.south,a.south),n.east=h,n.north=Math.max(e.north,a.north),n},O.expand=function(e,a,i){return t.defined(i)||(i=new O),i.west=Math.min(e.west,a.longitude),i.south=Math.min(e.south,a.latitude),i.east=Math.max(e.east,a.longitude),i.north=Math.max(e.north,a.latitude),i},O.contains=function(e,t){var a=t.longitude,n=t.latitude,r=e.west,u=e.east;return u<r&&(u+=i.CesiumMath.TWO_PI,a<0&&(a+=i.CesiumMath.TWO_PI)),(a>r||i.CesiumMath.equalsEpsilon(a,r,i.CesiumMath.EPSILON14))&&(a<u||i.CesiumMath.equalsEpsilon(a,u,i.CesiumMath.EPSILON14))&&n>=e.south&&n<=e.north};var v=new o;O.subsample=function(e,a,n,r){a=t.defaultValue(a,C.WGS84),n=t.defaultValue(n,0),t.defined(r)||(r=[]);var u=0,o=e.north,s=e.south,d=e.east,h=e.west,l=v;l.height=n,l.longitude=h,l.latitude=o,r[u]=a.cartographicToCartesian(l,r[u]),u++,l.longitude=d,r[u]=a.cartographicToCartesian(l,r[u]),u++,l.latitude=s,r[u]=a.cartographicToCartesian(l,r[u]),u++,l.longitude=h,r[u]=a.cartographicToCartesian(l,r[u]),u++,l.latitude=o<0?o:s>0?s:0;for(var c=1;c<8;++c)l.longitude=-Math.PI+c*i.CesiumMath.PI_OVER_TWO,O.contains(e,l)&&(r[u]=a.cartographicToCartesian(l,r[u]),u++);return 0===l.latitude&&(l.longitude=h,r[u]=a.cartographicToCartesian(l,r[u]),u++,l.longitude=d,r[u]=a.cartographicToCartesian(l,r[u]),u++),r.length=u,r},O.MAX_VALUE=Object.freeze(new O(-Math.PI,-i.CesiumMath.PI_OVER_TWO,Math.PI,i.CesiumMath.PI_OVER_TWO)),e.Cartographic=o,e.Ellipsoid=C,e.Rectangle=O}));