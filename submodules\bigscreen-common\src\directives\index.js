/*
 * @Author: ya<PERSON>hen <EMAIL>
 * @Date: 2023-09-25 16:39:45
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2024-07-10 11:27:43
 * @Description: 全局指令
 *
 */
import autoScroll from './autoScroll' // 列表自动滚动
import closeTo from './closeTo' // 挂载到目标div边上
import drag from './drag' // 拖拽指令
import dragFixedRange from './dragFixedRange' // 固定范围拖拽
import iscroll from './iscroll' // iscroll.js滚动指令
import loadmore from './loadmore' // 列表触底指令
import tooltip from './tooltip' // 文字溢出显示省略号鼠标移入显示提示框
// 指令对象
const directives = {
  drag,
  tooltip,
  dragFixedRange,
  closeTo,
  autoScroll,
  iscroll,
  loadmore
}

export default {
  install(app) {
    Object.keys(directives).forEach((key) => {
      app.directive(key, directives[key])
    })
  }
}
