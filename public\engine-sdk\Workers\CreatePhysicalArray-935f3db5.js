define(["exports","./when-1807bd8d","./Cartesian2-d051b2ac","./Matrix4-a50b021f","./PrimitiveType-ec02f806"],(function(e,a,r,t,i){"use strict";class n{}var l=new r.Cartesian3,f=new t.Matrix4,o=new r.Cartesian3;function d(e,r,t,i,n){var l,f=a.defined(t)&&t.length>0;for(e.SetPtValNum(r.length,f),l=0;l<r.length;++l)e.SetPtValVal(l,r[l]);if(f)for(l=0;l<t.length;++l)e.SetBatchIdVal(l,t[l]);if(a.defined(i)&&i.length>0){var o=a.defined(n)&&n.length>0;for(e.SetIndexNum(i.length,o),l=0;l<i.length;++l)e.SetIndexVal(l,i[l]);if(o)for(l=0;l<n.length;++l)e.SetEdgeCheckVal(l,n[l])}else e.InitIndexByPt()}n.createPhysicalArrayFromTerrain=function(e,i,n,S,h){var s=a.defined(n);n=s?n:r.Cartesian3.ZERO,r.Cartesian3.negate(n,l),t.Matrix4.fromTranslation(l,f);for(var y=new Float32Array(3*S.length),v=0,g=0;g<S.length;++g){var c=S[g];t.Matrix4.multiplyByPoint(f,c,o),y[v++]=o.x,y[v++]=o.y,y[v++]=o.z}var p=new e.LBSpaPrimitive;d(p,y,void 0,h,void 0);var u=i.CreateTriangleSpatial(p),w=new e.LBSpaSerial;w.WriteSpatial(u);for(var B=new Uint8Array(w.GetBufferSize()),P=0;P<B.length;++P)B[P]=w.GetBufferVal(P);return e.destroy(w),e.destroy(u),B},n.createPhysicalArrayFromModel=function(e,a,r,t,n,l,f){var o,S=new e.LBSpaPrimitive;d(S,t,n,l,f),o=i.PrimitiveType.LINES===r?a.CreateStepLineSpatial(S):a.CreateTriangleSpatial(S);var h=new e.LBSpaSerial;h.WriteSpatial(o);for(var s=new Uint8Array(h.GetBufferSize()),y=0;y<s.length;++y)s[y]=h.GetBufferVal(y);return e.destroy(h),e.destroy(o),s},e.CreatePhysicalArray=n}));