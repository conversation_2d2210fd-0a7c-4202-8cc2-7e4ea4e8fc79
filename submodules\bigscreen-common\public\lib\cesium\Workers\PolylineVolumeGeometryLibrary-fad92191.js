/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./EllipsoidTangentPlane-c2c2ef6e","./Math-b5f4d889","./PolylinePipeline-1a06b90f","./Transforms-42ed7720","./defined-3b3eb2ba"],(function(e,a,t,n,r,i,s,o){"use strict";var l=Object.freeze({ROUNDED:0,MITERED:1,BEVELED:2});const c={};function C(e,a){o.defined(c[e])||(c[e]=!0,console.warn(o.defaultValue(a,e)))}C.geometryOutlines="Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.",C.geometryZIndex="Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored",C.geometryHeightReference="Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored",C.geometryExtrudedHeightReference="Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored";const u=[new a.Cartesian3,new a.Cartesian3],d=new a.Cartesian3,g=new a.Cartesian3,y=new a.Cartesian3,f=new a.Cartesian3,h=new a.Cartesian3,m=new a.Cartesian3,p=new a.Cartesian3,w=new a.Cartesian3,x=new a.Cartesian3,b=new a.Cartesian3,E=new a.Cartesian3,P={};let M=new t.Cartographic;function T(e,t,n,r){const i=e[0],s=e[1],o=a.Cartesian3.angleBetween(i,s),l=Math.ceil(o/r),c=new Array(l);let C;if(t===n){for(C=0;C<l;C++)c[C]=t;return c.push(n),c}const u=(n-t)/l;for(C=1;C<l;C++){const e=t+C*u;c[C]=e}return c[0]=t,c.push(n),c}const B=new a.Cartesian3,z=new a.Cartesian3;const S=new a.Cartesian3(-1,0,0);let A=new t.Matrix4;const R=new t.Matrix4;let D=new t.Matrix3;const O=t.Matrix3.IDENTITY.clone(),I=new a.Cartesian3,V=new t.Cartesian4,v=new a.Cartesian3;function N(e,r,i,o,l,c,C,u){let d=I,g=V;A=s.Transforms.eastNorthUpToFixedFrame(e,l,A),d=t.Matrix4.multiplyByPointAsVector(A,S,d),d=a.Cartesian3.normalize(d,d);const y=function(e,t,r,i){const s=new n.EllipsoidTangentPlane(r,i),o=s.projectPointOntoPlane(a.Cartesian3.add(r,e,B),B),l=s.projectPointOntoPlane(a.Cartesian3.add(r,t,z),z),c=a.Cartesian2.angleBetween(o,l);return l.x*o.y-l.y*o.x>=0?-c:c}(d,r,e,l);D=t.Matrix3.fromRotationZ(y,D),v.z=c,A=t.Matrix4.multiplyTransformation(A,t.Matrix4.fromRotationTranslation(D,v,R),A);const f=O;f[0]=C;for(let e=0;e<u;e++)for(let e=0;e<i.length;e+=3)g=a.Cartesian3.fromArray(i,e,g),g=t.Matrix3.multiplyByVector(f,g,g),g=t.Matrix4.multiplyByPoint(A,g,g),o.push(g.x,g.y,g.z);return o}const G=new a.Cartesian3;function H(e,t,n,r,i,s,o){for(let l=0;l<e.length;l+=3){r=N(a.Cartesian3.fromArray(e,l,G),t,n,r,i,s[l/3],o,1)}return r}function L(e,a){const t=e.length,n=new Array(3*t);let r=0;const i=a.x+a.width/2,s=a.y+a.height/2;for(let a=0;a<t;a++)n[r++]=e[a].x-i,n[r++]=0,n[r++]=e[a].y-s;return n}const j=new s.Quaternion,Q=new a.Cartesian3,q=new t.Matrix3;function F(e,n,i,o,c,C,u,d,g,y){const f=a.Cartesian3.angleBetween(a.Cartesian3.subtract(n,e,b),a.Cartesian3.subtract(i,e,E)),h=o===l.BEVELED?0:Math.ceil(f/r.CesiumMath.toRadians(5));let m,p,w;if(m=c?t.Matrix3.fromQuaternion(s.Quaternion.fromAxisAngle(a.Cartesian3.negate(e,b),f/(h+1),j),q):t.Matrix3.fromQuaternion(s.Quaternion.fromAxisAngle(e,f/(h+1),j),q),n=a.Cartesian3.clone(n,Q),h>0){const r=y?2:1;for(let i=0;i<h;i++)n=t.Matrix3.multiplyByVector(m,n,n),p=a.Cartesian3.subtract(n,e,b),p=a.Cartesian3.normalize(p,p),c||(p=a.Cartesian3.negate(p,p)),w=C.scaleToGeodeticSurface(n,E),u=N(w,p,d,u,C,g,1,r)}else p=a.Cartesian3.subtract(n,e,b),p=a.Cartesian3.normalize(p,p),c||(p=a.Cartesian3.negate(p,p)),w=C.scaleToGeodeticSurface(n,E),u=N(w,p,d,u,C,g,1,1),i=a.Cartesian3.clone(i,Q),p=a.Cartesian3.subtract(i,e,b),p=a.Cartesian3.normalize(p,p),c||(p=a.Cartesian3.negate(p,p)),w=C.scaleToGeodeticSurface(i,E),u=N(w,p,d,u,C,g,1,1);return u}P.removeDuplicatesFromShape=function(e){const t=e.length,n=[];for(let r=t-1,i=0;i<t;r=i++){const t=e[r],s=e[i];a.Cartesian2.equals(t,s)||n.push(s)}return n},P.angleIsGreaterThanPi=function(e,t,r,i){const s=new n.EllipsoidTangentPlane(r,i),o=s.projectPointOntoPlane(a.Cartesian3.add(r,e,B),B),l=s.projectPointOntoPlane(a.Cartesian3.add(r,t,z),z);return l.x*o.y-l.y*o.x>=0};const U=new a.Cartesian3,_=new a.Cartesian3;P.computePositions=function(e,t,n,s,o){const c=s._ellipsoid,E=function(e,a){const t=new Array(e.length);for(let n=0;n<e.length;n++){const r=e[n];M=a.cartesianToCartographic(r,M),t[n]=M.height,e[n]=a.scaleToGeodeticSurface(r,r)}return t}(e,c),B=s._granularity,z=s._cornerType,S=o?function(e,a){const t=e.length,n=new Array(6*t);let r=0;const i=a.x+a.width/2,s=a.y+a.height/2;let o=e[0];n[r++]=o.x-i,n[r++]=0,n[r++]=o.y-s;for(let a=1;a<t;a++){o=e[a];const t=o.x-i,l=o.y-s;n[r++]=t,n[r++]=0,n[r++]=l,n[r++]=t,n[r++]=0,n[r++]=l}return o=e[0],n[r++]=o.x-i,n[r++]=0,n[r++]=o.y-s,n}(t,n):L(t,n),A=o?L(t,n):void 0,R=n.height/2,D=n.width/2;let O=e.length,I=[],V=o?[]:void 0,v=d,G=g,j=y,Q=f,q=h,Z=m,W=p,Y=w,k=x,J=e[0],K=e[1];Q=c.geodeticSurfaceNormal(J,Q),v=a.Cartesian3.subtract(K,J,v),v=a.Cartesian3.normalize(v,v),Y=a.Cartesian3.cross(Q,v,Y),Y=a.Cartesian3.normalize(Y,Y);let X,$,ee=E[0],ae=E[1];o&&(V=N(J,Y,A,V,c,ee+R,1,1)),k=a.Cartesian3.clone(J,k),J=K,G=a.Cartesian3.negate(v,G);for(let t=1;t<O-1;t++){const n=o?2:1;if(K=e[t+1],J.equals(K)){C("Positions are too close and are considered equivalent with rounding error.");continue}v=a.Cartesian3.subtract(K,J,v),v=a.Cartesian3.normalize(v,v),j=a.Cartesian3.add(v,G,j),j=a.Cartesian3.normalize(j,j),Q=c.geodeticSurfaceNormal(J,Q);const s=a.Cartesian3.multiplyByScalar(Q,a.Cartesian3.dot(v,Q),U);a.Cartesian3.subtract(v,s,s),a.Cartesian3.normalize(s,s);const d=a.Cartesian3.multiplyByScalar(Q,a.Cartesian3.dot(G,Q),_);a.Cartesian3.subtract(G,d,d),a.Cartesian3.normalize(d,d);if(!r.CesiumMath.equalsEpsilon(Math.abs(a.Cartesian3.dot(s,d)),1,r.CesiumMath.EPSILON7)){j=a.Cartesian3.cross(j,Q,j),j=a.Cartesian3.cross(Q,j,j),j=a.Cartesian3.normalize(j,j);const e=1/Math.max(.25,a.Cartesian3.magnitude(a.Cartesian3.cross(j,G,b))),t=P.angleIsGreaterThanPi(v,G,J,c);t?(q=a.Cartesian3.add(J,a.Cartesian3.multiplyByScalar(j,e*D,j),q),Z=a.Cartesian3.add(q,a.Cartesian3.multiplyByScalar(Y,D,Z),Z),u[0]=a.Cartesian3.clone(k,u[0]),u[1]=a.Cartesian3.clone(Z,u[1]),X=T(u,ee+R,ae+R,B),$=i.PolylinePipeline.generateArc({positions:u,granularity:B,ellipsoid:c}),I=H($,Y,S,I,c,X,1),Y=a.Cartesian3.cross(Q,v,Y),Y=a.Cartesian3.normalize(Y,Y),W=a.Cartesian3.add(q,a.Cartesian3.multiplyByScalar(Y,D,W),W),z===l.ROUNDED||z===l.BEVELED?F(q,Z,W,z,t,c,I,S,ae+R,o):(j=a.Cartesian3.negate(j,j),I=N(J,j,S,I,c,ae+R,e,n)),k=a.Cartesian3.clone(W,k)):(q=a.Cartesian3.add(J,a.Cartesian3.multiplyByScalar(j,e*D,j),q),Z=a.Cartesian3.add(q,a.Cartesian3.multiplyByScalar(Y,-D,Z),Z),u[0]=a.Cartesian3.clone(k,u[0]),u[1]=a.Cartesian3.clone(Z,u[1]),X=T(u,ee+R,ae+R,B),$=i.PolylinePipeline.generateArc({positions:u,granularity:B,ellipsoid:c}),I=H($,Y,S,I,c,X,1),Y=a.Cartesian3.cross(Q,v,Y),Y=a.Cartesian3.normalize(Y,Y),W=a.Cartesian3.add(q,a.Cartesian3.multiplyByScalar(Y,-D,W),W),z===l.ROUNDED||z===l.BEVELED?F(q,Z,W,z,t,c,I,S,ae+R,o):I=N(J,j,S,I,c,ae+R,e,n),k=a.Cartesian3.clone(W,k)),G=a.Cartesian3.negate(v,G)}else I=N(k,Y,S,I,c,ee+R,1,1),k=J;ee=ae,ae=E[t+1],J=K}u[0]=a.Cartesian3.clone(k,u[0]),u[1]=a.Cartesian3.clone(J,u[1]),X=T(u,ee+R,ae+R,B),$=i.PolylinePipeline.generateArc({positions:u,granularity:B,ellipsoid:c}),I=H($,Y,S,I,c,X,1),o&&(V=N(J,Y,A,V,c,ae+R,1,1)),O=I.length;const te=o?O+V.length:O,ne=new Float64Array(te);return ne.set(I),o&&ne.set(V,O),ne};var Z=P;e.CornerType=l,e.PolylineVolumeGeometryLibrary=Z,e.oneTimeWarning=C}));
