<template>
  <div class="dynamic-maintenance-container">
    <HeadLine>
      <template #title>动态养护</template>
      <div class="content">
        <WorkControl></WorkControl>
        <TaskAssess></TaskAssess>
      </div>
    </HeadLine>
  </div>
</template>

<script setup>
  import TaskAssess from './Components/TaskAssess/index.vue'
  import WorkControl from './Components/WorkControl/index.vue'
  import HeadLine from '@/components/HeadLine/index.vue'
</script>

<style lang="scss" scoped>
  .dynamic-maintenance-container {
    .content {
      display: flex;
      justify-content: space-between;
      width: 1317px;
      height: 439px;
    }
  }
</style>
