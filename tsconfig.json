{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": false, //严格模式
    "noImplicitAny": false, // 是否在表达式和声明上有隐含的any类型时报错
    "noLib": false,
    "noEmit": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "checkJs": false,
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["vite/client", "unplugin-icons/types/vue", "element-plus/global", "vue3-baidu-map-gl/volar"],
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "allowSyntheticDefaultImports": true /* 允许默认导入 */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
 
 
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment"
  },
  "include": ["src/**/*.ts", "src/**/*.vue", "src/typings/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "auto-imports.d.ts", "components.d.ts"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}