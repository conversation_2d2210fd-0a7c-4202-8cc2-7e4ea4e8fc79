import ClockRange from"../../Core/ClockRange.js";import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import JulianDate from"../../Core/JulianDate.js";import getElement from"../getElement.js";import TimelineHighlightRange from"./TimelineHighlightRange.js";import TimelineTrack from"./TimelineTrack.js";var timelineWheelDelta=1e12,timelineMouseMode={none:0,scrub:1,slide:2,zoom:3,touchOnly:4},timelineTouchMode={none:0,scrub:1,slideZoom:2,singleTap:3,ignore:4},timelineTicScales=[.001,.002,.005,.01,.02,.05,.1,.25,.5,1,2,5,10,15,30,60,120,300,600,900,1800,3600,7200,14400,21600,43200,86400,172800,345600,604800,1296e3,2592e3,5184e3,7776e3,15552e3,31536e3,63072e3,126144e3,15768e4,31536e4,63072e4,126144e4,15768e5,31536e5,63072e5,126144e5,15768e6,31536e6],timelineMonthNames=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Timeline(e,t){if(!defined(e))throw new DeveloperError("container is required.");if(!defined(t))throw new DeveloperError("clock is required.");e=getElement(e);var i=e.ownerDocument;this.container=e;var n=i.createElement("div");n.className="cesium-timeline-main",e.appendChild(n),this._topDiv=n,this._endJulian=void 0,this._epochJulian=void 0,this._lastXPos=void 0,this._scrubElement=void 0,this._startJulian=void 0,this._timeBarSecondsSpan=void 0,this._clock=t,this._scrubJulian=t.currentTime,this._mainTicSpan=-1,this._mouseMode=timelineMouseMode.none,this._touchMode=timelineTouchMode.none,this._touchState={centerX:0,spanX:0},this._mouseX=0,this._timelineDrag=0,this._timelineDragLocation=void 0,this._lastHeight=void 0,this._lastWidth=void 0,this._topDiv.innerHTML='<div class="cesium-timeline-bar"></div><div class="cesium-timeline-trackContainer"><canvas class="cesium-timeline-tracks" width="10" height="1"></canvas></div><div class="cesium-timeline-needle"></div><span class="cesium-timeline-ruler"></span>',this._timeBarEle=this._topDiv.childNodes[0],this._trackContainer=this._topDiv.childNodes[1],this._trackListEle=this._topDiv.childNodes[1].childNodes[0],this._needleEle=this._topDiv.childNodes[2],this._rulerEle=this._topDiv.childNodes[3],this._context=this._trackListEle.getContext("2d"),this._trackList=[],this._highlightRanges=[],this.zoomTo(t.startTime,t.stopTime),this._onMouseDown=createMouseDownCallback(this),this._onMouseUp=createMouseUpCallback(this),this._onMouseMove=createMouseMoveCallback(this),this._onMouseWheel=createMouseWheelCallback(this),this._onTouchStart=createTouchStartCallback(this),this._onTouchMove=createTouchMoveCallback(this),this._onTouchEnd=createTouchEndCallback(this);var o=this._timeBarEle;i.addEventListener("mouseup",this._onMouseUp,!1),i.addEventListener("mousemove",this._onMouseMove,!1),o.addEventListener("mousedown",this._onMouseDown,!1),o.addEventListener("DOMMouseScroll",this._onMouseWheel,!1),o.addEventListener("mousewheel",this._onMouseWheel,!1),o.addEventListener("touchstart",this._onTouchStart,!1),o.addEventListener("touchmove",this._onTouchMove,!1),o.addEventListener("touchend",this._onTouchEnd,!1),o.addEventListener("touchcancel",this._onTouchEnd,!1),this._topDiv.oncontextmenu=function(){return!1},t.onTick.addEventListener(this.updateFromClock,this),this.updateFromClock()}function twoDigits(e){return e<10?"0"+e.toString():e.toString()}function createMouseDownCallback(e){return function(t){e._mouseMode!==timelineMouseMode.touchOnly&&(0===t.button?(e._mouseMode=timelineMouseMode.scrub,e._scrubElement&&(e._scrubElement.style.backgroundPosition="-16px 0"),e._onMouseMove(t)):(e._mouseX=t.clientX,2===t.button?e._mouseMode=timelineMouseMode.zoom:e._mouseMode=timelineMouseMode.slide)),t.preventDefault()}}function createMouseUpCallback(e){return function(t){e._mouseMode=timelineMouseMode.none,e._scrubElement&&(e._scrubElement.style.backgroundPosition="0 0"),e._timelineDrag=0,e._timelineDragLocation=void 0}}function createMouseMoveCallback(e){return function(t){var i;if(e._mouseMode===timelineMouseMode.scrub){t.preventDefault();var n=t.clientX-e._topDiv.getBoundingClientRect().left;n<0?(e._timelineDragLocation=0,e._timelineDrag=-.01*e._timeBarSecondsSpan):n>e._topDiv.clientWidth?(e._timelineDragLocation=e._topDiv.clientWidth,e._timelineDrag=.01*e._timeBarSecondsSpan):(e._timelineDragLocation=void 0,e._setTimeBarTime(n,n*e._timeBarSecondsSpan/e._topDiv.clientWidth))}else if(e._mouseMode===timelineMouseMode.slide){if(i=e._mouseX-t.clientX,e._mouseX=t.clientX,0!==i){var o=i*e._timeBarSecondsSpan/e._topDiv.clientWidth;e.zoomTo(JulianDate.addSeconds(e._startJulian,o,new JulianDate),JulianDate.addSeconds(e._endJulian,o,new JulianDate))}}else e._mouseMode===timelineMouseMode.zoom&&(i=e._mouseX-t.clientX,e._mouseX=t.clientX,0!==i&&e.zoomFrom(Math.pow(1.01,i)))}}function createMouseWheelCallback(e){return function(t){var i=t.wheelDeltaY||t.wheelDelta||-t.detail;timelineWheelDelta=Math.max(Math.min(Math.abs(i),timelineWheelDelta),1),i/=timelineWheelDelta,e.zoomFrom(Math.pow(1.05,-i))}}function createTouchStartCallback(e){return function(t){var i,n,o=t.touches.length,a=e._topDiv.getBoundingClientRect().left;t.preventDefault(),e._mouseMode=timelineMouseMode.touchOnly,1===o?(i=JulianDate.secondsDifference(e._scrubJulian,e._startJulian),n=Math.round(i*e._topDiv.clientWidth/e._timeBarSecondsSpan+a),Math.abs(t.touches[0].clientX-n)<50?(e._touchMode=timelineTouchMode.scrub,e._scrubElement&&(e._scrubElement.style.backgroundPosition=1===o?"-16px 0":"0 0")):(e._touchMode=timelineTouchMode.singleTap,e._touchState.centerX=t.touches[0].clientX-a)):2===o?(e._touchMode=timelineTouchMode.slideZoom,e._touchState.centerX=.5*(t.touches[0].clientX+t.touches[1].clientX)-a,e._touchState.spanX=Math.abs(t.touches[0].clientX-t.touches[1].clientX)):e._touchMode=timelineTouchMode.ignore}}function createTouchEndCallback(e){return function(t){var i=t.touches.length,n=e._topDiv.getBoundingClientRect().left;e._touchMode===timelineTouchMode.singleTap?(e._touchMode=timelineTouchMode.scrub,e._onTouchMove(t)):e._touchMode===timelineTouchMode.scrub&&e._onTouchMove(t),e._mouseMode=timelineMouseMode.touchOnly,1!==i?e._touchMode=i>0?timelineTouchMode.ignore:timelineTouchMode.none:e._touchMode===timelineTouchMode.slideZoom&&(e._touchState.centerX=t.touches[0].clientX-n),e._scrubElement&&(e._scrubElement.style.backgroundPosition="0 0")}}function createTouchMoveCallback(e){return function(t){var i,n,o,a,s,l,c=1,r=e._topDiv.getBoundingClientRect().left;e._touchMode===timelineTouchMode.singleTap&&(e._touchMode=timelineTouchMode.slideZoom),e._mouseMode=timelineMouseMode.touchOnly,e._touchMode===timelineTouchMode.scrub?(t.preventDefault(),1===t.changedTouches.length&&(n=t.changedTouches[0].clientX-r,n>=0&&n<=e._topDiv.clientWidth&&e._setTimeBarTime(n,n*e._timeBarSecondsSpan/e._topDiv.clientWidth))):e._touchMode===timelineTouchMode.slideZoom&&(o=t.touches.length,2===o?(a=.5*(t.touches[0].clientX+t.touches[1].clientX)-r,s=Math.abs(t.touches[0].clientX-t.touches[1].clientX)):1===o&&(a=t.touches[0].clientX-r,s=0),defined(a)&&(s>0&&e._touchState.spanX>0?(c=e._touchState.spanX/s,l=JulianDate.addSeconds(e._startJulian,(e._touchState.centerX*e._timeBarSecondsSpan-a*e._timeBarSecondsSpan*c)/e._topDiv.clientWidth,new JulianDate)):(i=e._touchState.centerX-a,l=JulianDate.addSeconds(e._startJulian,i*e._timeBarSecondsSpan/e._topDiv.clientWidth,new JulianDate)),e.zoomTo(l,JulianDate.addSeconds(l,e._timeBarSecondsSpan*c,new JulianDate)),e._touchState.centerX=a,e._touchState.spanX=s))}}Timeline.prototype.addEventListener=function(e,t,i){this._topDiv.addEventListener(e,t,i)},Timeline.prototype.removeEventListener=function(e,t,i){this._topDiv.removeEventListener(e,t,i)},Timeline.prototype.isDestroyed=function(){return!1},Timeline.prototype.destroy=function(){this._clock.onTick.removeEventListener(this.updateFromClock,this);var e=this.container.ownerDocument;e.removeEventListener("mouseup",this._onMouseUp,!1),e.removeEventListener("mousemove",this._onMouseMove,!1);var t=this._timeBarEle;t.removeEventListener("mousedown",this._onMouseDown,!1),t.removeEventListener("DOMMouseScroll",this._onMouseWheel,!1),t.removeEventListener("mousewheel",this._onMouseWheel,!1),t.removeEventListener("touchstart",this._onTouchStart,!1),t.removeEventListener("touchmove",this._onTouchMove,!1),t.removeEventListener("touchend",this._onTouchEnd,!1),t.removeEventListener("touchcancel",this._onTouchEnd,!1),this.container.removeChild(this._topDiv),destroyObject(this)},Timeline.prototype.addHighlightRange=function(e,t,i){var n=new TimelineHighlightRange(e,t,i);return this._highlightRanges.push(n),this.resize(),n},Timeline.prototype.addTrack=function(e,t,i,n){var o=new TimelineTrack(e,t,i,n);return this._trackList.push(o),this._lastHeight=void 0,this.resize(),o},Timeline.prototype.zoomTo=function(e,t){if(!defined(e))throw new DeveloperError("startTime is required.");if(!defined(t))throw new DeveloperError("stopTime is required");if(JulianDate.lessThanOrEquals(t,e))throw new DeveloperError("Start time must come before end time.");if(this._startJulian=e,this._endJulian=t,this._timeBarSecondsSpan=JulianDate.secondsDifference(t,e),this._clock&&this._clock.clockRange!==ClockRange.UNBOUNDED){var i=this._clock.startTime,n=this._clock.stopTime,o=JulianDate.secondsDifference(n,i),a=JulianDate.secondsDifference(i,this._startJulian),s=JulianDate.secondsDifference(n,this._endJulian);this._timeBarSecondsSpan>=o?(this._timeBarSecondsSpan=o,this._startJulian=this._clock.startTime,this._endJulian=this._clock.stopTime):a>0?(this._endJulian=JulianDate.addSeconds(this._endJulian,a,new JulianDate),this._startJulian=i,this._timeBarSecondsSpan=JulianDate.secondsDifference(this._endJulian,this._startJulian)):s<0&&(this._startJulian=JulianDate.addSeconds(this._startJulian,s,new JulianDate),this._endJulian=n,this._timeBarSecondsSpan=JulianDate.secondsDifference(this._endJulian,this._startJulian))}this._makeTics();var l=document.createEvent("Event");l.initEvent("setzoom",!0,!0),l.startJulian=this._startJulian,l.endJulian=this._endJulian,l.epochJulian=this._epochJulian,l.totalSpan=this._timeBarSecondsSpan,l.mainTicSpan=this._mainTicSpan,this._topDiv.dispatchEvent(l)},Timeline.prototype.zoomFrom=function(e){var t=JulianDate.secondsDifference(this._scrubJulian,this._startJulian);e>1||t<0||t>this._timeBarSecondsSpan?t=.5*this._timeBarSecondsSpan:t+=t-.5*this._timeBarSecondsSpan;var i=this._timeBarSecondsSpan-t;this.zoomTo(JulianDate.addSeconds(this._startJulian,t-t*e,new JulianDate),JulianDate.addSeconds(this._endJulian,i*e-i,new JulianDate))},Timeline.prototype.makeLabel=function(e){var t=JulianDate.toGregorianDate(e),i=t.millisecond,n=" UTC";if(i>0&&this._timeBarSecondsSpan<3600){n=Math.floor(i).toString();while(n.length<3)n="0"+n;n="."+n}return timelineMonthNames[t.month-1]+" "+t.day+" "+t.year+" "+twoDigits(t.hour)+":"+twoDigits(t.minute)+":"+twoDigits(t.second)+n},Timeline.prototype.smallestTicInPixels=7,Timeline.prototype._makeTics=function(){var e,t=this._timeBarEle,i=JulianDate.secondsDifference(this._scrubJulian,this._startJulian),n=Math.round(i*this._topDiv.clientWidth/this._timeBarSecondsSpan),o=n-8,a=this;this._needleEle.style.left=n.toString()+"px";var s="",l=.01,c=31536e6,r=1e-10,h=0,u=this._timeBarSecondsSpan;u<l?(u=l,this._timeBarSecondsSpan=l,this._endJulian=JulianDate.addSeconds(this._startJulian,l,new JulianDate)):u>c&&(u=c,this._timeBarSecondsSpan=c,this._endJulian=JulianDate.addSeconds(this._startJulian,c,new JulianDate));var d=this._timeBarEle.clientWidth;d<10&&(d=10);var m,_=this._startJulian,p=Math.min(u/d*1e-5,.4),v=JulianDate.toGregorianDate(_);m=u>31536e4?JulianDate.fromDate(new Date(Date.UTC(100*Math.floor(v.year/100),0))):u>31536e3?JulianDate.fromDate(new Date(Date.UTC(10*Math.floor(v.year/10),0))):u>86400?JulianDate.fromDate(new Date(Date.UTC(v.year,0))):JulianDate.fromDate(new Date(Date.UTC(v.year,v.month,v.day)));var D=JulianDate.secondsDifference(this._startJulian,JulianDate.addSeconds(m,p,new JulianDate)),M=D+u;function f(e){return Math.floor(D/e)*e}function S(e,t){return Math.ceil(e/t+.5)*t}function T(e){return(e-D)/u}function J(e,t){return e-t*Math.round(e/t)}this._epochJulian=m,this._rulerEle.innerHTML=this.makeLabel(JulianDate.addSeconds(this._endJulian,-l,new JulianDate));var g=this._rulerEle.offsetWidth+20;g<30&&(g=180);var E=h;h-=r;var b={startTime:D,startJulian:_,epochJulian:m,duration:u,timeBarWidth:d,getAlpha:T};this._highlightRanges.forEach((function(e){s+=e.render(b)}));var k=0,w=0,y=0,L=g/d;L>1&&(L=1),L*=this._timeBarSecondsSpan;var B,C=-1,X=-1,W=timelineTicScales.length;for(B=0;B<W;++B){var x=timelineTicScales[B];if(++C,k=x,x>L&&x>h)break;X<0&&d*(x/this._timeBarSecondsSpan)>=this.smallestTicInPixels&&(X=C)}if(C>0){while(C>0)if(--C,Math.abs(J(k,timelineTicScales[C]))<1e-5){timelineTicScales[C]>=h&&(w=timelineTicScales[C]);break}if(X>=0)while(X<C){if(Math.abs(J(w,timelineTicScales[X]))<1e-5&&timelineTicScales[X]>=h){y=timelineTicScales[X];break}++X}}h=E,h>r&&y<1e-5&&Math.abs(h-k)>r&&(y=h,h<=k+r&&(w=0));var z,R=-999999;if(d*(y/this._timeBarSecondsSpan)>=3)for(e=f(y);e<=M;e=S(e,y))s+='<span class="cesium-timeline-ticTiny" style="left: '+Math.round(d*T(e)).toString()+'px;"></span>';if(d*(w/this._timeBarSecondsSpan)>=3)for(e=f(w);e<=M;e=S(e,w))s+='<span class="cesium-timeline-ticSub" style="left: '+Math.round(d*T(e)).toString()+'px;"></span>';if(d*(k/this._timeBarSecondsSpan)>=2){this._mainTicSpan=k,M+=k,e=f(k);var U=JulianDate.computeTaiMinusUtc(m);while(e<=M){var H=JulianDate.addSeconds(_,e-D,new JulianDate);if(k>2.1){var O=JulianDate.computeTaiMinusUtc(H);Math.abs(O-U)>.1&&(e+=O-U,H=JulianDate.addSeconds(_,e-D,new JulianDate))}var N=Math.round(d*T(e)),j=this.makeLabel(H);this._rulerEle.innerHTML=j,z=this._rulerEle.offsetWidth,z<10&&(z=g);var P=N-(z/2-1);P>R?(R=P+z+5,s+='<span class="cesium-timeline-ticMain" style="left: '+N.toString()+'px;"></span><span class="cesium-timeline-ticLabel" style="left: '+P.toString()+'px;">'+j+"</span>"):s+='<span class="cesium-timeline-ticSub" style="left: '+N.toString()+'px;"></span>',e=S(e,k)}}else this._mainTicSpan=-1;s+='<span class="cesium-timeline-icon16" style="left:'+o+'px;bottom:0;background-position: 0 0;"></span>',t.innerHTML=s,this._scrubElement=t.lastChild,this._context.clearRect(0,0,this._trackListEle.width,this._trackListEle.height),b.y=0,this._trackList.forEach((function(e){e.render(a._context,b),b.y+=e.height}))},Timeline.prototype.updateFromClock=function(){this._scrubJulian=this._clock.currentTime;var e=this._scrubElement;if(defined(this._scrubElement)){var t=JulianDate.secondsDifference(this._scrubJulian,this._startJulian),i=Math.round(t*this._topDiv.clientWidth/this._timeBarSecondsSpan);this._lastXPos!==i&&(this._lastXPos=i,e.style.left=i-8+"px",this._needleEle.style.left=i+"px")}defined(this._timelineDragLocation)&&(this._setTimeBarTime(this._timelineDragLocation,this._timelineDragLocation*this._timeBarSecondsSpan/this._topDiv.clientWidth),this.zoomTo(JulianDate.addSeconds(this._startJulian,this._timelineDrag,new JulianDate),JulianDate.addSeconds(this._endJulian,this._timelineDrag,new JulianDate)))},Timeline.prototype._setTimeBarTime=function(e,t){if(e=Math.round(e),this._scrubJulian=JulianDate.addSeconds(this._startJulian,t,new JulianDate),this._scrubElement){var i=e-8;this._scrubElement.style.left=i.toString()+"px",this._needleEle.style.left=e.toString()+"px"}var n=document.createEvent("Event");n.initEvent("settime",!0,!0),n.clientX=e,n.timeSeconds=t,n.timeJulian=this._scrubJulian,n.clock=this._clock,this._topDiv.dispatchEvent(n)},Timeline.prototype.resize=function(){var e=this.container.clientWidth,t=this.container.clientHeight;if(e!==this._lastWidth||t!==this._lastHeight){this._trackContainer.style.height=t+"px";var i=1;this._trackList.forEach((function(e){i+=e.height})),this._trackListEle.style.height=i.toString()+"px",this._trackListEle.width=this._trackListEle.clientWidth,this._trackListEle.height=i,this._makeTics(),this._lastXPos=void 0,this._lastWidth=e,this._lastHeight=t}};export default Timeline;