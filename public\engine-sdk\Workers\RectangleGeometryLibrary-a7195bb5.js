define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./GeometryAttribute-2104918f"],(function(t,n,a,r,e,o,s){"use strict";var i=Math.cos,h=Math.sin,g=Math.sqrt,u={computePosition:function(t,a,r,e,o,s,u){var c=a.radiiSquared,C=t.nwCorner,l=t.boundingRectangle,d=C.latitude-t.granYCos*e+o*t.granXSin,S=i(d),w=h(d),M=c.z*w,m=C.longitude+e*t.granYSin+o*t.granXCos,f=S*i(m),R=S*h(m),X=c.x*f,Y=c.y*R,p=g(X*f+Y*R+M*w);if(s.x=X/p,s.y=Y/p,s.z=M/p,r){var v=t.stNwCorner;n.defined(v)?(d=v.latitude-t.stGranYCos*e+o*t.stGranXSin,m=v.longitude+e*t.stGranYSin+o*t.stGranXCos,u.x=(m-t.stWest)*t.lonScalar,u.y=(d-t.stSouth)*t.latScalar):(u.x=(m-l.west)*t.lonScalar,u.y=(d-l.south)*t.latScalar)}}},c=new s.Matrix2,C=new r.Cartesian3,l=new e.Cartographic,d=new r.Cartesian3,S=new o.GeographicProjection;function w(t,n,a,e,o,i,h){var g=Math.cos(n),u=e*g,l=a*g,w=Math.sin(n),M=e*w,m=a*w;C=S.project(t,C),C=r.Cartesian3.subtract(C,d,C);var f=s.Matrix2.fromRotation(n,c);C=s.Matrix2.multiplyByVector(f,C,C),C=r.Cartesian3.add(C,d,C),t=S.unproject(C,t),i-=1,h-=1;var R=t.latitude,X=R+i*m,Y=R-u*h,p=R-u*h+i*m,v=Math.max(R,X,Y,p),O=Math.min(R,X,Y,p),_=t.longitude,G=_+i*l,x=_+h*M,P=_+h*M+i*l,W=Math.max(_,G,x,P),y=Math.min(_,G,x,P);return{north:v,south:O,east:W,west:y,granYCos:u,granYSin:M,granXCos:l,granXSin:m,nwCorner:t}}u.computeOptions=function(t,n,o,s,i,h,g){var u,c,C,M,m,f=t.east,R=t.west,X=t.north,Y=t.south,p=!1,v=!1;X===r.CesiumMath.PI_OVER_TWO&&(p=!0),Y===-r.CesiumMath.PI_OVER_TWO&&(v=!0);var O=X-Y;m=R>f?r.CesiumMath.TWO_PI-R+f:f-R,u=Math.ceil(m/n)+1,c=Math.ceil(O/n)+1,C=m/(u-1),M=O/(c-1);var _=e.Rectangle.northwest(t,h),G=e.Rectangle.center(t,l);0===o&&0===s||(G.longitude<_.longitude&&(G.longitude+=r.CesiumMath.TWO_PI),d=S.project(G,d));var x=M,P=C,W=0,y=0,I=e.Rectangle.clone(t,i),b={granYCos:x,granYSin:W,granXCos:P,granXSin:y,nwCorner:_,boundingRectangle:I,width:u,height:c,northCap:p,southCap:v};if(0!==o){var T=w(_,o,C,M,G,u,c);if(X=T.north,Y=T.south,f=T.east,R=T.west,X<-r.CesiumMath.PI_OVER_TWO||X>r.CesiumMath.PI_OVER_TWO||Y<-r.CesiumMath.PI_OVER_TWO||Y>r.CesiumMath.PI_OVER_TWO)throw new a.DeveloperError("Rotated rectangle is invalid.  It crosses over either the north or south pole.");b.granYCos=T.granYCos,b.granYSin=T.granYSin,b.granXCos=T.granXCos,b.granXSin=T.granXSin,I.north=X,I.south=Y,I.east=f,I.west=R}if(0!==s){o-=s;var E=e.Rectangle.northwest(I,g),V=w(E,o,C,M,G,u,c);b.stGranYCos=V.granYCos,b.stGranXCos=V.granXCos,b.stGranYSin=V.granYSin,b.stGranXSin=V.granXSin,b.stNwCorner=E,b.stWest=V.west,b.stSouth=V.south}return b},t.RectangleGeometryLibrary=u}));