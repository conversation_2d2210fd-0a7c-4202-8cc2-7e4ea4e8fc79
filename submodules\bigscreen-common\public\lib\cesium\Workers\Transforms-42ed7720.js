/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Math-b5f4d889","./Resource-41d99fe7","./RuntimeError-592f0d41"],(function(e,t,n,r,a,i,s){"use strict";function o(e){this._ellipsoid=r.defaultValue(e,n.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(o.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),o.prototype.project=function(e,n){const a=this._semimajorAxis,i=e.longitude*a,s=e.latitude*a,o=e.height;return r.defined(n)?(n.x=i,n.y=s,n.z=o,n):new t.Cartesian3(i,s,o)},o.prototype.unproject=function(e,t){const a=this._oneOverSemimajorAxis,i=e.x*a,s=e.y*a,o=e.z;return r.defined(t)?(t.longitude=i,t.latitude=s,t.height=o,t):new n.Cartographic(i,s,o)};var u=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function l(e,t){this.start=r.defaultValue(e,0),this.stop=r.defaultValue(t,0)}function c(e,n){this.center=t.Cartesian3.clone(r.defaultValue(e,t.Cartesian3.ZERO)),this.radius=r.defaultValue(n,0)}const d=new t.Cartesian3,f=new t.Cartesian3,m=new t.Cartesian3,p=new t.Cartesian3,C=new t.Cartesian3,y=new t.Cartesian3,h=new t.Cartesian3,w=new t.Cartesian3,x=new t.Cartesian3,g=new t.Cartesian3,S=new t.Cartesian3,E=new t.Cartesian3,_=4/3*a.CesiumMath.PI;c.fromPoints=function(e,n){if(r.defined(n)||(n=new c),!r.defined(e)||0===e.length)return n.center=t.Cartesian3.clone(t.Cartesian3.ZERO,n.center),n.radius=0,n;const a=t.Cartesian3.clone(e[0],h),i=t.Cartesian3.clone(a,d),s=t.Cartesian3.clone(a,f),o=t.Cartesian3.clone(a,m),u=t.Cartesian3.clone(a,p),l=t.Cartesian3.clone(a,C),_=t.Cartesian3.clone(a,y),M=e.length;let O;for(O=1;O<M;O++){t.Cartesian3.clone(e[O],a);const n=a.x,r=a.y,c=a.z;n<i.x&&t.Cartesian3.clone(a,i),n>u.x&&t.Cartesian3.clone(a,u),r<s.y&&t.Cartesian3.clone(a,s),r>l.y&&t.Cartesian3.clone(a,l),c<o.z&&t.Cartesian3.clone(a,o),c>_.z&&t.Cartesian3.clone(a,_)}const D=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(u,i,w)),P=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(l,s,w)),T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(_,o,w));let A=i,R=u,I=D;P>I&&(I=P,A=s,R=l),T>I&&(I=T,A=o,R=_);const z=x;z.x=.5*(A.x+R.x),z.y=.5*(A.y+R.y),z.z=.5*(A.z+R.z);let N=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(R,z,w)),U=Math.sqrt(N);const b=g;b.x=i.x,b.y=s.y,b.z=o.z;const F=S;F.x=u.x,F.y=l.y,F.z=_.z;const q=t.Cartesian3.midpoint(b,F,E);let W=0;for(O=0;O<M;O++){t.Cartesian3.clone(e[O],a);const n=t.Cartesian3.magnitude(t.Cartesian3.subtract(a,q,w));n>W&&(W=n);const r=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(a,z,w));if(r>N){const e=Math.sqrt(r);U=.5*(U+e),N=U*U;const t=e-U;z.x=(U*z.x+t*a.x)/e,z.y=(U*z.y+t*a.y)/e,z.z=(U*z.z+t*a.z)/e}}return U<W?(t.Cartesian3.clone(z,n.center),n.radius=U):(t.Cartesian3.clone(q,n.center),n.radius=W),n};const M=new o,O=new t.Cartesian3,D=new t.Cartesian3,P=new n.Cartographic,T=new n.Cartographic;c.fromRectangle2D=function(e,t,n){return c.fromRectangleWithHeights2D(e,t,0,0,n)},c.fromRectangleWithHeights2D=function(e,a,i,s,o){if(r.defined(o)||(o=new c),!r.defined(e))return o.center=t.Cartesian3.clone(t.Cartesian3.ZERO,o.center),o.radius=0,o;a=r.defaultValue(a,M),n.Rectangle.southwest(e,P),P.height=i,n.Rectangle.northeast(e,T),T.height=s;const u=a.project(P,O),l=a.project(T,D),d=l.x-u.x,f=l.y-u.y,m=l.z-u.z;o.radius=.5*Math.sqrt(d*d+f*f+m*m);const p=o.center;return p.x=u.x+.5*d,p.y=u.y+.5*f,p.z=u.z+.5*m,o};const A=[];c.fromRectangle3D=function(e,a,i,s){if(a=r.defaultValue(a,n.Ellipsoid.WGS84),i=r.defaultValue(i,0),r.defined(s)||(s=new c),!r.defined(e))return s.center=t.Cartesian3.clone(t.Cartesian3.ZERO,s.center),s.radius=0,s;const o=n.Rectangle.subsample(e,a,i,A);return c.fromPoints(o,s)},c.fromVertices=function(e,n,a,i){if(r.defined(i)||(i=new c),!r.defined(e)||0===e.length)return i.center=t.Cartesian3.clone(t.Cartesian3.ZERO,i.center),i.radius=0,i;n=r.defaultValue(n,t.Cartesian3.ZERO),a=r.defaultValue(a,3);const s=h;s.x=e[0]+n.x,s.y=e[1]+n.y,s.z=e[2]+n.z;const o=t.Cartesian3.clone(s,d),u=t.Cartesian3.clone(s,f),l=t.Cartesian3.clone(s,m),_=t.Cartesian3.clone(s,p),M=t.Cartesian3.clone(s,C),O=t.Cartesian3.clone(s,y),D=e.length;let P;for(P=0;P<D;P+=a){const r=e[P]+n.x,a=e[P+1]+n.y,i=e[P+2]+n.z;s.x=r,s.y=a,s.z=i,r<o.x&&t.Cartesian3.clone(s,o),r>_.x&&t.Cartesian3.clone(s,_),a<u.y&&t.Cartesian3.clone(s,u),a>M.y&&t.Cartesian3.clone(s,M),i<l.z&&t.Cartesian3.clone(s,l),i>O.z&&t.Cartesian3.clone(s,O)}const T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(_,o,w)),A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(M,u,w)),R=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(O,l,w));let I=o,z=_,N=T;A>N&&(N=A,I=u,z=M),R>N&&(N=R,I=l,z=O);const U=x;U.x=.5*(I.x+z.x),U.y=.5*(I.y+z.y),U.z=.5*(I.z+z.z);let b=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(z,U,w)),F=Math.sqrt(b);const q=g;q.x=o.x,q.y=u.y,q.z=l.z;const W=S;W.x=_.x,W.y=M.y,W.z=O.z;const v=t.Cartesian3.midpoint(q,W,E);let V=0;for(P=0;P<D;P+=a){s.x=e[P]+n.x,s.y=e[P+1]+n.y,s.z=e[P+2]+n.z;const r=t.Cartesian3.magnitude(t.Cartesian3.subtract(s,v,w));r>V&&(V=r);const a=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(s,U,w));if(a>b){const e=Math.sqrt(a);F=.5*(F+e),b=F*F;const t=e-F;U.x=(F*U.x+t*s.x)/e,U.y=(F*U.y+t*s.y)/e,U.z=(F*U.z+t*s.z)/e}}return F<V?(t.Cartesian3.clone(U,i.center),i.radius=F):(t.Cartesian3.clone(v,i.center),i.radius=V),i},c.fromEncodedCartesianVertices=function(e,n,a){if(r.defined(a)||(a=new c),!r.defined(e)||!r.defined(n)||e.length!==n.length||0===e.length)return a.center=t.Cartesian3.clone(t.Cartesian3.ZERO,a.center),a.radius=0,a;const i=h;i.x=e[0]+n[0],i.y=e[1]+n[1],i.z=e[2]+n[2];const s=t.Cartesian3.clone(i,d),o=t.Cartesian3.clone(i,f),u=t.Cartesian3.clone(i,m),l=t.Cartesian3.clone(i,p),_=t.Cartesian3.clone(i,C),M=t.Cartesian3.clone(i,y),O=e.length;let D;for(D=0;D<O;D+=3){const r=e[D]+n[D],a=e[D+1]+n[D+1],c=e[D+2]+n[D+2];i.x=r,i.y=a,i.z=c,r<s.x&&t.Cartesian3.clone(i,s),r>l.x&&t.Cartesian3.clone(i,l),a<o.y&&t.Cartesian3.clone(i,o),a>_.y&&t.Cartesian3.clone(i,_),c<u.z&&t.Cartesian3.clone(i,u),c>M.z&&t.Cartesian3.clone(i,M)}const P=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(l,s,w)),T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(_,o,w)),A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(M,u,w));let R=s,I=l,z=P;T>z&&(z=T,R=o,I=_),A>z&&(z=A,R=u,I=M);const N=x;N.x=.5*(R.x+I.x),N.y=.5*(R.y+I.y),N.z=.5*(R.z+I.z);let U=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(I,N,w)),b=Math.sqrt(U);const F=g;F.x=s.x,F.y=o.y,F.z=u.z;const q=S;q.x=l.x,q.y=_.y,q.z=M.z;const W=t.Cartesian3.midpoint(F,q,E);let v=0;for(D=0;D<O;D+=3){i.x=e[D]+n[D],i.y=e[D+1]+n[D+1],i.z=e[D+2]+n[D+2];const r=t.Cartesian3.magnitude(t.Cartesian3.subtract(i,W,w));r>v&&(v=r);const a=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(i,N,w));if(a>U){const e=Math.sqrt(a);b=.5*(b+e),U=b*b;const t=e-b;N.x=(b*N.x+t*i.x)/e,N.y=(b*N.y+t*i.y)/e,N.z=(b*N.z+t*i.z)/e}}return b<v?(t.Cartesian3.clone(N,a.center),a.radius=b):(t.Cartesian3.clone(W,a.center),a.radius=v),a},c.fromCornerPoints=function(e,n,a){r.defined(a)||(a=new c);const i=t.Cartesian3.midpoint(e,n,a.center);return a.radius=t.Cartesian3.distance(i,n),a},c.fromEllipsoid=function(e,n){return r.defined(n)||(n=new c),t.Cartesian3.clone(t.Cartesian3.ZERO,n.center),n.radius=e.maximumRadius,n};const R=new t.Cartesian3;c.fromBoundingSpheres=function(e,n){if(r.defined(n)||(n=new c),!r.defined(e)||0===e.length)return n.center=t.Cartesian3.clone(t.Cartesian3.ZERO,n.center),n.radius=0,n;const a=e.length;if(1===a)return c.clone(e[0],n);if(2===a)return c.union(e[0],e[1],n);const i=[];let s;for(s=0;s<a;s++)i.push(e[s].center);const o=(n=c.fromPoints(i,n)).center;let u=n.radius;for(s=0;s<a;s++){const n=e[s];u=Math.max(u,t.Cartesian3.distance(o,n.center,R)+n.radius)}return n.radius=u,n};const I=new t.Cartesian3,z=new t.Cartesian3,N=new t.Cartesian3;c.fromOrientedBoundingBox=function(e,a){r.defined(a)||(a=new c);const i=e.halfAxes,s=n.Matrix3.getColumn(i,0,I),o=n.Matrix3.getColumn(i,1,z),u=n.Matrix3.getColumn(i,2,N);return t.Cartesian3.add(s,o,s),t.Cartesian3.add(s,u,s),a.center=t.Cartesian3.clone(e.center,a.center),a.radius=t.Cartesian3.magnitude(s),a};const U=new t.Cartesian3,b=new t.Cartesian3;c.fromTransformation=function(e,a){r.defined(a)||(a=new c);const i=n.Matrix4.getTranslation(e,U),s=n.Matrix4.getScale(e,b),o=.5*t.Cartesian3.magnitude(s);return a.center=t.Cartesian3.clone(i,a.center),a.radius=o,a},c.clone=function(e,n){if(r.defined(e))return r.defined(n)?(n.center=t.Cartesian3.clone(e.center,n.center),n.radius=e.radius,n):new c(e.center,e.radius)},c.packedLength=4,c.pack=function(e,t,n){n=r.defaultValue(n,0);const a=e.center;return t[n++]=a.x,t[n++]=a.y,t[n++]=a.z,t[n]=e.radius,t},c.unpack=function(e,t,n){t=r.defaultValue(t,0),r.defined(n)||(n=new c);const a=n.center;return a.x=e[t++],a.y=e[t++],a.z=e[t++],n.radius=e[t],n};const F=new t.Cartesian3,q=new t.Cartesian3;c.union=function(e,n,a){r.defined(a)||(a=new c);const i=e.center,s=e.radius,o=n.center,u=n.radius,l=t.Cartesian3.subtract(o,i,F),d=t.Cartesian3.magnitude(l);if(s>=d+u)return e.clone(a),a;if(u>=d+s)return n.clone(a),a;const f=.5*(s+d+u),m=t.Cartesian3.multiplyByScalar(l,(-s+f)/d,q);return t.Cartesian3.add(m,i,m),t.Cartesian3.clone(m,a.center),a.radius=f,a};const W=new t.Cartesian3;c.expand=function(e,n,r){r=c.clone(e,r);const a=t.Cartesian3.magnitude(t.Cartesian3.subtract(n,r.center,W));return a>r.radius&&(r.radius=a),r},c.intersectPlane=function(e,n){const r=e.center,a=e.radius,i=n.normal,s=t.Cartesian3.dot(i,r)+n.distance;return s<-a?u.OUTSIDE:s<a?u.INTERSECTING:u.INSIDE},c.transform=function(e,t,a){return r.defined(a)||(a=new c),a.center=n.Matrix4.multiplyByPoint(t,e.center,a.center),a.radius=n.Matrix4.getMaximumScale(t)*e.radius,a};const v=new t.Cartesian3;c.distanceSquaredTo=function(e,n){const r=t.Cartesian3.subtract(e.center,n,v),a=t.Cartesian3.magnitude(r)-e.radius;return a<=0?0:a*a},c.transformWithoutScale=function(e,t,a){return r.defined(a)||(a=new c),a.center=n.Matrix4.multiplyByPoint(t,e.center,a.center),a.radius=e.radius,a};const V=new t.Cartesian3;c.computePlaneDistances=function(e,n,a,i){r.defined(i)||(i=new l);const s=t.Cartesian3.subtract(e.center,n,V),o=t.Cartesian3.dot(a,s);return i.start=o-e.radius,i.stop=o+e.radius,i};const B=new t.Cartesian3,$=new t.Cartesian3,L=new t.Cartesian3,j=new t.Cartesian3,k=new t.Cartesian3,Z=new n.Cartographic,Y=new Array(8);for(let e=0;e<8;++e)Y[e]=new t.Cartesian3;const G=new o;let X;c.projectTo2D=function(e,n,a){const i=(n=r.defaultValue(n,G)).ellipsoid;let s=e.center;const o=e.radius;let u;u=t.Cartesian3.equals(s,t.Cartesian3.ZERO)?t.Cartesian3.clone(t.Cartesian3.UNIT_X,B):i.geodeticSurfaceNormal(s,B);const l=t.Cartesian3.cross(t.Cartesian3.UNIT_Z,u,$);t.Cartesian3.normalize(l,l);const d=t.Cartesian3.cross(u,l,L);t.Cartesian3.normalize(d,d),t.Cartesian3.multiplyByScalar(u,o,u),t.Cartesian3.multiplyByScalar(d,o,d),t.Cartesian3.multiplyByScalar(l,o,l);const f=t.Cartesian3.negate(d,k),m=t.Cartesian3.negate(l,j),p=Y;let C=p[0];t.Cartesian3.add(u,d,C),t.Cartesian3.add(C,l,C),C=p[1],t.Cartesian3.add(u,d,C),t.Cartesian3.add(C,m,C),C=p[2],t.Cartesian3.add(u,f,C),t.Cartesian3.add(C,m,C),C=p[3],t.Cartesian3.add(u,f,C),t.Cartesian3.add(C,l,C),t.Cartesian3.negate(u,u),C=p[4],t.Cartesian3.add(u,d,C),t.Cartesian3.add(C,l,C),C=p[5],t.Cartesian3.add(u,d,C),t.Cartesian3.add(C,m,C),C=p[6],t.Cartesian3.add(u,f,C),t.Cartesian3.add(C,m,C),C=p[7],t.Cartesian3.add(u,f,C),t.Cartesian3.add(C,l,C);const y=p.length;for(let e=0;e<y;++e){const r=p[e];t.Cartesian3.add(s,r,r);const a=i.cartesianToCartographic(r,Z);n.project(a,r)}s=(a=c.fromPoints(p,a)).center;const h=s.x,w=s.y,x=s.z;return s.x=x,s.y=h,s.z=w,a},c.isOccluded=function(e,t){return!t.isBoundingSphereVisible(e)},c.equals=function(e,n){return e===n||r.defined(e)&&r.defined(n)&&t.Cartesian3.equals(e.center,n.center)&&e.radius===n.radius},c.prototype.intersectPlane=function(e){return c.intersectPlane(this,e)},c.prototype.distanceSquaredTo=function(e){return c.distanceSquaredTo(this,e)},c.prototype.computePlaneDistances=function(e,t,n){return c.computePlaneDistances(this,e,t,n)},c.prototype.isOccluded=function(e){return c.isOccluded(this,e)},c.prototype.equals=function(e){return c.equals(this,e)},c.prototype.clone=function(e){return c.clone(this,e)},c.prototype.volume=function(){const e=this.radius;return _*e*e*e};const J={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},H={};Object.defineProperties(H,{element:{get:function(){if(H.supportsFullscreen())return document[J.fullscreenElement]}},changeEventName:{get:function(){if(H.supportsFullscreen())return J.fullscreenchange}},errorEventName:{get:function(){if(H.supportsFullscreen())return J.fullscreenerror}},enabled:{get:function(){if(H.supportsFullscreen())return document[J.fullscreenEnabled]}},fullscreen:{get:function(){if(H.supportsFullscreen())return null!==H.element}}}),H.supportsFullscreen=function(){if(r.defined(X))return X;X=!1;const e=document.body;if("function"==typeof e.requestFullscreen)return J.requestFullscreen="requestFullscreen",J.exitFullscreen="exitFullscreen",J.fullscreenEnabled="fullscreenEnabled",J.fullscreenElement="fullscreenElement",J.fullscreenchange="fullscreenchange",J.fullscreenerror="fullscreenerror",X=!0,X;const t=["webkit","moz","o","ms","khtml"];let n;for(let r=0,a=t.length;r<a;++r){const a=t[r];n=`${a}RequestFullscreen`,"function"==typeof e[n]?(J.requestFullscreen=n,X=!0):(n=`${a}RequestFullScreen`,"function"==typeof e[n]&&(J.requestFullscreen=n,X=!0)),n=`${a}ExitFullscreen`,"function"==typeof document[n]?J.exitFullscreen=n:(n=`${a}CancelFullScreen`,"function"==typeof document[n]&&(J.exitFullscreen=n)),n=`${a}FullscreenEnabled`,void 0!==document[n]?J.fullscreenEnabled=n:(n=`${a}FullScreenEnabled`,void 0!==document[n]&&(J.fullscreenEnabled=n)),n=`${a}FullscreenElement`,void 0!==document[n]?J.fullscreenElement=n:(n=`${a}FullScreenElement`,void 0!==document[n]&&(J.fullscreenElement=n)),n=`${a}fullscreenchange`,void 0!==document[`on${n}`]&&("ms"===a&&(n="MSFullscreenChange"),J.fullscreenchange=n),n=`${a}fullscreenerror`,void 0!==document[`on${n}`]&&("ms"===a&&(n="MSFullscreenError"),J.fullscreenerror=n)}return X},H.requestFullscreen=function(e,t){H.supportsFullscreen()&&e[J.requestFullscreen]({vrDisplay:t})},H.exitFullscreen=function(){H.supportsFullscreen()&&document[J.exitFullscreen]()},H._names=J;var Q=H;let K,ee,te,ne,re,ae,ie,se,oe,ue,le,ce,de,fe,me,pe,Ce,ye;function he(e){const t=e.split(".");for(let e=0,n=t.length;e<n;++e)t[e]=parseInt(t[e],10);return t}function we(){if(!r.defined(ee)&&(ee=!1,!Ee())){const e=/ Chrome\/([\.0-9]+)/.exec(K.userAgent);null!==e&&(ee=!0,te=he(e[1]))}return ee}function xe(){if(!r.defined(ne)&&(ne=!1,!we()&&!Ee()&&/ Safari\/[\.0-9]+/.test(K.userAgent))){const e=/ Version\/([\.0-9]+)/.exec(K.userAgent);null!==e&&(ne=!0,re=he(e[1]))}return ne}function ge(){if(!r.defined(ae)){ae=!1;const e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(K.userAgent);null!==e&&(ae=!0,ie=he(e[1]),ie.isNightly=!!e[2])}return ae}function Se(){if(!r.defined(se)){let e;se=!1,"Microsoft Internet Explorer"===K.appName?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(K.userAgent),null!==e&&(se=!0,oe=he(e[1]))):"Netscape"===K.appName&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(K.userAgent),null!==e&&(se=!0,oe=he(e[1])))}return se}function Ee(){if(!r.defined(ue)){ue=!1;const e=/ Edg\/([\.0-9]+)/.exec(K.userAgent);null!==e&&(ue=!0,le=he(e[1]))}return ue}function _e(){if(!r.defined(ce)){ce=!1;const e=/Firefox\/([\.0-9]+)/.exec(K.userAgent);null!==e&&(ce=!0,de=he(e[1]))}return ce}function Me(){if(!r.defined(ye)){const e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");const t=e.style.imageRendering;ye=r.defined(t)&&""!==t,ye&&(Ce=t)}return ye}function Oe(){return Oe._result}K="undefined"!=typeof navigator?navigator:{},Oe._promise=void 0,Oe._result=void 0,Oe.initialize=function(){return r.defined(Oe._promise)||(Oe._promise=new Promise((e=>{const t=new Image;t.onload=function(){Oe._result=t.width>0&&t.height>0,e(Oe._result)},t.onerror=function(){Oe._result=!1,e(Oe._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"}))),Oe._promise},Object.defineProperties(Oe,{initialized:{get:function(){return r.defined(Oe._result)}}});const De=[];"undefined"!=typeof ArrayBuffer&&(De.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&De.push(Uint8ClampedArray),"undefined"!=typeof Uint8ClampedArray&&De.push(Uint8ClampedArray),"undefined"!=typeof BigInt64Array&&De.push(BigInt64Array),"undefined"!=typeof BigUint64Array&&De.push(BigUint64Array));const Pe={isChrome:we,chromeVersion:function(){return we()&&te},isSafari:xe,safariVersion:function(){return xe()&&re},isWebkit:ge,webkitVersion:function(){return ge()&&ie},isInternetExplorer:Se,internetExplorerVersion:function(){return Se()&&oe},isEdge:Ee,edgeVersion:function(){return Ee()&&le},isFirefox:_e,firefoxVersion:function(){return _e()&&de},isWindows:function(){return r.defined(fe)||(fe=/Windows/i.test(K.appVersion)),fe},isIPadOrIOS:function(){return r.defined(me)||(me="iPhone"===navigator.platform||"iPod"===navigator.platform||"iPad"===navigator.platform),me},hardwareConcurrency:r.defaultValue(K.hardwareConcurrency,3),supportsPointerEvents:function(){return r.defined(pe)||(pe=!_e()&&"undefined"!=typeof PointerEvent&&(!r.defined(K.pointerEnabled)||K.pointerEnabled)),pe},supportsImageRenderingPixelated:Me,supportsWebP:Oe,imageRenderingValue:function(){return Me()?Ce:void 0},typedArrayTypes:De,supportsBasis:function(e){return Pe.supportsWebAssembly()&&e.context.supportsBasis},supportsFullscreen:function(){return Q.supportsFullscreen()},supportsTypedArrays:function(){return"undefined"!=typeof ArrayBuffer},supportsBigInt64Array:function(){return"undefined"!=typeof BigInt64Array},supportsBigUint64Array:function(){return"undefined"!=typeof BigUint64Array},supportsBigInt:function(){return"undefined"!=typeof BigInt},supportsWebWorkers:function(){return"undefined"!=typeof Worker},supportsWebAssembly:function(){return"undefined"!=typeof WebAssembly}};var Te=Pe;function Ae(e,t,n,a){this.x=r.defaultValue(e,0),this.y=r.defaultValue(t,0),this.z=r.defaultValue(n,0),this.w=r.defaultValue(a,0)}let Re=new t.Cartesian3;Ae.fromAxisAngle=function(e,n,a){const i=n/2,s=Math.sin(i);Re=t.Cartesian3.normalize(e,Re);const o=Re.x*s,u=Re.y*s,l=Re.z*s,c=Math.cos(i);return r.defined(a)?(a.x=o,a.y=u,a.z=l,a.w=c,a):new Ae(o,u,l,c)};const Ie=[1,2,0],ze=new Array(3);Ae.fromRotationMatrix=function(e,t){let a,i,s,o,u;const l=e[n.Matrix3.COLUMN0ROW0],c=e[n.Matrix3.COLUMN1ROW1],d=e[n.Matrix3.COLUMN2ROW2],f=l+c+d;if(f>0)a=Math.sqrt(f+1),u=.5*a,a=.5/a,i=(e[n.Matrix3.COLUMN1ROW2]-e[n.Matrix3.COLUMN2ROW1])*a,s=(e[n.Matrix3.COLUMN2ROW0]-e[n.Matrix3.COLUMN0ROW2])*a,o=(e[n.Matrix3.COLUMN0ROW1]-e[n.Matrix3.COLUMN1ROW0])*a;else{const t=Ie;let r=0;c>l&&(r=1),d>l&&d>c&&(r=2);const f=t[r],m=t[f];a=Math.sqrt(e[n.Matrix3.getElementIndex(r,r)]-e[n.Matrix3.getElementIndex(f,f)]-e[n.Matrix3.getElementIndex(m,m)]+1);const p=ze;p[r]=.5*a,a=.5/a,u=(e[n.Matrix3.getElementIndex(m,f)]-e[n.Matrix3.getElementIndex(f,m)])*a,p[f]=(e[n.Matrix3.getElementIndex(f,r)]+e[n.Matrix3.getElementIndex(r,f)])*a,p[m]=(e[n.Matrix3.getElementIndex(m,r)]+e[n.Matrix3.getElementIndex(r,m)])*a,i=-p[0],s=-p[1],o=-p[2]}return r.defined(t)?(t.x=i,t.y=s,t.z=o,t.w=u,t):new Ae(i,s,o,u)};const Ne=new Ae;let Ue=new Ae,be=new Ae,Fe=new Ae;Ae.fromHeadingPitchRoll=function(e,n){return Fe=Ae.fromAxisAngle(t.Cartesian3.UNIT_X,e.roll,Ne),be=Ae.fromAxisAngle(t.Cartesian3.UNIT_Y,-e.pitch,n),n=Ae.multiply(be,Fe,be),Ue=Ae.fromAxisAngle(t.Cartesian3.UNIT_Z,-e.heading,Ne),Ae.multiply(Ue,n,n)};const qe=new t.Cartesian3,We=new t.Cartesian3,ve=new Ae,Ve=new Ae,Be=new Ae;Ae.packedLength=4,Ae.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},Ae.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new Ae),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},Ae.packedInterpolationLength=3,Ae.convertPackedArrayForInterpolation=function(e,t,n,a){Ae.unpack(e,4*n,Be),Ae.conjugate(Be,Be);for(let i=0,s=n-t+1;i<s;i++){const n=3*i;Ae.unpack(e,4*(t+i),ve),Ae.multiply(ve,Be,ve),ve.w<0&&Ae.negate(ve,ve),Ae.computeAxis(ve,qe);const s=Ae.computeAngle(ve);r.defined(a)||(a=[]),a[n]=qe.x*s,a[n+1]=qe.y*s,a[n+2]=qe.z*s}},Ae.unpackInterpolationResult=function(e,n,a,i,s){r.defined(s)||(s=new Ae),t.Cartesian3.fromArray(e,0,We);const o=t.Cartesian3.magnitude(We);return Ae.unpack(n,4*i,Ve),0===o?Ae.clone(Ae.IDENTITY,ve):Ae.fromAxisAngle(We,o,ve),Ae.multiply(ve,Ve,s)},Ae.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new Ae(e.x,e.y,e.z,e.w)},Ae.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},Ae.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},Ae.magnitude=function(e){return Math.sqrt(Ae.magnitudeSquared(e))},Ae.normalize=function(e,t){const n=1/Ae.magnitude(e),r=e.x*n,a=e.y*n,i=e.z*n,s=e.w*n;return t.x=r,t.y=a,t.z=i,t.w=s,t},Ae.inverse=function(e,t){const n=Ae.magnitudeSquared(e);return t=Ae.conjugate(e,t),Ae.multiplyByScalar(t,1/n,t)},Ae.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},Ae.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},Ae.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},Ae.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},Ae.multiply=function(e,t,n){const r=e.x,a=e.y,i=e.z,s=e.w,o=t.x,u=t.y,l=t.z,c=t.w,d=s*o+r*c+a*l-i*u,f=s*u-r*l+a*c+i*o,m=s*l+r*u-a*o+i*c,p=s*c-r*o-a*u-i*l;return n.x=d,n.y=f,n.z=m,n.w=p,n},Ae.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},Ae.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},Ae.computeAxis=function(e,t){const n=e.w;if(Math.abs(n-1)<a.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;const r=1/Math.sqrt(1-n*n);return t.x=e.x*r,t.y=e.y*r,t.z=e.z*r,t},Ae.computeAngle=function(e){return Math.abs(e.w-1)<a.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};let $e=new Ae;Ae.lerp=function(e,t,n,r){return $e=Ae.multiplyByScalar(t,n,$e),r=Ae.multiplyByScalar(e,1-n,r),Ae.add($e,r,r)};let Le=new Ae,je=new Ae,ke=new Ae;Ae.slerp=function(e,t,n,r){let i=Ae.dot(e,t),s=t;if(i<0&&(i=-i,s=Le=Ae.negate(t,Le)),1-i<a.CesiumMath.EPSILON6)return Ae.lerp(e,s,n,r);const o=Math.acos(i);return je=Ae.multiplyByScalar(e,Math.sin((1-n)*o),je),ke=Ae.multiplyByScalar(s,Math.sin(n*o),ke),r=Ae.add(je,ke,r),Ae.multiplyByScalar(r,1/Math.sin(o),r)},Ae.log=function(e,n){const r=a.CesiumMath.acosClamped(e.w);let i=0;return 0!==r&&(i=r/Math.sin(r)),t.Cartesian3.multiplyByScalar(e,i,n)},Ae.exp=function(e,n){const r=t.Cartesian3.magnitude(e);let a=0;return 0!==r&&(a=Math.sin(r)/r),n.x=e.x*a,n.y=e.y*a,n.z=e.z*a,n.w=Math.cos(r),n};const Ze=new t.Cartesian3,Ye=new t.Cartesian3,Ge=new Ae,Xe=new Ae;Ae.computeInnerQuadrangle=function(e,n,r,a){const i=Ae.conjugate(n,Ge);Ae.multiply(i,r,Xe);const s=Ae.log(Xe,Ze);Ae.multiply(i,e,Xe);const o=Ae.log(Xe,Ye);return t.Cartesian3.add(s,o,s),t.Cartesian3.multiplyByScalar(s,.25,s),t.Cartesian3.negate(s,s),Ae.exp(s,Ge),Ae.multiply(n,Ge,a)},Ae.squad=function(e,t,n,r,a,i){const s=Ae.slerp(e,t,a,Ge),o=Ae.slerp(n,r,a,Xe);return Ae.slerp(s,o,2*a*(1-a),i)};const Je=new Ae,He=1.9011074535173003,Qe=Te.supportsTypedArrays()?new Float32Array(8):[],Ke=Te.supportsTypedArrays()?new Float32Array(8):[],et=Te.supportsTypedArrays()?new Float32Array(8):[],tt=Te.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){const t=e+1,n=2*t+1;Qe[e]=1/(t*n),Ke[e]=t/n}function nt(e,t,n){let r,a,i=0,s=e.length-1;for(;i<=s;)if(r=~~((i+s)/2),a=n(e[r],t),a<0)i=r+1;else{if(!(a>0))return r;s=r-1}return~(s+1)}function rt(e,t,n,r,a){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=r,this.ut1MinusUtc=a}function at(e,t,n,r,a,i,s,o){this.year=e,this.month=t,this.day=n,this.hour=r,this.minute=a,this.second=i,this.millisecond=s,this.isLeapSecond=o}function it(e){return e%4==0&&e%100!=0||e%400==0}function st(e,t){this.julianDate=e,this.offset=t}Qe[7]=He/136,Ke[7]=8*He/17,Ae.fastSlerp=function(e,t,n,r){let a,i=Ae.dot(e,t);i>=0?a=1:(a=-1,i=-i);const s=i-1,o=1-n,u=n*n,l=o*o;for(let e=7;e>=0;--e)et[e]=(Qe[e]*u-Ke[e])*s,tt[e]=(Qe[e]*l-Ke[e])*s;const c=a*n*(1+et[0]*(1+et[1]*(1+et[2]*(1+et[3]*(1+et[4]*(1+et[5]*(1+et[6]*(1+et[7])))))))),d=o*(1+tt[0]*(1+tt[1]*(1+tt[2]*(1+tt[3]*(1+tt[4]*(1+tt[5]*(1+tt[6]*(1+tt[7])))))))),f=Ae.multiplyByScalar(e,d,Je);return Ae.multiplyByScalar(t,c,r),Ae.add(f,r,r)},Ae.fastSquad=function(e,t,n,r,a,i){const s=Ae.fastSlerp(e,t,a,Ge),o=Ae.fastSlerp(n,r,a,Xe);return Ae.fastSlerp(s,o,2*a*(1-a),i)},Ae.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},Ae.equalsEpsilon=function(e,t,n){return n=r.defaultValue(n,0),e===t||r.defined(e)&&r.defined(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},Ae.ZERO=Object.freeze(new Ae(0,0,0,0)),Ae.IDENTITY=Object.freeze(new Ae(0,0,0,1)),Ae.prototype.clone=function(e){return Ae.clone(this,e)},Ae.prototype.equals=function(e){return Ae.equals(this,e)},Ae.prototype.equalsEpsilon=function(e,t){return Ae.equalsEpsilon(this,e,t)},Ae.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var ot=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5});var ut=Object.freeze({UTC:0,TAI:1});const lt=new at,ct=[31,28,31,30,31,30,31,31,30,31,30,31];function dt(e,t){return Dt.compare(e.julianDate,t.julianDate)}const ft=new st;function mt(e){ft.julianDate=e;const t=Dt.leapSeconds;let n=nt(t,ft,dt);n<0&&(n=~n),n>=t.length&&(n=t.length-1);let r=t[n].offset;if(n>0){Dt.secondsDifference(t[n].julianDate,e)>r&&(n--,r=t[n].offset)}Dt.addSeconds(e,r,e)}function pt(e,t){ft.julianDate=e;const n=Dt.leapSeconds;let r=nt(n,ft,dt);if(r<0&&(r=~r),0===r)return Dt.addSeconds(e,-n[0].offset,t);if(r>=n.length)return Dt.addSeconds(e,-n[r-1].offset,t);const a=Dt.secondsDifference(n[r].julianDate,e);return 0===a?Dt.addSeconds(e,-n[r].offset,t):a<=1?void 0:Dt.addSeconds(e,-n[--r].offset,t)}function Ct(e,t,n){const r=t/ot.SECONDS_PER_DAY|0;return e+=r,(t-=ot.SECONDS_PER_DAY*r)<0&&(e--,t+=ot.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function yt(e,t,n,r,a,i,s){const o=(t-14)/12|0,u=e+4800+o;let l=(1461*u/4|0)+(367*(t-2-12*o)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(r-=12)<0&&(r+=24);const c=i+(r*ot.SECONDS_PER_HOUR+a*ot.SECONDS_PER_MINUTE+s*ot.SECONDS_PER_MILLISECOND);return c>=43200&&(l-=1),[l,c]}const ht=/^(\d{4})$/,wt=/^(\d{4})-(\d{2})$/,xt=/^(\d{4})-?(\d{3})$/,gt=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,St=/^(\d{4})-?(\d{2})-?(\d{2})$/,Et=/([Z+\-])?(\d{2})?:?(\d{2})?$/,_t=/^(\d{2})(\.\d+)?/.source+Et.source,Mt=/^(\d{2}):?(\d{2})(\.\d+)?/.source+Et.source,Ot=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+Et.source;function Dt(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=r.defaultValue(e,0),t=r.defaultValue(t,0),n=r.defaultValue(n,ut.UTC);const a=0|e;Ct(a,t+=(e-a)*ot.SECONDS_PER_DAY,this),n===ut.UTC&&mt(this)}Dt.fromGregorianDate=function(e,t){const n=yt(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return r.defined(t)?(Ct(n[0],n[1],t),mt(t),t):new Dt(n[0],n[1],ut.UTC)},Dt.fromDate=function(e,t){const n=yt(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return r.defined(t)?(Ct(n[0],n[1],t),mt(t),t):new Dt(n[0],n[1],ut.UTC)},Dt.fromIso8601=function(e,t){let n,a=(e=e.replace(",",".")).split("T"),i=1,s=1,o=0,u=0,l=0,c=0;const d=a[0],f=a[1];let m,p,C;if(a=d.match(St),null!==a)n=+a[1],i=+a[2],s=+a[3];else if(a=d.match(wt),null!==a)n=+a[1],i=+a[2];else if(a=d.match(ht),null!==a)n=+a[1];else{let e;if(a=d.match(xt),null!==a)n=+a[1],e=+a[2],p=it(n);else if(a=d.match(gt),null!==a){n=+a[1];e=7*+a[2]+(+a[3]||0)-new Date(Date.UTC(n,0,4)).getUTCDay()-3}m=new Date(Date.UTC(n,0,1)),m.setUTCDate(e),i=m.getUTCMonth()+1,s=m.getUTCDate()}if(p=it(n),r.defined(f)){a=f.match(Ot),null!==a?(o=+a[1],u=+a[2],l=+a[3],c=1e3*+(a[4]||0),C=5):(a=f.match(Mt),null!==a?(o=+a[1],u=+a[2],l=60*+(a[3]||0),C=4):(a=f.match(_t),null!==a&&(o=+a[1],u=60*+(a[2]||0),C=3)));const e=a[C],t=+a[C+1],r=+(a[C+2]||0);switch(e){case"+":o-=t,u-=r;break;case"-":o+=t,u+=r;break;case"Z":break;default:u+=new Date(Date.UTC(n,i-1,s,o,u)).getTimezoneOffset()}}const y=60===l;for(y&&l--;u>=60;)u-=60,o++;for(;o>=24;)o-=24,s++;for(m=p&&2===i?29:ct[i-1];s>m;)s-=m,i++,i>12&&(i-=12,n++),m=p&&2===i?29:ct[i-1];for(;u<0;)u+=60,o--;for(;o<0;)o+=24,s--;for(;s<1;)i--,i<1&&(i+=12,n--),m=p&&2===i?29:ct[i-1],s+=m;const h=yt(n,i,s,o,u,l,c);return r.defined(t)?(Ct(h[0],h[1],t),mt(t)):t=new Dt(h[0],h[1],ut.UTC),y&&Dt.addSeconds(t,1,t),t},Dt.now=function(e){return Dt.fromDate(new Date,e)};const Pt=new Dt(0,0,ut.TAI);function Tt(e){if(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=r.defaultValue(e.addNewLeapSeconds,!0),r.defined(e.data))Rt(this,e.data);else if(r.defined(e.url)){const t=i.Resource.createIfNeeded(e.url),n=this;this._downloadPromise=t.fetchJson().then((function(e){Rt(n,e)})).catch((function(){n._dataError=`An error occurred while retrieving the EOP data from the URL ${t.url}.`}))}else Rt(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function At(e,t){return Dt.compare(e.julianDate,t)}function Rt(e,t){if(!r.defined(t.columnNames))return void(e._dataError="Error in loaded EOP data: The columnNames property is required.");if(!r.defined(t.samples))return void(e._dataError="Error in loaded EOP data: The samples property is required.");const n=t.columnNames.indexOf("modifiedJulianDateUtc"),a=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),o=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||a<0||i<0||s<0||o<0||u<0||l<0)return void(e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");const c=e._samples=t.samples,d=e._dates=[];let f;e._dateColumn=n,e._xPoleWanderRadiansColumn=a,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=o,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=l,e._columnCount=t.columnNames.length,e._lastIndex=void 0;const m=e._addNewLeapSeconds;for(let t=0,a=c.length;t<a;t+=e._columnCount){const e=c[t+n],a=c[t+l],i=new Dt(e+ot.MODIFIED_JULIAN_DATE_DIFFERENCE,a,ut.TAI);if(d.push(i),m){if(a!==f&&r.defined(f)){const e=Dt.leapSeconds,t=nt(e,i,At);if(t<0){const n=new st(i,a);e.splice(~t,0,n)}}f=a}}}function It(e,t,n,r,a){const i=n*r;a.xPoleWander=t[i+e._xPoleWanderRadiansColumn],a.yPoleWander=t[i+e._yPoleWanderRadiansColumn],a.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],a.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],a.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function zt(e,t,n){return t+e*(n-t)}function Nt(e,t,n,r,a,i,s){const o=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;const u=t[a],l=t[i];if(u.equals(l)||r.equals(u))return It(e,n,a,o,s),s;if(r.equals(l))return It(e,n,i,o,s),s;const c=Dt.secondsDifference(r,u)/Dt.secondsDifference(l,u),d=a*o,f=i*o;let m=n[d+e._ut1MinusUtcSecondsColumn],p=n[f+e._ut1MinusUtcSecondsColumn];const C=p-m;if(C>.5||C<-.5){const t=n[d+e._taiMinusUtcSecondsColumn],a=n[f+e._taiMinusUtcSecondsColumn];t!==a&&(l.equals(r)?m=p:p-=a-t)}return s.xPoleWander=zt(c,n[d+e._xPoleWanderRadiansColumn],n[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=zt(c,n[d+e._yPoleWanderRadiansColumn],n[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=zt(c,n[d+e._xCelestialPoleOffsetRadiansColumn],n[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=zt(c,n[d+e._yCelestialPoleOffsetRadiansColumn],n[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=zt(c,m,p),s}function Ut(e,t,n){this.heading=r.defaultValue(e,0),this.pitch=r.defaultValue(t,0),this.roll=r.defaultValue(n,0)}Dt.toGregorianDate=function(e,t){let n=!1,a=pt(e,Pt);r.defined(a)||(Dt.addSeconds(e,-1,Pt),a=pt(Pt,Pt),n=!0);let i=a.dayNumber;const s=a.secondsOfDay;s>=43200&&(i+=1);let o=i+68569|0;const u=4*o/146097|0;o=o-((146097*u+3)/4|0)|0;const l=4e3*(o+1)/1461001|0;o=o-(1461*l/4|0)+31|0;const c=80*o/2447|0,d=o-(2447*c/80|0)|0;o=c/11|0;const f=c+2-12*o|0,m=100*(u-49)+l+o|0;let p=s/ot.SECONDS_PER_HOUR|0,C=s-p*ot.SECONDS_PER_HOUR;const y=C/ot.SECONDS_PER_MINUTE|0;C-=y*ot.SECONDS_PER_MINUTE;let h=0|C;const w=(C-h)/ot.SECONDS_PER_MILLISECOND;return p+=12,p>23&&(p-=24),n&&(h+=1),r.defined(t)?(t.year=m,t.month=f,t.day=d,t.hour=p,t.minute=y,t.second=h,t.millisecond=w,t.isLeapSecond=n,t):new at(m,f,d,p,y,h,w,n)},Dt.toDate=function(e){const t=Dt.toGregorianDate(e,lt);let n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},Dt.toIso8601=function(e,t){const n=Dt.toGregorianDate(e,lt);let a=n.year,i=n.month,s=n.day,o=n.hour;const u=n.minute,l=n.second,c=n.millisecond;let d;return 1e4===a&&1===i&&1===s&&0===o&&0===u&&0===l&&0===c&&(a=9999,i=12,s=31,o=24),r.defined(t)||0===c?r.defined(t)&&0!==t?(d=(.01*c).toFixed(t).replace(".","").slice(0,t),`${a.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${o.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}.${d}Z`):`${a.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${o.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}Z`:(d=(.01*c).toString().replace(".",""),`${a.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}T${o.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}.${d}Z`)},Dt.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new Dt(e.dayNumber,e.secondsOfDay,ut.TAI)},Dt.compare=function(e,t){const n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},Dt.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},Dt.equalsEpsilon=function(e,t,n){return n=r.defaultValue(n,0),e===t||r.defined(e)&&r.defined(t)&&Math.abs(Dt.secondsDifference(e,t))<=n},Dt.totalDays=function(e){return e.dayNumber+e.secondsOfDay/ot.SECONDS_PER_DAY},Dt.secondsDifference=function(e,t){return(e.dayNumber-t.dayNumber)*ot.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},Dt.daysDifference=function(e,t){return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/ot.SECONDS_PER_DAY},Dt.computeTaiMinusUtc=function(e){ft.julianDate=e;const t=Dt.leapSeconds;let n=nt(t,ft,dt);return n<0&&(n=~n,--n,n<0&&(n=0)),t[n].offset},Dt.addSeconds=function(e,t,n){return Ct(e.dayNumber,e.secondsOfDay+t,n)},Dt.addMinutes=function(e,t,n){const r=e.secondsOfDay+t*ot.SECONDS_PER_MINUTE;return Ct(e.dayNumber,r,n)},Dt.addHours=function(e,t,n){const r=e.secondsOfDay+t*ot.SECONDS_PER_HOUR;return Ct(e.dayNumber,r,n)},Dt.addDays=function(e,t,n){return Ct(e.dayNumber+t,e.secondsOfDay,n)},Dt.lessThan=function(e,t){return Dt.compare(e,t)<0},Dt.lessThanOrEquals=function(e,t){return Dt.compare(e,t)<=0},Dt.greaterThan=function(e,t){return Dt.compare(e,t)>0},Dt.greaterThanOrEquals=function(e,t){return Dt.compare(e,t)>=0},Dt.prototype.clone=function(e){return Dt.clone(this,e)},Dt.prototype.equals=function(e){return Dt.equals(this,e)},Dt.prototype.equalsEpsilon=function(e,t){return Dt.equalsEpsilon(this,e,t)},Dt.prototype.toString=function(){return Dt.toIso8601(this)},Dt.leapSeconds=[new st(new Dt(2441317,43210,ut.TAI),10),new st(new Dt(2441499,43211,ut.TAI),11),new st(new Dt(2441683,43212,ut.TAI),12),new st(new Dt(2442048,43213,ut.TAI),13),new st(new Dt(2442413,43214,ut.TAI),14),new st(new Dt(2442778,43215,ut.TAI),15),new st(new Dt(2443144,43216,ut.TAI),16),new st(new Dt(2443509,43217,ut.TAI),17),new st(new Dt(2443874,43218,ut.TAI),18),new st(new Dt(2444239,43219,ut.TAI),19),new st(new Dt(2444786,43220,ut.TAI),20),new st(new Dt(2445151,43221,ut.TAI),21),new st(new Dt(2445516,43222,ut.TAI),22),new st(new Dt(2446247,43223,ut.TAI),23),new st(new Dt(2447161,43224,ut.TAI),24),new st(new Dt(2447892,43225,ut.TAI),25),new st(new Dt(2448257,43226,ut.TAI),26),new st(new Dt(2448804,43227,ut.TAI),27),new st(new Dt(2449169,43228,ut.TAI),28),new st(new Dt(2449534,43229,ut.TAI),29),new st(new Dt(2450083,43230,ut.TAI),30),new st(new Dt(2450630,43231,ut.TAI),31),new st(new Dt(2451179,43232,ut.TAI),32),new st(new Dt(2453736,43233,ut.TAI),33),new st(new Dt(2454832,43234,ut.TAI),34),new st(new Dt(2456109,43235,ut.TAI),35),new st(new Dt(2457204,43236,ut.TAI),36),new st(new Dt(2457754,43237,ut.TAI),37)],Tt.NONE=Object.freeze({getPromiseToLoad:function(){return Promise.resolve()},compute:function(e,t){return r.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new rt(0,0,0,0,0),t}}),Tt.prototype.getPromiseToLoad=function(){return Promise.resolve(this._downloadPromise)},Tt.prototype.compute=function(e,t){if(!r.defined(this._samples)){if(r.defined(this._dataError))throw new s.RuntimeError(this._dataError);return}if(r.defined(t)||(t=new rt(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;const n=this._dates,a=this._lastIndex;let i=0,o=0;if(r.defined(a)){const s=n[a],u=n[a+1],l=Dt.lessThanOrEquals(s,e),c=!r.defined(u),d=c||Dt.greaterThanOrEquals(u,e);if(l&&d)return i=a,!c&&u.equals(e)&&++i,o=i+1,Nt(this,n,this._samples,e,i,o,t),t}let u=nt(n,e,Dt.compare,this._dateColumn);return u>=0?(u<n.length-1&&n[u+1].equals(e)&&++u,i=u,o=u):(o=~u,i=o-1,i<0&&(i=0)),this._lastIndex=i,Nt(this,n,this._samples,e,i,o,t),t},Ut.fromQuaternion=function(e,t){r.defined(t)||(t=new Ut);const n=2*(e.w*e.y-e.z*e.x),i=1-2*(e.x*e.x+e.y*e.y),s=2*(e.w*e.x+e.y*e.z),o=1-2*(e.y*e.y+e.z*e.z),u=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(u,o),t.roll=Math.atan2(s,i),t.pitch=-a.CesiumMath.asinClamped(n),t},Ut.fromDegrees=function(e,t,n,i){return r.defined(i)||(i=new Ut),i.heading=e*a.CesiumMath.RADIANS_PER_DEGREE,i.pitch=t*a.CesiumMath.RADIANS_PER_DEGREE,i.roll=n*a.CesiumMath.RADIANS_PER_DEGREE,i},Ut.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new Ut(e.heading,e.pitch,e.roll)},Ut.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},Ut.equalsEpsilon=function(e,t,n,i){return e===t||r.defined(e)&&r.defined(t)&&a.CesiumMath.equalsEpsilon(e.heading,t.heading,n,i)&&a.CesiumMath.equalsEpsilon(e.pitch,t.pitch,n,i)&&a.CesiumMath.equalsEpsilon(e.roll,t.roll,n,i)},Ut.prototype.clone=function(e){return Ut.clone(this,e)},Ut.prototype.equals=function(e){return Ut.equals(this,e)},Ut.prototype.equalsEpsilon=function(e,t,n){return Ut.equalsEpsilon(this,e,t,n)},Ut.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};const bt=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;let Ft,qt,Wt;function vt(e){return"undefined"==typeof document?e:(r.defined(Ft)||(Ft=document.createElement("a")),Ft.href=e,Ft.href=Ft.href,Ft.href)}function Vt(){if(r.defined(qt))return qt;let e;return e="undefined"!=typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"==typeof define&&r.defined(define.amd)&&!define.amd.toUrlUndefined&&r.defined(require.toUrl)?i.getAbsoluteUri("..",Lt("Core/buildModuleUrl.js")):function(){const e=document.getElementsByTagName("script");for(let t=0,n=e.length;t<n;++t){const n=e[t].getAttribute("src"),r=bt.exec(n);if(null!==r)return r[1]}}(),qt=new i.Resource({url:vt(e)}),qt.appendForwardSlash(),qt}function Bt(e){return vt(require.toUrl(`../${e}`))}function $t(e){return Vt().getDerivedResource({url:e}).url}function Lt(e){r.defined(Wt)||(Wt="object"==typeof define&&r.defined(define.amd)&&!define.amd.toUrlUndefined&&r.defined(require.toUrl)?Bt:$t);return Wt(e)}function jt(e,t,n){this.x=e,this.y=t,this.s=n}function kt(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=i.Resource.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=r.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=r.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new Dt(this._sampleZeroJulianEphemerisDate,0,ut.TAI),this._stepSizeDays=r.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=r.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=r.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];const t=this._interpolationOrder,n=this._denominators=new Array(t+1),a=this._xTable=new Array(t+1),s=Math.pow(this._stepSizeDays,t);for(let e=0;e<=t;++e){n[e]=s,a[e]=e*this._stepSizeDays;for(let r=0;r<=t;++r)r!==e&&(n[e]*=e-r);n[e]=1/n[e]}this._work=new Array(t+1),this._coef=new Array(t+1)}Lt._cesiumScriptRegex=bt,Lt._buildModuleUrlFromBaseUrl=$t,Lt._clearBaseResource=function(){qt=void 0},Lt.setBaseUrl=function(e){qt=i.Resource.DEFAULT.getDerivedResource({url:e})},Lt.getCesiumBaseUrl=Vt;const Zt=new Dt(0,0,ut.TAI);function Yt(e,t,n){const r=Zt;return r.dayNumber=t,r.secondsOfDay=n,Dt.daysDifference(r,e._sampleZeroDateTT)}function Gt(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];let n;const a=e._xysFileUrlTemplate;n=r.defined(a)?a.getDerivedResource({templateValues:{0:t}}):new i.Resource({url:Lt(`Assets/IAU2006_XYS/IAU2006_XYS_${t}.json`)});const s=n.fetchJson().then((function(n){e._chunkDownloadsInProgress[t]=!1;const r=e._samples,a=n.samples,i=t*e._samplesPerXysFile*3;for(let e=0,t=a.length;e<t;++e)r[i+e]=a[e]}));return e._chunkDownloadsInProgress[t]=s,s}kt.prototype.preload=function(e,t,n,r){const a=Yt(this,e,t),i=Yt(this,n,r);let s=a/this._stepSizeDays-this._interpolationOrder/2|0;s<0&&(s=0);let o=i/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;o>=this._totalSamples&&(o=this._totalSamples-1);const u=s/this._samplesPerXysFile|0,l=o/this._samplesPerXysFile|0,c=[];for(let e=u;e<=l;++e)c.push(Gt(this,e));return Promise.all(c)},kt.prototype.computeXysRadians=function(e,t,n){const a=Yt(this,e,t);if(a<0)return;const i=a/this._stepSizeDays|0;if(i>=this._totalSamples)return;const s=this._interpolationOrder;let o=i-(s/2|0);o<0&&(o=0);let u=o+s;u>=this._totalSamples&&(u=this._totalSamples-1,o=u-s,o<0&&(o=0));let l=!1;const c=this._samples;if(r.defined(c[3*o])||(Gt(this,o/this._samplesPerXysFile|0),l=!0),r.defined(c[3*u])||(Gt(this,u/this._samplesPerXysFile|0),l=!0),l)return;r.defined(n)?(n.x=0,n.y=0,n.s=0):n=new jt(0,0,0);const d=a-o*this._stepSizeDays,f=this._work,m=this._denominators,p=this._coef,C=this._xTable;let y,h;for(y=0;y<=s;++y)f[y]=d-C[y];for(y=0;y<=s;++y){for(p[y]=1,h=0;h<=s;++h)h!==y&&(p[y]*=f[h]);p[y]*=m[y];let e=3*(o+y);n.x+=p[y]*c[e++],n.y+=p[y]*c[e++],n.s+=p[y]*c[e]}return n};const Xt={},Jt={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Ht={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Qt={},Kt={east:new t.Cartesian3,north:new t.Cartesian3,up:new t.Cartesian3,west:new t.Cartesian3,south:new t.Cartesian3,down:new t.Cartesian3};let en=new t.Cartesian3,tn=new t.Cartesian3,nn=new t.Cartesian3;Xt.localFrameToFixedFrameGenerator=function(e,s){if(!Jt.hasOwnProperty(e)||!Jt[e].hasOwnProperty(s))throw new i.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");const o=Jt[e][s];let u;const l=e+s;return r.defined(Qt[l])?u=Qt[l]:(u=function(i,u,l){if(r.defined(l)||(l=new n.Matrix4),t.Cartesian3.equalsEpsilon(i,t.Cartesian3.ZERO,a.CesiumMath.EPSILON14))t.Cartesian3.unpack(Ht[e],0,en),t.Cartesian3.unpack(Ht[s],0,tn),t.Cartesian3.unpack(Ht[o],0,nn);else if(a.CesiumMath.equalsEpsilon(i.x,0,a.CesiumMath.EPSILON14)&&a.CesiumMath.equalsEpsilon(i.y,0,a.CesiumMath.EPSILON14)){const n=a.CesiumMath.sign(i.z);t.Cartesian3.unpack(Ht[e],0,en),"east"!==e&&"west"!==e&&t.Cartesian3.multiplyByScalar(en,n,en),t.Cartesian3.unpack(Ht[s],0,tn),"east"!==s&&"west"!==s&&t.Cartesian3.multiplyByScalar(tn,n,tn),t.Cartesian3.unpack(Ht[o],0,nn),"east"!==o&&"west"!==o&&t.Cartesian3.multiplyByScalar(nn,n,nn)}else{(u=r.defaultValue(u,n.Ellipsoid.WGS84)).geodeticSurfaceNormal(i,Kt.up);const a=Kt.up,l=Kt.east;l.x=-i.y,l.y=i.x,l.z=0,t.Cartesian3.normalize(l,Kt.east),t.Cartesian3.cross(a,l,Kt.north),t.Cartesian3.multiplyByScalar(Kt.up,-1,Kt.down),t.Cartesian3.multiplyByScalar(Kt.east,-1,Kt.west),t.Cartesian3.multiplyByScalar(Kt.north,-1,Kt.south),en=Kt[e],tn=Kt[s],nn=Kt[o]}return l[0]=en.x,l[1]=en.y,l[2]=en.z,l[3]=0,l[4]=tn.x,l[5]=tn.y,l[6]=tn.z,l[7]=0,l[8]=nn.x,l[9]=nn.y,l[10]=nn.z,l[11]=0,l[12]=i.x,l[13]=i.y,l[14]=i.z,l[15]=1,l},Qt[l]=u),u},Xt.eastNorthUpToFixedFrame=Xt.localFrameToFixedFrameGenerator("east","north"),Xt.northEastDownToFixedFrame=Xt.localFrameToFixedFrameGenerator("north","east"),Xt.northUpEastToFixedFrame=Xt.localFrameToFixedFrameGenerator("north","up"),Xt.northWestUpToFixedFrame=Xt.localFrameToFixedFrameGenerator("north","west");const rn=new Ae,an=new t.Cartesian3(1,1,1),sn=new n.Matrix4;Xt.headingPitchRollToFixedFrame=function(e,a,i,s,o){s=r.defaultValue(s,Xt.eastNorthUpToFixedFrame);const u=Ae.fromHeadingPitchRoll(a,rn),l=n.Matrix4.fromTranslationQuaternionRotationScale(t.Cartesian3.ZERO,u,an,sn);return o=s(e,i,o),n.Matrix4.multiply(o,l,o)};const on=new n.Matrix4,un=new n.Matrix3;Xt.headingPitchRollQuaternion=function(e,t,r,a,i){const s=Xt.headingPitchRollToFixedFrame(e,t,r,a,on),o=n.Matrix4.getMatrix3(s,un);return Ae.fromRotationMatrix(o,i)};const ln=new t.Cartesian3(1,1,1),cn=new t.Cartesian3,dn=new n.Matrix4,fn=new n.Matrix4,mn=new n.Matrix3,pn=new Ae;Xt.fixedFrameToHeadingPitchRoll=function(e,a,i,s){a=r.defaultValue(a,n.Ellipsoid.WGS84),i=r.defaultValue(i,Xt.eastNorthUpToFixedFrame),r.defined(s)||(s=new Ut);const o=n.Matrix4.getTranslation(e,cn);if(t.Cartesian3.equals(o,t.Cartesian3.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;let u=n.Matrix4.inverseTransformation(i(o,a,dn),dn),l=n.Matrix4.setScale(e,ln,fn);l=n.Matrix4.setTranslation(l,t.Cartesian3.ZERO,l),u=n.Matrix4.multiply(u,l,u);let c=Ae.fromRotationMatrix(n.Matrix4.getMatrix3(u,mn),pn);return c=Ae.normalize(c,c),Ut.fromQuaternion(c,s)};const Cn=a.CesiumMath.TWO_PI/86400;let yn=new Dt;Xt.computeTemeToPseudoFixedMatrix=function(e,t){yn=Dt.addSeconds(e,-Dt.computeTaiMinusUtc(e),yn);const i=yn.dayNumber,s=yn.secondsOfDay;let o;const u=i-2451545;o=s>=43200?(u+.5)/ot.DAYS_PER_JULIAN_CENTURY:(u-.5)/ot.DAYS_PER_JULIAN_CENTURY;const l=(24110.54841+o*(8640184.812866+o*(.093104+-62e-7*o)))*Cn%a.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(i-2451545.5))*((s+.5*ot.SECONDS_PER_DAY)%ot.SECONDS_PER_DAY),c=Math.cos(l),d=Math.sin(l);return r.defined(t)?(t[0]=c,t[1]=-d,t[2]=0,t[3]=d,t[4]=c,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new n.Matrix3(c,d,0,-d,c,0,0,0,1)},Xt.iau2006XysData=new kt,Xt.earthOrientationParameters=Tt.NONE;const hn=32.184;Xt.preloadIcrfFixed=function(e){const t=e.start.dayNumber,n=e.start.secondsOfDay+hn,r=e.stop.dayNumber,a=e.stop.secondsOfDay+hn,i=Xt.iau2006XysData.preload(t,n,r,a),s=Xt.earthOrientationParameters.getPromiseToLoad();return Promise.all([i,s])},Xt.computeIcrfToFixedMatrix=function(e,t){r.defined(t)||(t=new n.Matrix3);const a=Xt.computeFixedToIcrfMatrix(e,t);if(r.defined(a))return n.Matrix3.transpose(a,t)};const wn=new jt(0,0,0),xn=new rt(0,0,0,0,0),gn=new n.Matrix3,Sn=new n.Matrix3;Xt.computeFixedToIcrfMatrix=function(e,t){r.defined(t)||(t=new n.Matrix3);const i=Xt.earthOrientationParameters.compute(e,xn);if(!r.defined(i))return;const s=e.dayNumber,o=e.secondsOfDay+hn,u=Xt.iau2006XysData.computeXysRadians(s,o,wn);if(!r.defined(u))return;const l=u.x+i.xPoleOffset,c=u.y+i.yPoleOffset,d=1/(1+Math.sqrt(1-l*l-c*c)),f=gn;f[0]=1-d*l*l,f[3]=-d*l*c,f[6]=l,f[1]=-d*l*c,f[4]=1-d*c*c,f[7]=c,f[2]=-l,f[5]=-c,f[8]=1-d*(l*l+c*c);const m=n.Matrix3.fromRotationZ(-u.s,Sn),p=n.Matrix3.multiply(f,m,gn),C=e.dayNumber-2451545,y=(e.secondsOfDay-Dt.computeTaiMinusUtc(e)+i.ut1MinusUtc)/ot.SECONDS_PER_DAY;let h=.779057273264+y+.00273781191135448*(C+y);h=h%1*a.CesiumMath.TWO_PI;const w=n.Matrix3.fromRotationZ(h,Sn),x=n.Matrix3.multiply(p,w,gn),g=Math.cos(i.xPoleWander),S=Math.cos(i.yPoleWander),E=Math.sin(i.xPoleWander),_=Math.sin(i.yPoleWander);let M=s-2451545+o/ot.SECONDS_PER_DAY;M/=36525;const O=-47e-6*M*a.CesiumMath.RADIANS_PER_DEGREE/3600,D=Math.cos(O),P=Math.sin(O),T=Sn;return T[0]=g*D,T[1]=g*P,T[2]=E,T[3]=-S*P+_*E*D,T[4]=S*D+_*E*P,T[5]=-_*g,T[6]=-_*P-S*E*D,T[7]=_*D-S*E*P,T[8]=S*g,n.Matrix3.multiply(x,T,t)};const En=new n.Cartesian4;Xt.pointToWindowCoordinates=function(e,t,n,r){return(r=Xt.pointToGLWindowCoordinates(e,t,n,r)).y=2*t[5]-r.y,r},Xt.pointToGLWindowCoordinates=function(e,a,i,s){r.defined(s)||(s=new t.Cartesian2);const o=En;return n.Matrix4.multiplyByVector(e,n.Cartesian4.fromElements(i.x,i.y,i.z,1,o),o),n.Cartesian4.multiplyByScalar(o,1/o.w,o),n.Matrix4.multiplyByVector(a,o,o),t.Cartesian2.fromCartesian4(o,s)};const _n=new t.Cartesian3,Mn=new t.Cartesian3,On=new t.Cartesian3;Xt.rotationMatrixFromPositionVelocity=function(e,i,s,o){const u=r.defaultValue(s,n.Ellipsoid.WGS84).geodeticSurfaceNormal(e,_n);let l=t.Cartesian3.cross(i,u,Mn);t.Cartesian3.equalsEpsilon(l,t.Cartesian3.ZERO,a.CesiumMath.EPSILON6)&&(l=t.Cartesian3.clone(t.Cartesian3.UNIT_X,l));const c=t.Cartesian3.cross(l,i,On);return t.Cartesian3.normalize(c,c),t.Cartesian3.cross(i,c,l),t.Cartesian3.negate(l,l),t.Cartesian3.normalize(l,l),r.defined(o)||(o=new n.Matrix3),o[0]=i.x,o[1]=i.y,o[2]=i.z,o[3]=l.x,o[4]=l.y,o[5]=l.z,o[6]=c.x,o[7]=c.y,o[8]=c.z,o};const Dn=new n.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Pn=new n.Cartographic,Tn=new t.Cartesian3,An=new t.Cartesian3,Rn=new n.Matrix3,In=new n.Matrix4,zn=new n.Matrix4;Xt.basisTo2D=function(e,r,a){const i=n.Matrix4.getTranslation(r,An),s=e.ellipsoid,o=s.cartesianToCartographic(i,Pn),u=e.project(o,Tn);t.Cartesian3.fromElements(u.z,u.x,u.y,u);const l=Xt.eastNorthUpToFixedFrame(i,s,In),c=n.Matrix4.inverseTransformation(l,zn),d=n.Matrix4.getMatrix3(r,Rn),f=n.Matrix4.multiplyByMatrix3(c,d,a);return n.Matrix4.multiply(Dn,f,a),n.Matrix4.setTranslation(a,u,a),a},Xt.wgs84To2DModelMatrix=function(e,r,a){const i=e.ellipsoid,s=Xt.eastNorthUpToFixedFrame(r,i,In),o=n.Matrix4.inverseTransformation(s,zn),u=i.cartesianToCartographic(r,Pn),l=e.project(u,Tn);t.Cartesian3.fromElements(l.z,l.x,l.y,l);const c=n.Matrix4.fromTranslation(l,In);return n.Matrix4.multiply(Dn,o,a),n.Matrix4.multiply(c,a,a),a};var Nn=Xt;e.BoundingSphere=c,e.FeatureDetection=Te,e.GeographicProjection=o,e.Intersect=u,e.Interval=l,e.Quaternion=Ae,e.Transforms=Nn,e.buildModuleUrl=Lt}));
