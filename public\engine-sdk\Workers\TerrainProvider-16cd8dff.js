define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./IndexDatatype-e1c63859"],(function(r,e,t,o,n){"use strict";function i(){t.DeveloperError.throwInstantiationError()}Object.defineProperties(i.prototype,{errorEvent:{get:t.DeveloperError.throwInstantiationError},credit:{get:t.DeveloperError.throwInstantiationError},tilingScheme:{get:t.DeveloperError.throwInstantiationError},ready:{get:t.DeveloperError.throwInstantiationError},readyPromise:{get:t.DeveloperError.throwInstantiationError},hasWaterMask:{get:t.DeveloperError.throwInstantiationError},hasVertexNormals:{get:t.DeveloperError.throwInstantiationError},availability:{get:t.DeveloperError.throwInstantiationError}});var a=[];i.getRegularGridIndices=function(r,n){if(r*n>=o.CesiumMath.FOUR_GIGABYTES)throw new t.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var i=a[r];e.defined(i)||(a[r]=i=[]);var s=i[n];return e.defined(s)||(s=r*n<o.CesiumMath.SIXTY_FOUR_KILOBYTES?i[n]=new Uint16Array((r-1)*(n-1)*6):i[n]=new Uint32Array((r-1)*(n-1)*6),u(r,n,s,0)),s};var s=[];i.getRegularGridIndicesAndEdgeIndices=function(r,n){if(r*n>=o.CesiumMath.FOUR_GIGABYTES)throw new t.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var a=s[r];e.defined(a)||(s[r]=a=[]);var h=a[n];if(!e.defined(h)){var u=i.getRegularGridIndices(r,n),c=d(r,n),E=c.westIndicesSouthToNorth,I=c.southIndicesEastToWest,l=c.eastIndicesNorthToSouth,v=c.northIndicesWestToEast;h=a[n]={indices:u,westIndicesSouthToNorth:E,southIndicesEastToWest:I,eastIndicesNorthToSouth:l,northIndicesWestToEast:v}}return h};var h=[];function d(r,e){var t,o=new Array(e),n=new Array(r),i=new Array(e),a=new Array(r);for(t=0;t<r;++t)a[t]=t,n[t]=r*e-1-t;for(t=0;t<e;++t)i[t]=(t+1)*r-1,o[t]=(e-t-1)*r;return{westIndicesSouthToNorth:o,southIndicesEastToWest:n,eastIndicesNorthToSouth:i,northIndicesWestToEast:a}}function u(r,e,t,o){for(var n=0,i=0;i<e-1;++i){for(var a=0;a<r-1;++a){var s=n,h=s+r,d=h+1,u=s+1;t[o++]=s,t[o++]=h,t[o++]=u,t[o++]=u,t[o++]=h,t[o++]=d,++n}++n}}function c(r,e,t,o,n,i){for(var a=r[0],s=r.length-1,h=0,d=1;d<s;++d)h=r[d],n[i++]=a,n[i++]=h,n[i++]=t,n[i++]=t,n[i++]=h,n[i++]=t+1,a=h,++t;return h=e[0],n[i++]=a,n[i++]=h,n[i++]=t,n[i++]=t,n[i++]=h,n[i++]=o,i}i.getRegularGridAndSkirtIndicesAndEdgeIndices=function(r,a){if(r*a>=o.CesiumMath.FOUR_GIGABYTES)throw new t.DeveloperError("The total number of vertices (width * height) must be less than 4,294,967,296.");var s=h[r];e.defined(s)||(h[r]=s=[]);var c=s[a];if(!e.defined(c)){var E=r*a,I=(r-1)*(a-1)*6,l=2*r+2*a,v=6*Math.max(0,l-4),T=E+l,p=I+v,w=d(r,a),f=w.westIndicesSouthToNorth,g=w.southIndicesEastToWest,m=w.eastIndicesNorthToSouth,y=w.northIndicesWestToEast,D=n.IndexDatatype.createTypedArray(T,p);u(r,a,D,0),i.addSkirtIndices(f,g,m,y,E,D,I),c=s[a]={indices:D,westIndicesSouthToNorth:f,southIndicesEastToWest:g,eastIndicesNorthToSouth:m,northIndicesWestToEast:y,indexCountWithoutSkirts:I}}return c},i.addSkirtIndices=function(r,e,t,o,n,i,a,s,h,d){d=c(r,o,n,s,h,d),d=c(e,r,i,n,h,d),d=c(t,e,a,i,h,d),c(o,t,s,a,h,d)},i.heightmapTerrainQuality=.25,i.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(r,e,t){return 2*r.maximumRadius*Math.PI*i.heightmapTerrainQuality/(e*t)},i.prototype.requestTileGeometry=t.DeveloperError.throwInstantiationError,i.prototype.getLevelMaximumGeometricError=t.DeveloperError.throwInstantiationError,i.prototype.getTileDataAvailable=t.DeveloperError.throwInstantiationError,i.prototype.loadTileDataAvailability=t.DeveloperError.throwInstantiationError,r.TerrainProvider=i}));