<template>
  <div class="ai-robot-container" v-drag="{ draggableClass: 'robot-mask' }" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag">
    <img src="@/assets/ScreenMiddle/AiRobot/robot.gif" class="w240 h240 absolute top-0 left-0" v-if="submitLoading" />
    <img src="@/assets/ScreenMiddle/AiRobot/robot.png" class="h220 absolute top-15 left-89 cursor-pointer" v-else />
    <div class="h220 absolute top-15 left-89 cursor-pointer bg-transparent z-1000 w114 robot-mask" @click="handleShowSender"></div>
    <div class="result-container" v-if="reply.length > 0" :class="{ zhanKai: isZhanKai }">
      <img
        src="@/assets/ScreenMiddle/AiRobot/shouqi.svg"
        class="absolute bottom-8 right-8 cursor-pointer w44 h44 cursor-pointer"
        v-if="isZhanKai"
        @click="isZhanKai = false" />
      <img
        src="@/assets/ScreenMiddle/AiRobot/zhankai.svg"
        class="absolute bottom-8 right-8 cursor-pointer w44 h44 cursor-pointer"
        v-else
        @click="isZhanKai = true" />
      <div class="result-content" ref="resultContentRef">
        <Typewriter ref="typerRef" :content="reply" :typing="{ interval: 40 }" :is-markdown="true" isFog />
      </div>
    </div>
    <div class="sender-container animate__animated animate__faster animate__flipInX" v-show="showSender && !fromQuestion">
      <Sender
        class="sender-style"
        ref="refSender"
        :input-style="{ color: '#000000', fontSize: '25px', backgroundColor: 'transparent', backDropFilter: 'blur(10px)' }"
        v-model="inputValue"
        :loading="submitLoading"
        @submit="handleSubmit">
        <template #action-list>
          <div style="display: flex; gap: 8px; align-items: center; width: 220px">
            <div class="tools-btns flex mt-6 ml-20">
              <div
                class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
                :class="{ selected: !isQuestionType }"
                v-for="(item, index) in toolsBtns"
                :key="index"
                @click="handleSelectTools(item)">
                {{ !isQuestionType ? item : '提问' }}
              </div>
            </div>
            <el-button round color="rgba(255,255,255,0.74)" @click="handleRecord">
              <el-icon size="22px" color="#000000">
                <Microphone v-if="!recording" />
                <Loading v-else />
              </el-icon>
            </el-button>
            <el-button
              round
              color="rgba(255,255,255,0.74)"
              :loading="submitLoading"
              v-if="!submitLoading"
              @click="handleSubmit"
              :disabled="inputValue.length === 0">
              <el-icon size="22px" color="#000000"><Promotion /></el-icon>
            </el-button>
            <el-button v-else type="danger" circle @click="handleCancel">
              <el-icon class="is-loaidng">
                <Loading />
              </el-icon>
            </el-button>
          </div>
        </template>
      </Sender>
      <!-- <div class="tools-btns flex mt-6 ml-20">
        <div
          class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
          :class="{ selected: !isQuestionType }"
          v-for="(item, index) in toolsBtns"
          :key="index"
          @click="handleSelectTools(item)">
          {{ !isQuestionType ? item : '提问' }}
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useAiRobot } from './helper/index'
  import CreateMqtt from '@/utils/mqtt.js'

  import lib from '@/utils/lib'
  const fromQuestion = ref(false)
  const toolsBtns = ['操作']
  const selectedTools = ref('操作')
  const isDragging = ref(false)
  const mouseDownPos = reactive({ x: 0, y: 0 })
  const dragThreshold = 5 // 拖动阈值，移动超过这个像素才算拖动
  const isZhanKai = ref(false)

  const isQuestionType = computed(() => {
    return lib.store().storeScreenData.isQuestionType
  })
  watch(isQuestionType, (newVal) => {
    selectedTools.value = newVal ? '操作' : ''
  })

  const handleSelectTools = (item) => {
    lib.store().storeScreenData.isQuestionType = !lib.store().storeScreenData.isQuestionType
    // if (selectedTools.value === item) {
    //   lib.store().storeScreenData.isQuestionType = true
    //   // selectedTools.value = ''
    // } else {
    //   lib.store().storeScreenData.isQuestionType = false
    //   // selectedTools.value = item
    // }
  }
  const appKey = computed(() => {
    // return lib.store().storeScreenData.isQuestionType ? 'qjxiangmuwenda' : 'BimEngineCode2'
    return lib.store().storeScreenData.isQuestionType ? 'qjxiangmuwenda' : 'BimEngineMCPAgent'
  })
  const { inputValue, recording, submitLoading, reply, showSender, currentConversationId, handleRecord, handleCancel, handleSubmit } = useAiRobot(appKey)

  const startDrag = (e) => {
    isDragging.value = false
    mouseDownPos.x = e.clientX
    mouseDownPos.y = e.clientY
  }

  const onDrag = (e) => {
    if (!isDragging.value) {
      // 计算移动距离
      const dx = Math.abs(e.clientX - mouseDownPos.x)
      const dy = Math.abs(e.clientY - mouseDownPos.y)
      // 只有当移动距离超过阈值时才认为是拖动
      if (dx > dragThreshold || dy > dragThreshold) {
        isDragging.value = true
      }
    }
  }

  const endDrag = () => {
    // 使用setTimeout确保点击事件在拖动结束后触发
    setTimeout(() => {
      isDragging.value = false
    }, 10)
  }

  const handleShowSender = () => {
    if (isDragging.value) return
    console.log('handleShowSender')
    fromQuestion.value = false
    showSender.value = !showSender.value
    inputValue.value = ''
    if (!showSender.value) {
      reply.value = ''
      handleCancel()
    }
  }

  lib.bus.busAIChat.on(async (question: string) => {
    console.log('AI机器人接收到：', question)
    fromQuestion.value = true
    // selectedTools.value = ''
    currentConversationId.value = ''
    lib.store().storeScreenData.isQuestionType = true

    if (question) {
      if (submitLoading.value) {
        handleCancel()
      }
      inputValue.value = question
      handleSubmit()
    }
  })
  onUnmounted(() => {
    lib.bus.busAIChat.reset()
    // handleClear()
  })

  const resultContentRef = ref(null)
  const scrollInterval = ref(null)

  const clear = () => {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }

  const autoScroll = () => {
    const el = resultContentRef.value
    if (el) {
      el.scrollTop = el.scrollHeight
      // 如果已经滚动到底部，清除定时器
      console.log(1111, el.scrollTop + el.clientHeight, el.scrollHeight)
      if (el.scrollTop + el.clientHeight >= el.scrollHeight) {
        clear()
      }
    }
  }
  watch(reply, () => {
    console.log('reply', reply)
    clear()
    // nextTick(() => {
    scrollInterval.value = setInterval(autoScroll, 300)
    // })
  })

  // 添加定时器，每隔100毫秒检查一次是否需要滚动
  onUnmounted(() => {
    clear()
  })

  // #region mqtt

  const PublicMqtt = ref(null)
  const startMqtt = () => {
    // val = 订阅地址
    // 设置订阅地址
    PublicMqtt.value = new CreateMqtt('screenAi')
    // 初始化mqtt
    PublicMqtt.value.init()
    // 链接mqtt
    PublicMqtt.value.link()
    // isSendToUe.value = false
    getMessage()
  }
  // 获取订阅数据
  let listening = false //正在监听
  const getMessage = () => {
    PublicMqtt.value.client.on('message', (topic, message) => {
      const str = message.toString()
      console.log(str, '返回的数据')
      if (str.includes('检测到唤醒词')) {
        handleCancel()
        listening = true
        showSender.value = true
        reply.value = '你好，我是钱江“通泰号”AI数字员工，有什么可以帮你的吗？'
      }
      if (listening) {
        if (str.includes('实时识别')) {
          inputValue.value = str.slice(str.indexOf('实时识别:') + 5)
        } else if (str.includes('最终句子:')) {
          inputValue.value = str.slice(str.indexOf('最终句子:') + 5)
          listening = false
          handleSubmit()
        }
      }
    })
  }
  // 取消MQTT订阅
  const unsubscribe = () => {
    // 如果页面并没有初始化MQTT，无需取消订阅
    if (PublicMqtt.value) {
      PublicMqtt.value.unsubscribes()
      PublicMqtt.value.over()
    }
  }

  onMounted(() => {
    startMqtt()
  })
  onUnmounted(() => {
    unsubscribe()
  })

  // #endregion
</script>

<style lang="scss">
  .ai-robot-container {
    width: 240px;
    height: 240px;
    .result-container {
      position: absolute;
      right: 200px;
      bottom: 200px;
      width: 475px;
      height: 223px;
      padding: 28px;
      font-size: 20px;
      line-height: 28px;
      color: #ffffff;
      background: rgb(6 84 174 / 30%);
      backdrop-filter: blur(6px);
      box-shadow: inset 0 0 6px 1px rgb(255 255 255 / 69%);
      transition: all 0.5s ease-in-out;
      .result-content {
        width: 100%;
        height: 100%;
        overflow-y: auto;

        // /* 隐藏滚动条 */
        // &::-webkit-scrollbar {
        //   display: none;
        //   width: 0;
        // }
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
          background-color: #999999;
        }
        &::-webkit-scrollbar-track {
          background-color: rgb(255 255 255 / 50%);
          border-radius: 10px;
          box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
        }
        &::-webkit-scrollbar-thumb {
          background-color: rgb(255 255 255 / 80%);
          border-radius: 10px;
          box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
        }
      }
      &.zhanKai {
        width: 1400px;
        height: 800px;
        font-size: 24px;
      }
    }
    .sender-container {
      @apply absolute bottom-20 -left-600 w600;

      background: rgb(255 255 255 / 50%);
      backdrop-filter: blur(10px);
      border: none;
      border: 1px solid #ffffff;
      border-radius: 20px;
      box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);
      .sender-style {
        .el-sender {
          border: none !important;
        }
        .el-textarea__inner::placeholder {
          color: #777777;
        }
        .el-textarea__inner {
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background-color: #999999;
          }
          &::-webkit-scrollbar-track {
            background-color: rgb(255 255 255 / 50%);
            border-radius: 10px;
            box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
          }
          &::-webkit-scrollbar-thumb {
            background-color: rgb(255 255 255 / 80%);
            border-radius: 10px;
            box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
          }
        }
      }
    }
    .tools-btns {
      display: flex;
      font-size: 20px;
      .selected {
        color: #0e1df0;
        border-color: #0e1df0;
      }
    }
  }
</style>
