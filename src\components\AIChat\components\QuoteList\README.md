# QuoteList 引用列表组件

## 功能描述

QuoteList 是一个用于显示AI对话中引用文档列表的组件。支持引用文档的展示、选择、详情预览等功能。

## 功能特性

- ✅ 引用文档列表展示
- ✅ 引用文档多选功能（最多5个）
- ✅ 引用文档悬停详情预览
- ✅ 引用文档分组和计数
- ✅ 引用文档相关度排序
- ✅ Markdown内容渲染
- ✅ 响应式设计
- ✅ 优雅的交互动画

## 组件接口

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| quoteList | `any[]` | `[]` | 引用列表数据 |

### 双向绑定

| 属性名 | 类型 | 说明 |
|--------|------|------|
| v-model:selected-files | `Array<{ id: string; name: string; url?: string }>` | 已选择的文件列表 |

### 引用数据格式

```typescript
interface QuoteItem {
  id?: string | number           // 引用ID
  document_id?: string           // 文档ID（备用）
  docId?: string                 // 文档ID（备用）
  segment_id?: string            // 片段ID（备用）
  fileName?: string              // 文件名
  document_name?: string         // 文档名（备用）
  title?: string                 // 标题（备用）
  url?: string                   // 文档链接
  content?: string               // 引用内容
  score?: number                 // 相关度分数（0-1）
  position?: number              // 位置编号
  word_count?: number            // 字数
  dataset_name?: string          // 数据集名称
}
```

## 使用示例

### 基础用法

```vue
<template>
  <QuoteList 
    :quote-list="quoteData"
    v-model:selected-files="selectedFiles" />
</template>

<script setup>
import { ref } from 'vue'
import QuoteList from '@/components/AIChat/components/QuoteList/index.vue'

const selectedFiles = ref([])
const quoteData = ref([
  {
    id: '1',
    fileName: '技术文档.pdf',
    content: '这是引用的内容...',
    score: 0.95,
    url: 'https://example.com/doc1.pdf',
    word_count: 256,
    dataset_name: '技术文档库'
  },
  {
    id: '2', 
    fileName: '用户手册.docx',
    content: '用户操作指南...',
    score: 0.87,
    url: 'https://example.com/doc2.docx',
    word_count: 412,
    dataset_name: '产品文档库'
  }
])
</script>
```

### 在AIChat中的用法

```vue
<template>
  <BubbleList>
    <template #footer="{ item }">
      <QuoteList 
        v-if="item?.quoteList?.length > 0"
        :quote-list="item.quoteList"
        v-model:selected-files="selectedFiles" />
    </template>
  </BubbleList>
</template>
```

## 组件行为

### 文档合并逻辑

- 相同文件名的引用会自动合并
- 显示合并后的引用计数
- 按最高相关度排序
- 原始引用按相关度降序排列

### 选择限制

- 最多同时选择5个文件
- 超出限制时禁用未选中项的复选框
- 已选中的文件可以随时取消选择

### 悬停预览

- 鼠标悬停显示详情弹窗
- 支持折叠展开多个引用片段
- 默认展开第一个引用片段
- 弹窗内容支持Markdown渲染

### 点击行为

- 点击文件名打开新窗口预览文档
- 点击复选框切换选择状态
- 弹窗内容可滚动查看

## 样式特性

### 视觉设计

- 现代化毛玻璃效果
- 渐变背景和阴影
- 悬停动画效果
- 响应式网格布局

### 响应式支持

- 桌面端：2列网格布局
- 移动端：1列布局
- 自适应字体和间距

### 主题色彩

- 主色调：`#667eea` 到 `#764ba2` 渐变
- 背景色：半透明白色毛玻璃
- 文本色：深灰色系统
- 交互色：蓝紫色渐变

## 目录结构

```
QuoteList/
├── index.vue              # 主组件文件
├── helper/
│   └── index.tsx          # 逻辑处理文件
└── README.md              # 使用文档
```

## 依赖说明

- `vue`: Vue3 框架
- `@element-plus/icons-vue`: Element Plus 图标
- `vue-element-plus-x`: XMarkdown 组件
- `element-plus`: ElPopover、ElCheckbox、ElCollapse 等组件

## 注意事项

1. **数据格式兼容**：组件支持多种字段名格式，自动识别文件名和ID
2. **性能优化**：大量引用时会自动合并相同文件，提升渲染性能
3. **URL处理**：确保传入的URL是有效的，否则点击时可能无法正常打开
4. **选择同步**：selectedFiles 的变更会实时反映到组件状态中
5. **样式隔离**：组件使用scoped样式，不会影响外部样式

## 更新历史

- **v1.0.0**: 初始版本，实现基础引用列表功能
- 从 AIChat 组件中提取封装，支持独立使用
- 使用 defineModel 实现双向绑定
- 完整的 TypeScript 类型支持 