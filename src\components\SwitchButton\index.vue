<template>
  <div class="switch-button-container">
    <!-- <div class="switch-button-item" :class="{ selected: item.selected }" v-for="(item, index) in list" :key="index" @click="handleClick(item)"> -->
    <!-- {{ item.name }}
    </div> -->

    <el-popover
      placement="bottom"
      trigger="hover"
      v-for="(item, index) in list"
      :key="index"
      width="250"
      popper-class="popper-sublist"
      effect="customized"
      :disabled="item.subList === undefined || item.subList.length == 0">
      <template #reference>
        <div class="switch-button-item" :class="{ selected: item.selected }" :style="{ width: switchItemWidth }" @click="handleClick(item)">
          {{ item.name }}
          <!-- <el-icon><QuestionFilled /></el-icon> -->
        </div>
      </template>
      <template v-if="item.subList && item.subList.length > 0">
        <div class="sub-list-container" @mouseenter="handleMouseEnter">
          <div
            class="sub-item"
            v-tooltip
            :class="{ selected: subItem.selected }"
            v-for="(subItem, subIndex) in item.subList"
            :key="subIndex"
            @click="handleSubitemClick(item, subItem)">
            {{ subItem.name }}
          </div>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script setup>
  const props = defineProps({
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '22px'
    },
    /**
     * @description: 按钮名称是否根据子标题选中改变
     */
    showSubTitleName: {
      type: Boolean,
      default: false
    },
    switchItemWidth: {
      type: String,
      default: 'auto'
    }
  })
  const emits = defineEmits(['handleMouseEnterPop'])
  /**
   * @description: 例子
  ```
  [
    { id: 1, name: '沉降', selected: true, subList: [] },
    {
      id: 2,
      name: '收敛',
      selected: false,
      subList: [
        { id: 1, name: 'aaa', selected: false },
        { id: 2, name: 'bbb', selected: false },
        { id: 3, name: 'ccc', selected: false }
      ]
    },
    { id: 3, name: '变形', selected: false },
    { id: 4, name: '渗漏水', selected: false }
  ]
   ```
   */
  const list = defineModel()
  const handleClick = (val) => {
    list.value.forEach((item) => {
      item.selected = item.id === val.id
    })
  }

  const handleSubitemClick = (selectedItem, val) => {
    selectedItem.subList.forEach((item) => {
      item.selected = item.id === val.id
    })
    if (props.showSubTitleName) {
      selectedItem.name = val.name
    }
  }
  const handleMouseEnter = () => {
    emits('handleMouseEnterPop')
  }
</script>

<style lang="scss" scoped>
  .switch-button-container {
    display: flex;
    justify-content: space-between;
    width: v-bind('props.width');
    height: v-bind('props.height');
    line-height: v-bind('props.height');
    .switch-button-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10px;
      font-family: 'Alibaba PuHuiTi-Regular', 'Alibaba PuHuiTi';
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      color: #8bb4d9;
      text-align: center;
      cursor: pointer;

      // background: rgb(0 163 255 / 40%);
      // border-radius: 5px;
      background: rgb(35 92 122 / 61%);
      border: 1px solid #63d2fc;
      border-radius: 7px;
      box-shadow: inset 0 0 12px 1px rgb(255 255 255 / 37.9%);
      &.selected {
        color: #ffffff;
        background: #004d84;
        border: 1px solid #00b2ff;
      }
    }
  }
</style>
<style>
  .el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 0;
    background: rgb(2 17 27 / 71%);
    border: 1px solid #0080b6;
  }
  .el-popper.is-customized .el-popper__arrow::before {
    right: 0;
    background: rgb(2 17 27 / 71%);
  }
  .popper-sublist {
    .sub-list-container {
      position: relative;
      width: 250px;
      height: 150px;
      overflow-y: scroll;
      scrollbar-width: none;
      .sub-item {
        box-sizing: border-box;
        width: 100%;
        height: 36px;
        padding: 0 16px;

        /* padding: 10px 16px; */
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-weight: 400;
        line-height: 36px;
        color: #ffffff;
        cursor: pointer;
        &.selected {
          font-size: 20px;
          color: #43ddff;
        }
      }
    }
  }
</style>
