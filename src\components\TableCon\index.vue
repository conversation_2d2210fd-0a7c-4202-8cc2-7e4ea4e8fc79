<template>
  <!-- 表格 -->
  <div class="table-con-container">
    <div class="table-title" ref="tableTitle">
      <span v-show="isShowIndex" style="width: 42px">序号</span>
      <span v-for="(item, index) in tableHeader" :key="index" :style="item.width ? { width: item.width } : 'flex:1'">
        {{ item.label }}
      </span>
    </div>
    <el-scrollbar :style="`height:${height}`" v-load="handleScroll" v-if="tableData.length > 0">
      <div class="table-body">
        <div
          class="body-row"
          :class="`${state.indexActive == index + 1 ? 'active' : ''}`"
          v-for="(item, index) in tableData"
          :key="index"
          @click="handleClick(item, index)">
          <span class="body-col" v-show="isShowIndex" :style="`width:${indexWidth}px`">{{ index + 1 }}</span>
          <span class="body-col" v-for="(itemCon, indexCon) in tableHeader" :key="indexCon" :style="`width:${itemCon.width ? itemCon.width : 'auto'}`">
            <el-tooltip v-if="itemCon.render" class="box-item" effect="dark" placement="top">
              <span v-if="itemCon.render">
                <ex-slot :render="itemCon.render" :row="item" :index="index" :column="itemCon" />
              </span>
              <template v-slot:content>
                <ex-slot :render="itemCon.render" :row="item" :index="index" :column="itemCon" />
              </template>
            </el-tooltip>
            <el-tooltip v-else class="box-item" effect="dark" :content="item[itemCon.prop] + ''" placement="top">
              <span>
                {{ item[itemCon.prop] ?? '-' }}
              </span>
            </el-tooltip>
          </span>
        </div>
        <slot></slot>
      </div>
    </el-scrollbar>
    <div class="no-data" :style="`height:${height}`" v-else>暂无数据</div>
  </div>
</template>

<script setup>
  import { h, nextTick, onMounted, reactive, ref } from 'vue'

  import { ElScrollbar, ElTooltip } from 'element-plus'
  const props = defineProps({
    maxNumber: {
      type: Number,
      default: 1
    },
    tableHeader: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '300px'
    },
    isShowIndex: false
  })
  const exSlot = (props, context) => {
    const params = {
      row: props.row,
      index: props.index
    }
    if (props.column) params.column = props.column
    return props.render(h, params)
  }
  exSlot.props = {
    row: Object,
    render: Function,
    index: Number,
    numIndex: [Number, String],
    column: {
      type: Object,
      default: null
    }
  }

  const vLoad = {
    mounted: (el, binding, vnode) => {
      const selectWrap = el.querySelector('.el-scrollbar__wrap')
      let timer
      selectWrap.addEventListener(
        'scroll',
        function () {
          const sign = 1
          const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
          if (scrollDistance <= sign) {
            if (timer) {
              clearTimeout(timer)
            }
            timer = setTimeout(function () {
              timer = undefined
              binding.value()
            }, 500)
          }
        },
        true
      )
    }
  }
  const indexWidth = ref(null)
  const emits = defineEmits(['handleClickRow', 'handleLoadMore'])
  const state = reactive({
    indexActive: null // 选中的选项
  })
  const tableTitle = ref(null)
  // methods

  const handleClick = (item, index) => {
    if (state.indexActive != index + 1) {
      state.indexActive = index + 1
    } else {
      state.indexActive = null
    }
    emits('handleClickRow', { row: item, index, isSelect: !!state.indexActive })
  }
  const handleScroll = () => {
    emits('handleLoadMore')
  }
  // mounted
  onMounted(() => {
    nextTick(() => {
      const newArr = [].slice.call(tableTitle.value.childNodes)
      indexWidth.value = newArr[0].getBoundingClientRect().width
      const newArrSlice = newArr.slice(2, newArr.length - 1)
      const newArrSliceWidth = newArrSlice.map((item) => item.getBoundingClientRect().width)
      props.tableHeader.map((item, index) => {
        item.width = newArrSliceWidth[index] + 'px'
      })
    })
  })
  defineExpose({
    state
  })
</script>

<style lang="scss" scoped>
  .table-con-container {
    width: 100%;
    background: #012740;
    border: 1px solid #014f75;
    font-family: Alibaba-PuHuiTi, Alibaba-PuHuiTi;
    font-weight: normal;
    padding: 4px 3px;
    .table-title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-size: 14px;
      color: #ffffff;
      line-height: 14px;
      span {
        padding: 3px 2px;
        background: #004668;
        text-align: center;
        margin-right: 2px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .table-body {
      .body-row {
        margin-top: 2px;
        display: flex;
        justify-content: space-between;
        cursor: pointer;
        background: #02324e;
        &.active {
          background: #005e95;
        }
        .body-col {
          padding: 3px 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          color: #abccdd;
          line-height: 14px;
        }
        // 加载更多
        > span {
          display: inline-block;
          flex-wrap: nowrap;
          font-size: 16px;
          color: #bfcfe1;
          text-align: center;
        }
      }
    }
    .no-data {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #ffffff;
    }
  }
  /* 滚动条样式 */
  :deep(.el-scrollbar__bar) {
    width: 2px; /* y轴滚动条宽度 */
    background-color: #02324e;
  }
  /* 滑块样式 */
  :deep(.el-scrollbar__thumb) {
    background-color: #006190;
    opacity: 1;
  }
</style>
