/*
 * @Description: 百度地图相关方法
 * @Autor: qian
 * @Date: 2023-06-19 17:53:37
 * @LastEditors: wang<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-04-30 11:21:00
 */
import { defineStore } from 'pinia'

const useScreenPageStore = defineStore('screenPage', {
  state: () => ({
    activePage: 'HomePage' // HomePage,Traffic,Monitor,Emergency,Inspect  首页|交通态势|健康监测|应急管理|巡检养护
  }),
  getters: {},
  actions: {
    // 当前激活的菜单
    setActivePage(activePage) {
      this.activePage = activePage
    }
  }
})

export default useScreenPageStore
