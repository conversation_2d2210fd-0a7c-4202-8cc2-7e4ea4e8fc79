<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-11 10:57:09
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-01 14:02:28
 * @FilePath: \bigscreen-qj-web\src\components\InfoCom\index.vue
 * @Description:
 *
-->
<template>
  <div class="infoCom-container">
    <!-- <div v-if="item" class="item">
      <div style="width: 72px">{{ item.name }}</div>
      <div class="value" v-tooltip>{{ item.value }}{{ item.unit }}</div>
    </div>
    <div v-else class="no-data">暂无数据</div> -->
    <div class="info-box" v-if="data.length > 0">
      <div class="name-block">
        <div
          v-for="(item, index) in data"
          :key="index"
          style="width: 170px !important; overflow: hidden; color: #dbefff; text-overflow: ellipsis; white-space: nowrap"
          v-tooltip>
          {{ item.name }}:
        </div>
      </div>
      <div class="value-block">
        <div v-for="(item, index) in data" :key="index" class="value-item">
          <div v-tooltip>{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="no-data" v-else>暂无数据</div>
  </div>
</template>

<script setup>
  import { vTooltip } from 'znyg-frontend-common'
  const props = defineProps({
    item: {
      type: Object,
      default: () => ({})
    },
    infoData: {
      type: Array,
      default: () => []
    }
  })

  const data = ref([])
  watch(
    () => props.infoData,
    (newVal) => {
      data.value =
        newVal?.map((item) => {
          return {
            name: item.name ?? '-',
            value: item.value ?? '-'
          }
        }) || []
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<style lang="scss" scoped>
  .infoCom-container {
    margin-top: 10px;
    .info-box {
      display: flex;
      flex-wrap: wrap;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      .name-block,
      .value-block {
        display: flex;
        flex: 1;
        flex-direction: column;
        gap: 10px;
        width: 203px;
        height: 332px;
        padding: 10px;
        margin-right: 10px;
        color: #5bb5ff;
        background: rgb(39 75 113 / 22%);
        border-radius: 13px;
        div {
          width: 100% !important;
        }
      }
      .value-block {
        flex: 2;
        margin-right: 0;
        color: #ffffff;
      }
      .value-item {
        height:23px;
      }
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      margin-bottom: 8px;

      // font-family: 'Alibaba PuHuiTi';
      // font-size: 16px;
      // font-weight: 400;
      // color: #dbefff;
      font-family: 'Source Han Sans CN', 'Source Han Sans CN';
      font-size: 15px;
      font-weight: bold;
      color: #bcddff;
    }
    .value {
      width: calc(100% - 172px);
      font-family: 'Source Han Sans CN', 'Source Han Sans CN';
      font-size: 15px;
      font-weight: 400;
      color: #2b93ff;
      text-align: left;
    }
    .no-data {
      width: 100%;
      height: 100%;
      font-size: 16px;
      font-weight: 400;
      line-height: 100%;
      color: #dbefff;
      text-align: center;
    }
  }
</style>
