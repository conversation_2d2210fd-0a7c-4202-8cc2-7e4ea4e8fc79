define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./RuntimeError-d0e509ca"],(function(e,t,n,a,r){"use strict";function i(e,n,a,r,i,u,o,s,l){this[0]=t.defaultValue(e,0),this[1]=t.defaultValue(r,0),this[2]=t.defaultValue(o,0),this[3]=t.defaultValue(n,0),this[4]=t.defaultValue(i,0),this[5]=t.defaultValue(s,0),this[6]=t.defaultValue(a,0),this[7]=t.defaultValue(u,0),this[8]=t.defaultValue(l,0)}i.packedLength=9,i.pack=function(e,n,a){return a=t.defaultValue(a,0),n[a++]=e[0],n[a++]=e[1],n[a++]=e[2],n[a++]=e[3],n[a++]=e[4],n[a++]=e[5],n[a++]=e[6],n[a++]=e[7],n[a++]=e[8],n},i.unpack=function(e,n,a){return n=t.defaultValue(n,0),t.defined(a)||(a=new i),a[0]=e[n++],a[1]=e[n++],a[2]=e[n++],a[3]=e[n++],a[4]=e[n++],a[5]=e[n++],a[6]=e[n++],a[7]=e[n++],a[8]=e[n++],a},i.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n):new i(e[0],e[3],e[6],e[1],e[4],e[7],e[2],e[5],e[8])},i.fromArray=function(e,n,a){return n=t.defaultValue(n,0),t.defined(a)||(a=new i),a[0]=e[n],a[1]=e[n+1],a[2]=e[n+2],a[3]=e[n+3],a[4]=e[n+4],a[5]=e[n+5],a[6]=e[n+6],a[7]=e[n+7],a[8]=e[n+8],a},i.fromColumnMajorArray=function(e,t){return i.clone(e,t)},i.fromRowMajorArray=function(e,n){return t.defined(n)?(n[0]=e[0],n[1]=e[3],n[2]=e[6],n[3]=e[1],n[4]=e[4],n[5]=e[7],n[6]=e[2],n[7]=e[5],n[8]=e[8],n):new i(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},i.fromQuaternion=function(e,n){var a=e.x*e.x,r=e.x*e.y,u=e.x*e.z,o=e.x*e.w,s=e.y*e.y,l=e.y*e.z,c=e.y*e.w,f=e.z*e.z,h=e.z*e.w,y=e.w*e.w,m=a-s-f+y,d=2*(r-h),C=2*(u+c),p=2*(r+h),x=-a+s-f+y,w=2*(l-o),z=2*(u-c),O=2*(l+o),b=-a-s+f+y;return t.defined(n)?(n[0]=m,n[1]=p,n[2]=z,n[3]=d,n[4]=x,n[5]=O,n[6]=C,n[7]=w,n[8]=b,n):new i(m,d,C,p,x,w,z,O,b)},i.fromHeadingPitchRoll=function(e,n){var a=Math.cos(-e.pitch),r=Math.cos(-e.heading),u=Math.cos(e.roll),o=Math.sin(-e.pitch),s=Math.sin(-e.heading),l=Math.sin(e.roll),c=a*r,f=-u*s+l*o*r,h=l*s+u*o*r,y=a*s,m=u*r+l*o*s,d=-l*r+u*o*s,C=-o,p=l*a,x=u*a;return t.defined(n)?(n[0]=c,n[1]=y,n[2]=C,n[3]=f,n[4]=m,n[5]=p,n[6]=h,n[7]=d,n[8]=x,n):new i(c,f,h,y,m,d,C,p,x)},i.fromScale=function(e,n){return t.defined(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=e.y,n[5]=0,n[6]=0,n[7]=0,n[8]=e.z,n):new i(e.x,0,0,0,e.y,0,0,0,e.z)},i.fromUniformScale=function(e,n){return t.defined(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=e,n[5]=0,n[6]=0,n[7]=0,n[8]=e,n):new i(e,0,0,0,e,0,0,0,e)},i.fromCrossProduct=function(e,n){return t.defined(n)?(n[0]=0,n[1]=e.z,n[2]=-e.y,n[3]=-e.z,n[4]=0,n[5]=e.x,n[6]=e.y,n[7]=-e.x,n[8]=0,n):new i(0,-e.z,e.y,e.z,0,-e.x,-e.y,e.x,0)},i.fromRotationX=function(e,n){var a=Math.cos(e),r=Math.sin(e);return t.defined(n)?(n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=a,n[5]=r,n[6]=0,n[7]=-r,n[8]=a,n):new i(1,0,0,0,a,-r,0,r,a)},i.fromRotationY=function(e,n){var a=Math.cos(e),r=Math.sin(e);return t.defined(n)?(n[0]=a,n[1]=0,n[2]=-r,n[3]=0,n[4]=1,n[5]=0,n[6]=r,n[7]=0,n[8]=a,n):new i(a,0,r,0,1,0,-r,0,a)},i.fromRotationZ=function(e,n){var a=Math.cos(e),r=Math.sin(e);return t.defined(n)?(n[0]=a,n[1]=r,n[2]=0,n[3]=-r,n[4]=a,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new i(a,-r,0,r,a,0,0,0,1)},i.toArray=function(e,n){return t.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},i.getElementIndex=function(e,t){return n.Check.typeOf.number.greaterThanOrEquals("row",t,0),n.Check.typeOf.number.lessThanOrEquals("row",t,2),n.Check.typeOf.number.greaterThanOrEquals("column",e,0),n.Check.typeOf.number.lessThanOrEquals("column",e,2),3*e+t},i.getColumn=function(e,t,a){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,2),n.Check.typeOf.object("result",a);var r=3*t,i=e[r],u=e[r+1],o=e[r+2];return a.x=i,a.y=u,a.z=o,a},i.setColumn=function(e,t,a,r){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,2),n.Check.typeOf.object("cartesian",a),n.Check.typeOf.object("result",r),r=i.clone(e,r);var u=3*t;return r[u]=a.x,r[u+1]=a.y,r[u+2]=a.z,r},i.getRow=function(e,t,a){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,2),n.Check.typeOf.object("result",a);var r=e[t],i=e[t+3],u=e[t+6];return a.x=r,a.y=i,a.z=u,a},i.setRow=function(e,t,a,r){return n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,2),n.Check.typeOf.object("cartesian",a),n.Check.typeOf.object("result",r),r=i.clone(e,r),r[t]=a.x,r[t+3]=a.y,r[t+6]=a.z,r};var u=new a.Cartesian3;i.getScale=function(e,t){return t.x=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[0],e[1],e[2],u)),t.y=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[3],e[4],e[5],u)),t.z=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[6],e[7],e[8],u)),t};var o=new a.Cartesian3;i.getMaximumScale=function(e){return i.getScale(e,o),a.Cartesian3.maximumComponent(o)},i.multiply=function(e,t,n){var a=e[0]*t[0]+e[3]*t[1]+e[6]*t[2],r=e[1]*t[0]+e[4]*t[1]+e[7]*t[2],i=e[2]*t[0]+e[5]*t[1]+e[8]*t[2],u=e[0]*t[3]+e[3]*t[4]+e[6]*t[5],o=e[1]*t[3]+e[4]*t[4]+e[7]*t[5],s=e[2]*t[3]+e[5]*t[4]+e[8]*t[5],l=e[0]*t[6]+e[3]*t[7]+e[6]*t[8],c=e[1]*t[6]+e[4]*t[7]+e[7]*t[8],f=e[2]*t[6]+e[5]*t[7]+e[8]*t[8];return n[0]=a,n[1]=r,n[2]=i,n[3]=u,n[4]=o,n[5]=s,n[6]=l,n[7]=c,n[8]=f,n},i.add=function(e,t,n){return n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n[4]=e[4]+t[4],n[5]=e[5]+t[5],n[6]=e[6]+t[6],n[7]=e[7]+t[7],n[8]=e[8]+t[8],n},i.subtract=function(e,t,n){return n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n[4]=e[4]-t[4],n[5]=e[5]-t[5],n[6]=e[6]-t[6],n[7]=e[7]-t[7],n[8]=e[8]-t[8],n},i.multiplyByVector=function(e,t,n){var a=t.x,r=t.y,i=t.z,u=e[0]*a+e[3]*r+e[6]*i,o=e[1]*a+e[4]*r+e[7]*i,s=e[2]*a+e[5]*r+e[8]*i;return n.x=u,n.y=o,n.z=s,n},i.multiplyByScalar=function(e,t,n){return n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n[4]=e[4]*t,n[5]=e[5]*t,n[6]=e[6]*t,n[7]=e[7]*t,n[8]=e[8]*t,n},i.multiplyByScale=function(e,t,n){return n[0]=e[0]*t.x,n[1]=e[1]*t.x,n[2]=e[2]*t.x,n[3]=e[3]*t.y,n[4]=e[4]*t.y,n[5]=e[5]*t.y,n[6]=e[6]*t.z,n[7]=e[7]*t.z,n[8]=e[8]*t.z,n},i.negate=function(e,t){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t[4]=-e[4],t[5]=-e[5],t[6]=-e[6],t[7]=-e[7],t[8]=-e[8],t},i.transpose=function(e,t){var n=e[0],a=e[3],r=e[6],i=e[1],u=e[4],o=e[7],s=e[2],l=e[5],c=e[8];return t[0]=n,t[1]=a,t[2]=r,t[3]=i,t[4]=u,t[5]=o,t[6]=s,t[7]=l,t[8]=c,t};var s=new a.Cartesian3(1,1,1);function l(e){for(var t=0,n=0;n<9;++n){var a=e[n];t+=a*a}return Math.sqrt(t)}i.getRotation=function(e,t){var n=a.Cartesian3.divideComponents(s,i.getScale(e,o),o);return t=i.multiplyByScale(e,n,t),t};var c=[1,0,0],f=[2,2,1];function h(e){for(var t=0,n=0;n<3;++n){var a=e[i.getElementIndex(f[n],c[n])];t+=2*a*a}return Math.sqrt(t)}function y(e,t){for(var n=a.CesiumMath.EPSILON15,r=0,u=1,o=0;o<3;++o){var s=Math.abs(e[i.getElementIndex(f[o],c[o])]);s>r&&(u=o,r=s)}var l=1,h=0,y=c[u],m=f[u];if(Math.abs(e[i.getElementIndex(m,y)])>n){var d,C=e[i.getElementIndex(m,m)],p=e[i.getElementIndex(y,y)],x=e[i.getElementIndex(m,y)],w=(C-p)/2/x;d=w<0?-1/(-w+Math.sqrt(1+w*w)):1/(w+Math.sqrt(1+w*w)),l=1/Math.sqrt(1+d*d),h=d*l}return t=i.clone(i.IDENTITY,t),t[i.getElementIndex(y,y)]=t[i.getElementIndex(m,m)]=l,t[i.getElementIndex(m,y)]=h,t[i.getElementIndex(y,m)]=-h,t}var m=new i,d=new i;function C(e,n,a,r){this.x=t.defaultValue(e,0),this.y=t.defaultValue(n,0),this.z=t.defaultValue(a,0),this.w=t.defaultValue(r,0)}i.computeEigenDecomposition=function(e,n){var r=a.CesiumMath.EPSILON20,u=10,o=0,s=0;t.defined(n)||(n={});var c=n.unitary=i.clone(i.IDENTITY,n.unitary),f=n.diagonal=i.clone(e,n.diagonal),C=r*l(f);while(s<u&&h(f)>C)y(f,m),i.transpose(m,d),i.multiply(f,m,f),i.multiply(d,f,f),i.multiply(c,m,c),++o>2&&(++s,o=0);return n},i.fromNormal=function(e){let t=a.Cartesian3.clone(a.Cartesian3.UNIT_X,new a.Cartesian3);Math.abs(a.Cartesian3.dot(e,a.Cartesian3.UNIT_Z))<.1&&a.Cartesian3.clone(a.Cartesian3.UNIT_Z,t);let n=e,r=a.Cartesian3.cross(n,t,new a.Cartesian3);return a.Cartesian3.cross(r,n,t),a.Cartesian3.normalize(r,r),a.Cartesian3.normalize(t,t),new i(t.x,r.x,n.x,t.y,r.y,n.y,t.z,r.z,n.z)},i.abs=function(e,t){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t[4]=Math.abs(e[4]),t[5]=Math.abs(e[5]),t[6]=Math.abs(e[6]),t[7]=Math.abs(e[7]),t[8]=Math.abs(e[8]),t},i.determinant=function(e){var t=e[0],n=e[3],a=e[6],r=e[1],i=e[4],u=e[7],o=e[2],s=e[5],l=e[8];return t*(i*l-s*u)+r*(s*a-n*l)+o*(n*u-i*a)},i.inverse=function(e,t){var r=e[0],u=e[1],o=e[2],s=e[3],l=e[4],c=e[5],f=e[6],h=e[7],y=e[8],m=i.determinant(e);if(Math.abs(m)<=a.CesiumMath.EPSILON15)throw new n.DeveloperError("matrix is not invertible");t[0]=l*y-h*c,t[1]=h*o-u*y,t[2]=u*c-l*o,t[3]=f*c-s*y,t[4]=r*y-f*o,t[5]=s*o-r*c,t[6]=s*h-f*l,t[7]=f*u-r*h,t[8]=r*l-s*u;var d=1/m;return i.multiplyByScalar(t,d,t)},i.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[7]===n[7]&&e[8]===n[8]},i.equalsEpsilon=function(e,n,a){return a=t.defaultValue(a,0),e===n||t.defined(e)&&t.defined(n)&&Math.abs(e[0]-n[0])<=a&&Math.abs(e[1]-n[1])<=a&&Math.abs(e[2]-n[2])<=a&&Math.abs(e[3]-n[3])<=a&&Math.abs(e[4]-n[4])<=a&&Math.abs(e[5]-n[5])<=a&&Math.abs(e[6]-n[6])<=a&&Math.abs(e[7]-n[7])<=a&&Math.abs(e[8]-n[8])<=a},i.IDENTITY=Object.freeze(new i(1,0,0,0,1,0,0,0,1)),i.ZERO=Object.freeze(new i(0,0,0,0,0,0,0,0,0)),i.COLUMN0ROW0=0,i.COLUMN0ROW1=1,i.COLUMN0ROW2=2,i.COLUMN1ROW0=3,i.COLUMN1ROW1=4,i.COLUMN1ROW2=5,i.COLUMN2ROW0=6,i.COLUMN2ROW1=7,i.COLUMN2ROW2=8,Object.defineProperties(i.prototype,{length:{get:function(){return i.packedLength}}}),i.prototype.clone=function(e){return i.clone(this,e)},i.prototype.equals=function(e){return i.equals(this,e)},i.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]&&e[4]===t[n+4]&&e[5]===t[n+5]&&e[6]===t[n+6]&&e[7]===t[n+7]&&e[8]===t[n+8]},i.prototype.equalsEpsilon=function(e,t){return i.equalsEpsilon(this,e,t)},i.prototype.toString=function(){return"("+this[0]+", "+this[3]+", "+this[6]+")\n("+this[1]+", "+this[4]+", "+this[7]+")\n("+this[2]+", "+this[5]+", "+this[8]+")"},C.fromElements=function(e,n,a,r,i){return t.defined(i)?(i.x=e,i.y=n,i.z=a,i.w=r,i):new C(e,n,a,r)},C.fromColor=function(e,n){return t.defined(n)?(n.x=e.red,n.y=e.green,n.z=e.blue,n.w=e.alpha,n):new C(e.red,e.green,e.blue,e.alpha)},C.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new C(e.x,e.y,e.z,e.w)},C.packedLength=4,C.pack=function(e,n,a){return a=t.defaultValue(a,0),n[a++]=e.x,n[a++]=e.y,n[a++]=e.z,n[a]=t.defaultValue(e.w,0),n},C.unpack=function(e,n,a){return n=t.defaultValue(n,0),t.defined(a)||(a=new C),a.x=e[n++],a.y=e[n++],a.z=e[n++],a.w=e[n],a},C.packArray=function(e,a){var r=e.length,i=4*r;if(t.defined(a)){if(!Array.isArray(a)&&a.length!==i)throw new n.DeveloperError("If result is a typed array, it must have exactly array.length * 4 elements");a.length!==i&&(a.length=i)}else a=new Array(i);for(var u=0;u<r;++u)C.pack(e[u],a,4*u);return a},C.unpackArray=function(e,a){if(n.Check.defined("array",e),n.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new n.DeveloperError("array length must be a multiple of 4.");var r=e.length;t.defined(a)?a.length=r/4:a=new Array(r/4);for(var i=0;i<r;i+=4){var u=i/4;a[u]=C.unpack(e,i,a[u])}return a},C.fromArray=C.unpack,C.maximumComponent=function(e){return Math.max(e.x,e.y,e.z,e.w)},C.minimumComponent=function(e){return Math.min(e.x,e.y,e.z,e.w)},C.minimumByComponent=function(e,t,n){return n.x=Math.min(e.x,t.x),n.y=Math.min(e.y,t.y),n.z=Math.min(e.z,t.z),n.w=Math.min(e.w,t.w),n},C.maximumByComponent=function(e,t,n){return n.x=Math.max(e.x,t.x),n.y=Math.max(e.y,t.y),n.z=Math.max(e.z,t.z),n.w=Math.max(e.w,t.w),n},C.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},C.magnitude=function(e){return Math.sqrt(C.magnitudeSquared(e))};var p=new C;C.distance=function(e,t){return C.subtract(e,t,p),C.magnitude(p)},C.distanceSquared=function(e,t){return C.subtract(e,t,p),C.magnitudeSquared(p)},C.normalize=function(e,t){var a=C.magnitude(e);if(t.x=e.x/a,t.y=e.y/a,t.z=e.z/a,t.w=e.w/a,isNaN(t.x)||isNaN(t.y)||isNaN(t.z)||isNaN(t.w))throw new n.DeveloperError("normalized result is not a number");return t},C.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},C.multiplyComponents=function(e,t,n){return n.x=e.x*t.x,n.y=e.y*t.y,n.z=e.z*t.z,n.w=e.w*t.w,n},C.divideComponents=function(e,t,n){return n.x=e.x/t.x,n.y=e.y/t.y,n.z=e.z/t.z,n.w=e.w/t.w,n},C.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},C.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},C.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},C.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},C.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},C.abs=function(e,t){return t.x=Math.abs(e.x),t.y=Math.abs(e.y),t.z=Math.abs(e.z),t.w=Math.abs(e.w),t};var x=new C;C.lerp=function(e,t,n,a){return C.multiplyByScalar(t,n,x),a=C.multiplyByScalar(e,1-n,a),C.add(x,a,a)};var w=new C;C.mostOrthogonalAxis=function(e,t){var n=C.normalize(e,w);return C.abs(n,n),t=n.x<=n.y?n.x<=n.z?n.x<=n.w?C.clone(C.UNIT_X,t):C.clone(C.UNIT_W,t):n.z<=n.w?C.clone(C.UNIT_Z,t):C.clone(C.UNIT_W,t):n.y<=n.z?n.y<=n.w?C.clone(C.UNIT_Y,t):C.clone(C.UNIT_W,t):n.z<=n.w?C.clone(C.UNIT_Z,t):C.clone(C.UNIT_W,t),t},C.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w},C.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]&&e.w===t[n+3]},C.equalsEpsilon=function(e,n,r,i){return e===n||t.defined(e)&&t.defined(n)&&a.CesiumMath.equalsEpsilon(e.x,n.x,r,i)&&a.CesiumMath.equalsEpsilon(e.y,n.y,r,i)&&a.CesiumMath.equalsEpsilon(e.z,n.z,r,i)&&a.CesiumMath.equalsEpsilon(e.w,n.w,r,i)},C.ZERO=Object.freeze(new C(0,0,0,0)),C.UNIT_X=Object.freeze(new C(1,0,0,0)),C.UNIT_Y=Object.freeze(new C(0,1,0,0)),C.UNIT_Z=Object.freeze(new C(0,0,1,0)),C.UNIT_W=Object.freeze(new C(0,0,0,1)),C.prototype.clone=function(e){return C.clone(this,e)},C.prototype.equals=function(e){return C.equals(this,e)},C.prototype.equalsEpsilon=function(e,t,n){return C.equalsEpsilon(this,e,t,n)},C.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var z=new Float32Array(1),O=256,b=65536,M=16777216,g=1/O,v=1/b,E=1/M,T=38;function k(e,n,a,r,i,u,o,s,l,c,f,h,y,m,d,C){this[0]=t.defaultValue(e,1),this[1]=t.defaultValue(i,0),this[2]=t.defaultValue(l,0),this[3]=t.defaultValue(y,0),this[4]=t.defaultValue(n,0),this[5]=t.defaultValue(u,1),this[6]=t.defaultValue(c,0),this[7]=t.defaultValue(m,0),this[8]=t.defaultValue(a,0),this[9]=t.defaultValue(o,0),this[10]=t.defaultValue(f,1),this[11]=t.defaultValue(d,0),this[12]=t.defaultValue(r,0),this[13]=t.defaultValue(s,0),this[14]=t.defaultValue(h,0),this[15]=t.defaultValue(C,1)}C.packFloat=function(e,n){if(t.defined(n)||(n=new C),z[0]=e,e=z[0],0===e)return C.clone(C.ZERO,n);var r,i=e<0?1:0;isFinite(e)?(e=Math.abs(e),r=Math.floor(a.CesiumMath.logBase(e,10))+1,e/=Math.pow(10,r)):(e=.1,r=T);var u=e*O;return n.x=Math.floor(u),u=(u-n.x)*O,n.y=Math.floor(u),u=(u-n.y)*O,n.z=Math.floor(u),n.w=2*(r+T)+i,n},C.unpackFloat=function(e){var t=e.w/2,n=Math.floor(t),a=2*(t-n);if(n-=T,a=2*a-1,a=-a,n>=T)return a<0?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY;var r=a*e.x*g;return r+=a*e.y*v,r+=a*e.z*E,r*Math.pow(10,n)},k.packedLength=16,k.pack=function(e,n,a){return a=t.defaultValue(a,0),n[a++]=e[0],n[a++]=e[1],n[a++]=e[2],n[a++]=e[3],n[a++]=e[4],n[a++]=e[5],n[a++]=e[6],n[a++]=e[7],n[a++]=e[8],n[a++]=e[9],n[a++]=e[10],n[a++]=e[11],n[a++]=e[12],n[a++]=e[13],n[a++]=e[14],n[a]=e[15],n},k.getProjectionMatrixByPositions=function(e,t,r){if(e.length<3||t.length<3)throw new n.DeveloperError("worldPositions\u548ccullingPositions\u7684\u6570\u7ec4\u957f\u5ea6\u4e0d\u80fd\u5c0f\u4e8e3");let u=a.Cartesian3.distance(e[0],e[1]),o=a.Cartesian2.distance(t[0],t[1]),s=a.Cartesian3.getProjectPtOnLine(e[2],e[0],e[1],new a.Cartesian3),l=a.Cartesian2.getProjectPtOnLine(t[2],t[0],t[1],new a.Cartesian2),c=a.Cartesian3.distance(e[2],s),f=a.Cartesian2.distance(t[2],l),h=new a.Cartesian2(o/u,f/c),y=a.Cartesian2.subtract(t[1],t[0],new a.Cartesian3),m=a.Cartesian2.subtract(t[2],t[0],new a.Cartesian3),d=a.Cartesian3.cross(y,m,new a.Cartesian3),C=!0;d.z>-1e-5&&(C=!1);let p=a.Cartesian3.subtract(e[1],e[0],new a.Cartesian3),x=a.Cartesian3.subtract(e[2],e[0],new a.Cartesian3);a.Cartesian3.cross(p,x,d),C&&a.Cartesian3.negate(d,d),a.Cartesian3.normalize(d,d);let w=i.fromNormal(d),z=i.inverse(w,new i);i.multiplyByVector(z,p,p),a.Cartesian2.normalize(p,p),a.Cartesian2.normalize(y,y);let O=Math.acos(a.Cartesian2.dot(y,p));a.Cartesian3.cross(p,y,new a.Cartesian3).z>0&&(O=-O);let b=i.fromRotationZ(O);i.multiply(w,b,w);let M=a.Cartesian2.divideComponents(t[0],h,new a.Cartesian2),g=new a.Cartesian3(M.x,M.y,0);i.multiplyByVector(w,g,g),a.Cartesian3.subtract(e[0],g,g);let v=i.getColumn(w,2,new a.Cartesian3);a.Cartesian3.multiplyByScalar(v,r,v),a.Cartesian3.add(g,v,g);let E=k.fromRotationTranslation(w,g);return E},k.getProjectionMatrixByPictureNormalRightAndPosition=function(e,t,n,r,u,o){let s=a.Cartesian3.cross(e,t,new a.Cartesian3);t=a.Cartesian3.cross(s,e,new a.Cartesian3),e=a.Cartesian3.normalize(e,new a.Cartesian3),a.Cartesian3.normalize(s,s),a.Cartesian3.normalize(e,e);let l=new i(t.x,s.x,e.x,t.y,s.y,e.y,t.z,s.z,e.z),c=new a.Cartesian3(2/n.x,2/n.y,0),f=a.Cartesian2.divideComponents(u,c,new a.Cartesian3);i.multiplyByVector(l,f,f);let h=a.Cartesian3.subtract(r,f,f),y=i.getColumn(l,2,new a.Cartesian3);a.Cartesian3.multiplyByScalar(y,o,y),a.Cartesian3.add(h,y,h);let m=new k(t.x,s.x,e.x,h.x,t.y,s.y,e.y,h.y,t.z,s.z,e.z,h.z,0,0,0,1);return m},k.unpack=function(e,n,a){return n=t.defaultValue(n,0),t.defined(a)||(a=new k),a[0]=e[n++],a[1]=e[n++],a[2]=e[n++],a[3]=e[n++],a[4]=e[n++],a[5]=e[n++],a[6]=e[n++],a[7]=e[n++],a[8]=e[n++],a[9]=e[n++],a[10]=e[n++],a[11]=e[n++],a[12]=e[n++],a[13]=e[n++],a[14]=e[n++],a[15]=e[n],a},k.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):new k(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])},k.fromArray=k.unpack,k.fromColumnMajorArray=function(e,t){return k.clone(e,t)},k.fromRowMajorArray=function(e,n){return t.defined(n)?(n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15],n):new k(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},k.fromRotationTranslation=function(e,n,r){return n=t.defaultValue(n,a.Cartesian3.ZERO),t.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=0,r[4]=e[3],r[5]=e[4],r[6]=e[5],r[7]=0,r[8]=e[6],r[9]=e[7],r[10]=e[8],r[11]=0,r[12]=n.x,r[13]=n.y,r[14]=n.z,r[15]=1,r):new k(e[0],e[3],e[6],n.x,e[1],e[4],e[7],n.y,e[2],e[5],e[8],n.z,0,0,0,1)},k.fromTranslationQuaternionRotationScale=function(e,n,a,r){t.defined(r)||(r=new k);var i=a.x,u=a.y,o=a.z,s=n.x*n.x,l=n.x*n.y,c=n.x*n.z,f=n.x*n.w,h=n.y*n.y,y=n.y*n.z,m=n.y*n.w,d=n.z*n.z,C=n.z*n.w,p=n.w*n.w,x=s-h-d+p,w=2*(l-C),z=2*(c+m),O=2*(l+C),b=-s+h-d+p,M=2*(y-f),g=2*(c-m),v=2*(y+f),E=-s-h+d+p;return r[0]=x*i,r[1]=O*i,r[2]=g*i,r[3]=0,r[4]=w*u,r[5]=b*u,r[6]=v*u,r[7]=0,r[8]=z*o,r[9]=M*o,r[10]=E*o,r[11]=0,r[12]=e.x,r[13]=e.y,r[14]=e.z,r[15]=1,r},k.fromTranslationRotationScale=function(e,t){return k.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,t)},k.fromTranslation=function(e,t){return k.fromRotationTranslation(i.IDENTITY,e,t)},k.fromScale=function(e,n){return t.defined(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e.y,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new k(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)},k.fromUniformScale=function(e,n){return t.defined(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new k(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};var N=new a.Cartesian3,q=new a.Cartesian3,V=new a.Cartesian3,R=new a.Cartesian3;k.fromCamera=function(e,t){var n=e.position,a=e.direction,r=e.up;return k.fromCameraDetail(n,a,r,t)},k.fromCoordSysTransform=function(e,t,n,r){return a.Cartesian3.cross(t,n,R),k.fromCameraDetail(e,n,R,r)},k.fromCameraDetail=function(e,n,r,i){a.Cartesian3.normalize(n,N),a.Cartesian3.normalize(a.Cartesian3.cross(N,r,q),q),a.Cartesian3.normalize(a.Cartesian3.cross(q,N,V),V);var u=q.x,o=q.y,s=q.z,l=N.x,c=N.y,f=N.z,h=V.x,y=V.y,m=V.z,d=e.x,C=e.y,p=e.z,x=u*-d+o*-C+s*-p,w=h*-d+y*-C+m*-p,z=l*d+c*C+f*p;return t.defined(i)?(i[0]=u,i[1]=h,i[2]=-l,i[3]=0,i[4]=o,i[5]=y,i[6]=-c,i[7]=0,i[8]=s,i[9]=m,i[10]=-f,i[11]=0,i[12]=x,i[13]=w,i[14]=z,i[15]=1,i):new k(u,o,s,x,h,y,m,w,-l,-c,-f,z,0,0,0,1)},k.computePerspectiveFieldOfView=function(e,t,n,a,r){var i=Math.tan(.5*e),u=1/i,o=u/t,s=(a+n)/(n-a),l=2*a*n/(n-a);return r[0]=o,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=s,r[11]=-1,r[12]=0,r[13]=0,r[14]=l,r[15]=0,r},k.computeOrthographicOffCenter=function(e,t,n,a,r,i,u){var o=1/(t-e),s=1/(a-n),l=1/(i-r),c=-(t+e)*o,f=-(a+n)*s,h=-(i+r)*l;return o*=2,s*=2,l*=-2,u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=l,u[11]=0,u[12]=c,u[13]=f,u[14]=h,u[15]=1,u},k.computePerspectiveOffCenter=function(e,t,n,a,r,i,u){var o=2*r/(t-e),s=2*r/(a-n),l=(t+e)/(t-e),c=(a+n)/(a-n),f=-(i+r)/(i-r),h=-1,y=-2*i*r/(i-r);return u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=l,u[9]=c,u[10]=f,u[11]=h,u[12]=0,u[13]=0,u[14]=y,u[15]=0,u},k.computeInfinitePerspectiveOffCenter=function(e,t,n,a,r,i){var u=2*r/(t-e),o=2*r/(a-n),s=(t+e)/(t-e),l=(a+n)/(a-n),c=-1,f=-1,h=-2*r;return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=o,i[6]=0,i[7]=0,i[8]=s,i[9]=l,i[10]=c,i[11]=f,i[12]=0,i[13]=0,i[14]=h,i[15]=0,i},k.computeViewportTransformation=function(e,n,a,r){r||(r=new k),e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var i=t.defaultValue(e.x,0),u=t.defaultValue(e.y,0),o=t.defaultValue(e.width,0),s=t.defaultValue(e.height,0);n=t.defaultValue(n,0),a=t.defaultValue(a,1);var l=.5*o,c=.5*s,f=.5*(a-n),h=l,y=c,m=f,d=i+l,C=u+c,p=n+f,x=1;return r[0]=h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=y,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=m,r[11]=0,r[12]=d,r[13]=C,r[14]=p,r[15]=x,r},k.computeView=function(e,t,n,r,i){return i[0]=r.x,i[1]=n.x,i[2]=-t.x,i[3]=0,i[4]=r.y,i[5]=n.y,i[6]=-t.y,i[7]=0,i[8]=r.z,i[9]=n.z,i[10]=-t.z,i[11]=0,i[12]=-a.Cartesian3.dot(r,e),i[13]=-a.Cartesian3.dot(n,e),i[14]=a.Cartesian3.dot(t,e),i[15]=1,i},k.toArray=function(e,n){return t.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]},k.getElementIndex=function(e,t){return n.Check.typeOf.number.greaterThanOrEquals("row",t,0),n.Check.typeOf.number.lessThanOrEquals("row",t,3),n.Check.typeOf.number.greaterThanOrEquals("column",e,0),n.Check.typeOf.number.lessThanOrEquals("column",e,3),4*e+t},k.getColumn=function(e,t,a){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,3),n.Check.typeOf.object("result",a);var r=4*t,i=e[r],u=e[r+1],o=e[r+2],s=e[r+3];return a.x=i,a.y=u,a.z=o,a.w=s,a},k.setColumn=function(e,t,a,r){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,3),n.Check.typeOf.object("cartesian",a),n.Check.typeOf.object("result",r),r=k.clone(e,r);var i=4*t;return r[i]=a.x,r[i+1]=a.y,r[i+2]=a.z,r[i+3]=a.w,r},k.setTranslation=function(e,t,n){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=t.x,n[13]=t.y,n[14]=t.z,n[15]=e[15],n},k.addTransform=function(e,t,n){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12]+t.x,n[13]=e[13]+t.y,n[14]=e[14]+t.z,n[15]=e[15],n},k.subTransform=function(e,t,n){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12]-t.x,n[13]=e[13]-t.y,n[14]=e[14]-t.z,n[15]=e[15],n};var I=new a.Cartesian3;k.setScale=function(e,t,n){var r=k.getScale(e,I),i=a.Cartesian3.divideComponents(t,r,I);return k.multiplyByScale(e,i,n)},k.fromRotation=function(e,a){return n.Check.typeOf.object("rotation",e),t.defined(a)||(a=new k),a[0]=e[0],a[1]=e[1],a[2]=e[2],a[3]=0,a[4]=e[3],a[5]=e[4],a[6]=e[5],a[7]=0,a[8]=e[6],a[9]=e[7],a[10]=e[8],a[11]=0,a[12]=0,a[13]=0,a[14]=0,a[15]=1,a};const S=new a.Cartesian3;k.setRotation=function(e,t,a){n.Check.typeOf.object("matrix",e),n.Check.typeOf.object("result",a);const r=k.getScale(e,S);return a[0]=t[0]*r.x,a[1]=t[1]*r.x,a[2]=t[2]*r.x,a[3]=e[3],a[4]=t[3]*r.y,a[5]=t[4]*r.y,a[6]=t[5]*r.y,a[7]=e[7],a[8]=t[6]*r.z,a[9]=t[7]*r.z,a[10]=t[8]*r.z,a[11]=e[11],a[12]=e[12],a[13]=e[13],a[14]=e[14],a[15]=e[15],a};const j=new a.Cartesian3;k.getRotation=function(e,t){n.Check.typeOf.object("matrix",e),n.Check.typeOf.object("result",t);const a=k.getScale(e,j);return t[0]=e[0]/a.x,t[1]=e[1]/a.x,t[2]=e[2]/a.x,t[3]=e[4]/a.y,t[4]=e[5]/a.y,t[5]=e[6]/a.y,t[6]=e[8]/a.z,t[7]=e[9]/a.z,t[8]=e[10]/a.z,t},k.getRow=function(e,t,a){n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,3),n.Check.typeOf.object("result",a);var r=e[t],i=e[t+4],u=e[t+8],o=e[t+12];return a.x=r,a.y=i,a.z=u,a.w=o,a},k.setRow=function(e,t,a,r){return n.Check.typeOf.object("matrix",e),n.Check.typeOf.number.greaterThanOrEquals("index",t,0),n.Check.typeOf.number.lessThanOrEquals("index",t,3),n.Check.typeOf.object("cartesian",a),n.Check.typeOf.object("result",r),r=k.clone(e,r),r[t]=a.x,r[t+4]=a.y,r[t+8]=a.z,r[t+12]=a.w,r};var U=new a.Cartesian3;k.getScale=function(e,t){return t.x=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[0],e[1],e[2],U)),t.y=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[4],e[5],e[6],U)),t.z=a.Cartesian3.magnitude(a.Cartesian3.fromElements(e[8],e[9],e[10],U)),t};var L=new a.Cartesian3;k.getMaximumScale=function(e){return k.getScale(e,L),a.Cartesian3.maximumComponent(L)},k.multiply=function(e,t,n){var a=e[0],r=e[1],i=e[2],u=e[3],o=e[4],s=e[5],l=e[6],c=e[7],f=e[8],h=e[9],y=e[10],m=e[11],d=e[12],C=e[13],p=e[14],x=e[15],w=t[0],z=t[1],O=t[2],b=t[3],M=t[4],g=t[5],v=t[6],E=t[7],T=t[8],k=t[9],N=t[10],q=t[11],V=t[12],R=t[13],I=t[14],S=t[15],j=a*w+o*z+f*O+d*b,U=r*w+s*z+h*O+C*b,L=i*w+l*z+y*O+p*b,B=u*w+c*z+m*O+x*b,W=a*M+o*g+f*v+d*E,P=r*M+s*g+h*v+C*E,A=i*M+l*g+y*v+p*E,_=u*M+c*g+m*v+x*E,D=a*T+o*k+f*N+d*q,Z=r*T+s*k+h*N+C*q,Y=i*T+l*k+y*N+p*q,F=u*T+c*k+m*N+x*q,X=a*V+o*R+f*I+d*S,Q=r*V+s*R+h*I+C*S,G=i*V+l*R+y*I+p*S,H=u*V+c*R+m*I+x*S;return n[0]=j,n[1]=U,n[2]=L,n[3]=B,n[4]=W,n[5]=P,n[6]=A,n[7]=_,n[8]=D,n[9]=Z,n[10]=Y,n[11]=F,n[12]=X,n[13]=Q,n[14]=G,n[15]=H,n},k.add=function(e,t,n){return n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n[4]=e[4]+t[4],n[5]=e[5]+t[5],n[6]=e[6]+t[6],n[7]=e[7]+t[7],n[8]=e[8]+t[8],n[9]=e[9]+t[9],n[10]=e[10]+t[10],n[11]=e[11]+t[11],n[12]=e[12]+t[12],n[13]=e[13]+t[13],n[14]=e[14]+t[14],n[15]=e[15]+t[15],n},k.subtract=function(e,t,n){return n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n[4]=e[4]-t[4],n[5]=e[5]-t[5],n[6]=e[6]-t[6],n[7]=e[7]-t[7],n[8]=e[8]-t[8],n[9]=e[9]-t[9],n[10]=e[10]-t[10],n[11]=e[11]-t[11],n[12]=e[12]-t[12],n[13]=e[13]-t[13],n[14]=e[14]-t[14],n[15]=e[15]-t[15],n},k.multiplyTransformation=function(e,t,n){var a=e[0],r=e[1],i=e[2],u=e[4],o=e[5],s=e[6],l=e[8],c=e[9],f=e[10],h=e[12],y=e[13],m=e[14],d=t[0],C=t[1],p=t[2],x=t[4],w=t[5],z=t[6],O=t[8],b=t[9],M=t[10],g=t[12],v=t[13],E=t[14],T=a*d+u*C+l*p,k=r*d+o*C+c*p,N=i*d+s*C+f*p,q=a*x+u*w+l*z,V=r*x+o*w+c*z,R=i*x+s*w+f*z,I=a*O+u*b+l*M,S=r*O+o*b+c*M,j=i*O+s*b+f*M,U=a*g+u*v+l*E+h,L=r*g+o*v+c*E+y,B=i*g+s*v+f*E+m;return n[0]=T,n[1]=k,n[2]=N,n[3]=0,n[4]=q,n[5]=V,n[6]=R,n[7]=0,n[8]=I,n[9]=S,n[10]=j,n[11]=0,n[12]=U,n[13]=L,n[14]=B,n[15]=1,n},k.multiplyByMatrix3=function(e,t,n){var a=e[0],r=e[1],i=e[2],u=e[4],o=e[5],s=e[6],l=e[8],c=e[9],f=e[10],h=t[0],y=t[1],m=t[2],d=t[3],C=t[4],p=t[5],x=t[6],w=t[7],z=t[8],O=a*h+u*y+l*m,b=r*h+o*y+c*m,M=i*h+s*y+f*m,g=a*d+u*C+l*p,v=r*d+o*C+c*p,E=i*d+s*C+f*p,T=a*x+u*w+l*z,k=r*x+o*w+c*z,N=i*x+s*w+f*z;return n[0]=O,n[1]=b,n[2]=M,n[3]=0,n[4]=g,n[5]=v,n[6]=E,n[7]=0,n[8]=T,n[9]=k,n[10]=N,n[11]=0,n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n},k.multiplyByTranslation=function(e,t,n){var a=t.x,r=t.y,i=t.z,u=a*e[0]+r*e[4]+i*e[8]+e[12],o=a*e[1]+r*e[5]+i*e[9]+e[13],s=a*e[2]+r*e[6]+i*e[10]+e[14];return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=u,n[13]=o,n[14]=s,n[15]=e[15],n};var B=new a.Cartesian3;k.multiplyByUniformScale=function(e,t,n){return B.x=t,B.y=t,B.z=t,k.multiplyByScale(e,B,n)},k.multiplyByScale=function(e,t,n){var a=t.x,r=t.y,i=t.z;return 1===a&&1===r&&1===i?k.clone(e,n):(n[0]=a*e[0],n[1]=a*e[1],n[2]=a*e[2],n[3]=0,n[4]=r*e[4],n[5]=r*e[5],n[6]=r*e[6],n[7]=0,n[8]=i*e[8],n[9]=i*e[9],n[10]=i*e[10],n[11]=0,n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=1,n)},k.multiplyByVector=function(e,t,n){var a=t.x,r=t.y,i=t.z,u=t.w,o=e[0]*a+e[4]*r+e[8]*i+e[12]*u,s=e[1]*a+e[5]*r+e[9]*i+e[13]*u,l=e[2]*a+e[6]*r+e[10]*i+e[14]*u,c=e[3]*a+e[7]*r+e[11]*i+e[15]*u;return n.x=o,n.y=s,n.z=l,n.w=c,n},k.multiplyByPointAsVector=function(e,t,n){var a=t.x,r=t.y,i=t.z,u=e[0]*a+e[4]*r+e[8]*i,o=e[1]*a+e[5]*r+e[9]*i,s=e[2]*a+e[6]*r+e[10]*i;return n.x=u,n.y=o,n.z=s,n},k.multiplyByPoint=function(e,t,n){var a=t.x,r=t.y,i=t.z,u=e[0]*a+e[4]*r+e[8]*i+e[12],o=e[1]*a+e[5]*r+e[9]*i+e[13],s=e[2]*a+e[6]*r+e[10]*i+e[14];return n.x=u,n.y=o,n.z=s,n},k.multiplyByScalar=function(e,t,n){return n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n[4]=e[4]*t,n[5]=e[5]*t,n[6]=e[6]*t,n[7]=e[7]*t,n[8]=e[8]*t,n[9]=e[9]*t,n[10]=e[10]*t,n[11]=e[11]*t,n[12]=e[12]*t,n[13]=e[13]*t,n[14]=e[14]*t,n[15]=e[15]*t,n},k.negate=function(e,t){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t[4]=-e[4],t[5]=-e[5],t[6]=-e[6],t[7]=-e[7],t[8]=-e[8],t[9]=-e[9],t[10]=-e[10],t[11]=-e[11],t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=-e[15],t},k.transpose=function(e,t){var n=e[1],a=e[2],r=e[3],i=e[6],u=e[7],o=e[11];return t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=n,t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=a,t[9]=i,t[10]=e[10],t[11]=e[14],t[12]=r,t[13]=u,t[14]=o,t[15]=e[15],t},k.abs=function(e,t){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t[4]=Math.abs(e[4]),t[5]=Math.abs(e[5]),t[6]=Math.abs(e[6]),t[7]=Math.abs(e[7]),t[8]=Math.abs(e[8]),t[9]=Math.abs(e[9]),t[10]=Math.abs(e[10]),t[11]=Math.abs(e[11]),t[12]=Math.abs(e[12]),t[13]=Math.abs(e[13]),t[14]=Math.abs(e[14]),t[15]=Math.abs(e[15]),t},k.equals=function(e,n){return e===n||t.defined(e)&&t.defined(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[7]===n[7]&&e[8]===n[8]&&e[9]===n[9]&&e[10]===n[10]&&e[11]===n[11]&&e[12]===n[12]&&e[13]===n[13]&&e[14]===n[14]&&e[15]===n[15]},k.equalsEpsilon=function(e,n,a){return a=t.defaultValue(a,0),e===n||t.defined(e)&&t.defined(n)&&Math.abs(e[0]-n[0])<=a&&Math.abs(e[1]-n[1])<=a&&Math.abs(e[2]-n[2])<=a&&Math.abs(e[3]-n[3])<=a&&Math.abs(e[4]-n[4])<=a&&Math.abs(e[5]-n[5])<=a&&Math.abs(e[6]-n[6])<=a&&Math.abs(e[7]-n[7])<=a&&Math.abs(e[8]-n[8])<=a&&Math.abs(e[9]-n[9])<=a&&Math.abs(e[10]-n[10])<=a&&Math.abs(e[11]-n[11])<=a&&Math.abs(e[12]-n[12])<=a&&Math.abs(e[13]-n[13])<=a&&Math.abs(e[14]-n[14])<=a&&Math.abs(e[15]-n[15])<=a},k.getTranslation=function(e,t){return t.x=e[12],t.y=e[13],t.z=e[14],t},k.getMatrix3=function(e,t){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t};var W=new i,P=new i,A=new C,_=new C(0,0,0,1);k.inverse=function(e,t){var n=e[0],u=e[4],o=e[8],s=e[12],l=e[1],c=e[5],f=e[9],h=e[13],y=e[2],m=e[6],d=e[10],p=e[14],x=e[3],w=e[7],z=e[11],O=e[15],b=d*O,M=p*z,g=m*O,v=p*w,E=m*z,T=d*w,N=y*O,q=p*x,V=y*z,R=d*x,I=y*w,S=m*x,j=b*c+v*f+E*h-(M*c+g*f+T*h),U=M*l+N*f+R*h-(b*l+q*f+V*h),L=g*l+q*c+I*h-(v*l+N*c+S*h),B=T*l+V*c+S*f-(E*l+R*c+I*f),D=M*u+g*o+T*s-(b*u+v*o+E*s),Z=b*n+q*o+V*s-(M*n+N*o+R*s),Y=v*n+N*u+S*s-(g*n+q*u+I*s),F=E*n+R*u+I*o-(T*n+V*u+S*o);b=o*h,M=s*f,g=u*h,v=s*c,E=u*f,T=o*c,N=n*h,q=s*l,V=n*f,R=o*l,I=n*c,S=u*l;var X=b*w+v*z+E*O-(M*w+g*z+T*O),Q=M*x+N*z+R*O-(b*x+q*z+V*O),G=g*x+q*w+I*O-(v*x+N*w+S*O),H=T*x+V*w+S*z-(E*x+R*w+I*z),J=g*d+T*p+M*m-(E*p+b*m+v*d),K=V*p+b*y+q*d-(N*d+R*p+M*y),$=N*m+S*p+v*y-(I*p+g*y+q*m),ee=I*d+E*y+R*m-(V*m+S*d+T*y),te=n*j+u*U+o*L+s*B;if(Math.abs(te)<a.CesiumMath.EPSILON21){if(i.equalsEpsilon(k.getMatrix3(e,W),P,a.CesiumMath.EPSILON7)&&C.equals(k.getRow(e,3,A),_))return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=0,t[11]=0,t[12]=-e[12],t[13]=-e[13],t[14]=-e[14],t[15]=1,t;throw new r.RuntimeError("matrix is not invertible because its determinate is zero.")}return te=1/te,t[0]=j*te,t[1]=U*te,t[2]=L*te,t[3]=B*te,t[4]=D*te,t[5]=Z*te,t[6]=Y*te,t[7]=F*te,t[8]=X*te,t[9]=Q*te,t[10]=G*te,t[11]=H*te,t[12]=J*te,t[13]=K*te,t[14]=$*te,t[15]=ee*te,t},k.inverseTransformation=function(e,t){var n=e[0],a=e[1],r=e[2],i=e[4],u=e[5],o=e[6],s=e[8],l=e[9],c=e[10],f=e[12],h=e[13],y=e[14],m=-n*f-a*h-r*y,d=-i*f-u*h-o*y,C=-s*f-l*h-c*y;return t[0]=n,t[1]=i,t[2]=s,t[3]=0,t[4]=a,t[5]=u,t[6]=l,t[7]=0,t[8]=r,t[9]=o,t[10]=c,t[11]=0,t[12]=m,t[13]=d,t[14]=C,t[15]=1,t},k.IDENTITY=Object.freeze(new k(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),k.ZERO=Object.freeze(new k(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),k.COLUMN0ROW0=0,k.COLUMN0ROW1=1,k.COLUMN0ROW2=2,k.COLUMN0ROW3=3,k.COLUMN1ROW0=4,k.COLUMN1ROW1=5,k.COLUMN1ROW2=6,k.COLUMN1ROW3=7,k.COLUMN2ROW0=8,k.COLUMN2ROW1=9,k.COLUMN2ROW2=10,k.COLUMN2ROW3=11,k.COLUMN3ROW0=12,k.COLUMN3ROW1=13,k.COLUMN3ROW2=14,k.COLUMN3ROW3=15,Object.defineProperties(k.prototype,{length:{get:function(){return k.packedLength}}}),k.prototype.clone=function(e){return k.clone(this,e)},k.prototype.equals=function(e){return k.equals(this,e)},k.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]&&e[4]===t[n+4]&&e[5]===t[n+5]&&e[6]===t[n+6]&&e[7]===t[n+7]&&e[8]===t[n+8]&&e[9]===t[n+9]&&e[10]===t[n+10]&&e[11]===t[n+11]&&e[12]===t[n+12]&&e[13]===t[n+13]&&e[14]===t[n+14]&&e[15]===t[n+15]},k.fromPositions=function(e){let t=e[0],n=e[1],r=e[2],i=a.Cartesian3.subtract(t,n,new a.Cartesian3),u=a.Cartesian3.subtract(t,r,new a.Cartesian3),o=a.Cartesian3.cross(i,u,new a.Cartesian3);return a.Cartesian3.normalize(o,o),a.Cartesian3.cross(o,i,u),a.Cartesian3.normalize(o,o),a.Cartesian3.normalize(i,i),a.Cartesian3.normalize(u,u),new k(i.x,u.x,o.x,t.x,i.y,u.y,o.y,t.y,i.z,u.z,o.z,t.z,0,0,0,1)},k.prototype.equalsEpsilon=function(e,t){return k.equalsEpsilon(this,e,t)},k.prototype.toString=function(){return"("+this[0]+", "+this[4]+", "+this[8]+", "+this[12]+")\n("+this[1]+", "+this[5]+", "+this[9]+", "+this[13]+")\n("+this[2]+", "+this[6]+", "+this[10]+", "+this[14]+")\n("+this[3]+", "+this[7]+", "+this[11]+", "+this[15]+")"},k.prototype.leftTransformBy=function(e){return k.multiply(e,this,this),this},k.prototype.rightTransformBy=function(e){return k.multiply(this,e,this),this},k.prototype.createInverse=function(){const e=new k;return k.inverse(this,e),e},e.Cartesian4=C,e.Matrix3=i,e.Matrix4=k}));