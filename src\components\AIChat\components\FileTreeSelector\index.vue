<template>
  <div v-if="visible" class="file-tree-selector-overlay" @click="handleOverlayClick">
    <div class="file-tree-selector" @click.stop>
      <!-- 标题栏 -->
      <div class="selector-header">
        <h3 class="selector-title">选择文件</h3>
        <el-icon class="close-btn" @click="handleClose">
          <Close />
        </el-icon>
      </div>
      
      <!-- 内容区域 -->
      <div class="file-tree-container">
        <div class="search-box">
          <el-input
            v-model="searchText"
            placeholder="搜索文件..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="tree-container">
          <div v-if="loading" class="loading-container">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span>正在加载文件数据...</span>
          </div>
          <div v-else-if="filteredTreeData.length === 0" class="empty-container">
            <el-empty description="暂无文件数据" />
          </div>
          <el-tree-v2
            v-else
            ref="treeRef"
            :data="filteredTreeData"
            :props="treeProps"
            show-checkbox
            :default-expanded-keys="expandedKeys"
            :check-strictly="false"
            @check="handleCheck"
            :filter-method="filterNode"
            :height="treeHeight"
          >
            <template #default="{ node, data }">
              <span class="tree-node" :title="node.label">
                <div v-if="data.type === 'folder'" class="file-icon folder-icon">
                  <el-icon><Folder /></el-icon>
                </div>
                <div v-else-if="getFileExtension(data.label)" class="file-icon text-icon" :class="getFileTypeClass(data.label)">
                  {{ getFileExtension(data.label) }}
                </div>
                <div v-else class="file-icon default-icon">
                  <el-icon size="16px"><Document /></el-icon>
                </div>
                <span class="node-label">{{ node.label }}</span>
                <span v-if="data.size" class="file-size">({{ formatFileSize(data.size) }})</span>
              </span>
            </template>
          </el-tree-v2>
        </div>
      </div>
      
      <!-- 底部操作栏 -->
      <div class="selector-footer">
        <div class="selected-info">
          已选择 {{ selectedFiles.length }}{{ maxCount ? `/${maxCount}` : '' }} 个文件
          <span v-if="leafOnly" class="leaf-only-tip">（仅可选择文件）</span>
        </div>
                  <div class="buttons">
            <el-button @click="handleClose">取消</el-button>
            <!-- <el-button @click="handleSelectAll">全选</el-button> -->
            <!-- <el-button @click="handleClearAll">清空</el-button> -->
            <el-button v-if="!searchText.trim()" @click="handleToggleExpandAll">
              {{ isAllExpanded ? '全部折叠' : '全部展开' }}
            </el-button>
            <el-button type="primary" @click="handleConfirm" :disabled="selectedFiles.length === 0">
              确定
            </el-button>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search, Folder, Document, Close, Loading } from '@element-plus/icons-vue'
import type { ElTreeV2 } from 'element-plus'
import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'

interface FileNode {
  id: string
  label: string
  type: 'folder' | 'file'
  size?: number
  children?: FileNode[]
  path: string
}

const isAllExpanded = ref(false)

const props = defineProps<{
  modelValue: boolean
  selectedFileIds?: string[] // 已选择的文件ID数组（用于控制选中状态）
  maxCount?: number // 最大选择数量，默认不限制
  leafOnly?: boolean // 是否只能选择叶子节点，默认false
  requestApi?: (params?: any) => Promise<any> // 请求文件数据的API函数
  initParams?: Record<string, any> // 初始化参数，默认为{}
  maxHeight?: number // 树形控件的最大高度，默认400px
  fieldConfig?: {
    // 字段映射配置，用于适配不同的数据结构
    // 注意：数据结构为两级树形结构
    // - 一级节点（文件夹）：使用 id 字段作为唯一标识
    // - 二级节点（文件）：使用 childId 字段作为唯一标识，通过 parentId 关联到一级节点
    // 使用示例：
    // :field-config="{
    //   id: 'folderId',         // 一级节点ID字段（文件夹）
    //   childId: 'fileId',      // 二级节点ID字段（文件）
    //   parentId: 'folderId',   // 父文件夹ID字段  
    //   name: 'fileName',       // 文件名字段
    //   leaf: 'isFile',         // 是否为文件的标识字段
    //   path: 'fullPath',       // 文件路径字段
    //   size: 'fileSize'        // 文件大小字段
    // }"
    id?: string // 一级节点ID字段名，默认为 'id'
    childId?: string // 二级节点ID字段名，默认为 'knowledgeFileId'
    parentId?: string // 父ID字段名，默认为 'parentId'
    name?: string // 名称字段名，默认为 'name'
    leaf?: string // 叶子节点标识字段，默认为 'leaf'
    path?: string // 路径字段名，默认为 'url'
    size?: string // 大小字段名，默认为 'size'
  }
}>()

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  'update:selectedFileIds': [value: string[]]
  'confirm': [files: FileNode[]]
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

const treeRef = ref<InstanceType<typeof ElTreeV2>>()
const searchText = ref('')
const selectedFiles = ref<FileNode[]>([])
const loading = ref(false)

// 计算属性
const maxCount = computed(() => props.maxCount)
const leafOnly = computed(() => props.leafOnly)
const treeHeight = computed(() => props.maxHeight || 400)

// 字段配置，支持自定义字段映射
const fieldMap = computed(() => ({
  id: props.fieldConfig?.id || 'id', // 一级节点使用id字段
  childId: props.fieldConfig?.childId || 'id', // 二级节点使用id字段
  parentId: props.fieldConfig?.parentId || 'parentId',
  name: props.fieldConfig?.name || 'name',
  leaf: props.fieldConfig?.leaf || 'leaf',
  path: props.fieldConfig?.path || 'path', // 默认使用url字段
  size: props.fieldConfig?.size || 'size'
}))

// 树形控件配置 - el-tree-v2使用不同的props结构
const treeProps = {
  value: 'id', // el-tree-v2使用value而不是node-key
  label: 'label',
  children: 'children',
  disabled: (data: FileNode) => {
    // 如果设置了只能选择叶子节点，则禁用所有文件夹
    if (props.leafOnly && data.type === 'folder') {
      return true
    }
    // 原有逻辑：禁用没有子节点的文件夹
    return data.type === 'folder' && !data.children?.length
  }
}

// 将扁平数据转换为树形结构（两级结构：一级用id，二级用childId）
const buildTreeFromFlatData = (flatData: any[]): FileNode[] => {
  if (!flatData || flatData.length === 0) return []
  
  const fields = fieldMap.value
  const nodeMap = new Map()
  const result: FileNode[] = []

  // 首先创建所有节点
  flatData.forEach(item => {
    const isLeaf = item[fields.leaf]
    // 根据是否为叶子节点使用不同的ID字段
    // 一级节点（文件夹）使用 id，二级节点（文件）使用 childId
    const nodeId = isLeaf ? item[fields.childId]?.toString() : item[fields.id]?.toString()
    
    if (!nodeId) return // 跳过没有ID的节点
    
    const node: FileNode = {
      id: nodeId,
      label: item[fields.name] || '',
      type: isLeaf ? 'file' : 'folder',
      path: item[fields.path] || '',
      size: item[fields.size] || 0,
      children: isLeaf ? undefined : []
    }
    nodeMap.set(nodeId, node)
  })

  // 构建树形关系
  flatData.forEach(item => {
    const isLeaf = item[fields.leaf]
    
    if (isLeaf) {
      // 二级节点（文件）：通过parentId关联到一级节点
      const fileNode = nodeMap.get(item[fields.childId]?.toString())
      const parentId = item[fields.parentId]?.toString()
      
      if (fileNode && parentId) {
        const parentNode = nodeMap.get(parentId)
        if (parentNode && parentNode.children) {
          parentNode.children.push(fileNode)
        }
      }
    } else {
      // 一级节点（文件夹）：直接添加到结果中
      const folderNode = nodeMap.get(item[fields.id]?.toString())
      if (folderNode) {
        result.push(folderNode)
      }
    }
  })

  return result
}

// 默认展开的节点
const expandedKeys = ref<string[]>([])

// 实际使用的树数据
const actualTreeData = ref<FileNode[]>([])

// 过滤后的树数据
const filteredTreeData = ref<FileNode[]>([])

// 原始扁平数据，用于懒加载
const rawFlatData = ref<any[]>([])

// 节点映射，用于快速查找
const nodeMap = ref<Map<string, any>>(new Map())

// 获取所有文件夹ID的工具函数
const getAllFolderIds = (nodes: FileNode[]): string[] => {
  let folderIds: string[] = []
  nodes.forEach(node => {
    if (node.type === 'folder') {
      folderIds.push(node.id)
      if (node.children) {
        folderIds = folderIds.concat(getAllFolderIds(node.children))
      }
    }
  })
  return folderIds
}

// 防抖函数
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件扩展名（用于显示）
const getFileExtension = (fileName: string): string => {
  const supportedExtensions = ['.pdf', '.docx', '.xlsx']
  const lowerFileName = fileName.toLowerCase()
  
  for (const ext of supportedExtensions) {
    if (lowerFileName.endsWith(ext)) {
      return ext.substring(1).toUpperCase() // 去掉点号并转大写
    }
  }
  
  return '' // 不支持的扩展名返回空字符串，使用默认图标
}

// 获取文件类型样式类名
const getFileTypeClass = (fileName: string): string => {
  const lowerFileName = fileName.toLowerCase()
  
  if (lowerFileName.endsWith('.pdf')) return 'pdf-file'
  if (lowerFileName.endsWith('.docx')) return 'docx-file'
  if (lowerFileName.endsWith('.xlsx')) return 'xlsx-file'
  
  return 'default-file'
}



// 搜索功能（防抖处理）
const debouncedSearch = debounce(() => {
  if (treeRef.value) {
    treeRef.value.filter(searchText.value)
    // 搜索时如果有内容，认为是展开状态
    if (searchText.value.trim()) {
      isAllExpanded.value = true
    } else {
      // 清空搜索时，重置为折叠状态
      // isAllExpanded.value = false
      isAllExpanded.value = true
    }
  }
}, 300)

const handleSearch = () => {
  debouncedSearch()
}

// 过滤节点 - el-tree-v2的过滤方法
const filterNode = (query: string, data: FileNode) => {
  if (!query) return true
  return data.label.toLowerCase().includes(query.toLowerCase())
}

// 获取所有文件节点
const getAllFileNodes = (nodes?: FileNode[]): FileNode[] => {
  const nodesToProcess = nodes || actualTreeData.value
  let files: FileNode[] = []
  nodesToProcess.forEach(node => {
    if (node.type === 'file') {
      files.push(node)
    }
    if (node.children) {
      files = files.concat(getAllFileNodes(node.children))
    }
  })
  return files
}

// 加载文件数据
const loadFileData = async () => {
  if (props.requestApi) {
    try {
      loading.value = true
      const params = props.initParams || {}
      const response = await props.requestApi(params)
      const flatData = response.result || response
      
      // 存储原始数据
      rawFlatData.value = flatData
      
      // 构建节点映射以提高查找性能
      const newNodeMap = new Map()
      flatData.forEach((item: any) => {
        const isLeaf = item[fieldMap.value.leaf]
        const nodeId = isLeaf ? item[fieldMap.value.childId]?.toString() : item[fieldMap.value.id]?.toString()
        if (nodeId) {
          newNodeMap.set(nodeId, item)
        }
      })
      nodeMap.value = newNodeMap
      
      // 直接构建完整树形结构，虚拟滚动会处理性能问题
      const treeData = buildTreeFromFlatData(flatData)
      actualTreeData.value = treeData
      filteredTreeData.value = treeData
      
      // 默认收起所有文件夹
      expandedKeys.value = []
      
      console.log(`加载完成，数据量: ${flatData.length}，使用虚拟滚动模式`)
    } catch (error) {
      console.error('加载文件数据失败:', error)
      ElMessage.error('加载文件数据失败')
    } finally {
      loading.value = false
    }
  }
}

// 处理选择变化 - el-tree-v2的事件参数不同
const handleCheck = (data: FileNode, checkInfo: any) => {
  // 使用防抖处理，避免频繁更新
  setTimeout(() => {
    const checkedNodes = treeRef.value?.getCheckedNodes() || []
    let filteredFiles = checkedNodes.filter((node: any) => node.type === 'file') as FileNode[]
    
    // 如果设置了最大选择数量限制
    if (props.maxCount && filteredFiles.length > props.maxCount) {
      // 超出限制，恢复到之前的状态
      ElMessage.warning(`最多只能选择 ${props.maxCount} 个文件`)
      nextTick(() => {
        if (treeRef.value) {
          const currentKeys = selectedFiles.value.map(file => file.id)
          treeRef.value.setCheckedKeys(currentKeys)
        }
      })
      return
    }
    
    selectedFiles.value = filteredFiles
    
    // 发出双向绑定事件，只包含文件ID，不包含文件夹ID
    const fileIds = filteredFiles.map(file => file.id)
    emits('update:selectedFileIds', fileIds)
  }, 50)
}

// 切换全部展开/折叠
const handleToggleExpandAll = () => {
  isAllExpanded.value = !isAllExpanded.value
  if (isAllExpanded.value) {
    treeRef.value?.setExpandedKeys(getAllFolderIds(actualTreeData.value))
  } else {
    treeRef.value?.setExpandedKeys([])
  }
}

// 全选
const handleSelectAll = () => {
  const allFiles = getAllFileNodes()
  
  // 如果设置了数量限制，检查是否超出
  if (props.maxCount && allFiles.length > props.maxCount) {
    ElMessage.warning(`最多只能选择 ${props.maxCount} 个文件，当前共有 ${allFiles.length} 个文件`)
    return
  }
  
  const allFileIds = allFiles.map(file => file.id).filter(id => id)
  treeRef.value?.setCheckedKeys(allFileIds)
  selectedFiles.value = allFiles
  
  // 发出事件
  const fileIds = allFiles.map(file => file.id)
  emits('update:selectedFileIds', fileIds)
}

// 清空选择
const handleClearAll = () => {
  treeRef.value?.setCheckedKeys([])
  selectedFiles.value = []
  
  // 发出事件
  emits('update:selectedFileIds', [])
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  handleClose()
}

// 确认选择
const handleConfirm = () => {
  emits('confirm', selectedFiles.value)
  handleClose()
}

// 监听弹窗打开，设置初始状态
watch(visible, async (newVal) => {
  if (newVal) {
    searchText.value = ''
    
    // 加载文件数据
    await loadFileData()
    
    // 优先使用selectedFileIds设置选中状态
    if (props.selectedFileIds && props.selectedFileIds.length > 0) {
      const allFiles = getAllFileNodes()
      const matchedFiles = allFiles.filter(file => props.selectedFileIds!.includes(file.id))
      selectedFiles.value = matchedFiles
      
      // 设置树形控件的选中状态
      nextTick(() => {
        if (treeRef.value && matchedFiles.length > 0) {
          const checkedKeys = matchedFiles.map(file => file.id).filter(id => id)
          treeRef.value.setCheckedKeys(checkedKeys)
        }
      })
    } else {
      selectedFiles.value = []
    }
  }
})

// 监听参数变化，重新加载数据
watch(() => props.initParams, async (newParams, oldParams) => {
  if (visible.value && JSON.stringify(newParams) !== JSON.stringify(oldParams)) {
    await loadFileData()
  }
}, { deep: true })

// 监听selectedFileIds变化，同步选中状态
watch(() => props.selectedFileIds, (newIds) => {
  if (newIds && newIds.length > 0 && actualTreeData.value.length > 0) {
    const allFiles = getAllFileNodes()
    const matchedFiles = allFiles.filter(file => newIds.includes(file.id))
    selectedFiles.value = matchedFiles
    

    
    // 设置树形控件的选中状态
    nextTick(() => {
      if (treeRef.value && matchedFiles.length > 0) {
        const checkedKeys = matchedFiles.map(file => file.id).filter(id => id)
        treeRef.value.setCheckedKeys(checkedKeys)
      }
    })
  } else if (!newIds || newIds.length === 0) {
    selectedFiles.value = []

    
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCheckedKeys([])
      }
    })
  }
}, { deep: true })
</script>

<style scoped>
.file-tree-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.file-tree-selector {
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
}

.selector-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  font-size: 20px;
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #606266;
}

.file-tree-container {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-box {
  margin-bottom: 16px;
}

.tree-container {
  flex: 1;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
  min-height: 300px;
  height: v-bind('treeHeight + "px"');
}

.tree-container :deep(.el-tree) {
  background: transparent;
  height: 100%;
}

.tree-container :deep(.el-tree-node__content) {
  height: 36px;
  line-height: 36px;
  padding: 0 8px;
}

/* el-tree-v2 虚拟滚动样式优化 */
.tree-container :deep(.el-tree-v2) {
  height: 100%;
}

.tree-container :deep(.el-virtual-list) {
  height: 100% !important;
}

.tree-container :deep(.el-scrollbar) {
  height: 100%;
}

.tree-container :deep(.el-scrollbar__view) {
  height: auto;
}

.tree-container :deep(.el-scrollbar__bar) {
  opacity: 1 !important;
  transition: opacity 0.3s;
}

.tree-container :deep(.el-scrollbar__bar.is-vertical) {
  right: 2px;
  width: 8px;
}

.tree-container :deep(.el-scrollbar__thumb) {
  background-color: #c1c1c1 !important;
  border-radius: 4px;
}

.tree-container :deep(.el-scrollbar__thumb:hover) {
  background-color: #a8a8a8 !important;
}

/* el-tree-v2 节点样式 */
.tree-container :deep(.el-tree-v2__node) {
  padding: 2px 0;
}

.tree-container :deep(.el-tree-v2__node-content) {
  height: auto;
  min-height: 26px;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #606266;
  font-size: 14px;
  gap: 12px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.file-icon {
  flex-shrink: 0;
  width: 24px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  transition: all 0.2s;
}

.folder-icon {
  color: #E6A23C;
  background: transparent;
}

.text-icon {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.default-icon {
  color: #606266;
  background: transparent;
}

/* PDF文件样式 */
.pdf-file {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
}

.pdf-file:hover {
  background: linear-gradient(135deg, #ff3742, #ff2f3a);
  transform: scale(1.05);
}

/* DOCX文件样式 */
.docx-file {
  background: linear-gradient(135deg, #2e86de, #54a0ff);
  box-shadow: 0 2px 4px rgba(46, 134, 222, 0.3);
}

.docx-file:hover {
  background: linear-gradient(135deg, #54a0ff, #74b9ff);
  transform: scale(1.05);
}

/* XLSX文件样式 */
.xlsx-file {
  background: linear-gradient(135deg, #20bf6b, #26d0ce);
  box-shadow: 0 2px 4px rgba(32, 191, 107, 0.3);
}

.xlsx-file:hover {
  background: linear-gradient(135deg, #26d0ce, #45aaf2);
  transform: scale(1.05);
}

.node-label {
  flex: 1;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #909399;
  font-size: 12px;
  flex-shrink: 0;
}

.selector-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  background: #f8f9fa;
}

.selected-info {
  color: #606266;
  font-size: 14px;
  font-weight: 500;

  .leaf-only-tip {
    color: #909399;
    font-size: 12px;
    margin-left: 8px;
  }
}

.buttons {
  display: flex;
  gap: 12px;
}
</style>

<style>
.el-tree-node__content {
  padding: 8px 0;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.el-tree-node__content:hover {
  background-color: #f0f2f5;
}

.el-tree-node__expand-icon {
  color: #606266;
}

.el-tree-node__label {
  font-size: 14px;
}
</style> 