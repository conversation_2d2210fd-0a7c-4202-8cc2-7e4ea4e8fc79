<template>
  <div class="popup-sub-title-container popup-title">
    <span class="title">{{ title }}</span>
    <img class="sub-title-bg" src="@/assets/CommonPopup/sub-title-bg.png" alt="" />
    <slot></slot>
  </div>
</template>

<script setup>
  defineProps({
    title: {
      type: String,
      default: ''
    }
  })
</script>
<style lang="scss" scoped>
  .popup-sub-title-container {
    position: relative;
    width: 330px;
    height: 28px;
    letter-spacing: 1px;
    .title {
      font-family: 'Source Han Sans CN', 'Source Han Sans CN';
      font-size: 15px;
      font-weight: bold;
      color: #bcddff;
    }
    .sub-title-bg {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 330px;
    }
  }
</style>
