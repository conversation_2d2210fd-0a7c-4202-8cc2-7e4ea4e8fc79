<template>
  <div class="traffic-container">
    <div class="title-container">
      <div v-show-ai-question @click="handleTitleClick">交通运行监测</div>
      <OpenPage style="top: 6px; left: 218px" :path="'/trafficFlow/FlowManagement'" :params="{ dateType: type, date: handleDate }"></OpenPage>

      <!-- <SwitchButton class="btn-wrap" width="200px" height="22px" v-model="btnList"></SwitchButton> -->
      <DatePickerNew style="top: 6px; right: 34px" v-model="type" :currentDate="date" @change="handleDateChange($event)"></DatePickerNew>
      <el-popover placement="bottom-end" :width="120" trigger="hover" popper-class="popper-line" effect="customized">
        <template #reference>
          <el-icon class="icon-select"><CaretBottom /></el-icon>
        </template>
        <div class="sub-list-container">
          <div
            class="sub-item"
            :class="{ selected: subItem.selected }"
            v-for="(subItem, subIndex) in lineList"
            :key="subIndex"
            @click="handleSubitemClick(subItem)">
            {{ subItem.name }}
          </div>
        </div>
      </el-popover>
    </div>
    <div class="content">
      <el-row>
        <el-col :span="12">
          <CarInfo 
            :type="type" 
            :date="handleDate" 
            :lineName="lineName" 
            :resultList="resultList">
          </CarInfo>
        </el-col>
        <el-col :span="12">
          <CarRate
            :type="type"
            :date="handleDate"
            :lineName="lineName"
            :queryParams="queryParams"
            :resultList="resultList"
            style="margin-top: 18px; margin-left: 70px">
          </CarRate>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import moment from 'moment'

  import CarInfo from './Components/carInfo.vue'
  import CarRate from './Components/carRate.vue'
  import DatePickerNew from '@/components/DatePickerNew/index.vue'
  import OpenPage from '@/components/OpenPage/index.vue'

  import lib from '@/utils/lib'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'
  import CreateMqtt from '@/utils/mqtt.js'

  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleTitleClick = () => {
    if (showAIQuestion.value) {
      lib.utils.sendQuestionToAIChat('交通运行监测')
    }
  }
  // import SwitchButton from '@/components/SwitchButton/index.vue'
  defineOptions({
    name: 'Traffic'
  })
  // const btnList = ref([
  //   { id: 1, label: '今日', selected: true, value: 'day' },
  //   { id: 2, label: '本月', selected: false, value: 'month' },
  //   { id: 3, label: '本年', selected: false, value: 'year' }
  // ])
  // const type = computed(() => {
  //   const selectedBtn = btnList.value.find((item) => item.selected)
  //   return selectedBtn.type
  // })
  const type = ref('day') // 默认当天
  const date = moment().format('YYYY-MM-DD')
  const handleDate = ref(new Date())
  const handleDateChange = (val) => {
    handleDate.value = val 
  }
  const lineList = ref([
    { name: '全部', selected: true },
    { name: '东线', selected: false },
    { name: '西线', selected: false }
  ])
  const lineName = computed(() => {
    const selectedBtn = lineList.value.find((item) => item.selected)
    return selectedBtn.name
  })
  const queryParams = ref({
    period: type.value,
    lineId: null,
    startTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  })
  const handleSubitemClick = (val) => {
    lineList.value.forEach((item) => {
      item.selected = item.name === val.name
    })
    const obj = {
      东线: 67,
      西线: 71
    }
    if (val.selected) {
      queryParams.value.lineId = obj[val.name]
      startMqtt()
    }
  }

  const resultList = ref([])
  const PublicMqtt = ref(null)
  const startMqtt = () => {
    // 仅当天需要实时推流(东西线切换也是调接口)
    const isCurrentDay = queryParams.value.period === 'day' && queryParams.value.startTime === moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    const isUseMqtt = isCurrentDay && lineName.value === '全部'
    if(!isUseMqtt) {
      unsubscribe()
      vehicleType()
    } else { 
      PublicMqtt.value = new CreateMqtt('vehicleType')
      PublicMqtt.value.init()
      PublicMqtt.value.link()
      getMessage()
    }
  }
  const vehicleType = async () => {
    await lib.api.bigscreenApi.vehicleType( queryParams.value ).then((res) => {
      if (res.success && res.result) {
        resultList.value = res.result
      }
    })
  }

  const getMessage = () => {
    // 接收消息
    PublicMqtt.value.client.on('message', (topic, message) => {
      try {
        const str = JSON.parse(message.toString())
        if (message) {
          resultList.value = str
        }
      } catch (error) {
        console.error('数据解析失败:', error)
        resultList.value = []
      }
    })
  }

  // 取消订阅
  const unsubscribe = () => {
    // 如果页面并没有初始化MQTT，无需取消订阅
    if (PublicMqtt.value) {
      PublicMqtt.value.unsubscribes()
      PublicMqtt.value.over()
    }
  }

  onMounted(() => {
    startMqtt()
  })

  onUnmounted(() => {
    unsubscribe()
  })

  watch(
    () => type.value,
    (value) => {
      queryParams.value.period = value
      updateParams()
    }
  )
  watch(
    () => handleDate.value,
    (value) => {
      updateParams()
    }
  )
  const updateParams = () => {
    const typeMap = {
      day: {
        period: 'day',
        startTime: moment(handleDate.value).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(handleDate.value).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      },
      month: {
        period: 'month',
        startTime: moment(handleDate.value).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(handleDate.value).endOf('month').format('YYYY-MM-DD HH:mm:ss')
      },
      year: {
        period: 'year',
        startTime: moment(handleDate.value).startOf('year').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(handleDate.value).endOf('year').format('YYYY-MM-DD HH:mm:ss')
      }
    }
    queryParams.value = typeMap[type.value]
    startMqtt()
  }
</script>

<style lang="scss" scoped>
  .traffic-container {
    // margin-top: 20px;
    .title-container {
      width: 216px;
      height: 50px;
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      font-weight: 400;
      line-height: 45px;
      color: #d4e9ff;
      text-align: center;
      background: url('@/assets/ScreenRight/Perception/Device/head.png') no-repeat;
      background-size: 100% 100%;
      .btn-wrap {
        position: absolute;
        top: 10px;
        right: 35px;
      }
      .icon-select {
        position: absolute;
        top: 26px;
        right: 0;
        font-size: 20px;
        color: #77c6ff;
        cursor: pointer;
      }
    }
    .content {
      width: 1280px;
      height: 430px;

      // padding-top: 15px;
    }
  }
</style>
<style>
  .el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 0;
    background: rgb(2 17 27 / 71%);
    border: 1px solid #0080b6;
  }
  .el-popper.is-customized .el-popper__arrow::before {
    right: 0;
    background: rgb(2 17 27 / 71%);
  }
  .popper-line {
    .sub-list-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 110px;
      margin: 0 auto;
      overflow-y: scroll;
      scrollbar-width: none;
      .sub-item {
        box-sizing: border-box;
        width: 96px;
        height: 25px;
        padding: 0 16px;
        margin-bottom: 5px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-weight: 400;
        line-height: 25px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        &.selected {
          font-size: 20px;
          color: #43ddff;
          background: linear-gradient(90deg, rgb(86 194 255 / 0%) 0%, rgb(86 194 255 / 85%) 51%, rgb(86 194 255 / 0%) 100%);
        }
      }
    }
  }
</style>
