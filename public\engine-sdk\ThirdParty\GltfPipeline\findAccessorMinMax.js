import getAccessorByteStride from"./getAccessorByteStride.js";import getComponentReader from"./getComponentReader.js";import numberOfComponentsForType from"./numberOfComponentsForType.js";import arrayFill from"../../Core/arrayFill.js";import ComponentDatatype from"../../Core/ComponentDatatype.js";import defined from"../../Core/defined.js";function findAccessorMinMax(e,r){var t=e.bufferViews,o=e.buffers,n=r.bufferView,a=numberOfComponentsForType(r.type);if(!defined(r.bufferView))return{min:arrayFill(new Array(a),0),max:arrayFill(new Array(a),0)};for(var f=arrayFill(new Array(a),Number.POSITIVE_INFINITY),i=arrayFill(new Array(a),Number.NEGATIVE_INFINITY),m=t[n],p=m.buffer,s=o[p],y=s.extras._pipeline.source,u=r.count,d=getAccessorByteStride(e,r),b=r.byteOffset+m.byteOffset+y.byteOffset,c=r.componentType,l=ComponentDatatype.getSizeInBytes(c),C=new DataView(y.buffer),A=new Array(a),F=getComponentReader(c),w=0;w<u;w++){F(C,b,a,l,A);for(var I=0;I<a;I++){var T=A[I];f[I]=Math.min(f[I],T),i[I]=Math.max(i[I],T)}b+=d}return{min:f,max:i}}export default findAccessorMinMax;