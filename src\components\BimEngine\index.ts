import type { TagItem } from './interface/index'
import { OverlayOp } from './interface/index'
import { playCrushAnimation, stopCrushAnimation } from './src/animation'
import {
  cameraFollowCar,
  flyToBimId,
  flyToByGId,
  gotoViewPortByGeo,
  gotoViewportByName,
  gotoViewPortByPositionWC,
  romaByName,
  romaPause,
  romaResum,
  romaStop
} from './src/camera'
import {
  enableClickToFollow,
  getAllGid,
  getBimInfoByCode,
  getBimInfoByCodeList,
  resetColor,
  setBowlingEnable,
  setColorByList,
  setDLCarFollow,
  setDLRoadLineVisible,
  setUeResouseVisible,
  setVisibilityByBimIds,
  startTrafficStream,
  startTrafficStreamCommon
} from './src/models'
import { addBillboard, addCzml, addCzmlByCartesian, addHtml, clearCzml, removeCzmlByTagstr } from './src/overlay'
import lib from '@/utils/lib'
import type { Model, ModTagInstance, PickObject, Project, Vector3, Viewer } from 'BimEngine'
import { closePopWindowByTag, showPopWindow } from 'znyg-frontend-common'
import { getToken } from '@/utils/auth'
import { plcControlApi } from '@/api/control'

const BimEngine = window.BimEngine
const { useUe } = window.config
class EngineController {
  viewer: Viewer = null
  mcpWebsocket = null
  currentProject: Project = null
  overlayList = {
    billboard: [],
    czmlList: [] // 存放使用addCzml添加的标签
  }
  selectedModel: Model = null
  markCollection = null
  inputListener = null
  vehicleTwinOpen = false

  constructor(container: string, serverUrl: string, appId: string, secret: string, projectId: string) {
    if (container && serverUrl && appId && secret) {
      this.viewer = new BimEngine.Viewer({
        container: container,
        baseUrl: serverUrl,
        appId: appId,
        secret: secret,
        // 开启UE
        useUE: useUe,
        openId: projectId,
        openType: 'project'
      })
      this.viewer.setPanRotateApplyType(BimEngine.ControlApplyType.LEFT_MOUSE_BTN, BimEngine.ControlApplyType.RIGHT_MOUSE_BTN)

      localStorage.setItem('fixResolution', '')
      window._viewer = this.viewer
    }
  }

  openProj = async (projId) => {
    await this.viewer.Init()

    setTimeout(() => {
      if (useUe) {
        // this.viewer.viewer.ueViewer.setResolution(2560, 720, true)
        // this.viewer.viewer.ueViewer.setResolution(3840, 1080, true)
        this.viewer.viewer.ueViewer.setResolution(4096, 1152, true)
        // this.viewer.viewer.ueViewer.setResolution(1920, 1080, true)
        localStorage.setItem('fixResolution', 'true')
        // this.viewer.viewer.ueViewer.setKeyboardMode(true, 150)
      }
    }, 2000)

    this.currentProject = await this.viewer.queryProject(projId)
    await this.currentProject.open()
    if (this.currentProject.viewPosition) {
      this.viewer.camera.setViewToViewPosition(this.currentProject.viewPosition)
    } else {
      this.viewer.camera.setViewToProject(this.currentProject)
    }

    //#region MCP服务
    // 1. 初始化
    this.mcpWebsocket = new MCPWebsocket(this.currentProject, this.viewer, BimEngine, {
      url: 'ws://***********:5525/graphics-ws', // websocket 地址
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      enableAutoReconnect: true,
      // 这个参数必传 直接传递 token ，利用这个 token 进行鉴权, 必须确保不同的UE实例有不同的 token
      connectionId: getToken()
    })

    // 2. 连接 WebSocket
    this.mcpWebsocket
      .connect()
      .then(() => {
        console.log('WebSocket 连接成功')
      })
      .catch((error) => {
        console.log(`连接失败: ${error.message}`)
      })

    // 3. 添加自定义的业务层设备控制处理器

    // 使用新的设备执行器注册方式
    // registerDeviceExecutor 方法三个参数 deviceType callback pipelineType
    // pipelineType
    // core 覆盖引擎core的实现
    // before 保留引擎core实现，在引擎core调用之前调用一个方法
    // after 保留引擎core实现，在引擎core调用之后调用一个方法
    // 覆盖默认的设备执行器 车辆孪生 每个业务都是不一样的 所以业务层驱动
    this.mcpWebsocket.registerDeviceExecutor(
      'toggle_vehicle_twin',
      async (ids, payload) => {
        //   // 车辆孪生
        console.log('车辆孪生:', payload)
        const { enabled } = payload
        if (enabled) {
          // 打开车辆孪生
          console.log('打开车辆孪生:', ids, payload)
          lib.ai.startTrafficStreamCommon(true, 'ws://***********:10000/mqtt/publish', 'tessng', 'mosquito', 'Cjxx-2024')
          return
          // 这里可以调用业务系统的API来打开车辆孪生
        } else {
          // 关闭车辆孪生
          console.log('关闭车辆孪生:', ids, payload)
          lib.ai.startTrafficStreamCommon(false, 'ws://***********:10000/mqtt/publish', 'tessng', 'mosquito', 'Cjxx-2024')
          return
          // 这里可以调用业务系统的API来关闭车辆孪生
        }
        // await myFanSDK.setSpeed(ids, payload.speed);
        // 有错误抛出
        throw new Error('无权限控制车辆孪生')
      },
      'core'
    )

    // 后置 bim引擎风扇控制 之后执行真实风扇控制
    this.mcpWebsocket.registerDeviceExecutor(
      'fan',
      async (ids, payload) => {
        // 加一段自有业务逻辑
        // await myFanSDK.setSpeed(ids, payload.speed);
        console.log('风扇控制:', payload)
        const res = await plcControlApi.control({
          deviceNo: 1,
          op: payload.isRunning ? 'open' : 'close'
        })
        console.log(res)
        if (!res.success) {
          //如果失败，就再调一次接口
          const res2 = await plcControlApi.control({
            deviceNo: 1,
            op: payload.isRunning ? 'open' : 'close'
          })
          if (!res2.success) {
            //再失败就抛出异常
            throw new Error(res2.message)
          }
        }

        // if (!res.success) {
        //   throw new Error(res.message)
        // }
        // 比如权限
        // return Promise.resolve(true);
        // throw new Error('只有中控大屏才有权限控制真实风扇');
        // throw new Error('只有中控大屏才有权限控制真实风扇')
      },
      'after'
    )

    // 相机控制 这个是一个自定义的设备控制逻辑
    // 这个设备控制器会覆盖默认的设备控制器
    this.mcpWebsocket.registerDeviceExecutor(
      'camera',
      async (ids, payload) => {
        debugger
        // 处理业务层自定义设备控制逻辑
        console.log('控制自定义设备:', payload)
        // 例如：调用业务系统的API
        const { names, zoneNames, isOpen } = payload
        if (isOpen) {
          // 打开设备
          console.log('打开设备:', ids, names, zoneNames, payload.ids)
          const validNames = ['江南', '江北']
          if (names && names.length > 0) {
            // 如果有设备名称，打印出来
            if (validNames.includes(names[0])) {
              console.log('设备名称:', names[0])
              // 调用业务系统的API来打开设备
              // 这里可以根据设备名称进行不同的处理
              const cameraName = names[0]
              lib.ai.openVideo(cameraName)
            } else {
              console.log('设备名称不在预期列表中:', names[0])
              // 一定要抛出错误 这个错误消息会发送给大模型
              throw new Error(`设备名称不在预期列表中, ${names[0]}，预期的列表有 ${validNames.join(', ')}`)
            }
          }
        } else {
          // 关闭设备
          console.log('关闭设备:', ids, names, zoneNames)
          // 这里可以调用业务系统的API来关闭设备
        }
        return Promise.resolve(true)
      },
      'core'
    )

    // UI面板控制 - 业务层自定义UI面板打开逻辑
    this.mcpWebsocket.registerDeviceExecutor(
      'ui_open_panel',
      async (ids, payload) => {
        debugger
        console.log('打开UI面板:', payload)
        const { panelName } = payload

        // 根据面板名称执行不同的打开逻辑
        switch (panelName) {
          case '设备监控面板':
            console.log('打开设备监控面板')
            // 这里可以调用业务系统的API来打开设备监控面板
            // window.openDeviceMonitorPanel();
            break
          case '数据分析面板':
            console.log('打开数据分析面板')
            // window.openDataAnalysisPanel();
            break
          case '系统设置面板':
            console.log('打开系统设置面板')
            // window.openSystemSettingsPanel();
            break
          default:
            // 如果面板名称不在预期列表中，抛出错误 告诉大模型 支持哪些面板
            throw new Error(`不支持的面板名称: ${panelName} 支持的面板有: 设备监控面板, 数据分析面板, 系统设置面板`)
        }

        return Promise.resolve(true)
      },
      'core'
    )

    //#endregion

    // 设置车辆跟随视角配置
    this.viewer.ueViewer.setVehicleFollowOptions(4, 9, 0.8)
    this.viewer.ueViewer.setBowlingOptions({
      plateId: 'plateId',
      shiftUp: 5,
      drawBack: 10,
      lerpTime_is_0_to_1: '',
      lerpTime: 0.5
    })

    this.inputListener = new BimEngine.InputMap(this.viewer)
    this.inputListener.setInput(BimEngine.InputType.LEFT_CLICK, async (windowPosition, event) => {
      // 开启车辆孪生时，不进行点击事件
      // if (this.vehicleTwinOpen) return
      console.log(windowPosition, event)
      const pickPosition = await this.viewer.pickPosition(windowPosition)
      console.log('坐标', pickPosition)
      if (pickPosition) {
        let carto = this.currentProject.worldToGeo(pickPosition)
        console.log(carto + '经纬度坐标(弧度):  经度=' + carto.x.toFixed(8) + '  纬度=' + carto.y.toFixed(8) + '  高度=' + carto.z.toFixed(2))
      }

      let clickClinetX = event.data?.data?.clientX ?? 1420
      let clickClinetY = event.data?.data?.clientY ?? 150
      const pickObj: PickObject = await this.viewer?.pickAdaptor(windowPosition)
      if (pickObj) {
        console.log('pickObj:', pickObj, pickObj.model?.mod?.packet)

        if (pickObj.model.type === BimEngine.ModelType.BIM) {
          if (pickObj.model.id === this.selectedModel?.id) {
            this.selectedModel?.deselect()
            this.selectedModel = null
          } else {
            this.selectedModel?.deselect()
            this.selectedModel = pickObj.model
            pickObj.model.select(pickObj.id)
          }

          const res = await pickObj.model.queryElement(pickObj.id)
          console.log('模糊查找结果', res)
          if (res.bimProperties?.code?.value) {
            // lib.bus.busEventHotMapClickBim.emit(res.bimProperties?.code?.value)

            if (clickClinetY > 1000) {
              clickClinetY = 1000
            }
            const code = res.bimProperties?.code?.value
            lib.popWindow.removeDialog('structureWindow')
            // lib.api.getPopupDetail.structureResumeCardStructure({ code }).then((res) => {
            //   if (res.success && res.result) {
            //     lib.popWindow.createPopWindow(
            //       './Components/StructureFile/index.vue',
            //       {
            //         left: clickClinetX,
            //         top: clickClinetY,
            //         tag: 'structureWindow',
            //         // appContext,
            //         appendParent: 'player',
            //         closeFunc: () => {
            //           this.selectedModel?.deselect()
            //           this.selectedModel = null
            //         }
            //       },
            //       {
            //         ...res.result,
            //         code: code
            //       }
            //     )
            //   }
            // })

            const resStructure = await lib.api.getPopupDetail.structureResumeCardStructure({ code })
            if (resStructure.success && resStructure.result) {
              lib.popWindow.createPopWindow(
                './Components/StructureFile/index.vue',
                {
                  left: clickClinetX,
                  top: clickClinetY,
                  tag: 'structureWindow',
                  // appContext,
                  appendParent: 'player',
                  closeFunc: () => {
                    this.selectedModel?.deselect()
                    this.selectedModel = null
                  }
                },
                {
                  ...resStructure.result,
                  code: code
                }
              )
              return
            }
            // 由于不知道是设施还是设备，所以需要分别查询
            const resDevice = await lib.api.getPopupDetail.deviceResumeCardDevice({ code })
            if (resDevice.success && resDevice.result) {
              lib.popWindow.createPopWindow(
                './Components/DeviceFile/index.vue',
                {
                  left: clickClinetX,
                  top: clickClinetY,
                  tag: 'deviceWindow',
                  // appContext,
                  appendParent: 'player',
                  // followPoint: { typeId: obj.typeId, id },
                  closeFunc: () => {}
                },
                { id: resDevice.result?.device?.id }
              )
              return
            }
          }
        } else if (pickObj.model.type === BimEngine.ModelType.TAG) {
          // 使用ModTagInstance撒的点
          const czmlJson = pickObj.model.mod.packet
          if (czmlJson?.length === 2) {
            const tagCzml = czmlJson[1]
            tagCzml?.onClick?.(windowPosition, pickObj.model.mod, clickClinetX, clickClinetY)
            return
          }
        }

        const overlayOp: OverlayOp = pickObj.model.mod.packet
        overlayOp?.onClick?.()
      }
    })
  }

  /* 获取viewer */
  getViewer() {
    return this.viewer
  }
  // #region 撒点
  /**
   * 添加撒点
   */
  async addBillboard(option: OverlayOp) {
    this.#initMarkCollectionEditor()
    const billboard = await addBillboard(this, option)
    return billboard
  }

  /** 通过经纬度撒点，放html */
  addHtml(log, lat, height, html, czml?: any) {
    return addHtml(this, log, lat, height, html, czml)
  }

  /**
   * 根据经纬度撒点
   * @param log 经度
   * @param lat 纬度
   * @param height 高度
   * @param czml Cesium配置语言格式
   * @param tagStr
   * @param clearable 是否可以使用clearCzml方法清除
   * @param depth 深度,设置之后会进入真实场景，有远近之分
   * @returns
   */
  addCzml(log, lat, height, czml, tagStr, clearable: boolean = true, depth?: number) {
    return addCzml(this, log, lat, height, czml, tagStr, clearable, depth)
  }

  /** 根据标签名清除通过AddCzml添加的标签*/
  removeCzmlByTagstr(tagStr: string) {
    removeCzmlByTagstr(this, tagStr)
  }

  /** 清除所有使用czml撒的点 */
  clearCzml() {
    clearCzml(this)
  }
  /** 清除选中模型 */
  clearSelect() {
    this.selectedModel?.deselect()
    this.selectedModel = null
  }

  // #endregion
  /**
   * 移除撒点
   */
  async removeBillboard() {
    this.#initMarkCollectionEditor()
    this.overlayList.billboard.forEach((item) => {
      this.markCollection.remove(item)
    })
  }

  #initMarkCollectionEditor() {
    if (this.markCollection === null) {
      this.markCollection = new BimEngine.MarkCollectionEditor(this.viewer, this.currentProject)
    }
  }

  /**
   * 当前世界坐标系点转wgs84（gps）经纬度
   */
  worldToGeo(position: Vector3) {
    return this.currentProject.worldToGeo(position)
  }

  /** wgs84 (gps) 经纬度坐标转当前世界坐标系点 */
  geoToWorld(longitude: number, latitude: number, height: number) {
    return this.currentProject?.geoToWorld(longitude, latitude, height)
  }

  // #region camera相关
  /**
   * 根据实施的视口名称跳转视口
   * @param name 视口名称
   * @param durationTime 动画持续时间，单位秒
   */
  gotoViewportByName(name: string, durationTime?: number) {
    gotoViewportByName(this, name, durationTime)
  }
  /**
   * 根据实施的漫游名称进行路径漫游
   * @param name 漫游名称
   */
  romaByName(name: string) {
    romaByName(this, name)
  }

  /**
   * 停止漫游
   */
  romaStop() {
    // console.log('停止漫游')
    romaStop(this)
  }
  /**
   * 继续漫游
   */
  romaResum() {
    // console.log('继续漫游')
    romaResum(this)
  }
  /**
   * 暂停漫游
   */
  romaPause() {
    // console.log('暂停漫游')
    romaPause(this)
  }
  /** 根据bimid跳转到对应视口 */
  flyToBimId(bimid: string, distance?: number) {
    return flyToBimId(this, bimid, distance)
  }
  /** 根据构建Id跳转到对应视口 */
  flyToByGId(gid: string) {
    return flyToByGId(this, gid)
  }
  /** 根据deviceId跳转到对应视口 */

  cameraFollowCar(vehicleId: string, enableFollow: boolean = true) {
    cameraFollowCar(this, vehicleId, enableFollow)
  }

  /**
   * 相机飞到某个位置
   * @param positionWC 获取到的世界坐标位置
   * @param radius 控制相机远近  越大越远
   */
  gotoViewPortByPositionWC(positionWC, radius: number = 10) {
    gotoViewPortByPositionWC(this, positionWC, radius)
  }

  /**
   * 相机根据经纬度飞过去
   * @param lon 经度
   * @param lat 纬度
   * @param height 高度
   * @param radius 距离
   */
  gotoViewPortByGeo(lon = 0, lat = 0, height = 0, radius: number = 10) {
    gotoViewPortByGeo(this, lon, lat, height, radius)
  }

  // #endregion

  /** ue模式下设置白模的显示隐藏 */
  setUeResouseVisible(isShow: boolean, IdAry: Array<string>) {
    setUeResouseVisible(this, isShow, IdAry)
  }

  /** ue模式下车辆仿真时，开启车辆是否可以点击 */
  enableClickToFollow(enable: boolean) {
    enableClickToFollow(this, enable)
  }
  /** 开启车辆仿真 */
  startTrafficStream(enable: boolean) {
    startTrafficStream(this, enable)
  }

  /** 开启车辆仿真通用方法 */
  startTrafficStreamCommon(enable?: boolean, socketUrl?: string, topic?: string, usr?: string, pwd?: string) {
    startTrafficStreamCommon(this, enable, socketUrl, topic, usr, pwd)
  }

  /** 开启保龄球推流 */
  setBowlingEnable(enable: boolean, enableFollow: boolean) {
    setBowlingEnable(this, enable, enableFollow)
  }

  /**
   * 通过笛卡尔坐标添加czml
   * @param cartesian 笛卡尔坐标
   * @param czml
   * @param tagStr
   */
  addCzmlByCartesian(cartesian, czml, tagStr: string, clearable: boolean = true) {
    addCzmlByCartesian(this, cartesian, czml, tagStr, clearable)
  }
  /**
   * 大连路隧道开启车辆跟随，plate为空时，停止跟随
   * @param _this EngineController
   * @param plate 车牌号 为空或者不写就是取消跟随
   * @param camereRelativeUp 相机相对车辆插入点的高度
   * @param cameraRelativeBack 相机相对车辆插入点的后退距离
   * @param cameraAngle 相机的角度 0 水平视角 1 俯视视角
   */
  setDLCarFollow(plate: string, cameraRelativeUp: number = 3, cameraRelativeBack: number = 6, cameraAngle: number = 0.5) {
    setDLCarFollow(this, plate, cameraRelativeUp, cameraRelativeBack, cameraAngle)
  }
  /**
   * 道路线 默认开启道路显示，程序启动后不要立即调用本命令(简单起见)
   * @param _this EngineController
   * @param enableRoads  true 开启 false 关闭道路线
   * @param color 道路线颜色 默认[1，0，0，1]
   * @param radius 道路线半径 默认 1.1
   */

  setDLRoadLineVisible(enableRoads: boolean, color: number[] = [1, 0, 0, 1], radius: number = 1.1) {
    setDLRoadLineVisible(this, enableRoads, color, radius)
  }

  /** 播放车辆碰撞动画 */
  playCrushAnimation() {
    playCrushAnimation(this)
  }

  /** 停止车辆碰撞动画 */
  stopCrushAnimation() {
    stopCrushAnimation(this)
  }

  /** 获取所有构件Id */
  getAllGid() {
    return getAllGid(this)
  }

  // #region 模型
  getBimInfoByCode(code: string) {
    return getBimInfoByCode(this, code)
  }

  /**
   * 批量查询Bim信息
   * @param codeList 需要查询的code列表
   * @param map 存储到map中
   */
  getBimInfoByCodeList(codeList: Array<string>, map: Map<string, any>) {
    return getBimInfoByCodeList(this, codeList, map)
  }

  /**
   * 模型着色
   * @param list:Array<{bimId:string, color:string}>
   */
  setColorByList(list: Array<{ bimId: string; color: string }>) {
    return setColorByList(this, list)
  }

  /** 重置模型颜色 */
  resetColor() {
    return resetColor(this)
  }

  /**
   * 根据BimIds 设置模型可见性
   * @param visible 显示/隐藏
   * @param bimIds
   * @returns
   */
  setVisibilityByBimIds(visible: boolean, bimIds: Array<string>) {
    return setVisibilityByBimIds(this, visible, bimIds)
  }

  setSelectedModel(model: Model) {
    this.selectedModel?.deselect()
    this.selectedModel = model
    model.select()
  }
  // #endregion

  // #region 预制方法
  getCzmlData(item, base64, scale: number = 0.5, onClick?: (position, tagInstance) => void) {
    return {
      id: item?.id || lib.utils.getRandomString(10),
      name: item.name,
      billboard: {
        // 图片
        image: base64,
        scale: scale,
        disableDepthTestDistance: 999999,
        horizontalOrigin: 'CENTER',
        verticalOrigin: 'BOTTOM'
      },
      position: {
        cartesian: [0, 0, 0]
      },
      onClick: (position, tagInstance) => {
        onClick?.(position, tagInstance)
      }
    }
  }
  // #endregion

  /**
   * 设置天气时间
   * @param weatherKey 天气key
   * @param timeOfDay 1200 表示中午12点  1800 表示下午6点
   */
  setWeatherTime(weatherKey: number, timeOfDay: number = 1200) {
    this.viewer.ueViewer.setWeatherTime(weatherKey, timeOfDay)
  }
}

export default EngineController
