<template>
  <div class="events-view">
    <PopupBg title="事件查看" width="378px" height="579px" @close="handlerClose">
      <el-scrollbar style="height: 108px">
        <div class="timeline" v-if="state.reportList.length > 0">
          <div class="timeline-item" v-for="(item, index) in state.reportList" :key="index">
            <div class="dot">
              <div class="line" v-if="index !== state.reportList.length - 1"></div>
            </div>
            <div class="type">{{ item.status }}</div>
            <div class="time">{{ item.updateTime ? moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
          </div>
        </div>
        <div class="no-data" v-else>暂无数据</div>
      </el-scrollbar>
      <PopupSubTitle title="事件详情" class="mt12"></PopupSubTitle>
      <div class="info-box">
        <div class="name-block">
          <div v-for="(item, index) in infoData" :key="index" style="width: 73px; color: #dbefff">{{ item.name }}:</div>
        </div>
        <div class="value-block">
          <div v-for="(item, index) in infoData" :key="index">
            <div v-if="item.name !== '事件图片'" v-tooltip>{{ item.value }}</div>
            <div v-else class="images-container scroll-bar-style">
              <img v-for="(pic, ind) in pictureInfo" :key="ind" :src="pic.url" @click="clickImage(ind)" width="100px" height="55px" />
            </div>
          </div>
        </div>
        <!-- <div class="images">
          <div style="width: 73px; color: #dbefff">事件图片:</div>
          <div class="images-container scroll-bar-style">
            <img v-for="(item, index) in pictureInfo" :key="index" :src="item.url" @click="clickImage(index)" width="100px" height="55px" />
          </div>
        </div> -->
      </div>
    </PopupBg>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, watch } from 'vue'
  import moment from 'moment'
  import PopupBg from '@/components/PopupBg/index.vue'
  import PopupSubTitle from '@/components/PopupSubTitle/index.vue'
  import lib from '@/utils/lib'
  import { type ImageViewerProps, useImageViewer } from 'znyg-frontend-common'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const handlerClose = () => {
    lib.popWindow.removeDialog('eventsWindow')
  }
  const clickImage = async (index) => {
    // lib.store().storeDictionary.DIALOG_IMG([item.url])

    // const op: ImageViewerProps = {
    //   urlList: pictureInfo.value.map((_) => _.url),
    //   hideOnClickModal: true,
    //   initialIndex: index
    // }
    // useImageViewer(op)
    lib.utils.openImageZoom(pictureInfo.value.map((_) => _.url))
  }
  const infoData = ref([
    { name: '发生时间', value: '---' },
    { name: '事件类型', value: '---' },
    { name: '事件等级', value: '---' },
    { name: '事件位置', value: '---' },
    { name: '涉及车道', value: '---' },
    { name: '有无物损', value: '---' },
    { name: '事件描述', value: '---' },
    { name: '现场处置', value: '---' },
    { name: '事件图片', value: '---' }
  ])
  const state = reactive({
    reportList: []
  })
  const pictureInfo = ref([])
  watch(
    () => props.data,
    (newVal) => {
      console.log('newVal', newVal)
      if (!newVal.id) return
      lib.api.eventOrderDetailApi.getEmergency({ id: newVal.id }).then(async (res) => {
        if (res.success && res.result) {
          // state.reportList = res.result
          const filteredResult = res?.result.reduce((acc, curr) => {
            if (curr.status === '终报') {
              const existingIndex = acc.findIndex((item) => item.status === '终报')
              if (existingIndex !== -1) {
                if (parseInt(curr.updateTime) > parseInt(acc[existingIndex].updateTime)) {
                  acc[existingIndex] = curr
                }
              } else {
                acc.push(curr)
              }
            } else {
              acc.push(curr)
            }
            return acc
          }, [])
          // 多个终报 取时间最新的
          state.reportList = filteredResult

          const detailObj = res.result[res.result.length - 1]
          infoData.value[0].value = detailObj.detectionTime ? moment(detailObj.detectionTime).format('YYYY-MM-DD HH:mm:ss') : ''
          infoData.value[1].value = detailObj.subType == '无' ? detailObj.type : detailObj.subType
          infoData.value[2].value = detailObj.level
          infoData.value[3].value = detailObj.edetailPosition
          infoData.value[4].value = detailObj.eventLane
          infoData.value[5].value = detailObj.emergencyDefectOrders ? '有' : '无'
          infoData.value[6].value = detailObj.swqkDesc
          infoData.value[7].value = detailObj.swqkDisposalCase

          const picRes = await lib.api.docDiyDiyApi.getDocsFromUrl({ urlList: detailObj.imagePathList })
          pictureInfo.value = []
          if (picRes.success) {
            picRes.result.forEach((item, index) => {
              pictureInfo.value.push({ url: import.meta.env.VITE_DOC_URL + 'img/' + item.fileName })
            })
          }
          console.log('pictureInfo', pictureInfo.value)
        }
      })
    },
    { immediate: true, deep: true }
  )
</script>

<style lang="scss" scoped>
  .events-view {
    width: 378px;
    height: 579px;

    // background: url('@/assets/CommonPopup/popupBg2.png');
    // background-size: cover;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 17px 0;
      font-family: PangMenZhengDao;
      font-size: 24px;
      font-weight: 400;
      color: #ffffff;
    }
    .sub-title {
      width: 343px;
      height: 24px;
      padding-left: 28px;
      margin: 14px 21px 0 14px;
      font-family: PangMenZhengDao;
      font-size: 18px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/CommonPopup/title.png');
      background-size: 343px 24px;
    }
    .timeline {
      height: 108px;
      margin-bottom: 20px;
      margin-left: 12px;
      .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .type {
          width: 37px;
          height: 26px;
          margin-right: 25px;
          margin-left: 8px;
          font-family: YouSheBiaoTiHei;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: 23px;
          color: #19c8ff;
        }
        .time {
          width: 138px;
          height: 19px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          color: #b4f2ff;
        }
      }
      .dot {
        position: relative;
        width: 12px;
        height: 12px;
        background: #ffffff;
        border: 2px solid #3793ff;
        border-radius: 6px;
        .line {
          position: absolute;
          top: 12px;
          left: 3px;
          width: 2px;
          height: 25px;
          background-color: #3793ff;
        }
      }
    }
    .no-data {
      height: 108px;
      line-height: 108px;
      color: #cccccc;
      text-align: center;
    }
    .info-box {
      display: flex;
      flex-wrap: wrap;
      margin: 11px 21px 0 0;
      font-family: 'Alibaba PuHuiTi';
      font-size: 16px;
      .name-block,
      .value-block {
        display: flex;
        flex: 1;
        flex-direction: column;
        gap: 10px;
        width: 203px;
        height: 332px;
        padding: 10px;
        margin-right: 10px;
        color: #5bb5ff;
        background: rgb(39 75 113 / 22%);
        border-radius: 13px;
      }
      .value-block {
        flex: 2;
        margin-right: 0;
        color: #ffffff;
      }
    }
    .info-item {
      display: flex;
      width: 332px;
      height: 30px;
    }

    // .images {
    //   display: flex;
    .images-container {
      display: flex;
      flex-shrink: 0;
      justify-content: space-between;
      width: 182px;
      height: 55px;
      overflow: hidden;
      overflow-x: scroll;
      img {
        margin-right: 10px;
        margin-bottom: 10px;
      }

      // }
    }
  }
</style>
