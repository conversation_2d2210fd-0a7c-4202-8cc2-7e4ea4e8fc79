<template>
  <div class="ring-chart-container">
    <el-row>
      <el-col :span="12" class="ring-bg">
        <MyChart width="310px" height="240px" :option="options"></MyChart>
      </el-col>
      <el-col :span="12" class="ring-bg">
        <MyChart ref="cpChart" width="310px" height="240px" :option="options2"></MyChart>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { onMounted } from 'vue'

  import MyChart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib.ts'
  const props = defineProps({
    type: {
      type: String,
      default: '结构监测'
    }
  })

  const color1Out = ['#0EED82', '#ffc000']
  const color1In = ['rgba(14, 237, 130,0.2)', 'rgba(255, 192, 0,0.2)']
  const color2Out = ['#3BBAD5', '#0EED82']
  const color2In = ['rgba(59, 186, 213,0.2)', 'rgba(14, 237, 130,0.2)']
  const cpChart = ref(null)
  const options = ref({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      height: '110px',
      selectedMode: false,
      orient: 'vertical',
      top: '40%',
      left: '35%',
      itemGap: 15,
      icon: 'roundRect',
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      show: true,
      data: ['一级报警', '二级报警']
      // formatter: function(name) {
      //   const item = data.find((_) => _.name === name)
      //   if (item) {
      //     return name + '  ' + item.value
      //   }
      // }
    },
    series: [
      {
        type: 'pie',
        radius: ['75', '85'],
        center: ['50%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            name: '一级报警',
            value: 98,
            itemStyle: {
              color: '#0EED82'
            }
          },
          {
            name: '二级报警',
            value: 50,
            itemStyle: {
              color: '#ffc000'
            }
          }
        ],
        itemStyle: {
          normal: {}
        },
        emphasis: {
          scale: false
        }
      },
      {
        type: 'pie',
        radius: ['66', '75'],
        center: ['50%', '50%'],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            name: '一级报警',
            value: 98.74,
            itemStyle: {
              color: 'rgba(14, 237, 130,0.2)'
            }
          },
          {
            name: '二级报警',
            value: 1.26,
            itemStyle: {
              color: 'rgba(255, 192, 0,0.2)'
            }
          }
        ],
        itemStyle: {
          normal: {}
        },
        emphasis: {
          scale: false
        }
      }
    ]
  })

  const options2 = ref({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      height: '110px',
      selectedMode: false,
      orient: 'vertical',
      top: '40%',
      left: '35%',
      itemGap: 15,
      icon: 'roundRect',
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      show: true,
      data: ['已处理', '未处理']
      // formatter: function(name) {
      //   const item = data.find((_) => _.name === name)
      //   if (item) {
      //     return name + '  ' + item.value
      //   }
      // }
    },
    series: [
      {
        type: 'pie',
        radius: ['75', '85'],
        center: ['50%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: [
          {
            name: '已处理',
            value: 68,
            itemStyle: {
              color: '#3BBAD5'
            }
          },
          {
            name: '未处理',
            value: 10,
            itemStyle: {
              color: '#ffc000'
            }
          }
        ],
        itemStyle: {
          normal: {}
        },
        emphasis: {
          scale: false
        }
      },
      {
        type: 'pie',
        radius: ['66', '75'],
        center: ['50%', '50%'],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: [
          {
            name: '已处理',
            value: 68,
            itemStyle: {
              color: 'rgba(59, 186, 213,0.2)'
            }
          },
          {
            name: '未处理',
            value: 10,
            itemStyle: {
              color: 'rgba(255, 192, 0,0.2)'
            }
          }
        ],
        itemStyle: {
          normal: {}
        },
        emphasis: {
          scale: false
        }
      }
    ]
  })
  let secondChartData = null
  let chartData = null
  lib.bus.busMonitorRingChart.on((data) => {
    console.log('ddddddddd', data)
    const { alertLevelStatistics, dealStatusStatistics, alertStatusStatistics } = data
    secondChartData = alertStatusStatistics
    chartData = dealStatusStatistics
    if (alertLevelStatistics) {
      options.value.legend.data = alertLevelStatistics.map((_) => _.name + '报警')
      options.value.series[0].data = alertLevelStatistics.map((_, index) => {
        return {
          name: _.name + '报警',
          value: _.value,
          itemStyle: {
            color: color1Out[index]
          }
        }
      })
      options.value.series[1].data = alertLevelStatistics.map((_, index) => {
        return {
          name: _.name + '报警',
          value: _.value,
          itemStyle: {
            color: color1In[index]
          }
        }
      })
    }

    if (dealStatusStatistics) {
      options2.value.legend.data = dealStatusStatistics.map((_) => _.name)
      options2.value.series[0].data = dealStatusStatistics.map((_, index) => {
        return {
          name: _.name,
          value: _.value,
          itemStyle: {
            color: color2Out[index]
          }
        }
      })
      options2.value.series[1].data = dealStatusStatistics.map((_, index) => {
        return {
          name: _.name,
          value: _.value,
          itemStyle: {
            color: color2In[index]
          }
        }
      })
    }
  })
  watchEffect(() => {
    if (props.type === '机电监测') {
      options.value.legend.data = ['一级报警', '二级报警']
      options.value.series[0].data = [
        {
          name: '一级报警',
          value: 0,
          itemStyle: {
            color: color1Out[0]
          }
        },
        {
          name: '二级报警',
          value: 0,
          itemStyle: {
            color: color1Out[1]
          }
        }
      ]
      options.value.series[1].data = [
        {
          name: '一级报警',
          value: 0,
          itemStyle: {
            color: color1In[0]
          }
        },
        {
          name: '二级报警',
          value: 0,
          itemStyle: {
            color: color1In[1]
          }
        }
      ]

      options2.value.legend.data = ['未处理', '已处理']
      options2.value.series[0].data = [
        {
          name: '未处理',
          value: 0,
          itemStyle: {
            color: color2Out[0]
          }
        },
        {
          name: '已处理',
          value: 0,
          itemStyle: {
            color: color2Out[1]
          }
        }
      ]
      options2.value.series[1].data = [
        {
          name: '未处理',
          value: 0,
          itemStyle: {
            color: color2In[0]
          }
        },
        {
          name: '已处理',
          value: 0,
          itemStyle: {
            color: color2In[1]
          }
        }
      ]
    }
  })
  onMounted(() => {
    let isActive = false
    // 点击已处理时 下探展示确认和误报
    cpChart.value.getInstance().on('click', (params) => {
      if (params.name == '未处理') return
      isActive = !isActive
      const data = isActive ? secondChartData : chartData
      options2.value.legend.data = data.map((_) => _.name)
      options2.value.series[0].data = data.map((_, index) => {
        return {
          name: _.name,
          value: _.value,
          itemStyle: {
            color: color2Out[index]
          }
        }
      })
      options2.value.series[1].data = data.map((_, index) => {
        return {
          name: _.name,
          value: _.value,
          itemStyle: {
            color: color2In[index]
          }
        }
      })
    })
  })
</script>

<style lang="scss" scoped>
  .ring-chart-container {
    width: 620px;
    height: 240px;
    .ring-bg {
      background: url('@/assets/ScreenRight/Perception/Structure/<EMAIL>');
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 218px 153px;
    }
  }
</style>
