/**
 * Dify Chatbot SDK for embedding a chat interface in any web application
 * This class handles the creation and management of the chatbot UI and communication with the iframe
 */
export declare const MessageTypeObj: {
    ready: string;
    conversationCompleted: string;
    responseStream: string;
    userMessageSent: string;
    conversationHistory: string;
    error: string;
    feedbackUpdated: string;
    sendMessage: string;
    getConversationHistory: string;
    clearConversation: string;
    setInputs: string;
    getStatus: string;
    setLanguage: string;
};
export type MessageTypeEnum = 'ready' | 'conversation-completed' | 'response-stream' | 'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' | 'send-message' | 'get-conversation-history' | 'clear-conversation' | 'set-inputs' | 'get-status' | 'set-language';
/**
 * @typedef {'ready' |  'conversation-completed' |  'response-stream' |  'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' |  'send-message' |  'get-conversation-history' | 'clear-conversation' |  'set-inputs' |  'get-status' |  'set-language'} MessageTypeEnum
 */
type DifyChatbotOptions = {
    baseUrl: string;
    appKey: string;
    CHATBOT_CONFIG_NAME?: string;
    BUBBLE_BUTTON_ID?: string;
    BUBBLE_WINDOW_ID?: string;
    draggable?: boolean;
    ICONS?: {
        open: string;
        close: string;
    };
    bubbleWindowWidth?: number | string;
    bubbleWindowHeight?: number | string;
};
type SendMessageOptions = {
    query: string;
    files?: any[];
    inputs?: Record<string, any>;
};
export declare class DifyChatbot {
    private options;
    private iframe;
    private bubbleButton;
    private bubbleWindow;
    private isOpen;
    private communicationClient;
    private initialPosition;
    private dragOffset;
    private isDragging;
    /**
     * Create a new Dify Chatbot instance
     * @param options Configuration options for the chatbot
     */
    constructor(options: DifyChatbotOptions);
    /**
     * Initialize the chatbot UI elements
     */
    private init;
    /**
     * Create the chat bubble button element
     */
    private createBubbleButton;
    /**
     * Create the chat window element
     */
    private createBubbleWindow;
    /**
     * Create the iframe for the chatbot
     */
    private createIframe;
    /**
     * Initialize communication with the iframe
     */
    private initIframeCommunication;
    /**
     * Attach events to the bubble button
     */
    private attachButtonEvents;
    /**
     * Toggle the chat window visibility
     */
    private toggleChatWindow;
    /**
     * Attach drag events to the chat window if draggable is enabled
     */
    private attachDragEvents;
    /**
     * Send a message to the chatbot
     * @param query The message to send
     * @param files Optional files to attach
     * @param inputs Optional input variables
     */
    sendMessage({ query, files, inputs, }: SendMessageOptions): void;
    /**
     * Clear the current conversation
     */
    clearConversation(): void;
    /**
     * Set input variables for the conversation
     * @param inputs Key-value pairs of input variables
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * Request the conversation history
     */
    getConversationHistory(): void;
    /**
     * Set the interface language
     * @param language The language code
     */
    setLanguage(language: string): void;
    /**
     * Register a custom event handler
     * @param {MessageTypeEnum} type The message type to handle
     * @param handler The callback function
     */
    on(type: 'ready' | 'conversation-completed' | 'response-stream' | 'user-message-sent' | 'conversation-history' | 'error' | 'feedback-updated' | 'send-message' | 'get-conversation-history' | 'clear-conversation' | 'set-inputs' | 'get-status' | 'set-language', handler: (data: any) => void): void;
    /**
     * Open the chat window
     */
    open(): void;
    /**
     * Close the chat window
     */
    close(): void;
    /**
     * Destroy the chatbot instance and clean up resources
     */
    destroy(): void;
}
export {};
