<template>
  <div class="car-summary-container">
    <div class="car-summary-card" v-for="item in list" :key="item.id">
      <div class="car-summary-card-num">
        {{ item.num }}
        <span>万辆</span>
      </div>
      <div class="car-summary-card-line">
        <div class="label">{{ item.name }}</div>
        <div class="value" :class="[item.type]">
          {{ item.percent }}
          <span>%</span>
          <span><img src="@/assets/ScreenLeft/TrafficOperation/<EMAIL>" style="width: 8px; height: 10px" /></span>
        </div>
      </div>
      <div class="car-summary-card-line">
        <div class="label">总流量</div>
        <div class="value">环比率</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  defineOptions({
    name: 'carSummary'
  })

  const list = ref([
    { id: 1, num: 3.68, name: '全部', percent: 0.9, type: 'up' },
    { id: 2, num: 3.68, name: '上层', percent: 0.9, type: 'up' },
    { id: 3, num: 3.68, name: '下层', percent: 0.9, type: 'up' }
  ])
</script>

<style lang="scss" scoped>
  .car-summary-container {
    display: flex;
    justify-content: space-between;
    width: 480px;
    height: 71px;
    .car-summary-card {
      width: 150px;
      height: 71px;
      padding: 6px 20px 7px;
      background: url('@/assets/ScreenLeft/TrafficOperation/<EMAIL>');
      background-size: 150px 71px;
      .car-summary-card-num {
        height: 24px;
        font-size: 24px;
        font-weight: bold;
        line-height: 24px;
        color: #19c8ff;
        span {
          font-size: 10px;
          color: #ffffff;
        }
      }
      .car-summary-card-line {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 16px;
        margin-top: 2px;
        line-height: 16px;
        .label {
          width: 60px;
          font-size: 14px;
          font-weight: bold;
          line-height: 14px;
          color: #c6fcff;
        }
        .value {
          font-size: 14px;
          line-height: 16px;
          color: #c6fcff;
          span {
            font-size: 12px;
          }
          &.up {
            color: #ff1919;
          }
        }
      }
    }
  }
</style>
