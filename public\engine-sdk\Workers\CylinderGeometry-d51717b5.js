define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./BoundingSphere-f821e080","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./VertexFormat-9b18d410","./CylinderGeometryLibrary-03a8620f"],(function(e,t,r,o,a,n,i,s,u,d,m,f,p){"use strict";var l=new o.Cartesian2,y=new o.Cartesian3,b=new o.Cartesian3,v=new o.Cartesian3,c=new o.Cartesian3;function A(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var o=e.length,a=e.topRadius,n=e.bottomRadius,i=t.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT),s=t.defaultValue(e.slices,128);if(!t.defined(o))throw new r.DeveloperError("options.length must be defined.");if(!t.defined(a))throw new r.DeveloperError("options.topRadius must be defined.");if(!t.defined(n))throw new r.DeveloperError("options.bottomRadius must be defined.");if(s<3)throw new r.DeveloperError("options.slices must be greater than or equal to 3.");if(t.defined(e.offsetAttribute)&&e.offsetAttribute===m.GeometryOffsetAttribute.TOP)throw new r.DeveloperError("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._length=o,this._topRadius=a,this._bottomRadius=n,this._vertexFormat=f.VertexFormat.clone(i),this._slices=s,this._offsetAttribute=e.offsetAttribute,this._workerName="createCylinderGeometry"}A.packedLength=f.VertexFormat.packedLength+5,A.pack=function(e,o,a){if(!t.defined(e))throw new r.DeveloperError("value is required");if(!t.defined(o))throw new r.DeveloperError("array is required");return a=t.defaultValue(a,0),f.VertexFormat.pack(e._vertexFormat,o,a),a+=f.VertexFormat.packedLength,o[a++]=e._length,o[a++]=e._topRadius,o[a++]=e._bottomRadius,o[a++]=e._slices,o[a]=t.defaultValue(e._offsetAttribute,-1),o};var h,w=new f.VertexFormat,g={vertexFormat:w,length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,offsetAttribute:void 0};A.unpack=function(e,o,a){if(!t.defined(e))throw new r.DeveloperError("array is required");o=t.defaultValue(o,0);var n=f.VertexFormat.unpack(e,o,w);o+=f.VertexFormat.packedLength;var i=e[o++],s=e[o++],u=e[o++],d=e[o++],m=e[o];return t.defined(a)?(a._vertexFormat=f.VertexFormat.clone(n,a._vertexFormat),a._length=i,a._topRadius=s,a._bottomRadius=u,a._slices=d,a._offsetAttribute=-1===m?void 0:m,a):(g.length=i,g.topRadius=s,g.bottomRadius=u,g.slices=d,g.offsetAttribute=-1===m?void 0:m,new A(g))},A.createGeometry=function(e){var r=e._length,f=e._topRadius,A=e._bottomRadius,h=e._vertexFormat,w=e._slices;if(!(r<=0||f<0||A<0||0===f&&0===A)){var g,x=w+w,_=w+x,C=x+x,F=p.CylinderGeometryLibrary.computePositions(r,f,A,w,!0),D=h.st?new Float32Array(2*C):void 0,G=h.normal?new Float32Array(3*C):void 0,R=h.tangent?new Float32Array(3*C):void 0,O=h.bitangent?new Float32Array(3*C):void 0,T=h.normal||h.tangent||h.bitangent;if(T){var V=h.tangent||h.bitangent,E=0,L=0,P=0,k=Math.atan2(A-f,r),z=y;z.z=Math.sin(k);var M=Math.cos(k),N=v,I=b;for(g=0;g<w;g++){var S=g/w*o.CesiumMath.TWO_PI,U=M*Math.cos(S),B=M*Math.sin(S);T&&(z.x=U,z.y=B,V&&(N=o.Cartesian3.normalize(o.Cartesian3.cross(o.Cartesian3.UNIT_Z,z,N),N)),h.normal&&(G[E++]=z.x,G[E++]=z.y,G[E++]=z.z,G[E++]=z.x,G[E++]=z.y,G[E++]=z.z),h.tangent&&(R[L++]=N.x,R[L++]=N.y,R[L++]=N.z,R[L++]=N.x,R[L++]=N.y,R[L++]=N.z),h.bitangent&&(I=o.Cartesian3.normalize(o.Cartesian3.cross(z,N,I),I),O[P++]=I.x,O[P++]=I.y,O[P++]=I.z,O[P++]=I.x,O[P++]=I.y,O[P++]=I.z))}for(g=0;g<w;g++)h.normal&&(G[E++]=0,G[E++]=0,G[E++]=-1),h.tangent&&(R[L++]=1,R[L++]=0,R[L++]=0),h.bitangent&&(O[P++]=0,O[P++]=-1,O[P++]=0);for(g=0;g<w;g++)h.normal&&(G[E++]=0,G[E++]=0,G[E++]=1),h.tangent&&(R[L++]=1,R[L++]=0,R[L++]=0),h.bitangent&&(O[P++]=0,O[P++]=1,O[P++]=0)}var q=12*w-12,Y=d.IndexDatatype.createTypedArray(C,q),Z=0,J=0;for(g=0;g<w-1;g++)Y[Z++]=J,Y[Z++]=J+2,Y[Z++]=J+3,Y[Z++]=J,Y[Z++]=J+3,Y[Z++]=J+1,J+=2;for(Y[Z++]=x-2,Y[Z++]=0,Y[Z++]=1,Y[Z++]=x-2,Y[Z++]=1,Y[Z++]=x-1,g=1;g<w-1;g++)Y[Z++]=x+g+1,Y[Z++]=x+g,Y[Z++]=x;for(g=1;g<w-1;g++)Y[Z++]=_,Y[Z++]=_+g,Y[Z++]=_+g+1;var W=0;if(h.st){var j=Math.max(f,A);for(g=0;g<C;g++){var H=o.Cartesian3.fromArray(F,3*g,c);D[W++]=(H.x+j)/(2*j),D[W++]=(H.y+j)/(2*j)}}var K=new u.GeometryAttributes;h.position&&(K.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:F})),h.normal&&(K.normal=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G})),h.tangent&&(K.tangent=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),h.bitangent&&(K.bitangent=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O})),h.st&&(K.st=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),l.x=.5*r,l.y=Math.max(A,f);var Q=new a.BoundingSphere(o.Cartesian3.ZERO,o.Cartesian2.magnitude(l));if(t.defined(e._offsetAttribute)){r=F.length;var X=new Uint8Array(r/3),$=e._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;m.arrayFill(X,$),K.applyOffset=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:X})}return new i.Geometry({attributes:K,indices:Y,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:Q,offsetAttribute:e._offsetAttribute})}},A.getUnitCylinder=function(){return t.defined(h)||(h=A.createGeometry(new A({topRadius:1,bottomRadius:1,length:1,vertexFormat:f.VertexFormat.POSITION_ONLY}))),h},e.CylinderGeometry=A}));