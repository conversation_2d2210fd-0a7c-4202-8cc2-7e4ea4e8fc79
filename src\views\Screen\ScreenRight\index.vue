<!--
 * @Author: wangjialing <EMAIL>
 * @Date: 2024-04-29 14:08:53
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-22 16:14:26
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\index.vue
 * @Description:
 *
-->
<template>
  <div class="ScreenRight" :class="[activePage]">
    <component :is="activePage"></component>
  </div>
</template>

<script>
  import HomePage from './Components/HomePage/index.vue'
  export default {
    name: 'ScreenRight',
    components: { HomePage }
  }
</script>
<script setup>
  import { computed } from 'vue'

  import useStore from '@/store'

  const { screen } = useStore()

  const activePage = computed(() => {
    if (screen.activePage === 'Monitor') {
      return ''
    }
    return screen.activePage
  })
  // const width = computed(() => {
  //   if (activePage.value === 'Emergency') {
  //     return '594px'
  //   }
  //   return '556px'
  // })
</script>
<style lang="scss" scoped>
  .ScreenRight {
    z-index: 10;
    box-sizing: border-box;
    width: $screen-right-width;
    height: $screen-right-height;
    padding: 31px 20px 16px 87px;

    // margin-top: 23px;
    // margin-right: -$screen-right-width;
    overflow: hidden;
    pointer-events: auto;

    // background: linear-gradient(90deg, rgb(17 51 115 / 10%) 0%, rgb(0 53 116 / 80%) 100%);
    background: url('@/assets/ScreenRight/bg.png') no-repeat;

    //background: linear-gradient(270deg, rgb(9 38 72 / 80%) 0%, rgb(7 31 58 / 90%) 100%, rgb(0 45 96 / 90%) 100%);

    //background: seagreen;
    // &.HomePage {
    //   width: 556px;
    //   margin-right: -576px;
    // }
    // &.Emergency {
    //   // width: 594px;
    //   width: 690px;
    //   margin-right: -710px;
    // }
    // &.Municipal {
    //   width: 606px;
    //   margin-right: -626px;
    // }
  }
</style>
