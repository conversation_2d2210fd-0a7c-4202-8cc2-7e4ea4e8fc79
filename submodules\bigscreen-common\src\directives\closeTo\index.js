import { ref, watch } from 'vue'

import { useElementSize, useMutationObserver } from '@vueuse/core'

/**
 * @description: 将当前dom元素，挂载到目标盒子旁边
 * @param {String} targetId 目标盒子的ID，必填
 * @param {String} left 目标盒子在当前盒子左侧
 * @param {String} right 目标盒子在当前盒子左右侧
 * @param {String} top 可选，当前盒子top值加上设置的top值
 */
const vCloseTo = {
  mounted: (el, binding, vnode) => {
    const { targetId, left, right, top } = binding.value
    const targetDiv = document.getElementById(targetId)
    if (targetDiv) {
      // 先将当前节点移动到和目标节点同一个层级
      targetDiv.parentElement.appendChild(el)
      const x = ref(targetDiv.offsetLeft)
      const y = ref(targetDiv.offsetTop)
      useMutationObserver(
        targetDiv,
        (mutations) => {
          if (mutations[0]) {
            x.value = targetDiv.offsetLeft
            y.value = targetDiv.offsetTop
          }
        },
        {
          attributes: true
        }
      )
      const { width } = useElementSize(targetDiv)
      // 首次加载时，x,y返回为0，所以需要特殊处理

      watch(
        [x, y, width],
        () => {
          if (left !== undefined) {
            const toLeft = x.value + width.value + left
            el.style.left = toLeft + 'px'
          }
          if (right !== undefined) {
            const toLeft = x.value - el.clientWidth - right
            el.style.left = toLeft + 'px'
          }

          if (top !== undefined) {
            const toTop = y.value + top
            el.style.top = toTop + 'px'
          }
          console.log('修改后的值', el.style.left, el.style.top, el.style.top)
        },
        {
          immediate: true
        }
      )
    }
  }
}

export default vCloseTo
