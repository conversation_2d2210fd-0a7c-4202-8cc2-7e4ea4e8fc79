<template>
  <div class="material-container">
    <div class="head">物资</div>
    <div class="material-content">
      <div class="content-item" v-for="(item, index) in list" :key="index">
        <img class="icon" :src="getAssetsFile(`ScreenLeft/EventManage/${item.icon}.png`)" alt="" />
        <div class="details">
          <div>
            <span class="num">{{ item.num }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import SmallHeadLine from '@/components/SmallHeadLine/index.vue'

  import { getAssetsFile } from '@/utils'
  const list = ref([
    { name: '交通锥', icon: 'trafficCone', num: 500, unit: '只' },
    { name: '固态融雪剂', icon: 'snow', num: 6.6, unit: '吨' },
    { name: '挡水板', icon: 'water', num: 60, unit: '块' },
    { name: '防汛袋', icon: 'floodBag', num: 2500, unit: '只' },
    { name: '水马', icon: 'waterHorse', num: 40, unit: '只' },
    { name: '草包', icon: 'strawBag', num: 450, unit: '个' }
  ])
</script>

<style lang="scss" scoped>
  .material-container {
    display: flex;
    gap: 47px;
    align-items: center;
    width: 629px;
    height: 164px;
    padding: 16px 54px 8px 25px;
    background: url('@/assets/ScreenLeft/EventManage/materialBg.png') no-repeat;
    background-size: 100% 100%;
    .head {
      width: 48px;
      height: 40px;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      font-weight: 400;
      color: #ffffff;
      background: url('@/assets/ScreenLeft/EventManage/titleLine.png') no-repeat;
      background-position: 100% 100%;
    }
    .material-content {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      width: 475px;
      height: 165px;

      // margin-top: 28px;
      .content-item {
        display: flex;
        width: 150px;
        height: 50px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        font-weight: 400;
        color: #c3edff;
        .icon {
          width: 53px;
          height: 48px;
        }
        .details {
          display: flex;
          flex-flow: column wrap;
          margin-left: 8px;
          .name {
            font-family: 'Source Han Sans CN';
            font-size: 16px;
            font-weight: 400;
            line-height: 22px;
            color: #d8f3ff;
          }
          .num {
            margin-right: 3px;
            font-family: PangMenZhengDao;
            font-size: 24px;
            font-weight: 400;
            color: #48e6ff;
          }
          .unit {
            font-family: 'Source Han Sans CN';
            font-size: 14px;
            font-weight: 400;
            color: #c3edff;
          }
        }
      }
    }
  }
</style>
