import { fabric } from 'fabric'

export interface DragOptions {
  // Canvas实例
  canvas: fabric.Canvas

  // 目标区域选择器
  dropZoneSelector: string

  // 拖拽条件检查函数
  canDrag?: (obj: fabric.Object) => boolean

  // 拖拽成功回调
  onDrop?: (data: DragData, zoneId: string, zoneElement: Element) => void

  // 拖拽开始回调
  onDragStart?: (obj: fabric.Object, data: DragData) => void

  // 拖拽结束回调
  onDragEnd?: (success: boolean, data?: DragData, zoneId?: string) => void

  // 区域变化回调（鼠标移动时实时调用）
  onZoneChange?: (zoneId: string | null) => void

  // 自定义数据提取函数
  getCustomData?: (obj: fabric.Object) => any

  // 预览元素样式配置
  previewStyle?: Partial<CSSStyleDeclaration>

  // 是否启用调试日志
  debug?: boolean
}

export interface DragData {
  source: string
  object: any
  timestamp: number
  originalObject?: fabric.Object
}

export class CanvasDragHandler {
  private canvas: fabric.Canvas
  private options: Required<DragOptions>
  private dragPreviewElement: HTMLElement | null = null
  private currentDragObject: fabric.Object | null = null
  private dragData: DragData | null = null
  private isMouseDown = false
  private dragStartTimer: any = null
  private globalMouseUpHandler: ((e: MouseEvent) => void) | null = null

  constructor(options: DragOptions) {
    this.canvas = options.canvas
    this.options = {
      dropZoneSelector: options.dropZoneSelector,
      canDrag: options.canDrag || this.defaultCanDrag,
      onDrop: options.onDrop || this.defaultOnDrop,
      onDragStart: options.onDragStart || (() => {}),
      onDragEnd: options.onDragEnd || (() => {}),
      onZoneChange: options.onZoneChange || (() => {}),
      getCustomData: options.getCustomData || this.defaultGetCustomData,
      previewStyle: options.previewStyle || {},
      debug: options.debug || false,
      canvas: options.canvas
    }

    this.init()
  }

  private log(...args: any[]) {
    if (this.options.debug) {
      console.log('[CanvasDragHandler]', ...args)
    }
  }

  private defaultCanDrag(obj: fabric.Object): boolean {
    // 默认检查isCamera属性
    return !!(obj as any).isCamera
  }

  private defaultGetCustomData(obj: fabric.Object): any {
    return (
      (obj as any).customData || {
        id: `object-${Date.now()}`,
        type: obj.type || 'unknown',
        properties: {
          left: obj.left,
          top: obj.top,
          width: obj.width,
          height: obj.height
        }
      }
    )
  }

  private defaultOnDrop(data: DragData, zoneId: string, zoneElement: Element) {
    this.log('Default drop handler - data:', data, 'zoneId:', zoneId)
  }

  private init() {
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this))
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this))
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this))
  }

  private handleMouseDown(e: fabric.IEvent) {
    this.log('Mouse down event triggered')
    const activeObject = this.canvas.getActiveObject()

    if (!activeObject || !e.e) {
      this.log('No active object or event data')
      return
    }

    // 检查是否可以拖拽
    if (!this.options.canDrag(activeObject)) {
      this.log('Object cannot be dragged')
      return
    }

    this.log('Starting drag process for object:', activeObject.type)
    this.isMouseDown = true
    this.currentDragObject = activeObject

    const startX = e.e.clientX
    const startY = e.e.clientY

    // 立即创建拖拽预览元素
    this.createDragPreviewElement(activeObject, startX, startY)

    // 设置延时开始自定义拖拽
    this.dragStartTimer = setTimeout(() => {
      if (this.isMouseDown && this.currentDragObject) {
        this.log('Starting drag after timeout')
        this.startDragFromCanvas(this.currentDragObject, startX, startY)
      }
    }, 200)
  }

  private handleMouseUp() {
    this.log('Canvas mouse up event')
    this.isMouseDown = false

    if (this.dragStartTimer) {
      clearTimeout(this.dragStartTimer)
      this.dragStartTimer = null
    }
  }

  private handleMouseMove(e: fabric.IEvent) {
    if (!this.isMouseDown || !e.e) return

    // 更新拖拽预览元素位置
    this.updateDragPreviewPosition(e.e.clientX, e.e.clientY)

    // 如果鼠标移动距离较大，立即开始自定义拖拽
    if (this.currentDragObject && this.dragStartTimer) {
      this.log('Mouse moved, starting drag immediately')
      clearTimeout(this.dragStartTimer)
      this.dragStartTimer = null
      this.startDragFromCanvas(this.currentDragObject, e.e.clientX, e.e.clientY)
    }
  }

  private createDragPreviewElement(obj: fabric.Object, clientX: number, clientY: number) {
    this.log('Creating drag preview element')

    const customData = this.options.getCustomData(obj)

    // 清除已存在的预览元素
    this.clearDragPreviewElement(false)

    this.dragPreviewElement = this.createDragPreview(obj, customData)
    document.body.appendChild(this.dragPreviewElement)
    this.updateDragPreviewPosition(clientX, clientY)

    // 设置拖拽数据
    this.dragData = {
      source: 'canvas',
      object: customData,
      timestamp: Date.now(),
      originalObject: obj
    }

    // 触发拖拽开始回调
    this.options.onDragStart(obj, this.dragData)

    // 添加拖拽状态
    document.body.classList.add('canvas-dragging')
  }

  private createDragPreview(obj: fabric.Object, customData: any): HTMLElement {
    const preview = document.createElement('div')
    preview.className = 'canvas-drag-preview'

    // 设置基础样式
    Object.assign(preview.style, {
      position: 'fixed',
      pointerEvents: 'none',
      zIndex: '9999',
      width: '80px',
      height: '80px',
      borderRadius: '8px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px',
      fontWeight: 'bold',
      color: 'white',
      textAlign: 'center',
      boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
      transform: 'rotate(5deg)',
      transition: 'transform 0.2s ease',
      border: '2px solid rgba(255,255,255,0.3)',
      backgroundColor: this.getElementColor(obj, customData)
    })

    // 应用自定义样式
    Object.assign(preview.style, this.options.previewStyle)

    // preview.innerHTML = `
    //   <div style="font-size: 10px; opacity: 0.9; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">${customData.type || obj.type}</div>
    //   <div style="font-size: 8px; margin-top: 2px; opacity: 0.7; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">${customData.id || 'object'}</div>
    //   <div style="font-size: 6px; margin-top: 2px; opacity: 0.5;">拖拽中...</div>
    // `
    preview.innerHTML = `<img src="http://172.22.51.1:10000/stec-platform-doc/img/rBYzA2hBErGEUbOWAAAAABYOMuc220.png" style="width: 100%; height: 100%;">`

    return preview
  }

  private getElementColor(obj: fabric.Object, customData: any): string {
    if (obj.fill && typeof obj.fill === 'string') {
      return obj.fill
    }

    const colors: Record<string, string> = {
      rectangle: '#ff6b6b',
      rect: '#ff6b6b',
      circle: '#4ecdc4',
      text: '#45b7d1',
      triangle: '#96ceb4'
    }

    return colors[customData.type] || colors[obj.type || ''] || '#007bff'
  }

  private updateDragPreviewPosition(clientX: number, clientY: number) {
    if (this.dragPreviewElement && this.dragPreviewElement.parentNode) {
      this.dragPreviewElement.style.left = clientX + 10 + 'px'
      this.dragPreviewElement.style.top = clientY + 10 + 'px'

      // 检查当前区域并触发回调
      const targetResult = this.checkDropZone(clientX, clientY)
      this.options.onZoneChange(targetResult?.zoneId || null)
    }
  }

  private startDragFromCanvas(obj: fabric.Object, clientX: number, clientY: number) {
    if (!this.dragPreviewElement) {
      this.log('No drag preview element found!')
      return
    }

    this.log('Adding global mouseup listener')

    // 添加全局鼠标抬起监听器
    this.globalMouseUpHandler = (e: MouseEvent) => {
      this.log('Global mouse up detected at:', e.clientX, e.clientY)

      // 检查是否在目标区域内
      const targetResult = this.checkDropZone(e.clientX, e.clientY)

      if (targetResult && this.dragData) {
        this.log('Triggering drop for zone:', targetResult.zoneId)
        this.options.onDrop(this.dragData, targetResult.zoneId, targetResult.element)
        this.options.onDragEnd(true, this.dragData, targetResult.zoneId)
      } else {
        this.log('No valid drop target found')
        this.options.onDragEnd(false, this.dragData)
      }

      // 清除监听器和预览元素
      this.cleanup()
    }

    document.addEventListener('mouseup', this.globalMouseUpHandler)
  }

  private checkDropZone(clientX: number, clientY: number): { zoneId: string; element: Element } | null {
    const dropZones = document.querySelectorAll(this.options.dropZoneSelector)
    this.log(`Checking drop zones for position (${clientX}, ${clientY}), found ${dropZones.length} zones`)

    for (let i = 0; i < dropZones.length; i++) {
      const zone = dropZones[i]
      const rect = zone.getBoundingClientRect()

      if (clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom) {
        // 尝试从类名或数据属性获取zone ID
        const zoneId = this.getZoneId(zone, i)
        this.log(`Found target zone: ${zoneId}`)
        return { zoneId, element: zone }
      }
    }

    this.log('No target zone found')
    return null
  }

  private getZoneId(element: Element, index: number): string {
    // 尝试从data-zone-id属性获取
    const dataZoneId = element.getAttribute('data-zone-id')
    if (dataZoneId) return dataZoneId

    // 尝试从类名中提取（如zone-1, zone-2等）
    const classList = Array.from(element.classList)
    const zoneClass = classList.find((cls) => cls.startsWith('zone-'))
    if (zoneClass) return zoneClass

    // 如果都没有，使用索引
    return `zone-${index}`
  }

  private clearDragPreviewElement(resetCurrentObject = true) {
    if (this.dragPreviewElement && this.dragPreviewElement.parentNode) {
      document.body.removeChild(this.dragPreviewElement)
    }

    this.dragPreviewElement = null
    this.dragData = null

    if (resetCurrentObject) {
      this.currentDragObject = null
    }

    document.body.classList.remove('canvas-dragging')
  }

  private cleanup() {
    if (this.globalMouseUpHandler) {
      document.removeEventListener('mouseup', this.globalMouseUpHandler)
      this.globalMouseUpHandler = null
    }

    this.clearDragPreviewElement()
  }

  // 公共方法：手动设置拖拽条件
  public setCanDragFunction(canDragFn: (obj: fabric.Object) => boolean) {
    this.options.canDrag = canDragFn
  }

  // 公共方法：手动设置drop处理器
  public setDropHandler(onDropFn: (data: DragData, zoneId: string, zoneElement: Element) => void) {
    this.options.onDrop = onDropFn
  }

  // 公共方法：启用/禁用拖拽功能
  public setEnabled(enabled: boolean) {
    if (enabled) {
      this.init()
    } else {
      this.destroy()
    }
  }

  // 公共方法：销毁拖拽处理器
  public destroy() {
    this.canvas.off('mouse:down', this.handleMouseDown.bind(this))
    this.canvas.off('mouse:up', this.handleMouseUp.bind(this))
    this.canvas.off('mouse:move', this.handleMouseMove.bind(this))

    this.cleanup()

    if (this.dragStartTimer) {
      clearTimeout(this.dragStartTimer)
      this.dragStartTimer = null
    }
  }
}

// 全局样式（需要在CSS中定义或通过JS注入）
export const injectDragStyles = () => {
  const styleId = 'canvas-drag-handler-styles'
  if (document.getElementById(styleId)) return

  const style = document.createElement('style')
  style.id = styleId
  style.textContent = `
    .canvas-drag-preview {
      animation: canvas-drag-pulse 0.6s ease-in-out infinite alternate;
    }

    @keyframes canvas-drag-pulse {
      from {
        transform: rotate(3deg) scale(1);
      }
      to {
        transform: rotate(-3deg) scale(1.05);
      }
    }

    body.canvas-dragging {
      cursor: grabbing !important;
      user-select: none;
    }

    body.canvas-dragging * {
      cursor: grabbing !important;
    }
  `

  document.head.appendChild(style)
}
