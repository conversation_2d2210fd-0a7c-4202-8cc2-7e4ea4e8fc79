<template>
  <div class="car-rate-container">
    <!-- :resultList="resultList.slice(resultList.length - 4, resultList.length)" -->
    <TabsRate :type="type" :date="date" :lineName="lineName" :carItemTop="carItemTop" :resultList="resultList"></TabsRate>
    <div class="relative">
      <MyChart width="635px" height="418px" :option="options" style="position: sticky; z-index: 3; margin-top: 10px"></MyChart>
      <div class="chartBg"></div>
    </div>
  </div>
</template>

<script setup>
  import * as echarts from 'echarts'
  import moment from 'moment'

  import TabsRate from './Components/tabsRate.vue'
  import MyChart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib.ts'
  import CreateMqtt from '@/utils/mqtt.js'
  const props = defineProps({
    type: {
      type: String,
      default: 'day'
    },
    lineName: {
      type: String,
      default: '全部'
    },
    date: {
      type: String,
      default: ''
    },
    resultList: {
      type: Array,
      default: () => []
    },
    queryParams: {
      type: Object,
      default: () => {}
    }
  })
  const options = ref({
    grid: {
      top: '40px',
      bottom: '38px',
      left: '0px',
      right: '60px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis'
    },
    // legend: {
    //   top: '20',
    //   right: '50',
    //   data: ['计划执行率'],
    //   textStyle: {
    //     color: '#fff',
    //     fontSize: 14
    //   }
    // },
    xAxis: {
      type: 'category',
      boundaryGap: true, // 坐标轴两边留白
      // data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
      data: [],
      axisLabel: {
        // interval: 0,
        //	margin:15,
        textStyle: {
          color: '#ffffff',
          fontSize: 16
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '辆',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 16
        },
        splitNumber: 5,
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 16
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.2)',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      {
        type: 'value',
        min: 0,
        max: 2,
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 16
          }
        },
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '流量',
        yAxisIndex: 0,
        type: 'line',
        smooth: false,
        symbolSize: 5,
        connectNulls: true,
        itemStyle: {
          normal: {
            color: '#40e993',
            lineStyle: {
              color: '#40e993',
              width: 2
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(11, 255, 153, 0)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 233, 147, 0.24)'
                }
              ])
            }
          }
        },
        data: []
      },
      {
        name: '客货比',
        yAxisIndex: 1,
        type: 'line',
        smooth: false,
        symbolSize: 5,
        yAxisIndex: 1,
        connectNulls: true,
        itemStyle: {
          normal: {
            color: '#BED871',
            lineStyle: {
              color: '#BED871',
              width: 2
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(11, 255, 153, 0)'
                },
                {
                  offset: 1,
                  color: 'rgba(190, 216, 113, 0.25)'
                }
              ])
            }
          }
        },
        data: []
      }
    ]
  })
  const listCar = ref(null)
  const carItemTop = ref()
  const PublicMqtt = ref(null)
  const selectedCarType = ref('总车辆') // 添加选中车辆类型状态
  const typeSelectedData = ref()
  // 车辆类型配置
  const carTypeConfig = {
    客车: [1, 2, 3, 4],
    货车: [11, 12, 13, 14, 15, 16],
    专项作业车: [21, 22, 23, 24, 25, 26]
  }

  // 处理车辆类型选择的通用函数
  const handleCarTypeSelection = async (carType) => {
    if (carType === '总车辆') {
      carItemTop.value = listCar.value?.['总车辆']
      await trafficlist()
      return
    }

    const carTypeIds = carTypeConfig[carType]
    if (!carTypeIds) return

    carItemTop.value = listCar.value?.[carType]
    
    if (carType === '客车') {
      await trafficlist({ carTypeIds })
      await trafficlist({ carTypeIds: carTypeConfig['货车'] }, 1)
    } else {
      await trafficlist({ carTypeIds })
    }
  }

  // 选中tab展示对应的数据
  lib.bus.trafficMonitorTab.on(async (data) => {
    typeSelectedData.value = data
    selectedCarType.value = data.selected ? data.name : '总车辆'
    await handleCarTypeSelection(selectedCarType.value)
  })
  // 取消订阅
  const unsubscribe = () => {
    // 如果页面并没有初始化MQTT，无需取消订阅
    if (PublicMqtt.value) {
      PublicMqtt.value.unsubscribes()
      PublicMqtt.value.over()
    }
  }
  onUnmounted(() => {
    lib.bus.trafficMonitorChart.reset()
    lib.bus.trafficMonitorTab.reset()
    unsubscribe()
  })
  // // 计算比值的函数
  const calculateRatios = (array1, array2) => {
    if (array1.length !== array2.length) {
      throw new Error('两个数组的长度必须相同')
    }

    const ratios = []
    for (let i = 0; i < array1.length; i++) {
      if (array2[i] === 0) {
        throw new Error('除数不能为零')
      }
      ratios.push((array1[i] / array2[i]).toFixed(2))
    }
    return ratios
  }
  // 折线图接口
  const trafficlist = async (obj = {}, index = 0) => {
    options.value.series[1].data = []
    // 仅当天需要实时推流(东西线切换也是调接口) 选中车辆类型也是调接口
    const isCurrentDay = props.queryParams.period === 'day' && props.queryParams.startTime === moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    const isUseMqtt = isCurrentDay && props.lineName === '全部' && selectedCarType.value === '总车辆'
    if (!isUseMqtt) {
      unsubscribe()
      await lib.api.bigscreenApi.trafficlist({ ...props.queryParams, ...obj }).then((res) => {
        if (res.success) {
          setResult(res.result || [], index)
        }
      })
    } else {
      getDataByMqtt(index)
    }
  }
  const getDataByMqtt = (index) => {
    PublicMqtt.value = new CreateMqtt('trafficList')
    PublicMqtt.value.init()
    PublicMqtt.value.link()
    // 发送消息
    getMessage(index)
  }
  const getMessage = (index) => {
    // 接收消息
    PublicMqtt.value.client.on('message', (topic, message) => {
      try {
        const str = JSON.parse(message.toString())
        if (message) {
          setResult(str || [], index)
        }
      } catch (error) {
        console.error('数据解析失败:', error)
      }
    })
  }
  const setResult = (result, index) => {
    const formatDay = {
      day: 'HH',
      month: 'D日',
      year: 'M月'
    }
    const data = {
      xAxisData: result.map((_) => moment(_.time).format(formatDay[props.queryParams.period])),
      seriesData: result.map((_) => _.value)
    }
    if (index == 0) {
      options.value.yAxis[1].show = false
      options.value.xAxis.data = data.xAxisData
      options.value.series[0].data = data.seriesData
    }
    if (index == 1) {
      options.value.yAxis[1].show = true
      options.value.series[1].data = calculateRatios(options.value.series[0].data, data.seriesData).filter((item) => !isNaN(item))
    }
  }
  watch(
    () => props.queryParams,
    async (value) => {
      await handleCarTypeSelection(selectedCarType.value)
    },
    {
      immediate: true,
      deep: true
    }
  )
  watch(
    () => props.resultList,
    (value) => {
      if (value.length) {
        listCar.value = value.slice(value.length - 4).reduce((total, item) => {
          total[item.name] = item
          return total
        }, {})
        // 保持当前选中的车辆类型，而不是总是重置为总车辆
        if (listCar.value && selectedCarType.value) {
          carItemTop.value = listCar.value[selectedCarType.value] || listCar.value['总车辆']
        }
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<style lang="scss" scoped>
  .car-rate-container {
    width: 580px;
    height: 430px;
    .chartBg {
      position: absolute;
      bottom: 8px;
      z-index: 1;
      width: 593px;
      height: 63px;
      pointer-events: none;
      background: url('@/assets/ScreenRight/Perception/Traffic/lineBg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
