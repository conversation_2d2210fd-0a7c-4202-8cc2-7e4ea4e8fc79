import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import FeatureDetection from"../../Core/FeatureDetection.js";import knockout from"../../ThirdParty/knockout.js";import getElement from"../getElement.js";import GeocoderViewModel from"./GeocoderViewModel.js";var startSearchPath="M29.772,26.433l-7.126-7.126c0.96-1.583,1.523-3.435,1.524-5.421C24.169,8.093,19.478,3.401,13.688,3.399C7.897,3.401,3.204,8.093,3.204,13.885c0,5.789,4.693,10.481,10.484,10.481c1.987,0,3.839-0.563,5.422-1.523l7.128,7.127L29.772,26.433zM7.203,13.885c0.006-3.582,2.903-6.478,6.484-6.486c3.579,0.008,6.478,2.904,6.484,6.486c-0.007,3.58-2.905,6.476-6.484,6.484C10.106,20.361,7.209,17.465,7.203,13.885z",stopSearchPath="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z";function Geocoder(e){if(!defined(e)||!defined(e.container))throw new DeveloperError("options.container is required.");if(!defined(e.scene))throw new DeveloperError("options.scene is required.");var t=getElement(e.container),n=new GeocoderViewModel(e);n._startSearchPath=startSearchPath,n._stopSearchPath=stopSearchPath;var o=document.createElement("form");o.setAttribute("data-bind","submit: search");var r=document.createElement("input");r.type="search",r.className="cesium-geocoder-input",r.setAttribute("placeholder","Enter an address or landmark..."),r.setAttribute("data-bind",'textInput: searchText,disable: isSearchInProgress,event: { keyup: handleKeyUp, keydown: handleKeyDown, mouseover: deselectSuggestion },css: { "cesium-geocoder-input-wide" : keepExpanded || searchText.length > 0 },hasFocus: _focusTextbox'),this._onTextBoxFocus=function(){setTimeout((function(){r.select()}),0)},r.addEventListener("focus",this._onTextBoxFocus,!1),o.appendChild(r),this._textBox=r;var i=document.createElement("span");i.className="cesium-geocoder-searchButton",i.setAttribute("data-bind","click: search,cesiumSvgPath: { path: isSearchInProgress ? _stopSearchPath : _startSearchPath, width: 32, height: 32 }"),o.appendChild(i),t.appendChild(o);var s=document.createElement("div");s.className="search-results",s.setAttribute("data-bind","visible: _suggestionsVisible");var a=document.createElement("ul");a.setAttribute("data-bind","foreach: _suggestions");var d=document.createElement("li");a.appendChild(d),d.setAttribute("data-bind","text: $data.displayName, click: $parent.activateSuggestion, event: { mouseover: $parent.handleMouseover}, css: { active: $data === $parent._selectedSuggestion }"),s.appendChild(a),t.appendChild(s),knockout.applyBindings(n,o),knockout.applyBindings(n,s),this._container=t,this._searchSuggestionsContainer=s,this._viewModel=n,this._form=o,this._onInputBegin=function(e){var o=e.target;"function"===typeof e.composedPath&&(o=e.composedPath()[0]),t.contains(o)||(n._focusTextbox=!1,n.hideSuggestions())},this._onInputEnd=function(e){n._focusTextbox=!0,n.showSuggestions()},FeatureDetection.supportsPointerEvents()?(document.addEventListener("pointerdown",this._onInputBegin,!0),t.addEventListener("pointerup",this._onInputEnd,!0),t.addEventListener("pointercancel",this._onInputEnd,!0)):(document.addEventListener("mousedown",this._onInputBegin,!0),t.addEventListener("mouseup",this._onInputEnd,!0),document.addEventListener("touchstart",this._onInputBegin,!0),t.addEventListener("touchend",this._onInputEnd,!0),t.addEventListener("touchcancel",this._onInputEnd,!0))}Object.defineProperties(Geocoder.prototype,{container:{get:function(){return this._container}},searchSuggestionsContainer:{get:function(){return this._searchSuggestionsContainer}},viewModel:{get:function(){return this._viewModel}}}),Geocoder.prototype.isDestroyed=function(){return!1},Geocoder.prototype.destroy=function(){var e=this._container;return FeatureDetection.supportsPointerEvents()?(document.removeEventListener("pointerdown",this._onInputBegin,!0),e.removeEventListener("pointerup",this._onInputEnd,!0)):(document.removeEventListener("mousedown",this._onInputBegin,!0),e.removeEventListener("mouseup",this._onInputEnd,!0),document.removeEventListener("touchstart",this._onInputBegin,!0),e.removeEventListener("touchend",this._onInputEnd,!0)),this._viewModel.destroy(),knockout.cleanNode(this._form),knockout.cleanNode(this._searchSuggestionsContainer),e.removeChild(this._form),e.removeChild(this._searchSuggestionsContainer),this._textBox.removeEventListener("focus",this._onTextBoxFocus,!1),destroyObject(this)};export default Geocoder;