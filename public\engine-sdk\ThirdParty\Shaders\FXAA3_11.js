export default"#if (FXAA_QUALITY_PRESET == 10)\n#define FXAA_QUALITY_PS 3\n#define FXAA_QUALITY_P0 1.5\n#define FXAA_QUALITY_P1 3.0\n#define FXAA_QUALITY_P2 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 11)\n#define FXAA_QUALITY_PS 4\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 3.0\n#define FXAA_QUALITY_P3 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 12)\n#define FXAA_QUALITY_PS 5\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 4.0\n#define FXAA_QUALITY_P4 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 13)\n#define FXAA_QUALITY_PS 6\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 4.0\n#define FXAA_QUALITY_P5 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 14)\n#define FXAA_QUALITY_PS 7\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 4.0\n#define FXAA_QUALITY_P6 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 15)\n#define FXAA_QUALITY_PS 8\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 4.0\n#define FXAA_QUALITY_P7 12.0\n#endif\n#if (FXAA_QUALITY_PRESET == 20)\n#define FXAA_QUALITY_PS 3\n#define FXAA_QUALITY_P0 1.5\n#define FXAA_QUALITY_P1 2.0\n#define FXAA_QUALITY_P2 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 21)\n#define FXAA_QUALITY_PS 4\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 22)\n#define FXAA_QUALITY_PS 5\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 23)\n#define FXAA_QUALITY_PS 6\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 24)\n#define FXAA_QUALITY_PS 7\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 3.0\n#define FXAA_QUALITY_P6 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 25)\n#define FXAA_QUALITY_PS 8\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 4.0\n#define FXAA_QUALITY_P7 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 26)\n#define FXAA_QUALITY_PS 9\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 2.0\n#define FXAA_QUALITY_P7 4.0\n#define FXAA_QUALITY_P8 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 27)\n#define FXAA_QUALITY_PS 10\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 2.0\n#define FXAA_QUALITY_P7 2.0\n#define FXAA_QUALITY_P8 4.0\n#define FXAA_QUALITY_P9 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 28)\n#define FXAA_QUALITY_PS 11\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 2.0\n#define FXAA_QUALITY_P7 2.0\n#define FXAA_QUALITY_P8 2.0\n#define FXAA_QUALITY_P9 4.0\n#define FXAA_QUALITY_P10 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 29)\n#define FXAA_QUALITY_PS 12\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.5\n#define FXAA_QUALITY_P2 2.0\n#define FXAA_QUALITY_P3 2.0\n#define FXAA_QUALITY_P4 2.0\n#define FXAA_QUALITY_P5 2.0\n#define FXAA_QUALITY_P6 2.0\n#define FXAA_QUALITY_P7 2.0\n#define FXAA_QUALITY_P8 2.0\n#define FXAA_QUALITY_P9 2.0\n#define FXAA_QUALITY_P10 4.0\n#define FXAA_QUALITY_P11 8.0\n#endif\n#if (FXAA_QUALITY_PRESET == 39)\n#define FXAA_QUALITY_PS 12\n#define FXAA_QUALITY_P0 1.0\n#define FXAA_QUALITY_P1 1.0\n#define FXAA_QUALITY_P2 1.0\n#define FXAA_QUALITY_P3 1.0\n#define FXAA_QUALITY_P4 1.0\n#define FXAA_QUALITY_P5 1.5\n#define FXAA_QUALITY_P6 2.0\n#define FXAA_QUALITY_P7 2.0\n#define FXAA_QUALITY_P8 2.0\n#define FXAA_QUALITY_P9 2.0\n#define FXAA_QUALITY_P10 4.0\n#define FXAA_QUALITY_P11 8.0\n#endif\n#define FxaaBool bool\n#define FxaaFloat float\n#define FxaaFloat2 vec2\n#define FxaaFloat3 vec3\n#define FxaaFloat4 vec4\n#define FxaaHalf float\n#define FxaaHalf2 vec2\n#define FxaaHalf3 vec3\n#define FxaaHalf4 vec4\n#define FxaaInt2 vec2\n#define FxaaTex sampler2D\n#define FxaaSat(x) clamp(x, 0.0, 1.0)\n#define FxaaTexTop(t, p) texture2D(t, p)\n#define FxaaTexOff(t, p, o, r) texture2D(t, p + (o * r))\nFxaaFloat FxaaLuma(FxaaFloat4 rgba) { return rgba.y; }\nFxaaFloat4 FxaaPixelShader(\nFxaaFloat2 pos,\nFxaaTex tex,\nFxaaFloat2 fxaaQualityRcpFrame,\nFxaaFloat fxaaQualitySubpix,\nFxaaFloat fxaaQualityEdgeThreshold,\nFxaaFloat fxaaQualityEdgeThresholdMin\n) {\nFxaaFloat2 posM;\nposM.x = pos.x;\nposM.y = pos.y;\nFxaaFloat4 rgbyM = FxaaTexTop(tex, posM);\n#define lumaM rgbyM.y\nFxaaFloat lumaS = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2( 0, 1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaE = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2( 1, 0), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaN = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2( 0,-1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaW = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2(-1, 0), fxaaQualityRcpFrame.xy));\nFxaaFloat maxSM = max(lumaS, lumaM);\nFxaaFloat minSM = min(lumaS, lumaM);\nFxaaFloat maxESM = max(lumaE, maxSM);\nFxaaFloat minESM = min(lumaE, minSM);\nFxaaFloat maxWN = max(lumaN, lumaW);\nFxaaFloat minWN = min(lumaN, lumaW);\nFxaaFloat rangeMax = max(maxWN, maxESM);\nFxaaFloat rangeMin = min(minWN, minESM);\nFxaaFloat rangeMaxScaled = rangeMax * fxaaQualityEdgeThreshold;\nFxaaFloat range = rangeMax - rangeMin;\nFxaaFloat rangeMaxClamped = max(fxaaQualityEdgeThresholdMin, rangeMaxScaled);\nFxaaBool earlyExit = range < rangeMaxClamped;\nif(earlyExit)\nreturn rgbyM;\nFxaaFloat lumaNW = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2(-1,-1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaSE = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2( 1, 1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaNE = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2( 1,-1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaSW = FxaaLuma(FxaaTexOff(tex, posM, FxaaInt2(-1, 1), fxaaQualityRcpFrame.xy));\nFxaaFloat lumaNS = lumaN + lumaS;\nFxaaFloat lumaWE = lumaW + lumaE;\nFxaaFloat subpixRcpRange = 1.0/range;\nFxaaFloat subpixNSWE = lumaNS + lumaWE;\nFxaaFloat edgeHorz1 = (-2.0 * lumaM) + lumaNS;\nFxaaFloat edgeVert1 = (-2.0 * lumaM) + lumaWE;\nFxaaFloat lumaNESE = lumaNE + lumaSE;\nFxaaFloat lumaNWNE = lumaNW + lumaNE;\nFxaaFloat edgeHorz2 = (-2.0 * lumaE) + lumaNESE;\nFxaaFloat edgeVert2 = (-2.0 * lumaN) + lumaNWNE;\nFxaaFloat lumaNWSW = lumaNW + lumaSW;\nFxaaFloat lumaSWSE = lumaSW + lumaSE;\nFxaaFloat edgeHorz4 = (abs(edgeHorz1) * 2.0) + abs(edgeHorz2);\nFxaaFloat edgeVert4 = (abs(edgeVert1) * 2.0) + abs(edgeVert2);\nFxaaFloat edgeHorz3 = (-2.0 * lumaW) + lumaNWSW;\nFxaaFloat edgeVert3 = (-2.0 * lumaS) + lumaSWSE;\nFxaaFloat edgeHorz = abs(edgeHorz3) + edgeHorz4;\nFxaaFloat edgeVert = abs(edgeVert3) + edgeVert4;\nFxaaFloat subpixNWSWNESE = lumaNWSW + lumaNESE;\nFxaaFloat lengthSign = fxaaQualityRcpFrame.x;\nFxaaBool horzSpan = edgeHorz >= edgeVert;\nFxaaFloat subpixA = subpixNSWE * 2.0 + subpixNWSWNESE;\nif(!horzSpan) lumaN = lumaW;\nif(!horzSpan) lumaS = lumaE;\nif(horzSpan) lengthSign = fxaaQualityRcpFrame.y;\nFxaaFloat subpixB = (subpixA * (1.0/12.0)) - lumaM;\nFxaaFloat gradientN = lumaN - lumaM;\nFxaaFloat gradientS = lumaS - lumaM;\nFxaaFloat lumaNN = lumaN + lumaM;\nFxaaFloat lumaSS = lumaS + lumaM;\nFxaaBool pairN = abs(gradientN) >= abs(gradientS);\nFxaaFloat gradient = max(abs(gradientN), abs(gradientS));\nif(pairN) lengthSign = -lengthSign;\nFxaaFloat subpixC = FxaaSat(abs(subpixB) * subpixRcpRange);\nFxaaFloat2 posB;\nposB.x = posM.x;\nposB.y = posM.y;\nFxaaFloat2 offNP;\noffNP.x = (!horzSpan) ? 0.0 : fxaaQualityRcpFrame.x;\noffNP.y = ( horzSpan) ? 0.0 : fxaaQualityRcpFrame.y;\nif(!horzSpan) posB.x += lengthSign * 0.5;\nif( horzSpan) posB.y += lengthSign * 0.5;\nFxaaFloat2 posN;\nposN.x = posB.x - offNP.x * FXAA_QUALITY_P0;\nposN.y = posB.y - offNP.y * FXAA_QUALITY_P0;\nFxaaFloat2 posP;\nposP.x = posB.x + offNP.x * FXAA_QUALITY_P0;\nposP.y = posB.y + offNP.y * FXAA_QUALITY_P0;\nFxaaFloat subpixD = ((-2.0)*subpixC) + 3.0;\nFxaaFloat lumaEndN = FxaaLuma(FxaaTexTop(tex, posN));\nFxaaFloat subpixE = subpixC * subpixC;\nFxaaFloat lumaEndP = FxaaLuma(FxaaTexTop(tex, posP));\nif(!pairN) lumaNN = lumaSS;\nFxaaFloat gradientScaled = gradient * 1.0/4.0;\nFxaaFloat lumaMM = lumaM - lumaNN * 0.5;\nFxaaFloat subpixF = subpixD * subpixE;\nFxaaBool lumaMLTZero = lumaMM < 0.0;\nlumaEndN -= lumaNN * 0.5;\nlumaEndP -= lumaNN * 0.5;\nFxaaBool doneN = abs(lumaEndN) >= gradientScaled;\nFxaaBool doneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P1;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P1;\nFxaaBool doneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P1;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P1;\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P2;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P2;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P2;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P2;\n#if (FXAA_QUALITY_PS > 3)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P3;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P3;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P3;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P3;\n#if (FXAA_QUALITY_PS > 4)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P4;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P4;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P4;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P4;\n#if (FXAA_QUALITY_PS > 5)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P5;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P5;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P5;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P5;\n#if (FXAA_QUALITY_PS > 6)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P6;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P6;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P6;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P6;\n#if (FXAA_QUALITY_PS > 7)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P7;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P7;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P7;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P7;\n#if (FXAA_QUALITY_PS > 8)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P8;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P8;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P8;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P8;\n#if (FXAA_QUALITY_PS > 9)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P9;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P9;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P9;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P9;\n#if (FXAA_QUALITY_PS > 10)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P10;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P10;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P10;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P10;\n#if (FXAA_QUALITY_PS > 11)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P11;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P11;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P11;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P11;\n#if (FXAA_QUALITY_PS > 12)\nif(doneNP) {\nif(!doneN) lumaEndN = FxaaLuma(FxaaTexTop(tex, posN.xy));\nif(!doneP) lumaEndP = FxaaLuma(FxaaTexTop(tex, posP.xy));\nif(!doneN) lumaEndN = lumaEndN - lumaNN * 0.5;\nif(!doneP) lumaEndP = lumaEndP - lumaNN * 0.5;\ndoneN = abs(lumaEndN) >= gradientScaled;\ndoneP = abs(lumaEndP) >= gradientScaled;\nif(!doneN) posN.x -= offNP.x * FXAA_QUALITY_P12;\nif(!doneN) posN.y -= offNP.y * FXAA_QUALITY_P12;\ndoneNP = (!doneN) || (!doneP);\nif(!doneP) posP.x += offNP.x * FXAA_QUALITY_P12;\nif(!doneP) posP.y += offNP.y * FXAA_QUALITY_P12;\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\n#endif\n}\nFxaaFloat dstN = posM.x - posN.x;\nFxaaFloat dstP = posP.x - posM.x;\nif(!horzSpan) dstN = posM.y - posN.y;\nif(!horzSpan) dstP = posP.y - posM.y;\nFxaaBool goodSpanN = (lumaEndN < 0.0) != lumaMLTZero;\nFxaaFloat spanLength = (dstP + dstN);\nFxaaBool goodSpanP = (lumaEndP < 0.0) != lumaMLTZero;\nFxaaFloat spanLengthRcp = 1.0/spanLength;\nFxaaBool directionN = dstN < dstP;\nFxaaFloat dst = min(dstN, dstP);\nFxaaBool goodSpan = directionN ? goodSpanN : goodSpanP;\nFxaaFloat subpixG = subpixF * subpixF;\nFxaaFloat pixelOffset = (dst * (-spanLengthRcp)) + 0.5;\nFxaaFloat subpixH = subpixG * fxaaQualitySubpix;\nFxaaFloat pixelOffsetGood = goodSpan ? pixelOffset : 0.0;\nFxaaFloat pixelOffsetSubpix = max(pixelOffsetGood, subpixH);\nif(!horzSpan) posM.x += pixelOffsetSubpix * lengthSign;\nif( horzSpan) posM.y += pixelOffsetSubpix * lengthSign;\nreturn FxaaFloat4(FxaaTexTop(tex, posM).xyz, lumaM);\n}\n";