<template>
  <div class="inspect-route-container">
    <div class="close-btn" @click="handleClose"></div>
    <div class="flex justify-around mt-80">
      <div class="w700 h630 relative flex flex-col justify-between">
        <WSPlayer
          width="700px"
          height="305px"
          url="wss://*************/stream/?url=rtsp://admin:xm123456@192.168.100.5:554/h265/ch1/sub/av_stream&width=1550&height=633&bitrate=800"></WSPlayer>
        <WSPlayer
          width="700px"
          height="305px"
          url="wss://*************/stream/?url=rtsp://admin:xm123456@*************:554/h264/ch2/main/av_stream&width=704&height=396&bitrate=800"></WSPlayer>
      </div>
      <div class="w700 h630 relative">
        <WSPlayer
          width="700px"
          height="633px"
          url="wss://*************/stream/?url=rtsp://admin:qjsd12345@***************:554/h265/ch1/sub/av_stream&width=276&height=135&bitrate=800"></WSPlayer>
      </div>
    </div>
  </div>
</template>

<script setup>
  import WSPlayer from '@/components/WSPlayer/index.vue'

  import lib from '@/utils/lib.ts'

  const handleClose = lib.provideTools.handleClose.inject()
</script>

<style lang="scss" scoped>
  .inspect-route-container {
    position: absolute;
    z-index: 999;
    width: 1689px;
    height: 784px;
    background-image: url('@/assets/StructureDiagram/robot/videoBg.png');
    background-size: cover;
    .close-btn {
      position: absolute;
      top: 5px;
      right: 10px;
      width: 50px;
      height: 50px;
      cursor: pointer;
    }
  }
</style>
