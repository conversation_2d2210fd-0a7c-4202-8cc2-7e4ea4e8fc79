/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AttributeCompression-d661357e","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./Color-bcdd0092","./defined-3b3eb2ba","./IndexDatatype-00859b8b","./Math-b5f4d889","./OrientedBoundingBox-e47c7a90","./createTaskProcessorWorker","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./RuntimeError-592f0d41","./Transforms-42ed7720","./Resource-41d99fe7","./combine-0bec9016","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,n,a,r,o,s,i,c,f,d,l,u,h,g,p,b,m,y){"use strict";const C=new t.Cartesian3,I=new n.Ellipsoid,w=new n.Rectangle,x={min:void 0,max:void 0,indexBytesPerElement:void 0};function A(e,t,n){const r=t.length,o=2+r*i.OrientedBoundingBox.packedLength+1+function(e){const t=e.length;let n=0;for(let r=0;r<t;++r)n+=a.Color.packedLength+3+e[r].batchIds.length;return n}(n),s=new Float64Array(o);let c=0;s[c++]=e,s[c++]=r;for(let e=0;e<r;++e)i.OrientedBoundingBox.pack(t[e],s,c),c+=i.OrientedBoundingBox.packedLength;const f=n.length;s[c++]=f;for(let e=0;e<f;++e){const t=n[e];a.Color.pack(t.color,s,c),c+=a.Color.packedLength,s[c++]=t.offset,s[c++]=t.count;const r=t.batchIds,o=r.length;s[c++]=o;for(let e=0;e<o;++e)s[c++]=r[e]}return s}const E=new t.Cartesian3,N=new t.Cartesian3,T=new t.Cartesian3,B=new t.Cartesian3,k=new t.Cartesian3,L=new n.Cartographic,O=new n.Rectangle;return c((function(c,f){let d;!function(e){const a=new Float64Array(e);let r=0;x.indexBytesPerElement=a[r++],x.min=a[r++],x.max=a[r++],t.Cartesian3.unpack(a,r,C),r+=t.Cartesian3.packedLength,n.Ellipsoid.unpack(a,r,I),r+=n.Ellipsoid.packedLength,n.Rectangle.unpack(a,r,w)}(c.packedBuffer),d=2===x.indexBytesPerElement?new Uint16Array(c.indices):new Uint32Array(c.indices);const l=new Uint16Array(c.positions),u=new Uint32Array(c.counts),h=new Uint32Array(c.indexCounts),g=new Uint32Array(c.batchIds),p=new Uint32Array(c.batchTableColors),b=new Array(u.length),m=C,y=I;let U=w;const P=x.min,R=x.max;let F,S,D,M=c.minimumHeights,_=c.maximumHeights;r.defined(M)&&r.defined(_)&&(M=new Float32Array(M),_=new Float32Array(_));const G=l.length/2,Y=l.subarray(0,G),V=l.subarray(G,2*G);e.AttributeCompression.zigZagDeltaDecode(Y,V);const v=new Float64Array(3*G);for(F=0;F<G;++F){const e=Y[F],a=V[F],r=s.CesiumMath.lerp(U.west,U.east,e/32767),o=s.CesiumMath.lerp(U.south,U.north,a/32767),i=n.Cartographic.fromRadians(r,o,0,L),c=y.cartographicToCartesian(i,E);t.Cartesian3.pack(c,v,3*F)}const H=u.length,W=new Array(H),z=new Array(H);let Z=0,j=0;for(F=0;F<H;++F)W[F]=Z,z[F]=j,Z+=u[F],j+=h[F];const q=new Float32Array(3*G*2),J=new Uint16Array(2*G),K=new Uint32Array(z.length),Q=new Uint32Array(h.length);let X=[];const $={};for(F=0;F<H;++F)D=p[F],r.defined($[D])?($[D].positionLength+=u[F],$[D].indexLength+=h[F],$[D].batchIds.push(F)):$[D]={positionLength:u[F],indexLength:h[F],offset:0,indexOffset:0,batchIds:[F]};let ee,te=0,ne=0;for(D in $)if($.hasOwnProperty(D)){ee=$[D],ee.offset=te,ee.indexOffset=ne;const e=2*ee.positionLength,t=2*ee.indexLength+6*ee.positionLength;te+=e,ne+=t,ee.indexLength=t}const ae=[];for(D in $)$.hasOwnProperty(D)&&(ee=$[D],ae.push({color:a.Color.fromRgba(parseInt(D)),offset:ee.indexOffset,count:ee.indexLength,batchIds:ee.batchIds}));for(F=0;F<H;++F){D=p[F],ee=$[D];const e=ee.offset;let n=3*e,a=e;const o=W[F],s=u[F],c=g[F];let f=P,l=R;r.defined(M)&&r.defined(_)&&(f=M[F],l=_[F]);let C=Number.POSITIVE_INFINITY,I=Number.NEGATIVE_INFINITY,w=Number.POSITIVE_INFINITY,x=Number.NEGATIVE_INFINITY;for(S=0;S<s;++S){const e=t.Cartesian3.unpack(v,3*o+3*S,E);y.scaleToGeodeticSurface(e,e);const r=y.cartesianToCartographic(e,L),s=r.latitude,i=r.longitude;C=Math.min(s,C),I=Math.max(s,I),w=Math.min(i,w),x=Math.max(i,x);const d=y.geodeticSurfaceNormal(e,N);let u=t.Cartesian3.multiplyByScalar(d,f,T);const h=t.Cartesian3.add(e,u,B);u=t.Cartesian3.multiplyByScalar(d,l,u);const g=t.Cartesian3.add(e,u,k);t.Cartesian3.subtract(g,m,g),t.Cartesian3.subtract(h,m,h),t.Cartesian3.pack(g,q,n),t.Cartesian3.pack(h,q,n+3),J[a]=c,J[a+1]=c,n+=6,a+=2}U=O,U.west=w,U.east=x,U.south=C,U.north=I,b[F]=i.OrientedBoundingBox.fromRectangle(U,P,R,y);let A=ee.indexOffset;const G=z[F],Y=h[F];for(K[F]=A,S=0;S<Y;S+=3){const t=d[G+S]-o,n=d[G+S+1]-o,a=d[G+S+2]-o;X[A++]=2*t+e,X[A++]=2*n+e,X[A++]=2*a+e,X[A++]=2*a+1+e,X[A++]=2*n+1+e,X[A++]=2*t+1+e}for(S=0;S<s;++S){const t=S,n=(S+1)%s;X[A++]=2*t+1+e,X[A++]=2*n+e,X[A++]=2*t+e,X[A++]=2*t+1+e,X[A++]=2*n+1+e,X[A++]=2*n+e}ee.offset+=2*s,ee.indexOffset=A,Q[F]=A-K[F]}X=o.IndexDatatype.createTypedArray(q.length/3,X);const re=ae.length;for(let e=0;e<re;++e){const t=ae[e].batchIds;let n=0;const a=t.length;for(let e=0;e<a;++e)n+=Q[t[e]];ae[e].count=n}const oe=A(2===X.BYTES_PER_ELEMENT?o.IndexDatatype.UNSIGNED_SHORT:o.IndexDatatype.UNSIGNED_INT,b,ae);return f.push(q.buffer,X.buffer,K.buffer,Q.buffer,J.buffer,oe.buffer),{positions:q.buffer,indices:X.buffer,indexOffsets:K.buffer,indexCounts:Q.buffer,batchIds:J.buffer,packedBuffer:oe.buffer}}))}));
