<template>
  <div class="switch-canvas-btns">
    <div class="switch-canvas-btn" :class="{ selected: item.selected }" v-for="item in list" :key="item.id" @click="handleClick(item)">{{ item.name }}</div>
  </div>
</template>

<script setup>
  const code = defineModel()
  const emits = defineEmits(['change'])

  const list = ref([
    { id: 5, name: '健康监测系统', code: 'QJSDJKJC', selected: true },
    { id: 6, name: '机器人巡检系统', code: 'QJSDJROBOT', selected: false },
    { id: 1, name: '综合监控系统', code: 'QJSDZHJK', selected: false },
    { id: 2, name: '通风系统', code: 'QJSDTFXT', selected: false },
    { id: 3, name: '排水系统', code: 'QJSDPSXT', selected: false }
    // { id: 4, name: '火灾报警和消防系统', code: 'QJSDHZBJXF', selected: false }
  ])

  const handleClick = (item) => {
    list.value.forEach((_) => {
      _.selected = _.id === item.id
    })
    code.value = selectedCode.value
    emits('change', selectedCode)
  }
  const selectedCode = computed(() => {
    return list.value.find((_) => _.selected).code
  })
</script>

<style lang="scss" scoped>
  .switch-canvas-btns {
    display: flex;
    justify-content: space-between;
    width: 1700px;
    height: 50px;
    .switch-canvas-btn {
      width: 248px;
      height: 49px;
      font-family: 'Alibaba PuHuiTi';
      font-size: 24px;
      font-weight: 400;
      line-height: 49px;
      color: #ffffff;
      text-align: center;
      cursor: pointer;
      background-image: url('@/assets/StructureDiagram/btn.png');
      background-size: contain;
      &.selected {
        text-shadow: 0 2px 4px #003c5e;
        background-image: url('@/assets/StructureDiagram/btnSelected.png');
      }
    }
  }
</style>
