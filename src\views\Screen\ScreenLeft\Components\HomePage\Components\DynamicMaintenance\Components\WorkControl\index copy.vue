<!--
 * @Author: lugege <EMAIL>
 * @Date: 2025-04-15 16:43:00
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-07-03 22:06:58
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\DynamicMaintenance\Components\WorkControl\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="work-control-container">
    <SubHeadLine>
      <div class="title">
        <span :class="tabCur === 0 ? 'select' : 'default'" @click="changeTab('click', 0)">作业管控</span>
        <div class="line"></div>
        <span :class="tabCur === 1 ? 'select' : 'default'" @click="changeTab('click', 1)">缺陷管理</span>
      </div>
      <DatePicker
        :type="tabCur === 1 ? 'month' : 'day'"
        v-model="date"
        :clearable="false"
        @change="handleDateChange"
        :disabled-date="disabledDate"
        style="top: 0; right: 0"></DatePicker>
    </SubHeadLine>
    <div class="tab">
      <div class="tab-item" :class="item.active ? 'active' : ''" v-for="(item, index) in tabList" :key="index" @click="handlerTab(item)">
        <div class="content">
          <img class="icon" :src="getAssetsFile(`ScreenLeft/DynamicMaintenance/${item.icon}.png`)" />
          <span>{{ tabCur === 0 ? item.name : item.defectName }}</span>
        </div>
        <div class="num" :style="{ color: item.color }">{{ item.num }}</div>
      </div>
    </div>
    <div class="chart-box">
      <div v-for="item in chartData" :key="item.id" class="chart-item">
        <SmallHeadLine>
          <template #title>{{ item.name }}</template>
          <Mychart :option="item.barData" width="250px" height="180px"></Mychart>
        </SmallHeadLine>
      </div>
    </div>
    <div class="popup-container" v-if="showPop">
      <WorkList :list="workList" :data="tabCur == 0 ? '作业列表' : '缺陷列表'" @close="showPop = false"></WorkList>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import DatePicker from '@/components/DatePicker/index.vue'
  import SmallHeadLine from '@/components/SmallHeadLine/index.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  import WorkList from '@/views/Screen/MapPopupWindow/Components/WorkList/index.vue'
  import Mychart from '@Common/components/MyChart/index.vue'

  import { getAssetsFile } from '@/utils'
  import lib from '@/utils/lib.ts'
  const tabCur = ref(0)
  const date = ref('')
  const showPop = ref(false)
  const tabList = ref([
    {
      name: '总数',
      defectName: '总数',
      icon: 'sumIcon',
      num: 0,
      color: '#ffffff',
      active: false
    },
    {
      name: '已完成',
      defectName: '已处理',
      icon: 'finishIcon',
      num: 0,
      color: '#00FF47',
      active: false
    },
    {
      name: '未完成',
      defectName: '未处理',
      icon: 'unfinishIcon',
      num: 0,
      color: '#FFB800',
      active: false
    }
  ])
  const workList = ref([])
  const planList = ref([])
  const defectList = ref([])
  const chartData = ref([])
  const planAndFinishCountMap = ref({})
  const defectAndRepairCountMap = ref({})
  const specialTypeMap = ref({
    nameList: [],
    dataList: []
  })
  const workPlanTypeMap = ref({
    nameList: [],
    dataList: []
  })
  const defectSourceDataMap = ref({
    nameList: [],
    dataList: []
  })
  const defectDistributionMap = ref({
    nameList: [],
    dataList: []
  })
  const colorList = [
    ['#0c6981', '#69D6CF'],
    ['#18578f', '#53C1FF'],
    ['#65705d', '#FFCF53'],
    ['#3e495f', '#FF9B53'],
    ['#2f3783', '#C853FF']
  ]
  let status = ''
  onMounted(() => {
    date.value = moment().format('YYYY-MM-DD')
    // getAllData()
    useIntervalFn(
      () => {
        getAllData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })
  const changeTab = (type, index) => {
    if (type == 'click') {
      tabList.value.forEach((data) => {
        data.active = false
      })
      showPop.value = false
    }
    tabCur.value = index
    if (index === 0) {
      tabList.value[0].num = planAndFinishCountMap.value.planCount
      tabList.value[1].num = planAndFinishCountMap.value.finishCount
      tabList.value[2].num = planAndFinishCountMap.value.unFinishCount
      chartData.value = [
        {
          id: 1,
          name: '作业单类型'
        },
        {
          id: 2,
          name: '专业类型'
        }
      ]
      chartData.value[0].barData = upDateYhBar(workPlanTypeMap.value.nameList, workPlanTypeMap.value.dataList)
      chartData.value[1].barData = upDateYhBar(specialTypeMap.value.nameList, specialTypeMap.value.dataList)
    } else {
      tabList.value[0].num = defectAndRepairCountMap.value.total
      tabList.value[1].num = defectAndRepairCountMap.value.repairCount
      tabList.value[2].num = defectAndRepairCountMap.value.total - defectAndRepairCountMap.value.repairCount
      chartData.value = [
        {
          id: 1,
          name: '缺陷来源'
        },
        {
          id: 2,
          name: '缺陷分类'
        }
      ]
      chartData.value[0].barData = upDateBhBar(defectSourceDataMap.value.nameList, defectSourceDataMap.value.dataList)
      chartData.value[1].barData = upDateBhBar(defectDistributionMap.value.nameList, defectDistributionMap.value.dataList)
    }
  }
  const handleDateChange = (val) => {
    getAllData()
  }
  const handlerTab = (item) => {
    status = item.name == '总数' ? '' : item.name == '未完成' || item.name == '未处理' ? 'unfinish' : 'finish'
    getData(status)
    tabList.value.forEach((data) => {
      data.active = item.name === data.name ? !data.active : false
    })
    showPop.value = tabList.value.some((_) => _.active)
    if (showPop.value) {
      workList.value = tabCur.value ? defectList.value.find((_) => _.name === item.defectName).list : planList.value.find((_) => _.name === item.name).list
    }
  }
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
  const upDateYhBar = (xData, barData) => {
    return {
      grid: {
        top: '18%',
        bottom: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          formatter: function (value) {
            return value.substring(0, 2) + '\n' + value.substring(2)
          }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#5C91C4'
          }
        },
        data: xData
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#5C91C4',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: 8,
          label: {
            show: true,
            formatter: '{a|}',
            position: 'top',
            distance: 0,
            color: '#fff',
            backgroundColor: '#fff',
            padding: 1,
            rich: {
              a: {
                width: 10,
                height: 1,
                backgroundColor: '#fff'
              }
            }
          },
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: colorList[params.dataIndex][1] // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorList[params.dataIndex][0] // 100% 处的颜色
                    }
                  ]
                }
              }
            }
          },
          data: barData
        }
      ]
    }
  }
  const upDateBhBar = (xData, barData) => {
    return {
      grid: {
        top: '18%',
        bottom: '5%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        axisLabel: {
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function (value) {
            return value.substring(0, 2) + '\n' + value.substring(2)
          }
        },
        axisTick: { show: false },
        axisLine: {
          lineStyle: {
            color: '#5C91C4'
          }
        },
        data: xData
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: {
            lineStyle: {
              color: '#5C91C4',
              type: 'dashed'
            }
          }
        }
      ],
      series: [
        {
          type: 'pictorialBar',
          symbol: 'rect',
          symbolRepeat: true,
          symbolSize: [10, 10],
          symbolMargin: 2,
          symbolPosition: 'start',
          z: -20,
          barGap: '10%',
          itemStyle: {
            normal: {
              color: function (params) {
                return {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: colorList[params.dataIndex][1] // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: colorList[params.dataIndex][0] // 100% 处的颜色
                    }
                  ]
                }
              }
            }
          },
          data: barData
        }
      ]
    }
  }
  const getData = (status) => {
    const startDate = moment(date.value).format('YYYY-MM-DD 00:00:00')
    const endDate = moment(date.value).format('YYYY-MM-DD 23:59:59')
    // 获取作业管控及缺陷管理数据
    lib.api.bigscreenApi.smartMaintenance({ startDate, endDate, status }).then((response) => {
      if (response.success) {
        planAndFinishCountMap.value = response.result.overallMaintenanceMap.key.planAndFinishCountMap
        const specialTypeMapList = response.result.overallMaintenanceMap.key.specialTypeMapList
        const workPlanTypeMapList = response.result.overallMaintenanceMap.key.workPlanTypeMapList
        defectAndRepairCountMap.value = response.result.defectRepairMap.key.defectAndRepairCountMap
        const defectSourceDataMapList = response.result.defectRepairMap.key.defectSourceDataMapList
        const defectDistributionMapList = response.result.defectRepairMap.key.defectDistributionMapList
        specialTypeMap.value.nameList = specialTypeMapList.map((_) => _.professionalName)
        specialTypeMap.value.dataList = specialTypeMapList.map((_) => _.count)
        workPlanTypeMap.value.nameList = workPlanTypeMapList.map((_) => _.workPlanTypeName)
        workPlanTypeMap.value.dataList = workPlanTypeMapList.map((_) => _.count)
        defectSourceDataMap.value.nameList = defectSourceDataMapList.map((_) => _.name)
        defectSourceDataMap.value.dataList = defectSourceDataMapList.map((_) => _.value)
        defectDistributionMap.value.nameList = defectDistributionMapList.map((_) => _.name)
        defectDistributionMap.value.dataList = defectDistributionMapList.map((_) => _.value)
      }
      changeTab('get', tabCur.value)
    })
  }
  const getAllData = () => {
    getData(status)
    // 获取作业列表数据
    lib.api.bigscreenApi.jobControl({ date: date.value }).then((response) => {
      if (response.success) {
        planList.value = response.result
      }
    })
    // 获取缺陷列表数据
    lib.api.bigscreenApi
      .getDefectOrders({
        startTime: moment(date.value).format('YYYY-MM-01 00:00:00'),
        endTime: moment(date.value).endOf('month').format('YYYY-MM-DD 23:59:59')
      })
      .then((response) => {
        if (response.success) {
          defectList.value = response.result
        }
      })
  }
</script>

<style lang="scss" scoped>
  .work-control-container {
    width: 617px;
    height: 349px;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 286px;
      height: 34px;
      .line {
        width: 2px;
        height: 24px;
        background: #276897;
        border-radius: 0;
      }
      span {
        &.select {
          font-family: 'Alibaba PuHuiTi', 'Alibaba PuHuiTi';
          font-size: 32px;
          font-weight: 500;
          color: #d5ebff;
          cursor: pointer;
        }
        &.default {
          font-family: 'Alibaba PuHuiTi', 'Alibaba PuHuiTi';
          font-size: 32px;
          font-weight: 400;
          color: #8ec1ef;
          cursor: pointer;
        }
      }
    }
    .tab {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .tab-item {
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 184px;
        height: 87px;
        cursor: pointer;
        background: url('@/assets/ScreenLeft/DynamicMaintenance/workControlTab.png');
        background-size: 184px 87px;
        &.active {
          background: url('@/assets/ScreenLeft/DynamicMaintenance/workControlTabSelected.png');
          background-size: 184px 87px;
        }
      }
      .content {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
          width: 22px;
          height: 26px;
        }
        span {
          margin-top: 9px;
          font-family: 'Alibaba PuHuiTi';
          font-size: 20px;
          font-weight: 400;
          line-height: 24px;
          color: #ffffff;
        }
      }
      .num {
        flex: 1;
        font-family: PangMenZhengDao;
        font-size: 32px;
        font-weight: 400;
        line-height: 34px;
        color: #ffffff;
        text-align: center;
      }
    }
    .chart-box {
      display: flex;
      justify-content: space-between;
      margin-top: 25px;
    }
    .popup-container {
      position: fixed;
      top: 194px;
      left: 1390px;
    }
  }
</style>
