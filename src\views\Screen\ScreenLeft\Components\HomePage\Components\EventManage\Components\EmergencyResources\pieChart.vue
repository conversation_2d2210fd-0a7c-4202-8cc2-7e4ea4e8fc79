<template>
  <div class="pieChart-container">
    <Mychart ref="pieRef" :option="barData" width="280px" height="160px"></Mychart>
    <div class="chart-bg"></div>
  </div>
</template>

<script setup>
  import Mychart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib'
  const pieRef = ref('pieRef')
  const props = defineProps({
    chartData: {
      type: Array,
      default: () => []
    },
    chartTitle: {
      type: String,
      default: ''
    },
    chartUnit: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  })
  const colorList1 = ['#F08B1A', '', '#EDCC19', '', '#00CCFF', '', '#198DED', '', '#1AF070']
  const colorList2 = [
    'rgb(240, 139, 26,0.5)',
    '',
    'rgb(237, 204, 25,0.5)',
    '',
    'rgb(0, 204, 255,0.5)',
    '',
    'rgb(25, 141, 237,0.5)',
    '',
    'rgb(26, 240, 112,0.5)'
  ]

  const barData = ref({})

  onMounted(() => {
    if (props.type === 'eventType') {
      pieRef.value.getInstance().on('click', (e) => {
        lib.bus.busChangeEventType.emit(e.data)
      })
    }
  })

  watch(
    () => props.chartData,
    () => {
      let sum = 0
      props.chartData.forEach((_) => {
        sum += Number(_.value || 0)
      })
      const pieData = []

      props.chartData.forEach((item) => {
        const _item = { ...item }
        if (!_item.value) {
          _item.value = sum / 100
        }
        pieData.push(_item, {
          name: '',
          value: sum / 100,
          label: { show: false },
          itemStyle: {
            color: 'transparent'
          }
        })
      })
      const legendStyles = {}
      props.chartData.forEach((data, index) => {
        legendStyles[`value${index}Style`] = {
          fontFamily: 'PangMenZhengDao',
          fontSize: 16,
          color: ['#F08B1A', '#EDCC19', '#00CCFF', '#198DED', '#1AF070'][index],
          padding: [0, 0, 0, 5]
        }
        legendStyles[`unit${index}Style`] = {
          fontFamily: 'Alibaba PuHuiTi',
          fontSize: 12,
          color: ['#F08B1A', '#EDCC19', '#00CCFF', '#198DED', '#1AF070'][index],
          padding: [0, 0, 0, 2]
        }
      })
      barData.value = {
        title: {
          text: props.chartTitle,
          left: '15%',
          top: '36%',
          itemGap: 3,
          textStyle: {
            color: '#fff',
            fontSize: '20',
            fontWeight: '400'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            // console.log('ppppp', params, params.data.name)
            const dataIndex = props.chartData.findIndex((_) => _.name === params.name)
            if (dataIndex !== -1) {
              const data = props.chartData[dataIndex]
              return `${data.name}&nbsp;&nbsp;${data.value}`
            }
          }
        },
        legend: {
          top: '5%',
          left: '45%',
          itemGap: 10,
          itemWidth: 10,
          itemHeight: 10,
          formatter: function (name) {
            const dataIndex = props.chartData.findIndex((_) => _.name === name)
            if (dataIndex !== -1) {
              const data = props.chartData[dataIndex]
              return `{labelStyle|${data.name}} {value${dataIndex}Style|${props.chartUnit && props.chartUnit === '%' ? data.pro : data.value}} ${
                props.chartUnit && props.chartUnit != '%' ? `{unit${dataIndex}Style|${props.chartUnit}}` : ''
              }`
            }
          },
          textStyle: {
            rich: {
              labelStyle: {
                fontFamily: 'Alibaba PuHuiTi',
                fontSize: 14,
                color: '#ffffff',
                padding: [0, 0, 0, 5]
              },
              ...legendStyles
            }
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['70%', '78%'],
            center: ['24%', '45%'],
            minAngle: 10,
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList1[params.dataIndex]
                }
              }
            },
            data: pieData,
            z: 666
          },
          {
            type: 'pie',
            radius: ['55%', '72%'],
            center: ['24%', '45%'],
            hoverAnimation: false,
            minAngle: 10,
            emphasis: { scale: false },
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList2[params.dataIndex]
                }
              }
            },
            data: pieData,
            z: 1
          }
        ]
      }
    },
    {
      deep: true,
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  .pieChart-container {
    position: relative;
    .chart-bg {
      position: absolute;
      top: 36px;
      left: 30px;
      z-index: -1;
      width: 75px;
      height: 75px;
      background: #00417d;
      border: 1px solid #006fae;
      border-radius: 50%;
    }
  }
</style>
