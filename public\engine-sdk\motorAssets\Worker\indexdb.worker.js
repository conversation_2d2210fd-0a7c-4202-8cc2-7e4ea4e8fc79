!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";n.r(t);var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,a=Object.keys,u=Array.isArray;function s(e,t){return"object"!=typeof t||a(t).forEach((function(n){e[n]=t[n]})),e}"undefined"==typeof Promise||i.Promise||(i.Promise=Promise);var c=Object.getPrototypeOf,l={}.hasOwnProperty;function f(e,t){return l.call(e,t)}function h(e,t){"function"==typeof t&&(t=t(c(e))),("undefined"==typeof Reflect?a:Reflect.ownKeys)(t).forEach((function(n){p(e,n,t[n])}))}var d=Object.defineProperty;function p(e,t,n,r){d(e,t,s(n&&f(n,"get")&&"function"==typeof n.get?{get:n.get,set:n.set,configurable:!0}:{value:n,configurable:!0,writable:!0},r))}function y(e){return{from:function(t){return e.prototype=Object.create(t.prototype),p(e.prototype,"constructor",e),{extend:h.bind(null,e.prototype)}}}}var v=Object.getOwnPropertyDescriptor;function m(e,t){var n;return v(e,t)||(n=c(e))&&m(n,t)}var g=[].slice;function b(e,t,n){return g.call(e,t,n)}function _(e,t){return t(e)}function w(e){if(!e)throw new Error("Assertion Failed")}function x(e){i.setImmediate?setImmediate(e):setTimeout(e,0)}function k(e,t){return e.reduce((function(e,n,r){var o=t(n,r);return o&&(e[o[0]]=o[1]),e}),{})}function E(e,t){if(f(e,t))return e[t];if(!t)return e;if("string"!=typeof t){for(var n=[],r=0,o=t.length;r<o;++r){var i=E(e,t[r]);n.push(i)}return n}var a=t.indexOf(".");if(-1!==a){var u=e[t.substr(0,a)];return void 0===u?void 0:E(u,t.substr(a+1))}}function P(e,t,n){if(e&&void 0!==t&&(!("isFrozen"in Object)||!Object.isFrozen(e)))if("string"!=typeof t&&"length"in t){w("string"!=typeof n&&"length"in n);for(var r=0,o=t.length;r<o;++r)P(e,t[r],n[r])}else{var i=t.indexOf(".");if(-1!==i){var a=t.substr(0,i),s=t.substr(i+1);if(""===s)void 0===n?u(e)&&!isNaN(parseInt(a))?e.splice(a,1):delete e[a]:e[a]=n;else{var c=e[a];c&&f(e,a)||(c=e[a]={}),P(c,s,n)}}else void 0===n?u(e)&&!isNaN(parseInt(t))?e.splice(t,1):delete e[t]:e[t]=n}}function O(e){var t={};for(var n in e)f(e,n)&&(t[n]=e[n]);return t}var K=[].concat;function S(e){return K.apply([],e)}var I="Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey".split(",").concat(S([8,16,32,64].map((function(e){return["Int","Uint","Float"].map((function(t){return t+e+"Array"}))})))).filter((function(e){return i[e]})),j=I.map((function(e){return i[e]}));k(I,(function(e){return[e,!0]}));var A=null;function C(e){A="undefined"!=typeof WeakMap&&new WeakMap;var t=function e(t){if(!t||"object"!=typeof t)return t;var n=A&&A.get(t);if(n)return n;if(u(t)){n=[],A&&A.set(t,n);for(var r=0,o=t.length;r<o;++r)n.push(e(t[r]))}else if(j.indexOf(t.constructor)>=0)n=t;else{var i=c(t);for(var a in n=i===Object.prototype?{}:Object.create(i),A&&A.set(t,n),t)f(t,a)&&(n[a]=e(t[a]))}return n}(e);return A=null,t}var D={}.toString;function T(e){return D.call(e).slice(8,-1)}var B="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator",R="symbol"==typeof B?function(e){var t;return null!=e&&(t=e[B])&&t.apply(e)}:function(){return null},M={};function F(e){var t,n,r,o;if(1===arguments.length){if(u(e))return e.slice();if(this===M&&"string"==typeof e)return[e];if(o=R(e)){for(n=[];!(r=o.next()).done;)n.push(r.value);return n}if(null==e)return[e];if("number"==typeof(t=e.length)){for(n=new Array(t);t--;)n[t]=e[t];return n}return[e]}for(t=arguments.length,n=new Array(t);t--;)n[t]=arguments[t];return n}var N="undefined"!=typeof Symbol?function(e){return"AsyncFunction"===e[Symbol.toStringTag]}:function(){return!1},q="undefined"!=typeof location&&/^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(location.href);function U(e,t){q=e,L=t}var L=function(){return!0},V=!new Error("").stack;function W(){if(V)try{throw W.arguments,new Error}catch(e){return e}return new Error}function Y(e,t){var n=e.stack;return n?(t=t||0,0===n.indexOf(e.name)&&(t+=(e.name+e.message).split("\n").length),n.split("\n").slice(t).filter(L).map((function(e){return"\n"+e})).join("")):""}var z=["Unknown","Constraint","Data","TransactionInactive","ReadOnly","Version","NotFound","InvalidState","InvalidAccess","Abort","Timeout","QuotaExceeded","Syntax","DataClone"],G=["Modify","Bulk","OpenFailed","VersionChange","Schema","Upgrade","InvalidTable","MissingAPI","NoSuchDatabase","InvalidArgument","SubTransaction","Unsupported","Internal","DatabaseClosed","PrematureCommit","ForeignAwait"].concat(z),H={VersionChanged:"Database version changed by other database connection",DatabaseClosed:"Database has been closed",Abort:"Transaction aborted",TransactionInactive:"Transaction has already completed or failed",MissingAPI:"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb"};function Q(e,t){this._e=W(),this.name=e,this.message=t}function X(e,t){return e+". Errors: "+Object.keys(t).map((function(e){return t[e].toString()})).filter((function(e,t,n){return n.indexOf(e)===t})).join("\n")}function J(e,t,n,r){this._e=W(),this.failures=t,this.failedKeys=r,this.successCount=n,this.message=X(e,t)}function $(e,t){this._e=W(),this.name="BulkError",this.failures=Object.keys(t).map((function(e){return t[e]})),this.failuresByPos=t,this.message=X(e,t)}y(Q).from(Error).extend({stack:{get:function(){return this._stack||(this._stack=this.name+": "+this.message+Y(this._e,2))}},toString:function(){return this.name+": "+this.message}}),y(J).from(Q),y($).from(Q);var Z=G.reduce((function(e,t){return e[t]=t+"Error",e}),{}),ee=Q,te=G.reduce((function(e,t){var n=t+"Error";function r(e,r){this._e=W(),this.name=n,e?"string"==typeof e?(this.message=e+(r?"\n "+r:""),this.inner=r||null):"object"==typeof e&&(this.message=e.name+" "+e.message,this.inner=e):(this.message=H[t]||n,this.inner=null)}return y(r).from(ee),e[t]=r,e}),{});te.Syntax=SyntaxError,te.Type=TypeError,te.Range=RangeError;var ne=z.reduce((function(e,t){return e[t+"Error"]=te[t],e}),{}),re=G.reduce((function(e,t){return-1===["Syntax","Type","Range"].indexOf(t)&&(e[t+"Error"]=te[t]),e}),{});function oe(){}function ie(e){return e}function ae(e,t){return null==e||e===ie?t:function(n){return t(e(n))}}function ue(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}function se(e,t){return e===oe?t:function(){var n=e.apply(this,arguments);void 0!==n&&(arguments[0]=n);var r=this.onsuccess,o=this.onerror;this.onsuccess=null,this.onerror=null;var i=t.apply(this,arguments);return r&&(this.onsuccess=this.onsuccess?ue(r,this.onsuccess):r),o&&(this.onerror=this.onerror?ue(o,this.onerror):o),void 0!==i?i:n}}function ce(e,t){return e===oe?t:function(){e.apply(this,arguments);var n=this.onsuccess,r=this.onerror;this.onsuccess=this.onerror=null,t.apply(this,arguments),n&&(this.onsuccess=this.onsuccess?ue(n,this.onsuccess):n),r&&(this.onerror=this.onerror?ue(r,this.onerror):r)}}function le(e,t){return e===oe?t:function(n){var r=e.apply(this,arguments);s(n,r);var o=this.onsuccess,i=this.onerror;this.onsuccess=null,this.onerror=null;var a=t.apply(this,arguments);return o&&(this.onsuccess=this.onsuccess?ue(o,this.onsuccess):o),i&&(this.onerror=this.onerror?ue(i,this.onerror):i),void 0===r?void 0===a?void 0:a:s(r,a)}}function fe(e,t){return e===oe?t:function(){return!1!==t.apply(this,arguments)&&e.apply(this,arguments)}}function he(e,t){return e===oe?t:function(){var n=e.apply(this,arguments);if(n&&"function"==typeof n.then){for(var r=this,o=arguments.length,i=new Array(o);o--;)i[o]=arguments[o];return n.then((function(){return t.apply(r,i)}))}return t.apply(this,arguments)}}re.ModifyError=J,re.DexieError=Q,re.BulkError=$;var de={},pe="undefined"==typeof Promise?[]:function(){var e=Promise.resolve();if("undefined"==typeof crypto||!crypto.subtle)return[e,c(e),e];var t=crypto.subtle.digest("SHA-512",new Uint8Array([0]));return[t,c(t),e]}(),ye=pe[0],ve=pe[1],me=pe[2],ge=ve&&ve.then,be=ye&&ye.constructor,_e=!!me,we=!1,xe=me?function(){me.then(We)}:i.setImmediate?setImmediate.bind(null,We):i.MutationObserver?function(){var e=document.createElement("div");new MutationObserver((function(){We(),e=null})).observe(e,{attributes:!0}),e.setAttribute("i","1")}:function(){setTimeout(We,0)},ke=function(e,t){Ce.push([e,t]),Pe&&(xe(),Pe=!1)},Ee=!0,Pe=!0,Oe=[],Ke=[],Se=null,Ie=ie,je={id:"global",global:!0,ref:0,unhandleds:[],onunhandled:pt,pgp:!1,env:{},finalize:function(){this.unhandleds.forEach((function(e){try{pt(e[0],e[1])}catch(e){}}))}},Ae=je,Ce=[],De=0,Te=[];function Be(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");this._listeners=[],this.onuncatched=oe,this._lib=!1;var t=this._PSD=Ae;if(q&&(this._stackHolder=W(),this._prev=null,this._numPrev=0),"function"!=typeof e){if(e!==de)throw new TypeError("Not a function");return this._state=arguments[1],this._value=arguments[2],void(!1===this._state&&Ne(this,this._value))}this._state=null,this._value=null,++t.ref,Fe(this,e)}var Re={get:function(){var e=Ae,t=et;function n(n,r){var o=this,i=!e.global&&(e!==Ae||t!==et),a=i&&!ot(),u=new Be((function(t,u){Ue(o,new Me(ht(n,e,i,a),ht(r,e,i,a),t,u,e))}));return q&&Ve(u,this),u}return n.prototype=de,n},set:function(e){p(this,"then",e&&e.prototype===de?Re:{get:function(){return e},set:Re.set})}};function Me(e,t,n,r,o){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r,this.psd=o}function Fe(e,t){try{t((function(t){if(null===e._state){if(t===e)throw new TypeError("A promise cannot be resolved with itself.");var n=e._lib&&Ye();t&&"function"==typeof t.then?Fe(e,(function(e,n){t instanceof Be?t._then(e,n):t.then(e,n)})):(e._state=!0,e._value=t,qe(e)),n&&ze()}}),Ne.bind(null,e))}catch(t){Ne(e,t)}}function Ne(e,t){if(Ke.push(t),null===e._state){var n=e._lib&&Ye();t=Ie(t),e._state=!1,e._value=t,q&&null!==t&&"object"==typeof t&&!t._promise&&function(e,t,n){try{e.apply(null,n)}catch(e){t&&t(e)}}((function(){var n=m(t,"stack");t._promise=e,p(t,"stack",{get:function(){return we?n&&(n.get?n.get.apply(t):n.value):e.stack}})})),function(e){Oe.some((function(t){return t._value===e._value}))||Oe.push(e)}(e),qe(e),n&&ze()}}function qe(e){var t=e._listeners;e._listeners=[];for(var n=0,r=t.length;n<r;++n)Ue(e,t[n]);var o=e._PSD;--o.ref||o.finalize(),0===De&&(++De,ke((function(){0==--De&&Ge()}),[]))}function Ue(e,t){if(null!==e._state){var n=e._state?t.onFulfilled:t.onRejected;if(null===n)return(e._state?t.resolve:t.reject)(e._value);++t.psd.ref,++De,ke(Le,[n,e,t])}else e._listeners.push(t)}function Le(e,t,n){try{Se=t;var r,o=t._value;t._state?r=e(o):(Ke.length&&(Ke=[]),r=e(o),-1===Ke.indexOf(o)&&function(e){for(var t=Oe.length;t;)if(Oe[--t]._value===e._value)return void Oe.splice(t,1)}(t)),n.resolve(r)}catch(e){n.reject(e)}finally{Se=null,0==--De&&Ge(),--n.psd.ref||n.psd.finalize()}}function Ve(e,t){var n=t?t._numPrev+1:0;n<100&&(e._prev=t,e._numPrev=n)}function We(){Ye()&&ze()}function Ye(){var e=Ee;return Ee=!1,Pe=!1,e}function ze(){var e,t,n;do{for(;Ce.length>0;)for(e=Ce,Ce=[],n=e.length,t=0;t<n;++t){var r=e[t];r[0].apply(null,r[1])}}while(Ce.length>0);Ee=!0,Pe=!0}function Ge(){var e=Oe;Oe=[],e.forEach((function(e){e._PSD.onunhandled.call(null,e._value,e)}));for(var t=Te.slice(0),n=t.length;n;)t[--n]()}function He(e){return new Be(de,!1,e)}function Qe(e,t){var n=Ae;return function(){var r=Ye(),o=Ae;try{return st(n,!0),e.apply(this,arguments)}catch(e){t&&t(e)}finally{st(o,!1),r&&ze()}}}h(Be.prototype,{then:Re,_then:function(e,t){Ue(this,new Me(null,null,e,t,Ae))},catch:function(e){if(1===arguments.length)return this.then(null,e);var t=arguments[0],n=arguments[1];return"function"==typeof t?this.then(null,(function(e){return e instanceof t?n(e):He(e)})):this.then(null,(function(e){return e&&e.name===t?n(e):He(e)}))},finally:function(e){return this.then((function(t){return e(),t}),(function(t){return e(),He(t)}))},stack:{get:function(){if(this._stack)return this._stack;try{we=!0;var e=function e(t,n,r){if(n.length===r)return n;var o="";if(!1===t._state){var i,a,u=t._value;null!=u?(i=u.name||"Error",a=u.message||u,o=Y(u,0)):(i=u,a=""),n.push(i+(a?": "+a:"")+o)}return q&&((o=Y(t._stackHolder,2))&&-1===n.indexOf(o)&&n.push(o),t._prev&&e(t._prev,n,r)),n}(this,[],20).join("\nFrom previous: ");return null!==this._state&&(this._stack=e),e}finally{we=!1}}},timeout:function(e,t){var n=this;return e<1/0?new Be((function(r,o){var i=setTimeout((function(){return o(new te.Timeout(t))}),e);n.then(r,o).finally(clearTimeout.bind(null,i))})):this}}),"undefined"!=typeof Symbol&&Symbol.toStringTag&&p(Be.prototype,Symbol.toStringTag,"Dexie.Promise"),je.env=ct(),h(Be,{all:function(){var e=F.apply(null,arguments).map(it);return new Be((function(t,n){0===e.length&&t([]);var r=e.length;e.forEach((function(o,i){return Be.resolve(o).then((function(n){e[i]=n,--r||t(e)}),n)}))}))},resolve:function(e){if(e instanceof Be)return e;if(e&&"function"==typeof e.then)return new Be((function(t,n){e.then(t,n)}));var t=new Be(de,!0,e);return Ve(t,Se),t},reject:He,race:function(){var e=F.apply(null,arguments).map(it);return new Be((function(t,n){e.map((function(e){return Be.resolve(e).then(t,n)}))}))},PSD:{get:function(){return Ae},set:function(e){return Ae=e}},totalEchoes:{get:function(){return et}},newPSD:nt,usePSD:lt,scheduler:{get:function(){return ke},set:function(e){ke=e}},rejectionMapper:{get:function(){return Ie},set:function(e){Ie=e}},follow:function(e,t){return new Be((function(n,r){return nt((function(t,n){var r=Ae;r.unhandleds=[],r.onunhandled=n,r.finalize=ue((function(){var e=this;!function(e){Te.push((function t(){e(),Te.splice(Te.indexOf(t),1)})),++De,ke((function(){0==--De&&Ge()}),[])}((function(){0===e.unhandleds.length?t():n(e.unhandleds[0])}))}),r.finalize),e()}),t,n,r)}))}}),be&&(be.allSettled&&p(Be,"allSettled",(function(){var e=F.apply(null,arguments).map(it);return new Be((function(t){0===e.length&&t([]);var n=e.length,r=new Array(n);e.forEach((function(e,o){return Be.resolve(e).then((function(e){return r[o]={status:"fulfilled",value:e}}),(function(e){return r[o]={status:"rejected",reason:e}})).then((function(){return--n||t(r)}))}))}))})),be.any&&"undefined"!=typeof AggregateError&&p(Be,"any",(function(){var e=F.apply(null,arguments).map(it);return new Be((function(t,n){0===e.length&&n(new AggregateError([]));var r=e.length,o=new Array(r);e.forEach((function(e,i){return Be.resolve(e).then((function(e){return t(e)}),(function(e){o[i]=e,--r||n(new AggregateError(o))}))}))}))})));var Xe={awaits:0,echoes:0,id:0},Je=0,$e=[],Ze=0,et=0,tt=0;function nt(e,t,n,r){var o=Ae,i=Object.create(o);i.parent=o,i.ref=0,i.global=!1,i.id=++tt;var a=je.env;i.env=_e?{Promise:Be,PromiseProp:{value:Be,configurable:!0,writable:!0},all:Be.all,race:Be.race,allSettled:Be.allSettled,any:Be.any,resolve:Be.resolve,reject:Be.reject,nthen:dt(a.nthen,i),gthen:dt(a.gthen,i)}:{},t&&s(i,t),++o.ref,i.finalize=function(){--this.parent.ref||this.parent.finalize()};var u=lt(i,e,n,r);return 0===i.ref&&i.finalize(),u}function rt(){return Xe.id||(Xe.id=++Je),++Xe.awaits,Xe.echoes+=100,Xe.id}function ot(){return!!Xe.awaits&&(0==--Xe.awaits&&(Xe.id=0),Xe.echoes=100*Xe.awaits,!0)}function it(e){return Xe.echoes&&e&&e.constructor===be?(rt(),e.then((function(e){return ot(),e}),(function(e){return ot(),yt(e)}))):e}function at(e){++et,Xe.echoes&&0!=--Xe.echoes||(Xe.echoes=Xe.id=0),$e.push(Ae),st(e,!0)}function ut(){var e=$e[$e.length-1];$e.pop(),st(e,!1)}function st(e,t){var n=Ae;if((t?!Xe.echoes||Ze++&&e===Ae:!Ze||--Ze&&e===Ae)||ft(t?at.bind(null,e):ut),e!==Ae&&(Ae=e,n===je&&(je.env=ct()),_e)){var r=je.env.Promise,o=e.env;ve.then=o.nthen,r.prototype.then=o.gthen,(n.global||e.global)&&(Object.defineProperty(i,"Promise",o.PromiseProp),r.all=o.all,r.race=o.race,r.resolve=o.resolve,r.reject=o.reject,o.allSettled&&(r.allSettled=o.allSettled),o.any&&(r.any=o.any))}}function ct(){var e=i.Promise;return _e?{Promise:e,PromiseProp:Object.getOwnPropertyDescriptor(i,"Promise"),all:e.all,race:e.race,allSettled:e.allSettled,any:e.any,resolve:e.resolve,reject:e.reject,nthen:ve.then,gthen:e.prototype.then}:{}}function lt(e,t,n,r,o){var i=Ae;try{return st(e,!0),t(n,r,o)}finally{st(i,!1)}}function ft(e){ge.call(ye,e)}function ht(e,t,n,r){return"function"!=typeof e?e:function(){var o=Ae;n&&rt(),st(t,!0);try{return e.apply(this,arguments)}finally{st(o,!1),r&&ft(ot)}}}function dt(e,t){return function(n,r){return e.call(this,ht(n,t),ht(r,t))}}function pt(e,t){var n;try{n=t.onuncatched(e)}catch(e){}if(!1!==n)try{var r,o={promise:t,reason:e};if(i.document&&document.createEvent?((r=document.createEvent("Event")).initEvent("unhandledrejection",!0,!0),s(r,o)):i.CustomEvent&&s(r=new CustomEvent("unhandledrejection",{detail:o}),o),r&&i.dispatchEvent&&(dispatchEvent(r),!i.PromiseRejectionEvent&&i.onunhandledrejection))try{i.onunhandledrejection(r)}catch(e){}q&&r&&!r.defaultPrevented&&console.warn("Unhandled rejection: "+(e.stack||e))}catch(e){}}-1===(""+ge).indexOf("[native code]")&&(rt=ot=oe);var yt=Be.reject,vt=String.fromCharCode(65535),mt="Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.",gt=[],bt="undefined"!=typeof navigator&&/(MSIE|Trident|Edge)/.test(navigator.userAgent),_t=bt,wt=bt,xt=function(e){return!/(dexie\.js|dexie\.min\.js)/.test(e)};function kt(e,t){return e?t?function(){return e.apply(this,arguments)&&t.apply(this,arguments)}:e:t}var Et={type:3,lower:-1/0,lowerOpen:!1,upper:[[]],upperOpen:!1};function Pt(e){return"string"!=typeof e||/\./.test(e)?function(e){return e}:function(t){return void 0===t[e]&&e in t&&delete(t=C(t))[e],t}}var Ot=function(){function e(){}return e.prototype._trans=function(e,t,n){var r=this._tx||Ae.trans,o=this.name;function i(e,n,r){if(!r.schema[o])throw new te.NotFound("Table "+o+" not part of transaction");return t(r.idbtrans,r)}var a=Ye();try{return r&&r.db===this.db?r===Ae.trans?r._promise(e,i,n):nt((function(){return r._promise(e,i,n)}),{trans:r,transless:Ae.transless||Ae}):function e(t,n,r,o){if(t.idbdb&&(t._state.openComplete||Ae.letThrough||t._vip)){var i=t._createTransaction(n,r,t._dbSchema);try{i.create(),t._state.PR1398_maxLoop=3}catch(i){return i.name===Z.InvalidState&&t.isOpen()&&--t._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),t._close(),t.open().then((function(){return e(t,n,r,o)}))):yt(i)}return i._promise(n,(function(e,t){return nt((function(){return Ae.trans=i,o(e,t,i)}))})).then((function(e){return i._completion.then((function(){return e}))}))}if(t._state.openComplete)return yt(new te.DatabaseClosed(t._state.dbOpenError));if(!t._state.isBeingOpened){if(!t._options.autoOpen)return yt(new te.DatabaseClosed);t.open().catch(oe)}return t._state.dbReadyPromise.then((function(){return e(t,n,r,o)}))}(this.db,e,[this.name],i)}finally{a&&ze()}},e.prototype.get=function(e,t){var n=this;return e&&e.constructor===Object?this.where(e).first(t):this._trans("readonly",(function(t){return n.core.get({trans:t,key:e}).then((function(e){return n.hook.reading.fire(e)}))})).then(t)},e.prototype.where=function(e){if("string"==typeof e)return new this.db.WhereClause(this,e);if(u(e))return new this.db.WhereClause(this,"["+e.join("+")+"]");var t=a(e);if(1===t.length)return this.where(t[0]).equals(e[t[0]]);var n=this.schema.indexes.concat(this.schema.primKey).filter((function(e){return e.compound&&t.every((function(t){return e.keyPath.indexOf(t)>=0}))&&e.keyPath.every((function(e){return t.indexOf(e)>=0}))}))[0];if(n&&this.db._maxKey!==vt)return this.where(n.name).equals(n.keyPath.map((function(t){return e[t]})));!n&&q&&console.warn("The query "+JSON.stringify(e)+" on "+this.name+" would benefit of a compound index ["+t.join("+")+"]");var r=this.schema.idxByName,o=this.db._deps.indexedDB;function i(e,t){try{return 0===o.cmp(e,t)}catch(e){return!1}}var s=t.reduce((function(t,n){var o=t[0],a=t[1],s=r[n],c=e[n];return[o||s,o||!s?kt(a,s&&s.multi?function(e){var t=E(e,n);return u(t)&&t.some((function(e){return i(c,e)}))}:function(e){return i(c,E(e,n))}):a]}),[null,null]),c=s[0],l=s[1];return c?this.where(c.name).equals(e[c.keyPath]).filter(l):n?this.filter(l):this.where(t).equals("")},e.prototype.filter=function(e){return this.toCollection().and(e)},e.prototype.count=function(e){return this.toCollection().count(e)},e.prototype.offset=function(e){return this.toCollection().offset(e)},e.prototype.limit=function(e){return this.toCollection().limit(e)},e.prototype.each=function(e){return this.toCollection().each(e)},e.prototype.toArray=function(e){return this.toCollection().toArray(e)},e.prototype.toCollection=function(){return new this.db.Collection(new this.db.WhereClause(this))},e.prototype.orderBy=function(e){return new this.db.Collection(new this.db.WhereClause(this,u(e)?"["+e.join("+")+"]":e))},e.prototype.reverse=function(){return this.toCollection().reverse()},e.prototype.mapToClass=function(e){this.schema.mappedClass=e;var t=function(t){if(!t)return t;var n=Object.create(e.prototype);for(var r in t)if(f(t,r))try{n[r]=t[r]}catch(e){}return n};return this.schema.readHook&&this.hook.reading.unsubscribe(this.schema.readHook),this.schema.readHook=t,this.hook("reading",t),e},e.prototype.defineClass=function(){return this.mapToClass((function(e){s(this,e)}))},e.prototype.add=function(e,t){var n=this,r=this.schema.primKey,o=r.auto,i=r.keyPath,a=e;return i&&o&&(a=Pt(i)(e)),this._trans("readwrite",(function(e){return n.core.mutate({trans:e,type:"add",keys:null!=t?[t]:null,values:[a]})})).then((function(e){return e.numFailures?Be.reject(e.failures[0]):e.lastResult})).then((function(t){if(i)try{P(e,i,t)}catch(e){}return t}))},e.prototype.update=function(e,t){if("object"!=typeof e||u(e))return this.where(":id").equals(e).modify(t);var n=E(e,this.schema.primKey.keyPath);if(void 0===n)return yt(new te.InvalidArgument("Given object does not contain its primary key"));try{"function"!=typeof t?a(t).forEach((function(n){P(e,n,t[n])})):t(e,{value:e,primKey:n})}catch(e){}return this.where(":id").equals(n).modify(t)},e.prototype.put=function(e,t){var n=this,r=this.schema.primKey,o=r.auto,i=r.keyPath,a=e;return i&&o&&(a=Pt(i)(e)),this._trans("readwrite",(function(e){return n.core.mutate({trans:e,type:"put",values:[a],keys:null!=t?[t]:null})})).then((function(e){return e.numFailures?Be.reject(e.failures[0]):e.lastResult})).then((function(t){if(i)try{P(e,i,t)}catch(e){}return t}))},e.prototype.delete=function(e){var t=this;return this._trans("readwrite",(function(n){return t.core.mutate({trans:n,type:"delete",keys:[e]})})).then((function(e){return e.numFailures?Be.reject(e.failures[0]):void 0}))},e.prototype.clear=function(){var e=this;return this._trans("readwrite",(function(t){return e.core.mutate({trans:t,type:"deleteRange",range:Et})})).then((function(e){return e.numFailures?Be.reject(e.failures[0]):void 0}))},e.prototype.bulkGet=function(e){var t=this;return this._trans("readonly",(function(n){return t.core.getMany({keys:e,trans:n}).then((function(e){return e.map((function(e){return t.hook.reading.fire(e)}))}))}))},e.prototype.bulkAdd=function(e,t,n){var r=this,o=Array.isArray(t)?t:void 0,i=(n=n||(o?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",(function(t){var n=r.schema.primKey,a=n.auto,u=n.keyPath;if(u&&o)throw new te.InvalidArgument("bulkAdd(): keys argument invalid on tables with inbound keys");if(o&&o.length!==e.length)throw new te.InvalidArgument("Arguments objects and keys must have the same length");var s=e.length,c=u&&a?e.map(Pt(u)):e;return r.core.mutate({trans:t,type:"add",keys:o,values:c,wantResults:i}).then((function(e){var t=e.numFailures,n=e.results,o=e.lastResult,a=e.failures;if(0===t)return i?n:o;throw new $(r.name+".bulkAdd(): "+t+" of "+s+" operations failed",a)}))}))},e.prototype.bulkPut=function(e,t,n){var r=this,o=Array.isArray(t)?t:void 0,i=(n=n||(o?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",(function(t){var n=r.schema.primKey,a=n.auto,u=n.keyPath;if(u&&o)throw new te.InvalidArgument("bulkPut(): keys argument invalid on tables with inbound keys");if(o&&o.length!==e.length)throw new te.InvalidArgument("Arguments objects and keys must have the same length");var s=e.length,c=u&&a?e.map(Pt(u)):e;return r.core.mutate({trans:t,type:"put",keys:o,values:c,wantResults:i}).then((function(e){var t=e.numFailures,n=e.results,o=e.lastResult,a=e.failures;if(0===t)return i?n:o;throw new $(r.name+".bulkPut(): "+t+" of "+s+" operations failed",a)}))}))},e.prototype.bulkDelete=function(e){var t=this,n=e.length;return this._trans("readwrite",(function(n){return t.core.mutate({trans:n,type:"delete",keys:e})})).then((function(e){var r=e.numFailures,o=e.lastResult,i=e.failures;if(0===r)return o;throw new $(t.name+".bulkDelete(): "+r+" of "+n+" operations failed",i)}))},e}();function Kt(e){var t={},n=function(n,r){if(r){for(var o=arguments.length,i=new Array(o-1);--o;)i[o-1]=arguments[o];return t[n].subscribe.apply(null,i),e}if("string"==typeof n)return t[n]};n.addEventType=i;for(var r=1,o=arguments.length;r<o;++r)i(arguments[r]);return n;function i(e,r,o){if("object"==typeof e)return s(e);r||(r=fe),o||(o=oe);var i={subscribers:[],fire:o,subscribe:function(e){-1===i.subscribers.indexOf(e)&&(i.subscribers.push(e),i.fire=r(i.fire,e))},unsubscribe:function(e){i.subscribers=i.subscribers.filter((function(t){return t!==e})),i.fire=i.subscribers.reduce(r,o)}};return t[e]=n[e]=i,i}function s(e){a(e).forEach((function(t){var n=e[t];if(u(n))i(t,e[t][0],e[t][1]);else{if("asap"!==n)throw new te.InvalidArgument("Invalid event config");var r=i(t,ie,(function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];r.subscribers.forEach((function(e){x((function(){e.apply(null,t)}))}))}))}}))}}function St(e,t){return y(t).from({prototype:e}),t}function It(e,t){return!(e.filter||e.algorithm||e.or)&&(t?e.justLimit:!e.replayFilter)}function jt(e,t){e.filter=kt(e.filter,t)}function At(e,t,n){var r=e.replayFilter;e.replayFilter=r?function(){return kt(r(),t())}:t,e.justLimit=n&&!r}function Ct(e,t){if(e.isPrimKey)return t.primaryKey;var n=t.getIndexByKeyPath(e.index);if(!n)throw new te.Schema("KeyPath "+e.index+" on object store "+t.name+" is not indexed");return n}function Dt(e,t,n){var r=Ct(e,t.schema);return t.openCursor({trans:n,values:!e.keysOnly,reverse:"prev"===e.dir,unique:!!e.unique,query:{index:r,range:e.range}})}function Tt(e,t,n,r){var o=e.replayFilter?kt(e.filter,e.replayFilter()):e.filter;if(e.or){var i={},a=function(e,n,r){if(!o||o(n,r,(function(e){return n.stop(e)}),(function(e){return n.fail(e)}))){var a=n.primaryKey,u=""+a;"[object ArrayBuffer]"===u&&(u=""+new Uint8Array(a)),f(i,u)||(i[u]=!0,t(e,n,r))}};return Promise.all([e.or._iterate(a,n),Bt(Dt(e,r,n),e.algorithm,a,!e.keysOnly&&e.valueMapper)])}return Bt(Dt(e,r,n),kt(e.algorithm,o),t,!e.keysOnly&&e.valueMapper)}function Bt(e,t,n,r){var o=Qe(r?function(e,t,o){return n(r(e),t,o)}:n);return e.then((function(e){if(e)return e.start((function(){var n=function(){return e.continue()};t&&!t(e,(function(e){return n=e}),(function(t){e.stop(t),n=oe}),(function(t){e.fail(t),n=oe}))||o(e.value,e,(function(e){return n=e})),n()}))}))}function Rt(e,t){try{var n=Mt(e),r=Mt(t);if(n!==r)return"Array"===n?1:"Array"===r?-1:"binary"===n?1:"binary"===r?-1:"string"===n?1:"string"===r?-1:"Date"===n?1:"Date"!==r?NaN:-1;switch(n){case"number":case"Date":case"string":return e>t?1:e<t?-1:0;case"binary":return function(e,t){for(var n=e.length,r=t.length,o=n<r?n:r,i=0;i<o;++i)if(e[i]!==t[i])return e[i]<t[i]?-1:1;return n===r?0:n<r?-1:1}(Ft(e),Ft(t));case"Array":return function(e,t){for(var n=e.length,r=t.length,o=n<r?n:r,i=0;i<o;++i){var a=Rt(e[i],t[i]);if(0!==a)return a}return n===r?0:n<r?-1:1}(e,t)}}catch(e){}return NaN}function Mt(e){var t=typeof e;if("object"!==t)return t;if(ArrayBuffer.isView(e))return"binary";var n=T(e);return"ArrayBuffer"===n?"binary":n}function Ft(e){return e instanceof Uint8Array?e:ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(e)}var Nt=function(){function e(){}return e.prototype._read=function(e,t){var n=this._ctx;return n.error?n.table._trans(null,yt.bind(null,n.error)):n.table._trans("readonly",e).then(t)},e.prototype._write=function(e){var t=this._ctx;return t.error?t.table._trans(null,yt.bind(null,t.error)):t.table._trans("readwrite",e,"locked")},e.prototype._addAlgorithm=function(e){var t=this._ctx;t.algorithm=kt(t.algorithm,e)},e.prototype._iterate=function(e,t){return Tt(this._ctx,e,t,this._ctx.table.core)},e.prototype.clone=function(e){var t=Object.create(this.constructor.prototype),n=Object.create(this._ctx);return e&&s(n,e),t._ctx=n,t},e.prototype.raw=function(){return this._ctx.valueMapper=null,this},e.prototype.each=function(e){var t=this._ctx;return this._read((function(n){return Tt(t,e,n,t.table.core)}))},e.prototype.count=function(e){var t=this;return this._read((function(e){var n=t._ctx,r=n.table.core;if(It(n,!0))return r.count({trans:e,query:{index:Ct(n,r.schema),range:n.range}}).then((function(e){return Math.min(e,n.limit)}));var o=0;return Tt(n,(function(){return++o,!1}),e,r).then((function(){return o}))})).then(e)},e.prototype.sortBy=function(e,t){var n=e.split(".").reverse(),r=n[0],o=n.length-1;function i(e,t){return t?i(e[n[t]],t-1):e[r]}var a="next"===this._ctx.dir?1:-1;function u(e,t){var n=i(e,o),r=i(t,o);return n<r?-a:n>r?a:0}return this.toArray((function(e){return e.sort(u)})).then(t)},e.prototype.toArray=function(e){var t=this;return this._read((function(e){var n=t._ctx;if("next"===n.dir&&It(n,!0)&&n.limit>0){var r=n.valueMapper,o=Ct(n,n.table.core.schema);return n.table.core.query({trans:e,limit:n.limit,values:!0,query:{index:o,range:n.range}}).then((function(e){var t=e.result;return r?t.map(r):t}))}var i=[];return Tt(n,(function(e){return i.push(e)}),e,n.table.core).then((function(){return i}))}),e)},e.prototype.offset=function(e){var t=this._ctx;return e<=0||(t.offset+=e,It(t)?At(t,(function(){var t=e;return function(e,n){return 0===t||(1===t?(--t,!1):(n((function(){e.advance(t),t=0})),!1))}})):At(t,(function(){var t=e;return function(){return--t<0}}))),this},e.prototype.limit=function(e){return this._ctx.limit=Math.min(this._ctx.limit,e),At(this._ctx,(function(){var t=e;return function(e,n,r){return--t<=0&&n(r),t>=0}}),!0),this},e.prototype.until=function(e,t){return jt(this._ctx,(function(n,r,o){return!e(n.value)||(r(o),t)})),this},e.prototype.first=function(e){return this.limit(1).toArray((function(e){return e[0]})).then(e)},e.prototype.last=function(e){return this.reverse().first(e)},e.prototype.filter=function(e){var t,n;return jt(this._ctx,(function(t){return e(t.value)})),t=this._ctx,n=e,t.isMatch=kt(t.isMatch,n),this},e.prototype.and=function(e){return this.filter(e)},e.prototype.or=function(e){return new this.db.WhereClause(this._ctx.table,e,this)},e.prototype.reverse=function(){return this._ctx.dir="prev"===this._ctx.dir?"next":"prev",this._ondirectionchange&&this._ondirectionchange(this._ctx.dir),this},e.prototype.desc=function(){return this.reverse()},e.prototype.eachKey=function(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each((function(t,n){e(n.key,n)}))},e.prototype.eachUniqueKey=function(e){return this._ctx.unique="unique",this.eachKey(e)},e.prototype.eachPrimaryKey=function(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each((function(t,n){e(n.primaryKey,n)}))},e.prototype.keys=function(e){var t=this._ctx;t.keysOnly=!t.isMatch;var n=[];return this.each((function(e,t){n.push(t.key)})).then((function(){return n})).then(e)},e.prototype.primaryKeys=function(e){var t=this._ctx;if("next"===t.dir&&It(t,!0)&&t.limit>0)return this._read((function(e){var n=Ct(t,t.table.core.schema);return t.table.core.query({trans:e,values:!1,limit:t.limit,query:{index:n,range:t.range}})})).then((function(e){return e.result})).then(e);t.keysOnly=!t.isMatch;var n=[];return this.each((function(e,t){n.push(t.primaryKey)})).then((function(){return n})).then(e)},e.prototype.uniqueKeys=function(e){return this._ctx.unique="unique",this.keys(e)},e.prototype.firstKey=function(e){return this.limit(1).keys((function(e){return e[0]})).then(e)},e.prototype.lastKey=function(e){return this.reverse().firstKey(e)},e.prototype.distinct=function(){var e=this._ctx,t=e.index&&e.table.schema.idxByName[e.index];if(!t||!t.multi)return this;var n={};return jt(this._ctx,(function(e){var t=e.primaryKey.toString(),r=f(n,t);return n[t]=!0,!r})),this},e.prototype.modify=function(e){var t=this,n=this._ctx;return this._write((function(r){var o;if("function"==typeof e)o=e;else{var i=a(e),u=i.length;o=function(t){for(var n=!1,r=0;r<u;++r){var o=i[r],a=e[o];E(t,o)!==a&&(P(t,o,a),n=!0)}return n}}var s=n.table.core,c=s.schema.primaryKey,l=c.outbound,f=c.extractKey,h=t.db._options.modifyChunkSize||200,d=[],p=0,y=[],v=function(e,t){var n=t.failures,r=t.numFailures;p+=e-r;for(var o=0,i=a(n);o<i.length;o++){var u=i[o];d.push(n[u])}};return t.clone().primaryKeys().then((function(t){var i=function(a){var u=Math.min(h,t.length-a);return s.getMany({trans:r,keys:t.slice(a,a+u),cache:"immutable"}).then((function(c){for(var d=[],p=[],y=l?[]:null,m=[],g=0;g<u;++g){var b=c[g],_={value:C(b),primKey:t[a+g]};!1!==o.call(_,_.value,_)&&(null==_.value?m.push(t[a+g]):l||0===Rt(f(b),f(_.value))?(p.push(_.value),l&&y.push(t[a+g])):(m.push(t[a+g]),d.push(_.value)))}var w=It(n)&&n.limit===1/0&&("function"!=typeof e||e===qt)&&{index:n.index,range:n.range};return Promise.resolve(d.length>0&&s.mutate({trans:r,type:"add",values:d}).then((function(e){for(var t in e.failures)m.splice(parseInt(t),1);v(d.length,e)}))).then((function(){return(p.length>0||w&&"object"==typeof e)&&s.mutate({trans:r,type:"put",keys:y,values:p,criteria:w,changeSpec:"function"!=typeof e&&e}).then((function(e){return v(p.length,e)}))})).then((function(){return(m.length>0||w&&e===qt)&&s.mutate({trans:r,type:"delete",keys:m,criteria:w}).then((function(e){return v(m.length,e)}))})).then((function(){return t.length>a+u&&i(a+h)}))}))};return i(0).then((function(){if(d.length>0)throw new J("Error modifying one or more objects",d,p,y);return t.length}))}))}))},e.prototype.delete=function(){var e=this._ctx,t=e.range;return It(e)&&(e.isPrimKey&&!wt||3===t.type)?this._write((function(n){var r=e.table.core.schema.primaryKey,o=t;return e.table.core.count({trans:n,query:{index:r,range:o}}).then((function(t){return e.table.core.mutate({trans:n,type:"deleteRange",range:o}).then((function(e){var n=e.failures;e.lastResult,e.results;var r=e.numFailures;if(r)throw new J("Could not delete some values",Object.keys(n).map((function(e){return n[e]})),t-r);return t-r}))}))})):this.modify(qt)},e}(),qt=function(e,t){return t.value=null};function Ut(e,t){return e<t?-1:e===t?0:1}function Lt(e,t){return e>t?-1:e===t?0:1}function Vt(e,t,n){var r=e instanceof Qt?new e.Collection(e):e;return r._ctx.error=n?new n(t):new TypeError(t),r}function Wt(e){return new e.Collection(e,(function(){return Ht("")})).limit(0)}function Yt(e,t,n,r,o,i){for(var a=Math.min(e.length,r.length),u=-1,s=0;s<a;++s){var c=t[s];if(c!==r[s])return o(e[s],n[s])<0?e.substr(0,s)+n[s]+n.substr(s+1):o(e[s],r[s])<0?e.substr(0,s)+r[s]+n.substr(s+1):u>=0?e.substr(0,u)+t[u]+n.substr(u+1):null;o(e[s],c)<0&&(u=s)}return a<r.length&&"next"===i?e+n.substr(e.length):a<e.length&&"prev"===i?e.substr(0,n.length):u<0?null:e.substr(0,u)+r[u]+n.substr(u+1)}function zt(e,t,n,r){var o,i,a,u,s,c,l,f=n.length;if(!n.every((function(e){return"string"==typeof e})))return Vt(e,"String expected.");function h(e){o=function(e){return"next"===e?function(e){return e.toUpperCase()}:function(e){return e.toLowerCase()}}(e),i=function(e){return"next"===e?function(e){return e.toLowerCase()}:function(e){return e.toUpperCase()}}(e),a="next"===e?Ut:Lt;var t=n.map((function(e){return{lower:i(e),upper:o(e)}})).sort((function(e,t){return a(e.lower,t.lower)}));u=t.map((function(e){return e.upper})),s=t.map((function(e){return e.lower})),c=e,l="next"===e?"":r}h("next");var d=new e.Collection(e,(function(){return Gt(u[0],s[f-1]+r)}));d._ondirectionchange=function(e){h(e)};var p=0;return d._addAlgorithm((function(e,n,r){var o=e.key;if("string"!=typeof o)return!1;var h=i(o);if(t(h,s,p))return!0;for(var d=null,y=p;y<f;++y){var v=Yt(o,h,u[y],s[y],a,c);null===v&&null===d?p=y+1:(null===d||a(d,v)>0)&&(d=v)}return n(null!==d?function(){e.continue(d+l)}:r),!1})),d}function Gt(e,t,n,r){return{type:2,lower:e,upper:t,lowerOpen:n,upperOpen:r}}function Ht(e){return{type:1,lower:e,upper:e}}var Qt=function(){function e(){}return Object.defineProperty(e.prototype,"Collection",{get:function(){return this._ctx.table.db.Collection},enumerable:!1,configurable:!0}),e.prototype.between=function(e,t,n,r){n=!1!==n,r=!0===r;try{return this._cmp(e,t)>0||0===this._cmp(e,t)&&(n||r)&&(!n||!r)?Wt(this):new this.Collection(this,(function(){return Gt(e,t,!n,!r)}))}catch(e){return Vt(this,mt)}},e.prototype.equals=function(e){return null==e?Vt(this,mt):new this.Collection(this,(function(){return Ht(e)}))},e.prototype.above=function(e){return null==e?Vt(this,mt):new this.Collection(this,(function(){return Gt(e,void 0,!0)}))},e.prototype.aboveOrEqual=function(e){return null==e?Vt(this,mt):new this.Collection(this,(function(){return Gt(e,void 0,!1)}))},e.prototype.below=function(e){return null==e?Vt(this,mt):new this.Collection(this,(function(){return Gt(void 0,e,!1,!0)}))},e.prototype.belowOrEqual=function(e){return null==e?Vt(this,mt):new this.Collection(this,(function(){return Gt(void 0,e)}))},e.prototype.startsWith=function(e){return"string"!=typeof e?Vt(this,"String expected."):this.between(e,e+vt,!0,!0)},e.prototype.startsWithIgnoreCase=function(e){return""===e?this.startsWith(e):zt(this,(function(e,t){return 0===e.indexOf(t[0])}),[e],vt)},e.prototype.equalsIgnoreCase=function(e){return zt(this,(function(e,t){return e===t[0]}),[e],"")},e.prototype.anyOfIgnoreCase=function(){var e=F.apply(M,arguments);return 0===e.length?Wt(this):zt(this,(function(e,t){return-1!==t.indexOf(e)}),e,"")},e.prototype.startsWithAnyOfIgnoreCase=function(){var e=F.apply(M,arguments);return 0===e.length?Wt(this):zt(this,(function(e,t){return t.some((function(t){return 0===e.indexOf(t)}))}),e,vt)},e.prototype.anyOf=function(){var e=this,t=F.apply(M,arguments),n=this._cmp;try{t.sort(n)}catch(e){return Vt(this,mt)}if(0===t.length)return Wt(this);var r=new this.Collection(this,(function(){return Gt(t[0],t[t.length-1])}));r._ondirectionchange=function(r){n="next"===r?e._ascending:e._descending,t.sort(n)};var o=0;return r._addAlgorithm((function(e,r,i){for(var a=e.key;n(a,t[o])>0;)if(++o===t.length)return r(i),!1;return 0===n(a,t[o])||(r((function(){e.continue(t[o])})),!1)})),r},e.prototype.notEqual=function(e){return this.inAnyRange([[-1/0,e],[e,this.db._maxKey]],{includeLowers:!1,includeUppers:!1})},e.prototype.noneOf=function(){var e=F.apply(M,arguments);if(0===e.length)return new this.Collection(this);try{e.sort(this._ascending)}catch(e){return Vt(this,mt)}var t=e.reduce((function(e,t){return e?e.concat([[e[e.length-1][1],t]]):[[-1/0,t]]}),null);return t.push([e[e.length-1],this.db._maxKey]),this.inAnyRange(t,{includeLowers:!1,includeUppers:!1})},e.prototype.inAnyRange=function(e,t){var n=this,r=this._cmp,o=this._ascending,i=this._descending,a=this._min,u=this._max;if(0===e.length)return Wt(this);if(!e.every((function(e){return void 0!==e[0]&&void 0!==e[1]&&o(e[0],e[1])<=0})))return Vt(this,"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower",te.InvalidArgument);var s,c=!t||!1!==t.includeLowers,l=t&&!0===t.includeUppers,f=o;function h(e,t){return f(e[0],t[0])}try{(s=e.reduce((function(e,t){for(var n=0,o=e.length;n<o;++n){var i=e[n];if(r(t[0],i[1])<0&&r(t[1],i[0])>0){i[0]=a(i[0],t[0]),i[1]=u(i[1],t[1]);break}}return n===o&&e.push(t),e}),[])).sort(h)}catch(e){return Vt(this,mt)}var d=0,p=l?function(e){return o(e,s[d][1])>0}:function(e){return o(e,s[d][1])>=0},y=c?function(e){return i(e,s[d][0])>0}:function(e){return i(e,s[d][0])>=0},v=p,m=new this.Collection(this,(function(){return Gt(s[0][0],s[s.length-1][1],!c,!l)}));return m._ondirectionchange=function(e){"next"===e?(v=p,f=o):(v=y,f=i),s.sort(h)},m._addAlgorithm((function(e,t,r){for(var i=e.key;v(i);)if(++d===s.length)return t(r),!1;return!!function(e){return!p(e)&&!y(e)}(i)||(0===n._cmp(i,s[d][1])||0===n._cmp(i,s[d][0])||t((function(){f===o?e.continue(s[d][0]):e.continue(s[d][1])})),!1)})),m},e.prototype.startsWithAnyOf=function(){var e=F.apply(M,arguments);return e.every((function(e){return"string"==typeof e}))?0===e.length?Wt(this):this.inAnyRange(e.map((function(e){return[e,e+vt]}))):Vt(this,"startsWithAnyOf() only works with strings")},e}();function Xt(e){return Qe((function(t){return Jt(t),e(t.target.error),!1}))}function Jt(e){e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault()}var $t=Kt(null,"storagemutated"),Zt=function(){function e(){}return e.prototype._lock=function(){return w(!Ae.global),++this._reculock,1!==this._reculock||Ae.global||(Ae.lockOwnerFor=this),this},e.prototype._unlock=function(){if(w(!Ae.global),0==--this._reculock)for(Ae.global||(Ae.lockOwnerFor=null);this._blockedFuncs.length>0&&!this._locked();){var e=this._blockedFuncs.shift();try{lt(e[1],e[0])}catch(e){}}return this},e.prototype._locked=function(){return this._reculock&&Ae.lockOwnerFor!==this},e.prototype.create=function(e){var t=this;if(!this.mode)return this;var n=this.db.idbdb,r=this.db._state.dbOpenError;if(w(!this.idbtrans),!e&&!n)switch(r&&r.name){case"DatabaseClosedError":throw new te.DatabaseClosed(r);case"MissingAPIError":throw new te.MissingAPI(r.message,r);default:throw new te.OpenFailed(r)}if(!this.active)throw new te.TransactionInactive;return w(null===this._completion._state),(e=this.idbtrans=e||(this.db.core?this.db.core.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}):n.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}))).onerror=Qe((function(n){Jt(n),t._reject(e.error)})),e.onabort=Qe((function(n){Jt(n),t.active&&t._reject(new te.Abort(e.error)),t.active=!1,t.on("abort").fire(n)})),e.oncomplete=Qe((function(){t.active=!1,t._resolve(),"mutatedParts"in e&&$t.storagemutated.fire(e.mutatedParts)})),this},e.prototype._promise=function(e,t,n){var r=this;if("readwrite"===e&&"readwrite"!==this.mode)return yt(new te.ReadOnly("Transaction is readonly"));if(!this.active)return yt(new te.TransactionInactive);if(this._locked())return new Be((function(o,i){r._blockedFuncs.push([function(){r._promise(e,t,n).then(o,i)},Ae])}));if(n)return nt((function(){var e=new Be((function(e,n){r._lock();var o=t(e,n,r);o&&o.then&&o.then(e,n)}));return e.finally((function(){return r._unlock()})),e._lib=!0,e}));var o=new Be((function(e,n){var o=t(e,n,r);o&&o.then&&o.then(e,n)}));return o._lib=!0,o},e.prototype._root=function(){return this.parent?this.parent._root():this},e.prototype.waitFor=function(e){var t=this._root(),n=Be.resolve(e);if(t._waitingFor)t._waitingFor=t._waitingFor.then((function(){return n}));else{t._waitingFor=n,t._waitingQueue=[];var r=t.idbtrans.objectStore(t.storeNames[0]);!function e(){for(++t._spinCount;t._waitingQueue.length;)t._waitingQueue.shift()();t._waitingFor&&(r.get(-1/0).onsuccess=e)}()}var o=t._waitingFor;return new Be((function(e,r){n.then((function(n){return t._waitingQueue.push(Qe(e.bind(null,n)))}),(function(e){return t._waitingQueue.push(Qe(r.bind(null,e)))})).finally((function(){t._waitingFor===o&&(t._waitingFor=null)}))}))},e.prototype.abort=function(){this.active&&(this.active=!1,this.idbtrans&&this.idbtrans.abort(),this._reject(new te.Abort))},e.prototype.table=function(e){var t=this._memoizedTables||(this._memoizedTables={});if(f(t,e))return t[e];var n=this.schema[e];if(!n)throw new te.NotFound("Table "+e+" not part of transaction");var r=new this.db.Table(e,n,this);return r.core=this.db.core.table(e),t[e]=r,r},e}();function en(e,t,n,r,o,i,a){return{name:e,keyPath:t,unique:n,multi:r,auto:o,compound:i,src:(n&&!a?"&":"")+(r?"*":"")+(o?"++":"")+tn(t)}}function tn(e){return"string"==typeof e?e:e?"["+[].join.call(e,"+")+"]":""}function nn(e,t,n){return{name:e,primKey:t,indexes:n,mappedClass:null,idxByName:k(n,(function(e){return[e.name,e]}))}}var rn=function(e){try{return e.only([[]]),rn=function(){return[[]]},[[]]}catch(e){return rn=function(){return vt},vt}};function on(e){return null==e?function(){}:"string"==typeof e?function(e){return 1===e.split(".").length?function(t){return t[e]}:function(t){return E(t,e)}}(e):function(t){return E(t,e)}}function an(e){return[].slice.call(e)}var un=0;function sn(e){return null==e?":id":"string"==typeof e?e:"["+e.join("+")+"]"}function cn(e,t,n){function r(e){if(3===e.type)return null;if(4===e.type)throw new Error("Cannot convert never type to IDBKeyRange");var n=e.lower,r=e.upper,o=e.lowerOpen,i=e.upperOpen;return void 0===n?void 0===r?null:t.upperBound(r,!!i):void 0===r?t.lowerBound(n,!!o):t.bound(n,r,!!o,!!i)}var o=function(e,t){var n=an(e.objectStoreNames);return{schema:{name:e.name,tables:n.map((function(e){return t.objectStore(e)})).map((function(e){var t=e.keyPath,n=e.autoIncrement,r=u(t),o=null==t,i={},a={name:e.name,primaryKey:{name:null,isPrimaryKey:!0,outbound:o,compound:r,keyPath:t,autoIncrement:n,unique:!0,extractKey:on(t)},indexes:an(e.indexNames).map((function(t){return e.index(t)})).map((function(e){var t=e.name,n=e.unique,r=e.multiEntry,o=e.keyPath,a={name:t,compound:u(o),keyPath:o,unique:n,multiEntry:r,extractKey:on(o)};return i[sn(o)]=a,a})),getIndexByKeyPath:function(e){return i[sn(e)]}};return i[":id"]=a.primaryKey,null!=t&&(i[sn(t)]=a.primaryKey),a}))},hasGetAll:n.length>0&&"getAll"in t.objectStore(n[0])&&!("undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604)}}(e,n),i=o.schema,a=o.hasGetAll,s=i.tables.map((function(e){return function(e){var t=e.name;return{name:t,schema:e,mutate:function(e){var n=e.trans,o=e.type,i=e.keys,a=e.values,u=e.range;return new Promise((function(e,s){e=Qe(e);var c=n.objectStore(t),l=null==c.keyPath,f="put"===o||"add"===o;if(!f&&"delete"!==o&&"deleteRange"!==o)throw new Error("Invalid operation type: "+o);var h,d=(i||a||{length:1}).length;if(i&&a&&i.length!==a.length)throw new Error("Given keys array must have same length as given values array.");if(0===d)return e({numFailures:0,failures:{},results:[],lastResult:void 0});var p=[],y=[],v=0,m=function(e){++v,Jt(e)};if("deleteRange"===o){if(4===u.type)return e({numFailures:v,failures:y,results:[],lastResult:void 0});3===u.type?p.push(h=c.clear()):p.push(h=c.delete(r(u)))}else{var g=f?l?[a,i]:[a,null]:[i,null],b=g[0],_=g[1];if(f)for(var w=0;w<d;++w)p.push(h=_&&void 0!==_[w]?c[o](b[w],_[w]):c[o](b[w])),h.onerror=m;else for(w=0;w<d;++w)p.push(h=c[o](b[w])),h.onerror=m}var x=function(t){var n=t.target.result;p.forEach((function(e,t){return null!=e.error&&(y[t]=e.error)})),e({numFailures:v,failures:y,results:"delete"===o?i:p.map((function(e){return e.result})),lastResult:n})};h.onerror=function(e){m(e),x(e)},h.onsuccess=x}))},getMany:function(e){var n=e.trans,r=e.keys;return new Promise((function(e,o){e=Qe(e);for(var i,a=n.objectStore(t),u=r.length,s=new Array(u),c=0,l=0,f=function(t){var n=t.target;s[n._pos]=n.result,++l===c&&e(s)},h=Xt(o),d=0;d<u;++d)null!=r[d]&&((i=a.get(r[d]))._pos=d,i.onsuccess=f,i.onerror=h,++c);0===c&&e(s)}))},get:function(e){var n=e.trans,r=e.key;return new Promise((function(e,o){e=Qe(e);var i=n.objectStore(t).get(r);i.onsuccess=function(t){return e(t.target.result)},i.onerror=Xt(o)}))},query:function(e){return function(n){return new Promise((function(o,i){o=Qe(o);var a=n.trans,u=n.values,s=n.limit,c=n.query,l=s===1/0?void 0:s,f=c.index,h=c.range,d=a.objectStore(t),p=f.isPrimaryKey?d:d.index(f.name),y=r(h);if(0===s)return o({result:[]});if(e){var v=u?p.getAll(y,l):p.getAllKeys(y,l);v.onsuccess=function(e){return o({result:e.target.result})},v.onerror=Xt(i)}else{var m=0,g=u||!("openKeyCursor"in p)?p.openCursor(y):p.openKeyCursor(y),b=[];g.onsuccess=function(e){var t=g.result;return t?(b.push(u?t.value:t.primaryKey),++m===s?o({result:b}):void t.continue()):o({result:b})},g.onerror=Xt(i)}}))}}(a),openCursor:function(e){var n=e.trans,o=e.values,i=e.query,a=e.reverse,u=e.unique;return new Promise((function(e,s){e=Qe(e);var c=i.index,l=i.range,f=n.objectStore(t),h=c.isPrimaryKey?f:f.index(c.name),d=a?u?"prevunique":"prev":u?"nextunique":"next",p=o||!("openKeyCursor"in h)?h.openCursor(r(l),d):h.openKeyCursor(r(l),d);p.onerror=Xt(s),p.onsuccess=Qe((function(t){var r=p.result;if(r){r.___id=++un,r.done=!1;var o=r.continue.bind(r),i=r.continuePrimaryKey;i&&(i=i.bind(r));var a=r.advance.bind(r),u=function(){throw new Error("Cursor not stopped")};r.trans=n,r.stop=r.continue=r.continuePrimaryKey=r.advance=function(){throw new Error("Cursor not started")},r.fail=Qe(s),r.next=function(){var e=this,t=1;return this.start((function(){return t--?e.continue():e.stop()})).then((function(){return e}))},r.start=function(e){var t=new Promise((function(e,t){e=Qe(e),p.onerror=Xt(t),r.fail=t,r.stop=function(t){r.stop=r.continue=r.continuePrimaryKey=r.advance=u,e(t)}})),n=function(){if(p.result)try{e()}catch(e){r.fail(e)}else r.done=!0,r.start=function(){throw new Error("Cursor behind last entry")},r.stop()};return p.onsuccess=Qe((function(e){p.onsuccess=n,n()})),r.continue=o,r.continuePrimaryKey=i,r.advance=a,n(),t},e(r)}else e(null)}),s)}))},count:function(e){var n=e.query,o=e.trans,i=n.index,a=n.range;return new Promise((function(e,n){var u=o.objectStore(t),s=i.isPrimaryKey?u:u.index(i.name),c=r(a),l=c?s.count(c):s.count();l.onsuccess=Qe((function(t){return e(t.target.result)})),l.onerror=Xt(n)}))}}}(e)})),c={};return s.forEach((function(e){return c[e.name]=e})),{stack:"dbcore",transaction:e.transaction.bind(e),table:function(e){if(!c[e])throw new Error("Table '"+e+"' not found");return c[e]},MIN_KEY:-1/0,MAX_KEY:rn(t),schema:i}}function ln(e,t,n,o){var i=n.IDBKeyRange;return n.indexedDB,{dbcore:function(e,t){return t.reduce((function(e,t){var n=t.create;return r(r({},e),n(e))}),e)}(cn(t,i,o),e.dbcore)}}function fn(e,t){var n=e._novip,r=t.db,o=ln(n._middlewares,r,n._deps,t);n.core=o.dbcore,n.tables.forEach((function(e){var t=e.name;n.core.schema.tables.some((function(e){return e.name===t}))&&(e.core=n.core.table(t),n[t]instanceof n.Table&&(n[t].core=e.core))}))}function hn(e,t,n,r){var o=e._novip;n.forEach((function(e){var n=r[e];t.forEach((function(t){var r=m(t,e);(!r||"value"in r&&void 0===r.value)&&(t===o.Transaction.prototype||t instanceof o.Transaction?p(t,e,{get:function(){return this.table(e)},set:function(t){d(this,e,{value:t,writable:!0,configurable:!0,enumerable:!0})}}):t[e]=new o.Table(e,n))}))}))}function dn(e,t){var n=e._novip;t.forEach((function(e){for(var t in e)e[t]instanceof n.Table&&delete e[t]}))}function pn(e,t){return e._cfg.version-t._cfg.version}function yn(e,t,n,r){var o=e._dbSchema,i=e._createTransaction("readwrite",e._storeNames,o);i.create(n),i._completion.catch(r);var u=i._reject.bind(i),s=Ae.transless||Ae;nt((function(){Ae.trans=i,Ae.transless=s,0===t?(a(o).forEach((function(e){mn(n,e,o[e].primKey,o[e].indexes)})),fn(e,n),Be.follow((function(){return e.on.populate.fire(i)})).catch(u)):function(e,t,n,r){var o=e._novip,i=[],u=o._versions,s=o._dbSchema=bn(o,o.idbdb,r),c=!1;function l(){return i.length?Be.resolve(i.shift()(n.idbtrans)).then(l):Be.resolve()}return u.filter((function(e){return e._cfg.version>=t})).forEach((function(e){i.push((function(){var i=s,u=e._cfg.dbschema;_n(o,i,r),_n(o,u,r),s=o._dbSchema=u;var l=vn(i,u);l.add.forEach((function(e){mn(r,e[0],e[1].primKey,e[1].indexes)})),l.change.forEach((function(e){if(e.recreate)throw new te.Upgrade("Not yet support for changing primary key");var t=r.objectStore(e.name);e.add.forEach((function(e){return gn(t,e)})),e.change.forEach((function(e){t.deleteIndex(e.name),gn(t,e)})),e.del.forEach((function(e){return t.deleteIndex(e)}))}));var f=e._cfg.contentUpgrade;if(f&&e._cfg.version>t){fn(o,r),n._memoizedTables={},c=!0;var h=O(u);l.del.forEach((function(e){h[e]=i[e]})),dn(o,[o.Transaction.prototype]),hn(o,[o.Transaction.prototype],a(h),h),n.schema=h;var d,p=N(f);p&&rt();var y=Be.follow((function(){if((d=f(n))&&p){var e=ot.bind(null,null);d.then(e,e)}}));return d&&"function"==typeof d.then?Be.resolve(d):y.then((function(){return d}))}})),i.push((function(t){c&&_t||function(e,t){[].slice.call(t.db.objectStoreNames).forEach((function(n){return null==e[n]&&t.db.deleteObjectStore(n)}))}(e._cfg.dbschema,t),dn(o,[o.Transaction.prototype]),hn(o,[o.Transaction.prototype],o._storeNames,o._dbSchema),n.schema=o._dbSchema}))})),l().then((function(){var e,t;t=r,a(e=s).forEach((function(n){t.db.objectStoreNames.contains(n)||mn(t,n,e[n].primKey,e[n].indexes)}))}))}(e,t,i,n).catch(u)}))}function vn(e,t){var n,r={del:[],add:[],change:[]};for(n in e)t[n]||r.del.push(n);for(n in t){var o=e[n],i=t[n];if(o){var a={name:n,def:i,recreate:!1,del:[],add:[],change:[]};if(""+(o.primKey.keyPath||"")!=""+(i.primKey.keyPath||"")||o.primKey.auto!==i.primKey.auto&&!bt)a.recreate=!0,r.change.push(a);else{var u=o.idxByName,s=i.idxByName,c=void 0;for(c in u)s[c]||a.del.push(c);for(c in s){var l=u[c],f=s[c];l?l.src!==f.src&&a.change.push(f):a.add.push(f)}(a.del.length>0||a.add.length>0||a.change.length>0)&&r.change.push(a)}}else r.add.push([n,i])}return r}function mn(e,t,n,r){var o=e.db.createObjectStore(t,n.keyPath?{keyPath:n.keyPath,autoIncrement:n.auto}:{autoIncrement:n.auto});return r.forEach((function(e){return gn(o,e)})),o}function gn(e,t){e.createIndex(t.name,t.keyPath,{unique:t.unique,multiEntry:t.multi})}function bn(e,t,n){var r={};return b(t.objectStoreNames,0).forEach((function(e){for(var t=n.objectStore(e),o=t.keyPath,i=en(tn(o),o||"",!1,!1,!!t.autoIncrement,o&&"string"!=typeof o,!0),a=[],u=0;u<t.indexNames.length;++u){var s=t.index(t.indexNames[u]);o=s.keyPath;var c=en(s.name,o,!!s.unique,!!s.multiEntry,!1,o&&"string"!=typeof o,!1);a.push(c)}r[e]=nn(e,i,a)})),r}function _n(e,t,n){for(var r=e._novip,o=n.db.objectStoreNames,a=0;a<o.length;++a){var u=o[a],s=n.objectStore(u);r._hasGetAll="getAll"in s;for(var c=0;c<s.indexNames.length;++c){var l=s.indexNames[c],f=s.index(l).keyPath,h="string"==typeof f?f:"["+b(f).join("+")+"]";if(t[u]){var d=t[u].idxByName[h];d&&(d.name=l,delete t[u].idxByName[h],t[u].idxByName[l]=d)}}}"undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&i.WorkerGlobalScope&&i instanceof i.WorkerGlobalScope&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604&&(r._hasGetAll=!1)}var wn=function(){function e(){}return e.prototype._parseStoresSpec=function(e,t){a(e).forEach((function(n){if(null!==e[n]){var r=e[n].split(",").map((function(e,t){var n=(e=e.trim()).replace(/([&*]|\+\+)/g,""),r=/^\[/.test(n)?n.match(/^\[(.*)\]$/)[1].split("+"):n;return en(n,r||null,/\&/.test(e),/\*/.test(e),/\+\+/.test(e),u(r),0===t)})),o=r.shift();if(o.multi)throw new te.Schema("Primary key cannot be multi-valued");r.forEach((function(e){if(e.auto)throw new te.Schema("Only primary key can be marked as autoIncrement (++)");if(!e.keyPath)throw new te.Schema("Index must have a name and cannot be an empty string")})),t[n]=nn(n,o,r)}}))},e.prototype.stores=function(e){var t=this.db;this._cfg.storesSource=this._cfg.storesSource?s(this._cfg.storesSource,e):e;var n=t._versions,r={},o={};return n.forEach((function(e){s(r,e._cfg.storesSource),o=e._cfg.dbschema={},e._parseStoresSpec(r,o)})),t._dbSchema=o,dn(t,[t._allTables,t,t.Transaction.prototype]),hn(t,[t._allTables,t,t.Transaction.prototype,this._cfg.tables],a(o),o),t._storeNames=a(o),this},e.prototype.upgrade=function(e){return this._cfg.contentUpgrade=he(this._cfg.contentUpgrade||oe,e),this},e}();function xn(e,t){var n=e._dbNamesDB;return n||(n=e._dbNamesDB=new Gn("__dbnames",{addons:[],indexedDB:e,IDBKeyRange:t})).version(1).stores({dbnames:"name"}),n.table("dbnames")}function kn(e){return e&&"function"==typeof e.databases}function En(e,t){var n=e.indexedDB,r=e.IDBKeyRange;!kn(n)&&"__dbnames"!==t&&xn(n,r).delete(t).catch(oe)}function Pn(e){return nt((function(){return Ae.letThrough=!0,e()}))}function On(){var e;return!navigator.userAgentData&&/Safari\//.test(navigator.userAgent)&&!/Chrom(e|ium)\//.test(navigator.userAgent)&&indexedDB.databases?new Promise((function(t){var n=function(){return indexedDB.databases().finally(t)};e=setInterval(n,100),n()})).finally((function(){return clearInterval(e)})):Promise.resolve()}function Kn(e){var t=e._state,n=e._deps.indexedDB;if(t.isBeingOpened||e.idbdb)return t.dbReadyPromise.then((function(){return t.dbOpenError?yt(t.dbOpenError):e}));q&&(t.openCanceller._stackHolder=W()),t.isBeingOpened=!0,t.dbOpenError=null,t.openComplete=!1;var r=t.openCanceller;function o(){if(t.openCanceller!==r)throw new te.DatabaseClosed("db.open() was cancelled")}var i=t.dbReadyResolve,u=null,s=!1;return Be.race([r,("undefined"==typeof navigator?Be.resolve():On()).then((function(){return new Be((function(r,i){if(o(),!n)throw new te.MissingAPI;var c=e.name,l=t.autoSchema?n.open(c):n.open(c,Math.round(10*e.verno));if(!l)throw new te.MissingAPI;l.onerror=Xt(i),l.onblocked=Qe(e._fireOnBlocked),l.onupgradeneeded=Qe((function(r){if(u=l.transaction,t.autoSchema&&!e._options.allowEmptyDB){l.onerror=Jt,u.abort(),l.result.close();var o=n.deleteDatabase(c);o.onsuccess=o.onerror=Qe((function(){i(new te.NoSuchDatabase("Database "+c+" doesnt exist"))}))}else{u.onerror=Xt(i);var a=r.oldVersion>Math.pow(2,62)?0:r.oldVersion;s=a<1,e._novip.idbdb=l.result,yn(e,a/10,u,i)}}),i),l.onsuccess=Qe((function(){u=null;var n,o=e._novip.idbdb=l.result,i=b(o.objectStoreNames);if(i.length>0)try{var f=o.transaction(1===(n=i).length?n[0]:n,"readonly");t.autoSchema?function(e,t,n){var r=e._novip;r.verno=t.version/10;var o=r._dbSchema=bn(0,t,n);r._storeNames=b(t.objectStoreNames,0),hn(r,[r._allTables],a(o),o)}(e,o,f):(_n(e,e._dbSchema,f),function(e,t){var n=vn(bn(0,e.idbdb,t),e._dbSchema);return!(n.add.length||n.change.some((function(e){return e.add.length||e.change.length})))}(e,f)||console.warn("Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Some queries may fail.")),fn(e,f)}catch(e){}gt.push(e),o.onversionchange=Qe((function(n){t.vcFired=!0,e.on("versionchange").fire(n)})),o.onclose=Qe((function(t){e.on("close").fire(t)})),s&&function(e,t){var n=e.indexedDB,r=e.IDBKeyRange;!kn(n)&&"__dbnames"!==t&&xn(n,r).put({name:t}).catch(oe)}(e._deps,c),r()}),i)}))}))]).then((function(){return o(),t.onReadyBeingFired=[],Be.resolve(Pn((function(){return e.on.ready.fire(e.vip)}))).then((function n(){if(t.onReadyBeingFired.length>0){var r=t.onReadyBeingFired.reduce(he,oe);return t.onReadyBeingFired=[],Be.resolve(Pn((function(){return r(e.vip)}))).then(n)}}))})).finally((function(){t.onReadyBeingFired=null,t.isBeingOpened=!1})).then((function(){return e})).catch((function(n){t.dbOpenError=n;try{u&&u.abort()}catch(e){}return r===t.openCanceller&&e._close(),yt(n)})).finally((function(){t.openComplete=!0,i()}))}function Sn(e){var t=function(t){return e.next(t)},n=o(t),r=o((function(t){return e.throw(t)}));function o(e){return function(t){var o=e(t),i=o.value;return o.done?i:i&&"function"==typeof i.then?i.then(n,r):u(i)?Promise.all(i).then(n,r):n(i)}}return o(t)()}function In(e,t,n){var r=arguments.length;if(r<2)throw new te.InvalidArgument("Too few arguments");for(var o=new Array(r-1);--r;)o[r-1]=arguments[r];n=o.pop();var i=S(o);return[e,i,n]}function jn(e,t,n,r,o){return Be.resolve().then((function(){var i=Ae.transless||Ae,a=e._createTransaction(t,n,e._dbSchema,r),u={trans:a,transless:i};if(r)a.idbtrans=r.idbtrans;else try{a.create(),e._state.PR1398_maxLoop=3}catch(r){return r.name===Z.InvalidState&&e.isOpen()&&--e._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),e._close(),e.open().then((function(){return jn(e,t,n,null,o)}))):yt(r)}var s,c=N(o);c&&rt();var l=Be.follow((function(){if(s=o.call(a,a))if(c){var e=ot.bind(null,null);s.then(e,e)}else"function"==typeof s.next&&"function"==typeof s.throw&&(s=Sn(s))}),u);return(s&&"function"==typeof s.then?Be.resolve(s).then((function(e){return a.active?e:yt(new te.PrematureCommit("Transaction committed too early. See http://bit.ly/2kdckMn"))})):l.then((function(){return s}))).then((function(e){return r&&a._resolve(),a._completion.then((function(){return e}))})).catch((function(e){return a._reject(e),yt(e)}))}))}function An(e,t,n){for(var r=u(e)?e.slice():[e],o=0;o<n;++o)r.push(t);return r}var Cn={stack:"dbcore",name:"VirtualIndexMiddleware",level:1,create:function(e){return r(r({},e),{table:function(t){var n=e.table(t),o=n.schema,i={},a=[];function u(e,t,n){var o=sn(e),s=i[o]=i[o]||[],c=null==e?0:"string"==typeof e?1:e.length,l=t>0,f=r(r({},n),{isVirtual:l,keyTail:t,keyLength:c,extractKey:on(e),unique:!l&&n.unique});return s.push(f),f.isPrimaryKey||a.push(f),c>1&&u(2===c?e[0]:e.slice(0,c-1),t+1,n),s.sort((function(e,t){return e.keyTail-t.keyTail})),f}var s=u(o.primaryKey.keyPath,0,o.primaryKey);i[":id"]=[s];for(var c=0,l=o.indexes;c<l.length;c++){var f=l[c];u(f.keyPath,0,f)}function h(t){var n,o,i=t.query.index;return i.isVirtual?r(r({},t),{query:{index:i,range:(n=t.query.range,o=i.keyTail,{type:1===n.type?2:n.type,lower:An(n.lower,n.lowerOpen?e.MAX_KEY:e.MIN_KEY,o),lowerOpen:!0,upper:An(n.upper,n.upperOpen?e.MIN_KEY:e.MAX_KEY,o),upperOpen:!0})}}):t}return r(r({},n),{schema:r(r({},o),{primaryKey:s,indexes:a,getIndexByKeyPath:function(e){var t=i[sn(e)];return t&&t[0]}}),count:function(e){return n.count(h(e))},query:function(e){return n.query(h(e))},openCursor:function(t){var r=t.query.index,o=r.keyTail,i=r.isVirtual,a=r.keyLength;return i?n.openCursor(h(t)).then((function(n){return n&&function(n){return Object.create(n,{continue:{value:function(r){null!=r?n.continue(An(r,t.reverse?e.MAX_KEY:e.MIN_KEY,o)):t.unique?n.continue(n.key.slice(0,a).concat(t.reverse?e.MIN_KEY:e.MAX_KEY,o)):n.continue()}},continuePrimaryKey:{value:function(t,r){n.continuePrimaryKey(An(t,e.MAX_KEY,o),r)}},primaryKey:{get:function(){return n.primaryKey}},key:{get:function(){var e=n.key;return 1===a?e[0]:e.slice(0,a)}},value:{get:function(){return n.value}}})}(n)})):n.openCursor(t)}})}})}};function Dn(e,t,n,r){return n=n||{},r=r||"",a(e).forEach((function(o){if(f(t,o)){var i=e[o],a=t[o];if("object"==typeof i&&"object"==typeof a&&i&&a){var u=T(i);u!==T(a)?n[r+o]=t[o]:"Object"===u?Dn(i,a,n,r+o+"."):i!==a&&(n[r+o]=t[o])}else i!==a&&(n[r+o]=t[o])}else n[r+o]=void 0})),a(t).forEach((function(o){f(e,o)||(n[r+o]=t[o])})),n}var Tn={stack:"dbcore",name:"HooksMiddleware",level:2,create:function(e){return r(r({},e),{table:function(t){var n=e.table(t),i=n.schema.primaryKey;return r(r({},n),{mutate:function(e){var a=Ae.trans,u=a.table(t).hook,s=u.deleting,c=u.creating,l=u.updating;switch(e.type){case"add":if(c.fire===oe)break;return a._promise("readwrite",(function(){return h(e)}),!0);case"put":if(c.fire===oe&&l.fire===oe)break;return a._promise("readwrite",(function(){return h(e)}),!0);case"delete":if(s.fire===oe)break;return a._promise("readwrite",(function(){return h(e)}),!0);case"deleteRange":if(s.fire===oe)break;return a._promise("readwrite",(function(){return function(e){return function e(t,o,a){return n.query({trans:t,values:!1,query:{index:i,range:o},limit:a}).then((function(n){var i=n.result;return h({type:"delete",keys:i,trans:t}).then((function(n){return n.numFailures>0?Promise.reject(n.failures[0]):i.length<a?{failures:[],numFailures:0,lastResult:void 0}:e(t,r(r({},o),{lower:i[i.length-1],lowerOpen:!0}),a)}))}))}(e.trans,e.range,1e4)}(e)}),!0)}return n.mutate(e);function h(e){var t=Ae.trans,a=e.keys||function(e,t){return"delete"===t.type?t.keys:t.keys||t.values.map(e.extractKey)}(i,e);if(!a)throw new Error("Keys missing");return"delete"!==(e="add"===e.type||"put"===e.type?r(r({},e),{keys:a}):r({},e)).type&&(e.values=o([],e.values,!0)),e.keys&&(e.keys=o([],e.keys,!0)),function(e,t,n){return"add"===t.type?Promise.resolve([]):e.getMany({trans:t.trans,keys:n,cache:"immutable"})}(n,e,a).then((function(r){var o=a.map((function(n,o){var a=r[o],u={onerror:null,onsuccess:null};if("delete"===e.type)s.fire.call(u,n,a,t);else if("add"===e.type||void 0===a){var h=c.fire.call(u,n,e.values[o],t);null==n&&null!=h&&(n=h,e.keys[o]=n,i.outbound||P(e.values[o],i.keyPath,n))}else{var d=Dn(a,e.values[o]),p=l.fire.call(u,d,n,a,t);if(p){var y=e.values[o];Object.keys(p).forEach((function(e){f(y,e)?y[e]=p[e]:P(y,e,p[e])}))}}return u}));return n.mutate(e).then((function(t){for(var n=t.failures,i=t.results,u=t.numFailures,s=t.lastResult,c=0;c<a.length;++c){var l=i?i[c]:a[c],f=o[c];null==l?f.onerror&&f.onerror(n[c]):f.onsuccess&&f.onsuccess("put"===e.type&&r[c]?e.values[c]:l)}return{failures:n,results:i,numFailures:u,lastResult:s}})).catch((function(e){return o.forEach((function(t){return t.onerror&&t.onerror(e)})),Promise.reject(e)}))}))}}})}})}};function Bn(e,t,n){try{if(!t)return null;if(t.keys.length<e.length)return null;for(var r=[],o=0,i=0;o<t.keys.length&&i<e.length;++o)0===Rt(t.keys[o],e[i])&&(r.push(n?C(t.values[o]):t.values[o]),++i);return r.length===e.length?r:null}catch(e){return null}}var Rn,Mn={stack:"dbcore",level:-1,create:function(e){return{table:function(t){var n=e.table(t);return r(r({},n),{getMany:function(e){if(!e.cache)return n.getMany(e);var t=Bn(e.keys,e.trans._cache,"clone"===e.cache);return t?Be.resolve(t):n.getMany(e).then((function(t){return e.trans._cache={keys:e.keys,values:"clone"===e.cache?C(t):t},t}))},mutate:function(e){return"add"!==e.type&&(e.trans._cache=null),n.mutate(e)}})}}}};function Fn(e){return!("from"in e)}var Nn=function(e,t){if(!this){var n=new Nn;return e&&"d"in e&&s(n,e),n}s(this,arguments.length?{d:1,from:e,to:arguments.length>1?t:e}:{d:0})};function qn(e,t,n){var r=Rt(t,n);if(!isNaN(r)){if(r>0)throw RangeError();if(Fn(e))return s(e,{from:t,to:n,d:1});var o=e.l,i=e.r;if(Rt(n,e.from)<0)return o?qn(o,t,n):e.l={from:t,to:n,d:1,l:null,r:null},Vn(e);if(Rt(t,e.to)>0)return i?qn(i,t,n):e.r={from:t,to:n,d:1,l:null,r:null},Vn(e);Rt(t,e.from)<0&&(e.from=t,e.l=null,e.d=i?i.d+1:1),Rt(n,e.to)>0&&(e.to=n,e.r=null,e.d=e.l?e.l.d+1:1);var a=!e.r;o&&!e.l&&Un(e,o),i&&a&&Un(e,i)}}function Un(e,t){Fn(t)||function e(t,n){var r=n.from,o=n.to,i=n.l,a=n.r;qn(t,r,o),i&&e(t,i),a&&e(t,a)}(e,t)}function Ln(e){var t=Fn(e)?null:{s:0,n:e};return{next:function(e){for(var n=arguments.length>0;t;)switch(t.s){case 0:if(t.s=1,n)for(;t.n.l&&Rt(e,t.n.from)<0;)t={up:t,n:t.n.l,s:1};else for(;t.n.l;)t={up:t,n:t.n.l,s:1};case 1:if(t.s=2,!n||Rt(e,t.n.to)<=0)return{value:t.n,done:!1};case 2:if(t.n.r){t.s=3,t={up:t,n:t.n.r,s:0};continue}case 3:t=t.up}return{done:!0}}}}function Vn(e){var t,n,o=((null===(t=e.r)||void 0===t?void 0:t.d)||0)-((null===(n=e.l)||void 0===n?void 0:n.d)||0),i=o>1?"r":o<-1?"l":"";if(i){var a="r"===i?"l":"r",u=r({},e),s=e[i];e.from=s.from,e.to=s.to,e[i]=s[i],u[i]=s[a],e[a]=u,u.d=Wn(u)}e.d=Wn(e)}function Wn(e){var t=e.r,n=e.l;return(t?n?Math.max(t.d,n.d):t.d:n?n.d:0)+1}h(Nn.prototype,((Rn={add:function(e){return Un(this,e),this},addKey:function(e){return qn(this,e,e),this},addKeys:function(e){var t=this;return e.forEach((function(e){return qn(t,e,e)})),this}})[B]=function(){return Ln(this)},Rn));var Yn,zn={stack:"dbcore",level:0,create:function(e){var t=e.schema.name,n=new Nn(e.MIN_KEY,e.MAX_KEY);return r(r({},e),{table:function(o){var i=e.table(o),s=i.schema,c=s.primaryKey,l=c.extractKey,f=c.outbound,h=r(r({},i),{mutate:function(e){var r=e.trans,a=r.mutatedParts||(r.mutatedParts={}),c=function(e){var n="idb://"+t+"/"+o+"/"+e;return a[n]||(a[n]=new Nn)},l=c(""),f=c(":dels"),h=e.type,d="deleteRange"===e.type?[e.range]:"delete"===e.type?[e.keys]:e.values.length<50?[[],e.values]:[],p=d[0],y=d[1],v=e.trans._cache;return i.mutate(e).then((function(e){if(u(p)){"delete"!==h&&(p=e.results),l.addKeys(p);var t=Bn(p,v);t||"add"===h||f.addKeys(p),(t||y)&&function(e,t,n,r){t.indexes.forEach((function(t){var o=e(t.name||"");function i(e){return null!=e?t.extractKey(e):null}var a=function(e){return t.multiEntry&&u(e)?e.forEach((function(e){return o.addKey(e)})):o.addKey(e)};(n||r).forEach((function(e,t){var o=n&&i(n[t]),u=r&&i(r[t]);0!==Rt(o,u)&&(null!=o&&a(o),null!=u&&a(u))}))}))}(c,s,t,y)}else if(p){var r={from:p.lower,to:p.upper};f.add(r),l.add(r)}else l.add(n),f.add(n),s.indexes.forEach((function(e){return c(e.name).add(n)}));return e}))}}),d=function(t){var n,r,o=t.query,i=o.index,a=o.range;return[i,new Nn(null!==(n=a.lower)&&void 0!==n?n:e.MIN_KEY,null!==(r=a.upper)&&void 0!==r?r:e.MAX_KEY)]},p={get:function(e){return[c,new Nn(e.key)]},getMany:function(e){return[c,(new Nn).addKeys(e.keys)]},count:d,query:d,openCursor:d};return a(p).forEach((function(e){h[e]=function(a){var u=Ae.subscr;if(u){var s=function(e){var n="idb://"+t+"/"+o+"/"+e;return u[n]||(u[n]=new Nn)},c=s(""),h=s(":dels"),d=p[e](a),y=d[0],v=d[1];if(s(y.name||"").add(v),!y.isPrimaryKey){if("count"!==e){var m="query"===e&&f&&a.values&&i.query(r(r({},a),{values:!1}));return i[e].apply(this,arguments).then((function(t){if("query"===e){if(f&&a.values)return m.then((function(e){var n=e.result;return c.addKeys(n),t}));var n=a.values?t.result.map(l):t.result;a.values?c.addKeys(n):h.addKeys(n)}else if("openCursor"===e){var r=t,o=a.values;return r&&Object.create(r,{key:{get:function(){return h.addKey(r.primaryKey),r.key}},primaryKey:{get:function(){var e=r.primaryKey;return h.addKey(e),e}},value:{get:function(){return o&&c.addKey(r.primaryKey),r.value}}})}return t}))}h.add(n)}}return i[e].apply(this,arguments)}})),h}})}},Gn=function(){function e(t,n){var o=this;this._middlewares={},this.verno=0;var i=e.dependencies;this._options=n=r({addons:e.addons,autoOpen:!0,indexedDB:i.indexedDB,IDBKeyRange:i.IDBKeyRange},n),this._deps={indexedDB:n.indexedDB,IDBKeyRange:n.IDBKeyRange};var a=n.addons;this._dbSchema={},this._versions=[],this._storeNames=[],this._allTables={},this.idbdb=null,this._novip=this;var u,s={dbOpenError:null,isBeingOpened:!1,onReadyBeingFired:null,openComplete:!1,dbReadyResolve:oe,dbReadyPromise:null,cancelOpen:oe,openCanceller:null,autoSchema:!0,PR1398_maxLoop:3};s.dbReadyPromise=new Be((function(e){s.dbReadyResolve=e})),s.openCanceller=new Be((function(e,t){s.cancelOpen=t})),this._state=s,this.name=t,this.on=Kt(this,"populate","blocked","versionchange","close",{ready:[he,oe]}),this.on.ready.subscribe=_(this.on.ready.subscribe,(function(t){return function(n,r){e.vip((function(){var e=o._state;if(e.openComplete)e.dbOpenError||Be.resolve().then(n),r&&t(n);else if(e.onReadyBeingFired)e.onReadyBeingFired.push(n),r&&t(n);else{t(n);var i=o;r||t((function e(){i.on.ready.unsubscribe(n),i.on.ready.unsubscribe(e)}))}}))}})),this.Collection=(u=this,St(Nt.prototype,(function(e,t){this.db=u;var n=Et,r=null;if(t)try{n=t()}catch(e){r=e}var o=e._ctx,i=o.table,a=i.hook.reading.fire;this._ctx={table:i,index:o.index,isPrimKey:!o.index||i.schema.primKey.keyPath&&o.index===i.schema.primKey.name,range:n,keysOnly:!1,dir:"next",unique:"",algorithm:null,filter:null,replayFilter:null,justLimit:!0,isMatch:null,offset:0,limit:1/0,error:r,or:o.or,valueMapper:a!==ie?a:null}}))),this.Table=function(e){return St(Ot.prototype,(function(t,n,r){this.db=e,this._tx=r,this.name=t,this.schema=n,this.hook=e._allTables[t]?e._allTables[t].hook:Kt(null,{creating:[se,oe],reading:[ae,ie],updating:[le,oe],deleting:[ce,oe]})}))}(this),this.Transaction=function(e){return St(Zt.prototype,(function(t,n,r,o,i){var a=this;this.db=e,this.mode=t,this.storeNames=n,this.schema=r,this.chromeTransactionDurability=o,this.idbtrans=null,this.on=Kt(this,"complete","error","abort"),this.parent=i||null,this.active=!0,this._reculock=0,this._blockedFuncs=[],this._resolve=null,this._reject=null,this._waitingFor=null,this._waitingQueue=null,this._spinCount=0,this._completion=new Be((function(e,t){a._resolve=e,a._reject=t})),this._completion.then((function(){a.active=!1,a.on.complete.fire()}),(function(e){var t=a.active;return a.active=!1,a.on.error.fire(e),a.parent?a.parent._reject(e):t&&a.idbtrans&&a.idbtrans.abort(),yt(e)}))}))}(this),this.Version=function(e){return St(wn.prototype,(function(t){this.db=e,this._cfg={version:t,storesSource:null,dbschema:{},tables:{},contentUpgrade:null}}))}(this),this.WhereClause=function(e){return St(Qt.prototype,(function(t,n,r){this.db=e,this._ctx={table:t,index:":id"===n?null:n,or:r};var o=e._deps.indexedDB;if(!o)throw new te.MissingAPI;this._cmp=this._ascending=o.cmp.bind(o),this._descending=function(e,t){return o.cmp(t,e)},this._max=function(e,t){return o.cmp(e,t)>0?e:t},this._min=function(e,t){return o.cmp(e,t)<0?e:t},this._IDBKeyRange=e._deps.IDBKeyRange}))}(this),this.on("versionchange",(function(e){e.newVersion>0?console.warn("Another connection wants to upgrade database '"+o.name+"'. Closing db now to resume the upgrade."):console.warn("Another connection wants to delete database '"+o.name+"'. Closing db now to resume the delete request."),o.close()})),this.on("blocked",(function(e){!e.newVersion||e.newVersion<e.oldVersion?console.warn("Dexie.delete('"+o.name+"') was blocked"):console.warn("Upgrade '"+o.name+"' blocked by other connection holding version "+e.oldVersion/10)})),this._maxKey=rn(n.IDBKeyRange),this._createTransaction=function(e,t,n,r){return new o.Transaction(e,t,n,o._options.chromeTransactionDurability,r)},this._fireOnBlocked=function(e){o.on("blocked").fire(e),gt.filter((function(e){return e.name===o.name&&e!==o&&!e._state.vcFired})).map((function(t){return t.on("versionchange").fire(e)}))},this.use(Cn),this.use(Tn),this.use(zn),this.use(Mn),this.vip=Object.create(this,{_vip:{value:!0}}),a.forEach((function(e){return e(o)}))}return e.prototype.version=function(e){if(isNaN(e)||e<.1)throw new te.Type("Given version is not a positive number");if(e=Math.round(10*e)/10,this.idbdb||this._state.isBeingOpened)throw new te.Schema("Cannot add version when database is open");this.verno=Math.max(this.verno,e);var t=this._versions,n=t.filter((function(t){return t._cfg.version===e}))[0];return n||(n=new this.Version(e),t.push(n),t.sort(pn),n.stores({}),this._state.autoSchema=!1,n)},e.prototype._whenReady=function(e){var t=this;return this.idbdb&&(this._state.openComplete||Ae.letThrough||this._vip)?e():new Be((function(e,n){if(t._state.openComplete)return n(new te.DatabaseClosed(t._state.dbOpenError));if(!t._state.isBeingOpened){if(!t._options.autoOpen)return void n(new te.DatabaseClosed);t.open().catch(oe)}t._state.dbReadyPromise.then(e,n)})).then(e)},e.prototype.use=function(e){var t=e.stack,n=e.create,r=e.level,o=e.name;o&&this.unuse({stack:t,name:o});var i=this._middlewares[t]||(this._middlewares[t]=[]);return i.push({stack:t,create:n,level:null==r?10:r,name:o}),i.sort((function(e,t){return e.level-t.level})),this},e.prototype.unuse=function(e){var t=e.stack,n=e.name,r=e.create;return t&&this._middlewares[t]&&(this._middlewares[t]=this._middlewares[t].filter((function(e){return r?e.create!==r:!!n&&e.name!==n}))),this},e.prototype.open=function(){return Kn(this)},e.prototype._close=function(){var e=this._state,t=gt.indexOf(this);if(t>=0&&gt.splice(t,1),this.idbdb){try{this.idbdb.close()}catch(e){}this._novip.idbdb=null}e.dbReadyPromise=new Be((function(t){e.dbReadyResolve=t})),e.openCanceller=new Be((function(t,n){e.cancelOpen=n}))},e.prototype.close=function(){this._close();var e=this._state;this._options.autoOpen=!1,e.dbOpenError=new te.DatabaseClosed,e.isBeingOpened&&e.cancelOpen(e.dbOpenError)},e.prototype.delete=function(){var e=this,t=arguments.length>0,n=this._state;return new Be((function(r,o){var i=function(){e.close();var t=e._deps.indexedDB.deleteDatabase(e.name);t.onsuccess=Qe((function(){En(e._deps,e.name),r()})),t.onerror=Xt(o),t.onblocked=e._fireOnBlocked};if(t)throw new te.InvalidArgument("Arguments not allowed in db.delete()");n.isBeingOpened?n.dbReadyPromise.then(i):i()}))},e.prototype.backendDB=function(){return this.idbdb},e.prototype.isOpen=function(){return null!==this.idbdb},e.prototype.hasBeenClosed=function(){var e=this._state.dbOpenError;return e&&"DatabaseClosed"===e.name},e.prototype.hasFailed=function(){return null!==this._state.dbOpenError},e.prototype.dynamicallyOpened=function(){return this._state.autoSchema},Object.defineProperty(e.prototype,"tables",{get:function(){var e=this;return a(this._allTables).map((function(t){return e._allTables[t]}))},enumerable:!1,configurable:!0}),e.prototype.transaction=function(){var e=In.apply(this,arguments);return this._transaction.apply(this,e)},e.prototype._transaction=function(e,t,n){var r=this,o=Ae.trans;o&&o.db===this&&-1===e.indexOf("!")||(o=null);var i,a,u=-1!==e.indexOf("?");e=e.replace("!","").replace("?","");try{if(a=t.map((function(e){var t=e instanceof r.Table?e.name:e;if("string"!=typeof t)throw new TypeError("Invalid table argument to Dexie.transaction(). Only Table or String are allowed");return t})),"r"==e||"readonly"===e)i="readonly";else{if("rw"!=e&&"readwrite"!=e)throw new te.InvalidArgument("Invalid transaction mode: "+e);i="readwrite"}if(o){if("readonly"===o.mode&&"readwrite"===i){if(!u)throw new te.SubTransaction("Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY");o=null}o&&a.forEach((function(e){if(o&&-1===o.storeNames.indexOf(e)){if(!u)throw new te.SubTransaction("Table "+e+" not included in parent transaction.");o=null}})),u&&o&&!o.active&&(o=null)}}catch(e){return o?o._promise(null,(function(t,n){n(e)})):yt(e)}var s=jn.bind(null,this,i,a,o,n);return o?o._promise(i,s,"lock"):Ae.trans?lt(Ae.transless,(function(){return r._whenReady(s)})):this._whenReady(s)},e.prototype.table=function(e){if(!f(this._allTables,e))throw new te.InvalidTable("Table "+e+" does not exist");return this._allTables[e]},e}(),Hn="undefined"!=typeof Symbol&&"observable"in Symbol?Symbol.observable:"@@observable",Qn=function(){function e(e){this._subscribe=e}return e.prototype.subscribe=function(e,t,n){return this._subscribe(e&&"function"!=typeof e?e:{next:e,error:t,complete:n})},e.prototype[Hn]=function(){return this},e}();function Xn(e,t){return a(t).forEach((function(n){Un(e[n]||(e[n]=new Nn),t[n])})),e}try{Yn={indexedDB:i.indexedDB||i.mozIndexedDB||i.webkitIndexedDB||i.msIndexedDB,IDBKeyRange:i.IDBKeyRange||i.webkitIDBKeyRange}}catch(e){Yn={indexedDB:null,IDBKeyRange:null}}var Jn=Gn;function $n(e){var t=Zn;try{Zn=!0,$t.storagemutated.fire(e)}finally{Zn=t}}h(Jn,r(r({},re),{delete:function(e){return new Jn(e,{addons:[]}).delete()},exists:function(e){return new Jn(e,{addons:[]}).open().then((function(e){return e.close(),!0})).catch("NoSuchDatabaseError",(function(){return!1}))},getDatabaseNames:function(e){try{return function(e){var t=e.indexedDB,n=e.IDBKeyRange;return kn(t)?Promise.resolve(t.databases()).then((function(e){return e.map((function(e){return e.name})).filter((function(e){return"__dbnames"!==e}))})):xn(t,n).toCollection().primaryKeys()}(Jn.dependencies).then(e)}catch(e){return yt(new te.MissingAPI)}},defineClass:function(){return function(e){s(this,e)}},ignoreTransaction:function(e){return Ae.trans?lt(Ae.transless,e):e()},vip:Pn,async:function(e){return function(){try{var t=Sn(e.apply(this,arguments));return t&&"function"==typeof t.then?t:Be.resolve(t)}catch(e){return yt(e)}}},spawn:function(e,t,n){try{var r=Sn(e.apply(n,t||[]));return r&&"function"==typeof r.then?r:Be.resolve(r)}catch(e){return yt(e)}},currentTransaction:{get:function(){return Ae.trans||null}},waitFor:function(e,t){var n=Be.resolve("function"==typeof e?Jn.ignoreTransaction(e):e).timeout(t||6e4);return Ae.trans?Ae.trans.waitFor(n):n},Promise:Be,debug:{get:function(){return q},set:function(e){U(e,"dexie"===e?function(){return!0}:xt)}},derive:y,extend:s,props:h,override:_,Events:Kt,on:$t,liveQuery:function(e){var t=!1,n=void 0,r=new Qn((function(r){var o=N(e),i=!1,u={},s={},c={get closed(){return i},unsubscribe:function(){i=!0,$t.storagemutated.unsubscribe(d)}};r.start&&r.start(c);var l=!1,f=!1;function h(){return a(s).some((function(e){return u[e]&&function(e,t){var n=Ln(t),r=n.next();if(r.done)return!1;for(var o=r.value,i=Ln(e),a=i.next(o.from),u=a.value;!r.done&&!a.done;){if(Rt(u.from,o.to)<=0&&Rt(u.to,o.from)>=0)return!0;Rt(o.from,u.from)<0?o=(r=n.next(u.from)).value:u=(a=i.next(o.from)).value}return!1}(u[e],s[e])}))}var d=function(e){Xn(u,e),h()&&p()},p=function(){if(!l&&!i){u={};var a={},y=function(t){o&&rt();var n=function(){return nt(e,{subscr:t,trans:null})},r=Ae.trans?lt(Ae.transless,n):n();return o&&r.then(ot,ot),r}(a);f||($t("storagemutated",d),f=!0),l=!0,Promise.resolve(y).then((function(e){t=!0,n=e,l=!1,i||(h()?p():(u={},s=a,r.next&&r.next(e)))}),(function(e){l=!1,t=!1,r.error&&r.error(e),c.unsubscribe()}))}};return p(),c}));return r.hasValue=function(){return t},r.getValue=function(){return n},r},extendObservabilitySet:Xn,getByKeyPath:E,setByKeyPath:P,delByKeyPath:function(e,t){"string"==typeof t?P(e,t,void 0):"length"in t&&[].map.call(t,(function(t){P(e,t,void 0)}))},shallowClone:O,deepClone:C,getObjectDiff:Dn,cmp:Rt,asap:x,minKey:-1/0,addons:[],connections:gt,errnames:Z,dependencies:Yn,semVer:"3.2.4",version:"3.2.4".split(".").map((function(e){return parseInt(e)})).reduce((function(e,t,n){return e+t/Math.pow(10,2*n)}))})),Jn.maxKey=rn(Jn.dependencies.IDBKeyRange),"undefined"!=typeof dispatchEvent&&"undefined"!=typeof addEventListener&&($t("storagemutated",(function(e){var t;Zn||(bt?(t=document.createEvent("CustomEvent")).initCustomEvent("x-storagemutated-1",!0,!0,e):t=new CustomEvent("x-storagemutated-1",{detail:e}),Zn=!0,dispatchEvent(t),Zn=!1)})),addEventListener("x-storagemutated-1",(function(e){var t=e.detail;Zn||$n(t)})));var Zn=!1;if("undefined"!=typeof BroadcastChannel){var er=new BroadcastChannel("x-storagemutated-1");"function"==typeof er.unref&&er.unref(),$t("storagemutated",(function(e){Zn||er.postMessage(e)})),er.onmessage=function(e){e.data&&$n(e.data)}}else if("undefined"!=typeof self&&"undefined"!=typeof navigator){$t("storagemutated",(function(e){try{Zn||("undefined"!=typeof localStorage&&localStorage.setItem("x-storagemutated-1",JSON.stringify({trig:Math.random(),changedParts:e})),"object"==typeof self.clients&&o([],self.clients.matchAll({includeUncontrolled:!0}),!0).forEach((function(t){return t.postMessage({type:"x-storagemutated-1",changedParts:e})})))}catch(e){}})),"undefined"!=typeof addEventListener&&addEventListener("storage",(function(e){if("x-storagemutated-1"===e.key){var t=JSON.parse(e.newValue);t&&$n(t.changedParts)}}));var tr=self.document&&navigator.serviceWorker;tr&&tr.addEventListener("message",(function(e){var t=e.data;t&&"x-storagemutated-1"===t.type&&$n(t.changedParts)}))}Be.rejectionMapper=function(e,t){if(!e||e instanceof Q||e instanceof TypeError||e instanceof SyntaxError||!e.name||!ne[e.name])return e;var n=new ne[e.name](t||e.message,e);return"stack"in e&&p(n,"stack",{get:function(){return this.inner.stack}}),n},U(q,xt);const nr={bimInfo:{version:1,stores:{bimInfo:"bimId,dir,&index"}},modelRecord:{version:1,stores:{modelRecord:"modelId, lastUseTime"}},modelIdToBlob:{version:1,stores:{modelIdToBlob:"&modelId, accessCount, lastUseTime"}},modelIdToJSAry:{version:1,stores:{modelIdToJSAry:"&modelId, accessCount, lastUseTime"}}};function rr(e,t=1,n={bimInfo:"&bimId, dir, index"}){try{const r=new Gn(e);return r.version(t).stores(n),r}catch(e){console.error(e)}}async function or(e,t,n){const r=rr(e,nr[t].version,nr[t].stores)[t];try{const e=await r.orderBy("lastUseTime").limit(n).toArray(),t=Date.now(),o=2592e5,i=[];return e.forEach((e=>{t-e.lastUseTime>o&&i.push(e.modelId)})),await r.bulkDelete(i),e}catch(e){console.error(e)}}async function ir(e,t,n,r){const o="modelCompData";self.postMessage({modelId:e,type:"progress",progress:10});const i="modelIdToBlob";try{const t=await async function(e,t,n,r){const o=rr(e,nr[t].version,nr[t].stores)[t];try{const e=await o.where(n).equals(r).toArray();if(e.length>0){const t=e[0].accessCount;o.update(e[0].modelId,{lastUseTime:Date.now(),accessCount:t+1})}return e}catch(e){console.error(e)}}(o,i,"modelId",e);if(t&&t.length>0){const a=t[0];if(a.updateDate===r){const r=t[0].blob;self.postMessage({modelId:e,type:"progress",progress:30});const a=await r.text().then((e=>JSON.parse(e)));return self.postMessage({modelId:e,type:"progress",progress:90}),self.postMessage({modelId:e,taskId:n,data:a}),void or(o,i,100)}{const e=[];e.push(t[0].modelId),await async function(e,t,n){const r=rr(e,nr[t].version,nr[t].stores)[t];try{await r.bulkDelete(n)}catch(e){console.error(e)}}(o,i,e)}}}catch(e){console.error(e)}(function(e){return fetch(e).then((e=>e.json())).then((e=>e))})(t).then((async t=>{let a=t&&t.map((e=>({...e,bimId:e.bid,box:e.box,dir:e.dir,index:e.idx})));t||(a=[]),self.postMessage({modelId:e,taskId:n,data:a});const u=new Blob([JSON.stringify(a)],{type:"application/json"});self.postMessage({modelId:e,type:"progress"}),or(o,i,100);try{await(async(e,t,n)=>{const r=rr(t,nr[n].version,nr[n].stores)[n];try{await r.add(e)}catch(e){console.error(" error ",e)}})({modelId:e,blob:u,type:"application/json",createDate:Date.now(),lastUseTime:Date.now(),accessCount:1,updateDate:r},o,i)}catch(e){console.error(e)}})).catch((t=>{console.error(t),self.postMessage({modelId:e,taskId:n,data:[]})}))}self.addEventListener("message",(async e=>{const{type:t}=e.data;if("init"===t){const{modelId:t,url:n,taskId:r,updateDate:o}=e.data;ir(t,n,r,o)}}))}]);