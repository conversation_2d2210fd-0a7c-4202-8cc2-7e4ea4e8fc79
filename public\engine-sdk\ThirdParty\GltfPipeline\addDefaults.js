import addToArray from"./addToArray.js";import ForEach from"./ForEach.js";import getAccessorByteStride from"./getAccessorByteStride.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import WebGLConstants from"../../Core/WebGLConstants.js";function addDefaults(e){ForEach.accessor(e,(function(e){defined(e.bufferView)&&(e.byteOffset=defaultValue(e.byteOffset,0))})),ForEach.bufferView(e,(function(e){defined(e.buffer)&&(e.byteOffset=defaultValue(e.byteOffset,0))})),ForEach.mesh(e,(function(a){ForEach.meshPrimitive(a,(function(a){if(a.mode=defaultValue(a.mode,WebGLConstants.TRIANGLES),!defined(a.material)){defined(e.materials)||(e.materials=[]);var t={name:"default"};a.material=addToArray(e.materials,t)}}))})),ForEach.accessorContainingVertexAttributeData(e,(function(a){var t=e.accessors[a],r=t.bufferView;if(t.normalized=defaultValue(t.normalized,!1),defined(r)){var n=e.bufferViews[r];n.byteStride=getAccessorByteStride(e,t),n.target=WebGLConstants.ARRAY_BUFFER}})),ForEach.accessorContainingIndexData(e,(function(a){var t=e.accessors[a],r=t.bufferView;if(defined(r)){var n=e.bufferViews[r];n.target=WebGLConstants.ELEMENT_ARRAY_BUFFER}})),ForEach.material(e,(function(e){var a=defaultValue(e.extensions,defaultValue.EMPTY_OBJECT),t=a.KHR_materials_common;if(defined(t)){var r=t.technique,n=defined(t.values)?t.values:{};return t.values=n,n.ambient=defined(n.ambient)?n.ambient:[0,0,0,1],n.emission=defined(n.emission)?n.emission:[0,0,0,1],n.transparency=defaultValue(n.transparency,1),n.transparent=defaultValue(n.transparent,!1),n.doubleSided=defaultValue(n.doubleSided,!1),void("CONSTANT"!==r&&(n.diffuse=defined(n.diffuse)?n.diffuse:[0,0,0,1],"LAMBERT"!==r&&(n.specular=defined(n.specular)?n.specular:[0,0,0,1],n.shininess=defaultValue(n.shininess,0))))}e.emissiveFactor=defaultValue(e.emissiveFactor,[0,0,0]),e.alphaMode=defaultValue(e.alphaMode,"OPAQUE"),e.doubleSided=defaultValue(e.doubleSided,!1),"MASK"===e.alphaMode&&(e.alphaCutoff=defaultValue(e.alphaCutoff,.5));var o=a.KHR_techniques_webgl;defined(o)&&ForEach.materialValue(e,(function(e){defined(e.index)&&addTextureDefaults(e)})),addTextureDefaults(e.emissiveTexture),addTextureDefaults(e.normalTexture),addTextureDefaults(e.occlusionTexture);var d=e.pbrMetallicRoughness;defined(d)&&(d.baseColorFactor=defaultValue(d.baseColorFactor,[1,1,1,1]),d.metallicFactor=defaultValue(d.metallicFactor,1),d.roughnessFactor=defaultValue(d.roughnessFactor,1),addTextureDefaults(d.baseColorTexture),addTextureDefaults(d.metallicRoughnessTexture));var i=a.pbrSpecularGlossiness;defined(i)&&(i.diffuseFactor=defaultValue(i.diffuseFactor,[1,1,1,1]),i.specularFactor=defaultValue(i.specularFactor,[1,1,1]),i.glossinessFactor=defaultValue(i.glossinessFactor,1),addTextureDefaults(i.specularGlossinessTexture))})),ForEach.animation(e,(function(e){ForEach.animationSampler(e,(function(e){e.interpolation=defaultValue(e.interpolation,"LINEAR")}))}));var a=getAnimatedNodes(e);return ForEach.node(e,(function(e,t){var r=defined(a[t]);r||defined(e.translation)||defined(e.rotation)||defined(e.scale)?(e.translation=defaultValue(e.translation,[0,0,0]),e.rotation=defaultValue(e.rotation,[0,0,0,1]),e.scale=defaultValue(e.scale,[1,1,1])):e.matrix=defaultValue(e.matrix,[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])})),ForEach.sampler(e,(function(e){e.wrapS=defaultValue(e.wrapS,WebGLConstants.REPEAT),e.wrapT=defaultValue(e.wrapT,WebGLConstants.REPEAT)})),defined(e.scenes)&&!defined(e.scene)&&(e.scene=0),e}function getAnimatedNodes(e){var a={};return ForEach.animation(e,(function(e){ForEach.animationChannel(e,(function(e){var t=e.target,r=t.node,n=t.path;"translation"!==n&&"rotation"!==n&&"scale"!==n||(a[r]=!0)}))})),a}function addTextureDefaults(e){defined(e)&&(e.texCoord=defaultValue(e.texCoord,0))}export default addDefaults;