import defined from"../../Core/defined.js";import DeveloperError from"../../Core/DeveloperError.js";import CesiumInspector from"../CesiumInspector/CesiumInspector.js";function viewerCesiumInspectorMixin(e){if(!defined(e))throw new DeveloperError("viewer is required.");var r=document.createElement("div");r.className="cesium-viewer-cesiumInspectorContainer",e.container.appendChild(r);var i=new CesiumInspector(r,e.scene);Object.defineProperties(e,{cesiumInspector:{get:function(){return i}}})}export default viewerCesiumInspectorMixin;