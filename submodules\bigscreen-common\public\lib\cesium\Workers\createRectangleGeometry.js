/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryOffsetAttribute-5a4c2801","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./RectangleGeometryLibrary-194ff03f","./VertexFormat-86c096b8","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./IntersectionTests-25cff68e","./Plane-a268aa11","./EllipsoidRhumbLine-d5e7f3db"],(function(t,e,n,a,o,r,i,s,l,u,c,m,d,p,g,y,f,h,b,_,A,x,w,C){"use strict";const v=new a.Cartesian3,R=new a.Cartesian3,E=new a.Cartesian3,G=new a.Cartesian3,F=new e.Rectangle,P=new a.Cartesian2,V=new n.BoundingSphere,L=new n.BoundingSphere;function D(t,e){const n=new r.Geometry({attributes:new i.GeometryAttributes,primitiveType:r.PrimitiveType.TRIANGLES});return n.attributes.position=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e.positions}),t.normal&&(n.attributes.normal=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.normals})),t.tangent&&(n.attributes.tangent=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.tangents})),t.bitangent&&(n.attributes.bitangent=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.bitangents})),n}const M=new a.Cartesian3,T=new a.Cartesian3;function O(t,n){const i=t._vertexFormat,s=t._ellipsoid,l=n.height,u=n.width,m=n.northCap,d=n.southCap;let g=0,y=l,f=l,h=0;m&&(g=1,f-=1,h+=1),d&&(y-=1,f-=1,h+=1),h+=u*f;const b=i.position?new Float64Array(3*h):void 0,_=i.st?new Float32Array(2*h):void 0;let A=0,x=0;const w=v,C=P;let F=Number.MAX_VALUE,V=Number.MAX_VALUE,L=-Number.MAX_VALUE,M=-Number.MAX_VALUE;for(let t=g;t<y;++t)for(let e=0;e<u;++e)p.RectangleGeometryLibrary.computePosition(n,s,i.st,t,e,w,C),b[A++]=w.x,b[A++]=w.y,b[A++]=w.z,i.st&&(_[x++]=C.x,_[x++]=C.y,F=Math.min(F,C.x),V=Math.min(V,C.y),L=Math.max(L,C.x),M=Math.max(M,C.y));if(m&&(p.RectangleGeometryLibrary.computePosition(n,s,i.st,0,0,w,C),b[A++]=w.x,b[A++]=w.y,b[A++]=w.z,i.st&&(_[x++]=C.x,_[x++]=C.y,F=C.x,V=C.y,L=C.x,M=C.y)),d&&(p.RectangleGeometryLibrary.computePosition(n,s,i.st,l-1,0,w,C),b[A++]=w.x,b[A++]=w.y,b[A]=w.z,i.st&&(_[x++]=C.x,_[x]=C.y,F=Math.min(F,C.x),V=Math.min(V,C.y),L=Math.max(L,C.x),M=Math.max(M,C.y))),i.st&&(F<0||V<0||L>1||M>1))for(let t=0;t<_.length;t+=2)_[t]=(_[t]-F)/(L-F),_[t+1]=(_[t+1]-V)/(M-V);const T=function(t,n,o,r){const i=t.length,s=n.normal?new Float32Array(i):void 0,l=n.tangent?new Float32Array(i):void 0,u=n.bitangent?new Float32Array(i):void 0;let c=0;const m=G,d=E;let p=R;if(n.normal||n.tangent||n.bitangent)for(let g=0;g<i;g+=3){const i=a.Cartesian3.fromArray(t,g,v),y=c+1,f=c+2;p=o.geodeticSurfaceNormal(i,p),(n.tangent||n.bitangent)&&(a.Cartesian3.cross(a.Cartesian3.UNIT_Z,p,d),e.Matrix3.multiplyByVector(r,d,d),a.Cartesian3.normalize(d,d),n.bitangent&&a.Cartesian3.normalize(a.Cartesian3.cross(p,d,m),m)),n.normal&&(s[c]=p.x,s[y]=p.y,s[f]=p.z),n.tangent&&(l[c]=d.x,l[y]=d.y,l[f]=d.z),n.bitangent&&(u[c]=m.x,u[y]=m.y,u[f]=m.z),c+=3}return D(n,{positions:t,normals:s,tangents:l,bitangents:u})}(b,i,s,n.tangentRotationMatrix);let O=6*(u-1)*(f-1);m&&(O+=3*(u-1)),d&&(O+=3*(u-1));const N=c.IndexDatatype.createTypedArray(h,O);let S,I=0,k=0;for(S=0;S<f-1;++S){for(let t=0;t<u-1;++t){const t=I,e=t+u,n=e+1,a=t+1;N[k++]=t,N[k++]=e,N[k++]=a,N[k++]=a,N[k++]=e,N[k++]=n,++I}++I}if(m||d){let t=h-1;const e=h-1;let n,a;if(m&&d&&(t=h-2),I=0,m)for(S=0;S<u-1;S++)n=I,a=n+1,N[k++]=t,N[k++]=n,N[k++]=a,++I;if(d)for(I=(f-1)*u,S=0;S<u-1;S++)n=I,a=n+1,N[k++]=n,N[k++]=e,N[k++]=a,++I}return T.indices=N,i.st&&(T.attributes.st=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:_})),T}function N(t,e,n,a,o){return t[e++]=a[n],t[e++]=a[n+1],t[e++]=a[n+2],t[e++]=o[n],t[e++]=o[n+1],t[e]=o[n+2],t}function S(t,e,n,a){return t[e++]=a[n],t[e++]=a[n+1],t[e++]=a[n],t[e]=a[n+1],t}const I=new g.VertexFormat;function k(e,n){const i=e._shadowVolume,p=e._offsetAttribute,y=e._vertexFormat,f=e._extrudedHeight,h=e._surfaceHeight,b=e._ellipsoid,_=n.height,A=n.width;let x;if(i){const t=g.VertexFormat.clone(y,I);t.normal=!0,e._vertexFormat=t}const w=O(e,n);i&&(e._vertexFormat=y);let C=d.PolygonPipeline.scaleToGeodeticHeight(w.attributes.position.values,h,b,!1);C=new Float64Array(C);let F=C.length;const P=2*F,V=new Float64Array(P);V.set(C);const L=d.PolygonPipeline.scaleToGeodeticHeight(w.attributes.position.values,f,b);V.set(L,F),w.attributes.position.values=V;const k=y.normal?new Float32Array(P):void 0,H=y.tangent?new Float32Array(P):void 0,z=y.bitangent?new Float32Array(P):void 0,B=y.st?new Float32Array(P/3*2):void 0;let U,Y,q;if(y.normal){for(Y=w.attributes.normal.values,k.set(Y),x=0;x<F;x++)Y[x]=-Y[x];k.set(Y,F),w.attributes.normal.values=k}if(i){Y=w.attributes.normal.values,y.normal||(w.attributes.normal=void 0);const t=new Float32Array(P);for(x=0;x<F;x++)Y[x]=-Y[x];t.set(Y,F),w.attributes.extrudeDirection=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:t})}const X=t.defined(p);if(X){const t=F/3*2;let e=new Uint8Array(t);p===l.GeometryOffsetAttribute.TOP?e=e.fill(1,0,t/2):(q=p===l.GeometryOffsetAttribute.NONE?0:1,e=e.fill(q)),w.attributes.applyOffset=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}if(y.tangent){const t=w.attributes.tangent.values;for(H.set(t),x=0;x<F;x++)t[x]=-t[x];H.set(t,F),w.attributes.tangent.values=H}if(y.bitangent){const t=w.attributes.bitangent.values;z.set(t),z.set(t,F),w.attributes.bitangent.values=z}y.st&&(U=w.attributes.st.values,B.set(U),B.set(U,F/3*2),w.attributes.st.values=B);const Q=w.indices,W=Q.length,J=F/3,j=c.IndexDatatype.createTypedArray(P/3,2*W);for(j.set(Q),x=0;x<W;x+=3)j[x+W]=Q[x+2]+J,j[x+1+W]=Q[x+1]+J,j[x+2+W]=Q[x]+J;w.indices=j;const Z=n.northCap,K=n.southCap;let $=_,tt=2,et=0,nt=4,at=4;Z&&(tt-=1,$-=1,et+=1,nt-=2,at-=1),K&&(tt-=1,$-=1,et+=1,nt-=2,at-=1),et+=tt*A+2*$-nt;const ot=2*(et+at);let rt=new Float64Array(3*ot);const it=i?new Float32Array(3*ot):void 0;let st=X?new Uint8Array(ot):void 0,lt=y.st?new Float32Array(2*ot):void 0;const ut=p===l.GeometryOffsetAttribute.TOP;X&&!ut&&(q=p===l.GeometryOffsetAttribute.ALL?1:0,st=st.fill(q));let ct=0,mt=0,dt=0,pt=0;const gt=A*$;let yt;for(x=0;x<gt;x+=A)yt=3*x,rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*x,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1);if(K){const t=Z?gt+1:gt;for(yt=3*t,x=0;x<2;x++)rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*t,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1)}else for(x=gt-A;x<gt;x++)yt=3*x,rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*x,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1);for(x=gt-1;x>0;x-=A)yt=3*x,rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*x,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1);if(Z){const t=gt;for(yt=3*t,x=0;x<2;x++)rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*t,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1)}else for(x=A-1;x>=0;x--)yt=3*x,rt=N(rt,ct,yt,C,L),ct+=6,y.st&&(lt=S(lt,mt,2*x,U),mt+=4),i&&(dt+=3,it[dt++]=Y[yt],it[dt++]=Y[yt+1],it[dt++]=Y[yt+2]),ut&&(st[pt++]=1,pt+=1);let ft=function(t,e,n){const o=t.length,r=e.normal?new Float32Array(o):void 0,i=e.tangent?new Float32Array(o):void 0,s=e.bitangent?new Float32Array(o):void 0;let l=0,u=0,c=0,d=!0,p=G,g=E,y=R;if(e.normal||e.tangent||e.bitangent)for(let f=0;f<o;f+=6){const h=a.Cartesian3.fromArray(t,f,v),b=a.Cartesian3.fromArray(t,(f+6)%o,M);if(d){const e=a.Cartesian3.fromArray(t,(f+3)%o,T);a.Cartesian3.subtract(b,h,b),a.Cartesian3.subtract(e,h,e),y=a.Cartesian3.normalize(a.Cartesian3.cross(e,b,y),y),d=!1}a.Cartesian3.equalsEpsilon(b,h,m.CesiumMath.EPSILON10)&&(d=!0),(e.tangent||e.bitangent)&&(p=n.geodeticSurfaceNormal(h,p),e.tangent&&(g=a.Cartesian3.normalize(a.Cartesian3.cross(p,y,g),g))),e.normal&&(r[l++]=y.x,r[l++]=y.y,r[l++]=y.z,r[l++]=y.x,r[l++]=y.y,r[l++]=y.z),e.tangent&&(i[u++]=g.x,i[u++]=g.y,i[u++]=g.z,i[u++]=g.x,i[u++]=g.y,i[u++]=g.z),e.bitangent&&(s[c++]=p.x,s[c++]=p.y,s[c++]=p.z,s[c++]=p.x,s[c++]=p.y,s[c++]=p.z)}return D(e,{positions:t,normals:r,tangents:i,bitangents:s})}(rt,y,b);y.st&&(ft.attributes.st=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:lt})),i&&(ft.attributes.extrudeDirection=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:it})),X&&(ft.attributes.applyOffset=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:st}));const ht=c.IndexDatatype.createTypedArray(ot,6*et);let bt,_t,At,xt;F=rt.length/3;let wt=0;for(x=0;x<F-1;x+=2){bt=x,xt=(bt+2)%F;const t=a.Cartesian3.fromArray(rt,3*bt,M),e=a.Cartesian3.fromArray(rt,3*xt,T);a.Cartesian3.equalsEpsilon(t,e,m.CesiumMath.EPSILON10)||(_t=(bt+1)%F,At=(_t+2)%F,ht[wt++]=bt,ht[wt++]=_t,ht[wt++]=xt,ht[wt++]=xt,ht[wt++]=_t,ht[wt++]=At)}return ft.indices=ht,ft=u.GeometryPipeline.combineInstances([new s.GeometryInstance({geometry:w}),new s.GeometryInstance({geometry:ft})]),ft[0]}const H=[new a.Cartesian3,new a.Cartesian3,new a.Cartesian3,new a.Cartesian3],z=new e.Cartographic,B=new e.Cartographic;function U(t,n,a,o,r){if(0===a)return e.Rectangle.clone(t,r);const i=p.RectangleGeometryLibrary.computeOptions(t,n,a,0,F,z),s=i.height,l=i.width,u=H;return p.RectangleGeometryLibrary.computePosition(i,o,!1,0,0,u[0]),p.RectangleGeometryLibrary.computePosition(i,o,!1,0,l-1,u[1]),p.RectangleGeometryLibrary.computePosition(i,o,!1,s-1,0,u[2]),p.RectangleGeometryLibrary.computePosition(i,o,!1,s-1,l-1,u[3]),e.Rectangle.fromCartesianArray(u,o,r)}function Y(n){const a=(n=t.defaultValue(n,t.defaultValue.EMPTY_OBJECT)).rectangle,o=t.defaultValue(n.height,0),r=t.defaultValue(n.extrudedHeight,o);this._rectangle=e.Rectangle.clone(a),this._granularity=t.defaultValue(n.granularity,m.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.Ellipsoid.clone(t.defaultValue(n.ellipsoid,e.Ellipsoid.WGS84)),this._surfaceHeight=Math.max(o,r),this._rotation=t.defaultValue(n.rotation,0),this._stRotation=t.defaultValue(n.stRotation,0),this._vertexFormat=g.VertexFormat.clone(t.defaultValue(n.vertexFormat,g.VertexFormat.DEFAULT)),this._extrudedHeight=Math.min(o,r),this._shadowVolume=t.defaultValue(n.shadowVolume,!1),this._workerName="createRectangleGeometry",this._offsetAttribute=n.offsetAttribute,this._rotatedRectangle=void 0,this._textureCoordinateRotationPoints=void 0}Y.packedLength=e.Rectangle.packedLength+e.Ellipsoid.packedLength+g.VertexFormat.packedLength+7,Y.pack=function(n,a,o){return o=t.defaultValue(o,0),e.Rectangle.pack(n._rectangle,a,o),o+=e.Rectangle.packedLength,e.Ellipsoid.pack(n._ellipsoid,a,o),o+=e.Ellipsoid.packedLength,g.VertexFormat.pack(n._vertexFormat,a,o),o+=g.VertexFormat.packedLength,a[o++]=n._granularity,a[o++]=n._surfaceHeight,a[o++]=n._rotation,a[o++]=n._stRotation,a[o++]=n._extrudedHeight,a[o++]=n._shadowVolume?1:0,a[o]=t.defaultValue(n._offsetAttribute,-1),a};const q=new e.Rectangle,X=e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),Q={rectangle:q,ellipsoid:X,vertexFormat:I,granularity:void 0,height:void 0,rotation:void 0,stRotation:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};Y.unpack=function(n,a,o){a=t.defaultValue(a,0);const r=e.Rectangle.unpack(n,a,q);a+=e.Rectangle.packedLength;const i=e.Ellipsoid.unpack(n,a,X);a+=e.Ellipsoid.packedLength;const s=g.VertexFormat.unpack(n,a,I);a+=g.VertexFormat.packedLength;const l=n[a++],u=n[a++],c=n[a++],m=n[a++],d=n[a++],p=1===n[a++],y=n[a];return t.defined(o)?(o._rectangle=e.Rectangle.clone(r,o._rectangle),o._ellipsoid=e.Ellipsoid.clone(i,o._ellipsoid),o._vertexFormat=g.VertexFormat.clone(s,o._vertexFormat),o._granularity=l,o._surfaceHeight=u,o._rotation=c,o._stRotation=m,o._extrudedHeight=d,o._shadowVolume=p,o._offsetAttribute=-1===y?void 0:y,o):(Q.granularity=l,Q.height=u,Q.rotation=c,Q.stRotation=m,Q.extrudedHeight=d,Q.shadowVolume=p,Q.offsetAttribute=-1===y?void 0:y,new Y(Q))},Y.computeRectangle=function(n,a){const o=(n=t.defaultValue(n,t.defaultValue.EMPTY_OBJECT)).rectangle,r=t.defaultValue(n.granularity,m.CesiumMath.RADIANS_PER_DEGREE),i=t.defaultValue(n.ellipsoid,e.Ellipsoid.WGS84);return U(o,r,t.defaultValue(n.rotation,0),i,a)};const W=new e.Matrix3,J=new n.Quaternion,j=new e.Cartographic;Y.createGeometry=function(a){if(m.CesiumMath.equalsEpsilon(a._rectangle.north,a._rectangle.south,m.CesiumMath.EPSILON10)||m.CesiumMath.equalsEpsilon(a._rectangle.east,a._rectangle.west,m.CesiumMath.EPSILON10))return;let i=a._rectangle;const s=a._ellipsoid,u=a._rotation,c=a._stRotation,g=a._vertexFormat,y=p.RectangleGeometryLibrary.computeOptions(i,a._granularity,u,c,F,z,B),f=W;if(0!==c||0!==u){const t=e.Rectangle.center(i,j),a=s.geodeticSurfaceNormalCartographic(t,M);n.Quaternion.fromAxisAngle(a,-c,J),e.Matrix3.fromQuaternion(J,f)}else e.Matrix3.clone(e.Matrix3.IDENTITY,f);const h=a._surfaceHeight,b=a._extrudedHeight,_=!m.CesiumMath.equalsEpsilon(h,b,0,m.CesiumMath.EPSILON2);let A,x;if(y.lonScalar=1/a._rectangle.width,y.latScalar=1/a._rectangle.height,y.tangentRotationMatrix=f,i=a._rectangle,_){A=k(a,y);const t=n.BoundingSphere.fromRectangle3D(i,s,h,L),e=n.BoundingSphere.fromRectangle3D(i,s,b,V);x=n.BoundingSphere.union(t,e)}else{if(A=O(a,y),A.attributes.position.values=d.PolygonPipeline.scaleToGeodeticHeight(A.attributes.position.values,h,s,!1),t.defined(a._offsetAttribute)){const t=A.attributes.position.values.length,e=a._offsetAttribute===l.GeometryOffsetAttribute.NONE?0:1,n=new Uint8Array(t/3).fill(e);A.attributes.applyOffset=new r.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:n})}x=n.BoundingSphere.fromRectangle3D(i,s,h)}return g.position||delete A.attributes.position,new r.Geometry({attributes:A.attributes,indices:A.indices,primitiveType:A.primitiveType,boundingSphere:x,offsetAttribute:a._offsetAttribute})},Y.createShadowVolume=function(t,e,n){const a=t._granularity,o=t._ellipsoid,r=e(a,o),i=n(a,o);return new Y({rectangle:t._rectangle,rotation:t._rotation,ellipsoid:o,stRotation:t._stRotation,granularity:a,extrudedHeight:i,height:r,vertexFormat:g.VertexFormat.POSITION_ONLY,shadowVolume:!0})};const Z=new e.Rectangle,K=[new a.Cartesian2,new a.Cartesian2,new a.Cartesian2],$=new e.Matrix2,tt=new e.Cartographic;return Object.defineProperties(Y.prototype,{rectangle:{get:function(){return t.defined(this._rotatedRectangle)||(this._rotatedRectangle=U(this._rectangle,this._granularity,this._rotation,this._ellipsoid)),this._rotatedRectangle}},textureCoordinateRotationPoints:{get:function(){return t.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){if(0===t._stRotation)return[0,0,0,1,1,0];const n=e.Rectangle.clone(t._rectangle,Z),o=t._granularity,r=t._ellipsoid,i=U(n,o,t._rotation-t._stRotation,r,Z),s=K;s[0].x=i.west,s[0].y=i.south,s[1].x=i.west,s[1].y=i.north,s[2].x=i.east,s[2].y=i.south;const l=t.rectangle,u=e.Matrix2.fromRotation(t._stRotation,$),c=e.Rectangle.center(l,tt);for(let t=0;t<3;++t){const n=s[t];n.x-=c.longitude,n.y-=c.latitude,e.Matrix2.multiplyByVector(u,n,n),n.x+=c.longitude,n.y+=c.latitude,n.x=(n.x-l.west)/l.width,n.y=(n.y-l.south)/l.height}const m=s[0],d=s[1],p=s[2],g=new Array(6);return a.Cartesian2.pack(m,g),a.Cartesian2.pack(d,g,2),a.Cartesian2.pack(p,g,4),g}(this)),this._textureCoordinateRotationPoints}}}),function(n,a){return t.defined(a)&&(n=Y.unpack(n,a)),n._ellipsoid=e.Ellipsoid.clone(n._ellipsoid),n._rectangle=e.Rectangle.clone(n._rectangle),Y.createGeometry(n)}}));
