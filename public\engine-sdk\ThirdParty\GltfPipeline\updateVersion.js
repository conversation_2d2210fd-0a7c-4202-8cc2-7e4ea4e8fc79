import addExtensionsUsed from"./addExtensionsUsed.js";import addToArray from"./addToArray.js";import findAccessorMinMax from"./findAccessorMinMax.js";import ForEach from"./ForEach.js";import getAccessorByteStride from"./getAccessorByteStride.js";import numberOfComponentsForType from"./numberOfComponentsForType.js";import moveTechniqueRenderStates from"./moveTechniqueRenderStates.js";import moveTechniquesToExtension from"./moveTechniquesToExtension.js";import removeUnusedElements from"./removeUnusedElements.js";import updateAccessorComponentTypes from"./updateAccessorComponentTypes.js";import Cartesian3 from"../../Core/Cartesian3.js";import Cartesian4 from"../../Core/Cartesian4.js";import clone from"../../Core/clone.js";import ComponentDatatype from"../../Core/ComponentDatatype.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import Matrix4 from"../../Core/Matrix4.js";import Quaternion from"../../Core/Quaternion.js";import WebGLConstants from"../../Core/WebGLConstants.js";var updateFunctions={.8:glTF08to10,"1.0":glTF10to20,"2.0":void 0};function updateVersion(e,t){t=defaultValue(t,defaultValue.EMPTY_OBJECT);var n=t.targetVersion,r=e.version;e.asset=defaultValue(e.asset,{version:"1.0"}),e.asset.version=defaultValue(e.asset.version,"1.0"),r=defaultValue(r,e.asset.version).toString(),Object.prototype.hasOwnProperty.call(updateFunctions,r)||(defined(r)&&(r=r.substring(0,3)),Object.prototype.hasOwnProperty.call(updateFunctions,r)||(r="1.0"));var i=updateFunctions[r];while(defined(i)){if(r===n)break;i(e,t),r=e.asset.version,i=updateFunctions[r]}return e}function updateInstanceTechniques(e){var t=e.materials;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n],i=r.instanceTechnique;defined(i)&&(r.technique=i.technique,r.values=i.values,delete r.instanceTechnique)}}function setPrimitiveModes(e){var t=e.meshes;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n],i=r.primitives;if(defined(i))for(var a=i.length,o=0;o<a;++o){var s=i[o],f=defaultValue(s.primitive,WebGLConstants.TRIANGLES);s.mode=defaultValue(s.mode,f),delete s.primitive}}}function updateNodes(e){var t=e.nodes,n=new Cartesian3,r=new Quaternion;for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var a=t[i];if(defined(a.rotation)){var o=a.rotation;Cartesian3.fromArray(o,0,n),Quaternion.fromAxisAngle(n,o[3],r),a.rotation=[r.x,r.y,r.z,r.w]}var s=a.instanceSkin;defined(s)&&(a.skeletons=s.skeletons,a.skin=s.skin,a.meshes=s.meshes,delete a.instanceSkin)}}function updateAnimations(e){var t=e.animations,n=e.accessors,r=e.bufferViews,i=e.buffers,a={},o=new Cartesian3,s=new Quaternion;for(var f in t)if(Object.prototype.hasOwnProperty.call(t,f)){var d=t[f],c=d.channels,u=d.parameters,m=d.samplers;if(defined(c))for(var l=c.length,p=0;p<l;++p){var h=c[p];if("rotation"===h.target.path){var v=u[m[h.sampler].output];if(defined(a[v]))continue;a[v]=!0;for(var y=n[v],b=r[y.bufferView],E=i[b.buffer],O=E.extras._pipeline.source,T=O.byteOffset+b.byteOffset+y.byteOffset,g=y.componentType,x=y.count,F=numberOfComponentsForType(y.type),A=y.count*F,w=ComponentDatatype.createArrayBufferView(g,O.buffer,T,A),C=0;C<x;C++){var V=C*F;Cartesian3.unpack(w,V,o);var S=w[V+3];Quaternion.fromAxisAngle(o,S,s),Quaternion.pack(s,w,V)}}}}}function removeTechniquePasses(e){var t=e.techniques;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n],i=r.passes;if(defined(i)){var a=defaultValue(r.pass,"defaultPass");if(Object.prototype.hasOwnProperty.call(i,a)){var o=i[a],s=o.instanceProgram;r.attributes=defaultValue(r.attributes,s.attributes),r.program=defaultValue(r.program,s.program),r.uniforms=defaultValue(r.uniforms,s.uniforms),r.states=defaultValue(r.states,o.states)}delete r.passes,delete r.pass}}}function glTF08to10(e){defined(e.asset)||(e.asset={});var t=e.asset;if(t.version="1.0","string"===typeof t.profile){var n=t.profile.split(" ");t.profile={api:n[0],version:n[1]}}else t.profile={};if(defined(e.version)&&delete e.version,updateInstanceTechniques(e),setPrimitiveModes(e),updateNodes(e),updateAnimations(e),removeTechniquePasses(e),defined(e.allExtensions)&&(e.extensionsUsed=e.allExtensions,delete e.allExtensions),defined(e.lights)){var r=defaultValue(e.extensions,{});e.extensions=r;var i=defaultValue(r.KHR_materials_common,{});r.KHR_materials_common=i,i.lights=e.lights,delete e.lights,addExtensionsUsed(e,"KHR_materials_common")}}function removeAnimationSamplersIndirection(e){var t=e.animations;for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n],i=r.parameters;if(defined(i)){var a=r.samplers;for(var o in a)if(Object.prototype.hasOwnProperty.call(a,o)){var s=a[o];s.input=i[s.input],s.output=i[s.output]}delete r.parameters}}}function objectToArray(e,t){var n=[];for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){var i=e[r];t[r]=n.length,n.push(i),defined(i.name)||(i.name=r)}return n}function objectsToArrays(e){var t,n,r={accessors:{},animations:{},buffers:{},bufferViews:{},cameras:{},images:{},materials:{},meshes:{},nodes:{},programs:{},samplers:{},scenes:{},shaders:{},skins:{},textures:{},techniques:{}},i={},a=e.nodes;for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(n=a[o].jointName,defined(n)&&(i[n]=o));for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&defined(r[s])){var f={},d=e[s];e[s]=objectToArray(d,f),r[s]=f}for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(i[n]=r.nodes[i[n]]);defined(e.scene)&&(e.scene=r.scenes[e.scene]),ForEach.bufferView(e,(function(e){defined(e.buffer)&&(e.buffer=r.buffers[e.buffer])})),ForEach.accessor(e,(function(e){defined(e.bufferView)&&(e.bufferView=r.bufferViews[e.bufferView])})),ForEach.shader(e,(function(e){var t=e.extensions;if(defined(t)){var n=t.KHR_binary_glTF;defined(n)&&(e.bufferView=r.bufferViews[n.bufferView],delete t.KHR_binary_glTF),0===Object.keys(t).length&&delete e.extensions}})),ForEach.program(e,(function(e){defined(e.vertexShader)&&(e.vertexShader=r.shaders[e.vertexShader]),defined(e.fragmentShader)&&(e.fragmentShader=r.shaders[e.fragmentShader])})),ForEach.technique(e,(function(e){defined(e.program)&&(e.program=r.programs[e.program]),ForEach.techniqueParameter(e,(function(e){defined(e.node)&&(e.node=r.nodes[e.node]);var t=e.value;"string"===typeof t&&(e.value={index:r.textures[t]})}))})),ForEach.mesh(e,(function(e){ForEach.meshPrimitive(e,(function(e){defined(e.indices)&&(e.indices=r.accessors[e.indices]),ForEach.meshPrimitiveAttribute(e,(function(t,n){e.attributes[n]=r.accessors[t]})),defined(e.material)&&(e.material=r.materials[e.material])}))})),ForEach.node(e,(function(n){var i=n.children;if(defined(i)){var a=i.length;for(t=0;t<a;++t)i[t]=r.nodes[i[t]]}if(defined(n.meshes)){var o=n.meshes,s=o.length;if(s>0)for(n.mesh=r.meshes[o[0]],t=1;t<s;++t){var f={mesh:r.meshes[o[t]]},d=addToArray(e.nodes,f);defined(i)||(i=[],n.children=i),i.push(d)}delete n.meshes}if(defined(n.camera)&&(n.camera=r.cameras[n.camera]),defined(n.skin)&&(n.skin=r.skins[n.skin]),defined(n.skeletons)){var c=n.skeletons,u=c.length;if(u>0&&defined(n.skin)){var m=e.skins[n.skin];m.skeleton=r.nodes[c[0]]}delete n.skeletons}defined(n.jointName)&&delete n.jointName})),ForEach.skin(e,(function(e){defined(e.inverseBindMatrices)&&(e.inverseBindMatrices=r.accessors[e.inverseBindMatrices]);var n=e.jointNames;if(defined(n)){var a=[],o=n.length;for(t=0;t<o;++t)a[t]=i[n[t]];e.joints=a,delete e.jointNames}})),ForEach.scene(e,(function(e){var n=e.nodes;if(defined(n)){var i=n.length;for(t=0;t<i;++t)n[t]=r.nodes[n[t]]}})),ForEach.animation(e,(function(e){var t={};e.samplers=objectToArray(e.samplers,t),ForEach.animationSampler(e,(function(e){e.input=r.accessors[e.input],e.output=r.accessors[e.output]})),ForEach.animationChannel(e,(function(e){e.sampler=t[e.sampler];var n=e.target;defined(n)&&(n.node=r.nodes[n.id],delete n.id)}))})),ForEach.material(e,(function(e){defined(e.technique)&&(e.technique=r.techniques[e.technique]),ForEach.materialValue(e,(function(t,n){"string"===typeof t&&(e.values[n]={index:r.textures[t]})}));var t=e.extensions;if(defined(t)){var n=t.KHR_materials_common;defined(n)&&ForEach.materialValue(n,(function(e,t){"string"===typeof e&&(n.values[t]={index:r.textures[e]})}))}})),ForEach.image(e,(function(e){var t=e.extensions;if(defined(t)){var n=t.KHR_binary_glTF;defined(n)&&(e.bufferView=r.bufferViews[n.bufferView],e.mimeType=n.mimeType,delete t.KHR_binary_glTF),0===Object.keys(t).length&&delete e.extensions}ForEach.compressedImage(e,(function(e){var n=e.extensions;if(defined(n)){var i=n.KHR_binary_glTF;defined(i)&&(e.bufferView=r.bufferViews[i.bufferView],e.mimeType=i.mimeType,delete n.KHR_binary_glTF),0===Object.keys(t).length&&delete e.extensions}}))})),ForEach.texture(e,(function(e){defined(e.sampler)&&(e.sampler=r.samplers[e.sampler]),defined(e.source)&&(e.source=r.images[e.source])}))}function removeAnimationSamplerNames(e){ForEach.animation(e,(function(e){ForEach.animationSampler(e,(function(e){delete e.name}))}))}function removeEmptyArrays(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var n=e[t];Array.isArray(n)&&0===n.length&&delete e[t]}ForEach.node(e,(function(e){defined(e.children)&&0===e.children.length&&delete e.children}))}function stripAsset(e){var t=e.asset;delete t.profile,delete t.premultipliedAlpha}var knownExtensions={CESIUM_RTC:!0,KHR_materials_common:!0,WEB3D_quantized_attributes:!0};function requireKnownExtensions(e){var t=e.extensionsUsed;if(e.extensionsRequired=defaultValue(e.extensionsRequired,[]),defined(t))for(var n=t.length,r=0;r<n;++r){var i=t[r];defined(knownExtensions[i])&&e.extensionsRequired.push(i)}}function removeBufferType(e){ForEach.buffer(e,(function(e){delete e.type}))}function removeTextureProperties(e){ForEach.texture(e,(function(e){delete e.format,delete e.internalFormat,delete e.target,delete e.type}))}function requireAttributeSetIndex(e){ForEach.mesh(e,(function(e){ForEach.meshPrimitive(e,(function(e){ForEach.meshPrimitiveAttribute(e,(function(t,n){"TEXCOORD"===n?e.attributes.TEXCOORD_0=t:"COLOR"===n&&(e.attributes.COLOR_0=t)})),delete e.attributes.TEXCOORD,delete e.attributes.COLOR}))})),ForEach.technique(e,(function(e){ForEach.techniqueParameter(e,(function(e){var t=e.semantic;defined(t)&&("TEXCOORD"===t?e.semantic="TEXCOORD_0":"COLOR"===t&&(e.semantic="COLOR_0"))}))}))}var knownSemantics={POSITION:!0,NORMAL:!0,TANGENT:!0},indexedSemantics={COLOR:"COLOR",JOINT:"JOINTS",JOINTS:"JOINTS",TEXCOORD:"TEXCOORD",WEIGHT:"WEIGHTS",WEIGHTS:"WEIGHTS"};function underscoreApplicationSpecificSemantics(e){var t={};ForEach.mesh(e,(function(e){ForEach.meshPrimitive(e,(function(e){for(var n in ForEach.meshPrimitiveAttribute(e,(function(e,n){if("_"!==n.charAt(0)){var r,i=n.search(/_[0-9]+/g),a=n,o="_0";i>=0&&(a=n.substring(0,i),o=n.substring(i));var s=indexedSemantics[a];defined(s)?(r=s+o,t[n]=r):defined(knownSemantics[a])||(r="_"+n,t[n]=r)}})),t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n],i=e.attributes[n];defined(i)&&(delete e.attributes[n],e.attributes[r]=i)}}))})),ForEach.technique(e,(function(e){ForEach.techniqueParameter(e,(function(e){var n=t[e.semantic];defined(n)&&(e.semantic=n)}))}))}function clampCameraParameters(e){ForEach.camera(e,(function(e){var t=e.perspective;if(defined(t)){var n=t.aspectRatio;defined(n)&&0===n&&delete t.aspectRatio;var r=t.yfov;defined(r)&&0===r&&(t.yfov=1)}}))}function computeAccessorByteStride(e,t){return defined(t.byteStride)&&0!==t.byteStride?t.byteStride:getAccessorByteStride(e,t)}function requireByteLength(e){ForEach.buffer(e,(function(e){defined(e.byteLength)||(e.byteLength=e.extras._pipeline.source.length)})),ForEach.accessor(e,(function(t){var n=t.bufferView;if(defined(n)){var r=e.bufferViews[n],i=computeAccessorByteStride(e,t),a=t.byteOffset+t.count*i;r.byteLength=Math.max(defaultValue(r.byteLength,0),a)}}))}function moveByteStrideToBufferView(e){var t,n,r,i=e.bufferViews,a={};ForEach.accessorContainingVertexAttributeData(e,(function(t){var n=e.accessors[t];defined(n.bufferView)&&(a[n.bufferView]=!0)}));var o={};for(var s in ForEach.accessor(e,(function(e){defined(e.bufferView)&&(o[e.bufferView]=defaultValue(o[e.bufferView],[]),o[e.bufferView].push(e))})),o)if(Object.prototype.hasOwnProperty.call(o,s)){r=i[s];var f=o[s];f.sort((function(e,t){return e.byteOffset-t.byteOffset}));var d=0,c=0,u=f.length;for(t=0;t<u;++t){var m=f[t],l=computeAccessorByteStride(e,m),p=m.byteOffset,h=m.count*l;delete m.byteStride;var v=t<u-1,y=v?computeAccessorByteStride(e,f[t+1]):void 0;if(l!==y){var b=clone(r,!0);a[s]&&(b.byteStride=l),b.byteOffset+=d,b.byteLength=p+h-d;var E=addToArray(i,b);for(n=c;n<=t;++n)m=f[n],m.bufferView=E,m.byteOffset=m.byteOffset-d;d=v?f[t+1].byteOffset:void 0,c=t+1}}}removeUnusedElements(e,["accessor","bufferView","buffer"])}function requirePositionAccessorMinMax(e){ForEach.accessorWithSemantic(e,"POSITION",(function(t){var n=e.accessors[t];if(!defined(n.min)||!defined(n.max)){var r=findAccessorMinMax(e,n);n.min=r.min,n.max=r.max}}))}function isNodeEmpty(e){return(!defined(e.children)||0===e.children.length)&&(!defined(e.meshes)||0===e.meshes.length)&&!defined(e.camera)&&!defined(e.skin)&&!defined(e.skeletons)&&!defined(e.jointName)&&(!defined(e.translation)||Cartesian3.fromArray(e.translation).equals(Cartesian3.ZERO))&&(!defined(e.scale)||Cartesian3.fromArray(e.scale).equals(new Cartesian3(1,1,1)))&&(!defined(e.rotation)||Cartesian4.fromArray(e.rotation).equals(new Cartesian4(0,0,0,1)))&&(!defined(e.matrix)||Matrix4.fromColumnMajorArray(e.matrix).equals(Matrix4.IDENTITY))&&!defined(e.extensions)&&!defined(e.extras)}function deleteNode(e,t){ForEach.scene(e,(function(e){var n=e.nodes;if(defined(n))for(var r=n.length,i=r;i>=0;--i)if(n[i]===t)return void n.splice(i,1)})),ForEach.node(e,(function(n,r){if(defined(n.children)){var i=n.children.indexOf(t);i>-1&&(n.children.splice(i,1),isNodeEmpty(n)&&deleteNode(e,r))}})),delete e.nodes[t]}function removeEmptyNodes(e){return ForEach.node(e,(function(t,n){isNodeEmpty(t)&&deleteNode(e,n)})),e}function requireAnimationAccessorMinMax(e){ForEach.animation(e,(function(t){ForEach.animationSampler(t,(function(t){var n=e.accessors[t.input];if(!defined(n.min)||!defined(n.max)){var r=findAccessorMinMax(e,n);n.min=r.min,n.max=r.max}}))}))}function glTF10to20(e){e.asset=defaultValue(e.asset,{}),e.asset.version="2.0",updateInstanceTechniques(e),removeAnimationSamplersIndirection(e),removeEmptyNodes(e),objectsToArrays(e),removeAnimationSamplerNames(e),stripAsset(e),requireKnownExtensions(e),requireByteLength(e),moveByteStrideToBufferView(e),requirePositionAccessorMinMax(e),requireAnimationAccessorMinMax(e),removeBufferType(e),removeTextureProperties(e),requireAttributeSetIndex(e),underscoreApplicationSpecificSemantics(e),updateAccessorComponentTypes(e),clampCameraParameters(e),moveTechniqueRenderStates(e),moveTechniquesToExtension(e),removeEmptyArrays(e)}export default updateVersion;