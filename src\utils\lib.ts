/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-30 10:17:18
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-04-28 14:49:07
 * @FilePath: \bigscreen-qj-web\src\utils\lib.ts
 * @Description:封装Hooks，Provide，Store等
 *
 */
import StructureDiagram from './structureDiagram.js'
import * as api from '@/api/index.js'
import EngineController from '@/components/BimEngine/index'
import useUe from '@/hooks/useUE/index.js'
import * as ueTools from '@/hooks/useUE/tools.js'
import useStore from '@/store/index.js'
import { bus } from '@/utils/eventBus.js'
import * as utils from '@/utils/index'
import { provideTools } from '@/utils/provideMap.js'
import * as popWindow from '@/views/Screen/MapPopupWindow/MapPopWindow.js'
import * as enumMap from './enums'
import * as ai from './ai'

export default {
  /**
   * @description: Pinia
   */
  store: () => useStore(),
  /**
   * @description: UE引擎相关
   */
  ue: () => useUe(),
  /**
   * @description: ue工具集
   */
  ueTools,
  /**
   * @description: EventBus统一管理
   */
  /**
   * @description: EventBus统一管理
   */
  bus,
  /**
   * @description: provide,inject统一管理
   */
  provideTools,
  /**
   * @description: 接口
   */
  api,
  ai,
  /**
   * @description: 公用方法
   */
  utils,
  /**
   * @description: 弹出框方法
   */
  popWindow,
  typeIdMap: {
    /**
     * @description: 动态养护
     */
    动态养护: 10000,
    突发事件: 10001,
    设备类型: 10002,
    供配电系统: 10003,
    火灾报警和消防系统: 10004,
    排水系统: 10005,
    通风系统: 10006,
    通信系统: 10007,
    综合监控系统: 10008,
    照明系统: 10009
  },
  /**
   * @description: 预设结构图Canvas （此处定义一个示例仅仅是为了在vscode中能够有代码提示，没有实际意义）
   */
  structureDiagram: new StructureDiagram(),
  /** UE引擎实例 */
  _engineController: new EngineController(null, null, null, null, null),
  /** 当前跟随车辆车牌号 */
  currentFollowCarPlate: null,
  enumsList: {
    point: {
      突发事件撒点: 'emergencyPoint',
      设备撒点: 'devicePoint',
      设施撒点: 'structurePoint'
    },
    widows: {
      Ai对话框: 'AiDialog'
    }
  },
  codeBimInfoMap: new Map(),
  enumMap
}
