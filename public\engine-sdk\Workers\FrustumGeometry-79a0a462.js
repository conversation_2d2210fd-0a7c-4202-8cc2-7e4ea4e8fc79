define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./Plane-1b1689fd","./VertexFormat-9b18d410"],(function(e,t,r,a,i,n,o,s,f,u,d,h,p){"use strict";function l(e){this.planes=t.defaultValue(e,[])}var c=[new a.Cartesian3,new a.Cartesian3,new a.Cartesian3];a.Cartesian3.clone(a.Cartesian3.UNIT_X,c[0]),a.Cartesian3.clone(a.<PERSON><PERSON>3.UNIT_Y,c[1]),a.Cartesian3.clone(a.Cartesian3.UNIT_Z,c[2]);var m=new a.Cartesian3,C=new a.Cartesian3,w=new h.Plane(new a.Cartesian3(1,0,0),0);function v(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=t.defaultValue(e.near,1),this._near=this.near,this.far=t.defaultValue(e.far,5e8),this._far=this.far,this._cullingVolume=new l,this._orthographicMatrix=new o.Matrix4}function _(e){if(!t.defined(e.right)||!t.defined(e.left)||!t.defined(e.top)||!t.defined(e.bottom)||!t.defined(e.near)||!t.defined(e.far))throw new r.DeveloperError("right, left, top, bottom, near, or far parameters are not set.");if(e.top!==e._top||e.bottom!==e._bottom||e.left!==e._left||e.right!==e._right||e.near!==e._near||e.far!==e._far){if(e.left>e.right)throw new r.DeveloperError("right must be greater than left.");if(e.bottom>e.top)throw new r.DeveloperError("top must be greater than bottom.");if(e.near<=0||e.near>e.far)throw new r.DeveloperError("near must be greater than zero and less than far.");e._left=e.left,e._right=e.right,e._top=e.top,e._bottom=e.bottom,e._near=e.near,e._far=e.far,e._orthographicMatrix=o.Matrix4.computeOrthographicOffCenter(e.left,e.right,e.bottom,e.top,e.near,e.far,e._orthographicMatrix)}}l.fromBoundingSphere=function(e,i){if(!t.defined(e))throw new r.DeveloperError("boundingSphere is required.");t.defined(i)||(i=new l);var n=c.length,s=i.planes;s.length=2*n;for(var f=e.center,u=e.radius,d=0,h=0;h<n;++h){var p=c[h],w=s[d],v=s[d+1];t.defined(w)||(w=s[d]=new o.Cartesian4),t.defined(v)||(v=s[d+1]=new o.Cartesian4),a.Cartesian3.multiplyByScalar(p,-u,m),a.Cartesian3.add(f,m,m),w.x=p.x,w.y=p.y,w.z=p.z,w.w=-a.Cartesian3.dot(p,m),a.Cartesian3.multiplyByScalar(p,u,m),a.Cartesian3.add(f,m,m),v.x=-p.x,v.y=-p.y,v.z=-p.z,v.w=-a.Cartesian3.dot(a.Cartesian3.negate(p,C),m),d+=2}return i},l.prototype.computeVisibility=function(e){if(!t.defined(e))throw new r.DeveloperError("boundingVolume is required.");for(var a=this.planes,i=!1,o=0,s=a.length;o<s;++o){var f=e.intersectPlane(h.Plane.fromCartesian4(a[o],w));if(f===n.Intersect.OUTSIDE)return n.Intersect.OUTSIDE;f===n.Intersect.INTERSECTING&&(i=!0)}return i?n.Intersect.INTERSECTING:n.Intersect.INSIDE},l.prototype.computeVisibilityWithPlaneMask=function(e,a){if(!t.defined(e))throw new r.DeveloperError("boundingVolume is required.");if(!t.defined(a))throw new r.DeveloperError("parentPlaneMask is required.");if(a===l.MASK_OUTSIDE||a===l.MASK_INSIDE)return a;for(var i=l.MASK_INSIDE,o=this.planes,s=0,f=o.length;s<f;++s){var u=s<31?1<<s:0;if(!(s<31&&0===(a&u))){var d=e.intersectPlane(h.Plane.fromCartesian4(o[s],w));if(d===n.Intersect.OUTSIDE)return l.MASK_OUTSIDE;d===n.Intersect.INTERSECTING&&(i|=u)}}return i},l.MASK_OUTSIDE=4294967295,l.MASK_INSIDE=0,l.MASK_INDETERMINATE=2147483647,Object.defineProperties(v.prototype,{projectionMatrix:{get:function(){return _(this),this._orthographicMatrix}}});var y=new a.Cartesian3,g=new a.Cartesian3,x=new a.Cartesian3,b=new a.Cartesian3;function E(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new v,this.width=e.width,this._width=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=t.defaultValue(e.near,1),this._near=this.near,this.far=t.defaultValue(e.far,5e3),this.width=t.defaultValue(e.width,1e3),this._width=void 0,this.aspectRatio=t.defaultValue(e.aspectRatio,1),this._aspectRatio=void 0,M(this)}function M(e){if(!t.defined(e.width)||!t.defined(e.aspectRatio)||!t.defined(e.near)||!t.defined(e.far))throw new r.DeveloperError("width, aspectRatio, near, or far parameters are not set.");var a=e._offCenterFrustum;if(e.width!==e._width||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far){if(e.aspectRatio<0)throw new r.DeveloperError("aspectRatio must be positive.");if(e.near<0||e.near>e.far)throw new r.DeveloperError("near must be greater than zero and less than far.");e._aspectRatio=e.aspectRatio,e._width=e.width,e._near=e.near,e._far=e.far;var i=1/e.aspectRatio;a.right=.5*e.width,a.left=-a.right,a.top=i*a.right,a.bottom=-a.top,a.near=e.near,a.far=e.far}}function D(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this.left=e.left,this._left=void 0,this.right=e.right,this._right=void 0,this.top=e.top,this._top=void 0,this.bottom=e.bottom,this._bottom=void 0,this.near=t.defaultValue(e.near,1),this._near=this.near,this.far=t.defaultValue(e.far,5e8),this._far=this.far,this._cullingVolume=new l,this._perspectiveMatrix=new o.Matrix4,this._infinitePerspective=new o.Matrix4}function F(e){if(!t.defined(e.right)||!t.defined(e.left)||!t.defined(e.top)||!t.defined(e.bottom)||!t.defined(e.near)||!t.defined(e.far))throw new r.DeveloperError("right, left, top, bottom, near, or far parameters are not set.");var a=e.top,i=e.bottom,n=e.right,s=e.left,f=e.near,u=e.far;if(a!==e._top||i!==e._bottom||s!==e._left||n!==e._right||f!==e._near||u!==e._far){if(e.near<=0||e.near>e.far)throw new r.DeveloperError("near must be greater than zero and less than far.");e._left=s,e._right=n,e._top=a,e._bottom=i,e._near=f,e._far=u,e._perspectiveMatrix=o.Matrix4.computePerspectiveOffCenter(s,n,i,a,f,u,e._perspectiveMatrix),e._infinitePerspective=o.Matrix4.computeInfinitePerspectiveOffCenter(s,n,i,a,f,e._infinitePerspective)}}v.prototype.computeCullingVolume=function(e,i,n){if(!t.defined(e))throw new r.DeveloperError("position is required.");if(!t.defined(i))throw new r.DeveloperError("direction is required.");if(!t.defined(n))throw new r.DeveloperError("up is required.");var s=this._cullingVolume.planes,f=this.top,u=this.bottom,d=this.right,h=this.left,p=this.near,l=this.far,c=a.Cartesian3.cross(i,n,y);a.Cartesian3.normalize(c,c);var m=g;a.Cartesian3.multiplyByScalar(i,p,m),a.Cartesian3.add(e,m,m);var C=x;a.Cartesian3.multiplyByScalar(c,h,C),a.Cartesian3.add(m,C,C);var w=s[0];return t.defined(w)||(w=s[0]=new o.Cartesian4),w.x=c.x,w.y=c.y,w.z=c.z,w.w=-a.Cartesian3.dot(c,C),a.Cartesian3.multiplyByScalar(c,d,C),a.Cartesian3.add(m,C,C),w=s[1],t.defined(w)||(w=s[1]=new o.Cartesian4),w.x=-c.x,w.y=-c.y,w.z=-c.z,w.w=-a.Cartesian3.dot(a.Cartesian3.negate(c,b),C),a.Cartesian3.multiplyByScalar(n,u,C),a.Cartesian3.add(m,C,C),w=s[2],t.defined(w)||(w=s[2]=new o.Cartesian4),w.x=n.x,w.y=n.y,w.z=n.z,w.w=-a.Cartesian3.dot(n,C),a.Cartesian3.multiplyByScalar(n,f,C),a.Cartesian3.add(m,C,C),w=s[3],t.defined(w)||(w=s[3]=new o.Cartesian4),w.x=-n.x,w.y=-n.y,w.z=-n.z,w.w=-a.Cartesian3.dot(a.Cartesian3.negate(n,b),C),w=s[4],t.defined(w)||(w=s[4]=new o.Cartesian4),w.x=i.x,w.y=i.y,w.z=i.z,w.w=-a.Cartesian3.dot(i,m),a.Cartesian3.multiplyByScalar(i,l,C),a.Cartesian3.add(e,C,C),w=s[5],t.defined(w)||(w=s[5]=new o.Cartesian4),w.x=-i.x,w.y=-i.y,w.z=-i.z,w.w=-a.Cartesian3.dot(a.Cartesian3.negate(i,b),C),this._cullingVolume},v.prototype.getPixelDimensions=function(e,a,i,n,o){if(_(this),!t.defined(e)||!t.defined(a))throw new r.DeveloperError("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new r.DeveloperError("drawingBufferWidth must be greater than zero.");if(a<=0)throw new r.DeveloperError("drawingBufferHeight must be greater than zero.");if(!t.defined(i))throw new r.DeveloperError("distance is required.");if(!t.defined(n))throw new r.DeveloperError("pixelRatio is required.");if(n<=0)throw new r.DeveloperError("pixelRatio must be greater than zero.");if(!t.defined(o))throw new r.DeveloperError("A result object is required.");var s=this.right-this.left,f=this.top-this.bottom,u=n*s/e,d=n*f/a;return o.x=u,o.y=d,o},v.prototype.clone=function(e){return t.defined(e)||(e=new v),e.left=this.left,e.right=this.right,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e},v.prototype.equals=function(e){return t.defined(e)&&e instanceof v&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far},v.prototype.equalsEpsilon=function(e,r,i){return e===this||t.defined(e)&&e instanceof v&&a.CesiumMath.equalsEpsilon(this.right,e.right,r,i)&&a.CesiumMath.equalsEpsilon(this.left,e.left,r,i)&&a.CesiumMath.equalsEpsilon(this.top,e.top,r,i)&&a.CesiumMath.equalsEpsilon(this.bottom,e.bottom,r,i)&&a.CesiumMath.equalsEpsilon(this.near,e.near,r,i)&&a.CesiumMath.equalsEpsilon(this.far,e.far,r,i)},E.packedLength=4,E.pack=function(e,a,i){return r.Check.typeOf.object("value",e),r.Check.defined("array",a),i=t.defaultValue(i,0),a[i++]=e.width,a[i++]=e.aspectRatio,a[i++]=e.near,a[i]=e.far,a},E.unpack=function(e,a,i){return r.Check.defined("array",e),a=t.defaultValue(a,0),t.defined(i)||(i=new E),i.width=e[a++],i.aspectRatio=e[a++],i.near=e[a++],i.far=e[a],i},Object.defineProperties(E.prototype,{left:{get:function(){return this._offCenterFrustum.left}},right:{get:function(){return this._offCenterFrustum.right}},top:{get:function(){return this._offCenterFrustum.top}},bottom:{get:function(){return this._offCenterFrustum.bottom}},projectionMatrix:{get:function(){return M(this),this._offCenterFrustum.projectionMatrix}}}),E.prototype.computeCullingVolume=function(e,t,r){return M(this),this._offCenterFrustum.computeCullingVolume(e,t,r)},E.prototype.getPixelDimensions=function(e,t,r,a,i){return M(this),this._offCenterFrustum.getPixelDimensions(e,t,r,a,i)},E.prototype.clone=function(e){return t.defined(e)||(e=new E),e.aspectRatio=this.aspectRatio,e.width=this.width,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._width=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),M(e),e},E.prototype.equals=function(e){return!!(t.defined(e)&&e instanceof E)&&(M(this),M(e),this.width===e.width&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))},E.prototype.equalsEpsilon=function(e,r,i){return!!(t.defined(e)&&e instanceof E)&&(M(this),M(e),a.CesiumMath.equalsEpsilon(this.width,e.width,r,i)&&a.CesiumMath.equalsEpsilon(this.aspectRatio,e.aspectRatio,r,i)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,r,i))},Object.defineProperties(D.prototype,{projectionMatrix:{get:function(){return F(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return F(this),this._infinitePerspective}}});var V=new a.Cartesian3,R=new a.Cartesian3,O=new a.Cartesian3,z=new a.Cartesian3;function k(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new D,this.fov=e.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=e.aspectRatio,this._aspectRatio=void 0,this.near=t.defaultValue(e.near,.1),this._near=this.near,this.far=t.defaultValue(e.far,5e8),this._far=this.far,this.xOffset=t.defaultValue(e.xOffset,0),this._xOffset=this.xOffset,this.yOffset=t.defaultValue(e.yOffset,0),this._yOffset=this.yOffset}function P(e){if(!t.defined(e.fov)||!t.defined(e.aspectRatio)||!t.defined(e.near)||!t.defined(e.far))throw new r.DeveloperError("fov, aspectRatio, near, or far parameters are not set.");var a=e._offCenterFrustum;if(e.fov!==e._fov||e.aspectRatio!==e._aspectRatio||e.near!==e._near||e.far!==e._far||e.xOffset!==e._xOffset||e.yOffset!==e._yOffset){if(e.fov<0||e.fov>=Math.PI)throw new r.DeveloperError("fov must be in the range [0, PI).");if(e.aspectRatio<0)throw new r.DeveloperError("aspectRatio must be positive.");if(e.near<0||e.near>e.far)throw new r.DeveloperError("near must be greater than zero and less than far.");e._aspectRatio=e.aspectRatio,e._fov=e.fov,e._fovy=e.aspectRatio>1?e.fov:2*Math.atan(Math.tan(.5*e.fov)/e.aspectRatio),e._near=e.near,e._far=e.far,e._sseDenominator=2*Math.tan(.5*e._fovy),e._xOffset=e.xOffset,e._yOffset=e.yOffset,a.top=e.near*Math.tan(.5*e._fovy),a.bottom=-a.top,a.right=e.aspectRatio*a.top,a.left=-a.right,a.near=e.near,a.far=e.far,a.right+=e.xOffset,a.left+=e.xOffset,a.top+=e.yOffset,a.bottom+=e.yOffset}}D.prototype.computeCullingVolume=function(e,i,n){if(!t.defined(e))throw new r.DeveloperError("position is required.");if(!t.defined(i))throw new r.DeveloperError("direction is required.");if(!t.defined(n))throw new r.DeveloperError("up is required.");var s=this._cullingVolume.planes,f=this.top,u=this.bottom,d=this.right,h=this.left,p=this.near,l=this.far,c=a.Cartesian3.cross(i,n,V),m=R;a.Cartesian3.multiplyByScalar(i,p,m),a.Cartesian3.add(e,m,m);var C=O;a.Cartesian3.multiplyByScalar(i,l,C),a.Cartesian3.add(e,C,C);var w=z;a.Cartesian3.multiplyByScalar(c,h,w),a.Cartesian3.add(m,w,w),a.Cartesian3.subtract(w,e,w),a.Cartesian3.normalize(w,w),a.Cartesian3.cross(w,n,w),a.Cartesian3.normalize(w,w);var v=s[0];return t.defined(v)||(v=s[0]=new o.Cartesian4),v.x=w.x,v.y=w.y,v.z=w.z,v.w=-a.Cartesian3.dot(w,e),a.Cartesian3.multiplyByScalar(c,d,w),a.Cartesian3.add(m,w,w),a.Cartesian3.subtract(w,e,w),a.Cartesian3.cross(n,w,w),a.Cartesian3.normalize(w,w),v=s[1],t.defined(v)||(v=s[1]=new o.Cartesian4),v.x=w.x,v.y=w.y,v.z=w.z,v.w=-a.Cartesian3.dot(w,e),a.Cartesian3.multiplyByScalar(n,u,w),a.Cartesian3.add(m,w,w),a.Cartesian3.subtract(w,e,w),a.Cartesian3.cross(c,w,w),a.Cartesian3.normalize(w,w),v=s[2],t.defined(v)||(v=s[2]=new o.Cartesian4),v.x=w.x,v.y=w.y,v.z=w.z,v.w=-a.Cartesian3.dot(w,e),a.Cartesian3.multiplyByScalar(n,f,w),a.Cartesian3.add(m,w,w),a.Cartesian3.subtract(w,e,w),a.Cartesian3.cross(w,c,w),a.Cartesian3.normalize(w,w),v=s[3],t.defined(v)||(v=s[3]=new o.Cartesian4),v.x=w.x,v.y=w.y,v.z=w.z,v.w=-a.Cartesian3.dot(w,e),v=s[4],t.defined(v)||(v=s[4]=new o.Cartesian4),v.x=i.x,v.y=i.y,v.z=i.z,v.w=-a.Cartesian3.dot(i,m),a.Cartesian3.negate(i,w),v=s[5],t.defined(v)||(v=s[5]=new o.Cartesian4),v.x=w.x,v.y=w.y,v.z=w.z,v.w=-a.Cartesian3.dot(w,C),this._cullingVolume},D.prototype.getPixelDimensions=function(e,a,i,n,o){if(F(this),!t.defined(e)||!t.defined(a))throw new r.DeveloperError("Both drawingBufferWidth and drawingBufferHeight are required.");if(e<=0)throw new r.DeveloperError("drawingBufferWidth must be greater than zero.");if(a<=0)throw new r.DeveloperError("drawingBufferHeight must be greater than zero.");if(!t.defined(i))throw new r.DeveloperError("distance is required.");if(!t.defined(n))throw new r.DeveloperError("pixelRatio is required");if(n<=0)throw new r.DeveloperError("pixelRatio must be greater than zero.");if(!t.defined(o))throw new r.DeveloperError("A result object is required.");var s=1/this.near,f=this.top*s,u=2*n*i*f/a;f=this.right*s;var d=2*n*i*f/e;return o.x=d,o.y=u,o},D.prototype.clone=function(e){return t.defined(e)||(e=new D),e.right=this.right,e.left=this.left,e.top=this.top,e.bottom=this.bottom,e.near=this.near,e.far=this.far,e._left=void 0,e._right=void 0,e._top=void 0,e._bottom=void 0,e._near=void 0,e._far=void 0,e},D.prototype.equals=function(e){return t.defined(e)&&e instanceof D&&this.right===e.right&&this.left===e.left&&this.top===e.top&&this.bottom===e.bottom&&this.near===e.near&&this.far===e.far},D.prototype.equalsEpsilon=function(e,r,i){return e===this||t.defined(e)&&e instanceof D&&a.CesiumMath.equalsEpsilon(this.right,e.right,r,i)&&a.CesiumMath.equalsEpsilon(this.left,e.left,r,i)&&a.CesiumMath.equalsEpsilon(this.top,e.top,r,i)&&a.CesiumMath.equalsEpsilon(this.bottom,e.bottom,r,i)&&a.CesiumMath.equalsEpsilon(this.near,e.near,r,i)&&a.CesiumMath.equalsEpsilon(this.far,e.far,r,i)},k.packedLength=6,k.pack=function(e,a,i){return r.Check.typeOf.object("value",e),r.Check.defined("array",a),i=t.defaultValue(i,0),a[i++]=e.fov,a[i++]=e.aspectRatio,a[i++]=e.near,a[i++]=e.far,a[i++]=e.xOffset,a[i]=e.yOffset,a},k.unpack=function(e,a,i){return r.Check.defined("array",e),a=t.defaultValue(a,0),t.defined(i)||(i=new k),i.fov=e[a++],i.aspectRatio=e[a++],i.near=e[a++],i.far=e[a++],i.xOffset=e[a++],i.yOffset=e[a],i},Object.defineProperties(k.prototype,{projectionMatrix:{get:function(){return P(this),this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return P(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return P(this),this._fovy}},sseDenominator:{get:function(){return P(this),this._sseDenominator}}}),k.prototype.computeCullingVolume=function(e,t,r){return P(this),this._offCenterFrustum.computeCullingVolume(e,t,r)},k.prototype.getPixelDimensions=function(e,t,r,a,i){return P(this),this._offCenterFrustum.getPixelDimensions(e,t,r,a,i)},k.prototype.clone=function(e){return t.defined(e)||(e=new k),e.aspectRatio=this.aspectRatio,e.fov=this.fov,e.near=this.near,e.far=this.far,e._aspectRatio=void 0,e._fov=void 0,e._near=void 0,e._far=void 0,this._offCenterFrustum.clone(e._offCenterFrustum),P(e),e},k.prototype.equals=function(e){return!!(t.defined(e)&&e instanceof k)&&(P(this),P(e),this.fov===e.fov&&this.aspectRatio===e.aspectRatio&&this._offCenterFrustum.equals(e._offCenterFrustum))},k.prototype.equalsEpsilon=function(e,r,i){return!!(t.defined(e)&&e instanceof k)&&(P(this),P(e),a.CesiumMath.equalsEpsilon(this.fov,e.fov,r,i)&&a.CesiumMath.equalsEpsilon(this.aspectRatio,e.aspectRatio,r,i)&&this._offCenterFrustum.equalsEpsilon(e._offCenterFrustum,r,i))};var q=0,S=1;function T(e){r.Check.typeOf.object("options",e),r.Check.typeOf.object("options.frustum",e.frustum),r.Check.typeOf.object("options.origin",e.origin),r.Check.typeOf.object("options.orientation",e.orientation);var i,o,s=e.frustum,f=e.orientation,u=e.origin,d=t.defaultValue(e.vertexFormat,p.VertexFormat.DEFAULT),h=t.defaultValue(e._drawNearPlane,!0);s instanceof k?(i=q,o=k.packedLength):s instanceof E&&(i=S,o=E.packedLength),this._frustumType=i,this._frustum=s.clone(),this._origin=a.Cartesian3.clone(u),this._orientation=n.Quaternion.clone(f),this._drawNearPlane=h,this._vertexFormat=d,this._workerName="createFrustumGeometry",this.packedLength=2+o+a.Cartesian3.packedLength+n.Quaternion.packedLength+p.VertexFormat.packedLength}T.pack=function(e,i,o){r.Check.typeOf.object("value",e),r.Check.defined("array",i),o=t.defaultValue(o,0);var s=e._frustumType,f=e._frustum;return i[o++]=s,s===q?(k.pack(f,i,o),o+=k.packedLength):(E.pack(f,i,o),o+=E.packedLength),a.Cartesian3.pack(e._origin,i,o),o+=a.Cartesian3.packedLength,n.Quaternion.pack(e._orientation,i,o),o+=n.Quaternion.packedLength,p.VertexFormat.pack(e._vertexFormat,i,o),o+=p.VertexFormat.packedLength,i[o]=e._drawNearPlane?1:0,i};var A=new k,B=new E,I=new n.Quaternion,L=new a.Cartesian3,N=new p.VertexFormat;function j(e,r,a,i,n,o,s,f){for(var u=e/3*2,d=0;d<4;++d)t.defined(r)&&(r[e]=o.x,r[e+1]=o.y,r[e+2]=o.z),t.defined(a)&&(a[e]=s.x,a[e+1]=s.y,a[e+2]=s.z),t.defined(i)&&(i[e]=f.x,i[e+1]=f.y,i[e+2]=f.z),e+=3;n[u]=0,n[u+1]=0,n[u+2]=1,n[u+3]=0,n[u+4]=1,n[u+5]=1,n[u+6]=0,n[u+7]=1}T.unpack=function(e,i,o){r.Check.defined("array",e),i=t.defaultValue(i,0);var s,f=e[i++];f===q?(s=k.unpack(e,i,A),i+=k.packedLength):(s=E.unpack(e,i,B),i+=E.packedLength);var u=a.Cartesian3.unpack(e,i,L);i+=a.Cartesian3.packedLength;var d=n.Quaternion.unpack(e,i,I);i+=n.Quaternion.packedLength;var h=p.VertexFormat.unpack(e,i,N);i+=p.VertexFormat.packedLength;var l=1===e[i];if(!t.defined(o))return new T({frustum:s,origin:u,orientation:d,vertexFormat:h,_drawNearPlane:l});var c=f===o._frustumType?o._frustum:void 0;return o._frustum=s.clone(c),o._frustumType=f,o._origin=a.Cartesian3.clone(u,o._origin),o._orientation=n.Quaternion.clone(d,o._orientation),o._vertexFormat=p.VertexFormat.clone(h,o._vertexFormat),o._drawNearPlane=l,o};var G=new o.Matrix3,U=new o.Matrix4,Q=new o.Matrix4,K=new a.Cartesian3,W=new a.Cartesian3,Y=new a.Cartesian3,H=new a.Cartesian3,J=new a.Cartesian3,X=new a.Cartesian3,Z=new Array(3),$=new Array(4);$[0]=new o.Cartesian4(-1,-1,1,1),$[1]=new o.Cartesian4(1,-1,1,1),$[2]=new o.Cartesian4(1,1,1,1),$[3]=new o.Cartesian4(-1,1,1,1);for(var ee=new Array(4),te=0;te<4;++te)ee[te]=new o.Cartesian4;T._computeNearFarPlanes=function(e,r,i,n,s,f,u,d){var h=o.Matrix3.fromQuaternion(r,G),p=t.defaultValue(f,K),l=t.defaultValue(u,W),c=t.defaultValue(d,Y);p=o.Matrix3.getColumn(h,0,p),l=o.Matrix3.getColumn(h,1,l),c=o.Matrix3.getColumn(h,2,c),a.Cartesian3.normalize(p,p),a.Cartesian3.normalize(l,l),a.Cartesian3.normalize(c,c),a.Cartesian3.negate(p,p);var m,C,w=o.Matrix4.computeView(e,c,l,p,U);if(i===q){var v=n.projectionMatrix,_=o.Matrix4.multiply(v,w,Q);C=o.Matrix4.inverse(_,Q)}else m=o.Matrix4.inverseTransformation(w,Q);t.defined(C)?(Z[0]=n.near,Z[1]=n.far):(Z[0]=0,Z[1]=n.near,Z[2]=n.far);for(var y=0;y<2;++y)for(var g=0;g<4;++g){var x=o.Cartesian4.clone($[g],ee[g]);if(t.defined(C)){x=o.Matrix4.multiplyByVector(C,x,x);var b=1/x.w;a.Cartesian3.multiplyByScalar(x,b,x),a.Cartesian3.subtract(x,e,x),a.Cartesian3.normalize(x,x);var E=a.Cartesian3.dot(c,x);a.Cartesian3.multiplyByScalar(x,Z[y]/E,x),a.Cartesian3.add(x,e,x)}else{t.defined(n._offCenterFrustum)&&(n=n._offCenterFrustum);var M=Z[y],D=Z[y+1];x.x=.5*(x.x*(n.right-n.left)+n.left+n.right),x.y=.5*(x.y*(n.top-n.bottom)+n.bottom+n.top),x.z=.5*(x.z*(M-D)-M-D),x.w=1,o.Matrix4.multiplyByVector(m,x,x)}s[12*y+3*g]=x.x,s[12*y+3*g+1]=x.y,s[12*y+3*g+2]=x.z}},T.createGeometry=function(e){var r=e._frustumType,n=e._frustum,o=e._origin,h=e._orientation,p=e._drawNearPlane,l=e._vertexFormat,c=p?6:5,m=new Float64Array(72);T._computeNearFarPlanes(o,h,r,n,m);var C=24;m[C]=m[12],m[C+1]=m[13],m[C+2]=m[14],m[C+3]=m[0],m[C+4]=m[1],m[C+5]=m[2],m[C+6]=m[9],m[C+7]=m[10],m[C+8]=m[11],m[C+9]=m[21],m[C+10]=m[22],m[C+11]=m[23],C+=12,m[C]=m[15],m[C+1]=m[16],m[C+2]=m[17],m[C+3]=m[3],m[C+4]=m[4],m[C+5]=m[5],m[C+6]=m[0],m[C+7]=m[1],m[C+8]=m[2],m[C+9]=m[12],m[C+10]=m[13],m[C+11]=m[14],C+=12,m[C]=m[3],m[C+1]=m[4],m[C+2]=m[5],m[C+3]=m[15],m[C+4]=m[16],m[C+5]=m[17],m[C+6]=m[18],m[C+7]=m[19],m[C+8]=m[20],m[C+9]=m[6],m[C+10]=m[7],m[C+11]=m[8],C+=12,m[C]=m[6],m[C+1]=m[7],m[C+2]=m[8],m[C+3]=m[18],m[C+4]=m[19],m[C+5]=m[20],m[C+6]=m[21],m[C+7]=m[22],m[C+8]=m[23],m[C+9]=m[9],m[C+10]=m[10],m[C+11]=m[11],p||(m=m.subarray(12));var w=new d.GeometryAttributes({position:new f.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m})});if(t.defined(l.normal)||t.defined(l.tangent)||t.defined(l.bitangent)||t.defined(l.st)){var v=t.defined(l.normal)?new Float32Array(12*c):void 0,_=t.defined(l.tangent)?new Float32Array(12*c):void 0,y=t.defined(l.bitangent)?new Float32Array(12*c):void 0,g=t.defined(l.st)?new Float32Array(8*c):void 0,x=K,b=W,E=Y,M=a.Cartesian3.negate(x,H),D=a.Cartesian3.negate(b,J),F=a.Cartesian3.negate(E,X);C=0,p&&(j(C,v,_,y,g,F,x,b),C+=12),j(C,v,_,y,g,E,M,b),C+=12,j(C,v,_,y,g,M,F,b),C+=12,j(C,v,_,y,g,D,F,M),C+=12,j(C,v,_,y,g,x,E,b),C+=12,j(C,v,_,y,g,b,E,M),t.defined(v)&&(w.normal=new f.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),t.defined(_)&&(w.tangent=new f.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:_})),t.defined(y)&&(w.bitangent=new f.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),t.defined(g)&&(w.st=new f.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:g}))}for(var V=new Uint16Array(6*c),R=0;R<c;++R){var O=6*R,z=4*R;V[O]=z,V[O+1]=z+1,V[O+2]=z+2,V[O+3]=z,V[O+4]=z+2,V[O+5]=z+3}return new f.Geometry({attributes:w,indices:V,primitiveType:u.PrimitiveType.TRIANGLES,boundingSphere:i.BoundingSphere.fromVertices(m)})},e.FrustumGeometry=T,e.OrthographicFrustum=E,e.PerspectiveFrustum=k}));