import router from './router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@Common/utils/auth'

document.title = import.meta.env.VITE_APP_TITLE // Update the browser window title

NProgress.configure({
  showSpinner: false
})

const whiteList = ['/login'] // no redirect whitelist

// router.beforeEach((to, from, next) => {
//   NProgress.start()
//   const token = getToken()
//   if (token) {
//     // 登录成功，跳转到首页
//     if (to.path === '/login') {
//       next({ path: '/' })
//     } else {
//       next()
//     }
//     NProgress.done()
//   } else {
//     if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
//       next()
//     } else {
//       next({ path: '/login' })
//     }
//     NProgress.done()
//   }
// })

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
