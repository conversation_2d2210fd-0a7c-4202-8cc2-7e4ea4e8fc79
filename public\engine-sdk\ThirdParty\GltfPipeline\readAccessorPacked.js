import getAccessorByteStride from"./getAccessorByteStride.js";import getComponentReader from"./getComponentReader.js";import numberOfComponentsForType from"./numberOfComponentsForType.js";import arrayFill from"../../Core/arrayFill.js";import ComponentDatatype from"../../Core/ComponentDatatype.js";import defined from"../../Core/defined.js";function readAccessorPacked(e,r){var t=getAccessorByteStride(e,r),o=ComponentDatatype.getSizeInBytes(r.componentType),n=numberOfComponentsForType(r.type),f=r.count,p=new Array(n*f);if(!defined(r.bufferView))return arrayFill(p,0),p;for(var a=e.bufferViews[r.bufferView],s=e.buffers[a.buffer].extras._pipeline.source,m=r.byteOffset+a.byteOffset+s.byteOffset,i=new DataView(s.buffer),y=new Array(n),c=getComponentReader(r.componentType),d=0;d<f;++d){c(i,m,n,o,y);for(var u=0;u<n;++u)p[d*n+u]=y[u];m+=t}return p}export default readAccessorPacked;