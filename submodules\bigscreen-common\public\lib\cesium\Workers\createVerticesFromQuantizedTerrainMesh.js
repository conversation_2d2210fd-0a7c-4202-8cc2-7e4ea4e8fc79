/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AxisAlignedBoundingBox-6489d16d","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./TerrainEncoding-389ca311","./IndexDatatype-00859b8b","./Math-b5f4d889","./Resource-41d99fe7","./Transforms-42ed7720","./WebMercatorProjection-ce967e48","./createTaskProcessorWorker","./RuntimeError-592f0d41","./AttributeCompression-d661357e","./ComponentDatatype-dad47320","./WebGLConstants-433debbf","./combine-0bec9016"],(function(e,t,r,n,o,i,a,s,c,d,h,u,l,I,g,m){"use strict";function T(){s.DeveloperError.throwInstantiationError()}Object.defineProperties(T.prototype,{errorEvent:{get:s.DeveloperError.throwInstantiationError},credit:{get:s.DeveloperError.throwInstantiationError},tilingScheme:{get:s.DeveloperError.throwInstantiationError},ready:{get:s.DeveloperError.throwInstantiationError},readyPromise:{get:s.DeveloperError.throwInstantiationError},hasWaterMask:{get:s.DeveloperError.throwInstantiationError},hasVertexNormals:{get:s.DeveloperError.throwInstantiationError},availability:{get:s.DeveloperError.throwInstantiationError}});const f=[];T.getRegularGridIndices=function(e,t){let r=f[e];n.defined(r)||(f[e]=r=[]);let o=r[t];return n.defined(o)||(o=e*t<a.CesiumMath.SIXTY_FOUR_KILOBYTES?r[t]=new Uint16Array((e-1)*(t-1)*6):r[t]=new Uint32Array((e-1)*(t-1)*6),N(e,t,o,0)),o};const E=[];T.getRegularGridIndicesAndEdgeIndices=function(e,t){let r=E[e];n.defined(r)||(E[e]=r=[]);let o=r[t];if(!n.defined(o)){const n=T.getRegularGridIndices(e,t),i=y(e,t),a=i.westIndicesSouthToNorth,s=i.southIndicesEastToWest,c=i.eastIndicesNorthToSouth,d=i.northIndicesWestToEast;o=r[t]={indices:n,westIndicesSouthToNorth:a,southIndicesEastToWest:s,eastIndicesNorthToSouth:c,northIndicesWestToEast:d}}return o};const p=[];function y(e,t){const r=new Array(t),n=new Array(e),o=new Array(t),i=new Array(e);let a;for(a=0;a<e;++a)i[a]=a,n[a]=e*t-1-a;for(a=0;a<t;++a)o[a]=(a+1)*e-1,r[a]=(t-a-1)*e;return{westIndicesSouthToNorth:r,southIndicesEastToWest:n,eastIndicesNorthToSouth:o,northIndicesWestToEast:i}}function N(e,t,r,n){let o=0;for(let i=0;i<t-1;++i){for(let t=0;t<e-1;++t){const t=o,i=t+e,a=i+1,s=t+1;r[n++]=t,r[n++]=i,r[n++]=s,r[n++]=s,r[n++]=i,r[n++]=a,++o}++o}}function w(e,t,r,n){let o=e[0];const i=e.length;for(let a=1;a<i;++a){const i=e[a];r[n++]=o,r[n++]=i,r[n++]=t,r[n++]=t,r[n++]=i,r[n++]=t+1,o=i,++t}return n}T.getRegularGridAndSkirtIndicesAndEdgeIndices=function(e,t){let r=p[e];n.defined(r)||(p[e]=r=[]);let o=r[t];if(!n.defined(o)){const n=e*t,a=(e-1)*(t-1)*6,s=2*e+2*t,c=n+s,d=a+6*Math.max(0,s-4),h=y(e,t),u=h.westIndicesSouthToNorth,l=h.southIndicesEastToWest,I=h.eastIndicesNorthToSouth,g=h.northIndicesWestToEast,m=i.IndexDatatype.createTypedArray(c,d);N(e,t,m,0),T.addSkirtIndices(u,l,I,g,n,m,a),o=r[t]={indices:m,westIndicesSouthToNorth:u,southIndicesEastToWest:l,eastIndicesNorthToSouth:I,northIndicesWestToEast:g,indexCountWithoutSkirts:a}}return o},T.addSkirtIndices=function(e,t,r,n,o,i,a){let s=o;a=w(e,s,i,a),s+=e.length,a=w(t,s,i,a),s+=t.length,a=w(r,s,i,a),s+=r.length,w(n,s,i,a)},T.heightmapTerrainQuality=.25,T.getEstimatedLevelZeroGeometricErrorForAHeightmap=function(e,t,r){return 2*e.maximumRadius*Math.PI*T.heightmapTerrainQuality/(t*r)},T.prototype.requestTileGeometry=s.DeveloperError.throwInstantiationError,T.prototype.getLevelMaximumGeometricError=s.DeveloperError.throwInstantiationError,T.prototype.getTileDataAvailable=s.DeveloperError.throwInstantiationError,T.prototype.loadTileDataAvailability=s.DeveloperError.throwInstantiationError;const b=32767,S=new t.Cartesian3,M=new t.Cartesian3,x=new t.Cartesian3,A=new r.Cartographic,C=new t.Cartesian2;function W(e,n,o,i,s,c,d,h,u){let l=Number.POSITIVE_INFINITY;const I=s.north,g=s.south;let m=s.east;const T=s.west;m<T&&(m+=a.CesiumMath.TWO_PI);const f=e.length;for(let s=0;s<f;++s){const f=e[s],E=o[f],p=i[f];A.longitude=a.CesiumMath.lerp(T,m,p.x),A.latitude=a.CesiumMath.lerp(g,I,p.y),A.height=E-n;const y=c.cartographicToCartesian(A,S);r.Matrix4.multiplyByPoint(d,y,y),t.Cartesian3.minimumByComponent(y,h,h),t.Cartesian3.maximumByComponent(y,u,u),l=Math.min(l,A.height)}return l}function v(e,t,r,o,i,s,c,h,u,l,I,g,m,T){const f=n.defined(c),E=u.north,p=u.south;let y=u.east;const N=u.west;y<N&&(y+=a.CesiumMath.TWO_PI);const w=r.length;for(let n=0;n<w;++n){const u=r[n],w=i[u],b=s[u];A.longitude=a.CesiumMath.lerp(N,y,b.x)+m,A.latitude=a.CesiumMath.lerp(p,E,b.y)+T,A.height=w-l;const M=h.cartographicToCartesian(A,S);if(f){const e=2*u;C.x=c[e],C.y=c[e+1]}let x,W;o.hasWebMercatorT&&(x=(d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(A.latitude)-I)*g),o.hasGeodeticSurfaceNormals&&(W=h.geodeticSurfaceNormal(M)),t=o.encode(e,t,M,b,A.height,C,x,W)}}function P(e,t){let r;return"function"==typeof e.slice&&(r=e.slice(),"function"!=typeof r.sort&&(r=void 0)),n.defined(r)||(r=Array.prototype.slice.call(e)),r.sort(t),r}return h((function(s,h){const u=s.quantizedVertices,l=u.length/3,I=s.octEncodedNormals,g=s.westIndices.length+s.eastIndices.length+s.southIndices.length+s.northIndices.length,m=s.includeWebMercatorT,f=s.exaggeration,E=s.exaggerationRelativeHeight,p=1!==f,y=r.Rectangle.clone(s.rectangle),N=y.west,w=y.south,D=y.east,k=y.north,F=r.Ellipsoid.clone(s.ellipsoid),H=s.minimumHeight,_=s.maximumHeight,G=s.relativeToCenter,V=c.Transforms.eastNorthUpToFixedFrame(G,F),Y=r.Matrix4.inverseTransformation(V,new r.Matrix4);let O,B;m&&(O=d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(w),B=1/(d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(k)-O));const R=u.subarray(0,l),L=u.subarray(l,2*l),j=u.subarray(2*l,3*l),U=n.defined(I),z=new Array(l),q=new Array(l),Q=new Array(l),K=m?new Array(l):[],X=p?new Array(l):[],Z=M;Z.x=Number.POSITIVE_INFINITY,Z.y=Number.POSITIVE_INFINITY,Z.z=Number.POSITIVE_INFINITY;const J=x;J.x=Number.NEGATIVE_INFINITY,J.y=Number.NEGATIVE_INFINITY,J.z=Number.NEGATIVE_INFINITY;let $=Number.POSITIVE_INFINITY,ee=Number.NEGATIVE_INFINITY,te=Number.POSITIVE_INFINITY,re=Number.NEGATIVE_INFINITY;for(let e=0;e<l;++e){const n=R[e],o=L[e],i=n/b,s=o/b,c=a.CesiumMath.lerp(H,_,j[e]/b);A.longitude=a.CesiumMath.lerp(N,D,i),A.latitude=a.CesiumMath.lerp(w,k,s),A.height=c,$=Math.min(A.longitude,$),ee=Math.max(A.longitude,ee),te=Math.min(A.latitude,te),re=Math.max(A.latitude,re);const h=F.cartographicToCartesian(A);z[e]=new t.Cartesian2(i,s),q[e]=c,Q[e]=h,m&&(K[e]=(d.WebMercatorProjection.geodeticLatitudeToMercatorAngle(A.latitude)-O)*B),p&&(X[e]=F.geodeticSurfaceNormal(h)),r.Matrix4.multiplyByPoint(Y,h,S),t.Cartesian3.minimumByComponent(S,Z,Z),t.Cartesian3.maximumByComponent(S,J,J)}const ne=P(s.westIndices,(function(e,t){return z[e].y-z[t].y})),oe=P(s.eastIndices,(function(e,t){return z[t].y-z[e].y})),ie=P(s.southIndices,(function(e,t){return z[t].x-z[e].x})),ae=P(s.northIndices,(function(e,t){return z[e].x-z[t].x}));let se;if(H<0){se=new o.EllipsoidalOccluder(F).computeHorizonCullingPointPossiblyUnderEllipsoid(G,Q,H)}let ce=H;ce=Math.min(ce,W(s.westIndices,s.westSkirtHeight,q,z,y,F,Y,Z,J)),ce=Math.min(ce,W(s.southIndices,s.southSkirtHeight,q,z,y,F,Y,Z,J)),ce=Math.min(ce,W(s.eastIndices,s.eastSkirtHeight,q,z,y,F,Y,Z,J)),ce=Math.min(ce,W(s.northIndices,s.northSkirtHeight,q,z,y,F,Y,Z,J));const de=new e.AxisAlignedBoundingBox(Z,J,G),he=new o.TerrainEncoding(G,de,ce,_,V,U,m,p,f,E),ue=he.stride,le=new Float32Array(l*ue+g*ue);let Ie=0;for(let e=0;e<l;++e){if(U){const t=2*e;C.x=I[t],C.y=I[t+1]}Ie=he.encode(le,Ie,Q[e],z[e],q[e],C,K[e],X[e])}const ge=Math.max(0,2*(g-4)),me=s.indices.length+3*ge,Te=i.IndexDatatype.createTypedArray(l+g,me);Te.set(s.indices,0);const fe=1e-4,Ee=(ee-$)*fe,pe=(re-te)*fe,ye=-Ee,Ne=Ee,we=pe,be=-pe;let Se=l*ue;return v(le,Se,ne,he,q,z,I,F,y,s.westSkirtHeight,O,B,ye,0),Se+=s.westIndices.length*ue,v(le,Se,ie,he,q,z,I,F,y,s.southSkirtHeight,O,B,0,be),Se+=s.southIndices.length*ue,v(le,Se,oe,he,q,z,I,F,y,s.eastSkirtHeight,O,B,Ne,0),Se+=s.eastIndices.length*ue,v(le,Se,ae,he,q,z,I,F,y,s.northSkirtHeight,O,B,0,we),T.addSkirtIndices(ne,ie,oe,ae,l,Te,s.indices.length),h.push(le.buffer,Te.buffer),{vertices:le.buffer,indices:Te.buffer,westIndicesSouthToNorth:ne,southIndicesEastToWest:ie,eastIndicesNorthToSouth:oe,northIndicesWestToEast:ae,vertexStride:ue,center:G,minimumHeight:H,maximumHeight:_,occludeePointInScaledSpace:se,encoding:he,indexCountWithoutSkirts:s.indices.length}}))}));
