/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./ArcType-e42cfb05","./arrayRemoveDuplicates-5b666c82","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Color-bcdd0092","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolylinePipeline-1a06b90f","./VertexFormat-86c096b8","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,o,n,r,a,i,l,s,c,p,d,u,y,m,f,h,C,b,g,_,A){"use strict";const E=[];function P(e,t,o,n,r){const a=E;let l;a.length=r;const s=o.red,c=o.green,p=o.blue,d=o.alpha,u=n.red,y=n.green,m=n.blue,f=n.alpha;if(i.Color.equals(o,n)){for(l=0;l<r;l++)a[l]=i.Color.clone(o);return a}const h=(u-s)/r,C=(y-c)/r,b=(m-p)/r,g=(f-d)/r;for(l=0;l<r;l++)a[l]=new i.Color(s+l*h,c+l*C,p+l*b,d+l*g);return a}function w(n){const r=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).positions,l=n.colors,s=e.defaultValue(n.width,1),c=e.defaultValue(n.colorsPerVertex,!1);this._positions=r,this._colors=l,this._width=s,this._colorsPerVertex=c,this._vertexFormat=y.VertexFormat.clone(e.defaultValue(n.vertexFormat,y.VertexFormat.DEFAULT)),this._arcType=e.defaultValue(n.arcType,o.ArcType.GEODESIC),this._granularity=e.defaultValue(n.granularity,d.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84)),this._workerName="createPolylineGeometry";let p=1+r.length*a.Cartesian3.packedLength;p+=e.defined(l)?1+l.length*i.Color.packedLength:1,this.packedLength=p+t.Ellipsoid.packedLength+y.VertexFormat.packedLength+4}w.pack=function(o,n,r){let l;r=e.defaultValue(r,0);const s=o._positions;let c=s.length;for(n[r++]=c,l=0;l<c;++l,r+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[l],n,r);const p=o._colors;for(c=e.defined(p)?p.length:0,n[r++]=c,l=0;l<c;++l,r+=i.Color.packedLength)i.Color.pack(p[l],n,r);return t.Ellipsoid.pack(o._ellipsoid,n,r),r+=t.Ellipsoid.packedLength,y.VertexFormat.pack(o._vertexFormat,n,r),r+=y.VertexFormat.packedLength,n[r++]=o._width,n[r++]=o._colorsPerVertex?1:0,n[r++]=o._arcType,n[r]=o._granularity,n};const T=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),x=new y.VertexFormat,D={positions:void 0,colors:void 0,ellipsoid:T,vertexFormat:x,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};w.unpack=function(o,n,r){let l;n=e.defaultValue(n,0);let s=o[n++];const c=new Array(s);for(l=0;l<s;++l,n+=a.Cartesian3.packedLength)c[l]=a.Cartesian3.unpack(o,n);s=o[n++];const p=s>0?new Array(s):void 0;for(l=0;l<s;++l,n+=i.Color.packedLength)p[l]=i.Color.unpack(o,n);const d=t.Ellipsoid.unpack(o,n,T);n+=t.Ellipsoid.packedLength;const u=y.VertexFormat.unpack(o,n,x);n+=y.VertexFormat.packedLength;const m=o[n++],f=1===o[n++],h=o[n++],C=o[n];return e.defined(r)?(r._positions=c,r._colors=p,r._ellipsoid=t.Ellipsoid.clone(d,r._ellipsoid),r._vertexFormat=y.VertexFormat.clone(u,r._vertexFormat),r._width=m,r._colorsPerVertex=f,r._arcType=h,r._granularity=C,r):(D.positions=c,D.colors=p,D.width=m,D.colorsPerVertex=f,D.arcType=h,D.granularity=C,new w(D))};const k=new a.Cartesian3,v=new a.Cartesian3,V=new a.Cartesian3,L=new a.Cartesian3;return w.createGeometry=function(t){const y=t._width,m=t._vertexFormat;let f=t._colors;const h=t._colorsPerVertex,C=t._arcType,b=t._granularity,g=t._ellipsoid;let _,A,w;const T=[];let x=n.arrayRemoveDuplicates(t._positions,a.Cartesian3.equalsEpsilon,!1,T);if(e.defined(f)&&T.length>0){let e=0,t=T[0];f=f.filter((function(o,n){let r=!1;return r=h?n===t||0===n&&1===t:n+1===t,!r||(e++,t=T[e],!1)}))}let D=x.length;if(D<2||y<=0)return;if(C===o.ArcType.GEODESIC||C===o.ArcType.RHUMB){let t,n;C===o.ArcType.GEODESIC?(t=d.CesiumMath.chordLength(b,g.maximumRadius),n=u.PolylinePipeline.numberOfPoints):(t=b,n=u.PolylinePipeline.numberOfPointsRhumbLine);const r=u.PolylinePipeline.extractHeights(x,g);if(e.defined(f)){let e=1;for(_=0;_<D-1;++_)e+=n(x[_],x[_+1],t);const o=new Array(e);let r=0;for(_=0;_<D-1;++_){const a=x[_],l=x[_+1],s=f[_],c=n(a,l,t);if(h&&_<e){const e=P(0,0,s,f[_+1],c),t=e.length;for(A=0;A<t;++A)o[r++]=e[A]}else for(A=0;A<c;++A)o[r++]=i.Color.clone(s)}o[r]=i.Color.clone(f[f.length-1]),f=o,E.length=0}x=C===o.ArcType.GEODESIC?u.PolylinePipeline.generateCartesianArc({positions:x,minDistance:t,ellipsoid:g,height:r}):u.PolylinePipeline.generateCartesianRhumbArc({positions:x,granularity:t,ellipsoid:g,height:r})}D=x.length;const F=4*D-4,G=new Float64Array(3*F),R=new Float64Array(3*F),O=new Float64Array(3*F),I=new Float32Array(2*F),S=m.st?new Float32Array(2*F):void 0,B=e.defined(f)?new Uint8Array(4*F):void 0;let U,N=0,M=0,H=0,W=0;for(A=0;A<D;++A){let t,o;0===A?(U=k,a.Cartesian3.subtract(x[0],x[1],U),a.Cartesian3.add(x[0],U,U)):U=x[A-1],a.Cartesian3.clone(U,V),a.Cartesian3.clone(x[A],v),A===D-1?(U=k,a.Cartesian3.subtract(x[D-1],x[D-2],U),a.Cartesian3.add(x[D-1],U,U)):U=x[A+1],a.Cartesian3.clone(U,L),e.defined(B)&&(t=0===A||h?f[A]:f[A-1],A!==D-1&&(o=f[A]));const n=A===D-1?2:4;for(w=0===A?2:0;w<n;++w){a.Cartesian3.pack(v,G,N),a.Cartesian3.pack(V,R,N),a.Cartesian3.pack(L,O,N),N+=3;const n=w-2<0?-1:1;if(I[M++]=w%2*2-1,I[M++]=n*y,m.st&&(S[H++]=A/(D-1),S[H++]=Math.max(I[M-2],0)),e.defined(B)){const e=w<2?t:o;B[W++]=i.Color.floatToByte(e.red),B[W++]=i.Color.floatToByte(e.green),B[W++]=i.Color.floatToByte(e.blue),B[W++]=i.Color.floatToByte(e.alpha)}}}const Y=new c.GeometryAttributes;Y.position=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G}),Y.prevPosition=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:R}),Y.nextPosition=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:O}),Y.expandAndWidth=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:I}),m.st&&(Y.st=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:S})),e.defined(B)&&(Y.color=new s.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:B,normalize:!0}));const q=p.IndexDatatype.createTypedArray(F,6*D-6);let z=0,J=0;const j=D-1;for(A=0;A<j;++A)q[J++]=z,q[J++]=z+2,q[J++]=z+1,q[J++]=z+1,q[J++]=z+2,q[J++]=z+3,z+=4;return new s.Geometry({attributes:Y,indices:q,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:r.BoundingSphere.fromPoints(x),geometryType:s.GeometryType.POLYLINES})},function(o,n){return e.defined(n)&&(o=w.unpack(o,n)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),w.createGeometry(o)}}));
