<template>
  <div class="ai-buttons-container">
    <img
      class="w91 h77 cursor-pointer"
      v-for="item in list"
      :src="item.selected ? item.iconSelected : item.icon"
      :key="item.id"
      @click="throttledClick(item)" />
  </div>
</template>

<script setup lang="ts">
  import lib from '@/utils/lib'
  import AIChat from '@/components/AIChat/index.vue'
  import { useThrottleFn } from '@vueuse/core'

  import { usePopWindow, type PopWindowProps, closePopWindowByTag } from 'znyg-frontend-common'
  const { showPopWindow } = usePopWindow()
  const { storeScreenData } = lib.store()

  const list = ref([
    {
      id: 1,
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1Selected.png'),
      selected: false
    },
    {
      id: 2,
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2Selected.png'),
      selected: false
    },
    {
      id: 3,
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn3.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn3Selected.png'),
      selected: false
    }
  ])
  const isQuestionType = computed(() => {
    return lib.store().storeScreenData.isQuestionType
  })
  watch(isQuestionType, (newVal) => {
    list.value[1].selected = newVal
  })
  const emits = defineEmits(['btnClick'])
  const handleClick = (item) => {
    console.log(item)
    item.selected = !item.selected
    if (item.id === 1) {
      if (item.selected) {
        openAiChat()
      } else {
        closePopWindowByTag(lib.enumsList.widows.Ai对话框)
      }
    } else if (item.id === 2) {
      storeScreenData.showAIQuestion = item.selected
      lib.store().storeScreenData.isQuestionType = item.selected
    } else if (item.id === 3) {
      emits('btnClick', item.selected)
    }
  }
  const throttledClick = useThrottleFn(handleClick, 500)

  const openAiChat = () => {
    const op: PopWindowProps = {
      top: 150,
      left: 350,
      width: 1600,
      height: 1043,
      tag: lib.enumsList.widows.Ai对话框,
      id: 'ai-chat-container',
      appendParent: 'popUpRoot',
      style: {
        backdropFilter: 'blur(18px)',
        zIndex: 999,
        overflow: 'hidden',
        borderRadius: '26px'
      },
      zIndex:999,
      draggable: true,
      draggableClass: 'drag-container',
      animate: true,
      animateInClass: 'animate__animated animate__faster animate__zoomIn',
      animateOutClass: 'animate__animated animate__faster animate__zoomOut',
      onClose: () => {
        list.value[0].selected = false
      }
    }
    showPopWindow(op, AIChat)
  }

  lib.bus.busAIButtons.on((isShow: boolean) => {
    if (isShow) {
      openAiChat()
    } else {
      closePopWindowByTag(lib.enumsList.widows.Ai对话框)
    }
    list.value[0].selected = isShow
  })

  onUnmounted(() => {
    lib.bus.busAIButtons.reset()
  })
</script>

<style lang="scss" scoped>
  .ai-buttons-container {
    position: absolute;
    top: 20px;
    left: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 91px;
    height: 280px;
  }
</style>
