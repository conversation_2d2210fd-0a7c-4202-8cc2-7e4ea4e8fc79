define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./PrimitiveType-ec02f806","./createTaskProcessorWorker","./CreatePhysicalArray-935f3db5","./materem-d090bcd4","./EmWrapperManager-1ae94128"],(function(e,t,i,r,a,n,o,s,l,d,f){"use strict";var c,m,P,u,v,y,I,M={ADD_PRIMITIVE:0,UPDATE:1,PICK_FROM_RAY:2,UPDATE_INSTANCE_MATRIX:3,UPDATE_PRIMITIVE_MATRIX:4,PICK_FROM_BOX:5},x=Object.freeze(M),p={X:0,Y:1,Z:2,Y_UP_TO_Z_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationX(i.CesiumMath.PI_OVER_TWO)),Z_UP_TO_Y_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationX(-i.CesiumMath.PI_OVER_TWO)),X_UP_TO_Z_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationY(-i.CesiumMath.PI_OVER_TWO)),Z_UP_TO_X_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationY(i.CesiumMath.PI_OVER_TWO)),X_UP_TO_Y_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationZ(i.CesiumMath.PI_OVER_TWO)),Y_UP_TO_X_UP:r.Matrix4.fromRotationTranslation(r.Matrix3.fromRotationZ(-i.CesiumMath.PI_OVER_TWO)),fromName:function(e){return t.Check.typeOf.string("name",e),p[e]}},_=Object.freeze(p),A=r.Matrix4.toArray(_.Y_UP_TO_Z_UP),g=new r.Matrix4,R=new r.Matrix4,h=new r.Matrix4;function T(e){switch(e.logicType){case x.UPDATE_INSTANCE_MATRIX:U(e);break;case x.ADD_PRIMITIVE:C(e);break;case x.UPDATE_PRIMITIVE_MATRIX:b(e);break;case x.UPDATE:m.EnableAllPrimitiveSelected(!1),S(e.selectedModelIdArray),E(e.selectedModelIdIndexArray),w(e.removedModelIdArray);break;case x.PICK_FROM_RAY:return X(e);case x.PICK_FROM_BOX:return W(e);default:throw new t.DeveloperError("physicalLogicType is not a valid value.")}return!0}function G(){let e=[];const t=m.GetNumOfRemoveItems();if("number"===typeof t&&t>0){const i=Array.from({length:t},((e,t)=>t));i.map((t=>{const i=m.GetRemoveItemAt(t),r={iBatchId:i.iBatchId,iPrimitiveId:i.iPrimitiveId,iIndex:i.iIndex,bCluster:i.bCluster};i&&void 0!==i.iPrimitiveId&&(r.modelId=u.get(i.iPrimitiveId),r.bimId=i.iBimId),e.push(r)}))}return e}function S(t){if(e.defined(t)&&t.length>0)for(var i=0;i<t.length;++i){var r=t[i];if(P.has(r)){var a=P.get(r);for(var n of a)m.EnablePrimitiveSelected(n,!0)}}}function E(t){if(e.defined(t)&&t.length>0)for(var i=0;i<t.length;++i){var r=t[i].modelId,a=t[i].unSelectedIndexArray;if(P.has(r)){var n=P.get(r);for(var o of n){m.EnablePrimitiveSelected(o,!0);var s=m.GetPrimitiveCluster(o);if(0!==s.ptr){s.EnableAllIndexSelected(!0);for(let e=0;e<a.length;e++)s.EnableIndexSelected(a[e],!1)}}}}}function C(t){var i=t.primitives,r=t.modelId;if(i.length>0&&e.defined(r))for(var a of i)O(r,a)}function b(e){var t=e.primitiveMatrixArray;if(t.length>0)for(var i of t){var r=i.modelId,a=i.primitiveMatrixTypeArray;if(P.has(r))for(var n=P.get(r),o=0;o<n.length;++o){var s=n[o];z(s,a)}}}function O(t,i){var r,a,n=i.physicalArray,o=i.physicalArrayOptions,s=i.projectCenterMatrixArray;if(e.defined(n)){var d=new c.LBSpaSerial,f=c._malloc(n.byteLength);c.HEAPU8.set(n,f),a=d.ReadSpatial(f,n.byteLength),c._free(f),c.destroy(d)}if(e.defined(o)){var P=o.pPtAry,u=o.pBatchIdAry,v=o.pIndexAry,y=o.pEdgeCheckAry,I=o.primitiveMode;a=l.CreatePhysicalArray.createSpaPrimitive(c,m,I,P,u,v,y)}if(e.defined(i.instanceMatrixTypeArray)||e.defined(i.lodInstanceMatrixTypeArray)){var M=m.CreatePrimitiveCluster(a);r=m.AddPrimitiveSpatial(M);var x=e.defined(i.lodInstanceMatrixTypeArray),p=x?A:void 0,_=x?i.lodInstanceMatrixTypeArray:i.instanceMatrixTypeArray;k(M,s,p,_,x)}else r=m.AddPrimitiveSpatial(a),z(r,s);return D(t,r),r}function z(t,i){e.defined(i)&&(B(i,v),m.SetPrimitiveSpatialMat(t,v))}function k(t,i,a,n,o){e.defined(i)&&r.Matrix4.fromArray(i,0,g),e.defined(a)&&r.Matrix4.fromArray(a,0,R);for(var s=0;s<n.length;s+=16)r.Matrix4.fromArray(n,s,h),e.defined(a)&&r.Matrix4.multiply(h,R,h),!o&&e.defined(i)&&r.Matrix4.multiply(g,h,h),B(h,v),t.SetIndexMatrix(s/16,v)}function U(e){var t=e.instanceMatrixArray;for(var i of t){var r=i.modelId,a=i.instanceMatrixTypeArray;if(P.has(r))for(var n=P.get(r),o=0;o<n.length;++o){var s=n[o],l=m.GetPrimitiveCluster(s);0!==l.ptr&&(l.RemoveAllMatrix(),k(l,void 0,A,a,!0))}}}function B(e,t){for(var i=0;i<4;++i){var r=t.At(i);r.x=e[4*i],r.y=e[4*i+1],r.z=e[4*i+2],r.w=e[4*i+3]}}function D(e,t){var i;P.has(e)?(i=P.get(e),i.push(t)):(i=[],i.push(t),P.set(e,i)),u.set(t,e)}function w(t){if(e.defined(t)&&t.length>0)for(var i=0;i<t.length;++i){var r=t[i];if(P.has(r)){var a=P.get(r);for(var n of a)m.RemovePrimitiveSpatial(n),u.delete(n);P.delete(r)}}}function X(t){var i={},r=t.ray,a=t.triangleMode,n=t.lineMode,o=e.defined(t.maxDist)?t.maxDist:.1;if(y.SetRay(r[0],r[1],r[2],r[3],r[4],r[5],o,a,n),m.Select(y,I)){if(a){i.triPrimitives=[];for(let e=0;e<I.GetTriResultElemSize();++e){const t={};L(I.GetTriResultElem(e),t),i.triPrimitives.push(t)}}if(n){i.segPrimitives=[];for(let e=0;e<I.GetSegResultElemSize();++e){const t={};N(I.GetSegResultElem(e),t),i.segPrimitives.push(t)}}}return I.ClearAll(),i}function W(e){var t={},i=(e.pointAry,e.boxAry),r=e.triangleMode,a=e.lineMode,n=e.isLeftToRightDrag;i.map((e=>{y.AddBuffer(e.x,e.y,e.z)})),y.SetWedgeByBufferedPoints(r,a,n);const o=m.Select(y,I);if(o){if(r){t.triPrimitives=[];for(let e=0;e<I.GetTriResultElemSize();++e){const i={};L(I.GetTriResultElem(e),i),t.triPrimitives.push(i)}}if(a){t.segPrimitives=[];for(let e=0;e<I.GetSegResultElemSize();++e){const i={};N(I.GetSegResultElem(e),i),t.segPrimitives.push(i)}}const e=G();t.removePrimitives=e}return I.ClearAll(),y.ClearBuffer(),t}function L(e,t){t.modelId=u.get(e.GetResultId().iPrimitiveId),t.isCluster=e.GetResultId().bCluster,t.batchId=e.GetResultId().iBatchId,t.vertices=[{x:e.GetPt0().x,y:e.GetPt0().y,z:e.GetPt0().z},{x:e.GetPt1().x,y:e.GetPt1().y,z:e.GetPt1().z},{x:e.GetPt2().x,y:e.GetPt2().y,z:e.GetPt2().z}],t.pickPt={x:e.GetPickPt().x,y:e.GetPickPt().y,z:e.GetPickPt().z},t.pickNormal={x:e.GetPickNormal().x,y:e.GetPickNormal().y,z:e.GetPickNormal().z},t.pickDist=e.GetPickDist()}function N(e,t){t.modelId=u.get(e.GetResultId().iPrimitiveId),t.isCluster=e.GetResultId().bCluster,t.batchId=e.GetResultId().iBatchId,t.vertices=[{x:e.GetPt0().x,y:e.GetPt0().y,z:e.GetPt0().z},{x:e.GetPt1().x,y:e.GetPt1().y,z:e.GetPt1().z}],t.segPt={x:e.GetSegPt().x,y:e.GetSegPt().y,z:e.GetSegPt().z},t.pickPt={x:e.GetPickPt().x,y:e.GetPickPt().y,z:e.GetPickPt().z},t.pickDist=e.GetPickDist()}function V(){m=new c.LBSpaMgr,P=new Map,u=new Map,v=new c.LBSpaMat,y=new c.LBSpaSelectCondition,I=new c.LBSpaSelectResult}function Y(t){var i=t.data,r=i.webAssemblyConfig;if(e.defined(r)){if(e.defined(r.debug))return require([r.debug],(function(e){c=e(),V(),self.onmessage=s(T),self.postMessage(!0)}));f.EmWrapperManager.initWebAssembly(r.wasmBinaryFileES6).then((function(){c=f.emMod,V(),self.onmessage=s(T),self.postMessage(!0)}))}}return Y}));