/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AttributeCompression-d661357e","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./Math-b5f4d889","./createTaskProcessorWorker","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./WebGLConstants-433debbf","./RuntimeError-592f0d41"],(function(e,t,a,n,r,o,i,s,c){"use strict";const u=32767,p=new a.Cartographic,f=new t.Cartesian3,l=new a.Rectangle,b=new a.Ellipsoid,d={min:void 0,max:void 0};return r((function(r,o){const i=new Uint16Array(r.positions);!function(e){e=new Float64Array(e);let t=0;d.min=e[t++],d.max=e[t++],a.Rectangle.unpack(e,t,l),t+=a.Rectangle.packedLength,a.Ellipsoid.unpack(e,t,b)}(r.packedBuffer);const s=l,c=b,m=d.min,h=d.max,C=i.length/3,g=i.subarray(0,C),w=i.subarray(C,2*C),k=i.subarray(2*C,3*C);e.AttributeCompression.zigZagDeltaDecode(g,w,k);const y=new Float64Array(i.length);for(let e=0;e<C;++e){const r=g[e],o=w[e],i=k[e],l=n.CesiumMath.lerp(s.west,s.east,r/u),b=n.CesiumMath.lerp(s.south,s.north,o/u),d=n.CesiumMath.lerp(m,h,i/u),C=a.Cartographic.fromRadians(l,b,d,p),R=c.cartographicToCartesian(C,f);t.Cartesian3.pack(R,y,3*e)}return o.push(y.buffer),{positions:y.buffer}}))}));
