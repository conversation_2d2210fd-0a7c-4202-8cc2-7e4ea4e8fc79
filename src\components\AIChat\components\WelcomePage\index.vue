<template>
  <div class="welcome-page">
    <div class="page-title">
      <!-- <span class="font-size-34 fw-bold">Hi~有什么可以帮忙？</span> -->
      <span class="font-size-34 fw-bold mb23">Hi~有什么可以帮忙？</span>
      <span class="font-size-26">我是钱江“通泰号”AI智能助手，可以为您答疑解惑，快来体验吧~</span>
      <!-- 功能暂未开放提示-->
      <!-- <div class="no-use-tips-box">
        <div class="no-use-tips">项目目前正在开发中，暂时没有域名，因此如遇首次打开无法使用AI，无法查看</div>
        <div class="no-use-tips">机器人视频等问题，可以尝试打开下面链接并选择高级➡继续前往，尝试解决。</div>
        <div class="tip-item">1、AI问题解决链接：<el-link font-size="20px" type="primary" href="https://************:4936/api/chat-messages" target="_blank">https://************:4936/api/chat-messages</el-link></div>
        <div class="tip-item">2、机器人视频解决链接：<el-link font-size="20px" type="primary" href="https://*************/login" target="_blank">https://*************/login</el-link></div>
      </div> -->
    </div>
    <div class="font-size-26 c-#414141">常用提问</div>
    <!-- 问题列表 -->
    <div class="question-list">
      <div v-if="isShowQuestion" class="question-item" v-for="(item, index) in list" :key="index" @click="handleItem(item)">
        <span class="question-item-text">
          {{ item.content }}
          <el-icon size="22px" color="#8F8F8F"><Right /></el-icon>
        </span>
      </div>
      <div v-else class="no-data">{{ noDataText }}</div>
    </div>
    <div class="more" @click="handleMore" :disabled="loading">
      {{ loadText }}
      <!-- 更多 -->
      <el-icon size="26px" color="#8F8F8F"><CaretBottom /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Right, CaretBottom } from '@element-plus/icons-vue'
  import lib from '@/utils/lib.ts'

  const emit = defineEmits(['question'])

  const loading = ref(false) // 加载中
  const loadText = computed(() => {
    return loading.value ? '加载中...' : '更多'
  })

  const list = reactive([
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '按作业数量查询2025年1月总体日常养护任务完成情况。',
    //   id: 13,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // },
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '按作业内容查询2025年1月日常养护任务完成情况。',
    //   id: 14,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // },
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '按作业内容查询2025年1月日常养护任务未完成情况。',
    //   id: 15,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // },
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '考核内容为路面清扫作业的考核要求，考核频次和考核频次依据等',
    //   id: 16,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // },
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '查询养护作业分为哪几类，哪类包括哪些养护内容，及其对应的考核要求，频次要求，规范要求等。',
    //   id: 17,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // },
    // {
    //   askTimes: 0,
    //   code: '',
    //   content: '对比同期（月度、年度）路面清扫的完成率，完成率是否与天气情况相关？',
    //   id: 18,
    //   recordCreateDate: 1745892962000,
    //   recordUpdateDate: 1745892962000,
    //   state: 1,
    //   treeId: '',
    //   type: '日常养护情况查询'
    // }
  ])
  // 是否展示问题列表
  const isShowQuestion = computed(() => {
    return list.length > 0
  })
  const noDataText = ref('加载中...')
  const params = reactive({
    params: {},
    currPage: 1, // 当前页码
    pageSize: 3 // 每页显示条数 默认是3条
  })

  // 点击更多
  const handleMore = async () => {
    if (loading.value) return
    params.currPage += 1
    getData()
  }

  // 点击问题
  const handleItem = (item) => {
    // 传递问题给父组件
    emit('question', item.content)
  }

  // 获取常用问题列表
  const getData = async () => {
    loading.value = true
    try {
      const res = await lib.api.questionApi.list(params)
      list.push(...res.result?.list)
      await nextTick()
      //   将滚动条滚动到底部
      const el = document.querySelector('.question-list')
      el.scrollTop = el.scrollHeight
    } catch (error) {
      console.log(error)
    } finally {
      noDataText.value = '暂无数据'
      loading.value = false
    }
  }
  onMounted(() => {
    // 初始化加载
    getData()
  })
</script>

<style scoped lang="scss">
  .welcome-page {
    display: flex;
    flex-direction: column;

    /* width: 1353px; */
    width: 100%;
    height: 750px;
    padding: 68px 0 38px 144px;

    /* background: linear-gradient(180deg, #f6faff 0%, #ffffff 100%); */

    /* opacity: 0.68; */
    background: linear-gradient(180deg, rgb(246 250 255 / 68%) 0%, rgb(255 255 255 / 68%) 100%);
    border-radius: 30px;
    .page-title {
      display: flex;
      flex-direction: column;
      width: 1070px;

      // height: 310px;
      height: 218px;
      padding-top: 43px;

      // padding: 10px 22px 30px 71px;
      padding-left: 71px;
      color: #181818;
      background: url('@/assets/CommonPopup/AIChat/welcome-title.png') no-repeat;
      background-size: 100% 100%;
      .no-use-tips-box {
        .no-use-tips {
          font-size: 16px;
          color: #8f8f8f;
        }
        .tip-item {
          display: flex;
          align-items: flex-end;
          font-size: 16px;
          color: #8f8f8f;
        }
      }
    }
    .question-list {
      width: 88%;
      height: 300px;
      overflow-x: hidden;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background: #888888;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb:hover {
        background: #555555;
      }
      .question-item {
        margin-top: 14px;
        .question-item-text {
          display: inline-block;
          padding: 12px 27px;
          font-size: 22px;
          line-height: 22px;
          color: #181818;
          cursor: pointer;
          background: #ffffff;
          border-radius: 12px;
        }
      }
      .no-data {
        /* 垂直居中 */
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 26px;
        font-weight: 400;
        line-height: 100%;
        color: #dbefff;
        text-align: center;
      }
    }
    .more {
      padding-left: 500px;
      margin-top: 10px;
      font-size: 26px;
      color: #929292;
      cursor: pointer;

      /* width: 300px; */
    }
  }
</style>
