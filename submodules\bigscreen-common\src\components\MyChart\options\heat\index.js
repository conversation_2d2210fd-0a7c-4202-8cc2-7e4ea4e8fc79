import * as echarts from 'echarts'

export const heat1 = {
        tooltip: {
          show: false
        },
        grid: {
          height: '130px',
          top: '25%',
          bottom: '0%',
          width: '690px',
          left: '1%'
        },
        xAxis: {
          type: 'category',
          data: ['K1265', 'K1264', 'K1263', 'K1262', 'K1261', 'K1260', 'K1259', 'K1258', 'K1257', 'K1256', 'K1256', 'K1256', 'K1256', 'K1256'],
          offset: 7,
          axisLabel: {
            color: '#C4EDFF',
            fontSize: 12
          }
        },
        yAxis: {
          show: false,
          type: 'category',
          splitArea: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        visualMap: {
          show: true,
          calculable: true,
          min: 0,
          max: 67,
          inRange: {
            color: ['#245F7A', '#4BAA80', '#00AAD2 ', '#BEDF67']
          },
          left: 'left',
          top: '7%',
          hoverLink: false, // 鼠标移入高亮效果
          orient: 'horizontal', // 垂直方向
          itemWidth: 12, // 大小调整
          itemHeight: 88,
          text: ['多', '少'],
          textGap: 5,
          textStyle: {
            color: '#C4EDFF',
            fontSize: 14
          },
          //   图例两头的min和max去掉
          formatter: function (value) {
            return '' // 范围标签显示内容。
          }
        },
        series: [
          {
            name: 'Punch Card',
            type: 'heatmap',
            data: [
              // /第五行数据
              [0, 4, 67],
              [1, 4, 19],
              [2, 4, 28],
              [3, 4, 0],
              [4, 4, 54],
              [5, 4, 20],
              [6, 4, 0],
              [7, 4, 29],
              [8, 4, 52],
              [9, 4, 23],
              [10, 4, 23],
              [11, 4, 23],
              [12, 4, 23],
              [13, 4, 23],
              // ///第四行数据
              [0, 3, 16],
              [1, 3, 0],
              [2, 3, 18],
              [3, 3, 20],
              [4, 3, 0],
              [5, 3, 57],
              [6, 3, 12],
              [7, 3, 0],
              [8, 3, 19],
              [9, 3, 21],
              [10, 3, 21],
              [11, 3, 21],
              [12, 3, 21],
              [13, 3, 21],
              // /////第三行数据
              [0, 2, 24],
              [1, 2, 27],
              [2, 2, 0],
              [3, 2, 66],
              [4, 2, 29],
              [5, 2, 12],
              [6, 2, 29],
              [7, 2, 0],
              [8, 2, 21],
              [9, 2, 17],
              [10, 2, 17],
              [11, 2, 17],
              [12, 2, 17],
              [13, 2, 17],
              // ///第二行数据
              [0, 1, 0],
              [1, 1, 16],
              [2, 1, 0],
              [3, 1, 20],
              [4, 1, 18],
              [5, 1, 57],
              [6, 1, 0],
              [7, 1, 61],
              [8, 1, 0],
              [9, 1, 25],
              [10, 1, 25],
              [11, 1, 25],
              [12, 1, 25],
              [13, 1, 25],
              // //第一行数据
              [0, 0, 24],
              [1, 0, 0],
              [2, 0, 21],
              [3, 0, 0],
              [4, 0, 16],
              [5, 0, 21],
              [6, 0, 57],
              [7, 0, 22],
              [8, 0, 17],
              [9, 0, 28],
              [10, 0, 28],
              [11, 0, 28],
              [12, 0, 28],
              [13, 0, 28]
            ],
            label: {
              show: true,
              color: '#000000',
              fontSize: 10
            },
            itemStyle: {
              borderColor: '#001E44',
              borderWidth: 3
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
}
