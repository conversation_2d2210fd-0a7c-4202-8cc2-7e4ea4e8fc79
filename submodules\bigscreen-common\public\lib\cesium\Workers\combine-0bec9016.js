/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./defined-3b3eb2ba"],(function(e,n){"use strict";e.combine=function e(t,o,r){r=n.defaultValue(r,!1);const f={},i=n.defined(t),d=n.defined(o);let s,a,c;if(i)for(s in t)t.hasOwnProperty(s)&&(a=t[s],d&&r&&"object"==typeof a&&o.hasOwnProperty(s)?(c=o[s],f[s]="object"==typeof c?e(a,c,r):a):f[s]=a);if(d)for(s in o)o.hasOwnProperty(s)&&!f.hasOwnProperty(s)&&(c=o[s],f[s]=c);return f}}));
