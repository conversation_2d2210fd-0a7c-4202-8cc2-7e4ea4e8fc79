<template>
  <div class="tabs-container">
    <div class="tabs-item" :class="{ selected: item.selected }" v-for="item in list" :key="item.id" @click="handleClick(item)">
      <div class="left">
        <div class="icon"><img :src="lib.utils.getAssetsFile(`ScreenRight/Perception/Structure/icon${item.id}${item.selected ? 'Selected' : ''}.png`)" /></div>
        <div class="name">{{ item.name }}</div>
      </div>
      <div class="right">
        <div class="num">{{ item.num }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import lib from '@/utils/lib.ts'

  defineOptions({
    name: 'Tabs'
  })
  const list = ref([
    { id: 1, name: '报警总数', num: '0', selected: true },
    { id: 2, name: '风机报警', num: '0', selected: false },
    { id: 3, name: '水泵报警', num: '0', selected: false }
  ])
  const handleClick = (val) => {
    list.value.forEach((item) => {
      item.selected = item.id === val.id
    })
  }
</script>

<style lang="scss" scoped>
  .tabs-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 87px;
    .tabs-item {
      display: flex;
      width: 184px;
      height: 87px;
      padding: 14px 5px;
      cursor: pointer;
      background: url('@/assets/ScreenRight/Perception/Structure/<EMAIL>');
      background-size: cover;
      .left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100px;
        .icon {
          width: 100%;
          height: 24px;
          text-align: center;
          img {
            width: 24px;
            height: 24px;
          }
        }
        .name {
          margin-top: 5px;
          font-family: 'Alibaba PuHuiTi-Regular', 'Alibaba PuHuiTi';
          font-size: 20px;
          font-weight: 400;
          line-height: 24px;
          color: #ffffff;
          text-align: center;
        }
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 100%;
        .num {
          font-family: PangMenZhengDao;
          font-size: 32px;
          font-weight: 400;
          line-height: 34px;
          color: #5fc6ff;
          text-align: center;
        }
      }
      &.selected {
        background: url('@/assets/ScreenRight/Perception/Structure/<EMAIL>');
        background-size: cover;
        .num {
          color: #ffb800;
        }
        .name {
          color: #ffffff;
        }
      }
    }
  }
</style>
