/**
 * @description: 拖动指令，与v-drag不同的是不能超出指定父盒子
 * @param {String} draggableClass 能拖动的子class，如果不传，则默认为整个父级都可以拖动
 * @param {String} parentClass 父盒子，拖动范围不能超过父盒子，父盒子必须有宽高，不传或者找不到对应的父盒子，则默认body为父盒子
 */
const vDragFixedRange = {
  mounted: (el, binding, vnode) => {
    const { draggableClass, parentClass } = binding.value || {}
    let draggableEl = el
    if (draggableClass) {
      draggableEl = el.querySelector('.' + draggableClass)
    }
    let parentEl = document.body
    if (parentClass && document.querySelector('.' + parentClass)) {
      parentEl = document.querySelector('.' + parentClass)
    }

    // 禁止弹窗超出限制范围
    const maxLeft = parentEl.scrollWidth - el.clientWidth
    const maxTop = parentEl.scrollHeight - el.clientHeight

    if (draggableEl) {
      draggableEl.onmousedown = (eDown) => {
        const divx = eDown.pageX - el.offsetLeft
        const divy = eDown.pageY - el.offsetTop
        document.onmousemove = (e) => {
          const left = e.pageX - divx <= maxLeft ? e.pageX - divx : maxLeft
          const top = e.pageY - divy <= maxTop ? e.pageY - divy : maxTop
          el.style.left = left < 0 ? 0 : left + 'px'
          el.style.top = top < 0 ? 0 : top + 'px'
        }
        document.onmouseup = function () {
          document.onmousemove = null
          document.onmouseup = null
        }
      }
    }
  }
}
export default vDragFixedRange
