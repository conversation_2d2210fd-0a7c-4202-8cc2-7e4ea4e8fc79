<template>
  <div class="time-range-picker">
    <ElPopover
      ref="timePopover"
      effect="customized"
      :visible="showPicker"
      trigger="click"
      popper-class="time-popover"
      transition="el-zoom-in-top"
      :width="324 + 'px'">
      <template #reference>
        <el-input v-model="inputValue" :placeholder="placeholder" :prefix-icon="Calendar" :clearable="false" @click="open" @clear="clearModelValue" />
      </template>
      <div v-click-outside="closePopover" class="time-picker-content">
        <!-- Tab 选项 -->
        <div class="tab-container">
          <span :class="{ active: selectedTab === 'month' }" @click="switchTab('month')">按月</span>
          <span :class="{ active: selectedTab === 'quarter' }" @click="switchTab('quarter')">按季</span>
          <span :class="{ active: selectedTab === 'year' }" @click="switchTab('year')">按年</span>
        </div>
        <!-- 根据不同的 Tab 显示对应的时间选择器 -->
        <div class="picker-container">
          <!-- 按月选择器 -->
          <div v-if="selectedTab === 'month'">
            <div class="custom-date-picker">
              <div class="picker-header">
                <el-icon @click="prevYear"><DArrowLeft /></el-icon>
                <span>{{ currentYear }} 年</span>
                <el-icon @click="nextYear"><DArrowRight /></el-icon>
              </div>
              <div class="month-grid">
                <div v-for="month in 12" :key="month" :class="{ selected: isMonthSelected(month) }" @click="selectMonth(month)">{{ month }} 月</div>
              </div>
            </div>
          </div>
          <!-- 按季度选择器 -->
          <div v-if="selectedTab === 'quarter'">
            <div class="custom-date-picker">
              <div class="picker-header">
                <el-icon @click="prevYear"><DArrowLeft /></el-icon>
                <span>{{ currentYear }} 年</span>
                <el-icon @click="nextYear"><DArrowRight /></el-icon>
              </div>
              <table class="quarter-table" @click="handleQuarterClick">
                <tbody>
                  <tr>
                    <td :class="getQuarterCellStyle(0)">
                      <a class="cell">第一季度</a>
                    </td>
                    <td :class="getQuarterCellStyle(1)">
                      <a class="cell">第二季度</a>
                    </td>
                  </tr>
                  <tr>
                    <td :class="getQuarterCellStyle(2)">
                      <a class="cell">第三季度</a>
                    </td>
                    <td :class="getQuarterCellStyle(3)">
                      <a class="cell">第四季度</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- 按年选择器 -->
          <div v-if="selectedTab === 'year'">
            <div class="custom-date-picker">
              <div class="picker-header">
                <el-icon @click="prevYearRange"><DArrowLeft /></el-icon>
                <span>{{ yearRangeStart }} - {{ yearRangeEnd }} 年</span>
                <el-icon @click="nextYearRange"><DArrowRight /></el-icon>
              </div>
              <div class="year-grid">
                <div v-for="year in yearRange" :key="year" :class="{ selected: isYearSelected(year) }" @click="selectYear(year)">{{ year }} 年</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ElPopover>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElPopover } from 'element-plus'
  import { Calendar, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
  const props = withDefaults(
    defineProps<{
      modelValue: string | Date
      placeholder?: string
    }>(),
    {
      modelValue: '',
      placeholder: '请选择时间'
    }
  )

  const emit = defineEmits(['update:modelValue', 'update:selectedTab'])

  // 控制弹出框显示与隐藏
  const showPicker = ref(false)
  // 输入框的值
  const inputValue = ref(props.modelValue)
  // 当前选中的 Tab
  const selectedTab = ref('month')
  // 当前年份
  const currentYear = ref(new Date().getFullYear())
  // 年份范围起始
  const yearRangeStart = ref(Math.floor(currentYear.value / 10) * 10)
  // 年份范围结束
  const yearRangeEnd = ref(yearRangeStart.value + 9)

  // 计算年份范围
  const yearRange = computed(() => {
    return Array.from({ length: 10 }, (_, i) => yearRangeStart.value + i)
  })

  // 处理时间选择变化
  const handleChange = (value: string | Date) => {
    emit('update:modelValue', value)
    const date = new Date(value)
    let formattedValue = ''

    if (selectedTab.value === 'month') {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      formattedValue = `${year}-${month}`
    } else if (selectedTab.value === 'quarter') {
      const year = date.getFullYear()
      const quarter = Math.floor(date.getMonth() / 3) + 1
      formattedValue = `${year}年第${quarter}季度`
    } else if (selectedTab.value === 'year') {
      const year = date.getFullYear()
      formattedValue = `${year}`
    }
    console.log('formattedValue', formattedValue)
    inputValue.value = formattedValue
    showPicker.value = false
  }

  // 切换 tab 的方法
  const switchTab = (tabType: string) => {
    selectedTab.value = tabType
    emit('update:selectedTab', tabType)
  }

  // 关闭弹出框
  const closePopover = () => {
    showPicker.value = false
  }

  // 打开弹出框
  const open = () => {
    showPicker.value = !showPicker.value
  }

  // 清除选择的值
  const clearModelValue = () => {
    emit('update:modelValue', '')
    inputValue.value = ''
  }

  // 前一年
  const prevYear = () => {
    currentYear.value--
  }

  // 后一年
  const nextYear = () => {
    currentYear.value++
  }

  // 前十年
  const prevYearRange = () => {
    yearRangeStart.value -= 10
    yearRangeEnd.value -= 10
  }

  // 后十年
  const nextYearRange = () => {
    yearRangeStart.value += 10
    yearRangeEnd.value += 10
  }

  // 选择月份
  const selectMonth = (month: number) => {
    const selectedDate = new Date(currentYear.value, month - 1)
    handleChange(selectedDate)
  }

  // 判断月份是否被选中
  const isMonthSelected = (month: number) => {
    if (!props.modelValue) return false
    const selectedDate = new Date(props.modelValue)
    return selectedDate.getFullYear() === currentYear.value && selectedDate.getMonth() === month - 1
  }

  // 处理季度点击
  const handleQuarterClick = (event: MouseEvent) => {
    let target = event.target as HTMLElement
    if (target.tagName === 'A') {
      target = target.parentNode as HTMLTableCellElement
    }
    if (target.tagName !== 'TD') return
    const column = (target as HTMLTableCellElement).cellIndex
    const row = (target.parentNode as HTMLTableRowElement).rowIndex
    const quarter = row * 2 + column
    const month = quarter * 3
    const selectedDate = new Date(currentYear.value, month)
    handleChange(selectedDate)
  }

  // 获取季度单元格样式
  const getQuarterCellStyle = (quarter: number) => {
    if (!props.modelValue) return 'available'
    const selectedDate = new Date(props.modelValue)
    const selectedQuarter = Math.floor(selectedDate.getMonth() / 3)
    return {
      available: true,
      selected: selectedDate.getFullYear() === currentYear.value && selectedQuarter === quarter
    }
  }

  // 选择年份
  const selectYear = (year: number) => {
    const selectedDate = new Date(year, 0)
    handleChange(selectedDate)
  }

  // 判断年份是否被选中
  const isYearSelected = (year: number) => {
    if (!props.modelValue) return false
    const selectedDate = new Date(props.modelValue)
    return selectedDate.getFullYear() === year
  }

  // 定义 ElPopover 的 ref
  const timePopover = ref(null)
</script>
<style>
  .el-popper.is-customized {
    padding: 0;
    background: url('@/assets/ScreenLeft/DynamicMaintenance/timeBg.png') no-repeat !important;
    background-size: 100% 100% !important;
    border: none !important;
  }
</style>
<style lang="scss" scoped>
  .time-range-picker {
    position: absolute;
  }
  .time-popover {
    padding: 0 !important;
  }
  .tab-container {
    display: flex;
    justify-content: space-evenly;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .tab-container span {
    padding: 5px 10px;
    margin-right: -1px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    cursor: pointer;
  }
  .tab-container span.active {
    font-weight: bold;
    color: #63eaff;
  }
  .picker-container {
    padding: 10px;
  }
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    font-weight: 400;
  }
  .month-grid,
  .year-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
  }
  .month-grid div,
  .year-grid div {
    padding: 5px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
  }
  .month-grid div.selected,
  .year-grid div.selected {
    color: white;
    background-color: #63eaff;
    border-radius: 6px;
  }
  .quarter-table {
    width: 100%;
    border-collapse: collapse;
  }
  .quarter-table td {
    padding: 20px 3px;
    font-family: 'Source Han Sans CN';
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
  }
  .quarter-table td.selected .cell {
    color: #63eaff;
    border-radius: 6px;
  }
  .quarter-table td .cell {
    display: block;
    height: 20px;
    margin: 0 auto;
    line-height: 20px;
    color: #ffffff;
  }
  :deep(.el-input) {
    --el-input-height: 20px !important;
  }
  :deep(.el-input__wrapper) {
    width: 200px;
    height: 42px;
    background: url('@/assets/CommonPopup/dateTime.png') no-repeat !important;
    box-shadow: none;
  }
  :deep(.el-input__inner) {
    padding: 0 !important;
    margin-left: -5px !important;
    font-size: 18px !important;
  }
  :deep(.el-input__prefix) {
    color: #8dd8ff;
  }
  :deep(.el-input__icon svg) {
    scale: 1.5;
  }

  // 增加前缀图标的右边距
  :deep(.el-input__prefix) {
    padding-right: 20px; // 可以根据需要调整间隔大小
  }
</style>
