define(["exports"],(function(t){"use strict";var e=function(){var t="undefined"!==typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!==typeof __filename&&(t=t||__filename),function(e){e=e||{};var r,n="undefined"!==typeof e?e:{};n["ready"]=new Promise((function(t,e){r=t}));var p=!1,i=!1;n["onRuntimeInitialized"]=function(){p=!0,i&&"function"===typeof n["onModuleLoaded"]&&n["onModuleLoaded"](n)},n["onModuleParsed"]=function(){i=!0,p&&"function"===typeof n["onModuleLoaded"]&&n["onModuleLoaded"](n)};var o,_={};for(o in n)n.hasOwnProperty(o)&&(_[o]=n[o]);var a="./this.program",c=function(t,e){throw e},u=!1,l=!1,s=!1,y=!1;u="object"===typeof window,l="function"===typeof importScripts,s="object"===typeof process&&"object"===typeof process.versions&&"string"===typeof process.versions.node,y=!u&&!s&&!l;var d,m,f,b,S="";function v(t){return n["locateFile"]?n["locateFile"](t,S):S+t}s?(S=l?require("path").dirname(S)+"/":__dirname+"/",d=function(t,e){return f||(f=require("fs")),b||(b=require("path")),t=b["normalize"](t),f["readFileSync"](t,e?null:"utf8")},m=function(t){var e=d(t,!0);return e.buffer||(e=new Uint8Array(e)),R(e.buffer),e},process["argv"].length>1&&(a=process["argv"][1].replace(/\\/g,"/")),process["argv"].slice(2),process["on"]("uncaughtException",(function(t){if(!(t instanceof co))throw t})),process["on"]("unhandledRejection",mt),c=function(t){process["exit"](t)},n["inspect"]=function(){return"[Emscripten Module object]"}):y?("undefined"!=typeof read&&(d=function(t){return read(t)}),m=function(t){var e;return"function"===typeof readbuffer?new Uint8Array(readbuffer(t)):(e=read(t,"binary"),R("object"===typeof e),e)},"undefined"!=typeof scriptArgs?scriptArgs:"undefined"!=typeof arguments&&arguments,"function"===typeof quit&&(c=function(t){quit(t)}),"undefined"!==typeof print&&("undefined"===typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!==typeof printErr?printErr:print)):(u||l)&&(l?S=self.location.href:document.currentScript&&(S=document.currentScript.src),t&&(S=t),S=0!==S.indexOf("blob:")?S.substr(0,S.lastIndexOf("/")+1):"",d=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},l&&(m=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}));var h=n["print"]||console.log.bind(console),B=n["printErr"]||console.warn.bind(console);for(o in _)_.hasOwnProperty(o)&&(n[o]=_[o]);_=null,n["arguments"]&&n["arguments"],n["thisProgram"]&&(a=n["thisProgram"]),n["quit"]&&(c=n["quit"]);var g,L,P,I=0,j=function(t){I=t},G=function(){return I};n["wasmBinary"]&&(g=n["wasmBinary"]),n["noExitRuntime"]&&(L=n["noExitRuntime"]),"object"!==typeof WebAssembly&&B("no native wasm support detected");var x=new WebAssembly.Table({initial:1611,maximum:1611,element:"anyfunc"}),C=!1;function R(t,e){t||mt("Assertion failed: "+e)}function M(t){var e=n["_"+t];return R(e,"Cannot call unknown function "+t+", make sure it is exported"),e}function V(t,e,r,n,p){var i={string:function(t){var e=0;if(null!==t&&void 0!==t&&0!==t){var r=1+(t.length<<2);e=Ai(r),N(t,e,r)}return e},array:function(t){var e=Ai(t.length);return k(t,e),e}};function o(t){return"string"===e?A(t):"boolean"===e?Boolean(t):t}var _=M(t),a=[],c=0;if(n)for(var u=0;u<n.length;u++){var l=i[r[u]];l?(0===c&&(c=Di()),a[u]=l(n[u])):a[u]=n[u]}var s=_.apply(null,a);return s=o(s),0!==c&&Ei(c),s}function w(t,e,r,n){r=r||[];var p=r.every((function(t){return"number"===t})),i="string"!==e;return i&&p&&!n?M(t):function(){return V(t,e,r,arguments)}}var T="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;function D(t,e,r){var n=e+r,p=e;while(t[p]&&!(p>=n))++p;if(p-e>16&&t.subarray&&T)return T.decode(t.subarray(e,p));var i="";while(e<p){var o=t[e++];if(128&o){var _=63&t[e++];if(192!=(224&o)){var a=63&t[e++];if(o=224==(240&o)?(15&o)<<12|_<<6|a:(7&o)<<18|_<<12|a<<6|63&t[e++],o<65536)i+=String.fromCharCode(o);else{var c=o-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&o)<<6|_)}else i+=String.fromCharCode(o)}return i}function A(t,e){return t?D(H,t,e):""}function E(t,e,r,n){if(!(n>0))return 0;for(var p=r,i=r+n-1,o=0;o<t.length;++o){var _=t.charCodeAt(o);if(_>=55296&&_<=57343){var a=t.charCodeAt(++o);_=65536+((1023&_)<<10)|1023&a}if(_<=127){if(r>=i)break;e[r++]=_}else if(_<=2047){if(r+1>=i)break;e[r++]=192|_>>6,e[r++]=128|63&_}else if(_<=65535){if(r+2>=i)break;e[r++]=224|_>>12,e[r++]=128|_>>6&63,e[r++]=128|63&_}else{if(r+3>=i)break;e[r++]=240|_>>18,e[r++]=128|_>>12&63,e[r++]=128|_>>6&63,e[r++]=128|63&_}}return e[r]=0,r-p}function N(t,e,r){return E(t,H,e,r)}function O(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&t.charCodeAt(++r)),n<=127?++e:e+=n<=2047?2:n<=65535?3:4}return e}function k(t,e){W.set(t,e)}function z(t,e,r){for(var n=0;n<t.length;++n)W[e++>>0]=t.charCodeAt(n);r||(W[e>>0]=0)}var F,W,H,U,Y,q,X=65536;function J(t,e){return t%e>0&&(t+=e-t%e),t}function Z(t){F=t,n["HEAP8"]=W=new Int8Array(t),n["HEAP16"]=new Int16Array(t),n["HEAP32"]=U=new Int32Array(t),n["HEAPU8"]=H=new Uint8Array(t),n["HEAPU16"]=new Uint16Array(t),n["HEAPU32"]=new Uint32Array(t),n["HEAPF32"]=Y=new Float32Array(t),n["HEAPF64"]=q=new Float64Array(t)}var K=5329136,Q=86080,$=n["INITIAL_MEMORY"]||33554432;function tt(t){while(t.length>0){var e=t.shift();if("function"!=typeof e){var r=e.func;"number"===typeof r?void 0===e.arg?n["dynCall_v"](r):n["dynCall_vi"](r,e.arg):r(void 0===e.arg?null:e.arg)}else e(n)}}P=n["wasmMemory"]?n["wasmMemory"]:new WebAssembly.Memory({initial:$/X,maximum:2147483648/X}),P&&(F=P.buffer),$=F.byteLength,Z(F),U[Q>>2]=K;var et=[],rt=[],nt=[],pt=[];function it(){if(n["preRun"]){"function"==typeof n["preRun"]&&(n["preRun"]=[n["preRun"]]);while(n["preRun"].length)ct(n["preRun"].shift())}tt(et)}function ot(){tt(rt)}function _t(){tt(nt)}function at(){if(n["postRun"]){"function"==typeof n["postRun"]&&(n["postRun"]=[n["postRun"]]);while(n["postRun"].length)ut(n["postRun"].shift())}tt(pt)}function ct(t){et.unshift(t)}function ut(t){pt.unshift(t)}var lt=0,st=null;function yt(t){lt++,n["monitorRunDependencies"]&&n["monitorRunDependencies"](lt)}function dt(t){if(lt--,n["monitorRunDependencies"]&&n["monitorRunDependencies"](lt),0==lt&&st){var e=st;st=null,e()}}function mt(t){throw n["onAbort"]&&n["onAbort"](t),t+="",h(t),B(t),C=!0,t="abort("+t+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(t)}function ft(t,e){return String.prototype.startsWith?t.startsWith(e):0===t.indexOf(e)}n["preloadedImages"]={},n["preloadedAudios"]={};var bt="data:application/octet-stream;base64,";function St(t){return ft(t,bt)}var vt="file://";function ht(t){return ft(t,vt)}var Bt="materem.wasm";function gt(){try{if(g)return new Uint8Array(g);if(m)return m(Bt);throw"both async and sync fetching of the wasm failed"}catch(B){mt(B)}}function Lt(){return g||!u&&!l||"function"!==typeof fetch||ht(Bt)?new Promise((function(t,e){t(gt())})):fetch(Bt,{credentials:"same-origin"}).then((function(t){if(!t["ok"])throw"failed to load wasm binary file at '"+Bt+"'";return t["arrayBuffer"]()})).catch((function(){return gt()}))}function Pt(){var t={a:Se};function e(t,e){var r=t.exports;n["asm"]=r,dt()}function r(t){e(t["instance"])}function p(e){return Lt().then((function(e){return WebAssembly.instantiate(e,t)})).then(e,(function(t){B("failed to asynchronously prepare wasm: "+t),mt(t)}))}function i(){if(g||"function"!==typeof WebAssembly.instantiateStreaming||St(Bt)||ht(Bt)||"function"!==typeof fetch)return p(r);fetch(Bt,{credentials:"same-origin"}).then((function(e){var n=WebAssembly.instantiateStreaming(e,t);return n.then(r,(function(t){B("wasm streaming compile failed: "+t),B("falling back to ArrayBuffer instantiation"),p(r)}))}))}if(yt(),n["instantiateWasm"])try{var o=n["instantiateWasm"](t,e);return o}catch(_){return B("Module.instantiateWasm callback failed with error: "+_),!1}return i(),{}}function It(t){return pi(t)}St(Bt)||(Bt=v(Bt)),rt.push({func:function(){Be()}});var jt={},Gt=[];function xt(t){if(t){var e=jt[t];e.refcount++}}function Ct(t){if(!t||jt[t])return t;for(var e in jt)for(var r=+e,n=jt[r].adjusted,p=n.length,i=0;i<p;i++)if(n[i]===t)return r;return t}function Rt(t){var e=jt[t];return e&&!e.caught&&(e.caught=!0,_i.uncaught_exceptions--),e&&(e.rethrown=!1),Gt.push(t),xt(Ct(t)),t}var Mt=0;function Vt(t){return ni(t)}function wt(t){if(t){var e=jt[t];e.refcount--,0!==e.refcount||e.rethrown||(e.destructor&&n["dynCall_ii"](e.destructor,t),delete jt[t],Vt(t))}}function Tt(){oi(0);var t=Gt.pop();t&&(wt(Ct(t)),Mt=0)}function Dt(){var t=Mt;if(!t)return 0|(j(0),0);var e=jt[t],r=e.type;if(!r)return 0|(j(0),t);var n=Array.prototype.slice.call(arguments),p=(ci(r),86240);U[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&ai(n[i],r,t))return t=U[t>>2],e.adjusted.push(t),0|(j(n[i]),t);return t=U[t>>2],0|(j(r),t)}function At(){var t=Mt;if(!t)return 0|(j(0),0);var e=jt[t],r=e.type;if(!r)return 0|(j(0),t);var n=Array.prototype.slice.call(arguments),p=(ci(r),86240);U[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&ai(n[i],r,t))return t=U[t>>2],e.adjusted.push(t),0|(j(n[i]),t);return t=U[t>>2],0|(j(r),t)}function Et(){var t=Gt.pop();throw t=Ct(t),jt[t].rethrown||(Gt.push(t),jt[t].rethrown=!0),Mt=t,t}function Nt(t,e,r){throw jt[t]={ptr:t,adjusted:[t],type:e,destructor:r,refcount:0,caught:!1,rethrown:!1},Mt=t,"uncaught_exception"in _i?_i.uncaught_exceptions++:_i.uncaught_exceptions=1,t}function Ot(){return _i.uncaught_exceptions}function kt(t){return U[ii()>>2]=t,t}function zt(t,e){return kt(63),-1}function Ft(t){throw Mt||(Mt=t),t}var Wt={mappings:{},buffers:[null,[],[]],printChar:function(t,e){var r=Wt.buffers[t];0===e||10===e?((1===t?h:B)(D(r,0)),r.length=0):r.push(e)},varargs:void 0,get:function(){Wt.varargs+=4;var t=U[Wt.varargs-4>>2];return t},getStr:function(t){var e=A(t);return e},get64:function(t,e){return t}};function Ht(t,e){if(-1===(0|t)||0===e)return-28;var r=Wt.mappings[t];return r?(e===r.len&&(Wt.mappings[t]=null,r.allocated&&ni(r.malloc)),0):0}function Ut(t,e){return Ht(t,e)}function Yt(){mt()}function qt(t,e,r){H.copyWithin(t,e,e+r)}function Xt(){return H.length}function Jt(t){try{return P.grow(t-F.byteLength+65535>>>16),Z(P.buffer),1}catch(e){}}function Zt(t){t>>>=0;var e=Xt(),r=65536,n=2147483648;if(t>n)return!1;for(var p=16777216,i=1;i<=4;i*=2){var o=e*(1+.2/i);o=Math.min(o,t+100663296);var _=Math.min(n,J(Math.max(p,t,o),r)),a=Jt(_);if(a)return!0}return!1}var Kt={};function Qt(){return a||"./this.program"}function $t(){if(!$t.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"===typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:Qt()};for(var e in Kt)t[e]=Kt[e];var r=[];for(var e in t)r.push(e+"="+t[e]);$t.strings=r}return $t.strings}function te(t,e){var r=0;return $t().forEach((function(n,p){var i=e+r;U[t+4*p>>2]=i,z(n,i),r+=n.length+1})),0}function ee(t,e){var r=$t();U[t>>2]=r.length;var n=0;return r.forEach((function(t){n+=t.length+1})),U[e>>2]=n,0}function re(t){lo(t)}function ne(t){return 0}function pe(t,e,r,n){var p=Wt.getStreamFromFD(t),i=Wt.doReadv(p,e,r);return U[n>>2]=i,0}function ie(t,e,r,n,p){}function oe(t,e,r,n){for(var p=0,i=0;i<r;i++){for(var o=U[e+8*i>>2],_=U[e+(8*i+4)>>2],a=0;a<_;a++)Wt.printChar(t,H[o+a]);p+=_}return U[n>>2]=p,0}function _e(){return 0|G()}function ae(t){return t}function ce(t){return t%4===0&&(t%100!==0||t%400===0)}function ue(t,e){for(var r=0,n=0;n<=e;r+=t[n++]);return r}var le=[31,29,31,30,31,30,31,31,30,31,30,31],se=[31,28,31,30,31,30,31,31,30,31,30,31];function ye(t,e){var r=new Date(t.getTime());while(e>0){var n=ce(r.getFullYear()),p=r.getMonth(),i=(n?le:se)[p];if(!(e>i-r.getDate()))return r.setDate(r.getDate()+e),r;e-=i-r.getDate()+1,r.setDate(1),p<11?r.setMonth(p+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1))}return r}function de(t,e,r,n){var p=U[n+40>>2],i={tm_sec:U[n>>2],tm_min:U[n+4>>2],tm_hour:U[n+8>>2],tm_mday:U[n+12>>2],tm_mon:U[n+16>>2],tm_year:U[n+20>>2],tm_wday:U[n+24>>2],tm_yday:U[n+28>>2],tm_isdst:U[n+32>>2],tm_gmtoff:U[n+36>>2],tm_zone:p?A(p):""},o=A(r),_={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var a in _)o=o.replace(new RegExp(a,"g"),_[a]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],u=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(t,e,r){var n="number"===typeof t?t.toString():t||"";while(n.length<e)n=r[0]+n;return n}function s(t,e){return l(t,e,"0")}function y(t,e){function r(t){return t<0?-1:t>0?1:0}var n;return 0===(n=r(t.getFullYear()-e.getFullYear()))&&0===(n=r(t.getMonth()-e.getMonth()))&&(n=r(t.getDate()-e.getDate())),n}function d(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function m(t){var e=ye(new Date(t.tm_year+1900,0,1),t.tm_yday),r=new Date(e.getFullYear(),0,4),n=new Date(e.getFullYear()+1,0,4),p=d(r),i=d(n);return y(p,e)<=0?y(i,e)<=0?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var f={"%a":function(t){return c[t.tm_wday].substring(0,3)},"%A":function(t){return c[t.tm_wday]},"%b":function(t){return u[t.tm_mon].substring(0,3)},"%B":function(t){return u[t.tm_mon]},"%C":function(t){var e=t.tm_year+1900;return s(e/100|0,2)},"%d":function(t){return s(t.tm_mday,2)},"%e":function(t){return l(t.tm_mday,2," ")},"%g":function(t){return m(t).toString().substring(2)},"%G":function(t){return m(t)},"%H":function(t){return s(t.tm_hour,2)},"%I":function(t){var e=t.tm_hour;return 0==e?e=12:e>12&&(e-=12),s(e,2)},"%j":function(t){return s(t.tm_mday+ue(ce(t.tm_year+1900)?le:se,t.tm_mon-1),3)},"%m":function(t){return s(t.tm_mon+1,2)},"%M":function(t){return s(t.tm_min,2)},"%n":function(){return"\n"},"%p":function(t){return t.tm_hour>=0&&t.tm_hour<12?"AM":"PM"},"%S":function(t){return s(t.tm_sec,2)},"%t":function(){return"\t"},"%u":function(t){return t.tm_wday||7},"%U":function(t){var e=new Date(t.tm_year+1900,0,1),r=0===e.getDay()?e:ye(e,7-e.getDay()),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=ue(ce(n.getFullYear())?le:se,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%V":function(t){var e,r=new Date(t.tm_year+1900,0,4),n=new Date(t.tm_year+1901,0,4),p=d(r),i=d(n),o=ye(new Date(t.tm_year+1900,0,1),t.tm_yday);return y(o,p)<0?"53":y(i,o)<=0?"01":(e=p.getFullYear()<t.tm_year+1900?t.tm_yday+32-p.getDate():t.tm_yday+1-p.getDate(),s(Math.ceil(e/7),2))},"%w":function(t){return t.tm_wday},"%W":function(t){var e=new Date(t.tm_year,0,1),r=1===e.getDay()?e:ye(e,0===e.getDay()?1:7-e.getDay()+1),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=ue(ce(n.getFullYear())?le:se,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%y":function(t){return(t.tm_year+1900).toString().substring(2)},"%Y":function(t){return t.tm_year+1900},"%z":function(t){var e=t.tm_gmtoff,r=e>=0;return e=Math.abs(e)/60,e=e/60*100+e%60,(r?"+":"-")+String("0000"+e).slice(-4)},"%Z":function(t){return t.tm_zone},"%%":function(){return"%"}};for(var a in f)o.indexOf(a)>=0&&(o=o.replace(new RegExp(a,"g"),f[a](i)));var b=be(o,!1);return b.length>e?0:(k(b,t),b.length-1)}function me(t,e,r,n){return de(t,e,r,n)}function fe(t){var e=Date.now()/1e3|0;return t&&(U[t>>2]=e),e}function be(t,e,r){var n=r>0?r:O(t)+1,p=new Array(n),i=E(t,p,0,p.length);return e&&(p.length=i),p}var Se={m:It,q:Rt,t:Tt,b:Dt,l:At,n:Vt,F:Et,u:Nt,W:Ot,U:zt,f:Ft,T:Ut,K:Yt,R:qt,v:Zt,X:te,Y:ee,C:re,J:ne,V:pe,L:ie,I:oe,a:_e,x:Qi,M:ao,c:zi,E:$i,d:Ni,g:Wi,H:Zi,o:Yi,N:ro,y:qi,p:Xi,G:no,P:Ki,B:io,i:Oi,h:ki,e:Fi,D:eo,k:Hi,j:Ui,s:Ji,r:po,w:oo,O:to,A:_o,Q:ae,memory:P,S:me,table:x,z:fe},ve=Pt();n["asm"]=ve;var he,Be=n["___wasm_call_ctors"]=function(){return(Be=n["___wasm_call_ctors"]=n["asm"]["Z"]).apply(null,arguments)},ge=(n["___em_js__array_bounds_check_error"]=function(){return(n["___em_js__array_bounds_check_error"]=n["asm"]["_"]).apply(null,arguments)},n["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(ge=n["_emscripten_bind_VoidPtr___destroy___0"]=n["asm"]["$"]).apply(null,arguments)}),Le=n["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=function(){return(Le=n["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=n["asm"]["aa"]).apply(null,arguments)},Pe=n["_emscripten_bind_LBProj4Wrapper_Init_2"]=function(){return(Pe=n["_emscripten_bind_LBProj4Wrapper_Init_2"]=n["asm"]["ba"]).apply(null,arguments)},Ie=n["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=function(){return(Ie=n["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=n["asm"]["ca"]).apply(null,arguments)},je=n["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=function(){return(je=n["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=n["asm"]["da"]).apply(null,arguments)},Ge=n["_emscripten_bind_LBProj4Wrapper___destroy___0"]=function(){return(Ge=n["_emscripten_bind_LBProj4Wrapper___destroy___0"]=n["asm"]["ea"]).apply(null,arguments)},xe=n["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=function(){return(xe=n["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=n["asm"]["fa"]).apply(null,arguments)},Ce=n["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=function(){return(Ce=n["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=n["asm"]["ga"]).apply(null,arguments)},Re=n["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=function(){return(Re=n["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=n["asm"]["ha"]).apply(null,arguments)},Me=n["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=function(){return(Me=n["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=n["asm"]["ia"]).apply(null,arguments)},Ve=n["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=function(){return(Ve=n["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=n["asm"]["ja"]).apply(null,arguments)},we=n["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=function(){return(we=n["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=n["asm"]["ka"]).apply(null,arguments)},Te=n["_emscripten_bind_LBEdgeFormer___destroy___0"]=function(){return(Te=n["_emscripten_bind_LBEdgeFormer___destroy___0"]=n["asm"]["la"]).apply(null,arguments)},De=n["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=function(){return(De=n["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=n["asm"]["ma"]).apply(null,arguments)},Ae=n["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=function(){return(Ae=n["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=n["asm"]["na"]).apply(null,arguments)},Ee=n["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=function(){return(Ee=n["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=n["asm"]["oa"]).apply(null,arguments)},Ne=n["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=function(){return(Ne=n["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=n["asm"]["pa"]).apply(null,arguments)},Oe=n["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=function(){return(Oe=n["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=n["asm"]["qa"]).apply(null,arguments)},ke=n["_emscripten_bind_LBPlanishAry___destroy___0"]=function(){return(ke=n["_emscripten_bind_LBPlanishAry___destroy___0"]=n["asm"]["ra"]).apply(null,arguments)},ze=n["_emscripten_bind_LBDeal_LBDeal_0"]=function(){return(ze=n["_emscripten_bind_LBDeal_LBDeal_0"]=n["asm"]["sa"]).apply(null,arguments)},Fe=n["_emscripten_bind_LBDeal_Init_4"]=function(){return(Fe=n["_emscripten_bind_LBDeal_Init_4"]=n["asm"]["ta"]).apply(null,arguments)},We=n["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=function(){return(We=n["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=n["asm"]["ua"]).apply(null,arguments)},He=n["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=function(){return(He=n["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=n["asm"]["va"]).apply(null,arguments)},Ue=n["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=function(){return(Ue=n["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=n["asm"]["wa"]).apply(null,arguments)},Ye=n["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=function(){return(Ye=n["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=n["asm"]["xa"]).apply(null,arguments)},qe=n["_emscripten_bind_LBDeal___destroy___0"]=function(){return(qe=n["_emscripten_bind_LBDeal___destroy___0"]=n["asm"]["ya"]).apply(null,arguments)},Xe=n["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=function(){return(Xe=n["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=n["asm"]["za"]).apply(null,arguments)},Je=n["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=function(){return(Je=n["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=n["asm"]["Aa"]).apply(null,arguments)},Ze=n["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=function(){return(Ze=n["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=n["asm"]["Ba"]).apply(null,arguments)},Ke=n["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=function(){return(Ke=n["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=n["asm"]["Ca"]).apply(null,arguments)},Qe=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=function(){return(Qe=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=n["asm"]["Da"]).apply(null,arguments)},$e=n["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=function(){return($e=n["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=n["asm"]["Ea"]).apply(null,arguments)},tr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=function(){return(tr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=n["asm"]["Fa"]).apply(null,arguments)},er=n["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=function(){return(er=n["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=n["asm"]["Ga"]).apply(null,arguments)},rr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=function(){return(rr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=n["asm"]["Ha"]).apply(null,arguments)},nr=n["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=function(){return(nr=n["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=n["asm"]["Ia"]).apply(null,arguments)},pr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=function(){return(pr=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=n["asm"]["Ja"]).apply(null,arguments)},ir=n["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=function(){return(ir=n["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=n["asm"]["Ka"]).apply(null,arguments)},or=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=function(){return(or=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=n["asm"]["La"]).apply(null,arguments)},_r=n["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=function(){return(_r=n["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=n["asm"]["Ma"]).apply(null,arguments)},ar=n["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=function(){return(ar=n["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=n["asm"]["Na"]).apply(null,arguments)},cr=n["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=function(){return(cr=n["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=n["asm"]["Oa"]).apply(null,arguments)},ur=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=function(){return(ur=n["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=n["asm"]["Pa"]).apply(null,arguments)},lr=n["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=function(){return(lr=n["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=n["asm"]["Qa"]).apply(null,arguments)},sr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=function(){return(sr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=n["asm"]["Ra"]).apply(null,arguments)},yr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=function(){return(yr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=n["asm"]["Sa"]).apply(null,arguments)},dr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=function(){return(dr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=n["asm"]["Ta"]).apply(null,arguments)},mr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=function(){return(mr=n["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=n["asm"]["Ua"]).apply(null,arguments)},fr=n["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=function(){return(fr=n["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=n["asm"]["Va"]).apply(null,arguments)},br=n["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=function(){return(br=n["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=n["asm"]["Wa"]).apply(null,arguments)},Sr=n["_emscripten_bind_LBSpaVec_get_x_0"]=function(){return(Sr=n["_emscripten_bind_LBSpaVec_get_x_0"]=n["asm"]["Xa"]).apply(null,arguments)},vr=n["_emscripten_bind_LBSpaVec_set_x_1"]=function(){return(vr=n["_emscripten_bind_LBSpaVec_set_x_1"]=n["asm"]["Ya"]).apply(null,arguments)},hr=n["_emscripten_bind_LBSpaVec_get_y_0"]=function(){return(hr=n["_emscripten_bind_LBSpaVec_get_y_0"]=n["asm"]["Za"]).apply(null,arguments)},Br=n["_emscripten_bind_LBSpaVec_set_y_1"]=function(){return(Br=n["_emscripten_bind_LBSpaVec_set_y_1"]=n["asm"]["_a"]).apply(null,arguments)},gr=n["_emscripten_bind_LBSpaVec_get_z_0"]=function(){return(gr=n["_emscripten_bind_LBSpaVec_get_z_0"]=n["asm"]["$a"]).apply(null,arguments)},Lr=n["_emscripten_bind_LBSpaVec_set_z_1"]=function(){return(Lr=n["_emscripten_bind_LBSpaVec_set_z_1"]=n["asm"]["ab"]).apply(null,arguments)},Pr=n["_emscripten_bind_LBSpaVec___destroy___0"]=function(){return(Pr=n["_emscripten_bind_LBSpaVec___destroy___0"]=n["asm"]["bb"]).apply(null,arguments)},Ir=n["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=function(){return(Ir=n["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=n["asm"]["cb"]).apply(null,arguments)},jr=n["_emscripten_bind_LBSpaVec2_get_x_0"]=function(){return(jr=n["_emscripten_bind_LBSpaVec2_get_x_0"]=n["asm"]["db"]).apply(null,arguments)},Gr=n["_emscripten_bind_LBSpaVec2_set_x_1"]=function(){return(Gr=n["_emscripten_bind_LBSpaVec2_set_x_1"]=n["asm"]["eb"]).apply(null,arguments)},xr=n["_emscripten_bind_LBSpaVec2_get_y_0"]=function(){return(xr=n["_emscripten_bind_LBSpaVec2_get_y_0"]=n["asm"]["fb"]).apply(null,arguments)},Cr=n["_emscripten_bind_LBSpaVec2_set_y_1"]=function(){return(Cr=n["_emscripten_bind_LBSpaVec2_set_y_1"]=n["asm"]["gb"]).apply(null,arguments)},Rr=n["_emscripten_bind_LBSpaVec2___destroy___0"]=function(){return(Rr=n["_emscripten_bind_LBSpaVec2___destroy___0"]=n["asm"]["hb"]).apply(null,arguments)},Mr=n["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=function(){return(Mr=n["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=n["asm"]["ib"]).apply(null,arguments)},Vr=n["_emscripten_bind_LBSpaVec4_get_x_0"]=function(){return(Vr=n["_emscripten_bind_LBSpaVec4_get_x_0"]=n["asm"]["jb"]).apply(null,arguments)},wr=n["_emscripten_bind_LBSpaVec4_set_x_1"]=function(){return(wr=n["_emscripten_bind_LBSpaVec4_set_x_1"]=n["asm"]["kb"]).apply(null,arguments)},Tr=n["_emscripten_bind_LBSpaVec4_get_y_0"]=function(){return(Tr=n["_emscripten_bind_LBSpaVec4_get_y_0"]=n["asm"]["lb"]).apply(null,arguments)},Dr=n["_emscripten_bind_LBSpaVec4_set_y_1"]=function(){return(Dr=n["_emscripten_bind_LBSpaVec4_set_y_1"]=n["asm"]["mb"]).apply(null,arguments)},Ar=n["_emscripten_bind_LBSpaVec4_get_z_0"]=function(){return(Ar=n["_emscripten_bind_LBSpaVec4_get_z_0"]=n["asm"]["nb"]).apply(null,arguments)},Er=n["_emscripten_bind_LBSpaVec4_set_z_1"]=function(){return(Er=n["_emscripten_bind_LBSpaVec4_set_z_1"]=n["asm"]["ob"]).apply(null,arguments)},Nr=n["_emscripten_bind_LBSpaVec4_get_w_0"]=function(){return(Nr=n["_emscripten_bind_LBSpaVec4_get_w_0"]=n["asm"]["pb"]).apply(null,arguments)},Or=n["_emscripten_bind_LBSpaVec4_set_w_1"]=function(){return(Or=n["_emscripten_bind_LBSpaVec4_set_w_1"]=n["asm"]["qb"]).apply(null,arguments)},kr=n["_emscripten_bind_LBSpaVec4___destroy___0"]=function(){return(kr=n["_emscripten_bind_LBSpaVec4___destroy___0"]=n["asm"]["rb"]).apply(null,arguments)},zr=n["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=function(){return(zr=n["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=n["asm"]["sb"]).apply(null,arguments)},Fr=n["_emscripten_bind_LBSpaMat_At_1"]=function(){return(Fr=n["_emscripten_bind_LBSpaMat_At_1"]=n["asm"]["tb"]).apply(null,arguments)},Wr=n["_emscripten_bind_LBSpaMat___destroy___0"]=function(){return(Wr=n["_emscripten_bind_LBSpaMat___destroy___0"]=n["asm"]["ub"]).apply(null,arguments)},Hr=n["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=function(){return(Hr=n["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=n["asm"]["vb"]).apply(null,arguments)},Ur=n["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=function(){return(Ur=n["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=n["asm"]["wb"]).apply(null,arguments)},Yr=n["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=function(){return(Yr=n["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=n["asm"]["xb"]).apply(null,arguments)},qr=n["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=function(){return(qr=n["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=n["asm"]["yb"]).apply(null,arguments)},Xr=n["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=function(){return(Xr=n["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=n["asm"]["zb"]).apply(null,arguments)},Jr=n["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=function(){return(Jr=n["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=n["asm"]["Ab"]).apply(null,arguments)},Zr=n["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=function(){return(Zr=n["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=n["asm"]["Bb"]).apply(null,arguments)},Kr=n["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=function(){return(Kr=n["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=n["asm"]["Cb"]).apply(null,arguments)},Qr=n["_emscripten_bind_LBSpaPrimitive___destroy___0"]=function(){return(Qr=n["_emscripten_bind_LBSpaPrimitive___destroy___0"]=n["asm"]["Db"]).apply(null,arguments)},$r=n["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=function(){return($r=n["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=n["asm"]["Eb"]).apply(null,arguments)},tn=n["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=function(){return(tn=n["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=n["asm"]["Fb"]).apply(null,arguments)},en=n["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=function(){return(en=n["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=n["asm"]["Gb"]).apply(null,arguments)},rn=n["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=function(){return(rn=n["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=n["asm"]["Hb"]).apply(null,arguments)},nn=n["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=function(){return(nn=n["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=n["asm"]["Ib"]).apply(null,arguments)},pn=n["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=function(){return(pn=n["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=n["asm"]["Jb"]).apply(null,arguments)},on=n["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=function(){return(on=n["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=n["asm"]["Kb"]).apply(null,arguments)},_n=n["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=function(){return(_n=n["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=n["asm"]["Lb"]).apply(null,arguments)},an=n["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=function(){return(an=n["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=n["asm"]["Mb"]).apply(null,arguments)},cn=n["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=function(){return(cn=n["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=n["asm"]["Nb"]).apply(null,arguments)},un=n["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=function(){return(un=n["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=n["asm"]["Ob"]).apply(null,arguments)},ln=n["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=function(){return(ln=n["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=n["asm"]["Pb"]).apply(null,arguments)},sn=n["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=function(){return(sn=n["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=n["asm"]["Qb"]).apply(null,arguments)},yn=n["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=function(){return(yn=n["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=n["asm"]["Rb"]).apply(null,arguments)},dn=n["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=function(){return(dn=n["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=n["asm"]["Sb"]).apply(null,arguments)},mn=n["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=function(){return(mn=n["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=n["asm"]["Tb"]).apply(null,arguments)},fn=n["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=function(){return(fn=n["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=n["asm"]["Ub"]).apply(null,arguments)},bn=n["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=function(){return(bn=n["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=n["asm"]["Vb"]).apply(null,arguments)},Sn=n["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=function(){return(Sn=n["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=n["asm"]["Wb"]).apply(null,arguments)},vn=n["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=function(){return(vn=n["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=n["asm"]["Xb"]).apply(null,arguments)},hn=n["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=function(){return(hn=n["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=n["asm"]["Yb"]).apply(null,arguments)},Bn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=function(){return(Bn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=n["asm"]["Zb"]).apply(null,arguments)},gn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=function(){return(gn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=n["asm"]["_b"]).apply(null,arguments)},Ln=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=function(){return(Ln=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=n["asm"]["$b"]).apply(null,arguments)},Pn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=function(){return(Pn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=n["asm"]["ac"]).apply(null,arguments)},In=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=function(){return(In=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=n["asm"]["bc"]).apply(null,arguments)},jn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=function(){return(jn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=n["asm"]["cc"]).apply(null,arguments)},Gn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=function(){return(Gn=n["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=n["asm"]["dc"]).apply(null,arguments)},xn=n["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=function(){return(xn=n["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=n["asm"]["ec"]).apply(null,arguments)},Cn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=function(){return(Cn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=n["asm"]["fc"]).apply(null,arguments)},Rn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=function(){return(Rn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=n["asm"]["gc"]).apply(null,arguments)},Mn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=function(){return(Mn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=n["asm"]["hc"]).apply(null,arguments)},Vn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=function(){return(Vn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=n["asm"]["ic"]).apply(null,arguments)},wn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=function(){return(wn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=n["asm"]["jc"]).apply(null,arguments)},Tn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=function(){return(Tn=n["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=n["asm"]["kc"]).apply(null,arguments)},Dn=n["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=function(){return(Dn=n["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=n["asm"]["lc"]).apply(null,arguments)},An=n["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=function(){return(An=n["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=n["asm"]["mc"]).apply(null,arguments)},En=n["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=function(){return(En=n["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=n["asm"]["nc"]).apply(null,arguments)},Nn=n["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=function(){return(Nn=n["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=n["asm"]["oc"]).apply(null,arguments)},On=n["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=function(){return(On=n["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=n["asm"]["pc"]).apply(null,arguments)},kn=n["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=function(){return(kn=n["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=n["asm"]["qc"]).apply(null,arguments)},zn=n["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=function(){return(zn=n["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=n["asm"]["rc"]).apply(null,arguments)},Fn=n["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=function(){return(Fn=n["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=n["asm"]["sc"]).apply(null,arguments)},Wn=n["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=function(){return(Wn=n["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=n["asm"]["tc"]).apply(null,arguments)},Hn=n["_emscripten_bind_LBSpaSelectResult___destroy___0"]=function(){return(Hn=n["_emscripten_bind_LBSpaSelectResult___destroy___0"]=n["asm"]["uc"]).apply(null,arguments)},Un=n["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=function(){return(Un=n["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=n["asm"]["vc"]).apply(null,arguments)},Yn=n["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=function(){return(Yn=n["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=n["asm"]["wc"]).apply(null,arguments)},qn=n["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=function(){return(qn=n["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=n["asm"]["xc"]).apply(null,arguments)},Xn=n["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=function(){return(Xn=n["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=n["asm"]["yc"]).apply(null,arguments)},Jn=n["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=function(){return(Jn=n["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=n["asm"]["zc"]).apply(null,arguments)},Zn=n["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=function(){return(Zn=n["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=n["asm"]["Ac"]).apply(null,arguments)},Kn=n["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=function(){return(Kn=n["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=n["asm"]["Bc"]).apply(null,arguments)},Qn=n["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=function(){return(Qn=n["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=n["asm"]["Cc"]).apply(null,arguments)},$n=n["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=function(){return($n=n["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=n["asm"]["Dc"]).apply(null,arguments)},tp=n["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=function(){return(tp=n["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=n["asm"]["Ec"]).apply(null,arguments)},ep=n["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=function(){return(ep=n["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=n["asm"]["Fc"]).apply(null,arguments)},rp=n["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=function(){return(rp=n["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=n["asm"]["Gc"]).apply(null,arguments)},np=n["_emscripten_bind_LBSpaMgr_Select_2"]=function(){return(np=n["_emscripten_bind_LBSpaMgr_Select_2"]=n["asm"]["Hc"]).apply(null,arguments)},pp=n["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=function(){return(pp=n["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=n["asm"]["Ic"]).apply(null,arguments)},ip=n["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=function(){return(ip=n["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=n["asm"]["Jc"]).apply(null,arguments)},op=n["_emscripten_bind_LBSpaMgr___destroy___0"]=function(){return(op=n["_emscripten_bind_LBSpaMgr___destroy___0"]=n["asm"]["Kc"]).apply(null,arguments)},_p=n["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=function(){return(_p=n["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=n["asm"]["Lc"]).apply(null,arguments)},ap=n["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=function(){return(ap=n["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=n["asm"]["Mc"]).apply(null,arguments)},cp=n["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=function(){return(cp=n["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=n["asm"]["Nc"]).apply(null,arguments)},up=n["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=function(){return(up=n["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=n["asm"]["Oc"]).apply(null,arguments)},lp=n["_emscripten_bind_LBSpaBoxMgr_Select_0"]=function(){return(lp=n["_emscripten_bind_LBSpaBoxMgr_Select_0"]=n["asm"]["Pc"]).apply(null,arguments)},sp=n["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=function(){return(sp=n["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=n["asm"]["Qc"]).apply(null,arguments)},yp=n["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=function(){return(yp=n["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=n["asm"]["Rc"]).apply(null,arguments)},dp=n["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=function(){return(dp=n["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=n["asm"]["Sc"]).apply(null,arguments)},mp=n["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=function(){return(mp=n["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=n["asm"]["Tc"]).apply(null,arguments)},fp=n["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=function(){return(fp=n["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=n["asm"]["Uc"]).apply(null,arguments)},bp=n["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=function(){return(bp=n["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=n["asm"]["Vc"]).apply(null,arguments)},Sp=n["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=function(){return(Sp=n["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=n["asm"]["Wc"]).apply(null,arguments)},vp=n["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=function(){return(vp=n["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=n["asm"]["Xc"]).apply(null,arguments)},hp=n["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=function(){return(hp=n["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=n["asm"]["Yc"]).apply(null,arguments)},Bp=n["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=function(){return(Bp=n["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=n["asm"]["Zc"]).apply(null,arguments)},gp=n["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=function(){return(gp=n["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=n["asm"]["_c"]).apply(null,arguments)},Lp=n["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=function(){return(Lp=n["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=n["asm"]["$c"]).apply(null,arguments)},Pp=n["_emscripten_bind_LBSpaTriangle_GetPt_1"]=function(){return(Pp=n["_emscripten_bind_LBSpaTriangle_GetPt_1"]=n["asm"]["ad"]).apply(null,arguments)},Ip=n["_emscripten_bind_LBSpaTriangle_GetUV_1"]=function(){return(Ip=n["_emscripten_bind_LBSpaTriangle_GetUV_1"]=n["asm"]["bd"]).apply(null,arguments)},jp=n["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=function(){return(jp=n["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=n["asm"]["cd"]).apply(null,arguments)},Gp=n["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=function(){return(Gp=n["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=n["asm"]["dd"]).apply(null,arguments)},xp=n["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=function(){return(xp=n["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=n["asm"]["ed"]).apply(null,arguments)},Cp=n["_emscripten_bind_LBSpaTriangle___destroy___0"]=function(){return(Cp=n["_emscripten_bind_LBSpaTriangle___destroy___0"]=n["asm"]["fd"]).apply(null,arguments)},Rp=n["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=function(){return(Rp=n["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=n["asm"]["gd"]).apply(null,arguments)},Mp=n["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=function(){return(Mp=n["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=n["asm"]["hd"]).apply(null,arguments)},Vp=n["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=function(){return(Vp=n["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=n["asm"]["id"]).apply(null,arguments)},wp=n["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=function(){return(wp=n["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=n["asm"]["jd"]).apply(null,arguments)},Tp=n["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=function(){return(Tp=n["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=n["asm"]["kd"]).apply(null,arguments)},Dp=n["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=function(){return(Dp=n["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=n["asm"]["ld"]).apply(null,arguments)},Ap=n["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=function(){return(Ap=n["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=n["asm"]["md"]).apply(null,arguments)},Ep=n["_emscripten_bind_LBSpaSerial___destroy___0"]=function(){return(Ep=n["_emscripten_bind_LBSpaSerial___destroy___0"]=n["asm"]["nd"]).apply(null,arguments)},Np=n["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=function(){return(Np=n["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=n["asm"]["od"]).apply(null,arguments)},Op=n["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=function(){return(Op=n["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=n["asm"]["pd"]).apply(null,arguments)},kp=n["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=function(){return(kp=n["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=n["asm"]["qd"]).apply(null,arguments)},zp=n["_emscripten_bind_LBSpaGeoTool___destroy___0"]=function(){return(zp=n["_emscripten_bind_LBSpaGeoTool___destroy___0"]=n["asm"]["rd"]).apply(null,arguments)},Fp=n["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=function(){return(Fp=n["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=n["asm"]["sd"]).apply(null,arguments)},Wp=n["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=function(){return(Wp=n["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=n["asm"]["td"]).apply(null,arguments)},Hp=n["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=function(){return(Hp=n["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=n["asm"]["ud"]).apply(null,arguments)},Up=n["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=function(){return(Up=n["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=n["asm"]["vd"]).apply(null,arguments)},Yp=n["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=function(){return(Yp=n["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=n["asm"]["wd"]).apply(null,arguments)},qp=n["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=function(){return(qp=n["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=n["asm"]["xd"]).apply(null,arguments)},Xp=n["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=function(){return(Xp=n["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=n["asm"]["yd"]).apply(null,arguments)},Jp=n["_emscripten_bind_LBSpaBody_Init_2"]=function(){return(Jp=n["_emscripten_bind_LBSpaBody_Init_2"]=n["asm"]["zd"]).apply(null,arguments)},Zp=n["_emscripten_bind_LBSpaBody_GetTriangle_2"]=function(){return(Zp=n["_emscripten_bind_LBSpaBody_GetTriangle_2"]=n["asm"]["Ad"]).apply(null,arguments)},Kp=n["_emscripten_bind_LBSpaBody_CheckReference_1"]=function(){return(Kp=n["_emscripten_bind_LBSpaBody_CheckReference_1"]=n["asm"]["Bd"]).apply(null,arguments)},Qp=n["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=function(){return(Qp=n["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=n["asm"]["Cd"]).apply(null,arguments)},$p=n["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=function(){return($p=n["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=n["asm"]["Dd"]).apply(null,arguments)},ti=n["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=function(){return(ti=n["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=n["asm"]["Ed"]).apply(null,arguments)},ei=n["_emscripten_bind_LBSpaBody_GetVolume_0"]=function(){return(ei=n["_emscripten_bind_LBSpaBody_GetVolume_0"]=n["asm"]["Fd"]).apply(null,arguments)},ri=n["_emscripten_bind_LBSpaBody___destroy___0"]=function(){return(ri=n["_emscripten_bind_LBSpaBody___destroy___0"]=n["asm"]["Gd"]).apply(null,arguments)},ni=n["_free"]=function(){return(ni=n["_free"]=n["asm"]["Hd"]).apply(null,arguments)},pi=n["_malloc"]=function(){return(pi=n["_malloc"]=n["asm"]["Id"]).apply(null,arguments)},ii=n["___errno_location"]=function(){return(ii=n["___errno_location"]=n["asm"]["Jd"]).apply(null,arguments)},oi=n["_setThrew"]=function(){return(oi=n["_setThrew"]=n["asm"]["Kd"]).apply(null,arguments)},_i=n["__ZSt18uncaught_exceptionv"]=function(){return(_i=n["__ZSt18uncaught_exceptionv"]=n["asm"]["Ld"]).apply(null,arguments)},ai=n["___cxa_can_catch"]=function(){return(ai=n["___cxa_can_catch"]=n["asm"]["Md"]).apply(null,arguments)},ci=n["___cxa_is_pointer_type"]=function(){return(ci=n["___cxa_is_pointer_type"]=n["asm"]["Nd"]).apply(null,arguments)},ui=n["dynCall_v"]=function(){return(ui=n["dynCall_v"]=n["asm"]["Od"]).apply(null,arguments)},li=n["dynCall_vi"]=function(){return(li=n["dynCall_vi"]=n["asm"]["Pd"]).apply(null,arguments)},si=n["dynCall_vii"]=function(){return(si=n["dynCall_vii"]=n["asm"]["Qd"]).apply(null,arguments)},yi=n["dynCall_viii"]=function(){return(yi=n["dynCall_viii"]=n["asm"]["Rd"]).apply(null,arguments)},di=n["dynCall_viiii"]=function(){return(di=n["dynCall_viiii"]=n["asm"]["Sd"]).apply(null,arguments)},mi=n["dynCall_viiiii"]=function(){return(mi=n["dynCall_viiiii"]=n["asm"]["Td"]).apply(null,arguments)},fi=n["dynCall_viiiiiii"]=function(){return(fi=n["dynCall_viiiiiii"]=n["asm"]["Ud"]).apply(null,arguments)},bi=n["dynCall_viiiiiiiiii"]=function(){return(bi=n["dynCall_viiiiiiiiii"]=n["asm"]["Vd"]).apply(null,arguments)},Si=n["dynCall_viiiiiiiiiii"]=function(){return(Si=n["dynCall_viiiiiiiiiii"]=n["asm"]["Wd"]).apply(null,arguments)},vi=n["dynCall_viiiiiiiiiiiiiii"]=function(){return(vi=n["dynCall_viiiiiiiiiiiiiii"]=n["asm"]["Xd"]).apply(null,arguments)},hi=n["dynCall_viidd"]=function(){return(hi=n["dynCall_viidd"]=n["asm"]["Yd"]).apply(null,arguments)},Bi=n["dynCall_i"]=function(){return(Bi=n["dynCall_i"]=n["asm"]["Zd"]).apply(null,arguments)},gi=n["dynCall_ii"]=function(){return(gi=n["dynCall_ii"]=n["asm"]["_d"]).apply(null,arguments)},Li=n["dynCall_iii"]=function(){return(Li=n["dynCall_iii"]=n["asm"]["$d"]).apply(null,arguments)},Pi=n["dynCall_iiii"]=function(){return(Pi=n["dynCall_iiii"]=n["asm"]["ae"]).apply(null,arguments)},Ii=n["dynCall_iiiii"]=function(){return(Ii=n["dynCall_iiiii"]=n["asm"]["be"]).apply(null,arguments)},ji=n["dynCall_iiiiii"]=function(){return(ji=n["dynCall_iiiiii"]=n["asm"]["ce"]).apply(null,arguments)},Gi=n["dynCall_iiiiiii"]=function(){return(Gi=n["dynCall_iiiiiii"]=n["asm"]["de"]).apply(null,arguments)},xi=n["dynCall_iiiiiiii"]=function(){return(xi=n["dynCall_iiiiiiii"]=n["asm"]["ee"]).apply(null,arguments)},Ci=n["dynCall_iiiiiiiii"]=function(){return(Ci=n["dynCall_iiiiiiiii"]=n["asm"]["fe"]).apply(null,arguments)},Ri=n["dynCall_iiiiiiiiiiii"]=function(){return(Ri=n["dynCall_iiiiiiiiiiii"]=n["asm"]["ge"]).apply(null,arguments)},Mi=n["dynCall_iiiiid"]=function(){return(Mi=n["dynCall_iiiiid"]=n["asm"]["he"]).apply(null,arguments)},Vi=n["dynCall_iiiidiii"]=function(){return(Vi=n["dynCall_iiiidiii"]=n["asm"]["ie"]).apply(null,arguments)},wi=n["dynCall_iidd"]=function(){return(wi=n["dynCall_iidd"]=n["asm"]["je"]).apply(null,arguments)},Ti=n["dynCall_diii"]=function(){return(Ti=n["dynCall_diii"]=n["asm"]["ke"]).apply(null,arguments)},Di=n["stackSave"]=function(){return(Di=n["stackSave"]=n["asm"]["le"]).apply(null,arguments)},Ai=n["stackAlloc"]=function(){return(Ai=n["stackAlloc"]=n["asm"]["me"]).apply(null,arguments)},Ei=n["stackRestore"]=function(){return(Ei=n["stackRestore"]=n["asm"]["ne"]).apply(null,arguments)};function Ni(t,e,r){var n=Di();try{return Li(t,e,r)}catch(p){if(Ei(n),p!==p+0&&"longjmp"!==p)throw p;oi(1,0)}}function Oi(t){var e=Di();try{ui(t)}catch(r){if(Ei(e),r!==r+0&&"longjmp"!==r)throw r;oi(1,0)}}function ki(t,e){var r=Di();try{li(t,e)}catch(n){if(Ei(r),n!==n+0&&"longjmp"!==n)throw n;oi(1,0)}}function zi(t,e){var r=Di();try{return gi(t,e)}catch(n){if(Ei(r),n!==n+0&&"longjmp"!==n)throw n;oi(1,0)}}function Fi(t,e,r){var n=Di();try{si(t,e,r)}catch(p){if(Ei(n),p!==p+0&&"longjmp"!==p)throw p;oi(1,0)}}function Wi(t,e,r,n){var p=Di();try{return Pi(t,e,r,n)}catch(i){if(Ei(p),i!==i+0&&"longjmp"!==i)throw i;oi(1,0)}}function Hi(t,e,r,n){var p=Di();try{yi(t,e,r,n)}catch(i){if(Ei(p),i!==i+0&&"longjmp"!==i)throw i;oi(1,0)}}function Ui(t,e,r,n,p){var i=Di();try{di(t,e,r,n,p)}catch(o){if(Ei(i),o!==o+0&&"longjmp"!==o)throw o;oi(1,0)}}function Yi(t,e,r,n,p){var i=Di();try{return Ii(t,e,r,n,p)}catch(o){if(Ei(i),o!==o+0&&"longjmp"!==o)throw o;oi(1,0)}}function qi(t,e,r,n,p,i){var o=Di();try{return ji(t,e,r,n,p,i)}catch(_){if(Ei(o),_!==_+0&&"longjmp"!==_)throw _;oi(1,0)}}function Xi(t,e,r,n,p,i,o){var _=Di();try{return Gi(t,e,r,n,p,i,o)}catch(a){if(Ei(_),a!==a+0&&"longjmp"!==a)throw a;oi(1,0)}}function Ji(t,e,r,n,p,i){var o=Di();try{mi(t,e,r,n,p,i)}catch(_){if(Ei(o),_!==_+0&&"longjmp"!==_)throw _;oi(1,0)}}function Zi(t,e,r,n,p,i,o,_){var a=Di();try{return Vi(t,e,r,n,p,i,o,_)}catch(c){if(Ei(a),c!==c+0&&"longjmp"!==c)throw c;oi(1,0)}}function Ki(t,e,r,n,p,i,o,_,a){var c=Di();try{return Ci(t,e,r,n,p,i,o,_,a)}catch(u){if(Ei(c),u!==u+0&&"longjmp"!==u)throw u;oi(1,0)}}function Qi(t,e,r,n){var p=Di();try{return Ti(t,e,r,n)}catch(i){if(Ei(p),i!==i+0&&"longjmp"!==i)throw i;oi(1,0)}}function $i(t,e,r,n){var p=Di();try{return wi(t,e,r,n)}catch(i){if(Ei(p),i!==i+0&&"longjmp"!==i)throw i;oi(1,0)}}function to(t,e,r,n,p,i,o,_,a,c,u,l){var s=Di();try{Si(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Ei(s),y!==y+0&&"longjmp"!==y)throw y;oi(1,0)}}function eo(t,e,r,n,p){var i=Di();try{hi(t,e,r,n,p)}catch(o){if(Ei(i),o!==o+0&&"longjmp"!==o)throw o;oi(1,0)}}function ro(t,e,r,n,p,i){var o=Di();try{return Mi(t,e,r,n,p,i)}catch(_){if(Ei(o),_!==_+0&&"longjmp"!==_)throw _;oi(1,0)}}function no(t,e,r,n,p,i,o,_){var a=Di();try{return xi(t,e,r,n,p,i,o,_)}catch(c){if(Ei(a),c!==c+0&&"longjmp"!==c)throw c;oi(1,0)}}function po(t,e,r,n,p,i,o,_){var a=Di();try{fi(t,e,r,n,p,i,o,_)}catch(c){if(Ei(a),c!==c+0&&"longjmp"!==c)throw c;oi(1,0)}}function io(t,e,r,n,p,i,o,_,a,c,u,l){var s=Di();try{return Ri(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Ei(s),y!==y+0&&"longjmp"!==y)throw y;oi(1,0)}}function oo(t,e,r,n,p,i,o,_,a,c,u){var l=Di();try{bi(t,e,r,n,p,i,o,_,a,c,u)}catch(s){if(Ei(l),s!==s+0&&"longjmp"!==s)throw s;oi(1,0)}}function _o(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m){var f=Di();try{vi(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m)}catch(b){if(Ei(f),b!==b+0&&"longjmp"!==b)throw b;oi(1,0)}}function ao(t){var e=Di();try{return Bi(t)}catch(r){if(Ei(e),r!==r+0&&"longjmp"!==r)throw r;oi(1,0)}}function co(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function uo(t){function e(){he||(he=!0,n["calledRun"]=!0,C||(ot(),_t(),r(n),n["onRuntimeInitialized"]&&n["onRuntimeInitialized"](),at()))}lt>0||(it(),lt>0||(n["setStatus"]?(n["setStatus"]("Running..."),setTimeout((function(){setTimeout((function(){n["setStatus"]("")}),1),e()}),1)):e()))}function lo(t,e){e&&L&&0===t||(L||(C=!0,n["onExit"]&&n["onExit"](t)),c(t,new co(t)))}if(n["asm"]=ve,n["ccall"]=V,n["cwrap"]=w,st=function t(){he||uo(),he||(st=t)},n["run"]=uo,n["preInit"]){"function"==typeof n["preInit"]&&(n["preInit"]=[n["preInit"]]);while(n["preInit"].length>0)n["preInit"].pop()()}function so(){}function yo(t){return(t||so).__cache__}function mo(t,e){var r=yo(e),n=r[t];return n||(n=Object.create((e||so).prototype),n.ptr=t,r[t]=n)}function fo(t,e){return mo(t.ptr,e)}function bo(t){if(!t["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";t["__destroy__"](),delete yo(t.__class__)[t.ptr]}function So(t,e){return t.ptr===e.ptr}function vo(t){return t.ptr}function ho(t){return t.__class__}L=!0,uo(),so.prototype=Object.create(so.prototype),so.prototype.constructor=so,so.prototype.__class__=so,so.__cache__={},n["WrapperObject"]=so,n["getCache"]=yo,n["wrapPointer"]=mo,n["castObject"]=fo,n["NULL"]=mo(0),n["destroy"]=bo,n["compare"]=So,n["getPointer"]=vo,n["getClass"]=ho;var Bo={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(Bo.needed){for(var t=0;t<Bo.temps.length;t++)n["_free"](Bo.temps[t]);Bo.temps.length=0,n["_free"](Bo.buffer),Bo.buffer=0,Bo.size+=Bo.needed,Bo.needed=0}Bo.buffer||(Bo.size+=128,Bo.buffer=n["_malloc"](Bo.size),R(Bo.buffer)),Bo.pos=0},alloc:function(t,e){R(Bo.buffer);var r,p=e.BYTES_PER_ELEMENT,i=t.length*p;return i=i+7&-8,Bo.pos+i>=Bo.size?(R(i>0),Bo.needed+=i,r=n["_malloc"](i),Bo.temps.push(r)):(r=Bo.buffer+Bo.pos,Bo.pos+=i),r},copy:function(t,e,r){r>>>=0;var n=e.BYTES_PER_ELEMENT;switch(n){case 2:r>>>=1;break;case 4:r>>>=2;break;case 8:r>>>=3;break}for(var p=0;p<t.length;p++)e[r+p]=t[p]}};function go(t){if("string"===typeof t){var e=be(t),r=Bo.alloc(e,W);return Bo.copy(e,W,r),r}return t}function Lo(t){if("object"===typeof t){var e=Bo.alloc(t,W);return Bo.copy(t,W,e),e}return t}function Po(t){if("object"===typeof t){var e=Bo.alloc(t,U);return Bo.copy(t,U,e),e}return t}function Io(t){if("object"===typeof t){var e=Bo.alloc(t,Y);return Bo.copy(t,Y,e),e}return t}function jo(t){if("object"===typeof t){var e=Bo.alloc(t,q);return Bo.copy(t,q,e),e}return t}function Go(){throw"cannot construct a VoidPtr, no constructor in IDL"}function xo(){this.ptr=Le(),yo(xo)[this.ptr]=this}function Co(){this.ptr=xe(),yo(Co)[this.ptr]=this}function Ro(){this.ptr=De(),yo(Ro)[this.ptr]=this}function Mo(){this.ptr=ze(),yo(Mo)[this.ptr]=this}function Vo(){this.ptr=Xe(),yo(Vo)[this.ptr]=this}function wo(){this.ptr=br(),yo(wo)[this.ptr]=this}function To(){this.ptr=Ir(),yo(To)[this.ptr]=this}function Do(){this.ptr=Mr(),yo(Do)[this.ptr]=this}function Ao(){this.ptr=zr(),yo(Ao)[this.ptr]=this}function Eo(){this.ptr=Hr(),yo(Eo)[this.ptr]=this}function No(){throw"cannot construct a LBSpaPrimitiveSpatial, no constructor in IDL"}function Oo(){throw"cannot construct a LBSpaPrimitiveCluster, no constructor in IDL"}function ko(){this.ptr=on(),yo(ko)[this.ptr]=this}function zo(){throw"cannot construct a LBSpaSelectResultId, no constructor in IDL"}function Fo(){throw"cannot construct a LBSpaSelectTriResultItem, no constructor in IDL"}function Wo(){throw"cannot construct a LBSpaSelectSegResultItem, no constructor in IDL"}function Ho(){this.ptr=An(),yo(Ho)[this.ptr]=this}function Uo(){this.ptr=Un(),yo(Uo)[this.ptr]=this}function Yo(){this.ptr=_p(),yo(Yo)[this.ptr]=this}function qo(){this.ptr=mp(),yo(qo)[this.ptr]=this}function Xo(){this.ptr=Rp(),yo(Xo)[this.ptr]=this}function Jo(){this.ptr=Np(),yo(Jo)[this.ptr]=this}function Zo(){this.ptr=Fp(),yo(Zo)[this.ptr]=this}function Ko(){this.ptr=Xp(),yo(Ko)[this.ptr]=this}return Go.prototype=Object.create(so.prototype),Go.prototype.constructor=Go,Go.prototype.__class__=Go,Go.__cache__={},n["VoidPtr"]=Go,Go.prototype["__destroy__"]=Go.prototype.__destroy__=function(){var t=this.ptr;ge(t)},xo.prototype=Object.create(so.prototype),xo.prototype.constructor=xo,xo.prototype.__class__=xo,xo.__cache__={},n["LBProj4Wrapper"]=xo,xo.prototype["Init"]=xo.prototype.Init=function(t,e){var r=this.ptr;return Bo.prepare(),t=t&&"object"===typeof t?t.ptr:go(t),e=e&&"object"===typeof e?e.ptr:go(e),!!Pe(r,t,e)},xo.prototype["TranformAry"]=xo.prototype.TranformAry=function(t,e){var r=this.ptr;Bo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),Ie(r,t,e)},xo.prototype["InverseTranformAry"]=xo.prototype.InverseTranformAry=function(t,e){var r=this.ptr;Bo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),je(r,t,e)},xo.prototype["__destroy__"]=xo.prototype.__destroy__=function(){var t=this.ptr;Ge(t)},Co.prototype=Object.create(so.prototype),Co.prototype.constructor=Co,Co.prototype.__class__=Co,Co.__cache__={},n["LBEdgeFormer"]=Co,Co.prototype["SetPtAry"]=Co.prototype.SetPtAry=function(t,e){var r=this.ptr;Bo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),Ce(r,t,e)},Co.prototype["SetIndexAry"]=Co.prototype.SetIndexAry=function(t,e){var r=this.ptr;Bo.prepare(),"object"==typeof t&&(t=Po(t)),e&&"object"===typeof e&&(e=e.ptr),Re(r,t,e)},Co.prototype["FormEdge"]=Co.prototype.FormEdge=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Me(e,t)},Co.prototype["GetEdgeIndexArySize"]=Co.prototype.GetEdgeIndexArySize=function(){var t=this.ptr;return Ve(t)},Co.prototype["GetEdgeIndexAryVal"]=Co.prototype.GetEdgeIndexAryVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),we(e,t)},Co.prototype["__destroy__"]=Co.prototype.__destroy__=function(){var t=this.ptr;Te(t)},Ro.prototype=Object.create(so.prototype),Ro.prototype.constructor=Ro,Ro.prototype.__class__=Ro,Ro.__cache__={},n["LBPlanishAry"]=Ro,Ro.prototype["SetPlanishNum"]=Ro.prototype.SetPlanishNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Ae(e,t)},Ro.prototype["SetPlanishPtNum"]=Ro.prototype.SetPlanishPtNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ee(r,t,e)},Ro.prototype["SetPlanishPtVal"]=Ro.prototype.SetPlanishPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),Ne(p,t,e,r,n)},Ro.prototype["SetPlanishBot"]=Ro.prototype.SetPlanishBot=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Oe(r,t,e)},Ro.prototype["__destroy__"]=Ro.prototype.__destroy__=function(){var t=this.ptr;ke(t)},Mo.prototype=Object.create(so.prototype),Mo.prototype.constructor=Mo,Mo.prototype.__class__=Mo,Mo.__cache__={},n["LBDeal"]=Mo,Mo.prototype["Init"]=Mo.prototype.Init=function(t,e,r,n){var p=this.ptr;return Bo.prepare(),t=t&&"object"===typeof t?t.ptr:go(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),!!Fe(p,t,e,r,n)},Mo.prototype["ComputeProjToCartesian"]=Mo.prototype.ComputeProjToCartesian=function(t,e,r,n){var p=this.ptr;Bo.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=jo(r)),n&&"object"===typeof n&&(n=n.ptr),We(p,t,e,r,n)},Mo.prototype["ComputeCartesianToProj"]=Mo.prototype.ComputeCartesianToProj=function(t,e,r,n,p){var i=this.ptr;Bo.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=jo(r)),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),He(i,t,e,r,n,p)},Mo.prototype["TranformDegreeToProj"]=Mo.prototype.TranformDegreeToProj=function(t,e,r){var n=this.ptr;Bo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Ue(n,t,e,r)},Mo.prototype["TranformProjToDegree"]=Mo.prototype.TranformProjToDegree=function(t,e,r){var n=this.ptr;Bo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Ye(n,t,e,r)},Mo.prototype["__destroy__"]=Mo.prototype.__destroy__=function(){var t=this.ptr;qe(t)},Vo.prototype=Object.create(so.prototype),Vo.prototype.constructor=Vo,Vo.prototype.__class__=Vo,Vo.__cache__={},n["MaterPrimitiveDecoder"]=Vo,Vo.prototype["Decode"]=Vo.prototype.Decode=function(t,e){var r=this.ptr;return Bo.prepare(),"object"==typeof t&&(t=Lo(t)),e&&"object"===typeof e&&(e=e.ptr),!!Je(r,t,e)},Vo.prototype["GetPtNum"]=Vo.prototype.GetPtNum=function(){var t=this.ptr;return Ze(t)},Vo.prototype["GetPtVal"]=Vo.prototype.GetPtVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ke(r,t,e)},Vo.prototype["IsHaveUV"]=Vo.prototype.IsHaveUV=function(){var t=this.ptr;return!!Qe(t)},Vo.prototype["GetUVVal"]=Vo.prototype.GetUVVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),$e(r,t,e)},Vo.prototype["IsHaveNormal"]=Vo.prototype.IsHaveNormal=function(){var t=this.ptr;return!!tr(t)},Vo.prototype["GetNormalVal"]=Vo.prototype.GetNormalVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),er(r,t,e)},Vo.prototype["IsHaveBatchId"]=Vo.prototype.IsHaveBatchId=function(){var t=this.ptr;return!!rr(t)},Vo.prototype["GetBatchId"]=Vo.prototype.GetBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),nr(e,t)},Vo.prototype["IsHaveMaterialId"]=Vo.prototype.IsHaveMaterialId=function(){var t=this.ptr;return!!pr(t)},Vo.prototype["GetMaterialId"]=Vo.prototype.GetMaterialId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),ir(e,t)},Vo.prototype["IsHaveOutlineCoord"]=Vo.prototype.IsHaveOutlineCoord=function(){var t=this.ptr;return!!or(t)},Vo.prototype["GetOutlineCoord"]=Vo.prototype.GetOutlineCoord=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),_r(e,t)},Vo.prototype["GetIndexNum"]=Vo.prototype.GetIndexNum=function(){var t=this.ptr;return ar(t)},Vo.prototype["GetIndex"]=Vo.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),cr(e,t)},Vo.prototype["IsHaveEdgeCheck"]=Vo.prototype.IsHaveEdgeCheck=function(){var t=this.ptr;return!!ur(t)},Vo.prototype["GetEdgeCheck"]=Vo.prototype.GetEdgeCheck=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),lr(e,t)},Vo.prototype["GetInstanceNum"]=Vo.prototype.GetInstanceNum=function(){var t=this.ptr;return sr(t)},Vo.prototype["GetInstanceMatVal"]=Vo.prototype.GetInstanceMatVal=function(t,e,r){var n=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),yr(n,t,e,r)},Vo.prototype["GetInstanceBatchNum"]=Vo.prototype.GetInstanceBatchNum=function(){var t=this.ptr;return dr(t)},Vo.prototype["GetInstanceBatchId"]=Vo.prototype.GetInstanceBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mr(e,t)},Vo.prototype["__destroy__"]=Vo.prototype.__destroy__=function(){var t=this.ptr;fr(t)},wo.prototype=Object.create(so.prototype),wo.prototype.constructor=wo,wo.prototype.__class__=wo,wo.__cache__={},n["LBSpaVec"]=wo,wo.prototype["get_x"]=wo.prototype.get_x=function(){var t=this.ptr;return Sr(t)},wo.prototype["set_x"]=wo.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),vr(e,t)},Object.defineProperty(wo.prototype,"x",{get:wo.prototype.get_x,set:wo.prototype.set_x}),wo.prototype["get_y"]=wo.prototype.get_y=function(){var t=this.ptr;return hr(t)},wo.prototype["set_y"]=wo.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Br(e,t)},Object.defineProperty(wo.prototype,"y",{get:wo.prototype.get_y,set:wo.prototype.set_y}),wo.prototype["get_z"]=wo.prototype.get_z=function(){var t=this.ptr;return gr(t)},wo.prototype["set_z"]=wo.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Lr(e,t)},Object.defineProperty(wo.prototype,"z",{get:wo.prototype.get_z,set:wo.prototype.set_z}),wo.prototype["__destroy__"]=wo.prototype.__destroy__=function(){var t=this.ptr;Pr(t)},To.prototype=Object.create(so.prototype),To.prototype.constructor=To,To.prototype.__class__=To,To.__cache__={},n["LBSpaVec2"]=To,To.prototype["get_x"]=To.prototype.get_x=function(){var t=this.ptr;return jr(t)},To.prototype["set_x"]=To.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Gr(e,t)},Object.defineProperty(To.prototype,"x",{get:To.prototype.get_x,set:To.prototype.set_x}),To.prototype["get_y"]=To.prototype.get_y=function(){var t=this.ptr;return xr(t)},To.prototype["set_y"]=To.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Cr(e,t)},Object.defineProperty(To.prototype,"y",{get:To.prototype.get_y,set:To.prototype.set_y}),To.prototype["__destroy__"]=To.prototype.__destroy__=function(){var t=this.ptr;Rr(t)},Do.prototype=Object.create(so.prototype),Do.prototype.constructor=Do,Do.prototype.__class__=Do,Do.__cache__={},n["LBSpaVec4"]=Do,Do.prototype["get_x"]=Do.prototype.get_x=function(){var t=this.ptr;return Vr(t)},Do.prototype["set_x"]=Do.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),wr(e,t)},Object.defineProperty(Do.prototype,"x",{get:Do.prototype.get_x,set:Do.prototype.set_x}),Do.prototype["get_y"]=Do.prototype.get_y=function(){var t=this.ptr;return Tr(t)},Do.prototype["set_y"]=Do.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Dr(e,t)},Object.defineProperty(Do.prototype,"y",{get:Do.prototype.get_y,set:Do.prototype.set_y}),Do.prototype["get_z"]=Do.prototype.get_z=function(){var t=this.ptr;return Ar(t)},Do.prototype["set_z"]=Do.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Er(e,t)},Object.defineProperty(Do.prototype,"z",{get:Do.prototype.get_z,set:Do.prototype.set_z}),Do.prototype["get_w"]=Do.prototype.get_w=function(){var t=this.ptr;return Nr(t)},Do.prototype["set_w"]=Do.prototype.set_w=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Or(e,t)},Object.defineProperty(Do.prototype,"w",{get:Do.prototype.get_w,set:Do.prototype.set_w}),Do.prototype["__destroy__"]=Do.prototype.__destroy__=function(){var t=this.ptr;kr(t)},Ao.prototype=Object.create(so.prototype),Ao.prototype.constructor=Ao,Ao.prototype.__class__=Ao,Ao.__cache__={},n["LBSpaMat"]=Ao,Ao.prototype["At"]=Ao.prototype.At=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Fr(e,t),Do)},Ao.prototype["__destroy__"]=Ao.prototype.__destroy__=function(){var t=this.ptr;Wr(t)},Eo.prototype=Object.create(so.prototype),Eo.prototype.constructor=Eo,Eo.prototype.__class__=Eo,Eo.__cache__={},n["LBSpaPrimitive"]=Eo,Eo.prototype["SetPtValNum"]=Eo.prototype.SetPtValNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ur(r,t,e)},Eo.prototype["SetPtValVal"]=Eo.prototype.SetPtValVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Yr(r,t,e)},Eo.prototype["SetBatchIdVal"]=Eo.prototype.SetBatchIdVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),qr(r,t,e)},Eo.prototype["SetIndexNum"]=Eo.prototype.SetIndexNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Xr(r,t,e)},Eo.prototype["SetIndexVal"]=Eo.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Jr(r,t,e)},Eo.prototype["SetEdgeCheckVal"]=Eo.prototype.SetEdgeCheckVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Zr(r,t,e)},Eo.prototype["InitIndexByPt"]=Eo.prototype.InitIndexByPt=function(){var t=this.ptr;Kr(t)},Eo.prototype["__destroy__"]=Eo.prototype.__destroy__=function(){var t=this.ptr;Qr(t)},No.prototype=Object.create(so.prototype),No.prototype.constructor=No,No.prototype.__class__=No,No.__cache__={},n["LBSpaPrimitiveSpatial"]=No,No.prototype["__destroy__"]=No.prototype.__destroy__=function(){var t=this.ptr;$r(t)},Oo.prototype=Object.create(so.prototype),Oo.prototype.constructor=Oo,Oo.prototype.__class__=Oo,Oo.__cache__={},n["LBSpaPrimitiveCluster"]=Oo,Oo.prototype["SetIndexMatrix"]=Oo.prototype.SetIndexMatrix=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),tn(r,t,e)},Oo.prototype["RemoveAllMatrix"]=Oo.prototype.RemoveAllMatrix=function(){var t=this.ptr;en(t)},Oo.prototype["EnableIndexSelected"]=Oo.prototype.EnableIndexSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),rn(r,t,e)},Oo.prototype["EnableAllIndexSelected"]=Oo.prototype.EnableAllIndexSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),nn(e,t)},Oo.prototype["__destroy__"]=Oo.prototype.__destroy__=function(){var t=this.ptr;pn(t)},ko.prototype=Object.create(so.prototype),ko.prototype.constructor=ko,ko.prototype.__class__=ko,ko.__cache__={},n["LBSpaSelectCondition"]=ko,ko.prototype["SetBox"]=ko.prototype.SetBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),_n(o,t,e,r,n,p,i)},ko.prototype["SetRay"]=ko.prototype.SetRay=function(t,e,r,n,p,i,o,_,a){var c=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),an(c,t,e,r,n,p,i,o,_,a)},ko.prototype["SetWedge"]=ko.prototype.SetWedge=function(t,e,r,n,p,i,o,_,a,c){var u=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),c&&"object"===typeof c&&(c=c.ptr),cn(u,t,e,r,n,p,i,o,_,a,c)},ko.prototype["SetWedgeByBufferedPoints"]=ko.prototype.SetWedgeByBufferedPoints=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),un(n,t,e,r)},ko.prototype["ClearBuffer"]=ko.prototype.ClearBuffer=function(){var t=this.ptr;ln(t)},ko.prototype["AddBuffer"]=ko.prototype.AddBuffer=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),sn(n,t,e,r)},ko.prototype["__destroy__"]=ko.prototype.__destroy__=function(){var t=this.ptr;yn(t)},zo.prototype=Object.create(so.prototype),zo.prototype.constructor=zo,zo.prototype.__class__=zo,zo.__cache__={},n["LBSpaSelectResultId"]=zo,zo.prototype["get_iPrimitiveId"]=zo.prototype.get_iPrimitiveId=function(){var t=this.ptr;return dn(t)},zo.prototype["set_iPrimitiveId"]=zo.prototype.set_iPrimitiveId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),mn(e,t)},Object.defineProperty(zo.prototype,"iPrimitiveId",{get:zo.prototype.get_iPrimitiveId,set:zo.prototype.set_iPrimitiveId}),zo.prototype["get_bCluster"]=zo.prototype.get_bCluster=function(){var t=this.ptr;return!!fn(t)},zo.prototype["set_bCluster"]=zo.prototype.set_bCluster=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),bn(e,t)},Object.defineProperty(zo.prototype,"bCluster",{get:zo.prototype.get_bCluster,set:zo.prototype.set_bCluster}),zo.prototype["get_iBatchId"]=zo.prototype.get_iBatchId=function(){var t=this.ptr;return Sn(t)},zo.prototype["set_iBatchId"]=zo.prototype.set_iBatchId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),vn(e,t)},Object.defineProperty(zo.prototype,"iBatchId",{get:zo.prototype.get_iBatchId,set:zo.prototype.set_iBatchId}),zo.prototype["__destroy__"]=zo.prototype.__destroy__=function(){var t=this.ptr;hn(t)},Fo.prototype=Object.create(so.prototype),Fo.prototype.constructor=Fo,Fo.prototype.__class__=Fo,Fo.__cache__={},n["LBSpaSelectTriResultItem"]=Fo,Fo.prototype["GetResultId"]=Fo.prototype.GetResultId=function(){var t=this.ptr;return mo(Bn(t),zo)},Fo.prototype["GetPt0"]=Fo.prototype.GetPt0=function(){var t=this.ptr;return mo(gn(t),wo)},Fo.prototype["GetPt1"]=Fo.prototype.GetPt1=function(){var t=this.ptr;return mo(Ln(t),wo)},Fo.prototype["GetPt2"]=Fo.prototype.GetPt2=function(){var t=this.ptr;return mo(Pn(t),wo)},Fo.prototype["GetPickPt"]=Fo.prototype.GetPickPt=function(){var t=this.ptr;return mo(In(t),wo)},Fo.prototype["GetPickNormal"]=Fo.prototype.GetPickNormal=function(){var t=this.ptr;return mo(jn(t),wo)},Fo.prototype["GetPickDist"]=Fo.prototype.GetPickDist=function(){var t=this.ptr;return Gn(t)},Fo.prototype["__destroy__"]=Fo.prototype.__destroy__=function(){var t=this.ptr;xn(t)},Wo.prototype=Object.create(so.prototype),Wo.prototype.constructor=Wo,Wo.prototype.__class__=Wo,Wo.__cache__={},n["LBSpaSelectSegResultItem"]=Wo,Wo.prototype["GetResultId"]=Wo.prototype.GetResultId=function(){var t=this.ptr;return mo(Cn(t),zo)},Wo.prototype["GetPt0"]=Wo.prototype.GetPt0=function(){var t=this.ptr;return mo(Rn(t),wo)},Wo.prototype["GetPt1"]=Wo.prototype.GetPt1=function(){var t=this.ptr;return mo(Mn(t),wo)},Wo.prototype["GetSegPt"]=Wo.prototype.GetSegPt=function(){var t=this.ptr;return mo(Vn(t),wo)},Wo.prototype["GetPickPt"]=Wo.prototype.GetPickPt=function(){var t=this.ptr;return mo(wn(t),wo)},Wo.prototype["GetPickDist"]=Wo.prototype.GetPickDist=function(){var t=this.ptr;return Tn(t)},Wo.prototype["__destroy__"]=Wo.prototype.__destroy__=function(){var t=this.ptr;Dn(t)},Ho.prototype=Object.create(so.prototype),Ho.prototype.constructor=Ho,Ho.prototype.__class__=Ho,Ho.__cache__={},n["LBSpaSelectResult"]=Ho,Ho.prototype["ClearAll"]=Ho.prototype.ClearAll=function(){var t=this.ptr;En(t)},Ho.prototype["GetResultIdSize"]=Ho.prototype.GetResultIdSize=function(){var t=this.ptr;return Nn(t)},Ho.prototype["GetResultId"]=Ho.prototype.GetResultId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(On(e,t),zo)},Ho.prototype["GetTriResultElemSize"]=Ho.prototype.GetTriResultElemSize=function(){var t=this.ptr;return kn(t)},Ho.prototype["GetTriResultElem"]=Ho.prototype.GetTriResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(zn(e,t),Fo)},Ho.prototype["GetSegResultElemSize"]=Ho.prototype.GetSegResultElemSize=function(){var t=this.ptr;return Fn(t)},Ho.prototype["GetSegResultElem"]=Ho.prototype.GetSegResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Wn(e,t),Wo)},Ho.prototype["__destroy__"]=Ho.prototype.__destroy__=function(){var t=this.ptr;Hn(t)},Uo.prototype=Object.create(so.prototype),Uo.prototype.constructor=Uo,Uo.prototype.__class__=Uo,Uo.__cache__={},n["LBSpaMgr"]=Uo,Uo.prototype["CreateTriangleSpatial"]=Uo.prototype.CreateTriangleSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Yn(e,t),No)},Uo.prototype["CreateStepLineSpatial"]=Uo.prototype.CreateStepLineSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(qn(e,t),No)},Uo.prototype["CreatePrimitiveCluster"]=Uo.prototype.CreatePrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Xn(e,t),Oo)},Uo.prototype["AddPrimitiveSpatial"]=Uo.prototype.AddPrimitiveSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Jn(e,t)},Uo.prototype["GetPrimitiveCluster"]=Uo.prototype.GetPrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Zn(e,t),Oo)},Uo.prototype["EnablePrimitiveSelected"]=Uo.prototype.EnablePrimitiveSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Kn(r,t,e)},Uo.prototype["IsPrimitiveSelected"]=Uo.prototype.IsPrimitiveSelected=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Qn(e,t)},Uo.prototype["EnableAllPrimitiveSelected"]=Uo.prototype.EnableAllPrimitiveSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),$n(e,t)},Uo.prototype["SetPrimitiveSpatialMat"]=Uo.prototype.SetPrimitiveSpatialMat=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!tp(r,t,e)},Uo.prototype["RemovePrimitiveSpatial"]=Uo.prototype.RemovePrimitiveSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),ep(e,t)},Uo.prototype["RemoveAllPrimitiveSpatial"]=Uo.prototype.RemoveAllPrimitiveSpatial=function(){var t=this.ptr;rp(t)},Uo.prototype["Select"]=Uo.prototype.Select=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!np(r,t,e)},Uo.prototype["GetNumOfRemoveItems"]=Uo.prototype.GetNumOfRemoveItems=function(){var t=this.ptr;return pp(t)},Uo.prototype["GetRemoveItemAt"]=Uo.prototype.GetRemoveItemAt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(ip(e,t),zo)},Uo.prototype["__destroy__"]=Uo.prototype.__destroy__=function(){var t=this.ptr;op(t)},Yo.prototype=Object.create(so.prototype),Yo.prototype.constructor=Yo,Yo.prototype.__class__=Yo,Yo.__cache__={},n["LBSpaBoxMgr"]=Yo,Yo.prototype["InsertBox"]=Yo.prototype.InsertBox=function(t,e,r,n,p,i,o){var _=this.ptr;Bo.prepare(),t=t&&"object"===typeof t?t.ptr:go(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),ap(_,t,e,r,n,p,i,o)},Yo.prototype["RemoveBox"]=Yo.prototype.RemoveBox=function(t){var e=this.ptr;Bo.prepare(),t=t&&"object"===typeof t?t.ptr:go(t),cp(e,t)},Yo.prototype["SetSelectBox"]=Yo.prototype.SetSelectBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),up(o,t,e,r,n,p,i)},Yo.prototype["Select"]=Yo.prototype.Select=function(){var t=this.ptr;lp(t)},Yo.prototype["GetSelectedIdSize"]=Yo.prototype.GetSelectedIdSize=function(){var t=this.ptr;return sp(t)},Yo.prototype["GetSelectedId"]=Yo.prototype.GetSelectedId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),A(yp(e,t))},Yo.prototype["__destroy__"]=Yo.prototype.__destroy__=function(){var t=this.ptr;dp(t)},qo.prototype=Object.create(so.prototype),qo.prototype.constructor=qo,qo.prototype.__class__=qo,qo.__cache__={},n["LBSpaTriangle"]=qo,qo.prototype["SetPtNum"]=qo.prototype.SetPtNum=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),fp(n,t,e,r)},qo.prototype["SetPtVal"]=qo.prototype.SetPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),bp(p,t,e,r,n)},qo.prototype["SetUVVal"]=qo.prototype.SetUVVal=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Sp(n,t,e,r)},qo.prototype["SetNormVal"]=qo.prototype.SetNormVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),vp(p,t,e,r,n)},qo.prototype["SetIndexNum"]=qo.prototype.SetIndexNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),hp(e,t)},qo.prototype["SetIndexVal"]=qo.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Bp(r,t,e)},qo.prototype["AddTrangle"]=qo.prototype.AddTrangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),gp(e,t)},qo.prototype["GetPtNum"]=qo.prototype.GetPtNum=function(){var t=this.ptr;return Lp(t)},qo.prototype["GetPt"]=qo.prototype.GetPt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Pp(e,t),wo)},qo.prototype["GetUV"]=qo.prototype.GetUV=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(Ip(e,t),To)},qo.prototype["GetNorm"]=qo.prototype.GetNorm=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),mo(jp(e,t),wo)},qo.prototype["GetIndexNum"]=qo.prototype.GetIndexNum=function(){var t=this.ptr;return Gp(t)},qo.prototype["GetIndex"]=qo.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),xp(e,t)},qo.prototype["__destroy__"]=qo.prototype.__destroy__=function(){var t=this.ptr;Cp(t)},Xo.prototype=Object.create(so.prototype),Xo.prototype.constructor=Xo,Xo.prototype.__class__=Xo,Xo.__cache__={},n["LBSpaSerial"]=Xo,Xo.prototype["WriteSpatial"]=Xo.prototype.WriteSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Mp(e,t)},Xo.prototype["WriteTriangle"]=Xo.prototype.WriteTriangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Vp(e,t)},Xo.prototype["GetBufferSize"]=Xo.prototype.GetBufferSize=function(){var t=this.ptr;return wp(t)},Xo.prototype["GetBufferVal"]=Xo.prototype.GetBufferVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Tp(e,t)},Xo.prototype["ReadSpatial"]=Xo.prototype.ReadSpatial=function(t,e){var r=this.ptr;return Bo.prepare(),"object"==typeof t&&(t=Lo(t)),e&&"object"===typeof e&&(e=e.ptr),mo(Dp(r,t,e),No)},Xo.prototype["ReadTriangle"]=Xo.prototype.ReadTriangle=function(t,e){var r=this.ptr;return Bo.prepare(),"object"==typeof t&&(t=Lo(t)),e&&"object"===typeof e&&(e=e.ptr),mo(Ap(r,t,e),qo)},Xo.prototype["__destroy__"]=Xo.prototype.__destroy__=function(){var t=this.ptr;Ep(t)},Jo.prototype=Object.create(so.prototype),Jo.prototype.constructor=Jo,Jo.prototype.__class__=Jo,Jo.__cache__={},n["LBSpaGeoTool"]=Jo,Jo.prototype["GetRaySegIntersection"]=Jo.prototype.GetRaySegIntersection=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),Op(o,t,e,r,n,p,i)},Jo.prototype["GetTwoSegIntersection"]=Jo.prototype.GetTwoSegIntersection=function(t,e,r,n,p){var i=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),!!kp(i,t,e,r,n,p)},Jo.prototype["__destroy__"]=Jo.prototype.__destroy__=function(){var t=this.ptr;zp(t)},Zo.prototype=Object.create(so.prototype),Zo.prototype.constructor=Zo,Zo.prototype.__class__=Zo,Zo.__cache__={},n["LBSpaSkirtInfo"]=Zo,Zo.prototype["get_iPtSectIndex"]=Zo.prototype.get_iPtSectIndex=function(){var t=this.ptr;return Wp(t)},Zo.prototype["set_iPtSectIndex"]=Zo.prototype.set_iPtSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Hp(e,t)},Object.defineProperty(Zo.prototype,"iPtSectIndex",{get:Zo.prototype.get_iPtSectIndex,set:Zo.prototype.set_iPtSectIndex}),Zo.prototype["get_iIndexSectIndex"]=Zo.prototype.get_iIndexSectIndex=function(){var t=this.ptr;return Up(t)},Zo.prototype["set_iIndexSectIndex"]=Zo.prototype.set_iIndexSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Yp(e,t)},Object.defineProperty(Zo.prototype,"iIndexSectIndex",{get:Zo.prototype.get_iIndexSectIndex,set:Zo.prototype.set_iIndexSectIndex}),Zo.prototype["__destroy__"]=Zo.prototype.__destroy__=function(){var t=this.ptr;qp(t)},Ko.prototype=Object.create(so.prototype),Ko.prototype.constructor=Ko,Ko.prototype.__class__=Ko,Ko.__cache__={},n["LBSpaBody"]=Ko,Ko.prototype["Init"]=Ko.prototype.Init=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!Jp(r,t,e)},Ko.prototype["GetTriangle"]=Ko.prototype.GetTriangle=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!Zp(r,t,e)},Ko.prototype["CheckReference"]=Ko.prototype.CheckReference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Kp(e,t)},Ko.prototype["ComputeUnion"]=Ko.prototype.ComputeUnion=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Qp(e,t)},Ko.prototype["ComputeIntersection"]=Ko.prototype.ComputeIntersection=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!$p(e,t)},Ko.prototype["ComputeDifference"]=Ko.prototype.ComputeDifference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!ti(e,t)},Ko.prototype["GetVolume"]=Ko.prototype.GetVolume=function(){var t=this.ptr;return ei(t)},Ko.prototype["__destroy__"]=Ko.prototype.__destroy__=function(){var t=this.ptr;ri(t)},"function"===typeof n["onModuleParsed"]&&n["onModuleParsed"](),e.ready}}();t.materem=e}));