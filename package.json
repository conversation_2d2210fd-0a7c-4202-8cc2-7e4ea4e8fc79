{"name": "bigscreen-pddd-view", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --open", "prod": "vite --mode production --open", "static-copy": "node static-copy.js", "build:staging": "vite build --mode staging", "build": "vite build --mode production", "deployDev": "vite build --mode staging && node publish.js", "prettier": "prettier --write .", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@epicgames-ps/lib-pixelstreamingfrontend-ue5.4": "^1.1.5", "@epicgames-ps/lib-pixelstreamingfrontend-ui-ue5.4": "^1.0.4", "@types/node": "^22.13.1", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^10.4.1", "animate.css": "^4.1.1", "autofit.js": "^3.0.7", "axios": "^1.4.0", "chalk": "^5.3.0", "del": "^7.0.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "element-plus": "^2.3.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^10.0.0", "fabric": "^5.3.0", "iscroll": "^5.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-multimd-table": "^4.2.3", "moment": "^2.29.4", "mqtt": "^4.3.8", "nprogress": "^0.2.0", "ora": "^7.0.1", "pinia": "^2.1.4", "recursive-copy": "^2.0.14", "suit-cim": "^1.0.27", "suit-datav": "^2.0.3", "typescript": "^5.7.3", "vite-plugin-externals": "^0.6.2", "vite-plugin-html-config": "^1.0.11", "vue": "^3.4.14", "vue-element-plus-x": "^1.3.0", "vue-router": "^4.2.2", "vue3-video-play": "1.3.1-beta.6", "znyg-frontend-common": "^1.0.45"}, "devDependencies": {"@unocss/eslint-config": "^0.55.7", "@unocss/preset-rem-to-px": "^0.55.7", "@unocss/transformer-directives": "^0.55.7", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "babel-eslint": "^10.1.0", "code-inspector-plugin": "^0.9.2", "eslint": "^8.42.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.4.30", "postcss-html": "^1.5.0", "prettier": "2.8.8", "sass": "~1.77.3", "sass-loader": "^13.3.2", "scp2": "^0.5.0", "stylelint": "^15.10.3", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "unocss": "^0.55.7", "unplugin-auto-import": "^0.17.5", "vite": "^4.3.9", "vite-plugin-eslint": "^1.8.1", "vite-plugin-resolve-externals": "^0.2.2"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["prettier --write"]}, "volta": {"node": "16.20.2"}}