import * as echarts from 'echarts'

// import Color from 'cesium/Source/Core/Color'

export const radar1 = {
  color: ['rgba(19, 173, 255, 1)', 'rgba(245, 166, 35, 1)'],
  tooltip: {
    show: true,
    trigger: 'item'
  },
  legend: {
    icon: 'roundRect',
    top: '15%',
    left: '38%',
    show: true,
    y: '1',
    center: 0,
    itemWidth: 15,
    itemHeight: 3,
    itemGap: 26,
    z: 3,
    data: ['今日', '同比'],
    textStyle: {
      fontSize: 16,
      color: '#7EC9FF'
    }
  },
  radar: {
    center: ['45%', '58%'], // 外圆的位置
    radius: '40%',
    name: {
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
        fontFamily: 'PingFangSC-Regular,PingFang SC',
        fontStyle: 'normal'
      }
    },
    indicator: [
      {
        name: '复驶率',
        max: 100
      },
      {
        name: '满意度',
        max: 100
      },
      {
        name: '影响度',
        max: 100
      },
      {
        name: '时空分布/巡视路径相关度',
        max: 100
      },
      {
        name: '黑点/待命',
        max: 100
      },
      {
        name: '持续时间',
        max: 100
      }
    ],
    splitArea: {
      // 坐标轴在 grid 区域中的分隔区域，默认不显示。
      show: true,
      areaStyle: {
        // 分隔区域的样式设置。
        color: ['transparent'] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
      }
    },
    axisLine: {
      // 指向外圈文本的分隔线样式
      show: false,
      lineStyle: {
        color: 'rgba(255,255,255,0.2)'
      }
    },
    splitLine: {
      lineStyle: {
        type: 'solid',
        color: '#1e416f', // 分隔线颜色
        width: 2 // 分隔线线宽
      }
    }
  },
  series: [
    {
      type: 'radar',
      symbolSize: 0,
      data: [
        {
          value: [60, 80, 80, 70, 60, 50],
          name: '今日',
          areaStyle: {
            normal: {
              color: 'rgba(19, 173, 255, 0.5)'
            }
          },
          lineStyle: {
            normal: {
              color: 'rgba(19, 173, 255, 1)',
              width: 1,
              type: 'dashed'
            }
          }
        },
        {
          value: [80, 70, 50, 60, 30, 80],
          name: '同比',
          areaStyle: {
            normal: {
              color: 'rgba(245, 166, 35, 0.4)'
            }
          },
          lineStyle: {
            normal: {
              type: 'dashed',

              color: 'rgba(245, 166, 35, 1)',
              width: 1
            }
          }
        }
      ]
    }
  ],
  animationDuration: 5000
}
export const radar2 = {
  color: ['#00FFC5', '#e3ff00'],
  tooltip: {
    show: true,
    trigger: 'item'
  },
  legend: {
    icon: 'roundRect',
    top: '15%',
    left: '38%',
    show: true,
    y: '1',
    center: 0,
    itemWidth: 15,
    itemHeight: 3,
    itemGap: 26,
    z: 3,
    data: ['今日', '同比'],
    textStyle: {
      fontSize: 16,
      color: '#7EC9FF'
    }
  },
  radar: {
    center: ['45%', '58%'], // 外圆的位置
    radius: '40%',
    name: {
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
        fontFamily: 'PingFangSC-Regular,PingFang SC',
        fontStyle: 'normal'
      }
    },
    indicator: [
      {
        name: '复驶率',
        max: 100
      },
      {
        name: '满意度',
        max: 100
      },
      {
        name: '影响度',
        max: 100
      },
      {
        name: '时空分布/巡视路径相关度',
        max: 100
      },
      {
        name: '黑点/待命',
        max: 100
      },
      {
        name: '持续时间',
        max: 100
      }
    ],
    splitArea: {
      // 坐标轴在 grid 区域中的分隔区域，默认不显示。
      show: true,
      areaStyle: {
        // 分隔区域的样式设置。
        color: ['transparent'] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
      }
    },
    axisLine: {
      show: false,
      // 指向外圈文本的分隔线样式
      lineStyle: {
        color: 'rgba(255,255,255,0.2)'
      }
    },
    splitLine: {
      lineStyle: {
        type: 'solid',
        color: '#1e416f', // 分隔线颜色
        width: 2 // 分隔线线宽
      }
    }
  },
  series: [
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [100, 0, 0, 0, 0, 0],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [0, 100, 0, 0, 0, 0],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [0, 0, 100, 0, 0, 0],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [0, 0, 0, 100, 0, 0],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [0, 0, 0, 0, 100, 0],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 1,
      type: 'radar',
      silent: true,
      data: [
        {
          value: [0, 0, 0, 0, 0, 100],
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            normal: {
              color: 'transparent'
            }
          },
          itemStyle: {
            normal: {
              color: '#FF9A00'
            }
          }
        }
      ]
    },
    {
      z: 30,
      type: 'radar',
      symbolSize: 0,
      data: [
        {
          // TODO:
          value: [80, 80, 80, 70, 60, 50],
          name: '今日',
          areaStyle: {
            normal: {
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0,255,197, 0.14)' // 0% 处的颜色
                  },
                  {
                    offset: 0.15,
                    color: 'rgba(0,255,197, 0.14)' // 100% 处的颜色
                  },
                  {
                    offset: 0.75,
                    color: 'rgba(0,255,197, 0.4)' // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,255,197,0.5)' // 100% 处的颜色
                  }
                ]
              }
            }
          },
          itemStyle: {
            // 折线拐点标志的样式。
            normal: {
              // 普通状态时的样式
              lineStyle: {
                width: 1
              },
              opacity: 0.3
            },
            emphasis: {
              // 高亮时的样式
              lineStyle: {
                width: 5
              },
              opacity: 0
            }
          }
        },
        {
          // TODO:
          value: [40, 70, 50, 60, 30, 80],
          name: '同比',
          areaStyle: {
            normal: {
              color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(227, 255,0, 0.14)' // 0% 处的颜色
                  },
                  {
                    offset: 0.15,
                    color: 'rgba(227, 255,0, 0.14)' // 100% 处的颜色
                  },
                  {
                    offset: 0.75,
                    color: 'rgba(227, 255,0, 0.4)' // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(227, 255,0, 0.5)' // 100% 处的颜色
                  }
                ]
              }
            }
          },
          itemStyle: {
            // 折线拐点标志的样式。
            normal: {
              // 普通状态时的样式
              lineStyle: {
                width: 1
              },
              opacity: 0.3
            },
            emphasis: {
              // 高亮时的样式
              lineStyle: {
                width: 5
              },
              opacity: 0
            }
          }
        }
      ]
    }
  ],
  animationDuration: 5000
}
export const radar3 = {
  color: ['#00FFC5', '#e3ff00'],
  tooltip: {
    show: true,
    trigger: 'item'
  },
  legend: {
    icon: 'roundRect',
    top: '15%',
    left: '38%',
    show: true,
    y: '1',
    center: 0,
    itemWidth: 15,
    itemHeight: 3,
    itemGap: 26,
    z: 3,
    data: ['今日', '同比'],
    textStyle: {
      fontSize: 16,
      color: '#7EC9FF'
    }
  },
  radar: {
    shape: 'circle',
    center: ['45%', '58%'], // 外圆的位置
    radius: '40%',
    name: {
      textStyle: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 400,
        fontFamily: 'PingFangSC-Regular,PingFang SC',
        fontStyle: 'normal'
      }
    },
    indicator: [
      {
        name: '复驶率',
        max: 100
      },
      {
        name: '满意度',
        max: 100
      },
      {
        name: '影响度',
        max: 100
      },
      {
        name: '时空分布/巡视路径相关度',
        max: 100
      },
      {
        name: '黑点/待命',
        max: 100
      },
      {
        name: '持续时间',
        max: 100
      }
    ],
    splitArea: {
      // 坐标轴在 grid 区域中的分隔区域，默认不显示。
      show: true,
      areaStyle: {
        // 分隔区域的样式设置。
        color: ['transparent'] // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
      }
    },
    axisLine: {
      show: false,
      // 指向外圈文本的分隔线样式
      lineStyle: {
        color: 'rgba(255,255,255,0.2)'
      }
    },
    splitLine: {
      lineStyle: {
        type: 'solid',
        color: '#1e416f', // 分隔线颜色
        width: 2 // 分隔线线宽
      }
    }
  },
  series: [
    {
      z: 30,
      type: 'radar',
      symbolSize: 5,
      data: [
        {
          value: [100, 80, 90, 60, 70, 80],
          name: '今日'
        },
        {
          value: [50, 70, 50, 40, 100, 80],
          name: '同比'
        }
      ]
    }
  ],
  animationDuration: 5000
}
// 数据
const color = ['#00C9FF', '#13E7DF', '#0DF36A', '#EFE910 ', '#E39F0C', '#EB0C0C', '#B613E3']
const Data = [
  {
    name: '人员聚集',
    value: 245,
    proportion: '24%'
  },
  {
    name: '火灾',
    value: 185,
    proportion: '18%'
  },
  {
    name: '建筑施工',
    value: 185,
    proportion: '18%'
  },
  {
    name: '交通运输',
    value: 185,
    proportion: '18%'
  },
  {
    name: '危险施工',
    value: 185,
    proportion: '18%'
  },
  {
    name: '地址灾害',
    value: 148,
    proportion: '16%'
  },
  {
    name: '其他',
    value: 102,
    proportion: '9%'
  }
]
export const radar4 = {
    // 指定图表的配置项和数据
      legend: {
        width: '10px',
        height: '10px',
        itemHeight: 13,
        itemWidth: 13,
        itemGap: 15,
        align: 'auto',
        top: '5%',
        right: '10%',
        data: Data,
        formatter: (name) => {
          let target = 0
          let p = ''
          // let p = ''
          for (let i = 0; i < Data.length; i++) {
            if (Data[i].name === name) {
              target = Data[i].value
              p = Data[i].proportion
            }
          }
          if (name == '人员聚集') {
            return ['{a|' + name + '}{b|' + target + '}{c|' + p + '}']
          }
          if (name == '火灾') {
            return ['{a|' + name + '}{b|' + target + '}{d|' + p + '}']
          }
          if (name == '建筑施工') {
            return ['{a|' + name + '}{b|' + target + '}{e|' + p + '}']
          }
          if (name == '交通运输') {
            return ['{a|' + name + '}{b|' + target + '}{f|' + p + '}']
          }
          if (name == '危险施工') {
            return ['{a|' + name + '}{b|' + target + '}{g|' + p + '}']
          }
          if (name == '地址灾害') {
            return ['{a|' + name + '}{b|' + target + '}{h|' + p + '}']
          }
          if (name == '其他') {
            return ['{a|' + name + '}{b|' + target + '}{i|' + p + '}']
          }
        },
        textStyle: {
          // 图例的公用文本样式。
          fontSize: 14,
          color: '#fff',
          padding: 9,
          rich: {
            a: {
              verticalAlign: 'right',
              fontSize: 14,
              fontfamily: 'Alibaba-PuHuiTi-R, Alibaba-PuHuiTi',
              align: 'left',
              width: 80
            },
            b: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 80
            },
            c: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#00C9FF'
            },
            d: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#13E7DF'
            },
            e: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#0DF36A'
            },
            f: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#EFE910 '
            },
            g: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#E39F0C'
            },
            h: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#EB0C0C'
            },
            i: {
              fontSize: 16,
              align: 'left',
              fontFamily: 'YouSheBiaoTiHei',
              width: 50,
              color: '#B613E3'
            }
          }
        }
      },
      series: [
        {
          type: 'pie',
          center: ['23%', '30%'],
          radius: ['38%', '48%'],
          //
          emphasis: {
            scale: false
          },
          // hoverAnimation: true, // 是否开启 hover 在扇区上的放大动画效果。[ default: true ]
          color: color,
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              borderWidth: 5,
              borderColor: '#072647'
            }
          },
          data: Data
        },
        {
          type: 'pie',
          center: ['23%', '30%'],
          radius: ['35%', '37%']
        },
        {
          type: 'pie',
          center: ['23%', '30%'],
          radius: ['16%', '18%']
        },
        {
          type: 'pie',
          center: ['23%', '30%'],
          radius: ['49%', '50%']
        },
        {
          type: 'pie',
          center: ['23%', '30%'],
          radius: ['6%', '8%']
        }
      ]
}
export const radar5 = {
      legend: false,
      tooltip: {
        show: true,
        position: ['100%', '0%']
      },
      radar: [
        {
          indicator: [
            { name: '道路技术状况', max: 100 },
            { name: '养护作业执行率', max: 100 },
            { name: '设备完好率', max: 100 },
            { name: '巡视率', max: 100 }
          ],
          center: ['50.5%', '25.5%'],
          radius: 70,
          shape: 'circle',
          axisName: {
            show: true
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ['#08395a', '#065a78', '#065070', '#074767']
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: 0.5
            }
          },
          splitLine: {
            show: true
          }
        }
      ],
      series: [
        {
          type: 'radar',
          symbolSize: 4,
          color: '#FFC200',
          emphasis: {
            lineStyle: {
              width: 0.5
            }
          },
          lineStyle: {
            width: 1,
            color: '#FFC200'
          },
          data: [
            {
              value: [50, 100, 30, 30],
              areaStyle: {
                color: 'rgba(255, 194, 0, 0.2)'
              }
            }
          ]
        }
      ]
}
