/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./Rectangle-9bffefe4","./Math-b5f4d889","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf"],(function(e,t,n,a,i,r,o,u,s,m,f,c,d){"use strict";const b=new t.Cartesian3;function p(e){const n=(e=a.defaultValue(e,a.defaultValue.EMPTY_OBJECT)).minimum,i=e.maximum;this._min=t.Cartesian3.clone(n),this._max=t.Cartesian3.clone(i),this._offsetAttribute=e.offsetAttribute,this._workerName="createBoxOutlineGeometry"}p.fromDimensions=function(e){const n=(e=a.defaultValue(e,a.defaultValue.EMPTY_OBJECT)).dimensions,i=t.Cartesian3.multiplyByScalar(n,.5,new t.Cartesian3);return new p({minimum:t.Cartesian3.negate(i,new t.Cartesian3),maximum:i,offsetAttribute:e.offsetAttribute})},p.fromAxisAlignedBoundingBox=function(e){return new p({minimum:e.minimum,maximum:e.maximum})},p.packedLength=2*t.Cartesian3.packedLength+1,p.pack=function(e,n,i){return i=a.defaultValue(i,0),t.Cartesian3.pack(e._min,n,i),t.Cartesian3.pack(e._max,n,i+t.Cartesian3.packedLength),n[i+2*t.Cartesian3.packedLength]=a.defaultValue(e._offsetAttribute,-1),n};const l=new t.Cartesian3,y=new t.Cartesian3,C={minimum:l,maximum:y,offsetAttribute:void 0};return p.unpack=function(e,n,i){n=a.defaultValue(n,0);const r=t.Cartesian3.unpack(e,n,l),o=t.Cartesian3.unpack(e,n+t.Cartesian3.packedLength,y),u=e[n+2*t.Cartesian3.packedLength];return a.defined(i)?(i._min=t.Cartesian3.clone(r,i._min),i._max=t.Cartesian3.clone(o,i._max),i._offsetAttribute=-1===u?void 0:u,i):(C.offsetAttribute=-1===u?void 0:u,new p(C))},p.createGeometry=function(u){const s=u._min,m=u._max;if(t.Cartesian3.equals(s,m))return;const f=new r.GeometryAttributes,c=new Uint16Array(24),d=new Float64Array(24);d[0]=s.x,d[1]=s.y,d[2]=s.z,d[3]=m.x,d[4]=s.y,d[5]=s.z,d[6]=m.x,d[7]=m.y,d[8]=s.z,d[9]=s.x,d[10]=m.y,d[11]=s.z,d[12]=s.x,d[13]=s.y,d[14]=m.z,d[15]=m.x,d[16]=s.y,d[17]=m.z,d[18]=m.x,d[19]=m.y,d[20]=m.z,d[21]=s.x,d[22]=m.y,d[23]=m.z,f.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d}),c[0]=4,c[1]=5,c[2]=5,c[3]=6,c[4]=6,c[5]=7,c[6]=7,c[7]=4,c[8]=0,c[9]=1,c[10]=1,c[11]=2,c[12]=2,c[13]=3,c[14]=3,c[15]=0,c[16]=0,c[17]=4,c[18]=1,c[19]=5,c[20]=2,c[21]=6,c[22]=3,c[23]=7;const p=t.Cartesian3.subtract(m,s,b),l=.5*t.Cartesian3.magnitude(p);if(a.defined(u._offsetAttribute)){const e=d.length,t=u._offsetAttribute===o.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(e/3).fill(t);f.applyOffset=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new i.Geometry({attributes:f,indices:c,primitiveType:i.PrimitiveType.LINES,boundingSphere:new e.BoundingSphere(t.Cartesian3.ZERO,l),offsetAttribute:u._offsetAttribute})},function(e,t){return a.defined(t)&&(e=p.unpack(e,t)),p.createGeometry(e)}}));
