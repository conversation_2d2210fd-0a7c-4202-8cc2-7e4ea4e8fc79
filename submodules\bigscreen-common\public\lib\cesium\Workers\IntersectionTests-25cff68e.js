/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Transforms-42ed7720","./Math-b5f4d889"],(function(t,e,n,a,i,r){"use strict";const s={};function o(t,e,n){const a=t+e;return r.CesiumMath.sign(t)!==r.CesiumMath.sign(e)&&Math.abs(a/Math.max(Math.abs(t),Math.abs(e)))<n?0:a}s.computeDiscriminant=function(t,e,n){return e*e-4*t*n},s.computeRealRoots=function(t,e,n){let a;if(0===t)return 0===e?[]:[-n/e];if(0===e){if(0===n)return[0,0];const e=Math.abs(n),i=Math.abs(t);if(e<i&&e/i<r.CesiumMath.EPSILON14)return[0,0];if(e>i&&i/e<r.CesiumMath.EPSILON14)return[];if(a=-n/t,a<0)return[];const s=Math.sqrt(a);return[-s,s]}if(0===n)return a=-e/t,a<0?[a,0]:[0,a];const i=o(e*e,-(4*t*n),r.CesiumMath.EPSILON14);if(i<0)return[];const s=-.5*o(e,r.CesiumMath.sign(e)*Math.sqrt(i),r.CesiumMath.EPSILON14);return e>0?[s/t,n/s]:[n/s,s/t]};var c=s;const u={};function l(t,e,n,a){const i=t,r=e/3,s=n/3,o=a,c=i*s,u=r*o,l=r*r,C=s*s,h=i*s-l,M=i*o-r*s,f=r*o-C,m=4*h*f-M*M;let d,g;if(m<0){let t,e,n;l*u>=c*C?(t=i,e=h,n=-2*r*h+i*M):(t=o,e=f,n=-o*M+2*s*f);const a=-(n<0?-1:1)*Math.abs(t)*Math.sqrt(-m);g=-n+a;const p=g/2,w=p<0?-Math.pow(-p,1/3):Math.pow(p,1/3),R=g===a?-w:-e/w;return d=e<=0?w+R:-n/(w*w+R*R+e),l*u>=c*C?[(d-r)/i]:[-o/(d+s)]}const p=h,w=-2*r*h+i*M,R=f,S=-o*M+2*s*f,O=Math.sqrt(m),x=Math.sqrt(3)/2;let y=Math.abs(Math.atan2(i*O,-w)/3);d=2*Math.sqrt(-p);let b=Math.cos(y);g=d*b;let P=d*(-b/2-x*Math.sin(y));const N=g+P>2*r?g-r:P-r,q=i,L=N/q;y=Math.abs(Math.atan2(o*O,-S)/3),d=2*Math.sqrt(-R),b=Math.cos(y),g=d*b,P=d*(-b/2-x*Math.sin(y));const I=-o,E=g+P<2*s?g+s:P+s,v=I/E,z=-N*E-q*I,T=(s*z-r*(N*I))/(-r*z+s*(q*E));return L<=T?L<=v?T<=v?[L,T,v]:[L,v,T]:[v,L,T]:L<=v?[T,L,v]:T<=v?[T,v,L]:[v,T,L]}u.computeDiscriminant=function(t,e,n,a){const i=e*e,r=n*n;return 18*t*e*n*a+i*r-27*(t*t)*(a*a)-4*(t*r*n+i*e*a)},u.computeRealRoots=function(t,e,n,a){let i,r;if(0===t)return c.computeRealRoots(e,n,a);if(0===e){if(0===n){if(0===a)return[0,0,0];r=-a/t;const e=r<0?-Math.pow(-r,1/3):Math.pow(r,1/3);return[e,e,e]}return 0===a?(i=c.computeRealRoots(t,0,n),0===i.Length?[0]:[i[0],0,i[1]]):l(t,0,n,a)}return 0===n?0===a?(r=-e/t,r<0?[r,0,0]:[0,0,r]):l(t,e,0,a):0===a?(i=c.computeRealRoots(t,e,n),0===i.length?[0]:i[1]<=0?[i[0],i[1],0]:i[0]>=0?[0,i[0],i[1]]:[i[0],0,i[1]]):l(t,e,n,a)};var C=u;const h={};function M(t,e,n,a){const i=t*t,s=e-3*i/8,o=n-e*t/2+i*t/8,u=a-n*t/4+e*i/16-3*i*i/256,l=C.computeRealRoots(1,2*s,s*s-4*u,-o*o);if(l.length>0){const e=-t/4,n=l[l.length-1];if(Math.abs(n)<r.CesiumMath.EPSILON14){const t=c.computeRealRoots(1,s,u);if(2===t.length){const n=t[0],a=t[1];let i;if(n>=0&&a>=0){const t=Math.sqrt(n),i=Math.sqrt(a);return[e-i,e-t,e+t,e+i]}if(n>=0&&a<0)return i=Math.sqrt(n),[e-i,e+i];if(n<0&&a>=0)return i=Math.sqrt(a),[e-i,e+i]}return[]}if(n>0){const t=Math.sqrt(n),a=(s+n-o/t)/2,i=(s+n+o/t)/2,r=c.computeRealRoots(1,t,a),u=c.computeRealRoots(1,-t,i);return 0!==r.length?(r[0]+=e,r[1]+=e,0!==u.length?(u[0]+=e,u[1]+=e,r[1]<=u[0]?[r[0],r[1],u[0],u[1]]:u[1]<=r[0]?[u[0],u[1],r[0],r[1]]:r[0]>=u[0]&&r[1]<=u[1]?[u[0],r[0],r[1],u[1]]:u[0]>=r[0]&&u[1]<=r[1]?[r[0],u[0],u[1],r[1]]:r[0]>u[0]&&r[0]<u[1]?[u[0],r[0],u[1],r[1]]:[r[0],u[0],r[1],u[1]]):r):0!==u.length?(u[0]+=e,u[1]+=e,u):[]}}return[]}function f(t,e,n,a){const i=t*t,s=-2*e,o=n*t+e*e-4*a,u=i*a-n*e*t+n*n,l=C.computeRealRoots(1,s,o,u);if(l.length>0){const s=l[0],o=e-s,u=o*o,C=t/2,h=o/2,M=u-4*a,f=u+4*Math.abs(a),m=i-4*s,d=i+4*Math.abs(s);let g,p,w,R,S,O;if(s<0||M*d<m*f){const e=Math.sqrt(m);g=e/2,p=0===e?0:(t*h-n)/e}else{const e=Math.sqrt(M);g=0===e?0:(t*h-n)/e,p=e/2}0===C&&0===g?(w=0,R=0):r.CesiumMath.sign(C)===r.CesiumMath.sign(g)?(w=C+g,R=s/w):(R=C-g,w=s/R),0===h&&0===p?(S=0,O=0):r.CesiumMath.sign(h)===r.CesiumMath.sign(p)?(S=h+p,O=a/S):(O=h-p,S=a/O);const x=c.computeRealRoots(1,w,S),y=c.computeRealRoots(1,R,O);if(0!==x.length)return 0!==y.length?x[1]<=y[0]?[x[0],x[1],y[0],y[1]]:y[1]<=x[0]?[y[0],y[1],x[0],x[1]]:x[0]>=y[0]&&x[1]<=y[1]?[y[0],x[0],x[1],y[1]]:y[0]>=x[0]&&y[1]<=x[1]?[x[0],y[0],y[1],x[1]]:x[0]>y[0]&&x[0]<y[1]?[y[0],x[0],y[1],x[1]]:[x[0],y[0],x[1],y[1]]:x;if(0!==y.length)return y}return[]}h.computeDiscriminant=function(t,e,n,a,i){const r=t*t,s=e*e,o=s*e,c=n*n,u=c*n,l=a*a,C=l*a,h=i*i;return s*c*l-4*o*C-4*t*u*l+18*t*e*n*C-27*r*l*l+256*(r*t)*(h*i)+i*(18*o*n*a-4*s*u+16*t*c*c-80*t*e*c*a-6*t*s*l+144*r*n*l)+h*(144*t*s*n-27*s*s-128*r*c-192*r*e*a)},h.computeRealRoots=function(t,e,n,a,i){if(Math.abs(t)<r.CesiumMath.EPSILON15)return C.computeRealRoots(e,n,a,i);const s=e/t,o=n/t,c=a/t,u=i/t;let l=s<0?1:0;switch(l+=o<0?l+1:l,l+=c<0?l+1:l,l+=u<0?l+1:l,l){case 0:case 3:case 4:case 6:case 7:case 9:case 10:case 12:case 13:case 14:case 15:return M(s,o,c,u);case 1:case 2:case 5:case 8:case 11:return f(s,o,c,u);default:return}};var m=h;function d(t,n){n=e.Cartesian3.clone(a.defaultValue(n,e.Cartesian3.ZERO)),e.Cartesian3.equals(n,e.Cartesian3.ZERO)||e.Cartesian3.normalize(n,n),this.origin=e.Cartesian3.clone(a.defaultValue(t,e.Cartesian3.ZERO)),this.direction=n}d.clone=function(t,n){if(a.defined(t))return a.defined(n)?(n.origin=e.Cartesian3.clone(t.origin),n.direction=e.Cartesian3.clone(t.direction),n):new d(t.origin,t.direction)},d.getPoint=function(t,n,i){return a.defined(i)||(i=new e.Cartesian3),i=e.Cartesian3.multiplyByScalar(t.direction,n,i),e.Cartesian3.add(t.origin,i,i)};const g={rayPlane:function(t,n,i){a.defined(i)||(i=new e.Cartesian3);const s=t.origin,o=t.direction,c=n.normal,u=e.Cartesian3.dot(c,o);if(Math.abs(u)<r.CesiumMath.EPSILON15)return;const l=(-n.distance-e.Cartesian3.dot(c,s))/u;return l<0?void 0:(i=e.Cartesian3.multiplyByScalar(o,l,i),e.Cartesian3.add(s,i,i))}},p=new e.Cartesian3,w=new e.Cartesian3,R=new e.Cartesian3,S=new e.Cartesian3,O=new e.Cartesian3;g.rayTriangleParametric=function(t,n,i,s,o){o=a.defaultValue(o,!1);const c=t.origin,u=t.direction,l=e.Cartesian3.subtract(i,n,p),C=e.Cartesian3.subtract(s,n,w),h=e.Cartesian3.cross(u,C,R),M=e.Cartesian3.dot(l,h);let f,m,d,g,x;if(o){if(M<r.CesiumMath.EPSILON6)return;if(f=e.Cartesian3.subtract(c,n,S),d=e.Cartesian3.dot(f,h),d<0||d>M)return;if(m=e.Cartesian3.cross(f,l,O),g=e.Cartesian3.dot(u,m),g<0||d+g>M)return;x=e.Cartesian3.dot(C,m)/M}else{if(Math.abs(M)<r.CesiumMath.EPSILON6)return;const t=1/M;if(f=e.Cartesian3.subtract(c,n,S),d=e.Cartesian3.dot(f,h)*t,d<0||d>1)return;if(m=e.Cartesian3.cross(f,l,O),g=e.Cartesian3.dot(u,m)*t,g<0||d+g>1)return;x=e.Cartesian3.dot(C,m)*t}return x},g.rayTriangle=function(t,n,i,r,s,o){const c=g.rayTriangleParametric(t,n,i,r,s);if(a.defined(c)&&!(c<0))return a.defined(o)||(o=new e.Cartesian3),e.Cartesian3.multiplyByScalar(t.direction,c,o),e.Cartesian3.add(t.origin,o,o)};const x=new d;g.lineSegmentTriangle=function(t,n,i,r,s,o,c){const u=x;e.Cartesian3.clone(t,u.origin),e.Cartesian3.subtract(n,t,u.direction),e.Cartesian3.normalize(u.direction,u.direction);const l=g.rayTriangleParametric(u,i,r,s,o);if(!(!a.defined(l)||l<0||l>e.Cartesian3.distance(t,n)))return a.defined(c)||(c=new e.Cartesian3),e.Cartesian3.multiplyByScalar(u.direction,l,c),e.Cartesian3.add(u.origin,c,c)};const y={root0:0,root1:0};function b(t,n,r){a.defined(r)||(r=new i.Interval);const s=t.origin,o=t.direction,c=n.center,u=n.radius*n.radius,l=e.Cartesian3.subtract(s,c,R),C=function(t,e,n,a){const i=e*e-4*t*n;if(i<0)return;if(i>0){const n=1/(2*t),r=Math.sqrt(i),s=(-e+r)*n,o=(-e-r)*n;return s<o?(a.root0=s,a.root1=o):(a.root0=o,a.root1=s),a}const r=-e/(2*t);return 0!==r?(a.root0=a.root1=r,a):void 0}(e.Cartesian3.dot(o,o),2*e.Cartesian3.dot(o,l),e.Cartesian3.magnitudeSquared(l)-u,y);if(a.defined(C))return r.start=C.root0,r.stop=C.root1,r}g.raySphere=function(t,e,n){if(n=b(t,e,n),a.defined(n)&&!(n.stop<0))return n.start=Math.max(n.start,0),n};const P=new d;g.lineSegmentSphere=function(t,n,i,r){const s=P;e.Cartesian3.clone(t,s.origin);const o=e.Cartesian3.subtract(n,t,s.direction),c=e.Cartesian3.magnitude(o);if(e.Cartesian3.normalize(o,o),r=b(s,i,r),!(!a.defined(r)||r.stop<0||r.start>c))return r.start=Math.max(r.start,0),r.stop=Math.min(r.stop,c),r};const N=new e.Cartesian3,q=new e.Cartesian3;function L(t,e,n){const a=t+e;return r.CesiumMath.sign(t)!==r.CesiumMath.sign(e)&&Math.abs(a/Math.max(Math.abs(t),Math.abs(e)))<n?0:a}g.rayEllipsoid=function(t,n){const a=n.oneOverRadii,r=e.Cartesian3.multiplyComponents(a,t.origin,N),s=e.Cartesian3.multiplyComponents(a,t.direction,q),o=e.Cartesian3.magnitudeSquared(r),c=e.Cartesian3.dot(r,s);let u,l,C,h,M;if(o>1){if(c>=0)return;const t=c*c;if(u=o-1,l=e.Cartesian3.magnitudeSquared(s),C=l*u,t<C)return;if(t>C){h=c*c-C,M=-c+Math.sqrt(h);const t=M/l,e=u/M;return t<e?new i.Interval(t,e):{start:e,stop:t}}const n=Math.sqrt(u/l);return new i.Interval(n,n)}return o<1?(u=o-1,l=e.Cartesian3.magnitudeSquared(s),C=l*u,h=c*c-C,M=-c+Math.sqrt(h),new i.Interval(0,M/l)):c<0?(l=e.Cartesian3.magnitudeSquared(s),new i.Interval(0,-c/l)):void 0};const I=new e.Cartesian3,E=new e.Cartesian3,v=new e.Cartesian3,z=new e.Cartesian3,T=new e.Cartesian3,U=new n.Matrix3,W=new n.Matrix3,B=new n.Matrix3,V=new n.Matrix3,Z=new n.Matrix3,A=new n.Matrix3,D=new n.Matrix3,F=new e.Cartesian3,G=new e.Cartesian3,Y=new n.Cartographic;g.grazingAltitudeLocation=function(t,i){const s=t.origin,o=t.direction;if(!e.Cartesian3.equals(s,e.Cartesian3.ZERO)){const t=i.geodeticSurfaceNormal(s,I);if(e.Cartesian3.dot(o,t)>=0)return s}const u=a.defined(this.rayEllipsoid(t,i)),l=i.transformPositionToScaledSpace(o,I),C=e.Cartesian3.normalize(l,l),h=e.Cartesian3.mostOrthogonalAxis(l,z),M=e.Cartesian3.normalize(e.Cartesian3.cross(h,C,E),E),f=e.Cartesian3.normalize(e.Cartesian3.cross(C,M,v),v),d=U;d[0]=C.x,d[1]=C.y,d[2]=C.z,d[3]=M.x,d[4]=M.y,d[5]=M.z,d[6]=f.x,d[7]=f.y,d[8]=f.z;const g=n.Matrix3.transpose(d,W),p=n.Matrix3.fromScale(i.radii,B),w=n.Matrix3.fromScale(i.oneOverRadii,V),R=Z;R[0]=0,R[1]=-o.z,R[2]=o.y,R[3]=o.z,R[4]=0,R[5]=-o.x,R[6]=-o.y,R[7]=o.x,R[8]=0;const S=n.Matrix3.multiply(n.Matrix3.multiply(g,w,A),R,A),O=n.Matrix3.multiply(n.Matrix3.multiply(S,p,D),d,D),x=n.Matrix3.multiplyByVector(S,s,T),y=function(t,a,i,s,o){const u=s*s,l=o*o,C=(t[n.Matrix3.COLUMN1ROW1]-t[n.Matrix3.COLUMN2ROW2])*l,h=o*(s*L(t[n.Matrix3.COLUMN1ROW0],t[n.Matrix3.COLUMN0ROW1],r.CesiumMath.EPSILON15)+a.y),M=t[n.Matrix3.COLUMN0ROW0]*u+t[n.Matrix3.COLUMN2ROW2]*l+s*a.x+i,f=l*L(t[n.Matrix3.COLUMN2ROW1],t[n.Matrix3.COLUMN1ROW2],r.CesiumMath.EPSILON15),d=o*(s*L(t[n.Matrix3.COLUMN2ROW0],t[n.Matrix3.COLUMN0ROW2])+a.z);let g;const p=[];if(0===d&&0===f){if(g=c.computeRealRoots(C,h,M),0===g.length)return p;const t=g[0],n=Math.sqrt(Math.max(1-t*t,0));if(p.push(new e.Cartesian3(s,o*t,o*-n)),p.push(new e.Cartesian3(s,o*t,o*n)),2===g.length){const t=g[1],n=Math.sqrt(Math.max(1-t*t,0));p.push(new e.Cartesian3(s,o*t,o*-n)),p.push(new e.Cartesian3(s,o*t,o*n))}return p}const w=d*d,R=f*f,S=d*f,O=C*C+R,x=2*(h*C+S),y=2*M*C+h*h-R+w,b=2*(M*h-S),P=M*M-w;if(0===O&&0===x&&0===y&&0===b)return p;g=m.computeRealRoots(O,x,y,b,P);const N=g.length;if(0===N)return p;for(let t=0;t<N;++t){const n=g[t],a=n*n,i=Math.max(1-a,0),c=Math.sqrt(i);let u;u=r.CesiumMath.sign(C)===r.CesiumMath.sign(M)?L(C*a+M,h*n,r.CesiumMath.EPSILON12):r.CesiumMath.sign(M)===r.CesiumMath.sign(h*n)?L(C*a,h*n+M,r.CesiumMath.EPSILON12):L(C*a+h*n,M,r.CesiumMath.EPSILON12);const l=u*L(f*n,d,r.CesiumMath.EPSILON15);l<0?p.push(new e.Cartesian3(s,o*n,o*c)):l>0?p.push(new e.Cartesian3(s,o*n,o*-c)):0!==c?(p.push(new e.Cartesian3(s,o*n,o*-c)),p.push(new e.Cartesian3(s,o*n,o*c)),++t):p.push(new e.Cartesian3(s,o*n,o*c))}return p}(O,e.Cartesian3.negate(x,I),0,0,1);let b,P;const N=y.length;if(N>0){let t=e.Cartesian3.clone(e.Cartesian3.ZERO,G),a=Number.NEGATIVE_INFINITY;for(let i=0;i<N;++i){b=n.Matrix3.multiplyByVector(p,n.Matrix3.multiplyByVector(d,y[i],F),F);const r=e.Cartesian3.normalize(e.Cartesian3.subtract(b,s,z),z),c=e.Cartesian3.dot(r,o);c>a&&(a=c,t=e.Cartesian3.clone(b,t))}const c=i.cartesianToCartographic(t,Y);return a=r.CesiumMath.clamp(a,0,1),P=e.Cartesian3.magnitude(e.Cartesian3.subtract(t,s,z))*Math.sqrt(1-a*a),P=u?-P:P,c.height=P,i.cartographicToCartesian(c,new e.Cartesian3)}};const _=new e.Cartesian3;g.lineSegmentPlane=function(t,n,i,s){a.defined(s)||(s=new e.Cartesian3);const o=e.Cartesian3.subtract(n,t,_),c=i.normal,u=e.Cartesian3.dot(c,o);if(Math.abs(u)<r.CesiumMath.EPSILON6)return;const l=e.Cartesian3.dot(c,t),C=-(i.distance+l)/u;return C<0||C>1?void 0:(e.Cartesian3.multiplyByScalar(o,C,s),e.Cartesian3.add(t,s,s),s)},g.trianglePlaneIntersection=function(t,n,a,i){const r=i.normal,s=i.distance,o=e.Cartesian3.dot(r,t)+s<0,c=e.Cartesian3.dot(r,n)+s<0,u=e.Cartesian3.dot(r,a)+s<0;let l,C,h=0;if(h+=o?1:0,h+=c?1:0,h+=u?1:0,1!==h&&2!==h||(l=new e.Cartesian3,C=new e.Cartesian3),1===h){if(o)return g.lineSegmentPlane(t,n,i,l),g.lineSegmentPlane(t,a,i,C),{positions:[t,n,a,l,C],indices:[0,3,4,1,2,4,1,4,3]};if(c)return g.lineSegmentPlane(n,a,i,l),g.lineSegmentPlane(n,t,i,C),{positions:[t,n,a,l,C],indices:[1,3,4,2,0,4,2,4,3]};if(u)return g.lineSegmentPlane(a,t,i,l),g.lineSegmentPlane(a,n,i,C),{positions:[t,n,a,l,C],indices:[2,3,4,0,1,4,0,4,3]}}else if(2===h){if(!o)return g.lineSegmentPlane(n,t,i,l),g.lineSegmentPlane(a,t,i,C),{positions:[t,n,a,l,C],indices:[1,2,4,1,4,3,0,3,4]};if(!c)return g.lineSegmentPlane(a,n,i,l),g.lineSegmentPlane(t,n,i,C),{positions:[t,n,a,l,C],indices:[2,0,4,2,4,3,1,3,4]};if(!u)return g.lineSegmentPlane(t,a,i,l),g.lineSegmentPlane(n,a,i,C),{positions:[t,n,a,l,C],indices:[0,1,4,0,4,3,2,3,4]}}};var j=g;t.IntersectionTests=j,t.Ray=d}));
