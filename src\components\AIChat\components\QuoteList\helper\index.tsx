import { computed, type Ref } from 'vue'
import lib from '@/utils/lib'

// 合并引用列表的逻辑
export const useMergedQuoteList = (props: { quoteList?: any[] }) => {
  // 合并引用文件列表，添加计数和弹窗状态
  const getMergedQuoteList = (quoteList: any[]) => {
    const mergedQuotes: any[] = []
    const quoteMap = new Map()

    quoteList.forEach((quote) => {
      // 优化文件名获取逻辑，支持用户提供的数据格式
      const fileName = quote.fileName || quote.document_name || quote.title || '未知文件'
      const fileId = quote.id || quote.document_id || quote.docId || quote.segment_id

      if (!quoteMap.has(fileName)) {
        quoteMap.set(fileName, {
          id: fileId,
          fileName: fileName,
          url: quote.url,
          count: 1,
          originalQuotes: [quote]
        })
      } else {
        const existing = quoteMap.get(fileName)
        existing.count++
        existing.originalQuotes.push(quote)
      }
    })

    // 按相关度排序（取最高分）
    quoteMap.forEach((mergedQuote) => {
      // 按score降序排序原始引用
      mergedQuote.originalQuotes.sort((a, b) => (b.score || 0) - (a.score || 0))
      mergedQuotes.push(mergedQuote)
    })

    // 按最高相关度排序合并后的引用
    mergedQuotes.sort((a, b) => {
      const aMaxScore = Math.max(...a.originalQuotes.map((q) => q.score || 0))
      const bMaxScore = Math.max(...b.originalQuotes.map((q) => q.score || 0))
      return bMaxScore - aMaxScore
    })

    return mergedQuotes
  }

  const mergedQuoteList = computed(() => {
    if (!props.quoteList || !Array.isArray(props.quoteList)) {
      return []
    }
    return getMergedQuoteList(props.quoteList)
  })

  return {
    mergedQuoteList
  }
}

// 引用操作相关逻辑
export const useQuoteActions = (selectedFiles: Ref<Array<{ id: string; name: string; url?: string }>>) => {
  // 引用列表选中
  const handleQuoteSelect = (checked: boolean, quote: any) => {
    // 确保ID类型一致，都转换为字符串
    const quoteId = quote.id?.toString()

    if (checked) {
      // 选中：添加到selectedFiles，但不超过5个
      if (selectedFiles.value.length < 5 && quoteId) {
        selectedFiles.value.push({
          id: quoteId,
          name: quote.fileName,
          url: quote.url || undefined
        })
      }
    } else {
      // 取消选中：从selectedFiles中移除
      const index = selectedFiles.value.findIndex((file) => file.id === quoteId)
      if (index > -1) {
        selectedFiles.value.splice(index, 1)
      }
    }
  }

  // 处理引用点击，在新窗口打开
  const handleQuote = async (quote: any) => {
    console.log(quote)
    if (quote.url) {
      openDialogWindow(quote.url)
    } else {
      const res = await lib.api.knowledgeFileApi.getFromDocumentId({
        documentIdList: [quote.id]
      })
      if (res.result?.length > 0) {
        quote.url = res.result[0].url
        openDialogWindow(quote.url)
      }
    }
  }

  // 打开对话框窗口
  const openDialogWindow = (url: string) => {
    const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
    window.open(url, 'newwindow', windowFeatures)
  }

  // 获取折叠项的唯一名称
  const getCollapseItemName = (quote: any, index: number) => {
    return quote?.segment_id || quote?.id || index.toString()
  }

  return {
    handleQuoteSelect,
    handleQuote,
    getCollapseItemName
  }
}
