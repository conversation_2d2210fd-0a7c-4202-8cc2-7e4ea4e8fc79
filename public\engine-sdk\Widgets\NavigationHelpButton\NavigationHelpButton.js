import buildModuleUrl from"../../Core/buildModuleUrl.js";import defaultValue from"../../Core/defaultValue.js";import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import FeatureDetection from"../../Core/FeatureDetection.js";import knockout from"../../ThirdParty/knockout.js";import getElement from"../getElement.js";import NavigationHelpButtonViewModel from"./NavigationHelpButtonViewModel.js";function NavigationHelpButton(t){if(!defined(t)||!defined(t.container))throw new DeveloperError("options.container is required.");var e=getElement(t.container),i=new NavigationHelpButtonViewModel,o=defaultValue(t.instructionsInitiallyVisible,!1);i.showInstructions=o,i._svgPath="M16,1.466C7.973,1.466,1.466,7.973,1.466,16c0,8.027,6.507,14.534,14.534,14.534c8.027,0,14.534-6.507,14.534-14.534C30.534,7.973,24.027,1.466,16,1.466z M17.328,24.371h-2.707v-2.596h2.707V24.371zM17.328,19.003v0.858h-2.707v-1.057c0-3.19,3.63-3.696,3.63-5.963c0-1.034-0.924-1.826-2.134-1.826c-1.254,0-2.354,0.924-2.354,0.924l-1.541-1.915c0,0,1.519-1.584,4.137-1.584c2.487,0,4.796,1.54,4.796,4.136C21.156,16.208,17.328,16.627,17.328,19.003z";var n=document.createElement("span");n.className="cesium-navigationHelpButton-wrapper",e.appendChild(n);var a=document.createElement("button");a.type="button",a.className="cesium-button cesium-toolbar-button cesium-navigation-help-button",a.setAttribute("data-bind","attr: { title: tooltip },click: command,cesiumSvgPath: { path: _svgPath, width: 32, height: 32 }"),n.appendChild(a);var s=document.createElement("div");s.className="cesium-navigation-help",s.setAttribute("data-bind",'css: { "cesium-navigation-help-visible" : showInstructions}'),n.appendChild(s);var d=document.createElement("button");d.type="button",d.className="cesium-navigation-button cesium-navigation-button-left",d.setAttribute("data-bind",'click: showClick, css: {"cesium-navigation-button-selected": !_touch, "cesium-navigation-button-unselected": _touch}');var c=document.createElement("img");c.src=buildModuleUrl("Widgets/Images/NavigationHelp/Mouse.svg"),c.className="cesium-navigation-button-icon",c.style.width="25px",c.style.height="25px",d.appendChild(c),d.appendChild(document.createTextNode("Mouse"));var l=document.createElement("button");l.type="button",l.className="cesium-navigation-button cesium-navigation-button-right",l.setAttribute("data-bind",'click: showTouch, css: {"cesium-navigation-button-selected": _touch, "cesium-navigation-button-unselected": !_touch}');var r=document.createElement("img");r.src=buildModuleUrl("Widgets/Images/NavigationHelp/Touch.svg"),r.className="cesium-navigation-button-icon",r.style.width="25px",r.style.height="25px",l.appendChild(r),l.appendChild(document.createTextNode("Touch")),s.appendChild(d),s.appendChild(l);var u=document.createElement("div");u.className="cesium-click-navigation-help cesium-navigation-help-instructions",u.setAttribute("data-bind",'css: { "cesium-click-navigation-help-visible" : !_touch}'),u.innerHTML='            <table>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/MouseLeft.svg")+'" width="48" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-pan">Pan view</div>                        <div class="cesium-navigation-help-details">Left click + drag</div>                    </td>                </tr>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/MouseRight.svg")+'" width="48" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-zoom">Zoom view</div>                        <div class="cesium-navigation-help-details">Right click + drag, or</div>                        <div class="cesium-navigation-help-details">Mouse wheel scroll</div>                    </td>                </tr>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/MouseMiddle.svg")+'" width="48" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-rotate">Rotate view</div>                        <div class="cesium-navigation-help-details">Middle click + drag, or</div>                        <div class="cesium-navigation-help-details">CTRL + Left/Right click + drag</div>                    </td>                </tr>            </table>',s.appendChild(u);var v=document.createElement("div");v.className="cesium-touch-navigation-help cesium-navigation-help-instructions",v.setAttribute("data-bind",'css: { "cesium-touch-navigation-help-visible" : _touch}'),v.innerHTML='            <table>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/TouchDrag.svg")+'" width="70" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-pan">Pan view</div>                        <div class="cesium-navigation-help-details">One finger drag</div>                    </td>                </tr>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/TouchZoom.svg")+'" width="70" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-zoom">Zoom view</div>                        <div class="cesium-navigation-help-details">Two finger pinch</div>                    </td>                </tr>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/TouchTilt.svg")+'" width="70" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-rotate">Tilt view</div>                        <div class="cesium-navigation-help-details">Two finger drag, same direction</div>                    </td>                </tr>                <tr>                    <td><img src="'+buildModuleUrl("Widgets/Images/NavigationHelp/TouchRotate.svg")+'" width="70" height="48" /></td>                    <td>                        <div class="cesium-navigation-help-tilt">Rotate view</div>                        <div class="cesium-navigation-help-details">Two finger drag, opposite direction</div>                    </td>                </tr>            </table>',s.appendChild(v),knockout.applyBindings(i,n),this._container=e,this._viewModel=i,this._wrapper=n,this._closeInstructions=function(t){n.contains(t.target)||(i.showInstructions=!1)},FeatureDetection.supportsPointerEvents()?document.addEventListener("pointerdown",this._closeInstructions,!0):(document.addEventListener("mousedown",this._closeInstructions,!0),document.addEventListener("touchstart",this._closeInstructions,!0))}Object.defineProperties(NavigationHelpButton.prototype,{container:{get:function(){return this._container}},viewModel:{get:function(){return this._viewModel}}}),NavigationHelpButton.prototype.isDestroyed=function(){return!1},NavigationHelpButton.prototype.destroy=function(){return FeatureDetection.supportsPointerEvents()?document.removeEventListener("pointerdown",this._closeInstructions,!0):(document.removeEventListener("mousedown",this._closeInstructions,!0),document.removeEventListener("touchstart",this._closeInstructions,!0)),knockout.cleanNode(this._wrapper),this._container.removeChild(this._wrapper),destroyObject(this)};export default NavigationHelpButton;