import addBuffer from"./addBuffer.js";import ForEach from"./ForEach.js";import readAccessorPacked from"./readAccessorPacked.js";import ComponentDatatype from"../../Core/ComponentDatatype.js";import WebGLConstants from"../../Core/WebGLConstants.js";function updateAccessorComponentTypes(e){var t;return ForEach.accessorWithSemantic(e,"JOINTS_0",(function(o){var n=e.accessors[o];t=n.componentType,t===WebGLConstants.BYTE?convertType(e,n,ComponentDatatype.UNSIGNED_BYTE):t!==WebGLConstants.UNSIGNED_BYTE&&t!==WebGLConstants.UNSIGNED_SHORT&&convertType(e,n,ComponentDatatype.UNSIGNED_SHORT)})),ForEach.accessorWithSemantic(e,"WEIGHTS_0",(function(o){var n=e.accessors[o];t=n.componentType,t===WebGLConstants.BYTE?convertType(e,n,ComponentDatatype.UNSIGNED_BYTE):t===WebGLConstants.SHORT&&convertType(e,n,ComponentDatatype.UNSIGNED_SHORT)})),e}function convertType(e,t,o){var n=ComponentDatatype.createTypedArray(o,readAccessorPacked(e,t)),a=new Uint8Array(n.buffer);t.bufferView=addBuffer(e,a),t.componentType=o,t.byteOffset=0}export default updateAccessorComponentTypes;