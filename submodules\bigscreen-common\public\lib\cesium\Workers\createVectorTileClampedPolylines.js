/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./AttributeCompression-d661357e","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./combine-0bec9016","./IndexDatatype-00859b8b","./Math-b5f4d889","./createTaskProcessorWorker","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./WebGLConstants-433debbf","./RuntimeError-592f0d41"],(function(t,e,a,s,n,r,i,o,d,c,l){"use strict";const f=32767,h=Math.cos(r.CesiumMath.toRadians(150)),u=new a.Cartographic,C=new e.Cartesian3;const p=new a.Cartographic,b=new a.Cartographic;function m(t){const e=8*t,a=3*e,s=4*e;this.startEllipsoidNormals=new Float32Array(a),this.endEllipsoidNormals=new Float32Array(a),this.startPositionAndHeights=new Float32Array(s),this.startFaceNormalAndVertexCornerIds=new Float32Array(s),this.endPositionAndHeights=new Float32Array(s),this.endFaceNormalAndHalfWidths=new Float32Array(s),this.vertexBatchIds=new Uint16Array(e),this.indices=n.IndexDatatype.createTypedArray(e,36*t),this.vec3Offset=0,this.vec4Offset=0,this.batchIdOffset=0,this.indexOffset=0,this.volumeStartIndex=0}const A=new e.Cartesian3,w=new e.Cartesian3;function g(t,a,s,n,r){const i=e.Cartesian3.subtract(s,a,w);let o=e.Cartesian3.subtract(a,t,A);return e.Cartesian3.normalize(i,i),e.Cartesian3.normalize(o,o),e.Cartesian3.dot(i,o)<h&&(o=e.Cartesian3.multiplyByScalar(o,-1,A)),e.Cartesian3.add(i,o,r),e.Cartesian3.equals(r,e.Cartesian3.ZERO)&&(r=e.Cartesian3.subtract(t,a)),e.Cartesian3.cross(r,n,r),e.Cartesian3.cross(n,r,r),e.Cartesian3.normalize(r,r),r}const y=[0,2,6,0,6,4,0,1,3,0,3,2,0,4,5,0,5,1,5,3,1,5,7,3,7,5,4,7,4,6,7,6,2,7,2,3],N=y.length,k=new e.Cartesian3,I=new e.Cartesian3,x=new e.Cartesian3,E=new e.Cartesian3,F=new e.Cartesian3;m.prototype.addVolume=function(t,a,s,n,r,i,o,d,c,l){let f=e.Cartesian3.add(a,c,k);const h=l.geodeticSurfaceNormal(f,I);f=e.Cartesian3.add(s,c,k);const u=l.geodeticSurfaceNormal(f,E),C=g(t,a,s,h,x),p=g(n,s,a,u,F),b=this.startEllipsoidNormals,m=this.endEllipsoidNormals,A=this.startPositionAndHeights,w=this.startFaceNormalAndVertexCornerIds,H=this.endPositionAndHeights,O=this.endFaceNormalAndHalfWidths,P=this.vertexBatchIds;let v,D=this.batchIdOffset,R=this.vec3Offset,S=this.vec4Offset;for(v=0;v<8;v++)e.Cartesian3.pack(h,b,R),e.Cartesian3.pack(u,m,R),e.Cartesian3.pack(a,A,S),A[S+3]=r,e.Cartesian3.pack(s,H,S),H[S+3]=i,e.Cartesian3.pack(C,w,S),w[S+3]=v,e.Cartesian3.pack(p,O,S),O[S+3]=o,P[D++]=d,R+=3,S+=4;this.batchIdOffset=D,this.vec3Offset=R,this.vec4Offset=S;const M=this.indices,U=this.volumeStartIndex,B=this.indexOffset;for(v=0;v<N;v++)M[B+v]=y[v]+U;this.volumeStartIndex+=8,this.indexOffset+=N};const H=new a.Rectangle,O=new a.Ellipsoid,P=new e.Cartesian3,v=new e.Cartesian3,D=new e.Cartesian3,R=new e.Cartesian3,S=new e.Cartesian3;return i((function(i,o){const d=new Uint16Array(i.positions),c=new Uint16Array(i.widths),l=new Uint32Array(i.counts),h=new Uint16Array(i.batchIds),A=H,w=O,g=P,y=new Float64Array(i.packedBuffer);let N=0;const k=y[N++],I=y[N++];let x;a.Rectangle.unpack(y,N,A),N+=a.Rectangle.packedLength,a.Ellipsoid.unpack(y,N,w),N+=a.Ellipsoid.packedLength,e.Cartesian3.unpack(y,N,g);let E=d.length/3;const F=d.subarray(0,E),M=d.subarray(E,2*E),U=d.subarray(2*E,3*E);t.AttributeCompression.zigZagDeltaDecode(F,M,U),function(t,e,s,n){const r=n.length,i=t.length,o=new Uint8Array(i),d=p,c=b;let l=0;for(let s=0;s<r;s++){const r=n[s];let i=r;for(let s=1;s<r;s++){const n=l+s,r=n-1;c.longitude=t[n],c.latitude=e[n],d.longitude=t[r],d.latitude=e[r],a.Cartographic.equals(c,d)&&(i--,o[r]=1)}n[s]=i,l+=r}let f=0;for(let a=0;a<i;a++)1!==o[a]&&(t[f]=t[a],e[f]=e[a],s[f]=s[a],f++)}(F,M,U,l);const B=l.length;let T=0;for(x=0;x<B;x++){T+=l[x]-1}const V=new m(T),W=function(t,s,n,i,o,d,c){const l=t.length,h=new Float64Array(3*l);for(let p=0;p<l;++p){const l=t[p],b=s[p],m=n[p],A=r.CesiumMath.lerp(i.west,i.east,l/f),w=r.CesiumMath.lerp(i.south,i.north,b/f),g=r.CesiumMath.lerp(o,d,m/f),y=a.Cartographic.fromRadians(A,w,g,u),N=c.cartographicToCartesian(y,C);e.Cartesian3.pack(N,h,3*p)}return h}(F,M,U,A,k,I,w);E=F.length;const z=new Float32Array(3*E);for(x=0;x<E;++x)z[3*x]=W[3*x]-g.x,z[3*x+1]=W[3*x+1]-g.y,z[3*x+2]=W[3*x+2]-g.z;let q=0,L=0;for(x=0;x<B;x++){const t=l[x]-1,a=.5*c[x],s=h[x],n=q;for(let i=0;i<t;i++){const o=e.Cartesian3.unpack(z,q,D),d=e.Cartesian3.unpack(z,q+3,R);let c=U[L],l=U[L+1];c=r.CesiumMath.lerp(k,I,c/f),l=r.CesiumMath.lerp(k,I,l/f),L++;let h=v,u=S;if(0===i){const a=n+3*t,s=e.Cartesian3.unpack(z,a,v);if(e.Cartesian3.equals(s,o))e.Cartesian3.unpack(z,a-3,h);else{const t=e.Cartesian3.subtract(o,d,v);h=e.Cartesian3.add(t,o,v)}}else e.Cartesian3.unpack(z,q-3,h);if(i===t-1){const t=e.Cartesian3.unpack(z,n,S);if(e.Cartesian3.equals(t,d))e.Cartesian3.unpack(z,n+3,u);else{const t=e.Cartesian3.subtract(d,o,S);u=e.Cartesian3.add(t,d,S)}}else e.Cartesian3.unpack(z,q+6,u);V.addVolume(h,o,d,u,c,l,a,s,g,w),q+=3}q+=3,L++}const _=V.indices;o.push(V.startEllipsoidNormals.buffer),o.push(V.endEllipsoidNormals.buffer),o.push(V.startPositionAndHeights.buffer),o.push(V.startFaceNormalAndVertexCornerIds.buffer),o.push(V.endPositionAndHeights.buffer),o.push(V.endFaceNormalAndHalfWidths.buffer),o.push(V.vertexBatchIds.buffer),o.push(_.buffer);let G={indexDatatype:2===_.BYTES_PER_ELEMENT?n.IndexDatatype.UNSIGNED_SHORT:n.IndexDatatype.UNSIGNED_INT,startEllipsoidNormals:V.startEllipsoidNormals.buffer,endEllipsoidNormals:V.endEllipsoidNormals.buffer,startPositionAndHeights:V.startPositionAndHeights.buffer,startFaceNormalAndVertexCornerIds:V.startFaceNormalAndVertexCornerIds.buffer,endPositionAndHeights:V.endPositionAndHeights.buffer,endFaceNormalAndHalfWidths:V.endFaceNormalAndHalfWidths.buffer,vertexBatchIds:V.vertexBatchIds.buffer,indices:_.buffer};if(i.keepDecodedPositions){const t=function(t){const e=t.length,a=new Uint32Array(e+1);let s=0;for(let n=0;n<e;++n)a[n]=s,s+=t[n];return a[e]=s,a}(l);o.push(W.buffer,t.buffer),G=s.combine(G,{decodedPositions:W.buffer,decodedPositionOffsets:t.buffer})}return G}))}));
