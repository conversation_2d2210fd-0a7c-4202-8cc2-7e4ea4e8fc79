<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 11:26:07
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 16:15:05
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\EmergencyResources\car.vue
 * @Description:
 *
-->
<template>
  <div class="car-container">
    <SubHeadLine>车辆</SubHeadLine>
    <!-- <pieChart :chartData="chartDataCar" :chartTitle="'车辆'" :chartUnit="'辆'"></pieChart> -->
    <ThreePie :data="chartDataCar" :chartTitle="'车辆'" :chartUnit="'辆'"></ThreePie>
  </div>
</template>

<script setup>
  import pieChart from './pieChart.vue'
  import ThreePie from './threePie.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  const chartDataCar = ref([
    { value: 4, name: '道路巡视车' },
    { value: 3, name: '牵引清障车' },
    { value: 2, name: '养护作业车' },
    { value: 1, name: '防撞缓冲车' },
    { value: 4, name: '其他车' }
  ])
</script>

<style lang="scss" scoped>
  .car-container {
    width: 288px;
    height: 160px;
  }
</style>
