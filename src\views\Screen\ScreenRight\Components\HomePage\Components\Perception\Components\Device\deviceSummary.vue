<template>
  <div class="device-summary-container">
    <div class="device-summary-tabs">
      <div class="device-summary-tabs-item" v-for="item in list" :key="item.id" @click="handleClick(item)">
        <div class="icon"><img :src="lib.utils.getAssetsFile(`ScreenRight/Perception/Device/${item.icon}`)" /></div>
        <div class="content">
          <div class="content-value">
            {{ item.number }}
            <span>{{ item.unit }}</span>
          </div>
          <div class="content-title">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="device-chart">
      <MyChart width="600px" height="330px" :option="options" style="margin-top: 10px"></MyChart>
    </div> -->
  </div>
</template>
<script setup lang="ts">
  import * as echarts from 'echarts'

  import MyChart from '@Common/components/MyChart/index.vue'
  import { useIntervalFn } from '@vueuse/core'

  import lib from '@/utils/lib.ts'
  import { usePopWindow, type PopWindowProps } from 'znyg-frontend-common'
  import DefectList from './Components/DefectList/index.vue'
  const { showPopWindow } = usePopWindow()

  const list = ref([
    { id: 1, name: '设备总数', number: 0, unit: '个', icon: 'deviceAll.png', selected: true },
    { id: 2, name: '故障总数', number: 0, unit: '个', icon: 'deviceFault.png', selected: false },
    { id: 3, name: '设备完好率', number: 0, unit: '%', icon: 'deviceIntact.png', selected: false }
  ])
  let childList = []
  var color = '#ffffff'
  const options = ref({
    grid: {
      left: '10',
      right: '30',
      bottom: '10',
      top: '10',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: function (item) {
        let ret = ''
        if (item.seriesType !== 'pictorialBar') {
          // ret += `${item.marker}${item.name}:${item.value ?? '-'}${item.seriesName === '设备完好率' ? '%' : ''}<br />`
          ret += `${item.name}:${item.value ?? '-'}${item.seriesName === '设备完好率' ? '%' : '个'}<br />`
        }

        return ret
      }
    },
    yAxis: [
      {
        type: 'category',
        data: ['照明系统', '综合监控系统', '通信系统', '排水系统', '通风系统', '火灾报警和消防系统', '供配电系统'],
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 14
          },
          formatter: '{value}',
          interval: 0
        },
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#7CB1C8'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(80,224,255,0.3)',
            type: 'dashed'
          }
        }
      }
    ],
    xAxis: [
      {
        type: 'value',
        axisLabel: {
          textStyle: {
            color: color,
            fontSize: 16
          }
        },
        axisTick: {
          show: false,
          lineStyle: {
            color: color
          }
        },
        axisLine: {
          lineStyle: {
            color: ''
          }
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: 'rgba(80,224,255,0.3)',
            type: 'dashed'
          }
        },
        name: '个',
        nameTextStyle: {
          color: color,
          fontSize: 14,
          // padding: [20, 0, 0, 0],
          verticalAlign: 'bottom'
        }
      }
    ],

    series: [
      {
        name: '',
        type: 'bar',
        // data: [],
        data: [8617, 606, 781, 159, 196, 3351, 574],
        barWidth: 12,
        // barCategoryGap: '35%',
        // label: {
        //   normal: {
        //     show: true,
        //     position: 'right',
        //     distance: 20,
        //     formatter: function(params) {
        //       return params.data.value
        //     },
        //     textStyle: {
        //       color: '#ffc72b',
        //       fontSize: 16
        //     }
        //   }
        // },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: 'rgba(84, 194, 255, 0.17)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(0, 209, 255, 1)' // 100% 处的颜色
              }
            ],
            false
          ),
          borderWidth: 1,
          borderColor: 'rgba(255,255,255,.2)'
        }
      }
    ]
  })

  const getData = () => {
    lib.api.pdRoadDeviceAlertApi.deviceAlertStatistics({}).then((res) => {
      console.log(res)
      if (res.success) {
        const { goodRate, bugCount, count, thisChildList } = res.result
        childList = thisChildList
        list.value[0].number = count
        list.value[1].number = bugCount
        list.value[2].number = goodRate.slice(0, -1)
        options.value.series[0].data = thisChildList.filter((_) => _.count > 0).map((_) => _.count)
        options.value.yAxis[0].data = thisChildList.filter((_) => _.count > 0).map((_) => _.name)
      }
    })
  }
  onMounted(() => {
    // getData()
    useIntervalFn(
      () => {
        getData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })

  const handleClick = (item) => {
    list.value.forEach((_) => {
      _.selected = _.id === item.id
    })
    if (item.id == 1) {
      options.value.series[0].name = '设备总数'
      options.value.series[0].data = childList.filter((_) => _.count > 0).map((_) => _.count)
      options.value.yAxis[0].data = childList.filter((_) => _.count > 0).map((_) => _.name)
      options.value.xAxis[0].name = '个'
    } else if (item.id == 2) {
      options.value.series[0].data = childList.filter((_) => _.count > 0).map((_) => _.bugCount)
      options.value.yAxis[0].data = childList.filter((_) => _.count > 0).map((_) => _.name)
      options.value.series[0].name = '故障总数'
      options.value.xAxis[0].name = '个'

      //弹出故障列表
      const op: PopWindowProps = {
        left: 1596,
        top: 205,
        width: 800,
        height: 490,
        tag: 'DeviceDefectList',
        appendParent: 'player',
        draggable: true
      }
      showPopWindow(op, DefectList)
    } else if (item.id == 3) {
      options.value.series[0].data = childList.filter((_) => _.count > 0).map((_) => _.goodRate.slice(0, -1))
      options.value.series[0].name = '设备完好率'
      options.value.xAxis[0].name = '%'
    }
  }
</script>

<style lang="scss" scoped>
  .device-summary-container {
    width: 100%;
    height: 88px;
    font-family: 'Alibaba PuHuiTi';
    .device-summary-tabs {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 57px;
      .device-summary-tabs-item {
        display: flex;
        flex: 1;
        height: 57px;
        cursor: pointer;
        .icon {
          width: 61px;
          height: 61px;
          img {
            width: 61px;
            height: 61px;
          }
        }
        .content {
          box-sizing: border-box;
          flex: 1;
          padding-top: 5px;
          padding-left: 16px;
          .content-title {
            font-size: 17px;
            font-weight: 400;
            line-height: 16px;
            color: #dbefff;
          }
          .content-value {
            margin-top: 6px;
            font-size: 28px;
            font-weight: bold;
            line-height: 24px;
            color: #63eaff;
            span {
              font-size: 17px;
              color: #ffffff;
            }
          }
        }
      }
    }
    .device-chart {
      width: 600px;
      height: 320px;
    }
  }
</style>
