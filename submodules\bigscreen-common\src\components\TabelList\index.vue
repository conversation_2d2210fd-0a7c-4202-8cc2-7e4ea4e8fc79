<template>
  <div class="table-list">
    <el-table
      v-load="handleLoadMore"
      v-show="tableData && tableData.length > 0"
      class="popup-table"
      :style="`height:${height}`"
      v-bind="$attrs"
      header-row-class-name="headerClass"
      row-class-name="itemClass itemClassActive"
      :data="tableData"
      ref="singleTableRef"
      label-position="center"
      :highlight-current-row="highlightCurrentRow"
      @row-click="handleClick">
      <!-- @current-change="handleCurrentChange"  -->
      <el-table-column v-if="isCheckbox" type="selection" width="55" fixed="left" />
      <el-table-column v-if="showIndex" type="index" align="center" label="序号" width="60"></el-table-column>
      <el-table-column
        :show-overflow-tooltip="showOverflowTooltip"
        v-for="(item, index) in tableHeader"
        :key="index"
        style="background: red"
        :prop="item.prop"
        header-align="center"
        :align="item.align ? item.align : 'center'"
        :label="item.label"
        :fixed="item.fixed"
        :width="item.width ? item.width : 'auto'">
        <template #default="scoped">
          <ex-slot v-if="item.render" :render="item.render" :row="scoped.row" :numIndex="numIndex" :index="scoped.$index" :column="item" />
          <span v-else>
            {{ scoped.row[item.prop] || '-' }}
            <component v-if="item.icon" :is="item.icon" style="width: 16px; height: 16px; margin-left: 5px"></component>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="no-date" v-show="tableData.length == 0">暂无数据</div>
  </div>
</template>
<script setup>
  import { ElTable, ElTableColumn } from 'element-plus'
  import { h, ref, watch } from 'vue'

  const vLoad = {
    mounted: (el, binding, vnode) => {
      const selectWrap = el.querySelector('.el-table__body-wrapper')
      let timer
      selectWrap.addEventListener(
        'scroll',
        function () {
          let sign = 1
          const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight
          if (scrollDistance <= sign) {
            if (timer) {
              clearTimeout(timer)
            }
            timer = setTimeout(function () {
              timer = undefined
              binding.value()
            }, 500)
          }
        },
        true
      )
      // }, 100)
    }
  }
  const props = defineProps({
    isSelectRow: {
      //是否存在选中的
      type: Boolean,
      default: true
    },
    showOverflowTooltip: {
      //是否显示提示框
      type: Boolean,
      default: false
    },
    selectItem: {
      type: Object,
      default: () => null
    },
    // msg: String
    limit: {
      // 表格显示几条数据默认四条
      type: Number,
      default: 4
    },
    height: {
      type: [Number, String],
      default: null
    },
    numIndex: {
      type: [Number, String],
      default: null
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    tableHeader: {
      type: Array,
      default: () => null
    },
    tableData: {
      type: Array,
      default: () => null
    },
    isCheckbox: {
      type: Boolean,
      default: false
    },
    highlightCurrentRow: {
      //是否高亮
      type: Boolean,
      default: true
    }
  })
  const emits = defineEmits(['handleClickRow', 'loadMore'])
  const exSlot = (props, context) => {
    const params = {
      numIndex: props.numIndex,
      row: props.row,
      index: props.index
    }
    if (props.column) params.column = props.column
    return props.render(h, params)
  }
  exSlot.props = {
    row: Object,
    render: Function,
    index: Number,
    numIndex: [Number, String],
    column: {
      type: Object,
      default: null
    }
  }
  watch(
    () => props.isSelectRow,
    (value) => {
      if (value) {
        cancelSelect() //清除选中的
      } else {
        if (props.selectItem) selectItem(props.selectItem) //选中应该选中的
      }
    }
  )

  // methods
  let nowRow = ref(false)
  const singleTableRef = ref(null)

  const cancelSelect = () => {
    //清除选中
    singleTableRef.value.setCurrentRow(-1)
    nowRow.value = null
    emits('handleClickRow', null)
  }

  const selectItem = (row) => {
    //高亮选中
    singleTableRef.value.setCurrentRow(row)
    nowRow.value = row
  }

  const handleClick = (currentRow) => {
    if (currentRow == nowRow.value) {
      singleTableRef.value.setCurrentRow(-1)
      nowRow.value = null
      emits('handleClickRow', null)
      return
    }
    emits('handleClickRow', currentRow)
    nowRow.value = currentRow
  }
  const handleLoadMore = () => {
    // console.log('触底加载')
    emits('loadMore')
  }
</script>

<style lang="scss" scoped>
  .table-list {
    width: 100%;
    margin-top: 10px;
    .no-date {
      text-align: center;
      // padding: 30px 0;
      // background: #fff;
      color: #fff;
      line-height: 150px;
    }
    .popup-table {
      width: 100%;
      overflow: hidden;
    }
    .el-table__body-wrapper {
      max-height: 100px;
    }

    :deep(.el-table) {
      &::before {
        background-color: transparent;
      }
      .el-table__body tr.current-row > td {
        background: #325c8d;
      }
      .el-table__body-wrapper {
        background: #0b1f3e;
        // background:transparent;
        overflow-y: auto;
      }
      .el-table__inner-wrapper {
        &::before {
          background: transparent;
        }
      }
      .el-table__body-wrapper::-webkit-scrollbar {
        position: absolute;
        width: 0.125rem;
        background: transparent;
        display: block;
        background: transparent;
      }
      .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background: #c4edff;
        border-radius: 0.3125rem;
      }
      .el-table__body {
        background: #0b1f3e;
      }
      .el-table__empty-block {
        background: #0b1f3e;
        border: 1px solid #325c8d;
      }
      .headerClass {
        height: 32px;
        // background: linear-gradient(90deg, #06244D 0%, #123B6B 30%, #06244D 100%);
        background: linear-gradient(90deg, #00223a 0%, rgba(18, 59, 107, 1) 30%, rgb(0, 34, 58, 1) 100%);
        th.el-table__cell {
          margin-right: 1.25rem;
          text-align: center;
          font-size: 16px;
          padding: 0;
          background: transparent;
          margin-right: 0.625rem;
          font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
          color: #fff;
          font-weight: 400;
          height: 32px;
          border-bottom: 1px solid #123b6b;
          &.is-leaf {
            border-bottom: 0.125rem solid transparent;
          }
          &:nth-last-child(1) {
            border-right: none;
          }
        }
      }
      // .itemClass{
      //   height:20px;
      //   &:hover{
      //     td.el-table__cell{
      //       background: #325C8D;
      //     }
      //   }
      //   td.el-table__cell{
      //     color: #fff;
      //     font-size: .875rem;
      //     text-align: center;
      //     padding: 0;
      //     border-bottom: .125rem solid #0A1C38;

      //   }
      // }
      .itemClassActive {
        height: 40px;
        background: linear-gradient(90deg, #00223a 0%, #062e4d 30%, rgb(0, 34, 58, 1) 100%);
        // opacity: 0.9;

        font-size: 16px;
        font-family: Alibaba-PuHuiTi-R, Alibaba-PuHuiTi;
        font-weight: normal;
        color: #bfcfe1;

        td.el-table__cell {
          background: transparent;
          color: #bfcfe1;
          border-bottom: 1px solid #123b6b;
        }
        &:hover {
          td.el-table__cell {
            cursor: pointer;
            // background: #325C8D;
          }
        }
      }
    }
  }
</style>
