/*
 * @Description:
 * @Autor: wangjialing
 * @Date: 2023-06-27 14:49:58
 * @LastEditors: qian
 * @LastEditTime: 2023-10-23 10:55:53
 */
import { h, render } from 'vue'

import PopWindow from './PopWindow.vue'

import useStore from '@Common/store'

/**
 * @description: 函数式创建弹出层
 * @param {string} componentName  - 组件路径，如:DemoWindow/index.vue，也可以使用DemoWindow则默认会取该目录下的index.vue组件
 * @param {Object} options  -配置参数
 * @param {string} [options.left] -离左侧距离
 * @param {string} [options.top] - 离顶部距离
 * @param {string} [options.width] - 宽度
 * @param {string} [options.height] - 高度
 * @param {string} [options.id] - divId默认会生成一串字符串
 * @param {string} [options.tag] - 给节点添加标签，用于统一清除
 * @param {string} [options.appendParent] - 弹出框组件的挂载父容器的Id，传入app，则挂载到#App   如果传入：body,则挂载到body中  默认为app
 * @param {Boolean} [options.isFollow]  -是否跟随地图移动 // * 默认值false, 当值为true options.popPosition 必填
 * @param {Array} [options.popPosition]  -弹窗位置信息（wgs84）- [wgs84.lng, wgs84.lat] // * isFollow为true，必填
 * @param {Array} [options.offset]  -弹窗偏移值 默认值为[0, 0] -- 弹窗左上方
 * @param {Sting} [options.draggableClass]  -能拖动的子class 默认值为popup-title
 */
export const createPopWindow = (componentName, options, data) => {
  const { storeScreenPopWindowData } = useStore()
  const opt = {
    left: null,
    top: null,
    right: null,
    bottom: null,
    tag: null,
    isFollow: false,
    // appendParent: 'app',
    appendParent: 'ScreenBox',
    id: 'mappopwindow_' + Math.ceil(Math.random() * 1000000),
    closeFunc: null,
    isAllowAllDeletion: true,
    popPosition: [],
    offset: [],
    draggableClass: 'popup-title'
  }

  const option = {
    ...opt,
    ...options
  }

  removeDialogById(option.id) // 先清除相同id的弹窗

  const { left, top, width, height, id, tag, appendParent, closeFunc, popPosition, offset, isAllowAllDeletion, right, bottom, isFollow, draggableClass } =
    option
  const appendParentDiv = option.appendParent === 'body' ? document.body : document.getElementById(option.appendParent)
  const mountNode = document.createElement('div')
  mountNode.id = option.id + '_parent'
  const handleDestroy = () => {
    if (mountNode) {
      // document.getElementById('app').removeChild(mountNode)
      // appendParentDiv.removeChild(mountNode)
      // mountNode = null
      removeDialogById(option.id)
    }
  }

  // 使用 h 函数创建 vnode
  const vnode = h(PopWindow, {
    componentName,
    left,
    top,
    width,
    height,
    id,
    appendParent,
    data: data || {},
    closeFunc: closeFunc || null,
    popPosition,
    offset,
    isAllowAllDeletion,
    right,
    bottom,
    isFollow,
    draggableClass
  })
  // 使用 render 函数将 vnode 渲染为真实DOM并挂载到 body 上
  render(vnode, mountNode)
  storeScreenPopWindowData.dialogNodes.push({ id: option.id, tag: tag, node: mountNode, isAllowAllDeletion })
  // document.getElementById('app').appendChild(mountNode)
  appendParentDiv.appendChild(mountNode)
  return { id: option.id, handleDestroy }
}

/**
 * @description: 清除弹框
 * @param {string} [tag] - 清除相同tag的弹窗，如果不传则清除全部
 */
export const removeDialog = (tag) => {
  const { storeScreenPopWindowData } = useStore()
  const list = []
  if (tag) {
    storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
      _.tag === tag ? render(null, _.node) : list.push(_)
    })
  } else {
    storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
      _.isAllowAllDeletion ? render(null, _.node) : list.push(_)
    })
  }
  storeScreenPopWindowData.dialogNodes = [...list]
}

/**
 * @description: 根据id清除弹框
 * @param {*} id 弹窗id
 * @author: qian
 */
export const removeDialogById = (id) => {
  const { storeScreenPopWindowData } = useStore()
  storeScreenPopWindowData.dialogNodes.forEach((_, index) => {
    if (id == _.id) {
      storeScreenPopWindowData.dialogNodes.splice(index, 1)
      render(null, _.node)
    }
  })
}
