/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./arrayRemoveDuplicates-5b666c82","./BoundingRectangle-b4242da0","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./CoplanarPolygonGeometryLibrary-a05e7473","./defined-3b3eb2ba","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryInstance-d4f76a6a","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonGeometryLibrary-1842a245","./PolygonPipeline-805d6577","./VertexFormat-86c096b8","./Resource-41d99fe7","./combine-0bec9016","./RuntimeError-592f0d41","./WebGLConstants-433debbf","./OrientedBoundingBox-e47c7a90","./EllipsoidTangentPlane-c2c2ef6e","./AxisAlignedBoundingBox-6489d16d","./IntersectionTests-25cff68e","./Plane-a268aa11","./AttributeCompression-d661357e","./EncodedCartesian3-6d30a00c","./ArcType-e42cfb05","./EllipsoidRhumbLine-d5e7f3db"],(function(e,t,n,o,a,r,i,s,l,c,y,p,d,u,m,g,b,f,C,h,x,P,A,L,w,G,F,v,E,_){"use strict";const T=new o.Cartesian3,k=new t.BoundingRectangle,D=new o.Cartesian2,V=new o.Cartesian2,R=new o.Cartesian3,H=new o.Cartesian3,I=new o.Cartesian3,M=new o.Cartesian3,B=new o.Cartesian3,O=new o.Cartesian3,z=new n.Quaternion,S=new s.Matrix3,N=new s.Matrix3,Q=new o.Cartesian3;function j(e,t,r,y,p,m,b,f,C){const h=e.positions;let x=g.PolygonPipeline.triangulate(e.positions2D,e.holes);x.length<3&&(x=[0,1,2]);const P=d.IndexDatatype.createTypedArray(h.length,x.length);P.set(x);let A=S;if(0!==y){let e=n.Quaternion.fromAxisAngle(b,y,z);if(A=s.Matrix3.fromQuaternion(e,A),t.tangent||t.bitangent){e=n.Quaternion.fromAxisAngle(b,-y,z);const a=s.Matrix3.fromQuaternion(e,N);f=o.Cartesian3.normalize(s.Matrix3.multiplyByVector(a,f,f),f),t.bitangent&&(C=o.Cartesian3.normalize(o.Cartesian3.cross(b,f,C),C))}}else A=s.Matrix3.clone(s.Matrix3.IDENTITY,A);const L=V;t.st&&(L.x=r.x,L.y=r.y);const w=h.length,G=3*w,F=new Float64Array(G),v=t.normal?new Float32Array(G):void 0,E=t.tangent?new Float32Array(G):void 0,_=t.bitangent?new Float32Array(G):void 0,k=t.st?new Float32Array(2*w):void 0;let R=0,H=0,I=0,M=0,B=0;for(let e=0;e<w;e++){const n=h[e];if(F[R++]=n.x,F[R++]=n.y,F[R++]=n.z,t.st)if(i.defined(p)&&p.positions.length===w)k[B++]=p.positions[e].x,k[B++]=p.positions[e].y;else{const e=m(s.Matrix3.multiplyByVector(A,n,T),D);o.Cartesian2.subtract(e,L,e);const t=u.CesiumMath.clamp(e.x/r.width,0,1),a=u.CesiumMath.clamp(e.y/r.height,0,1);k[B++]=t,k[B++]=a}t.normal&&(v[H++]=b.x,v[H++]=b.y,v[H++]=b.z),t.tangent&&(E[M++]=f.x,E[M++]=f.y,E[M++]=f.z),t.bitangent&&(_[I++]=C.x,_[I++]=C.y,_[I++]=C.z)}const O=new c.GeometryAttributes;return t.position&&(O.position=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:F})),t.normal&&(O.normal=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),t.tangent&&(O.tangent=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.bitangent&&(O.bitangent=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:_})),t.st&&(O.st=new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:k})),new l.Geometry({attributes:O,indices:P,primitiveType:l.PrimitiveType.TRIANGLES})}function U(e){const t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy,n=e.textureCoordinates,a=i.defaultValue(e.vertexFormat,b.VertexFormat.DEFAULT);this._vertexFormat=b.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=i.defaultValue(e.stRotation,0),this._ellipsoid=s.Ellipsoid.clone(i.defaultValue(e.ellipsoid,s.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this._textureCoordinates=n,this.packedLength=m.PolygonGeometryLibrary.computeHierarchyPackedLength(t,o.Cartesian3)+b.VertexFormat.packedLength+s.Ellipsoid.packedLength+(i.defined(n)?m.PolygonGeometryLibrary.computeHierarchyPackedLength(n,o.Cartesian2):1)+2}U.fromPositions=function(e){return new U({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid,textureCoordinates:e.textureCoordinates})},U.pack=function(e,t,n){return n=i.defaultValue(n,0),n=m.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,n,o.Cartesian3),s.Ellipsoid.pack(e._ellipsoid,t,n),n+=s.Ellipsoid.packedLength,b.VertexFormat.pack(e._vertexFormat,t,n),n+=b.VertexFormat.packedLength,t[n++]=e._stRotation,i.defined(e._textureCoordinates)?n=m.PolygonGeometryLibrary.packPolygonHierarchy(e._textureCoordinates,t,n,o.Cartesian2):t[n++]=-1,t[n++]=e.packedLength,t};const Y=s.Ellipsoid.clone(s.Ellipsoid.UNIT_SPHERE),q=new b.VertexFormat,J={polygonHierarchy:{}};return U.unpack=function(e,t,n){t=i.defaultValue(t,0);const a=m.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,o.Cartesian3);t=a.startingIndex,delete a.startingIndex;const r=s.Ellipsoid.unpack(e,t,Y);t+=s.Ellipsoid.packedLength;const l=b.VertexFormat.unpack(e,t,q);t+=b.VertexFormat.packedLength;const c=e[t++],y=-1===e[t]?void 0:m.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t,o.Cartesian2);i.defined(y)?(t=y.startingIndex,delete y.startingIndex):t++;const p=e[t++];return i.defined(n)||(n=new U(J)),n._polygonHierarchy=a,n._ellipsoid=s.Ellipsoid.clone(r,n._ellipsoid),n._vertexFormat=b.VertexFormat.clone(l,n._vertexFormat),n._stRotation=c,n._textureCoordinates=y,n.packedLength=p,n},U.createGeometry=function(t){const a=t._vertexFormat,s=t._polygonHierarchy,c=t._stRotation,g=t._textureCoordinates,b=i.defined(g);let f=s.positions;if(f=e.arrayRemoveDuplicates(f,o.Cartesian3.equalsEpsilon,!0),f.length<3)return;let C=R,h=H,x=I,P=B;const A=O;if(!r.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(f,M,P,A))return;if(C=o.Cartesian3.cross(P,A,C),C=o.Cartesian3.normalize(C,C),!o.Cartesian3.equalsEpsilon(M,o.Cartesian3.ZERO,u.CesiumMath.EPSILON6)){const e=t._ellipsoid.geodeticSurfaceNormal(M,Q);o.Cartesian3.dot(C,e)<0&&(C=o.Cartesian3.negate(C,C),P=o.Cartesian3.negate(P,P))}const L=r.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(M,P,A),w=r.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(M,P,A);a.tangent&&(h=o.Cartesian3.clone(P,h)),a.bitangent&&(x=o.Cartesian3.clone(A,x));const G=m.PolygonGeometryLibrary.polygonsFromHierarchy(s,b,L,!1),F=G.hierarchy,v=G.polygons,E=b?m.PolygonGeometryLibrary.polygonsFromHierarchy(g,!0,(function(e){return e}),!1).polygons:void 0;if(0===F.length)return;f=F[0].outerRing;const _=n.BoundingSphere.fromPoints(f),T=m.PolygonGeometryLibrary.computeBoundingRectangle(C,w,f,c,k),D=[];for(let e=0;e<v.length;e++){const t=new y.GeometryInstance({geometry:j(v[e],a,T,c,b?E[e]:void 0,w,C,h,x)});D.push(t)}const V=p.GeometryPipeline.combineInstances(D)[0];V.attributes.position.values=new Float64Array(V.attributes.position.values),V.indices=d.IndexDatatype.createTypedArray(V.attributes.position.values.length/3,V.indices);const z=V.attributes;return a.position||delete z.position,new l.Geometry({attributes:z,indices:V.indices,primitiveType:V.primitiveType,boundingSphere:_})},function(e,t){return i.defined(t)&&(e=U.unpack(e,t)),U.createGeometry(e)}}));
