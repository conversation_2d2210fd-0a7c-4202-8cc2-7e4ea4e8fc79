<template>
  <div class="popup-bg-container" :style="{ width, height }">
    <div class="header popup-title cursor-move">
      <img mr-6 src="@/assets/CommonPopup/titleIcon.png" alt="" />
      <span>{{ title }}</span>
      <slot name="customContent"></slot>
      <img class="close" src="@/assets/CommonPopup/closeIcon.png" @click="handlerClose" />
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits } from 'vue'

  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    isCloseBtn: {
      type: Boolean,
      default: true
    }
  })
  console.log('props', props)
  const emit = defineEmits(['close'])
  const handlerClose = () => {
    console.log('handlerClose')
    emit('close')
  }
</script>

<style lang="scss" scoped>
  .popup-bg-container {
    // position: absolute;
    display: flex;
    flex-direction: column;
    padding: 11px 16px 17px;
    overflow: hidden;

    // background: #f50;
    background: url('@/assets/CommonPopup/bg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 20px;
    .header {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      height: 31px;

      // justify-content: center;
      margin-bottom: 13px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-size: 24px;
      font-weight: 400;
      line-height: 31px;
      color: #ffffff;
      letter-spacing: 1px;
      .close {
        position: absolute;
        right: 0;

        // top: 0;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }
    }
    .content {
      flex: 1;
    }
  }
</style>
