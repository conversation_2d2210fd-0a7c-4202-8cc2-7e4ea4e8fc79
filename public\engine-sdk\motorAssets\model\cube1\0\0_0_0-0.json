{"asset": {"version": "1.0"}, "root": {"boundingVolume": {"box": [256, 256, 256, 256, 0, 0, 0, 256, 0, 0, 0, 256]}, "children": [{"boundingVolume": {"box": [128, 128, 128, 128, 0, 0, 0, 128, 0, 0, 0, 128]}, "children": [{"boundingVolume": {"box": [64, 64, 64, 64, 0, 0, 0, 64, 0, 0, 0, 64]}, "children": [{"boundingVolume": {"box": [32, 32, 32, 32, 0, 0, 0, 32, 0, 0, 0, 32]}, "children": [{"boundingVolume": {"box": [16, 16, 16, 16, 0, 0, 0, 16, 0, 0, 0, 16]}, "content": {"uri": "0_0_0-0/0-0_0-0_0-0_0-0.json"}, "geometricError": 0.13531646934131852}], "content": {"uri": "0_0_0-0/0-0_0-0_0-0.b3dm"}, "geometricError": 0.14433756729740643}], "content": {"uri": "0_0_0-0/0-0_0-0.b3dm"}, "geometricError": 0.4330127018922193}], "content": {"uri": "0_0_0-0/0-0.b3dm"}, "geometricError": 1.7320508075688772}], "geometricError": 13.856406460551018, "refine": "REPLACE"}}