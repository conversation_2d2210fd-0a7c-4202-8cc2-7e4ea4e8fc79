/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryPipeline-9dbd2054","./IndexDatatype-00859b8b","./WebMercatorProjection-ce967e48"],(function(e,t,n,o,r,i,s,c,a,d){"use strict";function p(e,t,n){e=o.defaultValue(e,0),t=o.defaultValue(t,0),n=o.defaultValue(n,0),this.value=new Float32Array([e,t,n])}function u(e,t){const o=e.attributes,r=o.position,s=r.values.length/r.componentsPerAttribute;o.batchId=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(s)});const c=o.batchId.values;for(let e=0;e<s;++e)c[e]=t}function f(e){const i=e.instances,s=e.projection,a=e.elementIndexUintSupported,d=e.scene3DOnly,p=e.vertexCacheOptimize,f=e.compressVertices,l=e.modelMatrix;let m,h,g=i.length;for(m=0;m<g;++m)if(o.defined(i[m].geometry)){i[m].geometry.primitiveType;break}if(function(e,t,n){let i=!n;const s=e.length;let a;if(!i&&s>1){const t=e[0].modelMatrix;for(a=1;a<s;++a)if(!r.Matrix4.equals(t,e[a].modelMatrix)){i=!0;break}}if(i)for(a=0;a<s;++a)o.defined(e[a].geometry)&&c.GeometryPipeline.transformToWorldCoordinates(e[a]);else r.Matrix4.multiplyTransformation(t,e[0].modelMatrix,t)}(i,l,d),!d)for(m=0;m<g;++m)o.defined(i[m].geometry)&&c.GeometryPipeline.splitLongitude(i[m]);if(function(e){const t=e.length;for(let n=0;n<t;++n){const t=e[n];o.defined(t.geometry)?u(t.geometry,n):o.defined(t.westHemisphereGeometry)&&o.defined(t.eastHemisphereGeometry)&&(u(t.westHemisphereGeometry,n),u(t.eastHemisphereGeometry,n))}}(i),p)for(m=0;m<g;++m){const e=i[m];o.defined(e.geometry)?(c.GeometryPipeline.reorderForPostVertexCache(e.geometry),c.GeometryPipeline.reorderForPreVertexCache(e.geometry)):o.defined(e.westHemisphereGeometry)&&o.defined(e.eastHemisphereGeometry)&&(c.GeometryPipeline.reorderForPostVertexCache(e.westHemisphereGeometry),c.GeometryPipeline.reorderForPreVertexCache(e.westHemisphereGeometry),c.GeometryPipeline.reorderForPostVertexCache(e.eastHemisphereGeometry),c.GeometryPipeline.reorderForPreVertexCache(e.eastHemisphereGeometry))}let y=c.GeometryPipeline.combineInstances(i);for(g=y.length,m=0;m<g;++m){h=y[m];const e=h.attributes;if(d)for(const t in e)e.hasOwnProperty(t)&&e[t].componentDatatype===n.ComponentDatatype.DOUBLE&&c.GeometryPipeline.encodeAttribute(h,t,`${t}3DHigh`,`${t}3DLow`);else for(const r in e)if(e.hasOwnProperty(r)&&e[r].componentDatatype===n.ComponentDatatype.DOUBLE){const e=`${r}3D`,n=`${r}2D`;c.GeometryPipeline.projectTo2D(h,r,e,n,s),o.defined(h.boundingSphere)&&"position"===r&&(h.boundingSphereCV=t.BoundingSphere.fromVertices(h.attributes.position2D.values)),c.GeometryPipeline.encodeAttribute(h,e,`${e}High`,`${e}Low`),c.GeometryPipeline.encodeAttribute(h,n,`${n}High`,`${n}Low`)}f&&c.GeometryPipeline.compressVertices(h)}if(!a){let e=[];for(g=y.length,m=0;m<g;++m)h=y[m],e=e.concat(c.GeometryPipeline.fitToUnsignedShortIndices(h));y=e}return y}function l(e,t,n,r){let i,s,c;const a=r.length-1;if(a>=0){const e=r[a];i=e.offset+e.count,c=e.index,s=n[c].indices.length}else i=0,c=0,s=n[c].indices.length;const d=e.length;for(let a=0;a<d;++a){const d=e[a][t];if(!o.defined(d))continue;const p=d.indices.length;i+p>s&&(i=0,s=n[++c].indices.length),r.push({index:c,offset:i,count:p}),i+=p}}Object.defineProperties(p.prototype,{componentDatatype:{get:function(){return n.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),p.fromCartesian3=function(e){return new p(e.x,e.y,e.z)},p.toValue=function(e,t){return o.defined(t)||(t=new Float32Array([e.x,e.y,e.z])),t[0]=e.x,t[1]=e.y,t[2]=e.z,t};const m={};function h(e,t){const n=e.attributes;for(const e in n)if(n.hasOwnProperty(e)){const r=n[e];o.defined(r)&&o.defined(r.values)&&t.push(r.values.buffer)}o.defined(e.indices)&&t.push(e.indices.buffer)}function g(e,t){const n=e.length,i=new Float64Array(1+19*n);let s=0;i[s++]=n;for(let t=0;t<n;t++){const n=e[t];if(r.Matrix4.pack(n.modelMatrix,i,s),s+=r.Matrix4.packedLength,o.defined(n.attributes)&&o.defined(n.attributes.offset)){const e=n.attributes.offset.value;i[s]=e[0],i[s+1]=e[1],i[s+2]=e[2]}s+=3}return t.push(i.buffer),i}function y(e){const n=e.length,r=1+(t.BoundingSphere.packedLength+1)*n,i=new Float32Array(r);let s=0;i[s++]=n;for(let r=0;r<n;++r){const n=e[r];o.defined(n)?(i[s++]=1,t.BoundingSphere.pack(e[r],i,s)):i[s++]=0,s+=t.BoundingSphere.packedLength}return i}function b(e){const n=new Array(e[0]);let o=0,r=1;for(;r<e.length;)1===e[r++]&&(n[o]=t.BoundingSphere.unpack(e,r)),++o,r+=t.BoundingSphere.packedLength;return n}m.combineGeometry=function(e){let n,r;const i=e.instances,s=i.length;let a,d,p=!1;s>0&&(n=f(e),n.length>0&&(r=c.GeometryPipeline.createAttributeLocations(n[0]),e.createPickOffsets&&(a=function(e,t){const n=[];return l(e,"geometry",t,n),l(e,"westHemisphereGeometry",t,n),l(e,"eastHemisphereGeometry",t,n),n}(i,n))),o.defined(i[0].attributes)&&o.defined(i[0].attributes.offset)&&(d=new Array(s),p=!0));const u=new Array(s),m=new Array(s);for(let e=0;e<s;++e){const n=i[e],r=n.geometry;o.defined(r)&&(u[e]=r.boundingSphere,m[e]=r.boundingSphereCV,p&&(d[e]=n.geometry.offsetAttribute));const s=n.eastHemisphereGeometry,c=n.westHemisphereGeometry;o.defined(s)&&o.defined(c)&&(o.defined(s.boundingSphere)&&o.defined(c.boundingSphere)&&(u[e]=t.BoundingSphere.union(s.boundingSphere,c.boundingSphere)),o.defined(s.boundingSphereCV)&&o.defined(c.boundingSphereCV)&&(m[e]=t.BoundingSphere.union(s.boundingSphereCV,c.boundingSphereCV)))}return{geometries:n,modelMatrix:e.modelMatrix,attributeLocations:r,pickOffsets:a,offsetInstanceExtend:d,boundingSpheres:u,boundingSpheresCV:m}},m.packCreateGeometryResults=function(e,n){const r=new Float64Array(function(e){let n=1;const r=e.length;for(let i=0;i<r;i++){const r=e[i];if(++n,!o.defined(r))continue;const s=r.attributes;n+=7+2*t.BoundingSphere.packedLength+(o.defined(r.indices)?r.indices.length:0);for(const e in s)s.hasOwnProperty(e)&&o.defined(s[e])&&(n+=5+s[e].values.length)}return n}(e)),i=[],s={},c=e.length;let a=0;r[a++]=c;for(let n=0;n<c;n++){const c=e[n],d=o.defined(c);if(r[a++]=d?1:0,!d)continue;r[a++]=c.primitiveType,r[a++]=c.geometryType,r[a++]=o.defaultValue(c.offsetAttribute,-1);const p=o.defined(c.boundingSphere)?1:0;r[a++]=p,p&&t.BoundingSphere.pack(c.boundingSphere,r,a),a+=t.BoundingSphere.packedLength;const u=o.defined(c.boundingSphereCV)?1:0;r[a++]=u,u&&t.BoundingSphere.pack(c.boundingSphereCV,r,a),a+=t.BoundingSphere.packedLength;const f=c.attributes,l=[];for(const e in f)f.hasOwnProperty(e)&&o.defined(f[e])&&(l.push(e),o.defined(s[e])||(s[e]=i.length,i.push(e)));r[a++]=l.length;for(let e=0;e<l.length;e++){const t=l[e],n=f[t];r[a++]=s[t],r[a++]=n.componentDatatype,r[a++]=n.componentsPerAttribute,r[a++]=n.normalize?1:0,r[a++]=n.values.length,r.set(n.values,a),a+=n.values.length}const m=o.defined(c.indices)?c.indices.length:0;r[a++]=m,m>0&&(r.set(c.indices,a),a+=m)}return n.push(r.buffer),{stringTable:i,packedData:r}},m.unpackCreateGeometryResults=function(e){const o=e.stringTable,r=e.packedData;let c;const d=new Array(r[0]);let p=0,u=1;for(;u<r.length;){if(!(1===r[u++])){d[p++]=void 0;continue}const e=r[u++],f=r[u++];let l,m,h=r[u++];-1===h&&(h=void 0);1===r[u++]&&(l=t.BoundingSphere.unpack(r,u)),u+=t.BoundingSphere.packedLength;let g,y,b;1===r[u++]&&(m=t.BoundingSphere.unpack(r,u)),u+=t.BoundingSphere.packedLength;const G=new s.GeometryAttributes,x=r[u++];for(c=0;c<x;c++){const e=o[r[u++]],t=r[u++];b=r[u++];const s=0!==r[u++];g=r[u++],y=n.ComponentDatatype.createTypedArray(t,g);for(let e=0;e<g;e++)y[e]=r[u++];G[e]=new i.GeometryAttribute({componentDatatype:t,componentsPerAttribute:b,normalize:s,values:y})}let S;if(g=r[u++],g>0){const e=y.length/b;for(S=a.IndexDatatype.createTypedArray(e,g),c=0;c<g;c++)S[c]=r[u++]}d[p++]=new i.Geometry({primitiveType:e,geometryType:f,boundingSphere:l,boundingSphereCV:m,indices:S,attributes:G,offsetAttribute:h})}return d},m.packCombineGeometryParameters=function(e,n){const o=e.createGeometryResults,r=o.length;for(let e=0;e<r;e++)n.push(o[e].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:g(e.instances,n),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof t.GeographicProjection,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},m.unpackCombineGeometryParameters=function(e){const n=function(e){const t=e,n=new Array(t[0]);let i=0,s=1;for(;s<t.length;){const e=r.Matrix4.unpack(t,s);let c;s+=r.Matrix4.packedLength,o.defined(t[s])&&(c={offset:new p(t[s],t[s+1],t[s+2])}),s+=3,n[i++]={modelMatrix:e,attributes:c}}return n}(e.packedInstances),i=e.createGeometryResults,s=i.length;let c=0;for(let e=0;e<s;e++){const t=m.unpackCreateGeometryResults(i[e]),o=t.length;for(let e=0;e<o;e++){const o=t[e];n[c].geometry=o,++c}}const a=r.Ellipsoid.clone(e.ellipsoid);return{instances:n,ellipsoid:a,projection:e.isGeographic?new t.GeographicProjection(a):new d.WebMercatorProjection(a),elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:r.Matrix4.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},m.packCombineGeometryResults=function(e,t){o.defined(e.geometries)&&function(e,t){const n=e.length;for(let o=0;o<n;++o)h(e[o],t)}(e.geometries,t);const n=y(e.boundingSpheres),r=y(e.boundingSpheresCV);return t.push(n.buffer,r.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:n,boundingSpheresCV:r}},m.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:b(e.boundingSpheres),boundingSpheresCV:b(e.boundingSpheresCV)}};var G=m;e.PrimitivePipeline=G}));
