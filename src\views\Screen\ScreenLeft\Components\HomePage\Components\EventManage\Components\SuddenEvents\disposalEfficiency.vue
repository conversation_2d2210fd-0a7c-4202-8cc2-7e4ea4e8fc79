<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 15:16:09
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-23 17:01:42
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenLeft\Components\HomePage\Components\EventManage\Components\SuddenEvents\disposalEfficiency.vue
 * @Description:
 *
-->
<template>
  <div class="disposalEfficiency-container">
    <SubHeadLine>
      处置效率
      <span style="font-size: 12px">{{ eventTypeName }}</span>
    </SubHeadLine>
    <!-- <pieChart :chartData="props.disposalEfficiencyList" :chartTitle="'效率'" :chartUnit="'%'"></pieChart> -->
    <ThreePie :data="props.disposalEfficiencyList" :chartTitle="'效率'" :chartUnit="'%'"></ThreePie>
  </div>
</template>

<script setup>
  import pieChart from '../EmergencyResources/pieChart.vue'
  import SubHeadLine from '@/components/SubHeadLine/index.vue'
  import ThreePie from '../EmergencyResources/threePie.vue'
  const props = defineProps({
    disposalEfficiencyList: {
      type: Array,
      default: () => []
    },
    eventTypeName: {
      type: String,
      default: () => ''
    }
  })
  // const chartData = ref([
  //   { value: 50, name: '60分钟以上' },
  //   { value: 20, name: '30-60分钟' },
  //   { value: 30, name: '0-30分钟' }
  // ])
</script>

<style lang="scss" scoped>
  .disposalEfficiency-container {
    width: 288px;
    height: 180px;
  }
</style>
