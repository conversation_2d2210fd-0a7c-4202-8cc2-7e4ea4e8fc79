<template>
  <div class="popup-bg-row">
    <PopupBg title="设施档案" width="560px" @close="handlerClose">
      <template #customContent>
        <div class="video-btn" v-if="hasVideo" @click="handlerVideo">
          <el-icon size="22px" color="#01aef4" style="width: 22px; margin-right: 10px"><VideoCameraFilled  />
          </el-icon>视频
        </div>
      </template>
      <div class="main">
        <div class="tab-box">
          <div class="tab-item" :class="{ active: item.active }" v-for="(item, index) in tabData" :key="index" @click="handlerTab(item)">{{ item.name }}</div>
        </div>
        <DatePicker
          v-show="tabData[1].active || tabData[2].active"
          v-model="date"
          type="year"
          valueFormat="YYYY"
          @change="handleDateChange"
          :clearable="false"
          :disabled-date="disabledDate"
          style="top: 92px; right: 13px"></DatePicker>
        <TableList
          style="margin-top: 62px"
          listWidth="540px"
          v-if="tabData[1].active || tabData[2].active || tabData[3].active"
          :head-list="tableHeadList"
          :list="tableDataList"></TableList>
        <InfoCom v-else :info-data="infoData"></InfoCom>
      </div>
    </PopupBg>
    <PopupBg v-if="showVideo" title="视频监控" width="400px" class="video-popup-bg" @close="showVideo = false">
      <div style="width:100%;height:200px;display:flex;align-items:center;justify-content:center;background:#000;">
        <video v-if="videoUrl" :src="videoUrl" controls autoplay style="width:100%;height:100%;object-fit:contain;background:#000;" />
        <div v-else style="color:#fff;font-size:16px;">暂无视频</div>
      </div>
    </PopupBg>
  </div>
</template>

<script setup>
  import moment from 'moment'
  import DatePicker from '@/components/DatePicker/index.vue'

  import InfoCom from '@/components/InfoCom/index.vue'
  import TableList from '@/components/TableList/index.vue'
  import PopupBg from '@/components/PopupBg/index.vue'
  import PopupSubTitle from '@/components/PopupSubTitle/index.vue'

  import lib from '@/utils/lib'
  
  import { VideoCameraFilled  } from '@element-plus/icons-vue'
  import { ref, watch } from 'vue'

  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const date = ref(moment().format('YYYY'))
  const handleDateChange = (val) => {
    if (tabData.value[1].active) {
      getYhPlanData()
    } else if (tabData.value[2].active) {
      getWyWorkData()
    }
  }
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
  const hasVideo = ref(false)
  const showVideo = ref(false)
  const videoUrl = ref('')
  const handlerVideo = () => {
    // videoUrl.value = props.data.videoUrl || ''
    showVideo.value = true
  }
  const tabData = ref([
    { name: '基本信息', active: true },
    { name: '养护计划', active: false },
    { name: '维养作业', active: false },
    { name: '缺陷记录', active: false }
    // { name: '结构报警', active: false }
  ])
  const tableHeadList = ref([])
  const tableDataList = ref([])
  const infoData = ref([])
  const resultData = ref([])
  const setInfoData = (data, resultData) => {
    data.value.forEach((item) => {
      if (item.type == 'date') {
        item.value = moment(resultData.value?.structure[item.prop]).format('YYYY-MM-DD')
      } else {
        item.value = resultData.value?.structure[item.prop]
      }
    })
  }
  const handlerTab = (item) => {
    tableDataList.value = []
    tabData.value.forEach((data) => {
      data.active = data.name === item.name
    })
    switch (item.name) {
      case '基本信息':
        infoData.value = [
          { name: '设施类型', prop: 'structureTypeName', value: '---' },
          { name: '设施名称', prop: 'name', value: '---' },
          { name: '备注', prop: 'remark', value: '---' },
          { name: '设施编码', prop: 'code', value: '---' },
          { name: '起始里程', prop: 'startMileage', value: '---' },
          { name: '终止里程', prop: 'endMileage', value: '---' },
          { name: '更新时间', prop: 'completeDate', value: '---' },
          { name: '当前寿命', prop: 'currentLife', value: '---', unit: '个月' },
          { name: '剩余寿命', prop: 'restLife', value: '---', unit: '个月' },
          { name: '使用年限', prop: 'ageLimit', value: '---', unit: '年' }
        ]
        setInfoData(infoData, resultData)
        break

      case '养护计划':
        tableHeadList.value = [
          { label: '年份', prop: 'year' },
          { label: '计划类型', prop: 'workOrderType' },
          { label: '维修养护内容', prop: 'workPlanContent', dicp: 'work_plan_content' },
          { label: '计划名称', prop: 'name' },
          { label: '实施频次', prop: 'frequencyNumber', unit: '次/月' },
          { label: '完成率', prop: 'completionRate', unit: '%' }
        ]
        getYhPlanData()
        console.log('养护计划----', tableHeadList.value, tableDataList.value)
        break

      case '维养作业':
        tableHeadList.value = [
          { label: '计划作业日期', prop: 'startDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '作业单名称', prop: 'name' },
          { label: '作业单类型', prop: 'type', dicp: 'work_order_type' },
          { label: '实际开工时间', prop: 'realStartDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '验收时间', prop: 'acceptDate', formatDate: 'YYYY-MM-DD HH:mm:ss' }
        ]
        getWyWorkData()
        console.log('维养作业----', tableHeadList.value, tableDataList.value)
        break

      case '缺陷记录':
        tableHeadList.value = [
          { label: '发现时间', prop: 'discoveryDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '发现人', prop: 'discoveryUserName' },
          { label: '缺陷来源', prop: 'source', dicp: 'defect_source' },
          { label: '缺陷类型', prop: 'defectTypeName' },
          { label: '养护策略', prop: 'maintenanceStrategy', dicp: 'maintenance_strategy' },
          { label: '缺陷状态', prop: 'status', dicp: 'defect_status' }
        ]
        lib.api.getPopupDetail.structureResumeCardDefect({ code: props.data.code }).then((res) => {
          if (res.success && res.result) {
            tableDataList.value = res.result?.defectList
          }
        })
        console.log('缺陷记录----', tableHeadList.value, tableDataList.value)
        break

      case '结构报警':
        tableHeadList.value = [
          { label: '报警时间', prop: 'startDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '紧急程度', prop: 'alertLevel' },
          { label: '状态', prop: 'dealStatus' },
          { label: '报警判断', prop: 'alertStatus' }
        ]
        lib.api.getPopupDetail.structureResumeCardStructureAlert({ code: props.data.code }).then((res) => {
          if (res.success && res.result) {
            tableDataList.value = res.result?.structureAlert
          }
        })
        console.log('结构报警----', tableHeadList.value, tableDataList.value)
        break
      default:
        break
    }
  }
  // 养护作业单类型
  const getYhPlanData = () => {
    lib.api.getPopupDetail.structureResumeCardMaintenanceWorkPlan({ code: props.data.code, years: [date.value] }).then((res) => {
      if (res.success && res.result) {
        tableDataList.value = res.result?.maintenanceWorkPlan.map((_) => {
          return {
            ..._,
            completionRate: _.year < moment().format('YYYY') ? '100' : _.completionRate //历史完成率写死100%
          }
        })
      }
    })
  }
  // 维养作业
  const getWyWorkData = () => {
    lib.api.getPopupDetail
      .structureResumeCardMainTenanceOrder({ code: props.data.code, startDate: date.value + '-01-01 00:00:00', endDate: date.value + '-12-31 23:59:59' })
      .then((res) => {
        if (res.success && res.result) {
          tableDataList.value = res.result?.mainTenanceOrderList
        }
      })
  }
  const handlerClose = () => {
    // 关闭弹窗
    lib.popWindow.removeDialog('structureWindow')
  }
  watch(
    () => props.data,
    (newValue) => {
      if (newValue) {
        // hasVideo.value = !!newValue.videoUrl
        // 获取设施详情
        resultData.value = newValue
        handlerTab(tabData.value[0])
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<style lang="scss" scoped>
.popup-bg-row {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  position: relative;
  z-index: 1000;
}
.video-popup-bg {
  // 可自定义右侧弹窗样式
}
.video-btn {
  display: flex;
  align-items: center;
  height: 31px;
  margin-left: 10px;
  padding: 0 10px;
  line-height: 31px;
  text-align: center;
  cursor: pointer;
  color: #01aef4;
  background-color: #023968;
  border-radius: 4px;
}
.top {
  position: relative;
  width: 560px;
  height: 53px;
  padding: 7px 300px 17px 21px;
  font-family: PangMenZhengDao;
  font-size: 24px;
  font-weight: 400;
  line-height: 28px;
  color: #ffffff;

  // background: url('@/assets/CommonPopup/top-img.png') no-repeat;
  // background-size: 100% 100%;
  .close {
    position: absolute;
    top: 11px;
    right: 13px;
    width: 13px;
    height: 14px;
    cursor: pointer;
  }
}
.main {
  flex: 1;
  width: 100%;
  padding: 0 16px;

  // background: url('@/assets/CommonPopup/middle-img.png');
  // background-size: 100% 100%;
}
.tab-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .tab-item {
    height: 31px;
    font-family: 'Source Han Sans CN';
    font-size: 22px;

    // font-weight: 500;
    line-height: 31px;
    color: #bcddff;
    text-align: center;
    cursor: pointer;
    &.active {
      height: 31px;

      // width: 70px;
      // height: 24px;
      padding: 0 7px;

      // background: #00b2ff;
      // border-radius: 4px;
      font-weight: bold;
      line-height: 31px;
      background: url('@/assets/CommonPopup/tab-selected.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

// .bottom {
//   width: 560px;
//   height: 55px;
//   background: url('@/assets/CommonPopup/bottom-img.png') no-repeat;
//   background-size: cover;
// }
// }
</style>
