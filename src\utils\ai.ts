import { showPopWindow, closePopWindowById, closePopWindowByTag, type PopWindowProps } from 'znyg-frontend-common'
import lib from './lib'
import VideoPopup from '@/views/Screen/PopupWindow/VideoPopup/index.vue'

export const openVideo = (name) => {
  console.log('打开摄像头' + name)
  const videoUrlMap = {
    江南: 'http://172.22.51.3:10000/hls/test17-2.m3u8',
    江北: 'http://172.22.51.3:10000/hls/test15-2.m3u8'
  }

  closePopWindowByTag(lib.enumMap.PopWindowTag.视频弹窗)
  // 视频弹窗
  const op: PopWindowProps = {
    left: 1816,
    top: 330,
    tag: lib.enumMap.PopWindowTag.视频弹窗,
    // zIndex: 999,
    draggable: true
  }
  showPopWindow(op, VideoPopup, { title: name, url: videoUrlMap[name] })
}

export const startTrafficStreamCommon = (enable?: boolean, socketUrl?: string, topic?: string, usr?: string, pwd?: string) => {
  lib.store().storeScreenData.vehicleTwinOpen = enable
  if (enable) {
    lib._engineController.romaPause()
  }
  lib._engineController.startTrafficStreamCommon(enable, socketUrl, topic, usr, pwd)
  // lib._engineController.startTrafficStreamCommon(true, 'ws://172.22.51.1:10000/mqtt/publish', 'tessng', 'mosquito', 'Cjxx-2024')
}
