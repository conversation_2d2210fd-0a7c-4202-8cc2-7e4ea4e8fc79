define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84"],(function(e,t,i,r,a,n,o,s,u,m,f,d){"use strict";var l=new r.Cartesian3(1,1,1),c=Math.cos,p=Math.sin;function C(e){e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT);var a=t.defaultValue(e.radii,l),n=t.defaultValue(e.innerRadii,a),o=t.defaultValue(e.minimumClock,0),s=t.defaultValue(e.maximumClock,r.CesiumMath.TWO_PI),u=t.defaultValue(e.minimumCone,0),m=t.defaultValue(e.maximumCone,r.CesiumMath.PI),f=Math.round(t.defaultValue(e.stackPartitions,10)),c=Math.round(t.defaultValue(e.slicePartitions,8)),p=Math.round(t.defaultValue(e.subdivisions,128));if(f<1)throw new i.DeveloperError("options.stackPartitions cannot be less than 1");if(c<0)throw new i.DeveloperError("options.slicePartitions cannot be less than 0");if(p<0)throw new i.DeveloperError("options.subdivisions must be greater than or equal to zero.");if(t.defined(e.offsetAttribute)&&e.offsetAttribute===d.GeometryOffsetAttribute.TOP)throw new i.DeveloperError("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._radii=r.Cartesian3.clone(a),this._innerRadii=r.Cartesian3.clone(n),this._minimumClock=o,this._maximumClock=s,this._minimumCone=u,this._maximumCone=m,this._stackPartitions=f,this._slicePartitions=c,this._subdivisions=p,this._offsetAttribute=e.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}C.packedLength=2*r.Cartesian3.packedLength+8,C.pack=function(e,a,n){if(!t.defined(e))throw new i.DeveloperError("value is required");if(!t.defined(a))throw new i.DeveloperError("array is required");return n=t.defaultValue(n,0),r.Cartesian3.pack(e._radii,a,n),n+=r.Cartesian3.packedLength,r.Cartesian3.pack(e._innerRadii,a,n),n+=r.Cartesian3.packedLength,a[n++]=e._minimumClock,a[n++]=e._maximumClock,a[n++]=e._minimumCone,a[n++]=e._maximumCone,a[n++]=e._stackPartitions,a[n++]=e._slicePartitions,a[n++]=e._subdivisions,a[n]=t.defaultValue(e._offsetAttribute,-1),a};var h=new r.Cartesian3,_=new r.Cartesian3,v={radii:h,innerRadii:_,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};C.unpack=function(e,a,n){if(!t.defined(e))throw new i.DeveloperError("array is required");a=t.defaultValue(a,0);var o=r.Cartesian3.unpack(e,a,h);a+=r.Cartesian3.packedLength;var s=r.Cartesian3.unpack(e,a,_);a+=r.Cartesian3.packedLength;var u=e[a++],m=e[a++],f=e[a++],d=e[a++],l=e[a++],c=e[a++],p=e[a++],b=e[a];return t.defined(n)?(n._radii=r.Cartesian3.clone(o,n._radii),n._innerRadii=r.Cartesian3.clone(s,n._innerRadii),n._minimumClock=u,n._maximumClock=m,n._minimumCone=f,n._maximumCone=d,n._stackPartitions=l,n._slicePartitions=c,n._subdivisions=p,n._offsetAttribute=-1===b?void 0:b,n):(v.minimumClock=u,v.maximumClock=m,v.minimumCone=f,v.maximumCone=d,v.stackPartitions=l,v.slicePartitions=c,v.subdivisions=p,v.offsetAttribute=-1===b?void 0:b,new C(v))},C.createGeometry=function(e){var i=e._radii;if(!(i.x<=0||i.y<=0||i.z<=0)){var l=e._innerRadii;if(!(l.x<=0||l.y<=0||l.z<=0)){var C=e._minimumClock,h=e._maximumClock,_=e._minimumCone,v=e._maximumCone,b=e._subdivisions,y=a.Ellipsoid.fromCartesian3(i),k=e._slicePartitions+1,A=e._stackPartitions+1;k=Math.round(k*Math.abs(h-C)/r.CesiumMath.TWO_PI),A=Math.round(A*Math.abs(v-_)/r.CesiumMath.PI),k<2&&(k=2),A<2&&(A=2);var w=0,P=1,x=l.x!==i.x||l.y!==i.y||l.z!==i.z,E=!1,g=!1;x&&(P=2,_>0&&(E=!0,w+=k),v<Math.PI&&(g=!0,w+=k));var D,M,G,O,V=b*P*(A+k),T=new Float64Array(3*V),z=2*(V+w-(k+A)*P),I=f.IndexDatatype.createTypedArray(V,z),L=0,R=new Array(A),N=new Array(A);for(D=0;D<A;D++)O=_+D*(v-_)/(A-1),R[D]=p(O),N[D]=c(O);var B=new Array(b),S=new Array(b);for(D=0;D<b;D++)G=C+D*(h-C)/(b-1),B[D]=p(G),S[D]=c(G);for(D=0;D<A;D++)for(M=0;M<b;M++)T[L++]=i.x*R[D]*S[M],T[L++]=i.y*R[D]*B[M],T[L++]=i.z*N[D];if(x)for(D=0;D<A;D++)for(M=0;M<b;M++)T[L++]=l.x*R[D]*S[M],T[L++]=l.y*R[D]*B[M],T[L++]=l.z*N[D];for(R.length=b,N.length=b,D=0;D<b;D++)O=_+D*(v-_)/(b-1),R[D]=p(O),N[D]=c(O);for(B.length=k,S.length=k,D=0;D<k;D++)G=C+D*(h-C)/(k-1),B[D]=p(G),S[D]=c(G);for(D=0;D<b;D++)for(M=0;M<k;M++)T[L++]=i.x*R[D]*S[M],T[L++]=i.y*R[D]*B[M],T[L++]=i.z*N[D];if(x)for(D=0;D<b;D++)for(M=0;M<k;M++)T[L++]=l.x*R[D]*S[M],T[L++]=l.y*R[D]*B[M],T[L++]=l.z*N[D];for(L=0,D=0;D<A*P;D++){var q=D*b;for(M=0;M<b-1;M++)I[L++]=q+M,I[L++]=q+M+1}var U=A*b*P;for(D=0;D<k;D++)for(M=0;M<b-1;M++)I[L++]=U+D+M*k,I[L++]=U+D+(M+1)*k;if(x)for(U=A*b*P+k*b,D=0;D<k;D++)for(M=0;M<b-1;M++)I[L++]=U+D+M*k,I[L++]=U+D+(M+1)*k;if(x){var F=A*b*P,W=F+b*k;if(E)for(D=0;D<k;D++)I[L++]=F+D,I[L++]=W+D;if(g)for(F+=b*k-k,W+=b*k-k,D=0;D<k;D++)I[L++]=F+D,I[L++]=W+D}var Y=new m.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:T})});if(t.defined(e._offsetAttribute)){var J=T.length,j=new Uint8Array(J/3),H=e._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;d.arrayFill(j,H),Y.applyOffset=new s.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:j})}return new s.Geometry({attributes:Y,indices:I,primitiveType:u.PrimitiveType.LINES,boundingSphere:n.BoundingSphere.fromEllipsoid(y),offsetAttribute:e._offsetAttribute})}}},e.EllipsoidOutlineGeometry=C}));