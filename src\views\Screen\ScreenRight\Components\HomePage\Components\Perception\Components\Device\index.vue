<!--
 * @Author: lugege <EMAIL>
 * @Date: 2024-05-08 18:29:13
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-04-24 15:58:02
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\Components\HomePage\Components\Perception\Components\Device\index.vue
 * @Description:
 *
-->
<template>
  <div class="device-container">
    <!-- <SubHeadLine>设备概况</SubHeadLine> -->
    <div class="head" v-show-ai-question @click="handleTitleClick">设备概况</div>
    <OpenPage style="top: 12px; left: 218px" :path="'/inventory/assets'"></OpenPage>
    <div class="content">
      <DeviceSummary></DeviceSummary>
      <TreeTableStructure :data="structureData.treeTableList" :tableHeaderList="structureData.tableHeaderList" type="device"></TreeTableStructure>
      <DeviceLife></DeviceLife>
    </div>
  </div>
</template>

<script setup>
  import { onMounted } from 'vue'
  import { useIntervalFn } from '@vueuse/core'

  import DeviceLife from './deviceLife.vue'
  import DeviceSummary from './deviceSummary.vue'
  import TreeTableStructure from './TreeTableStructure.vue'
  import OpenPage from '@/components/OpenPage/index.vue'
  import lib from '@/utils/lib'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'
  defineOptions({
    name: 'Device'
  })

  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleTitleClick = () => {
    if (showAIQuestion.value) {
      lib.utils.sendQuestionToAIChat('作业管控')
    }
  }
  const structureData = reactive({
    tableHeaderList: ['设备类型', '设备数量', '故障数量'],
    // 静态数据
    treeTableList: []
  })
  onMounted(() => {
    // getData()
    useIntervalFn(
      () => {
        getData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })
  const getData = () => {
    lib.api.pdRoadDeviceAlertApi
      .getDeviceList({
        showDevice: true // false不显示最后一层
      })
      .then((res) => {
        if (res.success) {
          removeZeroCountNodes(res.result.thisChildList)
          // 调用函数遍历树形结构并设置 bugCount
          traverseAndSetBugCount(res.result.thisChildList)
          structureData.treeTableList = res.result.thisChildList
        }
      })
  }
  const removeZeroCountNodes = (nodes) => {
    // 逆序遍历数组 nodes 避免修改数组时对遍历造成影响
    for (let i = nodes.length - 1; i >= 0; i--) {
      const node = nodes[i]
      if (node.count === 0) {
        if (node.thisChildList && node.thisChildList.every((child) => child.count === 0)) {
          node.thisChildList = []
        }
        //  如果当前对象的count为0，移除此节点
        if (node.count === 0) {
          nodes.splice(i, 1)
        }
      }
      if (node.thisChildList) {
        removeZeroCountNodes(node.thisChildList)
      }
    }
  }
  const setBugCountForDefect = (node) => {
    if (node.thisChildList && node.thisChildList.length > 0) {
      for (const child of node.thisChildList) {
        setBugCountForDefect(child)
      }
    } else {
      if (node.defect === true) {
        node.bugCount = 1
      }
    }
  }
  // 遍历树形结构，并处理最里层的节点
  const traverseAndSetBugCount = (nodes) => {
    for (const node of nodes) {
      setBugCountForDefect(node)
    }
  }
</script>

<style lang="scss" scoped>
  .device-container {
    .head {
      width: 216px;
      height: 50px;
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      font-weight: 400;
      line-height: 45px;
      color: #d4e9ff;
      text-align: center;
      background: url('@/assets/ScreenRight/Perception/Device/head.png') no-repeat;
      background-size: 100% 100%;
    }
    .content {
      width: 100%;
      height: 640px;
      padding-top: 14px;
    }
  }
</style>
