<template>
  <div class="analysis-container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="subtitle-container" id="analysis-container">
      结构监测分析
      <SelectList
        v-if="selectCode != 'cjjc'"
        class="btn-wrap-list"
        :option-list="optionList"
        v-model:select-value="deviceSystemId"
        :clearable="false"
        width="150px"
        @change="handleChangeSelectList"
        :property-name="propertyName"
        filterable></SelectList>
      <SwitchButton class="btn-wrap" width="270px" height="22px" v-model="btnList" @handle-mouse-enter-pop="handleMouseEnter"></SwitchButton>
    </div>
    <div class="relative">
      <MyChart width="600px" height="240px" :option="options" style="position: sticky; z-index: 3; margin-top: 10px"></MyChart>
      <div class="chartBg"></div>
    </div>
    <el-popover placement="top" popper-class="analysis-summary-popover" :width="300" trigger="hover" v-if="popoverContent">
      <span v-html="popoverContent"></span>
      <template #reference>
        <el-icon class="!c-white cursor-pointer !absolute top-45 left-130 z-3"><QuestionFilled /></el-icon>
      </template>
    </el-popover>
  </div>
</template>
<script setup>
  import { computed, onMounted } from 'vue'
  import * as echarts from 'echarts'
  import moment from 'moment'
  import { useIntervalFn } from '@vueuse/core'

  import SelectList from '@/components/SelectList/index.vue'
  import SwitchButton from '@/components/SwitchButton/index.vue'
  import MyChart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib'
  const btnList = ref([
    { id: 1, name: '沉降', code: 'cjjc', selected: true, subList: [] },
    { id: 2, name: '收敛', code: 'sljc', selected: false, subList: [] },
    { id: 3, name: '变形', code: 'bxjc', selected: false, subList: [] },
    { id: 4, name: '渗漏水', code: 'slsjc', selected: false, subList: [] }
  ])

  const propertyName = ref({
    id: 'code',
    name: 'deviceName'
  })
  const optionList = ref([]) // 设备列表
  const deviceSystemId = ref(null) // 设备code
  const deviceItem = ref({})

  const selectCode = computed(() => {
    return btnList.value.find((item) => item.selected).code
  })
  const selectId = computed(() => {
    const selectItem = btnList.value.find((item) => item.selected).subList
    console.log('sellll', selectItem)
    if (selectItem.length != 0) {
      return selectItem.find((_) => _.selected).id
    } else {
      return ''
    }
  })
  const popoverContent = computed(() => {
    let suffix = ''
    const selectItem = btnList.value.find((item) => item.selected)
    if (selectItem.name == '沉降') {
      if (deviceItem.value && deviceItem.value.maxValue && deviceItem.value.maxLimit) {
        suffix = `<br />一级阈值${deviceItem.value.maxValue}${deviceItem.value.unit || ''}<br />二级阈值${deviceItem.value.maxLimit}${
          deviceItem.value.unit || ''
        }`
      }
      return '参考《城市轨道交通工程监测技术规范》(GB 50911-2013）<br />正为上浮，负为下沉。' + suffix
    } else if (selectItem.name == '收敛') {
      if (deviceItem.value && deviceItem.value.maxValue && deviceItem.value.maxLimit) {
        suffix = `<br />一级阈值：${deviceItem.value.maxValue}${deviceItem.value.unit || ''}<br />二级阈值：${deviceItem.value.maxLimit}${
          deviceItem.value.unit || ''
        }`
      }
      return '正为横径变大，负为横径变小。' + suffix
    } else if (selectItem.name == '变形') {
      if (deviceItem.value && deviceItem.value.maxValue && deviceItem.value.maxLimit) {
        return `参考《盾构法隧道施工及验收规范》(GB50446-2017)<br />张开（一级阈值${deviceItem.value.maxValue}${deviceItem.value.unit || ''}，二级阈值${deviceItem.value.maxLimit}${
          deviceItem.value.unit || ''
        }）：正为张开，负为收缩。<br />错台（一级阈值4.2${deviceItem.value.unit || ''}，二级阈值6${
          deviceItem.value.unit || ''
        }）：正为盾构段径向向外位移，负为盾构段径向向内位移。<br />错动（一级阈值4.9${deviceItem.value.unit || ''}，二级阈值7${
          deviceItem.value.unit || ''
        }）：正为盾构段竖直向上位移，负为盾构段竖直向下位移。`
      }
      return '参考《盾构法隧道施工及验收规范》(GB50446-2017)<br />张开：正为张开，负为收缩，错台：正为盾构段径向向外位移，负为盾构段径向向内位移，错动：正为盾构段竖直向上位移，负为盾构段竖直向下位移。'
    }
    return ''
  })
  const options = ref({
    grid: {
      top: '35px',
      bottom: '0px',
      left: '20px',
      right: '0px',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      show: false,
      top: '20',
      right: '50',
      data: ['沉降'],
      textStyle: {
        color: '#fff',
        fontSize: 14
      }
    },
    xAxis: {
      // type: 'category',
      // boundaryGap: true, // 坐标轴两边留白
      // data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
      // axisLabel: {
      //   interval: 0,
      //   rotate: 15,
      //   //	margin:15,
      //   textStyle: {
      //     color: '#ffffff',
      //     fontSize: 16
      //   }
      // },
      // axisTick: {
      //   show: false
      // }
      type: 'category',
      boundaryGap: true, // 坐标轴两边留白
      data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
      axisLabel: {
        // interval: 0,
        //	margin:15,
        rotate: 15,
        textStyle: {
          color: '#D4EFFF',
          fontSize: 16
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        name: '单位（mm）',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 16,
          padding: [0, 0, 0, 50]
        },
        type: 'value',
        splitNumber: 5,
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: 16
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.2)',
            type: 'dashed'
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      }
    ],
    series: [
      // {
      //   name: '沉降',
      //   type: 'line',
      //   smooth: true,
      //   symbolSize: 5,
      //   itemStyle: {
      //     normal: {
      //       color: '#00F0FF',
      //       lineStyle: {
      //         color: '#00F0FF',
      //         width: 2
      //       },
      //       areaStyle: {
      //         color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
      //           {
      //             offset: 0,
      //             color: 'rgba(0, 240, 255, 0)'
      //           },
      //           {
      //             offset: 1,
      //             color: 'rgba(0, 240, 255, 1)'
      //           }
      //         ])
      //       }
      //     }
      //   },
      //   data: [58, 60, 85, 65, 55, 90, 45, 55, 98]
      // }
    ]
  })
  // 设备列表
  const machineMonitorDeviceList = async () => {
    await lib.api.bigscreenApi
      .machineMonitorDeviceList({
        code: selectCode.value
      })
      .then((res) => {
        if (res.success) {
          optionList.value = res.result
          deviceSystemId.value = res.result[0].code
          deviceItem.value = res?.result[0] || {}
        }
      })
  }
  onMounted(() => {
    getSelectData()
    init()
    // useIntervalFn(
    //   () => {
    //     getSelectData()
    //   },
    //   1000 * 60,
    //   { immediateCallback: true }
    // )
  })
  const pauseTime = ref(null)
  const resumeTime = ref(null)
  const init = () => {
    // //// pauseTime.value && pauseTime.value()
    const { pause, resume } = useIntervalFn(async () => {
      const index = btnList.value.findIndex((item) => item.code == selectCode.value)
      let selectIndex = index + 1
      if (selectIndex > 3) {
        selectIndex = 0
      }
      const selectItem = btnList.value[selectIndex]
      btnList.value.map((item) => (item.selected = false))
      selectItem.selected = true
      selectCode.value = selectItem.code

      if (selectCode.value == 'cjjc') {
        // 沉降
        getSelectData()
      } else {
        await machineMonitorDeviceList() // 获取设备列表
        machineMonitorData()
      }
    }, 10000)
    pauseTime.value = pause
    resumeTime.value = resume
  }
  const handleMouseEnter = () => {
    pauseTime.value && pauseTime.value()
  }
  const handleMouseLeave = () => {
    resumeTime.value && resumeTime.value()
  }
  watch(
    () => selectId.value,
    () => {
      lib.api.monitorAnalysisApi.deviceGroupDataDetail({ id: selectId.value }).then((res) => {
        if (res.success && res.result) {
          // options.value.series[0].data = res.result.map((_) => _.value)
          deviceItem.value = res?.result[0] || []
          options.value.xAxis.data = res.result.map((_) => _.deviceName)
          options.value.series = [
            {
              name: '沉降',
              type: 'line',
              smooth: true,
              symbolSize: 5,
              itemStyle: {
                normal: {
                  color: '#3823F3',
                  lineStyle: {
                    color: '#3823F3',
                    width: 2
                  },
                  areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: 'rgba(33, 27, 118, 0)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(56, 35, 243, 0.3)'
                      }
                    ])
                  }
                }
              },
              data: res.result.map((_) => _.value)
            }
          ]
        }
      })
    }
  )
  watch(
    () => selectCode.value,
    async (value) => {
      if (value == 'cjjc') {
        getSelectData()
      } else {
        await machineMonitorDeviceList() // 获取设备列表
        machineMonitorData()
      }
    }
  )
  // 收敛 变形 渗漏水 数据
  const machineMonitorData = () => {
    const params = {
      code: deviceSystemId.value,
      type: selectCode.value
    }

    if (selectCode.value == 'slsjc') {
      options.value.yAxis[0].name = '单位（m）'
    } else {
      options.value.yAxis[0].name = '单位（mm）'
    }
    lib.api.bigscreenApi.machineMonitorData(params).then((res) => {
      const colors = ['#3823f3', '#2384f3', '#1DD4AE']
      const colors1 = ['rgba(33, 27, 118,0)', 'rgba(35, 132, 243,0)', 'rgba(29, 212, 174,0)']
      const colors2 = ['rgba(56, 35, 243,0.3)', 'rgba(35, 132, 243,0.3)', 'rgba(29, 212, 174,0.3)']
      if (res.success) {
        const arr = res.result.map((item, index) => {
          // const keys = Object.keys(item.data[0])
          const keys = item?.data[0] ? Object.keys(item.data[0]) : null

          const obj = {
            name: item.name,
            type: 'line',
            smooth: true,
            symbol: 'none',
            symbolSize: 5,
            itemStyle: {
              normal: {
                color: colors[index % 3],
                lineStyle: {
                  color: colors[index % 3],
                  width: 2
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: colors1[index % 3]
                    },
                    {
                      offset: 1,
                      color: colors2[index % 3]
                    }
                  ])
                }
              }
            },
            data: keys ? item.data.map((item) => item[keys[0]]) : []
          }
          return obj
        })
        options.value.series = arr
        options.value.xAxis.data = res.result[0].data.map((item) => moment(item.time).format('HH:mm'))
        // options.value.series[0].name = res.result[0].name
        // options.value.series[0].data = res.result[0].data.map(item => item[300269])
        // options.value.legend.data = [res.result[0].name]
      }
    })
  }
  // 测点选择
  const handleChangeSelectList = () => {
    machineMonitorData()
  }
  const getSelectData = () => {
    options.value.yAxis[0].name = '单位（mm）'
    lib.api.monitorAnalysisApi.deviceGroupData({ code: selectCode.value }).then((res) => {
      if (res.success && res.result) {
        btnList.value.forEach((item) => {
          if (item.code === selectCode.value) {
            item.selected = true
            item.subList = res.result.map((_) => {
              return { ..._, selected: false }
            })
            item.subList[0].selected = true
          } else {
            item.selected = false
          }
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .analysis-container {
    position: relative;
    width: 100%;
    height: 307px;

    // height: 200px;
    .btn-wrap-list {
      position: absolute;
      top: -10px;
      left: 170px;
    }
    .subtitle-container {
      width: 628px;
      height: 31px;
      padding-left: 26px;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      font-weight: 400;
      line-height: 14px;
      color: #ecf6ff;
      background: url('@/assets/subheadline2.png') no-repeat;
      .btn-wrap {
        position: absolute;
        top: -5px;
        right: 35px;
      }
    }
    .chartBg {
      position: absolute;
      bottom: -28px;
      z-index: 1;
      width: 653px;
      height: 68px;
      pointer-events: none;
      background: url('@/assets/ScreenRight/Perception/Structure/lineBg.png') no-repeat;
    }
  }
</style>
<style lang="scss">
  .analysis-summary-popover {
    --el-popover-font-size: 16px;
    --el-bg-color-overlay: rgb(0 0 0 / 70%);
  }
</style>
