import EngineController from '../index'
import { Element } from 'BimEngine'
const BimEngine = window.BimEngine

export const gotoViewportByName = (_this: EngineController, name: string, durationTime?: number) => {
  let duration = durationTime || 1
  const viewportList = _this.currentProject?.getViewPositionInfoList()
  const viewport = viewportList?.find((_) => _.name === name)
  if (viewport) {
    _this.viewer.camera.setViewToViewPosition(viewport.vp, duration)
  }
}
let currentPath = null
export const romaByName = (_this: EngineController, name: string) => {
  // let pathList = _this.currentProject.getRoamPathList()
  // pathList = BimEngine.pathManagerService.formatToClientData(pathList)
  let pathList = _this.currentProject.pathRoamingService.list
  currentPath = pathList.find((_) => _.name === name)
  if (currentPath) {
    const setViewFn = _this.getViewer().camera.setViewToViewPosition.bind(_this.getViewer().camera)
    currentPath.play(
      setViewFn,
      () => {
        console.log('播放结束！') // 除非这个地方调用了是停止播放
      },
      () => {
        console.log('播放中')
      },
      _this.viewer
    )
  }
}
export const romaStop = (_this: EngineController) => {
  // let pathList = _this.currentProject.getRoamPathList()
  // const pathObjList = BimEngine.pathManagerService.formatToClientData(pathList)
  // pathObjList.forEach((element) => {
  //   element.stop()
  // })
  // currentPath?.stop()
  currentPath?.pause(_this.viewer)
}

export const romaResum = (_this: EngineController) => {
  currentPath?.resume(_this.viewer)
}

export const romaPause = (_this: EngineController) => {
  currentPath?.pause(_this.viewer)
}

export const flyToBimId = async (_this: EngineController, bimId: string, distance: number = -10) => {
  // 获取bim信息
  const elements: Element[] = await _this.currentProject.queryElementByBimIds([bimId])
  console.log('elements:', elements)

  const model = elements[0]?.model
  if (model) {
    // 新增逻辑 开始  如果构件有绑定视口使用这个视口
    const element = elements[0]
    if (element) {
      const eleId = element.id
      // const viewPosition = model.getElementViewPosition(eleId)
      // if (viewPosition) {
      //   _this.viewer.camera.setViewToViewPosition(viewPosition, 0.6)
      //   return
      // }

      const extProp = model.mod.extraProperties
      if (extProp && extProp.modConstructorViewerOptions) {
        const viewPosition = extProp.modConstructorViewerOptions[eleId]
        if (viewPosition) {
          _this.viewer.camera.setViewToViewPosition(viewPosition, 1)

          _this.selectedModel?.deselect()
          _this.selectedModel = model
          model.select(elements[0].id)
          return
        }
      }
    }
    // 新增逻辑 结束

    model.queryBoundingBoxByBimIdAry([bimId], async (data) => {
      console.log(' --- box --- ', data)
      if (data.succData && data.succData.length > 0) {
        const boxAry = data.succData.map((d) => {
          return d.box
        })
        // _this.viewer.camera.setViewToBox(boxAry[0], null, null, 1)
        // const viewData = await _this.viewer.ueViewer.getViewPosition()
        const cameraInfo = await _this.viewer.ueViewer.getViewPosition()
        console.log(cameraInfo)
        const center = boxAry[0].center
        // const cameraInfo = await _this.viewer.getViewPosition()
        const direction = BimEngine.Vector3.subtract(center, BimEngine.Vector3.fromArray(cameraInfo.pos), new BimEngine.Vector3())
        // const dirNorm = BimEngine.Vector3.normalize(direction, new BimEngine.Vector3())
        let dirNorm = BimEngine.Vector3.normalize(direction, new BimEngine.Vector3())

        if (cameraInfo.target) {
          // 采集出来一个固定的相机相对于目标的方向。       这个就是用来采集的 代码 采集完了可以删掉
          const cameraPos = BimEngine.Vector3.fromArray(cameraInfo.pos)
          const cameraLookTargetPos = BimEngine.Vector3.fromArray(cameraInfo.target)
          const cameraDir = BimEngine.Vector3.subtract(cameraLookTargetPos, cameraPos, new BimEngine.Vector3())

          dirNorm = BimEngine.Vector3.normalize(cameraDir, new BimEngine.Vector3())
          console.log(dirNorm)
        }
        const distanceFactor = distance // 可以调整下这个 来取一个合适的点位
        const newCenter = BimEngine.Vector3.add(
          center,
          BimEngine.Vector3.multiplyByScalar(dirNorm, distanceFactor, new BimEngine.Vector3()),
          new BimEngine.Vector3()
        )

        // _this.viewer.ueViewer.setViewToPosition(BimEngine.Vector3.toArray(newCenter), -76, -27, undefined, 2)
        _this.viewer.ueViewer.setViewToPosition(BimEngine.Vector3.toArray(newCenter), cameraInfo.phi, cameraInfo.theta, undefined, 0.3)

        // _this.viewer.ueViewer.setViewToPosition([boxAry[0].center.x, boxAry[0].center.y, boxAry[0].center.z], viewData.phi, viewData.theta)

        _this.selectedModel?.deselect()
        _this.selectedModel = model
        model.select(elements[0].id)
        _this.currentProject.isolate([elements[0].id]) // 隔离构建
      }
    })
  }
}
export const flyToByGId = async (_this: EngineController, gId: string) => {
  const modAry = await _this.currentProject.queryModel()
  // modAry.some(async (modI, index) => {
  //   const res = await modI.queryBimInfoByCondition({
  //     compCondition: [{ conditionType: 'pty', operator: 'like', key: '构件编码', value: gId, valueType: 0 }],
  //     pageNo: 1,
  //     pageSize: 50
  //   })
  //   console.log(index, ' 精确查找结果 ', res)
  // })

  let bimInfo = null
  // 使用for..of 可以跳出循环和等待异步函数
  for (const modI of modAry) {
    const res = await modI.queryBimInfoByCondition({
      compCondition: [{ conditionType: 'pty', operator: 'like', key: '构件编码', value: gId, valueType: 0 }],
      pageNo: 1,
      pageSize: 50
    })
    // console.log('模糊查找结果', res)
    if (res && res.length > 0) {
      bimInfo = res[0]
      break
    }
  }
  if (bimInfo) {
    flyToBimId(_this, bimInfo.bimId)
  } else {
    console.log('未找到构件信息')
  }
}

export const cameraFollowCar = (_this: EngineController, vehicleId: string, enableFollow: boolean = true) => {
  if (vehicleId) {
    _this.viewer.ueViewer.setCameraFollow(enableFollow, vehicleId)
  }
}

export const gotoViewPortByPositionWC = (_this: EngineController, positionWC, radius: number = 10) => {
  const box = BimEngine.Box.fromBoundingSphere(positionWC, radius)
  _this.viewer.camera.setViewToBox(box)
}
/**
 * 相机根据经纬度飞过去
 * @param _this EngineController
 * @param lon 经度
 * @param lat 纬度
 * @param height 高度
 * @param radius 距离
 */
export const gotoViewPortByGeo = (_this: EngineController, lon = 0, lat = 0, height = 0, radius: number = 10) => {
  const positionWc = _this.geoToWorld(lon, lat, height)
  const box = BimEngine.Box.fromBoundingSphere(positionWc, radius)
  _this.viewer.camera.setViewToBox(box)
}
