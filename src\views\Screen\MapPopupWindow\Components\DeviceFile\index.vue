<template>
  <PopupBg title="设备档案" width="600px" height="450px" @close="handlerClose">
    <div class="device-file-container">
      <div class="main">
        <div class="tab-box">
          <div class="tab-item" :class="{ active: item.active }" v-for="(item, index) in tabData" :key="index" @click="handlerTab(item)">{{ item.name }}</div>
        </div>
        <DatePicker
          v-show="tabData[3].active || tabData[4].active"
          v-model="date"
          type="year"
          value-format="YYYY"
          @change="handleDateChange"
          :disabled-date="disabledDate"
          :clearable="false"
          dateStyle="width:120px;height:36px"
          style="top: 92px; right: -46px"></DatePicker>
        <TableList
          :style="{ 'margin-top': tabData[3].active || tabData[4].active ? '64px' : '0px' }"
          list-width="520px"
          v-if="tabData[2].active || tabData[3].active || tabData[4].active || tabData[5].active"
          :head-list="tableHeadList"
          :list="tableDataList"></TableList>
        <InfoCom v-else :info-data="infoData" :key="infoData.length"></InfoCom>
      </div>
    </div>
  </PopupBg>
</template>

<script setup>
  import moment from 'moment'

  import DatePicker from '@/components/DatePicker/index.vue'
  import InfoCom from '@/components/InfoCom/index.vue'
  import TableList from '@/components/TableList/index.vue'
  import PopupBg from '@/components/PopupBg/index.vue'

  import lib from '@/utils/lib'
  const props = defineProps({
    data: {
      type: Object,
      default: () => {}
    }
  })
  const date = ref(moment().format('YYYY'))
  const handleDateChange = (val) => {
    if (tabData.value[3].active) {
      getYhPlanData()
    } else if (tabData.value[4].active) {
      getWyWorkData()
    }
  }
  const disabledDate = (time) => {
    return time.getTime() > Date.now()
  }
  const tabData = ref([
    { name: '基本信息', active: true },
    { name: '参数', active: false },
    { name: '变更记录', active: false },
    { name: '养护计划', active: false },
    { name: '维养作业', active: false },
    { name: '缺陷记录', active: false }
  ])
  const tableHeadList = ref([])
  const tableDataList = ref([])
  const infoData = ref([])
  const resultData = ref([])
  const setInfoData = (data, resultData) => {
    data.value.forEach((item) => {
      if (item.type == 'date') {
        item.value = moment(resultData.value?.device[item.prop]).format('YYYY-MM-DD')
      } else {
        item.value = resultData.value?.device[item.prop]
      }
    })
  }
  const handlerTab = (item) => {
    tabData.value.forEach((data) => {
      data.active = data.name === item.name
    })
    switch (item.name) {
      case '基本信息':
        infoData.value = [
          { name: '设备类型', prop: 'bigTypeName', value: '---' },
          { name: '设备名称', prop: 'name', value: '---' },
          { name: '安装时间', prop: 'completeDate', value: '---' },
          { name: '设备编号', prop: 'code', value: '---' },
          { name: '起始里程', prop: 'startMileage', value: '---' },
          { name: '终止里程', prop: 'endMileage', value: '---' },
          { name: '当前寿命', prop: 'currentLife', value: '---', unit: '个月' },
          { name: '剩余寿命', prop: 'restLife', value: '---', unit: '个月' },
          { name: '使用年限', prop: 'ageLimit', value: '---', unit: '年' },
          { name: '质保期', prop: 'guaranteePeriod', value: '---', unit: '年' }
        ]
        setInfoData(infoData, resultData)
        break

      case '参数':
        infoData.value = resultData.value?.deviceParamList
        // infoData.value.forEach((data) => {
        //   data.value = data.code
        // })
        break

      case '变更记录':
        tableHeadList.value = [
          { label: '更新时间', prop: 'updateDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '更新人', prop: 'updateUserName' },
          { label: '品牌', prop: 'brand' },
          { label: '设备型号', prop: 'equipmentModel' },
          { label: '供应商', prop: 'supplier' },
          { label: '实际使用寿命', prop: 'serviceLife' }
        ]
        tableDataList.value = resultData.value?.deviceUpdateHistoryList
        break

      case '养护计划':
        tableHeadList.value = [
          { label: '年份', prop: 'year' },
          { label: '计划类型', prop: 'workOrderType' },
          { label: '维修养护内容', prop: 'workPlanContent', dicp: 'work_plan_content' },
          { label: '计划名称', prop: 'name' },
          { label: '实施频次', prop: 'frequencyNumber', unit: '次/月' },
          { label: '完成率', prop: 'completionRate', unit: '%' }
        ]
        getYhPlanData()
        console.log('养护计划----', tableHeadList.value, tableDataList.value)
        break

      case '维养作业':
        tableHeadList.value = [
          { label: '计划作业日期', prop: 'startDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '作业单名称', prop: 'name' },
          { label: '作业单类型', prop: 'workPlanTypeName' },
          { label: '实际开工时间', prop: 'realStartDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '验收时间', prop: 'acceptDate', formatDate: 'YYYY-MM-DD HH:mm:ss' }
        ]
        getWyWorkData()
        console.log('维养作业----', tableHeadList.value, tableDataList.value)
        break

      case '缺陷记录':
        tableHeadList.value = [
          { label: '发现时间', prop: 'discoveryDate', formatDate: 'YYYY-MM-DD HH:mm:ss' },
          { label: '发现人', prop: 'discoveryUserName' },
          { label: '缺陷来源', prop: 'source', dicp: 'defect_source' },
          { label: '缺陷类型', prop: 'defectTypeName' },
          { label: '养护策略', prop: 'maintenanceStrategy', dicp: 'maintenance_strategy' },
          { label: '缺陷状态', prop: 'status', dicp: 'defect_status' }
        ]
        tableDataList.value = resultData.value?.defectList
        console.log('缺陷记录----', tableHeadList.value, tableDataList.value)
        break

      default:
        break
    }
  }
  const handlerClose = () => {
    // 关闭弹窗
    lib.popWindow.removeDialog('deviceWindow')
  }
  const getYhPlanData = () => {
    lib.api.getPopupDetail.deviceResumeCardMaintenanceWorkPlan({ deviceid: props.data.id, years: [date.value] }).then((res) => {
      if (res.success && res.result) {
        // tableDataList.value = res.result?.maintenanceWorkPlan
        tableDataList.value = res.result?.maintenanceWorkPlan.map((_) => {
          return {
            ..._,
            completionRate: _.year < moment().format('YYYY') ? '100' : _.completionRate // 历史完成率写死100%
          }
        })
      }
    })
  }
  const getWyWorkData = () => {
    lib.api.getPopupDetail
      .deviceResumeCardMainTenanceOrder({ deviceid: props.data.id, startDate: date.value + '-01-01 00:00:00', endDate: date.value + '-12-31 23:59:59' })
      .then((res) => {
        if (res.success && res.result) {
          tableDataList.value = res.result?.mainTenanceOrderList
        }
      })
  }
  watch(
    () => props.data,
    (newValue) => {
      if (newValue) {
        // 获取设备详情
        lib.api.getPopupDetail.deviceResumeCardDevice({ deviceid: props.data.id }).then((res) => {
          if (res.success) {
            resultData.value = res.result
            handlerTab(tabData.value[0])
          }
        })
      }
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<style lang="scss" scoped>
  .device-file-container {
    display: flex;
    flex-direction: column;
    width: 560px;
    min-height: 100px;
    font-family: 'Alibaba PuHuiTi';
    font-size: 16px;
    .tab-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .tab-item {
        height: 31px;
        font-family: 'Source Han Sans CN';
        font-size: 22px;

        // font-weight: 500;
        line-height: 31px;
        color: #bcddff;
        text-align: center;
        cursor: pointer;
        &.active {
          height: 31px;

          // width: 70px;
          // height: 24px;
          padding: 0 7px;

          // background: #00b2ff;
          // border-radius: 4px;
          font-weight: bold;
          line-height: 31px;
          background: url('@/assets/CommonPopup/tab-selected.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }
    .bottom {
      width: 560px;
      height: 55px;
      background: url('@/assets/CommonPopup/bottom-img.png') no-repeat;
      background-size: cover;
    }
  }
</style>
