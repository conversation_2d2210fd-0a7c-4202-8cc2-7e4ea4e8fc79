<template>
  <div class="system-iframe">
    <!-- 可移动盒子, 用于处理iframe无法拖拽的问题 -->
    <div class="move-box"></div>
    <img class="absolute right-0 top-0 cursor-pointer" src="@/assets/CommonPopup/closeBtn.png" @click="handleclose" />
    <iframe :src="iframeAddress" frameborder="0" width="100%" height="100%" ref="iframeRef"></iframe>
  </div>
</template>

<script setup>
  import { useRoute } from 'vue-router' // 添加这行
  import { closePopWindowByTag } from 'znyg-frontend-common'
  import { PROJECT_ID } from '@/config/index.ts'
  import { getToken } from '@/utils/auth'
  import lib from '@/utils/lib'
  const route = useRoute() // 添加这行

  const props = defineProps({
    path: {
      // 路径
      type: String
      //   default: '',
    },
    params: {
      // 传参
      type: Object
      // default: '',
    }
  })
  // http://*************:8848/stec-qj-page/#/redirect?token=d40de37c-ecfe-460d-8f09-2af2a8dbc9a6&redirectUrl=/inventory/operations&projectId=28
  // ? token, projectId：项目id, redirectUrl: 路由地址, params: 传参json字符串, theme: 主题weak, isCollapse: 菜单折叠状态
  let iframeAddress = import.meta.env.VITE_SYSTEM_URL + 'redirect' + '?token=' + getToken() + '&redirectUrl=' + props.path + '&theme=weak' + '&isCollapse=true'
  // 项目id

  PROJECT_ID && (iframeAddress += '&projectId=' + PROJECT_ID)
  props.params && (iframeAddress += '&params=' + JSON.stringify(props.params))
  //   console.log(2222, iframeAddress)
  const iframeRef = ref(null)

  const handleclose = () => {
    closePopWindowByTag(lib.enumMap.PopWindowTag.后台管理)
  }
  onMounted(() => {
    if (iframeRef.value) {
      iframeRef.value.onload = function () {
        console.log(iframeRef.value)
        console.log('iframeRef.value.contentDocument')
        // ! 影响到iframe内部交互了
        // iframeRef.value.contentDocument.body.style.pointerEvents = 'none'
      }
    }
  })
</script>

<style lang="scss" scoped>
  .system-iframe {
    position: relative;
    width: 1946px;
    height: 1114px;
    .move-box {
      position: absolute;
      width: 500px;
      height: 50px;
      // 水平居中
      left: 50%;
      transform: translateX(-50%);
      cursor: move;
      //   background-color: #f50;
    }
  }
</style>
