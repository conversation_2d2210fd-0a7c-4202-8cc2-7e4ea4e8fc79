define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Matrix4-a50b021f","./ComponentDatatype-44dd9bc1","./AttributeCompression-a01059cd"],(function(e,t,i,r,a,n,o,s,c){"use strict";function m(e,a){i.Check.typeOf.object("ellipsoid",e),this._ellipsoid=e,this._cameraPosition=new r.Cartesian3,this._cameraPositionInScaledSpace=new r.Cartesian3,this._distanceToLimbInScaledSpaceSquared=0,t.defined(a)&&(this.cameraPosition=a)}Object.defineProperties(m.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},cameraPosition:{get:function(){return this._cameraPosition},set:function(e){var t=this._ellipsoid,i=t.transformPositionToScaledSpace(e,this._cameraPositionInScaledSpace),a=r.Cartesian3.magnitudeSquared(i)-1;r.Cartesian3.clone(e,this._cameraPosition),this._cameraPositionInScaledSpace=i,this._distanceToLimbInScaledSpaceSquared=a}}});var u=new r.Cartesian3;m.prototype.isPointVisible=function(e){var t=this._ellipsoid,i=t.transformPositionToScaledSpace(e,u);return S(i,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)},m.prototype.isScaledSpacePointVisible=function(e){return S(e,this._cameraPositionInScaledSpace,this._distanceToLimbInScaledSpaceSquared)};var d=new r.Cartesian3;m.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid=function(e,i){var r,a,n=this._ellipsoid;return t.defined(i)&&i<0&&n.minimumRadius>-i?(a=d,a.x=this._cameraPosition.x/(n.radii.x+i),a.y=this._cameraPosition.y/(n.radii.y+i),a.z=this._cameraPosition.z/(n.radii.z+i),r=a.x*a.x+a.y*a.y+a.z*a.z-1):(a=this._cameraPositionInScaledSpace,r=this._distanceToLimbInScaledSpaceSquared),S(e,a,r)},m.prototype.computeHorizonCullingPoint=function(e,t,i){return C(this._ellipsoid,e,t,i)};var l=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE);m.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid=function(e,t,i,r){var a=f(this._ellipsoid,i,l);return C(a,e,t,r)},m.prototype.computeHorizonCullingPointFromVertices=function(e,t,i,r,a){return y(this._ellipsoid,e,t,i,r,a)},m.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid=function(e,t,i,r,a,n){var o=f(this._ellipsoid,a,l);return y(o,e,t,i,r,n)};var p=[];m.prototype.computeHorizonCullingPointFromRectangle=function(e,t,o){i.Check.typeOf.object("rectangle",e);var s=a.Rectangle.subsample(e,t,0,p),c=n.BoundingSphere.fromPoints(s);if(!(r.Cartesian3.magnitude(c.center)<.1*t.minimumRadius))return this.computeHorizonCullingPoint(c.center,s,o)};var h=new r.Cartesian3;function f(e,i,n){if(t.defined(i)&&i<0&&e.minimumRadius>-i){var o=r.Cartesian3.fromElements(e.radii.x+i,e.radii.y+i,e.radii.z+i,h);e=a.Ellipsoid.fromCartesian3(o,n)}return e}function C(e,a,n,o){i.Check.typeOf.object("directionToPoint",a),i.Check.defined("positions",n),t.defined(o)||(o=new r.Cartesian3);for(var s=P(e,a),c=0,m=0,u=n.length;m<u;++m){var d=n[m],l=g(e,d,s);if(l<0)return;c=Math.max(c,l)}return M(s,c,o)}var x=new r.Cartesian3;function y(e,a,n,o,s,c){i.Check.typeOf.object("directionToPoint",a),i.Check.defined("vertices",n),i.Check.typeOf.number("stride",o),t.defined(c)||(c=new r.Cartesian3),o=t.defaultValue(o,3),s=t.defaultValue(s,r.Cartesian3.ZERO);for(var m=P(e,a),u=0,d=0,l=n.length;d<l;d+=o){x.x=n[d]+s.x,x.y=n[d+1]+s.y,x.z=n[d+2]+s.z;var p=g(e,x,m);if(p<0)return;u=Math.max(u,p)}return M(m,u,c)}function S(e,t,i){var a=t,n=i,o=r.Cartesian3.subtract(e,a,u),s=-r.Cartesian3.dot(o,a),c=n<0?s>0:s>n&&s*s/r.Cartesian3.magnitudeSquared(o)>n;return!c}var b=new r.Cartesian3,v=new r.Cartesian3;function g(e,t,i){var a=e.transformPositionToScaledSpace(t,b),n=r.Cartesian3.magnitudeSquared(a),o=Math.sqrt(n),s=r.Cartesian3.divideByScalar(a,o,v);n=Math.max(1,n),o=Math.max(1,o);var c=r.Cartesian3.dot(s,i),m=r.Cartesian3.magnitude(r.Cartesian3.cross(s,i,s)),u=1/o,d=Math.sqrt(n-1)*u;return 1/(c*u-m*d)}function M(e,t,i){if(!(t<=0||t===1/0||t!==t))return r.Cartesian3.multiplyByScalar(e,t,i)}var T=new r.Cartesian3;function P(e,t){return r.Cartesian3.equals(t,r.Cartesian3.ZERO)?t:(e.transformPositionToScaledSpace(t,T),r.Cartesian3.normalize(T,T))}var z={NONE:0,BITS12:1},E=Object.freeze(z),N=new r.Cartesian3,I=new r.Cartesian3,B=new r.Cartesian2,_=new o.Matrix4,w=new o.Matrix4,A=Math.pow(2,12);function q(e,i,a,n,s,c){var m,u,d,l=E.NONE;if(t.defined(e)&&t.defined(i)&&t.defined(a)&&t.defined(n)){var p=e.minimum,h=e.maximum,f=r.Cartesian3.subtract(h,p,I),C=a-i,x=Math.max(r.Cartesian3.maximumComponent(f),C);l=x<A-1?E.BITS12:E.NONE,m=e.center,u=o.Matrix4.inverseTransformation(n,new o.Matrix4);var y=r.Cartesian3.negate(p,N);o.Matrix4.multiply(o.Matrix4.fromTranslation(y,_),u,u);var S=N;S.x=1/f.x,S.y=1/f.y,S.z=1/f.z,o.Matrix4.multiply(o.Matrix4.fromScale(S,_),u,u),d=o.Matrix4.clone(n),o.Matrix4.setTranslation(d,r.Cartesian3.ZERO,d),n=o.Matrix4.clone(n,new o.Matrix4);var b=o.Matrix4.fromTranslation(p,_),v=o.Matrix4.fromScale(f,w),g=o.Matrix4.multiply(b,v,_);o.Matrix4.multiply(n,g,n),o.Matrix4.multiply(d,g,d)}this.quantization=l,this.minimumHeight=i,this.maximumHeight=a,this.center=m,this.toScaledENU=u,this.fromScaledENU=n,this.matrix=d,this.hasVertexNormals=s,this.hasWebMercatorT=t.defaultValue(c,!1)}q.prototype.encode=function(e,t,i,a,n,s,m){var u=a.x,d=a.y;if(this.quantization===E.BITS12){i=o.Matrix4.multiplyByPoint(this.toScaledENU,i,N),i.x=r.CesiumMath.clamp(i.x,0,1),i.y=r.CesiumMath.clamp(i.y,0,1),i.z=r.CesiumMath.clamp(i.z,0,1);var l=this.maximumHeight-this.minimumHeight,p=r.CesiumMath.clamp((n-this.minimumHeight)/l,0,1);r.Cartesian2.fromElements(i.x,i.y,B);var h=c.AttributeCompression.compressTextureCoordinates(B);r.Cartesian2.fromElements(i.z,p,B);var f=c.AttributeCompression.compressTextureCoordinates(B);r.Cartesian2.fromElements(u,d,B);var C=c.AttributeCompression.compressTextureCoordinates(B);if(e[t++]=h,e[t++]=f,e[t++]=C,this.hasWebMercatorT){r.Cartesian2.fromElements(m,0,B);var x=c.AttributeCompression.compressTextureCoordinates(B);e[t++]=x}}else r.Cartesian3.subtract(i,this.center,N),e[t++]=N.x,e[t++]=N.y,e[t++]=N.z,e[t++]=n,e[t++]=u,e[t++]=d,this.hasWebMercatorT&&(e[t++]=m);return this.hasVertexNormals&&(e[t++]=c.AttributeCompression.octPackFloat(s)),t},q.prototype.decodePosition=function(e,i,a){if(t.defined(a)||(a=new r.Cartesian3),i*=this.getStride(),this.quantization===E.BITS12){var n=c.AttributeCompression.decompressTextureCoordinates(e[i],B);a.x=n.x,a.y=n.y;var s=c.AttributeCompression.decompressTextureCoordinates(e[i+1],B);return a.z=s.x,o.Matrix4.multiplyByPoint(this.fromScaledENU,a,a)}return a.x=e[i],a.y=e[i+1],a.z=e[i+2],r.Cartesian3.add(a,this.center,a)},q.prototype.decodeTextureCoordinates=function(e,i,a){return t.defined(a)||(a=new r.Cartesian2),i*=this.getStride(),this.quantization===E.BITS12?c.AttributeCompression.decompressTextureCoordinates(e[i+2],a):r.Cartesian2.fromElements(e[i+4],e[i+5],a)},q.prototype.decodeHeight=function(e,t){if(t*=this.getStride(),this.quantization===E.BITS12){var i=c.AttributeCompression.decompressTextureCoordinates(e[t+1],B);return i.y*(this.maximumHeight-this.minimumHeight)+this.minimumHeight}return e[t+3]},q.prototype.decodeWebMercatorT=function(e,t){return t*=this.getStride(),this.quantization===E.BITS12?c.AttributeCompression.decompressTextureCoordinates(e[t+3],B).x:e[t+6]},q.prototype.getOctEncodedNormal=function(e,t,i){var a=this.getStride();t=(t+1)*a-1;var n=e[t]/256,o=Math.floor(n),s=256*(n-o);return r.Cartesian2.fromElements(o,s,i)},q.prototype.getStride=function(){var e;switch(this.quantization){case E.BITS12:e=3;break;default:e=6}return this.hasWebMercatorT&&++e,this.hasVertexNormals&&++e,e};var H={position3DAndHeight:0,textureCoordAndEncodedNormals:1},O={compressed0:0,compressed1:1};q.prototype.getAttributes=function(e){var t,i=s.ComponentDatatype.FLOAT,r=s.ComponentDatatype.getSizeInBytes(i);if(this.quantization===E.NONE){var a=4,n=2;return this.hasWebMercatorT&&++n,this.hasVertexNormals&&++n,t=(a+n)*r,[{index:H.position3DAndHeight,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:a,offsetInBytes:0,strideInBytes:t},{index:H.textureCoordAndEncodedNormals,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:n,offsetInBytes:a*r,strideInBytes:t}]}var o=3,c=0;return(this.hasWebMercatorT||this.hasVertexNormals)&&++o,this.hasWebMercatorT&&this.hasVertexNormals?(++c,t=(o+c)*r,[{index:O.compressed0,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:o,offsetInBytes:0,strideInBytes:t},{index:O.compressed1,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:c,offsetInBytes:o*r,strideInBytes:t}]):[{index:O.compressed0,vertexBuffer:e,componentDatatype:i,componentsPerAttribute:o}]},q.prototype.getAttributeLocations=function(){return this.quantization===E.NONE?H:O},q.clone=function(e,i){return t.defined(i)||(i=new q),i.quantization=e.quantization,i.minimumHeight=e.minimumHeight,i.maximumHeight=e.maximumHeight,i.center=r.Cartesian3.clone(e.center),i.toScaledENU=o.Matrix4.clone(e.toScaledENU),i.fromScaledENU=o.Matrix4.clone(e.fromScaledENU),i.matrix=o.Matrix4.clone(e.matrix),i.hasVertexNormals=e.hasVertexNormals,i.hasWebMercatorT=e.hasWebMercatorT,i},e.EllipsoidalOccluder=m,e.TerrainEncoding=q}));