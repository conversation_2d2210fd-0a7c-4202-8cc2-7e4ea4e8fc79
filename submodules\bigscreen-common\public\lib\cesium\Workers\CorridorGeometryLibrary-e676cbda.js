/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./PolylineVolumeGeometryLibrary-fad92191","./defined-3b3eb2ba","./Math-b5f4d889","./Rectangle-9bffefe4","./PolylinePipeline-1a06b90f","./Transforms-42ed7720"],(function(a,e,n,t,r,i,s,o){"use strict";const l={},C=new e.Cartesian3,y=new e.Cartesian3,c=new e.Cartesian3,u=new e.Cartesian3,d=[new e.Cartesian3,new e.Cartesian3],p=new e.Cartesian3,m=new e.Cartesian3,f=new e.Cartesian3,g=new e.Cartesian3,h=new e.Cartesian3,w=new e.Cartesian3,z=new e.Cartesian3,x=new e.Cartesian3,b=new e.Cartesian3,P=new e.Cartesian3,A=new o.Quaternion,B=new i.Matrix3;function E(a,t,s,l,c){const u=e.Cartesian3.angleBetween(e.Cartesian3.subtract(t,a,C),e.Cartesian3.subtract(s,a,y)),d=l===n.CornerType.BEVELED?1:Math.ceil(u/r.CesiumMath.toRadians(5))+1,p=3*d,m=new Array(p);let f;m[p-3]=s.x,m[p-2]=s.y,m[p-1]=s.z,f=c?i.Matrix3.fromQuaternion(o.Quaternion.fromAxisAngle(e.Cartesian3.negate(a,C),u/d,A),B):i.Matrix3.fromQuaternion(o.Quaternion.fromAxisAngle(a,u/d,A),B);let g=0;t=e.Cartesian3.clone(t,C);for(let a=0;a<d;a++)t=i.Matrix3.multiplyByVector(f,t,t),m[g++]=t.x,m[g++]=t.y,m[g++]=t.z;return m}function S(a,n,t,r){let i=C;return r||(n=e.Cartesian3.negate(n,n)),i=e.Cartesian3.add(a,n,i),[i.x,i.y,i.z,t.x,t.y,t.z]}function D(a,n,t,r){const i=new Array(a.length),s=new Array(a.length),o=e.Cartesian3.multiplyByScalar(n,t,C),l=e.Cartesian3.negate(o,y);let d=0,p=a.length-1;for(let n=0;n<a.length;n+=3){const t=e.Cartesian3.fromArray(a,n,c),r=e.Cartesian3.add(t,l,u);i[d++]=r.x,i[d++]=r.y,i[d++]=r.z;const C=e.Cartesian3.add(t,o,u);s[p--]=C.z,s[p--]=C.y,s[p--]=C.x}return r.push(i,s),r}l.addAttribute=function(a,e,n,r){const i=e.x,s=e.y,o=e.z;t.defined(n)&&(a[n]=i,a[n+1]=s,a[n+2]=o),t.defined(r)&&(a[r]=o,a[r-1]=s,a[r-2]=i)};const M=new e.Cartesian3,T=new e.Cartesian3;l.computePositions=function(a){const t=a.granularity,i=a.positions,o=a.ellipsoid,l=a.width/2,y=a.cornerType,c=a.saveAttributes;let u=p,A=m,B=f,N=g,L=h,R=w,O=z,V=x,Q=b,U=P,v=[];const G=c?[]:void 0,I=c?[]:void 0;let q,j=i[0],k=i[1];A=e.Cartesian3.normalize(e.Cartesian3.subtract(k,j,A),A),u=o.geodeticSurfaceNormal(j,u),N=e.Cartesian3.normalize(e.Cartesian3.cross(u,A,N),N),c&&(G.push(N.x,N.y,N.z),I.push(u.x,u.y,u.z)),O=e.Cartesian3.clone(j,O),j=k,B=e.Cartesian3.negate(A,B);const F=[];let H;const J=i.length;for(H=1;H<J-1;H++){u=o.geodeticSurfaceNormal(j,u),k=i[H+1],A=e.Cartesian3.normalize(e.Cartesian3.subtract(k,j,A),A),L=e.Cartesian3.normalize(e.Cartesian3.add(A,B,L),L);const a=e.Cartesian3.multiplyByScalar(u,e.Cartesian3.dot(A,u),M);e.Cartesian3.subtract(A,a,a),e.Cartesian3.normalize(a,a);const p=e.Cartesian3.multiplyByScalar(u,e.Cartesian3.dot(B,u),T);e.Cartesian3.subtract(B,p,p),e.Cartesian3.normalize(p,p);if(!r.CesiumMath.equalsEpsilon(Math.abs(e.Cartesian3.dot(a,p)),1,r.CesiumMath.EPSILON7)){L=e.Cartesian3.cross(L,u,L),L=e.Cartesian3.cross(u,L,L),L=e.Cartesian3.normalize(L,L);const a=l/Math.max(.25,e.Cartesian3.magnitude(e.Cartesian3.cross(L,B,C))),r=n.PolylineVolumeGeometryLibrary.angleIsGreaterThanPi(A,B,j,o);L=e.Cartesian3.multiplyByScalar(L,a,L),r?(V=e.Cartesian3.add(j,L,V),U=e.Cartesian3.add(V,e.Cartesian3.multiplyByScalar(N,l,U),U),Q=e.Cartesian3.add(V,e.Cartesian3.multiplyByScalar(N,2*l,Q),Q),d[0]=e.Cartesian3.clone(O,d[0]),d[1]=e.Cartesian3.clone(U,d[1]),q=s.PolylinePipeline.generateArc({positions:d,granularity:t,ellipsoid:o}),v=D(q,N,l,v),c&&(G.push(N.x,N.y,N.z),I.push(u.x,u.y,u.z)),R=e.Cartesian3.clone(Q,R),N=e.Cartesian3.normalize(e.Cartesian3.cross(u,A,N),N),Q=e.Cartesian3.add(V,e.Cartesian3.multiplyByScalar(N,2*l,Q),Q),O=e.Cartesian3.add(V,e.Cartesian3.multiplyByScalar(N,l,O),O),y===n.CornerType.ROUNDED||y===n.CornerType.BEVELED?F.push({leftPositions:E(V,R,Q,y,r)}):F.push({leftPositions:S(j,e.Cartesian3.negate(L,L),Q,r)})):(Q=e.Cartesian3.add(j,L,Q),U=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,l,U),U),U),V=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,2*l,V),V),V),d[0]=e.Cartesian3.clone(O,d[0]),d[1]=e.Cartesian3.clone(U,d[1]),q=s.PolylinePipeline.generateArc({positions:d,granularity:t,ellipsoid:o}),v=D(q,N,l,v),c&&(G.push(N.x,N.y,N.z),I.push(u.x,u.y,u.z)),R=e.Cartesian3.clone(V,R),N=e.Cartesian3.normalize(e.Cartesian3.cross(u,A,N),N),V=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,2*l,V),V),V),O=e.Cartesian3.add(Q,e.Cartesian3.negate(e.Cartesian3.multiplyByScalar(N,l,O),O),O),y===n.CornerType.ROUNDED||y===n.CornerType.BEVELED?F.push({rightPositions:E(Q,R,V,y,r)}):F.push({rightPositions:S(j,L,V,r)})),B=e.Cartesian3.negate(A,B)}j=k}let K;return u=o.geodeticSurfaceNormal(j,u),d[0]=e.Cartesian3.clone(O,d[0]),d[1]=e.Cartesian3.clone(j,d[1]),q=s.PolylinePipeline.generateArc({positions:d,granularity:t,ellipsoid:o}),v=D(q,N,l,v),c&&(G.push(N.x,N.y,N.z),I.push(u.x,u.y,u.z)),y===n.CornerType.ROUNDED&&(K=function(a){let t=p,r=m,i=f,s=a[1];r=e.Cartesian3.fromArray(a[1],s.length-3,r),i=e.Cartesian3.fromArray(a[0],0,i),t=e.Cartesian3.midpoint(r,i,t);const o=E(t,r,i,n.CornerType.ROUNDED,!1),l=a.length-1,C=a[l-1];return s=a[l],r=e.Cartesian3.fromArray(C,C.length-3,r),i=e.Cartesian3.fromArray(s,0,i),t=e.Cartesian3.midpoint(r,i,t),[o,E(t,r,i,n.CornerType.ROUNDED,!1)]}(v)),{positions:v,corners:F,lefts:G,normals:I,endPositions:K}};var N=l;a.CorridorGeometryLibrary=N}));
