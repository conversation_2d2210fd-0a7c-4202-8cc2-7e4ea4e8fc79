define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./GeometryOffsetAttribute-30ce4d84"],(function(e,t,n,a,r,i,o,u,f,s,m,c,d,y){"use strict";var p=new n.Cartesian3;function b(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var r=a.minimum,i=a.maximum;if(t.Check.typeOf.object("min",r),t.Check.typeOf.object("max",i),e.defined(a.offsetAttribute)&&a.offsetAttribute===y.GeometryOffsetAttribute.TOP)throw new t.DeveloperError("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._min=n.Cartesian3.clone(r),this._max=n.Cartesian3.clone(i),this._offsetAttribute=a.offsetAttribute,this._workerName="createBoxOutlineGeometry"}b.fromDimensions=function(a){a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT);var r=a.dimensions;t.Check.typeOf.object("dimensions",r),t.Check.typeOf.number.greaterThanOrEquals("dimensions.x",r.x,0),t.Check.typeOf.number.greaterThanOrEquals("dimensions.y",r.y,0),t.Check.typeOf.number.greaterThanOrEquals("dimensions.z",r.z,0);var i=n.Cartesian3.multiplyByScalar(r,.5,new n.Cartesian3);return new b({minimum:n.Cartesian3.negate(i,new n.Cartesian3),maximum:i,offsetAttribute:a.offsetAttribute})},b.fromAxisAlignedBoundingBox=function(e){return t.Check.typeOf.object("boundindBox",e),new b({minimum:e.minimum,maximum:e.maximum})},b.packedLength=2*n.Cartesian3.packedLength+1,b.pack=function(a,r,i){return t.Check.typeOf.object("value",a),t.Check.defined("array",r),i=e.defaultValue(i,0),n.Cartesian3.pack(a._min,r,i),n.Cartesian3.pack(a._max,r,i+n.Cartesian3.packedLength),r[i+2*n.Cartesian3.packedLength]=e.defaultValue(a._offsetAttribute,-1),r};var C=new n.Cartesian3,l=new n.Cartesian3,h={minimum:C,maximum:l,offsetAttribute:void 0};function A(t,n){return e.defined(n)&&(t=b.unpack(t,n)),b.createGeometry(t)}return b.unpack=function(a,r,i){t.Check.defined("array",a),r=e.defaultValue(r,0);var o=n.Cartesian3.unpack(a,r,C),u=n.Cartesian3.unpack(a,r+n.Cartesian3.packedLength,l),f=a[r+2*n.Cartesian3.packedLength];return e.defined(i)?(i._min=n.Cartesian3.clone(o,i._min),i._max=n.Cartesian3.clone(u,i._max),i._offsetAttribute=-1===f?void 0:f,i):(h.offsetAttribute=-1===f?void 0:f,new b(h))},b.createGeometry=function(t){var a=t._min,i=t._max;if(!n.Cartesian3.equals(a,i)){var o=new d.GeometryAttributes,u=new Uint16Array(24),f=new Float64Array(24);f[0]=a.x,f[1]=a.y,f[2]=a.z,f[3]=i.x,f[4]=a.y,f[5]=a.z,f[6]=i.x,f[7]=i.y,f[8]=a.z,f[9]=a.x,f[10]=i.y,f[11]=a.z,f[12]=a.x,f[13]=a.y,f[14]=i.z,f[15]=i.x,f[16]=a.y,f[17]=i.z,f[18]=i.x,f[19]=i.y,f[20]=i.z,f[21]=a.x,f[22]=i.y,f[23]=i.z,o.position=new m.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f}),u[0]=4,u[1]=5,u[2]=5,u[3]=6,u[4]=6,u[5]=7,u[6]=7,u[7]=4,u[8]=0,u[9]=1,u[10]=1,u[11]=2,u[12]=2,u[13]=3,u[14]=3,u[15]=0,u[16]=0,u[17]=4,u[18]=1,u[19]=5,u[20]=2,u[21]=6,u[22]=3,u[23]=7;var b=n.Cartesian3.subtract(i,a,p),C=.5*n.Cartesian3.magnitude(b);if(e.defined(t._offsetAttribute)){var l=f.length,h=new Uint8Array(l/3),A=t._offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1;y.arrayFill(h,A),o.applyOffset=new m.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:h})}return new m.Geometry({attributes:o,indices:u,primitiveType:c.PrimitiveType.LINES,boundingSphere:new r.BoundingSphere(n.Cartesian3.ZERO,C),offsetAttribute:t._offsetAttribute})}},A}));