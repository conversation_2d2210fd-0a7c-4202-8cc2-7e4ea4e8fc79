import { deepClone } from '@Common/utils/index.js'
/**
 * @description: 
 * @param {Object} option 基础配置
 * @param {Object} dataSet   修改数值，如果是数组，则会遍历修改
 * @param {Object} dataSet.yData  y轴数据
 * @param {Object} sdataSet.eriesData  数据
 * @param {Object} dataSet.seriesName  数据名称
 * @return {String} 修改后的值 
 */
export const setOptionData = (option = null, dataSet = { xData: null, yData: null, seriesData: null, seriesName: null }) => {
  const rawOption = deepClone(option)
  if (rawOption === null) return
  const { xData, yData, seriesData, seriesName } = dataSet
  if (xData) {
    if (xData.every((_) => Array.isArray(_))) {
      xData.forEach((item, index) => {
        rawOption.xAxis[index].data = item
      })
    } else {
      rawOption.xAxis.data = xData
    }
  }

  if (yData) {
    if (yData.every((_) => Array.isArray(_))) {
      yData.forEach((item, index) => {
        rawOption.yAxis[index].data = item
      })
    } else {
      rawOption.yAxis.data = yData
    }
  }

  if (seriesData) {
    if (seriesData.every((_) => Array.isArray(_))) {
      seriesData.forEach((item, index) => {
        rawOption.series[index].data = item
      })
    } else {
      rawOption.series.data = seriesData
    }
  }
  if (seriesName) {
    if (Array.isArray(seriesName)) {
      seriesName.forEach((item, index) => {
        rawOption.series[index].name = item
      })
    } else {
      rawOption.series.name = seriesName
    }

    if (rawOption.legend?.data) {
      if (Array.isArray(rawOption.legend.data)) {
        rawOption.legend.data = seriesName
      } else {
        rawOption.legend.data = [seriesName]
      }
    }
  }
  return rawOption
}
