import { waterData } from '@/hooks/useUE/data/waterData.js'

import { toUe5 } from './tools'
// PS 液位起始点需要调整到底板下面
export function mockWater() {
  // 最高液位
  const MAXHEIGHT = 50
  // const MAXHEIGHT = -80
  //  水位上升速度 每米水位上升需要的时间
  const SPEED = 0.005

  const INTERVAL = 3000 * 1
  //  是否开启
  let isStart = true
  //  是否暂停
  let isPause = false

  const raf = window.requestAnimationFrame
  let currentTime = performance.now()
  let oldTime = currentTime
  // 计算高度
  const computeHeight = (index) => {
    if (index == -1) return
    if (waterData[index]) {
      const rate = waterData[index].rate / 100
      return MAXHEIGHT * rate
    }
  }
  // 计算动画时间
  const computeDuration = (old, current) => Math.abs(current - old) * SPEED
  let waterDataIndex = 0 // 当前的水位数据索引值

  // 水位处理动画
  function modelAnimation(oldHeight, height, duration) {
    const modelRenderList = {
      list: [
        {
          // modelId: 13240182, // 水
          modelCode: 'SHSPDD03EXFXFM0001202212-01d',
          scale: {
            // 缩放型动画
            isPlay: true, // 是否播放动画，为false时，下列数据不用填写
            startValue: { x: 1, y: 1, z: oldHeight }, // 起始缩放，为空时则为该模型初始缩放
            // startValue: { x: 1, y: 1, z: 1 }, // 起始缩放，为空时则为该模型初始缩放
            targetValue: { x: 1, y: 1, z: height }, // 目标缩放  先写30
            delay: 0.0, // 延迟多少秒后播放动画
            duration: 3, // 持续时间，浮点数，单位秒
            isLoop: false, // 是否循环播放
            isPingpong: false // 循环开启时,是否为来回循环
          }
        }
      ]
    }
    toUe5('modelAnimation', modelRenderList)
  }
  // 开启动画
  function start() {
    if (!isStart) return
    currentTime = performance.now()
    if (currentTime - oldTime >= INTERVAL) {
      if (!isPause) {
        // 处理核心逻辑
        waterDataIndex++
        // 当数组遍历完成后 关闭动画
        if (waterDataIndex >= waterData.length - 1) {
          stop()
        }

        const oldHeight = computeHeight(waterDataIndex - 1)
        const height = computeHeight(waterDataIndex)
        const duration = computeDuration(oldHeight, height)
        if (oldHeight) {
          modelAnimation(oldHeight, height, duration)
        }

        // 重置oldTime
        oldTime = performance.now()
        console.log('动画执行', oldHeight, height, duration, MAXHEIGHT)
      }
    }
    raf(start)
  }
  // 暂停动画
  // eslint-disable-next-line no-unused-vars
  function pause() {
    isPause = true
    isStart = true
  }
  //  停止动画
  function stop() {
    isPause = false
    isStart = false
    waterDataIndex = 0
  }
  start()
}
