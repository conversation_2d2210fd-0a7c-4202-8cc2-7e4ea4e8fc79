# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.2.1":
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/@ampproject/remapping/-/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/install-pkg@^0.1.1":
  version "0.1.1"
  resolved "https://tech.suitbim.com/verdaccio/@antfu/install-pkg/-/install-pkg-0.1.1.tgz#157bb04f0de8100b9e4c01734db1a6c77e98bbb5"
  integrity sha512-LyB/8+bSfa0DFGC06zpCEfs89/XoWZwws5ygEa5D+Xsm3OfI+aXQ86VgVG7Acyef+rSZ5HE7J8rrxzrQeM3PjQ==
  dependencies:
    execa "^5.1.1"
    find-up "^5.0.0"

"@antfu/utils@^0.7.7":
  version "0.7.7"
  resolved "https://tech.suitbim.com/verdaccio/@antfu/utils/-/utils-0.7.7.tgz#26ea493a831b4f3a85475e7157be02fb4eab51fb"
  integrity sha512-gFPqTG7otEJ8uP6wrhDv6mqwGWYZKNvAcCq6u9hOj0c+IKCEsY4L1oC9trPq2SaWIzAfHvqfBDxF591JkMf+kg==

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.2":
  version "7.24.2"
  resolved "https://tech.suitbim.com/verdaccio/@babel/code-frame/-/code-frame-7.24.2.tgz#718b4b19841809a58b29b68cde80bc5e1aa6d9ae"
  integrity sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==
  dependencies:
    "@babel/highlight" "^7.24.2"
    picocolors "^1.0.0"

"@babel/compat-data@^7.23.5":
  version "7.24.4"
  resolved "https://tech.suitbim.com/verdaccio/@babel/compat-data/-/compat-data-7.24.4.tgz#6f102372e9094f25d908ca0d34fc74c74606059a"
  integrity sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==

"@babel/core@^7.23.3":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/core/-/core-7.24.5.tgz#15ab5b98e101972d171aeef92ac70d8d6718f06a"
  integrity sha512-tVQRucExLQ02Boi4vdPp49svNGcfL2GhdTCT9aldhXgCJVAI21EtRfBettiuLUwce/7r6bFdgs6JFkcdTiFttA==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.24.5"
    "@babel/helpers" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/generator/-/generator-7.24.5.tgz#e5afc068f932f05616b66713e28d0f04e99daeb3"
  integrity sha512-x32i4hEXvr+iI0NEoEfDKzlemF8AmtOP8CcrRaEcpzysWuoEb1KknpcvMsHKPONoKZiDuItklgWhB18xEhr9PA==
  dependencies:
    "@babel/types" "^7.24.5"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.22.5":
  version "7.22.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz#e7f06737b197d580a01edf75d97e2c8be99d3882"
  integrity sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-compilation-targets@^7.23.6":
  version "7.23.6"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz#4d79069b16cbcf1461289eccfbbd81501ae39991"
  integrity sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==
  dependencies:
    "@babel/compat-data" "^7.23.5"
    "@babel/helper-validator-option" "^7.23.5"
    browserslist "^4.22.2"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.24.1", "@babel/helper-create-class-features-plugin@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.5.tgz#7d19da92c7e0cd8d11c09af2ce1b8e7512a6e723"
  integrity sha512-uRc4Cv8UQWnE4NXlYTIIdM7wfFkOqlFztcC/gVXDKohKoVB3OyonfelUBaJzSwpBntZ2KYGF/9S7asCHsXwW6g==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-member-expression-to-functions" "^7.24.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-replace-supers" "^7.24.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    semver "^6.3.1"

"@babel/helper-environment-visitor@^7.22.20":
  version "7.22.20"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz#96159db61d34a29dba454c959f5ae4a649ba9167"
  integrity sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==

"@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz#1f9a3cdbd5b2698a670c30d2735f9af95ed52759"
  integrity sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz#c01a007dac05c085914e8fb652b339db50d823bb"
  integrity sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.23.0", "@babel/helper-member-expression-to-functions@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.24.5.tgz#5981e131d5c7003c7d1fa1ad49e86c9b097ec475"
  integrity sha512-4owRteeihKWKamtqg4JmWSsEZU445xpFRXPEwp44HbgbxdWlUV1b4Agg4lkA806Lil5XM/e+FJyS0vj5T6vmcA==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-module-imports@^7.24.3":
  version "7.24.3"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-module-imports/-/helper-module-imports-7.24.3.tgz#6ac476e6d168c7c23ff3ba3cf4f7841d46ac8128"
  integrity sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==
  dependencies:
    "@babel/types" "^7.24.0"

"@babel/helper-module-imports@~7.22.15":
  version "7.22.15"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz#16146307acdc40cc00c3b2c647713076464bdbf0"
  integrity sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.23.3", "@babel/helper-module-transforms@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-module-transforms/-/helper-module-transforms-7.24.5.tgz#ea6c5e33f7b262a0ae762fd5986355c45f54a545"
  integrity sha512-9GxeY8c2d2mdQUP1Dye0ks3VDyIMS98kt/llQ2nUId8IsWqTF0l1LkSX0/uP7l7MCDrzXS009Hyhe2gzTiGW8A==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-module-imports" "^7.24.3"
    "@babel/helper-simple-access" "^7.24.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/helper-validator-identifier" "^7.24.5"

"@babel/helper-optimise-call-expression@^7.22.5":
  version "7.22.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz#f21531a9ccbff644fdd156b4077c16ff0c3f609e"
  integrity sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.0", "@babel/helper-plugin-utils@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.5.tgz#a924607dd254a65695e5bd209b98b902b3b2f11a"
  integrity sha512-xjNLDopRzW2o6ba0gKbkZq5YWEBaK3PCyTOY1K2P/O07LGMhMqlMXPxwN4S5/RhWuCobT8z0jrlKGlYmeR1OhQ==

"@babel/helper-replace-supers@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-replace-supers/-/helper-replace-supers-7.24.1.tgz#7085bd19d4a0b7ed8f405c1ed73ccb70f323abc1"
  integrity sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-member-expression-to-functions" "^7.23.0"
    "@babel/helper-optimise-call-expression" "^7.22.5"

"@babel/helper-simple-access@^7.22.5", "@babel/helper-simple-access@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-simple-access/-/helper-simple-access-7.24.5.tgz#50da5b72f58c16b07fbd992810be6049478e85ba"
  integrity sha512-uH3Hmf5q5n7n8mz7arjUlDOCbttY/DW4DYhE6FUsjKJ/oYC1kQQUvwEQWxRwUpX9qQKRXeqLwWxrqilMrf32sQ==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.22.5":
  version "7.22.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz#007f15240b5751c537c40e77abb4e89eeaaa8847"
  integrity sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.24.5.tgz#b9a67f06a46b0b339323617c8c6213b9055a78b6"
  integrity sha512-5CHncttXohrHk8GWOFCcCl4oRD9fKosWlIRgWm4ql9VYioKm52Mk2xsmoohvm7f3JoiLSM5ZgJuRaf5QZZYd3Q==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-string-parser@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-string-parser/-/helper-string-parser-7.24.1.tgz#f99c36d3593db9540705d0739a1f10b5e20c696e"
  integrity sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==

"@babel/helper-validator-identifier@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.5.tgz#918b1a7fa23056603506370089bd990d8720db62"
  integrity sha512-3q93SSKX2TWCG30M2G2kwaKeTYgEUp5Snjuj8qm729SObL6nbtUldAi37qbxkD5gg3xnBio+f9nqpSepGZMvxA==

"@babel/helper-validator-option@^7.23.5":
  version "7.23.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz#907a3fbd4523426285365d1206c423c4c5520307"
  integrity sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==

"@babel/helpers@^7.24.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/helpers/-/helpers-7.24.5.tgz#fedeb87eeafa62b621160402181ad8585a22a40a"
  integrity sha512-CiQmBMMpMQHwM5m01YnrM6imUG1ebgYJ+fAIW4FZe6m4qHTPaRHti+R8cggAwkdz4oXhtO4/K9JWlh+8hIfR2Q==
  dependencies:
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"

"@babel/highlight@^7.24.2":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/highlight/-/highlight-7.24.5.tgz#bc0613f98e1dd0720e99b2a9ee3760194a704b6e"
  integrity sha512-8lLmua6AVh/8SLJRRVD6V8p73Hir9w5mJrhE+IPpILG31KKlI9iz5zmBYKcWPS59qSfgP9RaSBQSHHE81WKuEw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.5"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.23.9", "@babel/parser@^7.24.0", "@babel/parser@^7.24.4", "@babel/parser@^7.24.5", "@babel/parser@^7.7.0":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/parser/-/parser-7.24.5.tgz#4a4d5ab4315579e5398a82dcf636ca80c3392790"
  integrity sha512-EOv5IK8arwh3LI47dz1b0tKUb/1uhHAnHJOrjgtQMIpu1uXd9mlFrJg9IUgGUgZ41Ch0K8REPTYpO7B76b4vJg==

"@babel/plugin-proposal-decorators@^7.17.9":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.1.tgz#bab2b9e174a2680f0a80f341f3ec70f809f8bb4b"
  integrity sha512-zPEvzFijn+hRvJuX2Vu3KbEBN39LN3f7tW3MQO2LsIs57B26KU+kUc82BdAktS1VCM6libzh45eKGI65lg0cpA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-decorators" "^7.24.1"

"@babel/plugin-syntax-decorators@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.24.1.tgz#71d9ad06063a6ac5430db126b5df48c70ee885fa"
  integrity sha512-05RJdO/cCrtVWuAaSn1tS3bH8jbsJa/Y1uD186u6J4C/1mnHFxseeuWpsqr9anvo7TUulev7tm7GDwRV+VuhDw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-jsx@^7.23.3", "@babel/plugin-syntax-jsx@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.1.tgz#3f6ca04b8c841811dbc3c5c5f837934e0d626c10"
  integrity sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-typescript@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.24.1.tgz#b3bcc51f396d15f3591683f90239de143c076844"
  integrity sha512-Yhnmvy5HZEnHUty6i++gcfH1/l68AHnItFHnaCv6hn9dNh0hQvvQJsxpi4BMBFN5DLeHBuucT/0DgzXif/OyRw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-modules-commonjs@^7.24.1":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.24.1.tgz#e71ba1d0d69e049a22bf90b3867e263823d3f1b9"
  integrity sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-simple-access" "^7.22.5"

"@babel/plugin-transform-typescript@^7.23.3", "@babel/plugin-transform-typescript@^7.24.1":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.24.5.tgz#bcba979e462120dc06a75bd34c473a04781931b8"
  integrity sha512-E0VWu/hk83BIFUWnsKZ4D81KXjN5L3MobvevOHErASk9IPwKHOkTgvqzvNo1yP/ePJWqqK2SpUR5z+KQbl6NVw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.24.5"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-typescript" "^7.24.1"

"@babel/preset-typescript@^7.16.7":
  version "7.24.1"
  resolved "https://tech.suitbim.com/verdaccio/@babel/preset-typescript/-/preset-typescript-7.24.1.tgz#89bdf13a3149a17b3b2a2c9c62547f06db8845ec"
  integrity sha512-1DBaMmRDpuYQBPWD8Pf/WEwCrtgRHxsZnP4mIy9G/X+hFfbI47Q2G4t1Paakld84+qsk2fSsUPMKg71jkoOOaQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-syntax-jsx" "^7.24.1"
    "@babel/plugin-transform-modules-commonjs" "^7.24.1"
    "@babel/plugin-transform-typescript" "^7.24.1"

"@babel/runtime@^7.5.5":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/runtime/-/runtime-7.24.5.tgz#230946857c053a36ccc66e1dd03b17dd0c4ed02c"
  integrity sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.22.15", "@babel/template@^7.23.9", "@babel/template@^7.24.0":
  version "7.24.0"
  resolved "https://tech.suitbim.com/verdaccio/@babel/template/-/template-7.24.0.tgz#c6a524aa93a4a05d66aaf31654258fae69d87d50"
  integrity sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/parser" "^7.24.0"
    "@babel/types" "^7.24.0"

"@babel/traverse@^7.23.9", "@babel/traverse@^7.24.5", "@babel/traverse@^7.7.0":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/traverse/-/traverse-7.24.5.tgz#972aa0bc45f16983bf64aa1f877b2dd0eea7e6f8"
  integrity sha512-7aaBLeDQ4zYcUFDUD41lJc1fG8+5IU9DaNSJAgal866FGvmD5EbWQgnEC6kO1gGLsX0esNkfnJSndbTXA3r7UA==
  dependencies:
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/types" "^7.24.5"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.22.15", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.23.9", "@babel/types@^7.24.0", "@babel/types@^7.24.5", "@babel/types@^7.7.0":
  version "7.24.5"
  resolved "https://tech.suitbim.com/verdaccio/@babel/types/-/types-7.24.5.tgz#7661930afc638a5383eb0c4aee59b74f38db84d7"
  integrity sha512-6mQNsaLeXTw0nxYUYu+NSa4Hx4BlF1x1x8/PMFbiR+GBSr+2DkECc69b8hgy2frEodNcvPffeH8YfWd3LI6jhQ==
  dependencies:
    "@babel/helper-string-parser" "^7.24.1"
    "@babel/helper-validator-identifier" "^7.24.5"
    to-fast-properties "^2.0.0"

"@csstools/css-parser-algorithms@^2.3.1":
  version "2.6.3"
  resolved "https://tech.suitbim.com/verdaccio/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.6.3.tgz#b5e7eb2bd2a42e968ef61484f1490a8a4148a8eb"
  integrity sha512-xI/tL2zxzEbESvnSxwFgwvy5HS00oCXxL4MLs6HUiDcYfwowsoQaABKxUElp1ARITrINzBnsECOc1q0eg2GOrA==

"@csstools/css-tokenizer@^2.2.0":
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/@csstools/css-tokenizer/-/css-tokenizer-2.3.1.tgz#3d47e101ad48d815a4bdce8159fb5764f087f17a"
  integrity sha512-iMNHTyxLbBlWIfGtabT157LH9DUx9X8+Y3oymFEuMj8HNc+rpE3dPFGFgHjpKfjeFDjLjYIAIhXPGvS2lKxL9g==

"@csstools/media-query-list-parser@^2.1.4":
  version "2.1.11"
  resolved "https://tech.suitbim.com/verdaccio/@csstools/media-query-list-parser/-/media-query-list-parser-2.1.11.tgz#465aa42f268599729350e305e1ae14a30c1daf51"
  integrity sha512-uox5MVhvNHqitPP+SynrB1o8oPxPMt2JLgp5ghJOWf54WGQ5OKu47efne49r1SWqs3wRP8xSWjnO9MBKxhB1dA==

"@csstools/selector-specificity@^3.0.0":
  version "3.0.3"
  resolved "https://tech.suitbim.com/verdaccio/@csstools/selector-specificity/-/selector-specificity-3.0.3.tgz#208a3929ee614967a1fc8cd6cb758d9fcbf0caae"
  integrity sha512-KEPNw4+WW5AVEIyzC80rTbWEUatTW2lXpN8+8ILC8PiPeWPjwUzrPZDIOZ2wwqDmeqOYTdSGyL3+vE5GC3FB3Q==

"@ctrl/tinycolor@^3.4.1":
  version "3.6.1"
  resolved "https://tech.suitbim.com/verdaccio/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz#b6c75a56a1947cc916ea058772d666a2c8932f31"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@element-plus/icons-vue@^2.1.0", "@element-plus/icons-vue@^2.3.1":
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz#1f635ad5fdd5c85ed936481525570e82b5a8307a"
  integrity sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==

"@esbuild/android-arm64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/android-arm64/-/android-arm64-0.18.20.tgz#984b4f9c8d0377443cc2dfcef266d02244593622"
  integrity sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==

"@esbuild/android-arm@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/android-arm/-/android-arm-0.18.20.tgz#fedb265bc3a589c84cc11f810804f234947c3682"
  integrity sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==

"@esbuild/android-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/android-x64/-/android-x64-0.18.20.tgz#35cf419c4cfc8babe8893d296cd990e9e9f756f2"
  integrity sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==

"@esbuild/darwin-arm64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz#08172cbeccf95fbc383399a7f39cfbddaeb0d7c1"
  integrity sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==

"@esbuild/darwin-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz#d70d5790d8bf475556b67d0f8b7c5bdff053d85d"
  integrity sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==

"@esbuild/freebsd-arm64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz#98755cd12707f93f210e2494d6a4b51b96977f54"
  integrity sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==

"@esbuild/freebsd-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz#c1eb2bff03915f87c29cece4c1a7fa1f423b066e"
  integrity sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==

"@esbuild/linux-arm64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-arm64/-/linux-arm64-0.18.20.tgz#bad4238bd8f4fc25b5a021280c770ab5fc3a02a0"
  integrity sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==

"@esbuild/linux-arm@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz#3e617c61f33508a27150ee417543c8ab5acc73b0"
  integrity sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==

"@esbuild/linux-ia32@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz#699391cccba9aee6019b7f9892eb99219f1570a7"
  integrity sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==

"@esbuild/linux-loong64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-loong64/-/linux-loong64-0.18.20.tgz#e6fccb7aac178dd2ffb9860465ac89d7f23b977d"
  integrity sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==

"@esbuild/linux-mips64el@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz#eeff3a937de9c2310de30622a957ad1bd9183231"
  integrity sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==

"@esbuild/linux-ppc64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz#2f7156bde20b01527993e6881435ad79ba9599fb"
  integrity sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==

"@esbuild/linux-riscv64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-riscv64/-/linux-riscv64-0.18.20.tgz#6628389f210123d8b4743045af8caa7d4ddfc7a6"
  integrity sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==

"@esbuild/linux-s390x@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz#255e81fb289b101026131858ab99fba63dcf0071"
  integrity sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==

"@esbuild/linux-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/linux-x64/-/linux-x64-0.18.20.tgz#c7690b3417af318a9b6f96df3031a8865176d338"
  integrity sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==

"@esbuild/netbsd-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz#30e8cd8a3dded63975e2df2438ca109601ebe0d1"
  integrity sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==

"@esbuild/openbsd-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/openbsd-x64/-/openbsd-x64-0.18.20.tgz#7812af31b205055874c8082ea9cf9ab0da6217ae"
  integrity sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==

"@esbuild/sunos-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz#d5c275c3b4e73c9b0ecd38d1ca62c020f887ab9d"
  integrity sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==

"@esbuild/win32-arm64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz#73bc7f5a9f8a77805f357fab97f290d0e4820ac9"
  integrity sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==

"@esbuild/win32-ia32@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/win32-ia32/-/win32-ia32-0.18.20.tgz#ec93cbf0ef1085cc12e71e0d661d20569ff42102"
  integrity sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==

"@esbuild/win32-x64@0.18.20":
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz#786c5f41f043b07afb1af37683d7c33668858f6d"
  integrity sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  version "4.4.0"
  resolved "https://tech.suitbim.com/verdaccio/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz#a23514e8fb9af1269d5f7788aa556798d61c6b59"
  integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.6.1":
  version "4.10.0"
  resolved "https://tech.suitbim.com/verdaccio/@eslint-community/regexpp/-/regexpp-4.10.0.tgz#548f6de556857c8bb73bbee70c35dc82a2e74d63"
  integrity sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://tech.suitbim.com/verdaccio/@eslint/eslintrc/-/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.0":
  version "8.57.0"
  resolved "https://tech.suitbim.com/verdaccio/@eslint/js/-/js-8.57.0.tgz#a5417ae8427873f1dd08b70b3574b453e67b5f7f"
  integrity sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==

"@floating-ui/core@^1.0.0":
  version "1.6.1"
  resolved "https://tech.suitbim.com/verdaccio/@floating-ui/core/-/core-1.6.1.tgz#a4e6fef1b069cda533cbc7a4998c083a37f37573"
  integrity sha512-42UH54oPZHPdRHdw6BgoBD6cg/eVTmVrFcgeRDM3jbO7uxSoipVcmcIGFcA5jmOHO5apcyvBhkSKES3fQJnu7A==
  dependencies:
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/dom@^1.0.1":
  version "1.6.5"
  resolved "https://tech.suitbim.com/verdaccio/@floating-ui/dom/-/dom-1.6.5.tgz#323f065c003f1d3ecf0ff16d2c2c4d38979f4cb9"
  integrity sha512-Nsdud2X65Dz+1RHjAIP0t8z5e2ff/IRbei6BqFrl1urT8sDVzM1HMQ+R0XcU5ceRfyO3I6ayeqIfh+6Wb8LGTw==
  dependencies:
    "@floating-ui/core" "^1.0.0"
    "@floating-ui/utils" "^0.2.0"

"@floating-ui/utils@^0.2.0":
  version "0.2.2"
  resolved "https://tech.suitbim.com/verdaccio/@floating-ui/utils/-/utils-0.2.2.tgz#d8bae93ac8b815b2bd7a98078cf91e2724ef11e5"
  integrity sha512-J4yDIIthosAsRZ5CPYP/jQvUAQtlZTTD/4suA08/FEnlxqW3sKS9iAhgsa9VYLZ6vDHn/ixJgIqRQPotoBjxIw==

"@humanwhocodes/config-array@^0.11.14":
  version "0.11.14"
  resolved "https://tech.suitbim.com/verdaccio/@humanwhocodes/config-array/-/config-array-0.11.14.tgz#d78e481a039f7566ecc9660b4ea7fe6b1fec442b"
  integrity sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.2":
  version "2.0.3"
  resolved "https://tech.suitbim.com/verdaccio/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==

"@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/@iconify/types/-/types-2.0.0.tgz#ab0e9ea681d6c8a1214f30cd741fe3a20cc57f57"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@iconify/utils@^2.1.9":
  version "2.1.23"
  resolved "https://tech.suitbim.com/verdaccio/@iconify/utils/-/utils-2.1.23.tgz#1cde237dd5142a66d9fbd065b2fa7acc4f80f862"
  integrity sha512-YGNbHKM5tyDvdWZ92y2mIkrfvm5Fvhe6WJSkWu7vvOFhMtYDP0casZpoRz0XEHZCrYsR4stdGT3cZ52yp5qZdQ==
  dependencies:
    "@antfu/install-pkg" "^0.1.1"
    "@antfu/utils" "^0.7.7"
    "@iconify/types" "^2.0.0"
    debug "^4.3.4"
    kolorist "^1.8.0"
    local-pkg "^0.5.0"
    mlly "^1.6.1"

"@jiaminghi/bezier-curve@*":
  version "0.0.9"
  resolved "https://tech.suitbim.com/verdaccio/@jiaminghi/bezier-curve/-/bezier-curve-0.0.9.tgz#5196aca93c8b061a612b4c3eabcedf9490cef6ee"
  integrity sha512-u9xJPOEl6Dri2E9FfmJoGxYQY7vYJkURNX04Vj64tdi535tPrpkuf9Sm0lNr3QTKdHQh0DdNRsaa62FLQNQEEw==
  dependencies:
    "@babel/runtime" "^7.5.5"

"@jiaminghi/c-render@^0.4.3":
  version "0.4.3"
  resolved "https://tech.suitbim.com/verdaccio/@jiaminghi/c-render/-/c-render-0.4.3.tgz#982ebd8f71b443bb9507834227834973ebd9b6d8"
  integrity sha512-FJfzj5hGj7MLqqqI2D7vEzHKbQ1Ynnn7PJKgzsjXaZpJzTqs2Yw5OSeZnm6l7Qj7jyPAP53lFvEQNH4o4j6s+Q==
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@jiaminghi/bezier-curve" "*"
    "@jiaminghi/color" "*"
    "@jiaminghi/transition" "*"

"@jiaminghi/color@*":
  version "1.1.3"
  resolved "https://tech.suitbim.com/verdaccio/@jiaminghi/color/-/color-1.1.3.tgz#a2336750d1266155ffe80375c58c26fdec495611"
  integrity sha512-ZY3hdorgODk4OSTbxyXBPxAxHPIVf9rPlKJyK1C1db46a50J0reFKpAvfZG8zMG3lvM60IR7Qawgcu4ZDO3+Hg==

"@jiaminghi/transition@*":
  version "1.1.11"
  resolved "https://tech.suitbim.com/verdaccio/@jiaminghi/transition/-/transition-1.1.11.tgz#576d8af092434b34201eba5eaecc79dd33c8ad8c"
  integrity sha512-owBggipoHMikDHHDW5Gc7RZYlVuvxHADiU4bxfjBVkHDAmmck+fCkm46n2JzC3j33hWvP9nSCAeh37t6stgWeg==
  dependencies:
    "@babel/runtime" "^7.5.5"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://tech.suitbim.com/verdaccio/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz#dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://tech.suitbim.com/verdaccio/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://tech.suitbim.com/verdaccio/@jridgewell/set-array/-/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.4.15"
  resolved "https://tech.suitbim.com/verdaccio/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://tech.suitbim.com/verdaccio/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://tech.suitbim.com/verdaccio/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://tech.suitbim.com/verdaccio/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://tech.suitbim.com/verdaccio/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgr/core@^0.1.0":
  version "0.1.1"
  resolved "https://tech.suitbim.com/verdaccio/@pkgr/core/-/core-0.1.1.tgz#1ec17e2edbec25c8306d424ecfbf13c7de1aaa31"
  integrity sha512-cq8o4cWH0ibXh9VGi5P20Tu9XF/0fFXl9EUinr9QfTM7a7p0oTA4iJRCQWppXR1Pg8dSM0UCItCkPwsk9qWWYA==

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.25"
  resolved "https://tech.suitbim.com/verdaccio/@polka/url/-/url-1.0.0-next.25.tgz#f077fdc0b5d0078d30893396ff4827a13f99e817"
  integrity sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==

"@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  version "2.11.7"
  resolved "https://tech.suitbim.com/verdaccio/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz#a7f69e3665d3da9b115f9e71671dae1b97e13671"
  integrity sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/aspromise/-/aspromise-1.1.2.tgz#9b8b0cc663d669a7d8f6f5d0893a14d348f30fbf"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/base64/-/base64-1.1.2.tgz#4c85730e59b9a1f1f349047dbf24296034bb2735"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/codegen/-/codegen-2.0.4.tgz#7ef37f0d010fb028ad1ad59722e506d9262815cb"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz#355cbc98bafad5978f9ed095f397621f1d066b70"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/fetch/-/fetch-1.1.0.tgz#ba99fb598614af65700c1619ff06d454b0d84c45"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/float/-/float-1.0.2.tgz#5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/inquire/-/inquire-1.1.0.tgz#ff200e3e7cf2429e2dcafc1140828e8cc638f089"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/path/-/path-1.1.2.tgz#6cc2b20c5c9ad6ad0dccfd21ca7673d8d7fbf68d"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/pool/-/pool-1.1.0.tgz#09fd15f2d6d3abfa9b65bc366506d6ad7846ff54"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@protobufjs/utf8/-/utf8-1.1.0.tgz#a777360b5b39a1a2e5106f8e858f2fd2d060c570"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@rollup/pluginutils@^4.1.2", "@rollup/pluginutils@^4.2.1":
  version "4.2.1"
  resolved "https://tech.suitbim.com/verdaccio/@rollup/pluginutils/-/pluginutils-4.2.1.tgz#e6c6c3aba0744edce3fb2074922d3776c0af2a6d"
  integrity sha512-iKnFXr7NkdZAIHiIWE+BX5ULi/ucVFYWD6TbAV+rZctiRTY2PL6tsIKhoIOaoskiWAkgu+VsbXgUVDNLHf+InQ==
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rollup/pluginutils@^5.0.3", "@rollup/pluginutils@^5.1.0":
  version "5.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@rollup/pluginutils/-/pluginutils-5.1.0.tgz#7e53eddc8c7f483a4ad0b94afb1f7f5fd3c771e0"
  integrity sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^2.3.1"

"@turf/along@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/along/-/along-6.5.0.tgz#ab12eec58a14de60fe243a62d31a474f415c8fef"
  integrity sha512-LLyWQ0AARqJCmMcIEAXF4GEu8usmd4Kbz3qk1Oy5HoRNpZX47+i5exQtmIWKdqJ1MMhW26fCTXgpsEs5zgJ5gw==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/angle@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/angle/-/angle-6.5.0.tgz#985934171284e109d41e19ed48ad91cf9709a928"
  integrity sha512-4pXMbWhFofJJAOvTMCns6N4C8CMd5Ih4O2jSAG9b3dDHakj3O4yN1+Zbm+NUei+eVEZ9gFeVp9svE3aMDenIkw==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"

"@turf/area@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/area/-/area-6.5.0.tgz#1d0d7aee01d8a4a3d4c91663ed35cc615f36ad56"
  integrity sha512-xCZdiuojokLbQ+29qR6qoMD89hv+JAgWjLrwSEWL+3JV8IXKeNFl6XkEJz9HGkVpnXvQKJoRz4/liT+8ZZ5Jyg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/bbox-clip@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/bbox-clip/-/bbox-clip-6.5.0.tgz#8e07d51ef8c875f9490d5c8699a2e51918587c94"
  integrity sha512-F6PaIRF8WMp8EmgU/Ke5B1Y6/pia14UAYB5TiBC668w5rVVjy5L8rTm/m2lEkkDMHlzoP9vNY4pxpNthE7rLcQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/bbox-polygon@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/bbox-polygon/-/bbox-polygon-6.5.0.tgz#f18128b012eedfa860a521d8f2b3779cc0801032"
  integrity sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/bbox@*", "@turf/bbox@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/bbox/-/bbox-6.5.0.tgz#bec30a744019eae420dac9ea46fb75caa44d8dc5"
  integrity sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/bearing@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/bearing/-/bearing-6.5.0.tgz#462a053c6c644434bdb636b39f8f43fb0cd857b0"
  integrity sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/bezier-spline@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/bezier-spline/-/bezier-spline-6.5.0.tgz#d1b1764948b0fa3d9aa6e4895aebeba24048b11f"
  integrity sha512-vokPaurTd4PF96rRgGVm6zYYC5r1u98ZsG+wZEv9y3kJTuJRX/O3xIY2QnTGTdbVmAJN1ouOsD0RoZYaVoXORQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-clockwise@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-clockwise/-/boolean-clockwise-6.5.0.tgz#34573ecc18f900080f00e4ff364631a8b1135794"
  integrity sha512-45+C7LC5RMbRWrxh3Z0Eihsc8db1VGBO5d9BLTOAwU4jR6SgsunTfRWR16X7JUwIDYlCVEmnjcXJNi/kIU3VIw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-contains@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-contains/-/boolean-contains-6.5.0.tgz#f802e7432fb53109242d5bf57393ef2f53849bbf"
  integrity sha512-4m8cJpbw+YQcKVGi8y0cHhBUnYT+QRfx6wzM4GI1IdtYH3p4oh/DOBJKrepQyiDzFDaNIjxuWXBh0ai1zVwOQQ==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-crosses@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-crosses/-/boolean-crosses-6.5.0.tgz#4a1981475b9d6e23b25721f9fb8ef20696ff1648"
  integrity sha512-gvshbTPhAHporTlQwBJqyfW+2yV8q/mOTxG6PzRVl6ARsqNoqYQWkd4MLug7OmAqVyBzLK3201uAeBjxbGw0Ng==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"

"@turf/boolean-disjoint@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-disjoint/-/boolean-disjoint-6.5.0.tgz#e291d8f8f8cce7f7bb3c11e23059156a49afc5e4"
  integrity sha512-rZ2ozlrRLIAGo2bjQ/ZUu4oZ/+ZjGvLkN5CKXSKBcu6xFO6k2bgqeM8a1836tAW+Pqp/ZFsTA5fZHsJZvP2D5g==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"

"@turf/boolean-equal@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-equal/-/boolean-equal-6.5.0.tgz#b1c0ce14e9d9fb7778cddcf22558c9f523fe9141"
  integrity sha512-cY0M3yoLC26mhAnjv1gyYNQjn7wxIXmL2hBmI/qs8g5uKuC2hRWi13ydufE3k4x0aNRjFGlg41fjoYLwaVF+9Q==
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    geojson-equality "0.1.6"

"@turf/boolean-intersects@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-intersects/-/boolean-intersects-6.5.0.tgz#df2b831ea31a4574af6b2fefe391f097a926b9d6"
  integrity sha512-nIxkizjRdjKCYFQMnml6cjPsDOBCThrt+nkqtSEcxkKMhAQj5OO7o2CecioNTaX8EayqwMGVKcsz27oP4mKPTw==
  dependencies:
    "@turf/boolean-disjoint" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/boolean-overlap@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-overlap/-/boolean-overlap-6.5.0.tgz#f27c85888c3665d42d613a91a83adf1657cd1385"
  integrity sha512-8btMIdnbXVWUa1M7D4shyaSGxLRw6NjMcqKBcsTXcZdnaixl22k7ar7BvIzkaRYN3SFECk9VGXfLncNS3ckQUw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-overlap" "^6.5.0"
    "@turf/meta" "^6.5.0"
    geojson-equality "0.1.6"

"@turf/boolean-parallel@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-parallel/-/boolean-parallel-6.5.0.tgz#4e8a9dafdccaf18aca95f1265a5eade3f330173f"
  integrity sha512-aSHJsr1nq9e5TthZGZ9CZYeXklJyRgR5kCLm5X4urz7+MotMOp/LsGOsvKvK9NeUl9+8OUmfMn8EFTT8LkcvIQ==
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"

"@turf/boolean-point-in-polygon@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-point-in-polygon/-/boolean-point-in-polygon-6.5.0.tgz#6d2e9c89de4cd2e4365004c1e51490b7795a63cf"
  integrity sha512-DtSuVFB26SI+hj0SjrvXowGTUCHlgevPAIsukssW6BG5MlNSBQAo70wpICBNJL6RjukXg8d2eXaAWuD/CqL00A==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-point-on-line@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-point-on-line/-/boolean-point-on-line-6.5.0.tgz#a8efa7bad88760676f395afb9980746bc5b376e9"
  integrity sha512-A1BbuQ0LceLHvq7F/P7w3QvfpmZqbmViIUPHdNLvZimFNLo4e6IQunmzbe+8aSStH9QRZm3VOflyvNeXvvpZEQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/boolean-within@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/boolean-within/-/boolean-within-6.5.0.tgz#31a749d3be51065da8c470a1e5613f4d2efdee06"
  integrity sha512-YQB3oU18Inx35C/LU930D36RAVe7LDXk1kWsQ8mLmuqYn9YdPsDQTMTkLJMhoQ8EbN7QTdy333xRQ4MYgToteQ==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/buffer@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/buffer/-/buffer-6.5.0.tgz#22bd0d05b4e1e73eaebc69b8f574a410ff704842"
  integrity sha512-qeX4N6+PPWbKqp1AVkBVWFerGjMYMUyencwfnkCesoznU6qvfugFHNAngNqIBVnJjZ5n8IFyOf+akcxnrt9sNg==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/projection" "^6.5.0"
    d3-geo "1.7.1"
    turf-jsts "*"

"@turf/center-mean@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/center-mean/-/center-mean-6.5.0.tgz#2dc329c003f8012ba9ae7812a61b5647e1ae86a2"
  integrity sha512-AAX6f4bVn12pTVrMUiB9KrnV94BgeBKpyg3YpfnEbBpkN/znfVhL8dG8IxMAxAoSZ61Zt9WLY34HfENveuOZ7Q==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center-median@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/center-median/-/center-median-6.5.0.tgz#1b68e3f288af47f76c247d6bf671f30d8c25c974"
  integrity sha512-dT8Ndu5CiZkPrj15PBvslpuf01ky41DEYEPxS01LOxp5HOUHXp1oJxsPxvc+i/wK4BwccPNzU1vzJ0S4emd1KQ==
  dependencies:
    "@turf/center-mean" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center-of-mass@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/center-of-mass/-/center-of-mass-6.5.0.tgz#f9e6988bc296b7f763a0137ad6095f54843cf06a"
  integrity sha512-EWrriU6LraOfPN7m1jZi+1NLTKNkuIsGLZc2+Y8zbGruvUW+QV7K0nhf7iZWutlxHXTBqEXHbKue/o79IumAsQ==
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/convex" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/center@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/center/-/center-6.5.0.tgz#3bcb6bffcb8ba147430cfea84aabaed5dbdd4f07"
  integrity sha512-T8KtMTfSATWcAX088rEDKjyvQCBkUsLnK/Txb6/8WUXIeOZyHu42G7MkdkHRoHtwieLdduDdmPLFyTdG5/e7ZQ==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/centroid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/centroid/-/centroid-6.5.0.tgz#ecaa365412e5a4d595bb448e7dcdacfb49eb0009"
  integrity sha512-MwE1oq5E3isewPprEClbfU5pXljIK/GUOMbn22UM3IFPDJX0KeoyLNwghszkdmFp/qMGL/M13MMWvU+GNLXP/A==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/circle@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/circle/-/circle-6.5.0.tgz#****************************************"
  integrity sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A==
  dependencies:
    "@turf/destination" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/clean-coords@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/clean-coords/-/clean-coords-6.5.0.tgz#6690adf764ec4b649710a8a20dab7005efbea53f"
  integrity sha512-EMX7gyZz0WTH/ET7xV8MyrExywfm9qUi0/MY89yNffzGIEHuFfqwhcCqZ8O00rZIPZHUTxpmsxQSTfzJJA1CPw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/clone@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/clone/-/clone-6.5.0.tgz#895860573881ae10a02dfff95f274388b1cda51a"
  integrity sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/clusters-dbscan@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/clusters-dbscan/-/clusters-dbscan-6.5.0.tgz#e01f854d24fac4899009fc6811854424ea8f0985"
  integrity sha512-SxZEE4kADU9DqLRiT53QZBBhu8EP9skviSyl+FGj08Y01xfICM/RR9ACUdM0aEQimhpu+ZpRVcUK+2jtiCGrYQ==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    density-clustering "1.3.0"

"@turf/clusters-kmeans@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/clusters-kmeans/-/clusters-kmeans-6.5.0.tgz#aca6f66858af6476b7352a2bbbb392f9ddb7f5b4"
  integrity sha512-DwacD5+YO8kwDPKaXwT9DV46tMBVNsbi1IzdajZu1JDSWoN7yc7N9Qt88oi+p30583O0UPVkAK+A10WAQv4mUw==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    skmeans "0.9.7"

"@turf/clusters@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/clusters/-/clusters-6.5.0.tgz#a5ee7b62cdf345db2f1eafe2eb382adc186163e1"
  integrity sha512-Y6gfnTJzQ1hdLfCsyd5zApNbfLIxYEpmDibHUqR5z03Lpe02pa78JtgrgUNt1seeO/aJ4TG1NLN8V5gOrHk04g==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/collect@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/collect/-/collect-6.5.0.tgz#3749ca7d4b91fbcbe1b9b8858ed70df8b6290910"
  integrity sha512-4dN/T6LNnRg099m97BJeOcTA5fSI8cu87Ydgfibewd2KQwBexO69AnjEFqfPX3Wj+Zvisj1uAVIZbPmSSrZkjg==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    rbush "2.x"

"@turf/combine@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/combine/-/combine-6.5.0.tgz#e0f3468ac9c09c24fa7184ebbd8a79d2e595ef81"
  integrity sha512-Q8EIC4OtAcHiJB3C4R+FpB4LANiT90t17uOd851qkM2/o6m39bfN5Mv0PWqMZIHWrrosZqRqoY9dJnzz/rJxYQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/concave@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/concave/-/concave-6.5.0.tgz#19ab1a3f04087c478cebc5e631293f3eeb2e7ce4"
  integrity sha512-I/sUmUC8TC5h/E2vPwxVht+nRt+TnXIPRoztDFvS8/Y0+cBDple9inLSo9nnPXMXidrBlGXZ9vQx/BjZUJgsRQ==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/tin" "^6.5.0"
    topojson-client "3.x"
    topojson-server "3.x"

"@turf/convex@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/convex/-/convex-6.5.0.tgz#a7613e0d3795e2f5b9ce79a39271e86f54a3d354"
  integrity sha512-x7ZwC5z7PJB0SBwNh7JCeCNx7Iu+QSrH7fYgK0RhhNop13TqUlvHMirMLRgf2db1DqUetrAO2qHJeIuasquUWg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    concaveman "*"

"@turf/destination@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/destination/-/destination-6.5.0.tgz#30a84702f9677d076130e0440d3223ae503fdae1"
  integrity sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/difference@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/difference/-/difference-6.5.0.tgz#677b0d5641a93bba2e82f2c683f0d880105b3197"
  integrity sha512-l8iR5uJqvI+5Fs6leNbhPY5t/a3vipUF/3AeVLpwPQcgmedNXyheYuy07PcMGH5Jdpi5gItOiTqwiU/bUH4b3A==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    polygon-clipping "^0.15.3"

"@turf/dissolve@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/dissolve/-/dissolve-6.5.0.tgz#65debed7ef185087d842b450ebd01e81cc2e80f6"
  integrity sha512-WBVbpm9zLTp0Bl9CE35NomTaOL1c4TQCtEoO43YaAhNEWJOOIhZMFJyr8mbvYruKl817KinT3x7aYjjCMjTAsQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    polygon-clipping "^0.15.3"

"@turf/distance-weight@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/distance-weight/-/distance-weight-6.5.0.tgz#fe1fb45b5ae5ca4e09a898cb0a15c6c79ed0849e"
  integrity sha512-a8qBKkgVNvPKBfZfEJZnC3DV7dfIsC3UIdpRci/iap/wZLH41EmS90nM+BokAJflUHYy8PqE44wySGWHN1FXrQ==
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/distance@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/distance/-/distance-6.5.0.tgz#21f04d5f86e864d54e2abde16f35c15b4f36149a"
  integrity sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/ellipse@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/ellipse/-/ellipse-6.5.0.tgz#1e20cc9eb968f35ab891572892a0bffcef5e552a"
  integrity sha512-kuXtwFviw/JqnyJXF1mrR/cb496zDTSbGKtSiolWMNImYzGGkbsAsFTjwJYgD7+4FixHjp0uQPzo70KDf3AIBw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/transform-rotate" "^6.5.0"

"@turf/envelope@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/envelope/-/envelope-6.5.0.tgz#73e81b9b7ed519bd8a614d36322d6f9fbeeb0579"
  integrity sha512-9Z+FnBWvOGOU4X+fMZxYFs1HjFlkKqsddLuMknRaqcJd6t+NIv5DWvPtDL8ATD2GEExYDiFLwMdckfr1yqJgHA==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/explode@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/explode/-/explode-6.5.0.tgz#02c292cc143dd629643da5b70bb5b19b9f0f1c6b"
  integrity sha512-6cSvMrnHm2qAsace6pw9cDmK2buAlw8+tjeJVXMfMyY+w7ZUi1rprWMsY92J7s2Dar63Bv09n56/1V7+tcj52Q==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/flatten@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/flatten/-/flatten-6.5.0.tgz#0bd26161f4f1759bbad6ba9485e8ee65f3fa72a7"
  integrity sha512-IBZVwoNLVNT6U/bcUUllubgElzpMsNoCw8tLqBw6dfYg9ObGmpEjf9BIYLr7a2Yn5ZR4l7YIj2T7kD5uJjZADQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/flip@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/flip/-/flip-6.5.0.tgz#04b38eae8a78f2cf9240140b25401b16b37d20e2"
  integrity sha512-oyikJFNjt2LmIXQqgOGLvt70RgE2lyzPMloYWM7OR5oIFGRiBvqVD2hA6MNw6JewIm30fWZ8DQJw1NHXJTJPbg==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/great-circle@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/great-circle/-/great-circle-6.5.0.tgz#****************************************"
  integrity sha512-7ovyi3HaKOXdFyN7yy1yOMa8IyOvV46RC1QOQTT+RYUN8ke10eyqExwBpL9RFUPvlpoTzoYbM/+lWPogQlFncg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/helpers@6.x", "@turf/helpers@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/helpers/-/helpers-6.5.0.tgz#f79af094bd6b8ce7ed2bd3e089a8493ee6cae82e"
  integrity sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==

"@turf/hex-grid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/hex-grid/-/hex-grid-6.5.0.tgz#aa5ee46e291839d4405db74b7516c6da89ee56f7"
  integrity sha512-Ln3tc2tgZT8etDOldgc6e741Smg1CsMKAz1/Mlel+MEL5Ynv2mhx3m0q4J9IB1F3a4MNjDeVvm8drAaf9SF33g==
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/intersect" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/interpolate@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/interpolate/-/interpolate-6.5.0.tgz#9120def5d4498dd7b7d5e92a263aac3e1fd92886"
  integrity sha512-LSH5fMeiGyuDZ4WrDJNgh81d2DnNDUVJtuFryJFup8PV8jbs46lQGfI3r1DJ2p1IlEJIz3pmAZYeTfMMoeeohw==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/hex-grid" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/point-grid" "^6.5.0"
    "@turf/square-grid" "^6.5.0"
    "@turf/triangle-grid" "^6.5.0"

"@turf/intersect@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/intersect/-/intersect-6.5.0.tgz#a14e161ddd0264d0f07ac4e325553c70c421f9e6"
  integrity sha512-2legGJeKrfFkzntcd4GouPugoqPUjexPZnOvfez+3SfIMrHvulw8qV8u7pfVyn2Yqs53yoVCEjS5sEpvQ5YRQg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    polygon-clipping "^0.15.3"

"@turf/invariant@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/invariant/-/invariant-6.5.0.tgz#970afc988023e39c7ccab2341bd06979ddc7463f"
  integrity sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/isobands@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/isobands/-/isobands-6.5.0.tgz#5e0929d9d8d53147074a5cfe4533768782e2a2ce"
  integrity sha512-4h6sjBPhRwMVuFaVBv70YB7eGz+iw0bhPRnp+8JBdX1UPJSXhoi/ZF2rACemRUr0HkdVB/a1r9gC32vn5IAEkw==
  dependencies:
    "@turf/area" "^6.5.0"
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    object-assign "*"

"@turf/isolines@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/isolines/-/isolines-6.5.0.tgz#3435c7cb5a79411207a5657aa4095357cfd35831"
  integrity sha512-6ElhiLCopxWlv4tPoxiCzASWt/jMRvmp6mRYrpzOm3EUl75OhHKa/Pu6Y9nWtCMmVC/RcWtiiweUocbPLZLm0A==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    object-assign "*"

"@turf/kinks@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/kinks/-/kinks-6.5.0.tgz#80e7456367535365012f658cf1a988b39a2c920b"
  integrity sha512-ViCngdPt1eEL7hYUHR2eHR662GvCgTc35ZJFaNR6kRtr6D8plLaDju0FILeFFWSc+o8e3fwxZEJKmFj9IzPiIQ==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/length@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/length/-/length-6.5.0.tgz#ff4e9072d5f997e1c32a1311d214d184463f83fa"
  integrity sha512-5pL5/pnw52fck3oRsHDcSGrj9HibvtlrZ0QNy2OcW8qBFDNgZ4jtl6U7eATVoyWPKBHszW3dWETW+iLV7UARig==
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-arc@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-arc/-/line-arc-6.5.0.tgz#5ca35516ccf1f3a01149889d9facb39a77b07431"
  integrity sha512-I6c+V6mIyEwbtg9P9zSFF89T7QPe1DPTG3MJJ6Cm1MrAY0MdejwQKOpsvNl8LDU2ekHOlz2kHpPVR7VJsoMllA==
  dependencies:
    "@turf/circle" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/line-chunk@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-chunk/-/line-chunk-6.5.0.tgz#02cefa74564b9cf533a3ac8a5109c97cb7170d10"
  integrity sha512-i1FGE6YJaaYa+IJesTfyRRQZP31QouS+wh/pa6O3CC0q4T7LtHigyBSYjrbjSLfn2EVPYGlPCMFEqNWCOkC6zg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/length" "^6.5.0"
    "@turf/line-slice-along" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-intersect@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-intersect/-/line-intersect-6.5.0.tgz#dea48348b30c093715d2195d2dd7524aee4cf020"
  integrity sha512-CS6R1tZvVQD390G9Ea4pmpM6mJGPWoL82jD46y0q1KSor9s6HupMIo1kY4Ny+AEYQl9jd21V3Scz20eldpbTVA==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    geojson-rbush "3.x"

"@turf/line-offset@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-offset/-/line-offset-6.5.0.tgz#2bbd8fcf9ff82009b72890863da444b190e53689"
  integrity sha512-CEXZbKgyz8r72qRvPchK0dxqsq8IQBdH275FE6o4MrBkzMcoZsfSjghtXzKaz9vvro+HfIXal0sTk2mqV1lQTw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-overlap@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-overlap/-/line-overlap-6.5.0.tgz#10ebb805c2d047463379fc1f997785fa8f3f4cc1"
  integrity sha512-xHOaWLd0hkaC/1OLcStCpfq55lPHpPNadZySDXYiYjEz5HXr1oKmtMYpn0wGizsLwrOixRdEp+j7bL8dPt4ojQ==
  dependencies:
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    deep-equal "1.x"
    geojson-rbush "3.x"

"@turf/line-segment@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-segment/-/line-segment-6.5.0.tgz#ee73f3ffcb7c956203b64ed966d96af380a4dd65"
  integrity sha512-jI625Ho4jSuJESNq66Mmi290ZJ5pPZiQZruPVpmHkUw257Pew0alMmb6YrqYNnLUuiVVONxAAKXUVeeUGtycfw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/line-slice-along@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-slice-along/-/line-slice-along-6.5.0.tgz#6e7a861d72c6f80caba2c4418b69a776f0292953"
  integrity sha512-KHJRU6KpHrAj+BTgTNqby6VCTnDzG6a1sJx/I3hNvqMBLvWVA2IrkR9L9DtsQsVY63IBwVdQDqiwCuZLDQh4Ng==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/line-slice@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-slice/-/line-slice-6.5.0.tgz#7b6e0c8e8e93fdb4e65c3b9a123a2ec93a21bdb0"
  integrity sha512-vDqJxve9tBHhOaVVFXqVjF5qDzGtKWviyjbyi2QnSnxyFAmLlLnBfMX8TLQCAf2GxHibB95RO5FBE6I2KVPRuw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"

"@turf/line-split@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-split/-/line-split-6.5.0.tgz#116d7fbf714457878225187f5820ef98db7b02c2"
  integrity sha512-/rwUMVr9OI2ccJjw7/6eTN53URtGThNSD5I0GgxyFXMtxWiloRJ9MTff8jBbtPWrRka/Sh2GkwucVRAEakx9Sw==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    "@turf/square" "^6.5.0"
    "@turf/truncate" "^6.5.0"
    geojson-rbush "3.x"

"@turf/line-to-polygon@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/line-to-polygon/-/line-to-polygon-6.5.0.tgz#c919a03064a1cd5cef4c4e4d98dc786e12ffbc89"
  integrity sha512-qYBuRCJJL8Gx27OwCD1TMijM/9XjRgXH/m/TyuND4OXedBpIWlK5VbTIO2gJ8OCfznBBddpjiObLBrkuxTpN4Q==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/mask@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/mask/-/mask-6.5.0.tgz#a97f355ee071ac60d8d3782ae39e5bb4b4e26857"
  integrity sha512-RQha4aU8LpBrmrkH8CPaaoAfk0Egj5OuXtv6HuCQnHeGNOQt3TQVibTA3Sh4iduq4EPxnZfDjgsOeKtrCA19lg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    polygon-clipping "^0.15.3"

"@turf/meta@6.x", "@turf/meta@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/meta/-/meta-6.5.0.tgz#b725c3653c9f432133eaa04d3421f7e51e0418ca"
  integrity sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/midpoint@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/midpoint/-/midpoint-6.5.0.tgz#5f9428959309feccaf3f55873a8de70d4121bdce"
  integrity sha512-MyTzV44IwmVI6ec9fB2OgZ53JGNlgOpaYl9ArKoF49rXpL84F9rNATndbe0+MQIhdkw8IlzA6xVP4lZzfMNVCw==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/moran-index@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/moran-index/-/moran-index-6.5.0.tgz#456264bfb014a7b5f527807c9dcf25df3c6b2efd"
  integrity sha512-ItsnhrU2XYtTtTudrM8so4afBCYWNaB0Mfy28NZwLjB5jWuAsvyV+YW+J88+neK/ougKMTawkmjQqodNJaBeLQ==
  dependencies:
    "@turf/distance-weight" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/nearest-point-on-line@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/nearest-point-on-line/-/nearest-point-on-line-6.5.0.tgz#8e1cd2cdc0b5acaf4c8d8b3b33bb008d3cb99e7b"
  integrity sha512-WthrvddddvmymnC+Vf7BrkHGbDOUu6Z3/6bFYUGv1kxw8tiZ6n83/VG6kHz4poHOfS0RaNflzXSkmCi64fLBlg==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/nearest-point-to-line@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/nearest-point-to-line/-/nearest-point-to-line-6.5.0.tgz#5549b48690d523f9af4765fe64a3cbebfbc6bb75"
  integrity sha512-PXV7cN0BVzUZdjj6oeb/ESnzXSfWmEMrsfZSDRgqyZ9ytdiIj/eRsnOXLR13LkTdXVOJYDBuf7xt1mLhM4p6+Q==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/point-to-line-distance" "^6.5.0"
    object-assign "*"

"@turf/nearest-point@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/nearest-point/-/nearest-point-6.5.0.tgz#2f1781c26ff3f054005d4ff352042973318b92f1"
  integrity sha512-fguV09QxilZv/p94s8SMsXILIAMiaXI5PATq9d7YWijLxWUj6Q/r43kxyoi78Zmwwh1Zfqz9w+bCYUAxZ5+euA==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/planepoint@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/planepoint/-/planepoint-6.5.0.tgz#5cb788670c31a6b064ae464180d51b4d550d87de"
  integrity sha512-R3AahA6DUvtFbka1kcJHqZ7DMHmPXDEQpbU5WaglNn7NaCQg9HB0XM0ZfqWcd5u92YXV+Gg8QhC8x5XojfcM4Q==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/point-grid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/point-grid/-/point-grid-6.5.0.tgz#f628d30afe29d60dcbf54b195e46eab48a4fbfaa"
  integrity sha512-Iq38lFokNNtQJnOj/RBKmyt6dlof0yhaHEDELaWHuECm1lIZLY3ZbVMwbs+nXkwTAHjKfS/OtMheUBkw+ee49w==
  dependencies:
    "@turf/boolean-within" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/point-on-feature@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/point-on-feature/-/point-on-feature-6.5.0.tgz#37d07afeb31896e53c0833aa404993ba7d500f0c"
  integrity sha512-bDpuIlvugJhfcF/0awAQ+QI6Om1Y1FFYE8Y/YdxGRongivix850dTeXCo0mDylFdWFPGDo7Mmh9Vo4VxNwW/TA==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"

"@turf/point-to-line-distance@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/point-to-line-distance/-/point-to-line-distance-6.5.0.tgz#bc46fe09ea630aaf73f13c40b38a7df79050fff8"
  integrity sha512-opHVQ4vjUhNBly1bob6RWy+F+hsZDH9SA0UW36pIRzfpu27qipU18xup0XXEePfY6+wvhF6yL/WgCO2IbrLqEA==
  dependencies:
    "@turf/bearing" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/projection" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/points-within-polygon@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/points-within-polygon/-/points-within-polygon-6.5.0.tgz#d49f4d7cf19b7a440bf1e06f771ff4e1df13107f"
  integrity sha512-YyuheKqjliDsBDt3Ho73QVZk1VXX1+zIA2gwWvuz8bR1HXOkcuwk/1J76HuFMOQI3WK78wyAi+xbkx268PkQzQ==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/polygon-smooth@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/polygon-smooth/-/polygon-smooth-6.5.0.tgz#00ca366871cb6ea3bee44ff3ea870aaf75711733"
  integrity sha512-LO/X/5hfh/Rk4EfkDBpLlVwt3i6IXdtQccDT9rMjXEP32tRgy0VMFmdkNaXoGlSSKf/1mGqLl4y4wHd86DqKbg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/polygon-tangents@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/polygon-tangents/-/polygon-tangents-6.5.0.tgz#dc025202727ba2f3347baa95dbca4e0ffdb2ddf5"
  integrity sha512-sB4/IUqJMYRQH9jVBwqS/XDitkEfbyqRy+EH/cMRJURTg78eHunvJ708x5r6umXsbiUyQU4eqgPzEylWEQiunw==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/boolean-within" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"

"@turf/polygon-to-line@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/polygon-to-line/-/polygon-to-line-6.5.0.tgz#4dc86db66168b32bb83ce448cf966208a447d952"
  integrity sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/polygonize@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/polygonize/-/polygonize-6.5.0.tgz#8aa0f1e386e96c533a320c426aaf387020320fa3"
  integrity sha512-a/3GzHRaCyzg7tVYHo43QUChCspa99oK4yPqooVIwTC61npFzdrmnywMv0S+WZjHZwK37BrFJGFrZGf6ocmY5w==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/envelope" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/projection@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/projection/-/projection-6.5.0.tgz#d2aad862370bf03f2270701115464a8406c144b2"
  integrity sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/random@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/random/-/random-6.5.0.tgz#b19672cf4549557660034d4a303911656df7747e"
  integrity sha512-8Q25gQ/XbA7HJAe+eXp4UhcXM9aOOJFaxZ02+XSNwMvY8gtWSCBLVqRcW4OhqilgZ8PeuQDWgBxeo+BIqqFWFQ==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/rectangle-grid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/rectangle-grid/-/rectangle-grid-6.5.0.tgz#c3ef38e8cfdb763012beb1f22e2b77288a37a5cf"
  integrity sha512-yQZ/1vbW68O2KsSB3OZYK+72aWz/Adnf7m2CMKcC+aq6TwjxZjAvlbCOsNUnMAuldRUVN1ph6RXMG4e9KEvKvg==
  dependencies:
    "@turf/boolean-intersects" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/rewind@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/rewind/-/rewind-6.5.0.tgz#bc0088f8ec56f00c8eacd902bbe51e3786cb73a0"
  integrity sha512-IoUAMcHWotBWYwSYuYypw/LlqZmO+wcBpn8ysrBNbazkFNkLf3btSDZMkKJO/bvOzl55imr/Xj4fi3DdsLsbzQ==
  dependencies:
    "@turf/boolean-clockwise" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/rhumb-bearing@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/rhumb-bearing/-/rhumb-bearing-6.5.0.tgz#8c41ad62b44fb4e57c14fe790488056684eee7b9"
  integrity sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/rhumb-destination@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/rhumb-destination/-/rhumb-destination-6.5.0.tgz#12da8c85e674b182e8b0ec8ea9c5fe2186716dae"
  integrity sha512-RHNP1Oy+7xTTdRrTt375jOZeHceFbjwohPHlr9Hf68VdHHPMAWgAKqiX2YgSWDcvECVmiGaBKWus1Df+N7eE4Q==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/rhumb-distance@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/rhumb-distance/-/rhumb-distance-6.5.0.tgz#ed068004b1469512b857070fbf5cb7b7eabbe592"
  integrity sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"

"@turf/sample@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/sample/-/sample-6.5.0.tgz#00cca024514989448e57fb1bf34e9a33ed3f0755"
  integrity sha512-kSdCwY7el15xQjnXYW520heKUrHwRvnzx8ka4eYxX9NFeOxaFITLW2G7UtXb6LJK8mmPXI8Aexv23F2ERqzGFg==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/sector@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/sector/-/sector-6.5.0.tgz#599a87ebbe6ee613b4e04c5928e0ef1fc78fc16c"
  integrity sha512-cYUOkgCTWqa23SOJBqxoFAc/yGCUsPRdn/ovbRTn1zNTm/Spmk6hVB84LCKOgHqvSF25i0d2kWqpZDzLDdAPbw==
  dependencies:
    "@turf/circle" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/line-arc" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/shortest-path@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/shortest-path/-/shortest-path-6.5.0.tgz#e1fdf9b4758bd20caf845fdc03d0dc2eede2ff0e"
  integrity sha512-4de5+G7+P4hgSoPwn+SO9QSi9HY5NEV/xRJ+cmoFVRwv2CDsuOPDheHKeuIAhKyeKDvPvPt04XYWbac4insJMg==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/clean-coords" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/transform-scale" "^6.5.0"

"@turf/simplify@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/simplify/-/simplify-6.5.0.tgz#ec435460bde0985b781618b05d97146c32c8bc16"
  integrity sha512-USas3QqffPHUY184dwQdP8qsvcVH/PWBYdXY5am7YTBACaQOMAlf6AKJs9FT8jiO6fQpxfgxuEtwmox+pBtlOg==
  dependencies:
    "@turf/clean-coords" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/square-grid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/square-grid/-/square-grid-6.5.0.tgz#3a517301b42ed98aa62d727786dc5290998ddbae"
  integrity sha512-mlR0ayUdA+L4c9h7p4k3pX6gPWHNGuZkt2c5II1TJRmhLkW2557d6b/Vjfd1z9OVaajb1HinIs1FMSAPXuuUrA==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/rectangle-grid" "^6.5.0"

"@turf/square@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/square/-/square-6.5.0.tgz#ab43eef99d39c36157ab5b80416bbeba1f6b2122"
  integrity sha512-BM2UyWDmiuHCadVhHXKIx5CQQbNCpOxB6S/aCNOCLbhCeypKX5Q0Aosc5YcmCJgkwO5BERCC6Ee7NMbNB2vHmQ==
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"

"@turf/standard-deviational-ellipse@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/standard-deviational-ellipse/-/standard-deviational-ellipse-6.5.0.tgz#775c7b9a2be6546bf64ea8ac08cdcd80563f2935"
  integrity sha512-02CAlz8POvGPFK2BKK8uHGUk/LXb0MK459JVjKxLC2yJYieOBTqEbjP0qaWhiBhGzIxSMaqe8WxZ0KvqdnstHA==
  dependencies:
    "@turf/center-mean" "^6.5.0"
    "@turf/ellipse" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/points-within-polygon" "^6.5.0"

"@turf/tag@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/tag/-/tag-6.5.0.tgz#13eae85f36f9fd8c4e076714a894cb5b7716d381"
  integrity sha512-XwlBvrOV38CQsrNfrxvBaAPBQgXMljeU0DV8ExOyGM7/hvuGHJw3y8kKnQ4lmEQcmcrycjDQhP7JqoRv8vFssg==
  dependencies:
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/tesselate@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/tesselate/-/tesselate-6.5.0.tgz#de45b778f8e6a45535d8eb2aacea06f86c6b73fb"
  integrity sha512-M1HXuyZFCfEIIKkglh/r5L9H3c5QTEsnMBoZOFQiRnGPGmJWcaBissGb7mTFX2+DKE7FNWXh4TDnZlaLABB0dQ==
  dependencies:
    "@turf/helpers" "^6.5.0"
    earcut "^2.0.0"

"@turf/tin@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/tin/-/tin-6.5.0.tgz#b77bebb48237e6613ac6bc0e37a6658be8c17a09"
  integrity sha512-YLYikRzKisfwj7+F+Tmyy/LE3d2H7D4kajajIfc9mlik2+esG7IolsX/+oUz1biguDYsG0DUA8kVYXDkobukfg==
  dependencies:
    "@turf/helpers" "^6.5.0"

"@turf/transform-rotate@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/transform-rotate/-/transform-rotate-6.5.0.tgz#e50e96a8779af91d58149eedb00ffd7f6395c804"
  integrity sha512-A2Ip1v4246ZmpssxpcL0hhiVBEf4L8lGnSPWTgSv5bWBEoya2fa/0SnFX9xJgP40rMP+ZzRaCN37vLHbv1Guag==
  dependencies:
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/transform-scale@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/transform-scale/-/transform-scale-6.5.0.tgz#dcccd8b0f139de32e32225a29c107a1279137120"
  integrity sha512-VsATGXC9rYM8qTjbQJ/P7BswKWXHdnSJ35JlV4OsZyHBMxJQHftvmZJsFbOqVtQnIQIzf2OAly6rfzVV9QLr7g==
  dependencies:
    "@turf/bbox" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"

"@turf/transform-translate@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/transform-translate/-/transform-translate-6.5.0.tgz#631b13aca6402898029e03fc2d1f4bc1c667fc3e"
  integrity sha512-NABLw5VdtJt/9vSstChp93pc6oel4qXEos56RBMsPlYB8hzNTEKYtC146XJvyF4twJeeYS8RVe1u7KhoFwEM5w==
  dependencies:
    "@turf/clone" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"

"@turf/triangle-grid@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/triangle-grid/-/triangle-grid-6.5.0.tgz#75664e8b9d9c7ca4c845673134a1e0d82b5e6887"
  integrity sha512-2jToUSAS1R1htq4TyLQYPTIsoy6wg3e3BQXjm2rANzw4wPQCXGOxrur1Fy9RtzwqwljlC7DF4tg0OnWr8RjmfA==
  dependencies:
    "@turf/distance" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/intersect" "^6.5.0"

"@turf/truncate@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/truncate/-/truncate-6.5.0.tgz#c3a16cad959f1be1c5156157d5555c64b19185d8"
  integrity sha512-pFxg71pLk+eJj134Z9yUoRhIi8vqnnKvCYwdT4x/DQl/19RVdq1tV3yqOT3gcTQNfniteylL5qV1uTBDV5sgrg==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"

"@turf/turf@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/turf/-/turf-6.5.0.tgz#49cd07b942a757f3ebbdba6cb294bbb864825a83"
  integrity sha512-ipMCPnhu59bh92MNt8+pr1VZQhHVuTMHklciQURo54heoxRzt1neNYZOBR6jdL+hNsbDGAECMuIpAutX+a3Y+w==
  dependencies:
    "@turf/along" "^6.5.0"
    "@turf/angle" "^6.5.0"
    "@turf/area" "^6.5.0"
    "@turf/bbox" "^6.5.0"
    "@turf/bbox-clip" "^6.5.0"
    "@turf/bbox-polygon" "^6.5.0"
    "@turf/bearing" "^6.5.0"
    "@turf/bezier-spline" "^6.5.0"
    "@turf/boolean-clockwise" "^6.5.0"
    "@turf/boolean-contains" "^6.5.0"
    "@turf/boolean-crosses" "^6.5.0"
    "@turf/boolean-disjoint" "^6.5.0"
    "@turf/boolean-equal" "^6.5.0"
    "@turf/boolean-intersects" "^6.5.0"
    "@turf/boolean-overlap" "^6.5.0"
    "@turf/boolean-parallel" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/boolean-point-on-line" "^6.5.0"
    "@turf/boolean-within" "^6.5.0"
    "@turf/buffer" "^6.5.0"
    "@turf/center" "^6.5.0"
    "@turf/center-mean" "^6.5.0"
    "@turf/center-median" "^6.5.0"
    "@turf/center-of-mass" "^6.5.0"
    "@turf/centroid" "^6.5.0"
    "@turf/circle" "^6.5.0"
    "@turf/clean-coords" "^6.5.0"
    "@turf/clone" "^6.5.0"
    "@turf/clusters" "^6.5.0"
    "@turf/clusters-dbscan" "^6.5.0"
    "@turf/clusters-kmeans" "^6.5.0"
    "@turf/collect" "^6.5.0"
    "@turf/combine" "^6.5.0"
    "@turf/concave" "^6.5.0"
    "@turf/convex" "^6.5.0"
    "@turf/destination" "^6.5.0"
    "@turf/difference" "^6.5.0"
    "@turf/dissolve" "^6.5.0"
    "@turf/distance" "^6.5.0"
    "@turf/distance-weight" "^6.5.0"
    "@turf/ellipse" "^6.5.0"
    "@turf/envelope" "^6.5.0"
    "@turf/explode" "^6.5.0"
    "@turf/flatten" "^6.5.0"
    "@turf/flip" "^6.5.0"
    "@turf/great-circle" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/hex-grid" "^6.5.0"
    "@turf/interpolate" "^6.5.0"
    "@turf/intersect" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    "@turf/isobands" "^6.5.0"
    "@turf/isolines" "^6.5.0"
    "@turf/kinks" "^6.5.0"
    "@turf/length" "^6.5.0"
    "@turf/line-arc" "^6.5.0"
    "@turf/line-chunk" "^6.5.0"
    "@turf/line-intersect" "^6.5.0"
    "@turf/line-offset" "^6.5.0"
    "@turf/line-overlap" "^6.5.0"
    "@turf/line-segment" "^6.5.0"
    "@turf/line-slice" "^6.5.0"
    "@turf/line-slice-along" "^6.5.0"
    "@turf/line-split" "^6.5.0"
    "@turf/line-to-polygon" "^6.5.0"
    "@turf/mask" "^6.5.0"
    "@turf/meta" "^6.5.0"
    "@turf/midpoint" "^6.5.0"
    "@turf/moran-index" "^6.5.0"
    "@turf/nearest-point" "^6.5.0"
    "@turf/nearest-point-on-line" "^6.5.0"
    "@turf/nearest-point-to-line" "^6.5.0"
    "@turf/planepoint" "^6.5.0"
    "@turf/point-grid" "^6.5.0"
    "@turf/point-on-feature" "^6.5.0"
    "@turf/point-to-line-distance" "^6.5.0"
    "@turf/points-within-polygon" "^6.5.0"
    "@turf/polygon-smooth" "^6.5.0"
    "@turf/polygon-tangents" "^6.5.0"
    "@turf/polygon-to-line" "^6.5.0"
    "@turf/polygonize" "^6.5.0"
    "@turf/projection" "^6.5.0"
    "@turf/random" "^6.5.0"
    "@turf/rewind" "^6.5.0"
    "@turf/rhumb-bearing" "^6.5.0"
    "@turf/rhumb-destination" "^6.5.0"
    "@turf/rhumb-distance" "^6.5.0"
    "@turf/sample" "^6.5.0"
    "@turf/sector" "^6.5.0"
    "@turf/shortest-path" "^6.5.0"
    "@turf/simplify" "^6.5.0"
    "@turf/square" "^6.5.0"
    "@turf/square-grid" "^6.5.0"
    "@turf/standard-deviational-ellipse" "^6.5.0"
    "@turf/tag" "^6.5.0"
    "@turf/tesselate" "^6.5.0"
    "@turf/tin" "^6.5.0"
    "@turf/transform-rotate" "^6.5.0"
    "@turf/transform-scale" "^6.5.0"
    "@turf/transform-translate" "^6.5.0"
    "@turf/triangle-grid" "^6.5.0"
    "@turf/truncate" "^6.5.0"
    "@turf/union" "^6.5.0"
    "@turf/unkink-polygon" "^6.5.0"
    "@turf/voronoi" "^6.5.0"

"@turf/union@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/union/-/union-6.5.0.tgz#82d28f55190608f9c7d39559b7f543393b03b82d"
  integrity sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    polygon-clipping "^0.15.3"

"@turf/unkink-polygon@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/unkink-polygon/-/unkink-polygon-6.5.0.tgz#9e54186dcce08d7e62f608c8fa2d3f0342ebe826"
  integrity sha512-8QswkzC0UqKmN1DT6HpA9upfa1HdAA5n6bbuzHy8NJOX8oVizVAqfEPY0wqqTgboDjmBR4yyImsdPGUl3gZ8JQ==
  dependencies:
    "@turf/area" "^6.5.0"
    "@turf/boolean-point-in-polygon" "^6.5.0"
    "@turf/helpers" "^6.5.0"
    "@turf/meta" "^6.5.0"
    rbush "^2.0.1"

"@turf/voronoi@^6.5.0":
  version "6.5.0"
  resolved "https://tech.suitbim.com/verdaccio/@turf/voronoi/-/voronoi-6.5.0.tgz#afe6715a5c7eff687434010cde45cd4822489434"
  integrity sha512-C/xUsywYX+7h1UyNqnydHXiun4UPjK88VDghtoRypR9cLlb7qozkiLRphQxxsCM0KxyxpVPHBVQXdAL3+Yurow==
  dependencies:
    "@turf/helpers" "^6.5.0"
    "@turf/invariant" "^6.5.0"
    d3-voronoi "1.1.2"

"@tweenjs/tween.js@^18.6.4":
  version "18.6.4"
  resolved "https://tech.suitbim.com/verdaccio/@tweenjs/tween.js/-/tween.js-18.6.4.tgz#40a3d0a93647124872dec8e0fd1bd5926695b6ca"
  integrity sha512-lB9lMjuqjtuJrx7/kOkqQBtllspPIN+96OvTCeJ2j5FEzinoAXTdAMFnDAQT1KVPRlnYfBrqxtqP66vDM40xxQ==

"@types/eslint@^8.4.5":
  version "8.56.10"
  resolved "https://tech.suitbim.com/verdaccio/@types/eslint/-/eslint-8.56.10.tgz#eb2370a73bf04a901eeba8f22595c7ee0f7eb58d"
  integrity sha512-Shavhk87gCtY2fhXDctcfS3e6FdxWkCx1iUZ9eEUbh7rTqlZT0/IzOkCOVt0fCjcFuZ9FPYfuezTBImfHCDBGQ==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.5"
  resolved "https://tech.suitbim.com/verdaccio/@types/estree/-/estree-1.0.5.tgz#a6ce3e556e00fd9895dd872dd172ad0d4bd687f4"
  integrity sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==

"@types/geojson@7946.0.8":
  version "7946.0.8"
  resolved "https://tech.suitbim.com/verdaccio/@types/geojson/-/geojson-7946.0.8.tgz#30744afdb385e2945e22f3b033f897f76b1f12ca"
  integrity sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA==

"@types/json-schema@*", "@types/json-schema@^7.0.12":
  version "7.0.15"
  resolved "https://tech.suitbim.com/verdaccio/@types/json-schema/-/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/lodash-es@^4.17.6":
  version "4.17.12"
  resolved "https://tech.suitbim.com/verdaccio/@types/lodash-es/-/lodash-es-4.17.12.tgz#65f6d1e5f80539aa7cfbfc962de5def0cf4f341b"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.182":
  version "4.17.1"
  resolved "https://tech.suitbim.com/verdaccio/@types/lodash/-/lodash-4.17.1.tgz#0fabfcf2f2127ef73b119d98452bd317c4a17eb8"
  integrity sha512-X+2qazGS3jxLAIz5JDXDzglAF3KpijdhFxlf/V1+hEsOUc+HnWi81L/uv/EvGuV90WY+7mPGFCUDGfQC3Gj95Q==

"@types/minimist@^1.2.2":
  version "1.2.5"
  resolved "https://tech.suitbim.com/verdaccio/@types/minimist/-/minimist-1.2.5.tgz#ec10755e871497bcd83efe927e43ec46e8c0747e"
  integrity sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==

"@types/node@*", "@types/node@>=13.7.0":
  version "20.12.10"
  resolved "https://tech.suitbim.com/verdaccio/@types/node/-/node-20.12.10.tgz#8f0c3f12b0f075eee1fe20c1afb417e9765bef76"
  integrity sha512-Eem5pH9pmWBHoGAT8Dr5fdc5rYA+4NAovdM4EktRPVAAiJhmWWfQrA0cFhAbOsQdSfIHjAud6YdkbL69+zSKjw==
  dependencies:
    undici-types "~5.26.4"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "https://tech.suitbim.com/verdaccio/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz#56e2cc26c397c038fab0e3a917a12d5c5909e901"
  integrity sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==

"@types/semver@^7.5.0":
  version "7.5.8"
  resolved "https://tech.suitbim.com/verdaccio/@types/semver/-/semver-7.5.8.tgz#8268a8c57a3e4abd25c165ecd36237db7948a55e"
  integrity sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==

"@types/web-bluetooth@^0.0.16":
  version "0.0.16"
  resolved "https://tech.suitbim.com/verdaccio/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz#1d12873a8e49567371f2a75fe3e7f7edca6662d8"
  integrity sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==

"@types/web-bluetooth@^0.0.20":
  version "0.0.20"
  resolved "https://tech.suitbim.com/verdaccio/@types/web-bluetooth/-/web-bluetooth-0.0.20.tgz#f066abfcd1cbe66267cdbbf0de010d8a41b41597"
  integrity sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==

"@typescript-eslint/scope-manager@6.21.0":
  version "6.21.0"
  resolved "https://tech.suitbim.com/verdaccio/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz#ea8a9bfc8f1504a6ac5d59a6df308d3a0630a2b1"
  integrity sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"

"@typescript-eslint/types@6.21.0":
  version "6.21.0"
  resolved "https://tech.suitbim.com/verdaccio/@typescript-eslint/types/-/types-6.21.0.tgz#205724c5123a8fef7ecd195075fa6e85bac3436d"
  integrity sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==

"@typescript-eslint/typescript-estree@6.21.0":
  version "6.21.0"
  resolved "https://tech.suitbim.com/verdaccio/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz#c47ae7901db3b8bddc3ecd73daff2d0895688c46"
  integrity sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/visitor-keys" "6.21.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    minimatch "9.0.3"
    semver "^7.5.4"
    ts-api-utils "^1.0.1"

"@typescript-eslint/utils@^6.4.1":
  version "6.21.0"
  resolved "https://tech.suitbim.com/verdaccio/@typescript-eslint/utils/-/utils-6.21.0.tgz#4714e7a6b39e773c1c8e97ec587f520840cd8134"
  integrity sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "@types/json-schema" "^7.0.12"
    "@types/semver" "^7.5.0"
    "@typescript-eslint/scope-manager" "6.21.0"
    "@typescript-eslint/types" "6.21.0"
    "@typescript-eslint/typescript-estree" "6.21.0"
    semver "^7.5.4"

"@typescript-eslint/visitor-keys@6.21.0":
  version "6.21.0"
  resolved "https://tech.suitbim.com/verdaccio/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz#87a99d077aa507e20e238b11d56cc26ade45fe47"
  integrity sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==
  dependencies:
    "@typescript-eslint/types" "6.21.0"
    eslint-visitor-keys "^3.4.1"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"
  resolved "https://tech.suitbim.com/verdaccio/@ungap/structured-clone/-/structured-clone-1.2.0.tgz#756641adb587851b5ccb3e095daf27ae581c8406"
  integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==

"@unocss/astro@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/astro/-/astro-0.55.7.tgz#381e6a1768b7737c60c49ff732899023f53742dd"
  integrity sha512-mw8r14ArxUQBVCCisAJlF/WsZb650iBsduD/lXMk56N/nQ3MMArCcn62kcAxgZSb5tfIOQGQu/tbR8hEcD8y2g==
  dependencies:
    "@unocss/core" "0.55.7"
    "@unocss/reset" "0.55.7"
    "@unocss/vite" "0.55.7"

"@unocss/cli@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/cli/-/cli-0.55.7.tgz#b2c01cc7d750391050c6e45f62015bddce4428a5"
  integrity sha512-ZHX2SR2WQbKfcmgOOHjBLB3V57Ct76Zb76YULzBj2EVX43lX/YDCVG87n6ePDY7rOcjCAthjrFQYCLV5KVLKHg==
  dependencies:
    "@ampproject/remapping" "^2.2.1"
    "@rollup/pluginutils" "^5.0.3"
    "@unocss/config" "0.55.7"
    "@unocss/core" "0.55.7"
    "@unocss/preset-uno" "0.55.7"
    cac "^6.7.14"
    chokidar "^3.5.3"
    colorette "^2.0.20"
    consola "^3.2.3"
    fast-glob "^3.3.1"
    magic-string "^0.30.3"
    pathe "^1.1.1"
    perfect-debounce "^1.0.0"

"@unocss/config@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/config/-/config-0.55.7.tgz#6a9ac6feed23d6eee1f4c07bcca2e6a68bba0d3b"
  integrity sha512-+X6rPScyFEWbkZyCyM+HfoJhJNN+CEl2n2izWkm0kuDj3w9fY9B3f/0dsk+jmx/gJEI5Y797q9zspNMNDib1AA==
  dependencies:
    "@unocss/core" "0.55.7"
    unconfig "^0.3.10"

"@unocss/core@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/core/-/core-0.55.7.tgz#84b6b936018f07ee91a8e70f6baa154a8080dce1"
  integrity sha512-c+bWe844Xjlwc1EPwHj0+n3LpntJG7ELPbEOOxNIG+CQdcEX0l1G0rkM8+nKstJ9WJmgpf1HdJQLVMF62HXvhw==

"@unocss/eslint-config@^0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/eslint-config/-/eslint-config-0.55.7.tgz#092cf6903127efba912f22144acba6b1308fbdc7"
  integrity sha512-ykRUpPeT5kJSXHAduOJ2PxnEoaYsGOS2jG9iVjbbvER36ZQ+71xdwep/qpBlAr4LWLeoICy7Om+No1r43ZDntw==
  dependencies:
    "@unocss/eslint-plugin" "0.55.7"

"@unocss/eslint-plugin@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/eslint-plugin/-/eslint-plugin-0.55.7.tgz#846c7ef804a3dca634227cf0410cfce2f9a97163"
  integrity sha512-U+poFU/GJH8NvFBQxLp64zRRfnLWSbY41+Q8Vnlw/nP/tizZumO2lNJ+UBIGFLa1TwyMulFjru6CXt2uSFKtaA==
  dependencies:
    "@typescript-eslint/utils" "^6.4.1"
    "@unocss/config" "0.55.7"
    "@unocss/core" "0.55.7"
    magic-string "^0.30.3"
    synckit "^0.8.5"

"@unocss/extractor-arbitrary-variants@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/extractor-arbitrary-variants/-/extractor-arbitrary-variants-0.55.7.tgz#0680b77808b18073d7db870c81fc9f6f90102719"
  integrity sha512-imK2g/frlo5Ag0uVB+C/Psyo5+9AnqhoRAgYa6gyrQ/TJnrnwf+M3jFngU9evIMHw92vig1DGfPa2ZId901DwQ==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/inspector@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/inspector/-/inspector-0.55.7.tgz#d1d34cf2cc70e468f5dc8a9d1530f62e642adfb3"
  integrity sha512-N0mjZozDDyqx8Mh6C/ZlMTlDzGiq22sXY/hPRX55Cf44WZI4W/ZWajqAAp42B+lw2MN0k1FYEMIAwn9n+xgq/g==
  dependencies:
    gzip-size "^6.0.0"
    sirv "^2.0.3"

"@unocss/postcss@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/postcss/-/postcss-0.55.7.tgz#1422777bed8071a44c62fa94a841090a60d87816"
  integrity sha512-53Z/yv/CNdlTqKZQ9gpYRoLZSuzQ28J0SDrGCdzwjLcvHG/FD7/x1S7yxE7cUp/4sjvLL15HSzkWq8vNy6SkwQ==
  dependencies:
    "@unocss/config" "0.55.7"
    "@unocss/core" "0.55.7"
    css-tree "^2.3.1"
    fast-glob "^3.3.1"
    magic-string "^0.30.3"
    postcss "^8.4.28"

"@unocss/preset-attributify@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-attributify/-/preset-attributify-0.55.7.tgz#4e3baaee774cbbb97509fb2fbb07a837fe2de1cf"
  integrity sha512-L1sNw3DyM4mymIm4DBTTTOllk8LmhYlWMgDlaAW2MYWygjqDCsp99wRKT2175Ya5xHYBA6XetMoBryZD23qJYQ==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/preset-icons@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-icons/-/preset-icons-0.55.7.tgz#b4eb35932e7058236174f415cc0a2a2681904e0e"
  integrity sha512-JXLOHkyEKKAjLTqjAxYfhwln05WXilGg3jctkZWKpMNawPaonrGt3kZT12YMuMmOryxk7UcyKB0dtYc+p3QYvw==
  dependencies:
    "@iconify/utils" "^2.1.9"
    "@unocss/core" "0.55.7"
    ofetch "^1.3.3"

"@unocss/preset-mini@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-mini/-/preset-mini-0.55.7.tgz#0742b61aed5266f0e9a65b9d60c273671f096833"
  integrity sha512-ZCskE2uprjGkpQezEPM6KPMf84rIZEUNc1p2DxWVHaFUPRV24/JSNsO4PsKrQgNIb2dLQxzPNlMzQJI7ssdBXQ==
  dependencies:
    "@unocss/core" "0.55.7"
    "@unocss/extractor-arbitrary-variants" "0.55.7"

"@unocss/preset-rem-to-px@^0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-rem-to-px/-/preset-rem-to-px-0.55.7.tgz#ba2c97823541a5db91202b539d9a0aaa45c8b7a3"
  integrity sha512-aGSOFivnKwyDdW4Z9dVJuAy7pi2KffmV8oSnuKiPG5B/mT1YluicCRKniDRR/5Km8kkdhjsV/yzqGPaWIOiJhQ==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/preset-tagify@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-tagify/-/preset-tagify-0.55.7.tgz#4cbe1854fbe2ffca684a43998ef44a64d5f69497"
  integrity sha512-aDsuN3a/ZirbCDKpFsue9tc8MHs3l0Rl81n2ZOdIrJoZW4YWyydMVl++cz/HERZW81ZySK8EJKwGBaMJMgsnHA==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/preset-typography@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-typography/-/preset-typography-0.55.7.tgz#9bb98482d84c069b9a35564b7583e2772a76c7cc"
  integrity sha512-hLV4nsgsDIk66pt7Ej4NYUmaGtI2EfGb1h2yl5FmBtdtACrgPq+Skr2Br9Iq+Bj1QFhbsMOWLDdbojFQwBdH6A==
  dependencies:
    "@unocss/core" "0.55.7"
    "@unocss/preset-mini" "0.55.7"

"@unocss/preset-uno@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-uno/-/preset-uno-0.55.7.tgz#254b329e91b2fe81457dbbd7721f862517af5376"
  integrity sha512-z4pCxOv/OU1ARo++cvbijWNW2zy/EVTMqJXa+SEep9b99wFXPQE3gaPvLdURp/e5f1PoxVyPZ6JiBknbClSDuA==
  dependencies:
    "@unocss/core" "0.55.7"
    "@unocss/preset-mini" "0.55.7"
    "@unocss/preset-wind" "0.55.7"

"@unocss/preset-web-fonts@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-web-fonts/-/preset-web-fonts-0.55.7.tgz#01bba1276de36cfc0bbe25a3f684fceb034616ed"
  integrity sha512-ygAz0540kdBapErW2BcObWfQT/6g0SpVUPYg92PPiZD57CZAvuNXiYTfFMRXd88QrBL1zIrZ6NrzY0NZ645H+w==
  dependencies:
    "@unocss/core" "0.55.7"
    ofetch "^1.3.3"

"@unocss/preset-wind@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/preset-wind/-/preset-wind-0.55.7.tgz#86f927b64afcaaff2b0a1475da09871d1dbc6ba2"
  integrity sha512-vLi0mtYDnvx3uYtBR4fSCR52T59drTUp3XVAAqQTbhvRctnSWm65MWE4G+gqdt2qQ9fM4SVCsxLLaXuJkI2eqw==
  dependencies:
    "@unocss/core" "0.55.7"
    "@unocss/preset-mini" "0.55.7"

"@unocss/reset@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/reset/-/reset-0.55.7.tgz#9489a49c74795ea1488e3afbeb743e67e90c7749"
  integrity sha512-yvmLhxqUNgf6wue7IvhV/FdrQW9H9LF1Bmmhwwaiz2aV0E74aN4pbuYPZwNq3YafsQvNQ0UdtuXjddY4QMRCPw==

"@unocss/scope@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/scope/-/scope-0.55.7.tgz#dcc4e6cf0c707c5807056498bac88a8d99a64dcb"
  integrity sha512-r0CaS1aSpcC37ztqOJ3qaWIzM6zwdlX8r0rib2vTvWTckw1J0ocVhjNkWRBM9kRWte006JhecdiZzXNHA40akg==

"@unocss/transformer-attributify-jsx-babel@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/transformer-attributify-jsx-babel/-/transformer-attributify-jsx-babel-0.55.7.tgz#2c8299904cc386500495612cd3ef57ed45402ce7"
  integrity sha512-xl5K/Zg7tLyI6Oee+xHgvBm0gSEviYdBDwaGC4O6cP9VXTBm6waz9NUU6CmmVYKh4dSeLQ1PKNboMeg2nFuJMw==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/transformer-attributify-jsx@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/transformer-attributify-jsx/-/transformer-attributify-jsx-0.55.7.tgz#5c7d335b8d491bc375c9da1f2648bd91ad63664f"
  integrity sha512-ZyUBc0wguBhd+nbIlcrSYpmzKtqBi+8BII8SK4lIB/Ol1wBboByPTjBENsQkxRyffp5K9VTuZZ/LamFgPGOWDg==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/transformer-compile-class@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/transformer-compile-class/-/transformer-compile-class-0.55.7.tgz#289b05b25881e1814b6ce96ee3ddde6891d81362"
  integrity sha512-tiYiT9EG4ucSBvMo+9Hv43GY0YvXQjfQCXDhDm3tcJyreMg6BRMO412eir54RBS+JAdNU0DUoITVYu+PkF7hLg==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/transformer-directives@0.55.7", "@unocss/transformer-directives@^0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/transformer-directives/-/transformer-directives-0.55.7.tgz#f82f72f86c3e0024d27097ec75d4cad3ce060a2c"
  integrity sha512-xNmR40FssHWYJSmJv/9TQC2IdTyZPV8U3Iv/PIuke1zndMwMciclghEFiw0wSeRmhoRI7iFZck5EI/Bokyo7CQ==
  dependencies:
    "@unocss/core" "0.55.7"
    css-tree "^2.3.1"

"@unocss/transformer-variant-group@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/transformer-variant-group/-/transformer-variant-group-0.55.7.tgz#a9bd5b9dc761549f44e9993311d44bd5352c466b"
  integrity sha512-uLyZ08XXVriUDenZCTGA3xGgMD3B9GVr6mSz002pDlLpQDi8FcMQTOGg8X4ViCGzS3l03S/+r+JY7kJTpMFa9w==
  dependencies:
    "@unocss/core" "0.55.7"

"@unocss/vite@0.55.7":
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/@unocss/vite/-/vite-0.55.7.tgz#8814f2676ddf98220b61db2694cf11f730f55ace"
  integrity sha512-xmdyDnt9Ag4o7DGl22/P6MaB+HSjWOQw9qYYzIefSv3SVUvn3cEhIX/PCWqFp8Kts2HyvAoJLbZmygSf1XdZNQ==
  dependencies:
    "@ampproject/remapping" "^2.2.1"
    "@rollup/pluginutils" "^5.0.3"
    "@unocss/config" "0.55.7"
    "@unocss/core" "0.55.7"
    "@unocss/inspector" "0.55.7"
    "@unocss/scope" "0.55.7"
    "@unocss/transformer-directives" "0.55.7"
    chokidar "^3.5.3"
    fast-glob "^3.3.1"
    magic-string "^0.30.3"

"@vitejs/plugin-vue-jsx@^3.0.1":
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/@vitejs/plugin-vue-jsx/-/plugin-vue-jsx-3.1.0.tgz#9953fd9456539e1f0f253bf0fcd1289e66c67cd1"
  integrity sha512-w9M6F3LSEU5kszVb9An2/MmXNxocAnUb3WhRr8bHlimhDrXNt6n6D2nJQR3UXpGlZHh/EsgouOHCsM8V3Ln+WA==
  dependencies:
    "@babel/core" "^7.23.3"
    "@babel/plugin-transform-typescript" "^7.23.3"
    "@vue/babel-plugin-jsx" "^1.1.5"

"@vitejs/plugin-vue@^5.0.3":
  version "5.0.4"
  resolved "https://tech.suitbim.com/verdaccio/@vitejs/plugin-vue/-/plugin-vue-5.0.4.tgz#508d6a0f2440f86945835d903fcc0d95d1bb8a37"
  integrity sha512-WS3hevEszI6CEVEx28F8RjTX97k3KsrcY6kvTg7+Whm5y3oYvcqzVeGCU3hxSAn4uY2CLCkeokkGKpoctccilQ==

"@vue/babel-helper-vue-transform-on@1.2.2":
  version "1.2.2"
  resolved "https://tech.suitbim.com/verdaccio/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.2.2.tgz#7f1f817a4f00ad531651a8d1d22e22d9e42807ef"
  integrity sha512-nOttamHUR3YzdEqdM/XXDyCSdxMA9VizUKoroLX6yTyRtggzQMHXcmwh8a7ZErcJttIBIc9s68a1B8GZ+Dmvsw==

"@vue/babel-plugin-jsx@^1.1.5":
  version "1.2.2"
  resolved "https://tech.suitbim.com/verdaccio/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.2.2.tgz#eb426fb4660aa510bb8d188ff0ec140405a97d8a"
  integrity sha512-nYTkZUVTu4nhP199UoORePsql0l+wj7v/oyQjtThUVhJl1U+6qHuoVhIvR3bf7eVKjbCK+Cs2AWd7mi9Mpz9rA==
  dependencies:
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/template" "^7.23.9"
    "@babel/traverse" "^7.23.9"
    "@babel/types" "^7.23.9"
    "@vue/babel-helper-vue-transform-on" "1.2.2"
    "@vue/babel-plugin-resolve-type" "1.2.2"
    camelcase "^6.3.0"
    html-tags "^3.3.1"
    svg-tags "^1.0.0"

"@vue/babel-plugin-resolve-type@1.2.2":
  version "1.2.2"
  resolved "https://tech.suitbim.com/verdaccio/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.2.2.tgz#66844898561da6449e0f4a261b0c875118e0707b"
  integrity sha512-EntyroPwNg5IPVdUJupqs0CFzuf6lUrVvCspmv2J1FITLeGnUCuoGNNk78dgCusxEiYj6RMkTJflGSxk5aIC4A==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/parser" "^7.23.9"
    "@vue/compiler-sfc" "^3.4.15"

"@vue/compiler-core@3.4.26":
  version "3.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-core/-/compiler-core-3.4.26.tgz#d507886520e83a6f8339ed55ed0b2b5d84b44b73"
  integrity sha512-N9Vil6Hvw7NaiyFUFBPXrAyETIGlQ8KcFMkyk6hW1Cl6NvoqvP+Y8p1Eqvx+UdqsnrnI9+HMUEJegzia3mhXmQ==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/shared" "3.4.26"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-core@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-core/-/compiler-core-3.4.27.tgz#e69060f4b61429fe57976aa5872cfa21389e4d91"
  integrity sha512-E+RyqY24KnyDXsCuQrI+mlcdW3ALND6U7Gqa/+bVwbcpcR3BRRIckFoz7Qyd4TTlnugtwuI7YgjbvsLmxb+yvg==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/shared" "3.4.27"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@3.4.26", "@vue/compiler-dom@^3.2.47":
  version "3.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-dom/-/compiler-dom-3.4.26.tgz#acc7b788b48152d087d4bb9e655b795e3dbec554"
  integrity sha512-4CWbR5vR9fMg23YqFOhr6t6WB1Fjt62d6xdFPyj8pxrYub7d+OgZaObMsoxaF9yBUHPMiPFK303v61PwAuGvZA==
  dependencies:
    "@vue/compiler-core" "3.4.26"
    "@vue/shared" "3.4.26"

"@vue/compiler-dom@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-dom/-/compiler-dom-3.4.27.tgz#d51d35f40d00ce235d7afc6ad8b09dfd92b1cc1c"
  integrity sha512-kUTvochG/oVgE1w5ViSr3KUBh9X7CWirebA3bezTbB5ZKBQZwR2Mwj9uoSKRMFcz4gSMzzLXBPD6KpCLb9nvWw==
  dependencies:
    "@vue/compiler-core" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/compiler-sfc@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-sfc/-/compiler-sfc-3.4.27.tgz#399cac1b75c6737bf5440dc9cf3c385bb2959701"
  integrity sha512-nDwntUEADssW8e0rrmE0+OrONwmRlegDA1pD6QhVeXxjIytV03yDqTey9SBDiALsvAd5U4ZrEKbMyVXhX6mCGA==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/compiler-core" "3.4.27"
    "@vue/compiler-dom" "3.4.27"
    "@vue/compiler-ssr" "3.4.27"
    "@vue/shared" "3.4.27"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.38"
    source-map-js "^1.2.0"

"@vue/compiler-sfc@^3.4.15":
  version "3.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-sfc/-/compiler-sfc-3.4.26.tgz#c679f206829954c3c078d8a9be76d0098b8377ae"
  integrity sha512-It1dp+FAOCgluYSVYlDn5DtZBxk1NCiJJfu2mlQqa/b+k8GL6NG/3/zRbJnHdhV2VhxFghaDq5L4K+1dakW6cw==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/compiler-core" "3.4.26"
    "@vue/compiler-dom" "3.4.26"
    "@vue/compiler-ssr" "3.4.26"
    "@vue/shared" "3.4.26"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.38"
    source-map-js "^1.2.0"

"@vue/compiler-ssr@3.4.26":
  version "3.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-ssr/-/compiler-ssr-3.4.26.tgz#22842d8adfff972d87bb798b8d496111f7f814b5"
  integrity sha512-FNwLfk7LlEPRY/g+nw2VqiDKcnDTVdCfBREekF8X74cPLiWHUX6oldktf/Vx28yh4STNy7t+/yuLoMBBF7YDiQ==
  dependencies:
    "@vue/compiler-dom" "3.4.26"
    "@vue/shared" "3.4.26"

"@vue/compiler-ssr@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/compiler-ssr/-/compiler-ssr-3.4.27.tgz#2a8ecfef1cf448b09be633901a9c020360472e3d"
  integrity sha512-CVRzSJIltzMG5FcidsW0jKNQnNRYC8bT21VegyMMtHmhW3UOI7knmUehzswXLrExDLE6lQCZdrhD4ogI7c+vuw==
  dependencies:
    "@vue/compiler-dom" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/devtools-api@^6.5.0", "@vue/devtools-api@^6.5.1":
  version "6.6.1"
  resolved "https://tech.suitbim.com/verdaccio/@vue/devtools-api/-/devtools-api-6.6.1.tgz#7c14346383751d9f6ad4bea0963245b30220ef83"
  integrity sha512-LgPscpE3Vs0x96PzSSB4IGVSZXZBZHpfxs+ZA1d+VEPwHdOXowy/Y2CsvCAIFrf+ssVU1pD1jidj505EpUnfbA==

"@vue/reactivity@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/reactivity/-/reactivity-3.4.27.tgz#6ece72331bf719953f5eaa95ec60b2b8d49e3791"
  integrity sha512-kK0g4NknW6JX2yySLpsm2jlunZJl2/RJGZ0H9ddHdfBVHcNzxmQ0sS0b09ipmBoQpY8JM2KmUw+a6sO8Zo+zIA==
  dependencies:
    "@vue/shared" "3.4.27"

"@vue/runtime-core@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/runtime-core/-/runtime-core-3.4.27.tgz#1b6e1d71e4604ba7442dd25ed22e4a1fc6adbbda"
  integrity sha512-7aYA9GEbOOdviqVvcuweTLe5Za4qBZkUY7SvET6vE8kyypxVgaT1ixHLg4urtOlrApdgcdgHoTZCUuTGap/5WA==
  dependencies:
    "@vue/reactivity" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/runtime-dom@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/runtime-dom/-/runtime-dom-3.4.27.tgz#fe8d1ce9bbe8921d5dd0ad5c10df0e04ef7a5ee7"
  integrity sha512-ScOmP70/3NPM+TW9hvVAz6VWWtZJqkbdf7w6ySsws+EsqtHvkhxaWLecrTorFxsawelM5Ys9FnDEMt6BPBDS0Q==
  dependencies:
    "@vue/runtime-core" "3.4.27"
    "@vue/shared" "3.4.27"
    csstype "^3.1.3"

"@vue/server-renderer@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/server-renderer/-/server-renderer-3.4.27.tgz#3306176f37e648ba665f97dda3ce705687be63d2"
  integrity sha512-dlAMEuvmeA3rJsOMJ2J1kXU7o7pOxgsNHVr9K8hB3ImIkSuBrIdy0vF66h8gf8Tuinf1TK3mPAz2+2sqyf3KzA==
  dependencies:
    "@vue/compiler-ssr" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/shared@3.4.26":
  version "3.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@vue/shared/-/shared-3.4.26.tgz#f17854fb1faf889854aed4b23b60e86a8cab6403"
  integrity sha512-Fg4zwR0GNnjzodMt3KRy2AWGMKQXByl56+4HjN87soxLNU9P5xcJkstAlIeEF3cU6UYOzmJl1tV0dVPGIljCnQ==

"@vue/shared@3.4.27":
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/@vue/shared/-/shared-3.4.27.tgz#f05e3cd107d157354bb4ae7a7b5fc9cf73c63b50"
  integrity sha512-DL3NmY2OFlqmYYrzp39yi3LDkKxa5vZVwxWdQ3rG0ekuWscHraeIbnI8t+aZK7qhYqEqWKTUdijadunb9pnrgA==

"@vueuse/core@^10.4.1":
  version "10.9.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/core/-/core-10.9.0.tgz#7d779a95cf0189de176fee63cee4ba44b3c85d64"
  integrity sha512-/1vjTol8SXnx6xewDEKfS0Ra//ncg4Hb0DaZiwKf7drgfMsKFExQ+FnnENcN6efPen+1kIzhLQoGSy0eDUVOMg==
  dependencies:
    "@types/web-bluetooth" "^0.0.20"
    "@vueuse/metadata" "10.9.0"
    "@vueuse/shared" "10.9.0"
    vue-demi ">=0.14.7"

"@vueuse/core@^9.1.0":
  version "9.13.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/core/-/core-9.13.0.tgz#2f69e66d1905c1e4eebc249a01759cf88ea00cf4"
  integrity sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/metadata@10.9.0":
  version "10.9.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/metadata/-/metadata-10.9.0.tgz#769a1a9db65daac15cf98084cbf7819ed3758620"
  integrity sha512-iddNbg3yZM0X7qFY2sAotomgdHK7YJ6sKUvQqbvwnf7TmaVPxS4EJydcNsVejNdS8iWCtDk+fYXr7E32nyTnGA==

"@vueuse/metadata@9.13.0":
  version "9.13.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/metadata/-/metadata-9.13.0.tgz#bc25a6cdad1b1a93c36ce30191124da6520539ff"
  integrity sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==

"@vueuse/shared@10.9.0":
  version "10.9.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/shared/-/shared-10.9.0.tgz#13af2a348de15d07b7be2fd0c7fc9853a69d8fe0"
  integrity sha512-Uud2IWncmAfJvRaFYzv5OHDli+FbOzxiVEQdLCKQKLyhz94PIyFC3CHcH7EDMwIn8NPtD06+PNbC/PiO0LGLtw==
  dependencies:
    vue-demi ">=0.14.7"

"@vueuse/shared@9.13.0":
  version "9.13.0"
  resolved "https://tech.suitbim.com/verdaccio/@vueuse/shared/-/shared-9.13.0.tgz#089ff4cc4e2e7a4015e57a8f32e4b39d096353b9"
  integrity sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==
  dependencies:
    vue-demi "*"

"@yarn-tool/resolve-package@^1.0.40":
  version "1.0.47"
  resolved "https://tech.suitbim.com/verdaccio/@yarn-tool/resolve-package/-/resolve-package-1.0.47.tgz#8ec25f291a316280a281632331e88926a66fdf19"
  integrity sha512-Zaw58gQxjQceJqhqybJi1oUDaORT8i2GTgwICPs8v/X/Pkx35FXQba69ldHVg5pQZ6YLKpROXgyHvBaCJOFXiA==
  dependencies:
    pkg-dir "< 6 >= 5"
    tslib "^2"
    upath2 "^3.1.13"

"@zip.js/zip.js@2.4.x":
  version "2.4.26"
  resolved "https://tech.suitbim.com/verdaccio/@zip.js/zip.js/-/zip.js-2.4.26.tgz#b79bb2055dc6e185890ee01cdb710caba505d5b2"
  integrity sha512-I9HBO3BHIxEMQmltmHM3iqUW6IHqi3gsL9wTSXvHTRpOrA6q2OxtR58EDSaOGjHhDVJ+wIOAxZyKq2x00AVmqw==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://tech.suitbim.com/verdaccio/acorn-jsx/-/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn@^8.10.0, acorn@^8.11.2, acorn@^8.11.3, acorn@^8.4.0, acorn@^8.9.0:
  version "8.11.3"
  resolved "https://tech.suitbim.com/verdaccio/acorn/-/acorn-8.11.3.tgz#71e0b14e13a4ec160724b38fb7b0f233b1b81d7a"
  integrity sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==

aggregate-error@^4.0.0:
  version "4.0.1"
  resolved "https://tech.suitbim.com/verdaccio/aggregate-error/-/aggregate-error-4.0.1.tgz#25091fe1573b9e0be892aeda15c7c66a545f758e"
  integrity sha512-0poP0T7el6Vq3rstR8Mn4V/IQrpBLO6POkUSrN7RhyY+GF/InCFShQzsQ39T25gkHhLgSLByyAz+Kjb+c2L98w==
  dependencies:
    clean-stack "^4.0.0"
    indent-string "^5.0.0"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://tech.suitbim.com/verdaccio/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.13.0"
  resolved "https://tech.suitbim.com/verdaccio/ajv/-/ajv-8.13.0.tgz#a3939eaec9fb80d217ddf0c3376948c023f28c91"
  integrity sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==
  dependencies:
    fast-deep-equal "^3.1.3"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.4.1"

animate.css@^4.1.1:
  version "4.1.1"
  resolved "https://tech.suitbim.com/verdaccio/animate.css/-/animate.css-4.1.1.tgz#614ec5a81131d7e4dc362a58143f7406abd68075"
  integrity sha512-+mRmCTv6SbCmtYJCN4faJMNFVNN5EuCTTprDTAo7YzIGji2KADmakjVA3+8mVDkZ2Bf09vayB35lSQIex2+QaQ==

ansi-escapes@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/ansi-escapes/-/ansi-escapes-5.0.0.tgz#b6a0caf0eef0c41af190e9a749e0c00ec04bb2a6"
  integrity sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==
  dependencies:
    type-fest "^1.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://tech.suitbim.com/verdaccio/ansi-regex/-/ansi-regex-6.0.1.tgz#3183e38fae9a65d7cb5e53945cd5897d0260a06a"
  integrity sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://tech.suitbim.com/verdaccio/ansi-styles/-/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://tech.suitbim.com/verdaccio/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.0.0, ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://tech.suitbim.com/verdaccio/ansi-styles/-/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://tech.suitbim.com/verdaccio/anymatch/-/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/argparse/-/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-differ@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/array-differ/-/array-differ-1.0.0.tgz#eff52e3758249d33be402b8bb8e564bb2b5d4031"
  integrity sha1-7/UuN1gknTO+QCuLuOVkuytdQDE=

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

arrify@^1.0.0, arrify@^1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://tech.suitbim.com/verdaccio/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@~0.2.0:
  version "0.2.6"
  resolved "https://tech.suitbim.com/verdaccio/asn1/-/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==
  dependencies:
    safer-buffer "~2.1.0"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/astral-regex/-/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==

async-validator@^4.2.5:
  version "4.2.5"
  resolved "https://tech.suitbim.com/verdaccio/async-validator/-/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

async@^2.6.4:
  version "2.6.4"
  resolved "https://tech.suitbim.com/verdaccio/async/-/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==
  dependencies:
    lodash "^4.17.14"

async@~0.9.0:
  version "0.9.2"
  resolved "https://tech.suitbim.com/verdaccio/async/-/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://tech.suitbim.com/verdaccio/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

autofit.js@^3.0.7:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/autofit.js/-/autofit.js-3.1.0.tgz#b19df75541cf44f6f95fd071b09a590a96d835b6"
  integrity sha512-zX/2VbpxdDcgDDCT1A0psuF8Phs8QbmFXstvLkqrrOcv33LQttCh2J6GVs84585+LfMAfacemxRXIidrFXu02g==

autolinker@^3.14.3:
  version "3.16.2"
  resolved "https://tech.suitbim.com/verdaccio/autolinker/-/autolinker-3.16.2.tgz#6bb4f32432fc111b65659336863e653973bfbcc9"
  integrity sha512-JiYl7j2Z19F9NdTmirENSUUIIL/9MytEWtmzhfmsKPCp9E+G35Y0UNCMoM9tFigxT59qSc8Ml2dlZXOCVTYwuA==
  dependencies:
    tslib "^2.3.0"

axios@^1.4.0:
  version "1.6.8"
  resolved "https://tech.suitbim.com/verdaccio/axios/-/axios-1.6.8.tgz#66d294951f5d988a00e87a0ffb955316a619ea66"
  integrity sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://tech.suitbim.com/verdaccio/babel-eslint/-/babel-eslint-10.1.0.tgz#6968e568a910b78fb3779cdd8b6ac2f479943232"
  integrity sha512-ifWaTHQ0ce+448CYop8AdrQiBsGrnC+bMgfyKFdi6EsPLTAWG+QfyDeM6OH+FmWnKvEq5NnBMLvlBUPKQZoDSg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/balanced-match/-/balanced-match-2.0.0.tgz#dc70f920d78db8b858535795867bf48f820633d9"
  integrity sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://tech.suitbim.com/verdaccio/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/binary-extensions/-/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bitmap-sdf@^1.0.3:
  version "1.0.4"
  resolved "https://tech.suitbim.com/verdaccio/bitmap-sdf/-/bitmap-sdf-1.0.4.tgz#e87b8b1d84ee846567cfbb29d60eedd34bca5b6f"
  integrity sha512-1G3U4n5JE6RAiALMxu0p1XmeZkTeCwGKykzsLTCqVzfSDaN6S7fKnkIkfejogz+iwqBWc0UYAIKnKHNN7pSfDg==

bl@^4.0.2:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/bl/-/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bl@^5.0.0:
  version "5.1.0"
  resolved "https://tech.suitbim.com/verdaccio/bl/-/bl-5.1.0.tgz#183715f678c7188ecef9fe475d90209400624273"
  integrity sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==
  dependencies:
    buffer "^6.0.3"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://tech.suitbim.com/verdaccio/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/brace-expansion/-/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://tech.suitbim.com/verdaccio/braces/-/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.22.2:
  version "4.23.0"
  resolved "https://tech.suitbim.com/verdaccio/browserslist/-/browserslist-4.23.0.tgz#8f3acc2bbe73af7213399430890f86c63a5674ab"
  integrity sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==
  dependencies:
    caniuse-lite "^1.0.30001587"
    electron-to-chromium "^1.4.668"
    node-releases "^2.0.14"
    update-browserslist-db "^1.0.13"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://tech.suitbim.com/verdaccio/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://tech.suitbim.com/verdaccio/buffer/-/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

cac@^6.7.14:
  version "6.7.14"
  resolved "https://tech.suitbim.com/verdaccio/cac/-/cac-6.7.14.tgz#804e1e6f506ee363cb0e3ccbb09cad5dd9870959"
  integrity sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==

call-bind@^1.0.2, call-bind@^1.0.6, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://tech.suitbim.com/verdaccio/call-bind/-/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-keys@^7.0.0:
  version "7.0.2"
  resolved "https://tech.suitbim.com/verdaccio/camelcase-keys/-/camelcase-keys-7.0.2.tgz#d048d8c69448745bb0de6fc4c1c52a30dfbe7252"
  integrity sha512-Rjs1H+A9R+Ig+4E/9oyB66UC5Mj9Xq3N//vcLf2WzgdTi/3gUu3Z9KoqmlrEG4VuuLK8wJHofxzdQXz/knhiYg==
  dependencies:
    camelcase "^6.3.0"
    map-obj "^4.1.0"
    quick-lru "^5.1.1"
    type-fest "^1.2.1"

camelcase@^6.3.0:
  version "6.3.0"
  resolved "https://tech.suitbim.com/verdaccio/camelcase/-/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001587:
  version "1.0.30001616"
  resolved "https://tech.suitbim.com/verdaccio/caniuse-lite/-/caniuse-lite-1.0.30001616.tgz#4342712750d35f71ebba9fcac65e2cf8870013c3"
  integrity sha512-RHVYKov7IcdNjVHJFNY/78RdG4oGVjbayxv8u5IO74Wv7Hlq4PnJE6mo/OjFijjVFNy5ijnCt6H3IIo4t+wfEw==

chalk@4.1.1:
  version "4.1.1"
  resolved "https://tech.suitbim.com/verdaccio/chalk/-/chalk-4.1.1.tgz#c80b3fab28bf6371e6863325eee67e618b77e6ad"
  integrity sha512-diHzdDKxcU+bAsUboHLPEDQiw0qEe0qd7SYUn3HgcFlWgbDcfLGswOHYeGrHKzG9z6UYf01d9VFMfZxPM1xZSg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@5.3.0, chalk@^5.0.0, chalk@^5.3.0:
  version "5.3.0"
  resolved "https://tech.suitbim.com/verdaccio/chalk/-/chalk-5.3.0.tgz#67c20a7ebef70e7f3970a01f90fa210cb6860385"
  integrity sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==

chalk@^2.4.2:
  version "2.4.2"
  resolved "https://tech.suitbim.com/verdaccio/chalk/-/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.1:
  version "4.1.2"
  resolved "https://tech.suitbim.com/verdaccio/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.5.3, chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://tech.suitbim.com/verdaccio/chokidar/-/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

clean-stack@^4.0.0:
  version "4.2.0"
  resolved "https://tech.suitbim.com/verdaccio/clean-stack/-/clean-stack-4.2.0.tgz#c464e4cde4ac789f4e0735c5d75beb49d7b30b31"
  integrity sha512-LYv6XPxoyODi36Dp976riBtSY27VmFo+MKqEU9QCCWyTrdEPDog+RWA7xQWHi6Vbp61j5c4cdzzX1NidnwtUWg==
  dependencies:
    escape-string-regexp "5.0.0"

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/cli-cursor/-/cli-cursor-4.0.0.tgz#3cecfe3734bf4fe02a8361cbdc0f6fe28c6a57ea"
  integrity sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==
  dependencies:
    restore-cursor "^4.0.0"

cli-spinners@^2.9.0:
  version "2.9.2"
  resolved "https://tech.suitbim.com/verdaccio/cli-spinners/-/cli-spinners-2.9.2.tgz#1773a8f4b9c4d6ac31563df53b3fc1d79462fe41"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

cli-truncate@^3.1.0:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/cli-truncate/-/cli-truncate-3.1.0.tgz#3f23ab12535e3d73e839bb43e73c9de487db1389"
  integrity sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

code-inspector-core@0.9.3:
  version "0.9.3"
  resolved "https://tech.suitbim.com/verdaccio/code-inspector-core/-/code-inspector-core-0.9.3.tgz#481efa387c94f4dc6b49c132add41d1c35f2edab"
  integrity sha512-xu6G1jf5x59+JGtgzqm0KcTzizHhbe/WZ6OCx8NV7zra2L9S5Oq2dqNizI0JX/OxtxdhYRL/FuWdGhEm+zXWhw==
  dependencies:
    "@vue/compiler-dom" "^3.2.47"
    chalk "^4.1.1"
    portfinder "^1.0.28"

code-inspector-plugin@^0.9.2:
  version "0.9.3"
  resolved "https://tech.suitbim.com/verdaccio/code-inspector-plugin/-/code-inspector-plugin-0.9.3.tgz#7463548e59459a355468fdf1c6f5c604bcff11cb"
  integrity sha512-Fs7RUz0gdCKQ6DwIyLu7AA6UllsaeMKnKhjjX3TnzZSMWXraCVgKVlQmINkPAh7Mfj6xW3luXSd734EkZSib+A==
  dependencies:
    chalk "4.1.1"
    code-inspector-core "0.9.3"
    vite-code-inspector-plugin "0.9.3"
    webpack-code-inspector-plugin "0.9.3"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://tech.suitbim.com/verdaccio/color-convert/-/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://tech.suitbim.com/verdaccio/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://tech.suitbim.com/verdaccio/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.9.0:
  version "1.9.1"
  resolved "https://tech.suitbim.com/verdaccio/color-string/-/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.2.0:
  version "4.2.3"
  resolved "https://tech.suitbim.com/verdaccio/color/-/color-4.2.3.tgz#d781ecb5e57224ee43ea9627560107c0e0c6463a"
  integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

colord@^2.9.3:
  version "2.9.3"
  resolved "https://tech.suitbim.com/verdaccio/colord/-/colord-2.9.3.tgz#4f8ce919de456f1d5c1c368c307fe20f3e59fb43"
  integrity sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==

colorette@^2.0.20:
  version "2.0.20"
  resolved "https://tech.suitbim.com/verdaccio/colorette/-/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://tech.suitbim.com/verdaccio/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@11.0.0:
  version "11.0.0"
  resolved "https://tech.suitbim.com/verdaccio/commander/-/commander-11.0.0.tgz#43e19c25dbedc8256203538e8d7e9346877a6f67"
  integrity sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==

commander@2:
  version "2.20.3"
  resolved "https://tech.suitbim.com/verdaccio/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commist@^1.0.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/commist/-/commist-1.1.0.tgz#17811ec6978f6c15ee4de80c45c9beb77cee35d5"
  integrity sha512-rraC8NXWOEjhADbZe9QBNzLAN5Q3fsTPQtBV+fEVj6xKIgDgNiEVE6ZNfHpZOqfQ21YUzfVNUXLOEZquYvQPPg==
  dependencies:
    leven "^2.1.0"
    minimist "^1.1.0"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://tech.suitbim.com/verdaccio/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/concat-stream/-/concat-stream-2.0.0.tgz#414cf5af790a48c60ab9be4527d56d5e41133cb1"
  integrity sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.0.2"
    typedarray "^0.0.6"

concaveman@*:
  version "1.2.1"
  resolved "https://tech.suitbim.com/verdaccio/concaveman/-/concaveman-1.2.1.tgz#47d20b4521125c15fabf453653c2696d9ee41e0b"
  integrity sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==
  dependencies:
    point-in-polygon "^1.1.0"
    rbush "^3.0.1"
    robust-predicates "^2.0.4"
    tinyqueue "^2.0.3"

confbox@^0.1.7:
  version "0.1.7"
  resolved "https://tech.suitbim.com/verdaccio/confbox/-/confbox-0.1.7.tgz#ccfc0a2bcae36a84838e83a3b7f770fb17d6c579"
  integrity sha512-uJcB/FKZtBMCJpK8MQji6bJHgu1tixKPxRLeGkNzBoOZzpnZUJm0jm2/sBDWcuBx1dYgxV4JU+g5hmNxCyAmdA==

consola@^3.2.3:
  version "3.2.3"
  resolved "https://tech.suitbim.com/verdaccio/consola/-/consola-3.2.3.tgz#0741857aa88cfa0d6fd53f1cff0375136e98502f"
  integrity sha512-I5qxpzLv+sJhTVEoLYNcTW+bThDCPsit0vLNKShZx6rLtpilNpmmeTPaeqJb9ZE9dV3DGaeby6Vuhrw38WjeyQ==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/convert-source-map/-/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/core-util-is/-/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^8.2.0:
  version "8.3.6"
  resolved "https://tech.suitbim.com/verdaccio/cosmiconfig/-/cosmiconfig-8.3.6.tgz#060a2b871d66dba6c8538ea1118ba1ac16f5fae3"
  integrity sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://tech.suitbim.com/verdaccio/cross-spawn/-/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-functions-list@^3.2.1:
  version "3.2.2"
  resolved "https://tech.suitbim.com/verdaccio/css-functions-list/-/css-functions-list-3.2.2.tgz#9a54c6dd8416ed25c1079cd88234e927526c1922"
  integrity sha512-c+N0v6wbKVxTu5gOBBFkr9BEdBWaqqjQeiJ8QvSRIJOf+UxlJh930m8e6/WNeODIK0mYLFkoONrnj16i2EcvfQ==

css-tree@^2.3.1:
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/css-tree/-/css-tree-2.3.1.tgz#10264ce1e5442e8572fc82fbe490644ff54b5c20"
  integrity sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==
  dependencies:
    mdn-data "2.0.30"
    source-map-js "^1.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://tech.suitbim.com/verdaccio/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

d3-array@1:
  version "1.2.4"
  resolved "https://tech.suitbim.com/verdaccio/d3-array/-/d3-array-1.2.4.tgz#635ce4d5eea759f6f605863dbcfc30edc737f71f"
  integrity sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==

d3-geo@1.7.1:
  version "1.7.1"
  resolved "https://tech.suitbim.com/verdaccio/d3-geo/-/d3-geo-1.7.1.tgz#44bbc7a218b1fd859f3d8fd7c443ca836569ce99"
  integrity sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==
  dependencies:
    d3-array "1"

d3-voronoi@1.1.2:
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/d3-voronoi/-/d3-voronoi-1.1.2.tgz#1687667e8f13a2d158c80c1480c5a29cb0d8973c"
  integrity sha1-Fodmfo8TotFYyAwUgMWinLDYlzw=

dayjs@^1.11.3:
  version "1.11.11"
  resolved "https://tech.suitbim.com/verdaccio/dayjs/-/dayjs-1.11.11.tgz#dfe0e9d54c5f8b68ccf8ca5f72ac603e7e5ed59e"
  integrity sha512-okzr3f11N6WuqYtZSvm+F776mB41wRZMhKP+hc34YdW+KmtYYK9iqvHSwo2k9FEH3fhGXvOPV6yz2IcSrfRUDg==

debug@4.3.4, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "https://tech.suitbim.com/verdaccio/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://tech.suitbim.com/verdaccio/debug/-/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

decamelize-keys@^1.1.0:
  version "1.1.1"
  resolved "https://tech.suitbim.com/verdaccio/decamelize-keys/-/decamelize-keys-1.1.1.tgz#04a2d523b2f18d80d0158a43b895d56dff8d19d8"
  integrity sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==
  dependencies:
    decamelize "^1.1.0"
    map-obj "^1.0.0"

decamelize@^1.1.0:
  version "1.2.0"
  resolved "https://tech.suitbim.com/verdaccio/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decamelize@^5.0.0:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/decamelize/-/decamelize-5.0.1.tgz#db11a92e58c741ef339fb0a2868d8a06a9a7b1e9"
  integrity sha512-VfxadyCECXgQlkoEAjeghAr5gY3Hf+IKjKb+X8tGVDtveCjN+USwprd2q3QXBR9T1+x2DG0XZF5/w+7HAtSaXA==

deep-equal@1.x, deep-equal@^1.0.0:
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/deep-equal/-/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
  integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://tech.suitbim.com/verdaccio/deep-is/-/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://tech.suitbim.com/verdaccio/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://tech.suitbim.com/verdaccio/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defu@^6.1.4:
  version "6.1.4"
  resolved "https://tech.suitbim.com/verdaccio/defu/-/defu-6.1.4.tgz#4e0c9cf9ff68fe5f3d7f2765cc1a012dfdcb0479"
  integrity sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==

del@^7.0.0:
  version "7.1.0"
  resolved "https://tech.suitbim.com/verdaccio/del/-/del-7.1.0.tgz#0de0044d556b649ff05387f1fa7c885e155fd1b6"
  integrity sha512-v2KyNk7efxhlyHpjEvfyxaAihKKK0nWCuf6ZtqZcFFpQRG0bJ12Qsr0RpvsICMjAAZ8DOVCxrlqpxISlMHC4Kg==
  dependencies:
    globby "^13.1.2"
    graceful-fs "^4.2.10"
    is-glob "^4.0.3"
    is-path-cwd "^3.0.0"
    is-path-inside "^4.0.0"
    p-map "^5.5.0"
    rimraf "^3.0.2"
    slash "^4.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

density-clustering@1.3.0:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/density-clustering/-/density-clustering-1.3.0.tgz#dc9f59c8f0ab97e1624ac64930fd3194817dcac5"
  integrity sha1-3J9ZyPCrl+FiSsZJMP0xlIF9ysU=

destr@^2.0.3:
  version "2.0.3"
  resolved "https://tech.suitbim.com/verdaccio/destr/-/destr-2.0.3.tgz#7f9e97cb3d16dbdca7be52aca1644ce402cfe449"
  integrity sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/dom-serializer/-/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/domelementtype/-/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "https://tech.suitbim.com/verdaccio/domhandler/-/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
  dependencies:
    domelementtype "^2.3.0"

dompurify@^2.2.2:
  version "2.5.2"
  resolved "https://tech.suitbim.com/verdaccio/dompurify/-/dompurify-2.5.2.tgz#e02be61d621bea36a76eb2beb23b043f347aa9c7"
  integrity sha512-5vSyvxRAb45EoWwAktUT3AYqAwXK4FL7si22Cgj46U6ICsj/YJczCN+Bk7WNABIQmpWRymGfslMhrRUZkQNnqA==

domutils@^3.0.1:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/domutils/-/domutils-3.1.0.tgz#c47f551278d3dc4b0b1ab8cbb42d751a6f0d824e"
  integrity sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://tech.suitbim.com/verdaccio/duplexer/-/duplexer-0.1.2.tgz#3abe43aef3835f8ae077d136ddce0f276b0400e6"
  integrity sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==

duplexify@^4.1.1:
  version "4.1.3"
  resolved "https://tech.suitbim.com/verdaccio/duplexify/-/duplexify-4.1.3.tgz#a07e1c0d0a2c001158563d32592ba58bddb0236f"
  integrity sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==
  dependencies:
    end-of-stream "^1.4.1"
    inherits "^2.0.3"
    readable-stream "^3.1.1"
    stream-shift "^1.0.2"

earcut@^2.0.0, earcut@^2.2.4:
  version "2.2.4"
  resolved "https://tech.suitbim.com/verdaccio/earcut/-/earcut-2.2.4.tgz#6d02fd4d68160c114825d06890a92ecaae60343a"
  integrity sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://tech.suitbim.com/verdaccio/eastasianwidth/-/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

echarts@^5.5.0:
  version "5.5.0"
  resolved "https://tech.suitbim.com/verdaccio/echarts/-/echarts-5.5.0.tgz#c13945a7f3acdd67c134d8a9ac67e917830113ac"
  integrity sha512-rNYnNCzqDAPCr4m/fqyUFv7fD9qIsd50S6GDFgO1DxZhncCsNsG7IfUlAlvZe5oSEQxtsjnHiUuppzccry93Xw==
  dependencies:
    tslib "2.3.0"
    zrender "5.5.0"

electron-to-chromium@^1.4.668:
  version "1.4.757"
  resolved "https://tech.suitbim.com/verdaccio/electron-to-chromium/-/electron-to-chromium-1.4.757.tgz#45f7c9341b538f8c4b9ca8af9692e0ed1a776a44"
  integrity sha512-jftDaCknYSSt/+KKeXzH3LX5E2CvRLm75P3Hj+J/dv3CL0qUYcOt13d5FN1NiL5IJbbhzHrb3BomeG2tkSlZmw==

element-plus@^2.3.7:
  version "2.7.2"
  resolved "https://tech.suitbim.com/verdaccio/element-plus/-/element-plus-2.7.2.tgz#a5361ab0de49ea1e3c91955e4da3916a358b3bcf"
  integrity sha512-AdEzBU/A68iUleio0MkQ46JeU5SeQvFFd915GJFScJmUEo5AmYg3OQ4pVjcu+p3b3Nupg9MC5Wa4xjAiC51kUg==
  dependencies:
    "@ctrl/tinycolor" "^3.4.1"
    "@element-plus/icons-vue" "^2.3.1"
    "@floating-ui/dom" "^1.0.1"
    "@popperjs/core" "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash" "^4.14.182"
    "@types/lodash-es" "^4.17.6"
    "@vueuse/core" "^9.1.0"
    async-validator "^4.2.5"
    dayjs "^1.11.3"
    escape-html "^1.0.3"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    lodash-unified "^1.0.2"
    memoize-one "^6.0.0"
    normalize-wheel-es "^1.2.0"

emoji-regex@^10.2.1:
  version "10.3.0"
  resolved "https://tech.suitbim.com/verdaccio/emoji-regex/-/emoji-regex-10.3.0.tgz#76998b9268409eb3dae3de989254d456e70cfe23"
  integrity sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://tech.suitbim.com/verdaccio/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://tech.suitbim.com/verdaccio/emoji-regex/-/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://tech.suitbim.com/verdaccio/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

entities@^4.2.0, entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"
  resolved "https://tech.suitbim.com/verdaccio/entities/-/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

errno@^0.1.2:
  version "0.1.8"
  resolved "https://tech.suitbim.com/verdaccio/errno/-/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://tech.suitbim.com/verdaccio/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/es-define-property/-/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-module-lexer@^0.4.1:
  version "0.4.1"
  resolved "https://tech.suitbim.com/verdaccio/es-module-lexer/-/es-module-lexer-0.4.1.tgz#dda8c6a14d8f340a24e34331e0fab0cb50438e0e"
  integrity sha512-ooYciCUtfw6/d2w56UVeqHPcoCFAiJdz5XOkYpv/Txl1HMUozpXjz/2RIQgqwKdXNDPSF1W7mJCFse3G+HDyAA==

esbuild@^0.18.10:
  version "0.18.20"
  resolved "https://tech.suitbim.com/verdaccio/esbuild/-/esbuild-0.18.20.tgz#4709f5a34801b43b799ab7d6d82f7284a9b7a7a6"
  integrity sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==
  optionalDependencies:
    "@esbuild/android-arm" "0.18.20"
    "@esbuild/android-arm64" "0.18.20"
    "@esbuild/android-x64" "0.18.20"
    "@esbuild/darwin-arm64" "0.18.20"
    "@esbuild/darwin-x64" "0.18.20"
    "@esbuild/freebsd-arm64" "0.18.20"
    "@esbuild/freebsd-x64" "0.18.20"
    "@esbuild/linux-arm" "0.18.20"
    "@esbuild/linux-arm64" "0.18.20"
    "@esbuild/linux-ia32" "0.18.20"
    "@esbuild/linux-loong64" "0.18.20"
    "@esbuild/linux-mips64el" "0.18.20"
    "@esbuild/linux-ppc64" "0.18.20"
    "@esbuild/linux-riscv64" "0.18.20"
    "@esbuild/linux-s390x" "0.18.20"
    "@esbuild/linux-x64" "0.18.20"
    "@esbuild/netbsd-x64" "0.18.20"
    "@esbuild/openbsd-x64" "0.18.20"
    "@esbuild/sunos-x64" "0.18.20"
    "@esbuild/win32-arm64" "0.18.20"
    "@esbuild/win32-ia32" "0.18.20"
    "@esbuild/win32-x64" "0.18.20"

escalade@^3.1.2:
  version "3.1.2"
  resolved "https://tech.suitbim.com/verdaccio/escalade/-/escalade-3.1.2.tgz#54076e9ab29ea5bf3d8f1ed62acffbb88272df27"
  integrity sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@5.0.0, escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz#4683126b500b61762f2dbebace1806e8be31b1c8"
  integrity sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://tech.suitbim.com/verdaccio/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-plugin-simple-import-sort@^10.0.0:
  version "10.0.0"
  resolved "https://tech.suitbim.com/verdaccio/eslint-plugin-simple-import-sort/-/eslint-plugin-simple-import-sort-10.0.0.tgz#cc4ceaa81ba73252427062705b64321946f61351"
  integrity sha512-AeTvO9UCMSNzIHRkg8S6c3RPy5YEwKWSQPx3DYghLedo2ZQxowPFLGDN1AZ2evfg6r6mjBSZSLxLFsWSu3acsw==

eslint-plugin-vue@^9.17.0:
  version "9.25.0"
  resolved "https://tech.suitbim.com/verdaccio/eslint-plugin-vue/-/eslint-plugin-vue-9.25.0.tgz#615cb7bb6d0e2140d21840b9aa51dce69e803e7a"
  integrity sha512-tDWlx14bVe6Bs+Nnh3IGrD+hb11kf2nukfm6jLsmJIhmiRQ1SUaksvwY9U5MvPB0pcrg0QK0xapQkfITs3RKOA==
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    globals "^13.24.0"
    natural-compare "^1.4.0"
    nth-check "^2.1.1"
    postcss-selector-parser "^6.0.15"
    semver "^7.6.0"
    vue-eslint-parser "^9.4.2"
    xml-name-validator "^4.0.0"

eslint-scope@^7.1.1, eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://tech.suitbim.com/verdaccio/eslint-scope/-/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^1.0.0:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://tech.suitbim.com/verdaccio/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@^8.42.0:
  version "8.57.0"
  resolved "https://tech.suitbim.com/verdaccio/eslint/-/eslint-8.57.0.tgz#c786a6fd0e0b68941aaf624596fb987089195668"
  integrity sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.0"
    "@humanwhocodes/config-array" "^0.11.14"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.3.1, espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://tech.suitbim.com/verdaccio/espree/-/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.0, esquery@^1.4.2:
  version "1.5.0"
  resolved "https://tech.suitbim.com/verdaccio/esquery/-/esquery-1.5.0.tgz#6ce17738de8577694edd7361c57182ac8cb0db0b"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://tech.suitbim.com/verdaccio/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://tech.suitbim.com/verdaccio/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.1, estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/estree-walker/-/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

estree-walker@^3.0.3:
  version "3.0.3"
  resolved "https://tech.suitbim.com/verdaccio/estree-walker/-/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://tech.suitbim.com/verdaccio/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/eventemitter3/-/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

execa@7.2.0:
  version "7.2.0"
  resolved "https://tech.suitbim.com/verdaccio/execa/-/execa-7.2.0.tgz#657e75ba984f42a70f38928cedc87d6f2d4fe4e9"
  integrity sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

execa@^5.1.1:
  version "5.1.1"
  resolved "https://tech.suitbim.com/verdaccio/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://tech.suitbim.com/verdaccio/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.9, fast-glob@^3.3.0, fast-glob@^3.3.1, fast-glob@^3.3.2:
  version "3.3.2"
  resolved "https://tech.suitbim.com/verdaccio/fast-glob/-/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://tech.suitbim.com/verdaccio/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "https://tech.suitbim.com/verdaccio/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://tech.suitbim.com/verdaccio/fastq/-/fastq-1.17.1.tgz#2a523f07a4e7b1e81a42b91b8bf2254107753b47"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://tech.suitbim.com/verdaccio/file-entry-cache/-/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-entry-cache@^7.0.0:
  version "7.0.2"
  resolved "https://tech.suitbim.com/verdaccio/file-entry-cache/-/file-entry-cache-7.0.2.tgz#2d61bb70ba89b9548e3035b7c9173fe91deafff0"
  integrity sha512-TfW7/1iI4Cy7Y8L6iqNdZQVvdXn0f8B4QcIXmkIbtTIe/Okm/nSlHb4IwGzRVOd3WfSieCgvf5cMzEfySAIl0g==
  dependencies:
    flat-cache "^3.2.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://tech.suitbim.com/verdaccio/fill-range/-/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

find-cache-dir@^3.3.2:
  version "3.3.2"
  resolved "https://tech.suitbim.com/verdaccio/find-cache-dir/-/find-cache-dir-3.3.2.tgz#b30c5b6eff0730731aea9bbd9dbecbd80256d64b"
  integrity sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4, flat-cache@^3.2.0:
  version "3.2.0"
  resolved "https://tech.suitbim.com/verdaccio/flat-cache/-/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.1"
  resolved "https://tech.suitbim.com/verdaccio/flatted/-/flatted-3.3.1.tgz#21db470729a6734d4997002f439cb308987f567a"
  integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==

follow-redirects@^1.15.6:
  version "1.15.6"
  resolved "https://tech.suitbim.com/verdaccio/follow-redirects/-/follow-redirects-1.15.6.tgz#7f815c0cda4249c74ff09e95ef97c23b5fd0399b"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://tech.suitbim.com/verdaccio/fs-extra/-/fs-extra-10.1.0.tgz#02873cfbc4084dde127eaa5f9905eef2325d1abf"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://tech.suitbim.com/verdaccio/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://tech.suitbim.com/verdaccio/functions-have-names/-/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://tech.suitbim.com/verdaccio/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

geojson-equality@0.1.6:
  version "0.1.6"
  resolved "https://tech.suitbim.com/verdaccio/geojson-equality/-/geojson-equality-0.1.6.tgz#a171374ef043e5d4797995840bae4648e0752d72"
  integrity sha1-oXE3TvBD5dR5eZWEC65GSOB1LXI=
  dependencies:
    deep-equal "^1.0.0"

geojson-rbush@3.x:
  version "3.2.0"
  resolved "https://tech.suitbim.com/verdaccio/geojson-rbush/-/geojson-rbush-3.2.0.tgz#8b543cf0d56f99b78faf1da52bb66acad6dfc290"
  integrity sha512-oVltQTXolxvsz1sZnutlSuLDEcQAKYC/uXt9zDzJJ6bu0W+baTI8LZBaTup5afzibEH4N3jlq2p+a152wlBJ7w==
  dependencies:
    "@turf/bbox" "*"
    "@turf/helpers" "6.x"
    "@turf/meta" "6.x"
    "@types/geojson" "7946.0.8"
    rbush "^3.0.1"

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://tech.suitbim.com/verdaccio/get-intrinsic/-/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://tech.suitbim.com/verdaccio/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://tech.suitbim.com/verdaccio/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://tech.suitbim.com/verdaccio/glob-parent/-/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3, glob@^7.1.6:
  version "7.2.3"
  resolved "https://tech.suitbim.com/verdaccio/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.0.3:
  version "7.0.6"
  resolved "https://tech.suitbim.com/verdaccio/glob/-/glob-7.0.6.tgz#211bafaf49e525b8cd93260d14ab136152b3f57a"
  integrity sha1-IRuvr0nlJbjNkyYNFKsTYVKz9Xo=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/global-modules/-/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha512-NGbfmJBp9x8IxyJSd1P+otYK8vonoJactOogrVfFRIAEY1ukil8RSKDz2Yo7wh1oihl51l/r6W4epkeKJHqL8A==
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/global-prefix/-/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://tech.suitbim.com/verdaccio/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0, globals@^13.24.0:
  version "13.24.0"
  resolved "https://tech.suitbim.com/verdaccio/globals/-/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://tech.suitbim.com/verdaccio/globby/-/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globby@^13.1.2:
  version "13.2.2"
  resolved "https://tech.suitbim.com/verdaccio/globby/-/globby-13.2.2.tgz#63b90b1bf68619c2135475cbd4e71e66aa090592"
  integrity sha512-Y1zNGV+pzQdh7H39l9zgB4PJqjRNqydvdYCDG4HFXM4XuvSaQQlEc91IU1yALL8gUTDomgBAfz3XJdmUS+oo0w==
  dependencies:
    dir-glob "^3.0.1"
    fast-glob "^3.3.0"
    ignore "^5.2.4"
    merge2 "^1.4.1"
    slash "^4.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "https://tech.suitbim.com/verdaccio/globjoin/-/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/gopd/-/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.4, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.10:
  version "4.2.11"
  resolved "https://tech.suitbim.com/verdaccio/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

grapheme-splitter@^1.0.4:
  version "1.0.4"
  resolved "https://tech.suitbim.com/verdaccio/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz#9cf3a665c6247479896834af35cf1dbb4400767e"
  integrity sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://tech.suitbim.com/verdaccio/graphemer/-/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/gzip-size/-/gzip-size-6.0.0.tgz#065367fd50c239c0671cbcbad5be3e2eeb10e462"
  integrity sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==
  dependencies:
    duplexer "^0.1.2"

hard-rejection@^2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/hard-rejection/-/hard-rejection-2.1.0.tgz#1c6eda5c1685c63942766d79bb40ae773cecd883"
  integrity sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/has-flag/-/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/has-proto/-/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.0:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

help-me@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/help-me/-/help-me-3.0.0.tgz#9803c81b5f346ad2bce2c6a0ba01b82257d319e8"
  integrity sha512-hx73jClhyk910sidBB7ERlnhMlFsJJIBqSVMFDwPN8o2v9nmp5KgLq1Xz1Bf1fCMMZ6mPrX159iG0VLy/fPMtQ==
  dependencies:
    glob "^7.1.6"
    readable-stream "^3.6.0"

hls.js@^1.0.10:
  version "1.5.8"
  resolved "https://tech.suitbim.com/verdaccio/hls.js/-/hls.js-1.5.8.tgz#73c3d2984a56f103e6900bf7a5e65b288dba846e"
  integrity sha512-hJYMPfLhWO7/7+n4f9pn6bOheCGx0WgvVz7k3ouq3Pp1bja48NN+HeCQu3XCGYzqWQF/wo7Sk6dJAyWVJD8ECA==

hosted-git-info@^4.0.1:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/hosted-git-info/-/hosted-git-info-4.1.0.tgz#827b82867e9ff1c8d0c4d9d53880397d2c86d224"
  integrity sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==
  dependencies:
    lru-cache "^6.0.0"

html-tags@^3.3.1:
  version "3.3.1"
  resolved "https://tech.suitbim.com/verdaccio/html-tags/-/html-tags-3.3.1.tgz#a04026a18c882e4bba8a01a3d39cfe465d40b5ce"
  integrity sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==

htmlparser2@^8.0.0:
  version "8.0.2"
  resolved "https://tech.suitbim.com/verdaccio/htmlparser2/-/htmlparser2-8.0.2.tgz#f002151705b383e62433b5cf466f5b716edaec21"
  integrity sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

human-signals@^4.3.0:
  version "4.3.1"
  resolved "https://tech.suitbim.com/verdaccio/human-signals/-/human-signals-4.3.1.tgz#ab7f811e851fca97ffbd2c1fe9a958964de321b2"
  integrity sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==

husky@^8.0.3:
  version "8.0.3"
  resolved "https://tech.suitbim.com/verdaccio/husky/-/husky-8.0.3.tgz#4936d7212e46d1dea28fef29bb3a108872cd9184"
  integrity sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://tech.suitbim.com/verdaccio/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^5.2.0, ignore@^5.2.4:
  version "5.3.1"
  resolved "https://tech.suitbim.com/verdaccio/ignore/-/ignore-5.3.1.tgz#5073e554cd42c5b33b394375f538b8593e34d4ef"
  integrity sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==

immutable@^4.0.0:
  version "4.3.5"
  resolved "https://tech.suitbim.com/verdaccio/immutable/-/immutable-4.3.5.tgz#f8b436e66d59f99760dc577f5c99a4fd2a5cc5a0"
  integrity sha512-8eabxkth9gZatlwl5TBuJnCsoTADlL6ftEr7A4qgdaTsPyreilDSnUk57SO+jfKcNtxPa22U5KK6DSeAYhpBJw==

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "https://tech.suitbim.com/verdaccio/import-fresh/-/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/import-lazy/-/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://tech.suitbim.com/verdaccio/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/indent-string/-/indent-string-5.0.0.tgz#4fd2980fccaf8622d14c64d694f4cf33c81951a5"
  integrity sha512-m6FAo/spmsW2Ab2fU35JTYwtOKa2yAwXSwgjSv1TJzh4Mh7mC3lzAOVLBprb72XsTrgkEIsl7YrFNAiDiRhIGg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://tech.suitbim.com/verdaccio/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1:
  version "2.0.4"
  resolved "https://tech.suitbim.com/verdaccio/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ini@^1.3.5:
  version "1.3.8"
  resolved "https://tech.suitbim.com/verdaccio/ini/-/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==

is-arguments@^1.1.1:
  version "1.1.1"
  resolved "https://tech.suitbim.com/verdaccio/is-arguments/-/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://tech.suitbim.com/verdaccio/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://tech.suitbim.com/verdaccio/is-arrayish/-/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.13.0, is-core-module@^2.5.0:
  version "2.13.1"
  resolved "https://tech.suitbim.com/verdaccio/is-core-module/-/is-core-module-2.13.1.tgz#ad0d7532c6fea9da1ebdc82742d74525c6273384"
  integrity sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==
  dependencies:
    hasown "^2.0.0"

is-date-object@^1.0.5:
  version "1.0.5"
  resolved "https://tech.suitbim.com/verdaccio/is-date-object/-/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://tech.suitbim.com/verdaccio/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz#fae3167c729e7463f8461ce512b080a49268aa88"
  integrity sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://tech.suitbim.com/verdaccio/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-interactive/-/is-interactive-2.0.0.tgz#40c57614593826da1100ade6059778d597f16e90"
  integrity sha512-qP1vozQRI+BMOPcjFzrjXuQvdak2pHNUMZoeG2eRbiSqyvbEf/wQtEOTOX1guk6E3t36RkaqiSt8A/6YElNxLQ==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-path-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-path-cwd/-/is-path-cwd-3.0.0.tgz#889b41e55c8588b1eb2a96a61d05740a674521c7"
  integrity sha512-kyiNFFLU0Ampr6SDZitD/DwUo4Zs1nSdnygUBqsu3LooL00Qvb5j+UnvApUn/TTj1J3OuE6BTdQ5rudKmU2ZaA==

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://tech.suitbim.com/verdaccio/is-path-inside/-/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-path-inside@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-path-inside/-/is-path-inside-4.0.0.tgz#805aeb62c47c1b12fc3fd13bfb3ed1e7430071db"
  integrity sha512-lJJV/5dYS+RcL8uQdBDW9c9uWFLLBNRyFhnAKXw5tVqLlKZ4RMGZKv+YQ/IA3OhD+RpbJa1LLFM1FQPGyIXvOA==

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/is-plain-obj/-/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-plain-object/-/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://tech.suitbim.com/verdaccio/is-regex/-/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/is-stream/-/is-stream-3.0.0.tgz#e6bfd7aa6bef69f4f472ce9bb681e3e57b4319ac"
  integrity sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==

is-unicode-supported@^1.1.0, is-unicode-supported@^1.3.0:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/is-unicode-supported/-/is-unicode-supported-1.3.0.tgz#d824984b616c292a2e198207d4a609983842f714"
  integrity sha512-43r2mRvz+8JRIKnWJ+3j8JtjRKZ6GmjzfaE/qiBJnikNnYv/6bagRJ1kUhNk8R5EX/GkobD+r+sfxCPJsiKBLQ==

isarray@0.0.1:
  version "0.0.1"
  resolved "https://tech.suitbim.com/verdaccio/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

iscroll@^5.2.0:
  version "5.2.0"
  resolved "https://tech.suitbim.com/verdaccio/iscroll/-/iscroll-5.2.0.tgz#d513307088b5b25a4f893af474804468481829c8"
  integrity sha512-HyEu3cR8ulc0JONuQq6yht7s4iYgIPpYvy0h8IJflOUMDB95pwZ7e5FQtA6qfCESxhfxv7JZb9kIQe0JFPL05g==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

jiti@^1.21.0:
  version "1.21.0"
  resolved "https://tech.suitbim.com/verdaccio/jiti/-/jiti-1.21.0.tgz#7c97f8fe045724e136a397f7340475244156105d"
  integrity sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==

js-sdsl@4.3.0:
  version "4.3.0"
  resolved "https://tech.suitbim.com/verdaccio/js-sdsl/-/js-sdsl-4.3.0.tgz#aeefe32a451f7af88425b11fdb5f58c90ae1d711"
  integrity sha512-mifzlm2+5nZ+lEcLJMoBK0/IH/bDg8XnJfd/Wq6IP+xoCjLZsTOnV2QpxlVbX9bMnkl5PdEjNtBJ9Cj1NjifhQ==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-tokens@^8.0.0:
  version "8.0.3"
  resolved "https://tech.suitbim.com/verdaccio/js-tokens/-/js-tokens-8.0.3.tgz#1c407ec905643603b38b6be6977300406ec48775"
  integrity sha512-UfJMcSJc+SEXEl9lH/VLHSZbThQyLpw1vLO1Lb+j4RWDvG3N2f7yj3PVQA3cmkTBNldJ9eFnM+xEXxHIXrYiJw==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/js-yaml/-/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsep@^0.3.1:
  version "0.3.5"
  resolved "https://tech.suitbim.com/verdaccio/jsep/-/jsep-0.3.5.tgz#3fd79ebd92f6f434e4857d5272aaeef7d948264d"
  integrity sha512-AoRLBDc6JNnKjNcmonituEABS5bcfqDhQAWWXNTFrqu6nVXBpBAGfcoTGZMFlIrh9FjmE1CQyX9CTNwZrXMMDA==

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://tech.suitbim.com/verdaccio/jsesc/-/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/json-buffer/-/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://tech.suitbim.com/verdaccio/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^2.2.3:
  version "2.2.3"
  resolved "https://tech.suitbim.com/verdaccio/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://tech.suitbim.com/verdaccio/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

junk@^1.0.1:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/junk/-/junk-1.0.3.tgz#87be63488649cbdca6f53ab39bec9ccd2347f592"
  integrity sha1-h75jSIZJy9ym9Tqzm+yczSNH9ZI=

kdbush@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/kdbush/-/kdbush-3.0.0.tgz#f8484794d47004cc2d85ed3a79353dbe0abc2bf0"
  integrity sha512-hRkd6/XW4HTsA9vjVpY9tuXJYLSlelnkTmVFu4M9/7MIYQtFcHpbugAU7UbOfjOiVSVYl2fqgBuJ32JUmRo5Ew==

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://tech.suitbim.com/verdaccio/keyv/-/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.2, kind-of@^6.0.3:
  version "6.0.3"
  resolved "https://tech.suitbim.com/verdaccio/kind-of/-/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

known-css-properties@^0.29.0:
  version "0.29.0"
  resolved "https://tech.suitbim.com/verdaccio/known-css-properties/-/known-css-properties-0.29.0.tgz#e8ba024fb03886f23cb882e806929f32d814158f"
  integrity sha512-Ne7wqW7/9Cz54PDt4I3tcV+hAyat8ypyOGzYRJQfdxnnjeWsTxt1cy8pjvvKeI5kfXuyvULyeeAvwvvtAX3ayQ==

kolorist@^1.8.0:
  version "1.8.0"
  resolved "https://tech.suitbim.com/verdaccio/kolorist/-/kolorist-1.8.0.tgz#edddbbbc7894bc13302cdf740af6374d4a04743c"
  integrity sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==

ktx-parse@^0.4.5:
  version "0.4.5"
  resolved "https://tech.suitbim.com/verdaccio/ktx-parse/-/ktx-parse-0.4.5.tgz#79905e22281a9d3e602b2ff522df1ee7d1813aa6"
  integrity sha512-MK3FOody4TXbFf8Yqv7EBbySw7aPvEcPX++Ipt6Sox+/YMFvR5xaTyhfNSk1AEmMy+RYIw81ctN4IMxCB8OAlg==

lerc@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/lerc/-/lerc-2.0.0.tgz#82feca29ea6202799a815ca38f7b66a370e8cf9b"
  integrity sha512-7qo1Mq8ZNmaR4USHHm615nEW2lPeeWJ3bTyoqFbd35DLx0LUH7C6ptt5FDCTAlbIzs3+WKrk5SkJvw8AFDE2hg==

leven@^2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/leven/-/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://tech.suitbim.com/verdaccio/levn/-/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/lilconfig/-/lilconfig-2.1.0.tgz#78e23ac89ebb7e1bfbf25b18043de756548e7f52"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://tech.suitbim.com/verdaccio/lines-and-columns/-/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

lint-staged@^13.2.2:
  version "13.3.0"
  resolved "https://tech.suitbim.com/verdaccio/lint-staged/-/lint-staged-13.3.0.tgz#7965d72a8d6a6c932f85e9c13ccf3596782d28a5"
  integrity sha512-mPRtrYnipYYv1FEE134ufbWpeggNTo+O/UPzngoaKzbzHAthvR55am+8GfHTnqNRQVRRrYQLGW9ZyUoD7DsBHQ==
  dependencies:
    chalk "5.3.0"
    commander "11.0.0"
    debug "4.3.4"
    execa "7.2.0"
    lilconfig "2.1.0"
    listr2 "6.6.1"
    micromatch "4.0.5"
    pidtree "0.6.0"
    string-argv "0.3.2"
    yaml "2.3.1"

lint-staged@^14.0.1:
  version "14.0.1"
  resolved "https://tech.suitbim.com/verdaccio/lint-staged/-/lint-staged-14.0.1.tgz#57dfa3013a3d60762d9af5d9c83bdb51291a6232"
  integrity sha512-Mw0cL6HXnHN1ag0mN/Dg4g6sr8uf8sn98w2Oc1ECtFto9tvRF7nkXGJRbx8gPlHyoR0pLyBr2lQHbWwmUHe1Sw==
  dependencies:
    chalk "5.3.0"
    commander "11.0.0"
    debug "4.3.4"
    execa "7.2.0"
    lilconfig "2.1.0"
    listr2 "6.6.1"
    micromatch "4.0.5"
    pidtree "0.6.0"
    string-argv "0.3.2"
    yaml "2.3.1"

listr2@6.6.1:
  version "6.6.1"
  resolved "https://tech.suitbim.com/verdaccio/listr2/-/listr2-6.6.1.tgz#08b2329e7e8ba6298481464937099f4a2cd7f95d"
  integrity sha512-+rAXGHh0fkEWdXBmX+L6mmfmXmXvDGEKzkjxO+8mP3+nI/r/CWznVBvsibXdxda9Zz0OW2e2ikphN3OwCT/jSg==
  dependencies:
    cli-truncate "^3.1.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^5.0.1"
    rfdc "^1.3.0"
    wrap-ansi "^8.1.0"

local-pkg@^0.5.0:
  version "0.5.0"
  resolved "https://tech.suitbim.com/verdaccio/local-pkg/-/local-pkg-0.5.0.tgz#093d25a346bae59a99f80e75f6e9d36d7e8c925c"
  integrity sha512-ok6z3qlYyCDS4ZEU27HaU6x/xZa9Whf8jD4ptH5UZTQYZVYeb9bnZ3ojVhiJNLiXK1Hfc0GNbLXcmZ5plLDDBg==
  dependencies:
    mlly "^1.4.2"
    pkg-types "^1.0.3"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://tech.suitbim.com/verdaccio/lodash-es/-/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash-unified@^1.0.2:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/lodash-unified/-/lodash-unified-1.0.3.tgz#80b1eac10ed2eb02ed189f08614a29c27d07c894"
  integrity sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://tech.suitbim.com/verdaccio/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "https://tech.suitbim.com/verdaccio/lodash.truncate/-/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash@^4.17.14, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://tech.suitbim.com/verdaccio/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

lodash@~4.11.1:
  version "4.11.2"
  resolved "https://tech.suitbim.com/verdaccio/lodash/-/lodash-4.11.2.tgz#d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b"
  integrity sha1-1rQzixEKWOIdrlzrz9u/0rxM2zs=

log-symbols@^5.1.0:
  version "5.1.0"
  resolved "https://tech.suitbim.com/verdaccio/log-symbols/-/log-symbols-5.1.0.tgz#a20e3b9a5f53fac6aeb8e2bb22c07cf2c8f16d93"
  integrity sha512-l0x2DvrW294C9uDCoQe1VSU4gf529FkSZ6leBl4TiqZH/e+0R7hSfHQBNut2mNygDgHwvYHfFLn6Oxb3VWj2rA==
  dependencies:
    chalk "^5.0.0"
    is-unicode-supported "^1.1.0"

log-update@^5.0.1:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/log-update/-/log-update-5.0.1.tgz#9e928bf70cb183c1f0c9e91d9e6b7115d597ce09"
  integrity sha512-5UtUDQ/6edw4ofyljDNcOVJQ4c7OjDro4h3y8e1GQL5iYElYclVHJ3zeWchylvMaKnDbDilC8irOVyexnA/Slw==
  dependencies:
    ansi-escapes "^5.0.0"
    cli-cursor "^4.0.0"
    slice-ansi "^5.0.0"
    strip-ansi "^7.0.1"
    wrap-ansi "^8.0.1"

long@^5.0.0:
  version "5.2.3"
  resolved "https://tech.suitbim.com/verdaccio/long/-/long-5.2.3.tgz#a3ba97f3877cf1d778eccbcb048525ebb77499e1"
  integrity sha512-lcHwpNoggQTObv5apGNCTdJrO69eHOZMi4BNC+rTLER8iHAqGrUVeLh/irVIM7zTw2bOXA8T6uNPeujwOLg/2Q==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://tech.suitbim.com/verdaccio/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://tech.suitbim.com/verdaccio/magic-string/-/magic-string-0.25.9.tgz#de7f9faf91ef8a1c91d02c2e5314c8277dbcdd1c"
  integrity sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==
  dependencies:
    sourcemap-codec "^1.4.8"

magic-string@^0.30.10, magic-string@^0.30.3, magic-string@^0.30.5:
  version "0.30.10"
  resolved "https://tech.suitbim.com/verdaccio/magic-string/-/magic-string-0.30.10.tgz#123d9c41a0cb5640c892b041d4cfb3bd0aa4b39e"
  integrity sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^3.0.2:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/make-dir/-/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

map-obj@^1.0.0:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/map-obj/-/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"
  integrity sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=

map-obj@^4.1.0:
  version "4.3.0"
  resolved "https://tech.suitbim.com/verdaccio/map-obj/-/map-obj-4.3.0.tgz#9304f906e93faae70880da102a9f1df0ea8bb05a"
  integrity sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "https://tech.suitbim.com/verdaccio/mathml-tag-names/-/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha512-APMBEanjybaPzUrfqU0IMU5I0AswKMH7k8OTLs0vvV4KZpExkTkY87nR/zpbuTPj+gARop7aGUbl11pnDfW6xg==

maximatch@^0.1.0:
  version "0.1.0"
  resolved "https://tech.suitbim.com/verdaccio/maximatch/-/maximatch-0.1.0.tgz#86cd8d6b04c9f307c05a6b9419906d0360fb13a2"
  integrity sha512-9ORVtDUFk4u/NFfo0vG/ND/z7UQCVZBL539YW0+U1I7H1BkZwizcPx5foFv7LCPcBnm2U6RjFnQOsIvN4/Vm2A==
  dependencies:
    array-differ "^1.0.0"
    array-union "^1.0.1"
    arrify "^1.0.0"
    minimatch "^3.0.0"

mdn-data@2.0.30:
  version "2.0.30"
  resolved "https://tech.suitbim.com/verdaccio/mdn-data/-/mdn-data-2.0.30.tgz#ce4df6f80af6cfbe218ecd5c552ba13c4dfa08cc"
  integrity sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/memoize-one/-/memoize-one-6.0.0.tgz#b2591b871ed82948aee4727dc6abceeeac8c1045"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

meow@^10.1.5:
  version "10.1.5"
  resolved "https://tech.suitbim.com/verdaccio/meow/-/meow-10.1.5.tgz#be52a1d87b5f5698602b0f32875ee5940904aa7f"
  integrity sha512-/d+PQ4GKmGvM9Bee/DPa8z3mXs/pkvJE2KEThngVNOqtmljC6K7NMPxtc2JeZYTmpWb9k/TmxjeL18ez3h7vCw==
  dependencies:
    "@types/minimist" "^1.2.2"
    camelcase-keys "^7.0.0"
    decamelize "^5.0.0"
    decamelize-keys "^1.1.0"
    hard-rejection "^2.1.0"
    minimist-options "4.1.0"
    normalize-package-data "^3.0.2"
    read-pkg-up "^8.0.0"
    redent "^4.0.0"
    trim-newlines "^4.0.2"
    type-fest "^1.2.2"
    yargs-parser "^20.2.9"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://tech.suitbim.com/verdaccio/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

mersenne-twister@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/mersenne-twister/-/mersenne-twister-1.1.0.tgz#****************************************"
  integrity sha1-+RZhjuQ9cXnvz2Qb7EUx65Zwl4o=

meshoptimizer@^0.18.1:
  version "0.18.1"
  resolved "https://tech.suitbim.com/verdaccio/meshoptimizer/-/meshoptimizer-0.18.1.tgz#cdb90907f30a7b5b1190facd3b7ee6b7087797d8"
  integrity sha512-ZhoIoL7TNV4s5B6+rx5mC//fw8/POGyNxS/DZyCJeiZ12ScLfVwRE/GfsxwiTkMYYD5DmK2/JXnEVXqL4rF+Sw==

micromatch@4.0.5, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "https://tech.suitbim.com/verdaccio/micromatch/-/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://tech.suitbim.com/verdaccio/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://tech.suitbim.com/verdaccio/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/mimic-fn/-/mimic-fn-4.0.0.tgz#60a90550d5cb0b239cca65d893b1a53b29871ecc"
  integrity sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==

min-indent@^1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/min-indent/-/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==

minimatch@9.0.3:
  version "9.0.3"
  resolved "https://tech.suitbim.com/verdaccio/minimatch/-/minimatch-9.0.3.tgz#a6e00c3de44c3a542bfaae70abfc22420a6da825"
  integrity sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^3.0.0, minimatch@^3.0.2, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://tech.suitbim.com/verdaccio/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.3:
  version "9.0.4"
  resolved "https://tech.suitbim.com/verdaccio/minimatch/-/minimatch-9.0.4.tgz#8e49c731d1749cbec05050ee5145147b32496a51"
  integrity sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==
  dependencies:
    brace-expansion "^2.0.1"

minimist-options@4.1.0:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/minimist-options/-/minimist-options-4.1.0.tgz#c0655713c53a8a2ebd77ffa247d342c40f010619"
  integrity sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==
  dependencies:
    arrify "^1.0.1"
    is-plain-obj "^1.1.0"
    kind-of "^6.0.3"

minimist@^1.1.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://tech.suitbim.com/verdaccio/minimist/-/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

mkdirp@^0.5.1, mkdirp@^0.5.6:
  version "0.5.6"
  resolved "https://tech.suitbim.com/verdaccio/mkdirp/-/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

mlly@^1.4.2, mlly@^1.6.1:
  version "1.7.0"
  resolved "https://tech.suitbim.com/verdaccio/mlly/-/mlly-1.7.0.tgz#587383ae40dda23cadb11c3c3cc972b277724271"
  integrity sha512-U9SDaXGEREBYQgfejV97coK0UL1r+qnF2SyO9A3qcI8MzKnsIFKHNVEkrDyNncQTKQQumsasmeq84eNMdBfsNQ==
  dependencies:
    acorn "^8.11.3"
    pathe "^1.1.2"
    pkg-types "^1.1.0"
    ufo "^1.5.3"

moment@^2.29.4:
  version "2.30.1"
  resolved "https://tech.suitbim.com/verdaccio/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

mqtt-packet@^6.8.0:
  version "6.10.0"
  resolved "https://tech.suitbim.com/verdaccio/mqtt-packet/-/mqtt-packet-6.10.0.tgz#c8b507832c4152e3e511c0efa104ae4a64cd418f"
  integrity sha512-ja8+mFKIHdB1Tpl6vac+sktqy3gA8t9Mduom1BA75cI+R9AHnZOiaBQwpGiWnaVJLDGRdNhQmFaAqd7tkKSMGA==
  dependencies:
    bl "^4.0.2"
    debug "^4.1.1"
    process-nextick-args "^2.0.1"

mqtt@^4.3.8:
  version "4.3.8"
  resolved "https://tech.suitbim.com/verdaccio/mqtt/-/mqtt-4.3.8.tgz#b8cc9a6eb5e4e0cb6eea699f24cd70dd7b228f1d"
  integrity sha512-2xT75uYa0kiPEF/PE0VPdavmEkoBzMT/UL9moid0rAvlCtV48qBwxD62m7Ld/4j8tSkIO1E/iqRl/S72SEOhOw==
  dependencies:
    commist "^1.0.0"
    concat-stream "^2.0.0"
    debug "^4.1.1"
    duplexify "^4.1.1"
    help-me "^3.0.0"
    inherits "^2.0.3"
    lru-cache "^6.0.0"
    minimist "^1.2.5"
    mqtt-packet "^6.8.0"
    number-allocator "^1.0.9"
    pump "^3.0.0"
    readable-stream "^3.6.0"
    reinterval "^1.1.0"
    rfdc "^1.3.0"
    split2 "^3.1.0"
    ws "^7.5.5"
    xtend "^4.0.2"

mrmime@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/mrmime/-/mrmime-2.0.0.tgz#151082a6e06e59a9a39b46b3e14d5cfe92b3abb4"
  integrity sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==

ms@2.1.2:
  version "2.1.2"
  resolved "https://tech.suitbim.com/verdaccio/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@^2.1.1:
  version "2.1.3"
  resolved "https://tech.suitbim.com/verdaccio/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://tech.suitbim.com/verdaccio/nanoid/-/nanoid-3.3.7.tgz#d0c301a691bc8d54efa0a2226ccf3fe2fd656bd8"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://tech.suitbim.com/verdaccio/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://tech.suitbim.com/verdaccio/neo-async/-/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

node-fetch-native@^1.6.3:
  version "1.6.4"
  resolved "https://tech.suitbim.com/verdaccio/node-fetch-native/-/node-fetch-native-1.6.4.tgz#679fc8fd8111266d47d7e72c379f1bed9acff06e"
  integrity sha512-IhOigYzAKHd244OC0JIMIUrjzctirCmPkaIfhDeGcEETWof5zKYUW7e7MYvChGWh/4CJeXEgsRyGzuF334rOOQ==

node-releases@^2.0.14:
  version "2.0.14"
  resolved "https://tech.suitbim.com/verdaccio/node-releases/-/node-releases-2.0.14.tgz#2ffb053bceb8b2be8495ece1ab6ce600c4461b0b"
  integrity sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==

normalize-package-data@^3.0.2:
  version "3.0.3"
  resolved "https://tech.suitbim.com/verdaccio/normalize-package-data/-/normalize-package-data-3.0.3.tgz#dbcc3e2da59509a0983422884cd172eefdfa525e"
  integrity sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==
  dependencies:
    hosted-git-info "^4.0.1"
    is-core-module "^2.5.0"
    semver "^7.3.4"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-wheel-es@^1.2.0:
  version "1.2.0"
  resolved "https://tech.suitbim.com/verdaccio/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz#0fa2593d619f7245a541652619105ab076acf09e"
  integrity sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==

nosleep.js@^0.12.0:
  version "0.12.0"
  resolved "https://tech.suitbim.com/verdaccio/nosleep.js/-/nosleep.js-0.12.0.tgz#a01fddab2c13af357d673928b1f40a9013a4dc08"
  integrity sha512-9d1HbpKLh3sdWlhXMhU6MMH+wQzKkrgfRkYV0EBdvt99YJfj0ilCJrWRDYG2130Tm4GXbEoTCx5b34JSaP+HhA==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://tech.suitbim.com/verdaccio/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "https://tech.suitbim.com/verdaccio/npm-run-path/-/npm-run-path-5.3.0.tgz#e23353d0ebb9317f174e93417e4a4d82d0249e9f"
  integrity sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==
  dependencies:
    path-key "^4.0.0"

nprogress@^0.2.0:
  version "0.2.0"
  resolved "https://tech.suitbim.com/verdaccio/nprogress/-/nprogress-0.2.0.tgz#cb8f34c53213d895723fcbab907e9422adbcafb1"
  integrity sha1-y480xTIT2JVyP8urkH6UIq28r7E=

nth-check@^2.1.1:
  version "2.1.1"
  resolved "https://tech.suitbim.com/verdaccio/nth-check/-/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

number-allocator@^1.0.9:
  version "1.0.14"
  resolved "https://tech.suitbim.com/verdaccio/number-allocator/-/number-allocator-1.0.14.tgz#1f2e32855498a7740dcc8c78bed54592d930ee4d"
  integrity sha512-OrL44UTVAvkKdOdRQZIJpLkAdjXGTRda052sN4sO77bKEzYYqWKMBjQvrJFzqygI99gL6Z4u2xctPW1tB8ErvA==
  dependencies:
    debug "^4.3.1"
    js-sdsl "4.3.0"

object-assign@*:
  version "4.1.1"
  resolved "https://tech.suitbim.com/verdaccio/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://tech.suitbim.com/verdaccio/object-is/-/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://tech.suitbim.com/verdaccio/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

ofetch@^1.3.3:
  version "1.3.4"
  resolved "https://tech.suitbim.com/verdaccio/ofetch/-/ofetch-1.3.4.tgz#7ea65ced3c592ec2b9906975ae3fe1d26a56f635"
  integrity sha512-KLIET85ik3vhEfS+3fDlc/BAZiAp+43QEC/yCo5zkNoY2YaKvNkOaFr/6wCFgFH1kuYQM5pMNi0Tg8koiIemtw==
  dependencies:
    destr "^2.0.3"
    node-fetch-native "^1.6.3"
    ufo "^1.5.3"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://tech.suitbim.com/verdaccio/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://tech.suitbim.com/verdaccio/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/onetime/-/onetime-6.0.0.tgz#7c24c18ed1fd2e9bca4bd26806a33613c77d34b4"
  integrity sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==
  dependencies:
    mimic-fn "^4.0.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://tech.suitbim.com/verdaccio/optionator/-/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^7.0.1:
  version "7.0.1"
  resolved "https://tech.suitbim.com/verdaccio/ora/-/ora-7.0.1.tgz#cdd530ecd865fe39e451a0e7697865669cb11930"
  integrity sha512-0TUxTiFJWv+JnjWm4o9yvuskpEJLXTcng8MJuKd+SzAzp2o+OP3HWqNhB4OdJRt1Vsd9/mR0oyaEYlOnL7XIRw==
  dependencies:
    chalk "^5.3.0"
    cli-cursor "^4.0.0"
    cli-spinners "^2.9.0"
    is-interactive "^2.0.0"
    is-unicode-supported "^1.3.0"
    log-symbols "^5.1.0"
    stdin-discarder "^0.1.0"
    string-width "^6.1.0"
    strip-ansi "^7.1.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-map@^5.5.0:
  version "5.5.0"
  resolved "https://tech.suitbim.com/verdaccio/p-map/-/p-map-5.5.0.tgz#054ca8ca778dfa4cf3f8db6638ccb5b937266715"
  integrity sha512-VFqfGDHlx87K66yZrNdI4YGtD70IRyd+zSvgks6mzHPRNkoKy+9EKP4SFC77/vTTQYmRmti7dvqC+m5jBrBAcg==
  dependencies:
    aggregate-error "^4.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://tech.suitbim.com/verdaccio/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

pako@^2.0.4:
  version "2.1.0"
  resolved "https://tech.suitbim.com/verdaccio/pako/-/pako-2.1.0.tgz#266cc37f98c7d883545d11335c00fbd4062c9a86"
  integrity sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://tech.suitbim.com/verdaccio/parse-json/-/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-network-drive@^1.0.20:
  version "1.0.20"
  resolved "https://tech.suitbim.com/verdaccio/path-is-network-drive/-/path-is-network-drive-1.0.20.tgz#9c264db2e0fce5e9bc2ef9177fcab3f996d1a1b5"
  integrity sha512-p5wCWlRB4+ggzxWshqHH9aF3kAuVu295NaENXmVhThbZPJQBeJdxZTP6CIoUR+kWHDUW56S9YcaO1gXnc/BOxw==
  dependencies:
    tslib "^2"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://tech.suitbim.com/verdaccio/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/path-key/-/path-key-4.0.0.tgz#295588dc3aee64154f877adb9d780b81c554bf18"
  integrity sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://tech.suitbim.com/verdaccio/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-strip-sep@^1.0.17:
  version "1.0.17"
  resolved "https://tech.suitbim.com/verdaccio/path-strip-sep/-/path-strip-sep-1.0.17.tgz#3b7dd4f461cf73a9277333f50289ce9b00cffba3"
  integrity sha512-+2zIC2fNgdilgV7pTrktY6oOxxZUo9x5zJYfTzxsGze5kSGDDwhA5/0WlBn+sUyv/WuuyYn3OfM+Ue5nhdQUgA==
  dependencies:
    tslib "^2"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

pathe@^1.1.1, pathe@^1.1.2:
  version "1.1.2"
  resolved "https://tech.suitbim.com/verdaccio/pathe/-/pathe-1.1.2.tgz#6c4cb47a945692e48a1ddd6e4094d170516437ec"
  integrity sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/perfect-debounce/-/perfect-debounce-1.0.0.tgz#9c2e8bc30b169cc984a58b7d5b28049839591d2a"
  integrity sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/picocolors/-/picocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pidtree@0.6.0:
  version "0.6.0"
  resolved "https://tech.suitbim.com/verdaccio/pidtree/-/pidtree-0.6.0.tgz#90ad7b6d42d5841e69e0a2419ef38f8883aa057c"
  integrity sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pinia@^2.1.4:
  version "2.1.7"
  resolved "https://tech.suitbim.com/verdaccio/pinia/-/pinia-2.1.7.tgz#4cf5420d9324ca00b7b4984d3fbf693222115bbc"
  integrity sha512-+C2AHFtcFqjPih0zpYuvof37SFxMQ7OEG2zV9jRI12i9BOy3YQVAHwdKtyyc8pDcDyIc33WCIsZaCFWU7WWxGQ==
  dependencies:
    "@vue/devtools-api" "^6.5.0"
    vue-demi ">=0.14.5"

"pkg-dir@< 6 >= 5":
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/pkg-dir/-/pkg-dir-5.0.0.tgz#a02d6aebe6ba133a928f74aec20bafdfe6b8e760"
  integrity sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==
  dependencies:
    find-up "^5.0.0"

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://tech.suitbim.com/verdaccio/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pkg-types@^1.0.3, pkg-types@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/pkg-types/-/pkg-types-1.1.0.tgz#3ec1bf33379030fd0a34c227b6c650e8ea7ca271"
  integrity sha512-/RpmvKdxKf8uILTtoOhAgf30wYbP2Qw+L9p3Rvshx1JZVX+XQNZQFjlbmGHEGIm4CkVPlSn+NXmIM8+9oWQaSA==
  dependencies:
    confbox "^0.1.7"
    mlly "^1.6.1"
    pathe "^1.1.2"

point-in-polygon@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/point-in-polygon/-/point-in-polygon-1.1.0.tgz#b0af2616c01bdee341cbf2894df643387ca03357"
  integrity sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==

polygon-clipping@^0.15.3:
  version "0.15.7"
  resolved "https://tech.suitbim.com/verdaccio/polygon-clipping/-/polygon-clipping-0.15.7.tgz#3823ca1e372566f350795ce9dd9a7b19e97bdaad"
  integrity sha512-nhfdr83ECBg6xtqOAJab1tbksbBAOMUltN60bU+llHVOL0e5Onm1WpAXXWXVB39L8AJFssoIhEVuy/S90MmotA==
  dependencies:
    robust-predicates "^3.0.2"
    splaytree "^3.1.0"

portfinder@^1.0.28:
  version "1.0.32"
  resolved "https://tech.suitbim.com/verdaccio/portfinder/-/portfinder-1.0.32.tgz#2fe1b9e58389712429dc2bea5beb2146146c7f81"
  integrity sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

postcss-html@^1.5.0:
  version "1.6.0"
  resolved "https://tech.suitbim.com/verdaccio/postcss-html/-/postcss-html-1.6.0.tgz#3b5fc5dfcc43ec54934c3a2dee73643725596ec4"
  integrity sha512-OWgQ9/Pe23MnNJC0PL4uZp8k0EDaUvqpJFSiwFxOLClAhmD7UEisyhO3x5hVsD4xFrjReVTXydlrMes45dJ71w==
  dependencies:
    htmlparser2 "^8.0.0"
    js-tokens "^8.0.0"
    postcss "^8.4.0"
    postcss-safe-parser "^6.0.0"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "https://tech.suitbim.com/verdaccio/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-resolve-nested-selector@^0.1.1:
  version "0.1.1"
  resolved "https://tech.suitbim.com/verdaccio/postcss-resolve-nested-selector/-/postcss-resolve-nested-selector-0.1.1.tgz#29ccbc7c37dedfac304e9fff0bf1596b3f6a0e4e"
  integrity sha1-Kcy8fDfe36wwTp//C/FZaz9qDk4=

postcss-safe-parser@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/postcss-safe-parser/-/postcss-safe-parser-6.0.0.tgz#bb4c29894171a94bc5c996b9a30317ef402adaa1"
  integrity sha512-FARHN8pwH+WiS2OPCxJI8FuRJpTVnn6ZNFiqAM2aeW2LwTHWWmWgIyKC6cUo0L8aeKiF/14MNvnpls6R2PBeMQ==

postcss-scss@^4.0.9:
  version "4.0.9"
  resolved "https://tech.suitbim.com/verdaccio/postcss-scss/-/postcss-scss-4.0.9.tgz#a03c773cd4c9623cb04ce142a52afcec74806685"
  integrity sha512-AjKOeiwAitL/MXxQW2DliT28EKukvvbEWx3LBmJIRN8KfBGZbRTxNYW0kSqi1COiTZ57nZ9NW06S6ux//N1c9A==

postcss-selector-parser@^6.0.13, postcss-selector-parser@^6.0.15:
  version "6.0.16"
  resolved "https://tech.suitbim.com/verdaccio/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz#3b88b9f5c5abd989ef4e2fc9ec8eedd34b20fb04"
  integrity sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-sorting@^8.0.2:
  version "8.0.2"
  resolved "https://tech.suitbim.com/verdaccio/postcss-sorting/-/postcss-sorting-8.0.2.tgz#6393385ece272baf74bee9820fb1b58098e4eeca"
  integrity sha512-M9dkSrmU00t/jK7rF6BZSZauA5MAaBW4i5EnJXspMwt4iqTh/L9j6fgMnbElEOfyRyfLfVbIHj/R52zHzAPe1Q==

postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://tech.suitbim.com/verdaccio/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.4.0, postcss@^8.4.27, postcss@^8.4.28, postcss@^8.4.30, postcss@^8.4.32, postcss@^8.4.38:
  version "8.4.38"
  resolved "https://tech.suitbim.com/verdaccio/postcss/-/postcss-8.4.38.tgz#b387d533baf2054288e337066d81c6bee9db9e0e"
  integrity sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.0"
    source-map-js "^1.2.0"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://tech.suitbim.com/verdaccio/prelude-ls/-/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier@2.8.8:
  version "2.8.8"
  resolved "https://tech.suitbim.com/verdaccio/prettier/-/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

process-nextick-args@^2.0.1:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

promise@^7.0.1:
  version "7.3.1"
  resolved "https://tech.suitbim.com/verdaccio/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
  dependencies:
    asap "~2.0.3"

protobufjs@^7.1.0:
  version "7.2.6"
  resolved "https://tech.suitbim.com/verdaccio/protobufjs/-/protobufjs-7.2.6.tgz#4a0ccd79eb292717aacf07530a07e0ed20278215"
  integrity sha512-dgJaEDDL6x8ASUZ1YqWciTRrdOuYNzoOf27oHNfdyvKqHr5i0FV7FSLU+aIeFjyFgVxrpTOtQUi0BLLBymZaBw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/proxy-from-env/-/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

prr@~1.0.1:
  version "1.0.1"
  resolved "https://tech.suitbim.com/verdaccio/prr/-/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

pump@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/pump/-/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://tech.suitbim.com/verdaccio/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://tech.suitbim.com/verdaccio/quick-lru/-/quick-lru-5.1.1.tgz#366493e6b3e42a3a6885e2e99d18f80fb7a8c932"
  integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==

quickselect@^1.0.1:
  version "1.1.1"
  resolved "https://tech.suitbim.com/verdaccio/quickselect/-/quickselect-1.1.1.tgz#852e412ce418f237ad5b660d70cffac647ae94c2"
  integrity sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==

quickselect@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/quickselect/-/quickselect-2.0.0.tgz#f19680a486a5eefb581303e023e98faaf25dd018"
  integrity sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==

qweather-icons@^1.1.1:
  version "1.6.0"
  resolved "https://tech.suitbim.com/verdaccio/qweather-icons/-/qweather-icons-1.6.0.tgz#097676b8bbbe34e3e7c18371de8c6631d37c1e49"
  integrity sha512-uINrSOteHHarEeHRpP37aBnuuwYnWc1eyZ2gbnujoEqOVabIPDiEseF7a9eIOnBn7GZBlo5nYj29eOEfLH/bEA==

rbush@2.x, rbush@^2.0.1:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/rbush/-/rbush-2.0.2.tgz#bb6005c2731b7ba1d5a9a035772927d16a614605"
  integrity sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==
  dependencies:
    quickselect "^1.0.1"

rbush@^3.0.1:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/rbush/-/rbush-3.0.1.tgz#5fafa8a79b3b9afdfe5008403a720cc1de882ecf"
  integrity sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==
  dependencies:
    quickselect "^2.0.0"

read-pkg-up@^8.0.0:
  version "8.0.0"
  resolved "https://tech.suitbim.com/verdaccio/read-pkg-up/-/read-pkg-up-8.0.0.tgz#72f595b65e66110f43b052dd9af4de6b10534670"
  integrity sha512-snVCqPczksT0HS2EC+SxUndvSzn6LRCwpfSvLrIfR5BKDQQZMaI6jPRC9dYvYFDRAuFEAnkwww8kBBNE/3VvzQ==
  dependencies:
    find-up "^5.0.0"
    read-pkg "^6.0.0"
    type-fest "^1.0.1"

read-pkg@^6.0.0:
  version "6.0.0"
  resolved "https://tech.suitbim.com/verdaccio/read-pkg/-/read-pkg-6.0.0.tgz#a67a7d6a1c2b0c3cd6aa2ea521f40c458a4a504c"
  integrity sha512-X1Fu3dPuk/8ZLsMhEj5f4wFAF0DWoK7qhGJvgaijocXxBmSToKfbFtqbxMO7bVjNA1dmE5huAzjXj/ey86iw9Q==
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^3.0.2"
    parse-json "^5.2.0"
    type-fest "^1.0.1"

readable-stream@^3.0.0, readable-stream@^3.0.2, readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://tech.suitbim.com/verdaccio/readable-stream/-/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.0:
  version "1.0.34"
  resolved "https://tech.suitbim.com/verdaccio/readable-stream/-/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://tech.suitbim.com/verdaccio/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

recursive-copy@^2.0.14:
  version "2.0.14"
  resolved "https://tech.suitbim.com/verdaccio/recursive-copy/-/recursive-copy-2.0.14.tgz#6358af3b5f8da89562f000db44720c4daa94b6d7"
  integrity sha512-K8WNY8f8naTpfbA+RaXmkaQuD1IeW9EgNEfyGxSqqTQukpVtoOKros9jUqbpEsSw59YOmpd8nCBgtqJZy5nvog==
  dependencies:
    errno "^0.1.2"
    graceful-fs "^4.1.4"
    junk "^1.0.1"
    maximatch "^0.1.0"
    mkdirp "^0.5.1"
    pify "^2.3.0"
    promise "^7.0.1"
    rimraf "^2.7.1"
    slash "^1.0.0"

redent@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/redent/-/redent-4.0.0.tgz#0c0ba7caabb24257ab3bb7a4fd95dd1d5c5681f9"
  integrity sha512-tYkDkVVtYkSVhuQ4zBgfvciymHaeuel+zFKXShfDnFP5SyVEP7qo70Rf1jTOTCx3vGNAbnEi/xFkcfQVMIBWag==
  dependencies:
    indent-string "^5.0.0"
    strip-indent "^4.0.0"

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://tech.suitbim.com/verdaccio/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz#356ade10263f685dda125100cd862c1db895327f"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regexp.prototype.flags@^1.5.1:
  version "1.5.2"
  resolved "https://tech.suitbim.com/verdaccio/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz#138f644a3350f981a858c44f6bb1a61ff59be334"
  integrity sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==
  dependencies:
    call-bind "^1.0.6"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.1"

reinterval@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/reinterval/-/reinterval-1.1.0.tgz#3361ecfa3ca6c18283380dd0bb9546f390f5ece7"
  integrity sha512-QIRet3SYrGp0HUHO88jVskiG6seqUGC5iAG7AwI/BV4ypGcuqk9Du6YQBUOUqm9c8pw1eyLoIaONifRua1lsEQ==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/require-from-string/-/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://tech.suitbim.com/verdaccio/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve@^1.12.0, resolve@^1.20.0:
  version "1.22.8"
  resolved "https://tech.suitbim.com/verdaccio/resolve/-/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/restore-cursor/-/restore-cursor-4.0.0.tgz#519560a4318975096def6e609d44100edaa4ccb9"
  integrity sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://tech.suitbim.com/verdaccio/reusify/-/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rfdc@^1.3.0:
  version "1.3.1"
  resolved "https://tech.suitbim.com/verdaccio/rfdc/-/rfdc-1.3.1.tgz#2b6d4df52dffe8bb346992a10ea9451f24373a8f"
  integrity sha512-r5a3l5HzYlIC68TpmYKlxWjmOP6wiPJ1vWv2HeLhNsRZMrCkxeqxiHlQ21oXmQ4F3SiryXBHhAD7JZqvOJjFmg==

rimraf@^2.7.1:
  version "2.7.1"
  resolved "https://tech.suitbim.com/verdaccio/rimraf/-/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://tech.suitbim.com/verdaccio/rimraf/-/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

robust-predicates@^2.0.4:
  version "2.0.4"
  resolved "https://tech.suitbim.com/verdaccio/robust-predicates/-/robust-predicates-2.0.4.tgz#0a2367a93abd99676d075981707f29cfb402248b"
  integrity sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==

robust-predicates@^3.0.2:
  version "3.0.2"
  resolved "https://tech.suitbim.com/verdaccio/robust-predicates/-/robust-predicates-3.0.2.tgz#d5b28528c4824d20fc48df1928d41d9efa1ad771"
  integrity sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==

rollup-plugin-typescript2@^0.31.2:
  version "0.31.2"
  resolved "https://tech.suitbim.com/verdaccio/rollup-plugin-typescript2/-/rollup-plugin-typescript2-0.31.2.tgz#463aa713a7e2bf85b92860094b9f7fb274c5a4d8"
  integrity sha512-hRwEYR1C8xDGVVMFJQdEVnNAeWRvpaY97g5mp3IeLnzhNXzSVq78Ye/BJ9PAaUfN4DXa/uDnqerifMOaMFY54Q==
  dependencies:
    "@rollup/pluginutils" "^4.1.2"
    "@yarn-tool/resolve-package" "^1.0.40"
    find-cache-dir "^3.3.2"
    fs-extra "^10.0.0"
    resolve "^1.20.0"
    tslib "^2.3.1"

rollup@^2.77.2:
  version "2.79.1"
  resolved "https://tech.suitbim.com/verdaccio/rollup/-/rollup-2.79.1.tgz#bedee8faef7c9f93a2647ac0108748f497f081c7"
  integrity sha512-uKxbd0IhMZOhjAiD5oAFp7BqvkA4Dv47qpOCtaNvng4HBwdbWtdOh8f5nZNuk2rp51PMGk3bzfWu5oayNEuYnw==
  optionalDependencies:
    fsevents "~2.3.2"

rollup@^3.27.1:
  version "3.29.4"
  resolved "https://tech.suitbim.com/verdaccio/rollup/-/rollup-3.29.4.tgz#4d70c0f9834146df8705bfb69a9a19c9e1109981"
  integrity sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://tech.suitbim.com/verdaccio/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://tech.suitbim.com/verdaccio/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://tech.suitbim.com/verdaccio/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass-loader@^13.3.2:
  version "13.3.3"
  resolved "https://tech.suitbim.com/verdaccio/sass-loader/-/sass-loader-13.3.3.tgz#60df5e858788cffb1a3215e5b92e9cba61e7e133"
  integrity sha512-mt5YN2F1MOZr3d/wBRcZxeFgwgkH44wVc2zohO2YF6JiOMkiXe4BYRZpSu2sO1g71mo/j16txzUhsKZlqjVGzA==
  dependencies:
    neo-async "^2.6.2"

sass@^1.63.3:
  version "1.77.0"
  resolved "https://tech.suitbim.com/verdaccio/sass/-/sass-1.77.0.tgz#e736c69aff9fae4a4e6dae60a979eee9c942f321"
  integrity sha512-eGj4HNfXqBWtSnvItNkn7B6icqH14i3CiCGbzMKs3BAPTq62pp9NBYsBgyN4cA+qssqo9r26lW4JSvlaUUWbgw==
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

scp2@^0.5.0:
  version "0.5.0"
  resolved "https://tech.suitbim.com/verdaccio/scp2/-/scp2-0.5.0.tgz#64ee74bc3685f3a4c6290f2da8c1e3b4eef92e8d"
  integrity sha512-HzPWuOHM/qVjVYhjmgfBKyUXQsI+9+SdI5l+5E0S98bUHirf1NoTynfrAPb0kr0oJKg/JFdFSlZwq7FnqLttvw==
  dependencies:
    async "~0.9.0"
    glob "~7.0.3"
    lodash "~4.11.1"
    ssh2 "~0.4.10"

scule@^1.1.1:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/scule/-/scule-1.3.0.tgz#6efbd22fd0bb801bdcc585c89266a7d2daa8fbd3"
  integrity sha512-6FtHJEvt+pVMIB9IBY+IcCJ6Z5f1iQnytgyfKMhDKgmzYG+TeH/wx1y3l27rshSbLiSanrR9ffZDrEsmjlQF2g==

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://tech.suitbim.com/verdaccio/semver/-/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.4, semver@^7.3.5, semver@^7.3.6, semver@^7.5.4, semver@^7.6.0:
  version "7.6.0"
  resolved "https://tech.suitbim.com/verdaccio/semver/-/semver-7.6.0.tgz#1a46a4db4bffcccd97b743b5005c8325f23d4e2d"
  integrity sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==
  dependencies:
    lru-cache "^6.0.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://tech.suitbim.com/verdaccio/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.1:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/set-function-name/-/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://tech.suitbim.com/verdaccio/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://tech.suitbim.com/verdaccio/signal-exit/-/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://tech.suitbim.com/verdaccio/simple-swizzle/-/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sirv@^2.0.3:
  version "2.0.4"
  resolved "https://tech.suitbim.com/verdaccio/sirv/-/sirv-2.0.4.tgz#5dd9a725c578e34e449f332703eb2a74e46a29b0"
  integrity sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

skmeans@0.9.7:
  version "0.9.7"
  resolved "https://tech.suitbim.com/verdaccio/skmeans/-/skmeans-0.9.7.tgz#72670cebb728508f56e29c0e10d11e623529ce5d"
  integrity sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==

slash@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/slash/-/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slash@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/slash/-/slash-4.0.0.tgz#2422372176c4c6c5addb5e2ada885af984b396a7"
  integrity sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/slice-ansi/-/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "https://tech.suitbim.com/verdaccio/slice-ansi/-/slice-ansi-5.0.0.tgz#b73063c57aa96f9cd881654b15294d95d285c42a"
  integrity sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.0.1, source-map-js@^1.2.0:
  version "1.2.0"
  resolved "https://tech.suitbim.com/verdaccio/source-map-js/-/source-map-js-1.2.0.tgz#16b809c162517b5b8c3e7dcd315a2a5c2612b2af"
  integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://tech.suitbim.com/verdaccio/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://tech.suitbim.com/verdaccio/spdx-correct/-/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "https://tech.suitbim.com/verdaccio/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz#5d607d27fc806f66d7b64a766650fa890f04ed66"
  integrity sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.17"
  resolved "https://tech.suitbim.com/verdaccio/spdx-license-ids/-/spdx-license-ids-3.0.17.tgz#887da8aa73218e51a1d917502d79863161a93f9c"
  integrity sha512-sh8PWc/ftMqAAdFiBu6Fy6JUOYjqDJBJvIhpfDMyHrr0Rbp5liZqd4TjtQ/RgfLjKFZb+LMx5hpml5qOWy0qvg==

splaytree@^3.1.0:
  version "3.1.2"
  resolved "https://tech.suitbim.com/verdaccio/splaytree/-/splaytree-3.1.2.tgz#d1db2691665a3c69d630de98d55145a6546dc166"
  integrity sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==

split2@^3.1.0:
  version "3.2.2"
  resolved "https://tech.suitbim.com/verdaccio/split2/-/split2-3.2.2.tgz#bf2cf2a37d838312c249c89206fd7a17dd12365f"
  integrity sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==
  dependencies:
    readable-stream "^3.0.0"

ssh2-streams@~0.0.22:
  version "0.0.23"
  resolved "https://tech.suitbim.com/verdaccio/ssh2-streams/-/ssh2-streams-0.0.23.tgz#aeef30831bb5fc4af6aa3f6d0a261a413531612b"
  integrity sha512-llhegJ0WOuEZQoWvh+ZB/ZQpJNjuDVPVAh+UjIsi0YLM7GeKPX7xMnh5LJtjOBAChumuUg7hNfIUfKjrxfNNYg==
  dependencies:
    asn1 "~0.2.0"
    readable-stream "~1.0.0"
    streamsearch "~0.1.2"

ssh2@~0.4.10:
  version "0.4.15"
  resolved "https://tech.suitbim.com/verdaccio/ssh2/-/ssh2-0.4.15.tgz#07c6f4106d9f7b6ea6e4df636c6c53f1f9817ff8"
  integrity sha512-/KEtwbNly4LtP92bBfgY4RCj8YAvKaXg89nLaCINNMu1X5d++W5DOZz+yX2xwjlSmdw8we7AC9LYnAzBkA4OwA==
  dependencies:
    readable-stream "~1.0.0"
    ssh2-streams "~0.0.22"

stdin-discarder@^0.1.0:
  version "0.1.0"
  resolved "https://tech.suitbim.com/verdaccio/stdin-discarder/-/stdin-discarder-0.1.0.tgz#22b3e400393a8e28ebf53f9958f3880622efde21"
  integrity sha512-xhV7w8S+bUwlPTb4bAOUQhv8/cSS5offJuX8GQGq32ONF0ZtDWKfkdomM3HMRA+LhX6um/FZ0COqlwsjD53LeQ==
  dependencies:
    bl "^5.0.0"

stream-shift@^1.0.2:
  version "1.0.3"
  resolved "https://tech.suitbim.com/verdaccio/stream-shift/-/stream-shift-1.0.3.tgz#85b8fab4d71010fc3ba8772e8046cc49b8a3864b"
  integrity sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==

streamsearch@~0.1.2:
  version "0.1.2"
  resolved "https://tech.suitbim.com/verdaccio/streamsearch/-/streamsearch-0.1.2.tgz#808b9d0e56fc273d809ba57338e929919a1a9f1a"
  integrity sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=

string-argv@0.3.2:
  version "0.3.2"
  resolved "https://tech.suitbim.com/verdaccio/string-argv/-/string-argv-0.3.2.tgz#2b6d0ef24b656274d957d54e0a4bbf6153dc02b6"
  integrity sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==

string-width@^4.2.3:
  version "4.2.3"
  resolved "https://tech.suitbim.com/verdaccio/string-width/-/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0, string-width@^5.0.1:
  version "5.1.2"
  resolved "https://tech.suitbim.com/verdaccio/string-width/-/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^6.1.0:
  version "6.1.0"
  resolved "https://tech.suitbim.com/verdaccio/string-width/-/string-width-6.1.0.tgz#96488d6ed23f9ad5d82d13522af9e4c4c3fd7518"
  integrity sha512-k01swCJAgQmuADB0YIc+7TuatfNvTBVOoaUWJjTB9R4VJzR5vNWzf5t42ESVZFPS8xTySF7CAdV4t/aaIm3UnQ==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^10.2.1"
    strip-ansi "^7.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/string_decoder/-/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://tech.suitbim.com/verdaccio/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://tech.suitbim.com/verdaccio/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://tech.suitbim.com/verdaccio/strip-ansi/-/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/strip-final-newline/-/strip-final-newline-3.0.0.tgz#52894c313fbff318835280aed60ff71ebf12b8fd"
  integrity sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==

strip-indent@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/strip-indent/-/strip-indent-4.0.0.tgz#b41379433dd06f5eae805e21d631e07ee670d853"
  integrity sha512-mnVSV2l+Zv6BLpSD/8V87CW/y9EmmbYzGCIavsnsI6/nwn26DwffM/yztm30Z/I2DY9wdS3vXVCMnHDgZaVNoA==
  dependencies:
    min-indent "^1.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://tech.suitbim.com/verdaccio/strip-json-comments/-/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

strip-literal@^1.3.0:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/strip-literal/-/strip-literal-1.3.0.tgz#db3942c2ec1699e6836ad230090b84bb458e3a07"
  integrity sha512-PugKzOsyXpArk0yWmUwqOZecSO0GH0bPoctLcqNDH9J04pVW3lflYE0ujElBGTloevcxF5MofAOZ7C5l2b+wLg==
  dependencies:
    acorn "^8.10.0"

style-search@^0.1.0:
  version "0.1.0"
  resolved "https://tech.suitbim.com/verdaccio/style-search/-/style-search-0.1.0.tgz#7958c793e47e32e07d2b5cafe5c0bf8e12e77902"
  integrity sha1-eVjHk+R+MuB9K1yv5cC/jhLneQI=

stylelint-config-html@>=1.0.0, stylelint-config-html@^1.1.0:
  version "1.1.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-html/-/stylelint-config-html-1.1.0.tgz#999db19aea713b7ff6dde92ada76e4c1bd812b66"
  integrity sha512-IZv4IVESjKLumUGi+HWeb7skgO6/g4VMuAYrJdlqQFndgbj6WJAXPhaysvBiXefX79upBdQVumgYcdd17gCpjQ==

stylelint-config-prettier@^9.0.5:
  version "9.0.5"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-prettier/-/stylelint-config-prettier-9.0.5.tgz#9f78bbf31c7307ca2df2dd60f42c7014ee9da56e"
  integrity sha512-U44lELgLZhbAD/xy/vncZ2Pq8sh2TnpiPvo38Ifg9+zeioR+LAkHu0i6YORIOxFafZoVg0xqQwex6e6F25S5XA==

stylelint-config-recess-order@^4.3.0:
  version "4.6.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-recess-order/-/stylelint-config-recess-order-4.6.0.tgz#b4c851c490b4e626b14712686b1eab1e4b17e253"
  integrity sha512-V76fhv3YtcNXh/hyAuAdSzi5FmcrG54Mp2AThJ3D/PTMTSYzUPd7GIhP6z9mTqnRhmkk6YTfcu/JWB8h+Yrcaw==
  dependencies:
    stylelint-order "6.x"

stylelint-config-recommended-scss@^13.0.0, stylelint-config-recommended-scss@^13.1.0:
  version "13.1.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-recommended-scss/-/stylelint-config-recommended-scss-13.1.0.tgz#04e529ae0e9c1abb1e04de79258461c07811876f"
  integrity sha512-8L5nDfd+YH6AOoBGKmhH8pLWF1dpfY816JtGMePcBqqSsLU+Ysawx44fQSlMOJ2xTfI9yTGpup5JU77c17w1Ww==
  dependencies:
    postcss-scss "^4.0.9"
    stylelint-config-recommended "^13.0.0"
    stylelint-scss "^5.3.0"

stylelint-config-recommended-vue@^1.5.0:
  version "1.5.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-recommended-vue/-/stylelint-config-recommended-vue-1.5.0.tgz#c38775859c58a928cd34d95aa79db09b69964160"
  integrity sha512-65TAK/clUqkNtkZLcuytoxU0URQYlml+30Nhop7sRkCZ/mtWdXt7T+spPSB3KMKlb+82aEVJ4OrcstyDBdbosg==
  dependencies:
    semver "^7.3.5"
    stylelint-config-html ">=1.0.0"
    stylelint-config-recommended ">=6.0.0"

stylelint-config-recommended@>=6.0.0:
  version "14.0.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-recommended/-/stylelint-config-recommended-14.0.0.tgz#b395c7014838d2aaca1755eebd914d0bb5274994"
  integrity sha512-jSkx290CglS8StmrLp2TxAppIajzIBZKYm3IxT89Kg6fGlxbPiTiyH9PS5YUuVAFwaJLl1ikiXX0QWjI0jmgZQ==

stylelint-config-recommended@^13.0.0:
  version "13.0.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-recommended/-/stylelint-config-recommended-13.0.0.tgz#c48a358cc46b629ea01f22db60b351f703e00597"
  integrity sha512-EH+yRj6h3GAe/fRiyaoO2F9l9Tgg50AOFhaszyfov9v6ayXJ1IkSHwTxd7lB48FmOeSGDPLjatjO11fJpmarkQ==

stylelint-config-standard-scss@^11.0.0:
  version "11.1.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-standard-scss/-/stylelint-config-standard-scss-11.1.0.tgz#53c2fb9423ed89c0921aa83479892a912cc1ca15"
  integrity sha512-5gnBgeNTgRVdchMwiFQPuBOtj9QefYtfXiddrOMJA2pI22zxt6ddI2s+e5Oh7/6QYl7QLJujGnaUR5YyGq72ow==
  dependencies:
    stylelint-config-recommended-scss "^13.1.0"
    stylelint-config-standard "^34.0.0"

stylelint-config-standard@^34.0.0:
  version "34.0.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-config-standard/-/stylelint-config-standard-34.0.0.tgz#309f3c48118a02aae262230c174282e40e766cf4"
  integrity sha512-u0VSZnVyW9VSryBG2LSO+OQTjN7zF9XJaAJRX/4EwkmU0R2jYwmBSN10acqZisDitS0CLiEiGjX7+Hrq8TAhfQ==
  dependencies:
    stylelint-config-recommended "^13.0.0"

stylelint-order@6.x:
  version "6.0.4"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-order/-/stylelint-order-6.0.4.tgz#3e80d876c61a98d2640de181433686f24284748b"
  integrity sha512-0UuKo4+s1hgQ/uAxlYU4h0o0HS4NiQDud0NAUNI0aa8FJdmYHA5ZZTFHiV5FpmE3071e9pZx5j0QpVJW5zOCUA==
  dependencies:
    postcss "^8.4.32"
    postcss-sorting "^8.0.2"

stylelint-scss@^5.3.0:
  version "5.3.2"
  resolved "https://tech.suitbim.com/verdaccio/stylelint-scss/-/stylelint-scss-5.3.2.tgz#c54564dfbd98de0c08742b9c43025cda91acf940"
  integrity sha512-4LzLaayFhFyneJwLo0IUa8knuIvj+zF0vBFueQs4e3tEaAMIQX8q5th8ziKkgOavr6y/y9yoBe+RXN/edwLzsQ==
  dependencies:
    known-css-properties "^0.29.0"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-selector-parser "^6.0.13"
    postcss-value-parser "^4.2.0"

stylelint@^15.10.3:
  version "15.11.0"
  resolved "https://tech.suitbim.com/verdaccio/stylelint/-/stylelint-15.11.0.tgz#3ff8466f5f5c47362bc7c8c9d382741c58bc3292"
  integrity sha512-78O4c6IswZ9TzpcIiQJIN49K3qNoXTM8zEJzhaTE/xRTCZswaovSEVIa/uwbOltZrk16X4jAxjaOhzz/hTm1Kw==
  dependencies:
    "@csstools/css-parser-algorithms" "^2.3.1"
    "@csstools/css-tokenizer" "^2.2.0"
    "@csstools/media-query-list-parser" "^2.1.4"
    "@csstools/selector-specificity" "^3.0.0"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^8.2.0"
    css-functions-list "^3.2.1"
    css-tree "^2.3.1"
    debug "^4.3.4"
    fast-glob "^3.3.1"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^7.0.0"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.3.1"
    ignore "^5.2.4"
    import-lazy "^4.0.0"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.29.0"
    mathml-tag-names "^2.1.3"
    meow "^10.1.5"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    picocolors "^1.0.0"
    postcss "^8.4.28"
    postcss-resolve-nested-selector "^0.1.1"
    postcss-safe-parser "^6.0.0"
    postcss-selector-parser "^6.0.13"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    style-search "^0.1.0"
    supports-hyperlinks "^3.0.0"
    svg-tags "^1.0.0"
    table "^6.8.1"
    write-file-atomic "^5.0.1"

suit-cim@^1.0.27, suit-cim@^1.0.36:
  version "1.0.36"
  resolved "https://tech.suitbim.com/verdaccio/suit-cim/-/suit-cim-1.0.36.tgz#8adf1213d0ab545527e761b512ecdfc62c96024e"
  integrity sha512-7oIOOd1EyTyFxk0rpMFY3AbMWnDYOaVWUY5mwp6zJ9HghRd3CayYXPIUY8N3IEAZ8Hhsv7NNcm9WWQBcCDZ5wQ==
  dependencies:
    "@turf/turf" "^6.5.0"
    "@tweenjs/tween.js" "^18.6.4"
    "@zip.js/zip.js" "2.4.x"
    autolinker "^3.14.3"
    bitmap-sdf "^1.0.3"
    dompurify "^2.2.2"
    earcut "^2.2.4"
    grapheme-splitter "^1.0.4"
    jsep "^0.3.1"
    kdbush "^3.0.0"
    ktx-parse "^0.4.5"
    lerc "^2.0.0"
    lint-staged "^13.2.2"
    mersenne-twister "^1.1.0"
    meshoptimizer "^0.18.1"
    nosleep.js "^0.12.0"
    pako "^2.0.4"
    protobufjs "^7.1.0"
    rbush "^3.0.1"
    topojson-client "^3.1.0"
    urijs "^1.19.7"

suit-datav@^2.0.3:
  version "2.0.8"
  resolved "https://tech.suitbim.com/verdaccio/suit-datav/-/suit-datav-2.0.8.tgz#58bf1d3d93e1f8d9c73a6dc8d9cb28934b262caa"
  integrity sha512-hJR2YoXLGgC5zIgRPU5IyrgI6PgGhpTkfMDOnYWcdw8yocu+yzu83Wa8jgvaOlJ5pv0cDsUJK380WYoN1o4Vmg==
  dependencies:
    "@babel/plugin-proposal-decorators" "^7.17.9"
    "@babel/preset-typescript" "^7.16.7"
    "@jiaminghi/c-render" "^0.4.3"
    color "^4.2.0"
    qweather-icons "^1.1.1"
    resize-observer-polyfill "^1.5.1"
    rollup-plugin-typescript2 "^0.31.2"
    suit-cim "^1.0.36"
    vue "^2.0.0 || >=3.0.0-rc.0"
    vue-demi "^0.12.5"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://tech.suitbim.com/verdaccio/supports-color/-/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://tech.suitbim.com/verdaccio/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^3.0.0:
  version "3.0.0"
  resolved "https://tech.suitbim.com/verdaccio/supports-hyperlinks/-/supports-hyperlinks-3.0.0.tgz#c711352a5c89070779b4dad54c05a2f14b15c94b"
  integrity sha512-QBDPHyPQDRTy9ku4URNGY5Lah8PAaXs6tAAwp55sL5WCsSW7GIfdf6W5ixfziW+t7wh3GVvHyHHyQ1ESsoRvaA==
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://tech.suitbim.com/verdaccio/svg-tags/-/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

synckit@^0.8.5:
  version "0.8.8"
  resolved "https://tech.suitbim.com/verdaccio/synckit/-/synckit-0.8.8.tgz#fe7fe446518e3d3d49f5e429f443cf08b6edfcd7"
  integrity sha512-HwOKAP7Wc5aRGYdKH+dw0PRRpbO841v2DENBtjnR5HFWoiNByAl7vrx3p0G/rCyYXQsrxqtX48TImFtPcIHSpQ==
  dependencies:
    "@pkgr/core" "^0.1.0"
    tslib "^2.6.2"

table@^6.8.1:
  version "6.8.2"
  resolved "https://tech.suitbim.com/verdaccio/table/-/table-6.8.2.tgz#c5504ccf201213fa227248bdc8c5569716ac6c58"
  integrity sha512-w2sfv80nrAh2VCbqR5AK27wswXhqcck2AhfnNW76beQXskGZ1V12GwS//yYVa3d3fcvAip2OUnbDAjW2k3v9fA==
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://tech.suitbim.com/verdaccio/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/throttle-debounce/-/throttle-debounce-3.0.1.tgz#32f94d84dfa894f786c9a1f290e7a645b6a19abb"
  integrity sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==

tinyqueue@^2.0.3:
  version "2.0.3"
  resolved "https://tech.suitbim.com/verdaccio/tinyqueue/-/tinyqueue-2.0.3.tgz#64d8492ebf39e7801d7bd34062e29b45b2035f08"
  integrity sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://tech.suitbim.com/verdaccio/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

topojson-client@3.x, topojson-client@^3.1.0:
  version "3.1.0"
  resolved "https://tech.suitbim.com/verdaccio/topojson-client/-/topojson-client-3.1.0.tgz#22e8b1ed08a2b922feeb4af6f53b6ef09a467b99"
  integrity sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==
  dependencies:
    commander "2"

topojson-server@3.x:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/topojson-server/-/topojson-server-3.0.1.tgz#d2b3ec095b6732299be76a48406111b3201a34f5"
  integrity sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==
  dependencies:
    commander "2"

totalist@^3.0.0:
  version "3.0.1"
  resolved "https://tech.suitbim.com/verdaccio/totalist/-/totalist-3.0.1.tgz#ba3a3d600c915b1a97872348f79c127475f6acf8"
  integrity sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==

trim-newlines@^4.0.2:
  version "4.1.1"
  resolved "https://tech.suitbim.com/verdaccio/trim-newlines/-/trim-newlines-4.1.1.tgz#28c88deb50ed10c7ba6dc2474421904a00139125"
  integrity sha512-jRKj0n0jXWo6kh62nA5TEh3+4igKDXLvzBJcPpiizP7oOolUrYIxmVBG9TOtHYFHoddUk6YvAkGeGoSVTXfQXQ==

ts-api-utils@^1.0.1:
  version "1.3.0"
  resolved "https://tech.suitbim.com/verdaccio/ts-api-utils/-/ts-api-utils-1.3.0.tgz#4b490e27129f1e8e686b45cc4ab63714dc60eea1"
  integrity sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==

tslib@2.3.0:
  version "2.3.0"
  resolved "https://tech.suitbim.com/verdaccio/tslib/-/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==

tslib@^2, tslib@^2.3.0, tslib@^2.3.1, tslib@^2.6.2:
  version "2.6.2"
  resolved "https://tech.suitbim.com/verdaccio/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

turf-jsts@*:
  version "1.2.3"
  resolved "https://tech.suitbim.com/verdaccio/turf-jsts/-/turf-jsts-1.2.3.tgz#59757f542afbff9a577bbf411f183b8f48d38aa4"
  integrity sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA==

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://tech.suitbim.com/verdaccio/type-check/-/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://tech.suitbim.com/verdaccio/type-fest/-/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^1.0.1, type-fest@^1.0.2, type-fest@^1.2.1, type-fest@^1.2.2:
  version "1.4.0"
  resolved "https://tech.suitbim.com/verdaccio/type-fest/-/type-fest-1.4.0.tgz#e9fb813fe3bf1744ec359d55d1affefa76f14be1"
  integrity sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://tech.suitbim.com/verdaccio/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

ufo@^1.5.3:
  version "1.5.3"
  resolved "https://tech.suitbim.com/verdaccio/ufo/-/ufo-1.5.3.tgz#3325bd3c977b6c6cd3160bf4ff52989adc9d3344"
  integrity sha512-Y7HYmWaFwPUmkoQCUIAYpKqkOf+SbVj/2fJJZ4RJMCfZp0rTGwRbzQD+HghfnhKOjL9E01okqz+ncJskGYfBNw==

unconfig@^0.3.10:
  version "0.3.13"
  resolved "https://tech.suitbim.com/verdaccio/unconfig/-/unconfig-0.3.13.tgz#8612d57811c1316f30d95f45bb96ce8ce8afc10c"
  integrity sha512-N9Ph5NC4+sqtcOjPfHrRcHekBCadCXWTBzp2VYYbySOHW0PfD9XLCeXshTXjkPYwLrBr9AtSeU0CZmkYECJhng==
  dependencies:
    "@antfu/utils" "^0.7.7"
    defu "^6.1.4"
    jiti "^1.21.0"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://tech.suitbim.com/verdaccio/undici-types/-/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unimport@^3.7.1:
  version "3.7.1"
  resolved "https://tech.suitbim.com/verdaccio/unimport/-/unimport-3.7.1.tgz#37250d0f3f2dcf1e1b66ed13728db0e9f50ba0c3"
  integrity sha512-V9HpXYfsZye5bPPYUgs0Otn3ODS1mDUciaBlXljI4C2fTwfFpvFZRywmlOu943puN9sncxROMZhsZCjNXEpzEQ==
  dependencies:
    "@rollup/pluginutils" "^5.1.0"
    acorn "^8.11.2"
    escape-string-regexp "^5.0.0"
    estree-walker "^3.0.3"
    fast-glob "^3.3.2"
    local-pkg "^0.5.0"
    magic-string "^0.30.5"
    mlly "^1.4.2"
    pathe "^1.1.1"
    pkg-types "^1.0.3"
    scule "^1.1.1"
    strip-literal "^1.3.0"
    unplugin "^1.5.1"

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://tech.suitbim.com/verdaccio/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unocss@^0.55.7:
  version "0.55.7"
  resolved "https://tech.suitbim.com/verdaccio/unocss/-/unocss-0.55.7.tgz#79e1284c0a3f6c262aa78670cfac0ae344387830"
  integrity sha512-3W9P7vj2EhSk/4oPCHBS0VgrwSf5zZL6Az1/XARVOpBnRJtCM2szFInYxHkMgt9pkZTsW8SFCuk/g+QIJ6A8tg==
  dependencies:
    "@unocss/astro" "0.55.7"
    "@unocss/cli" "0.55.7"
    "@unocss/core" "0.55.7"
    "@unocss/extractor-arbitrary-variants" "0.55.7"
    "@unocss/postcss" "0.55.7"
    "@unocss/preset-attributify" "0.55.7"
    "@unocss/preset-icons" "0.55.7"
    "@unocss/preset-mini" "0.55.7"
    "@unocss/preset-tagify" "0.55.7"
    "@unocss/preset-typography" "0.55.7"
    "@unocss/preset-uno" "0.55.7"
    "@unocss/preset-web-fonts" "0.55.7"
    "@unocss/preset-wind" "0.55.7"
    "@unocss/reset" "0.55.7"
    "@unocss/transformer-attributify-jsx" "0.55.7"
    "@unocss/transformer-attributify-jsx-babel" "0.55.7"
    "@unocss/transformer-compile-class" "0.55.7"
    "@unocss/transformer-directives" "0.55.7"
    "@unocss/transformer-variant-group" "0.55.7"
    "@unocss/vite" "0.55.7"

unplugin-auto-import@^0.17.5:
  version "0.17.5"
  resolved "https://tech.suitbim.com/verdaccio/unplugin-auto-import/-/unplugin-auto-import-0.17.5.tgz#5ae7164fddd1560e96bf9be23b2f5ef46c808ca6"
  integrity sha512-fHNDkDSxv3PGagX1wmKBYBkgaM4AKAgZmdJw/bxjhNljx9KSXSgHpGfX0MwUrq9qw6q1bhHIZVWyOwoY2koo4w==
  dependencies:
    "@antfu/utils" "^0.7.7"
    "@rollup/pluginutils" "^5.1.0"
    fast-glob "^3.3.2"
    local-pkg "^0.5.0"
    magic-string "^0.30.5"
    minimatch "^9.0.3"
    unimport "^3.7.1"
    unplugin "^1.6.0"

unplugin@^1.5.1, unplugin@^1.6.0:
  version "1.10.1"
  resolved "https://tech.suitbim.com/verdaccio/unplugin/-/unplugin-1.10.1.tgz#8ceda065dc71bc67d923dea0920f05c67f2cd68c"
  integrity sha512-d6Mhq8RJeGA8UfKCu54Um4lFA0eSaRa3XxdAJg8tIdxbu1ubW0hBCZUL7yI2uGyYCRndvbK8FLHzqy2XKfeMsg==
  dependencies:
    acorn "^8.11.3"
    chokidar "^3.6.0"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.6.1"

upath2@^3.1.13:
  version "3.1.19"
  resolved "https://tech.suitbim.com/verdaccio/upath2/-/upath2-3.1.19.tgz#d987d34a62b2daad1c54a692fd5a720a30c9a786"
  integrity sha512-d23dQLi8nDWSRTIQwXtaYqMrHuca0As53fNiTLLFDmsGBbepsZepISaB2H1x45bDFN/n3Qw9bydvyZEacTrEWQ==
  dependencies:
    "@types/node" "*"
    path-is-network-drive "^1.0.20"
    path-strip-sep "^1.0.17"
    tslib "^2"

update-browserslist-db@^1.0.13:
  version "1.0.15"
  resolved "https://tech.suitbim.com/verdaccio/update-browserslist-db/-/update-browserslist-db-1.0.15.tgz#60ed9f8cba4a728b7ecf7356f641a31e3a691d97"
  integrity sha512-K9HWH62x3/EalU1U6sjSZiylm9C8tgq2mSvshZpqc7QE69RaA2qjhkW2HlNA0tFpEbtyFz7HTqbSdN4MSwUodA==
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.0"

uri-js@^4.2.2, uri-js@^4.4.1:
  version "4.4.1"
  resolved "https://tech.suitbim.com/verdaccio/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.7:
  version "1.19.11"
  resolved "https://tech.suitbim.com/verdaccio/urijs/-/urijs-1.19.11.tgz#204b0d6b605ae80bea54bea39280cdb7c9f923cc"
  integrity sha512-HXgFDgDommxn5/bIv0cnQZsPhHDA90NPHD6+c/v21U5+Sx5hoP8+dP9IZXBU1gIfvdRfhG8cel9QNPeionfcCQ==

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://tech.suitbim.com/verdaccio/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vite-code-inspector-plugin@0.9.3:
  version "0.9.3"
  resolved "https://tech.suitbim.com/verdaccio/vite-code-inspector-plugin/-/vite-code-inspector-plugin-0.9.3.tgz#b552291122727417a8112debf3671fa6ee7bf3a2"
  integrity sha512-UDBVb874pXxzc+VYlN8RC8Nr+T550I//rQFkYSASKTMfVU8H0aI4mXeTZGdna8KnXAHfNCqfeOE/zWOZFR7QMA==
  dependencies:
    code-inspector-core "0.9.3"

vite-plugin-eslint@^1.8.1:
  version "1.8.1"
  resolved "https://tech.suitbim.com/verdaccio/vite-plugin-eslint/-/vite-plugin-eslint-1.8.1.tgz#0381b8272e7f0fd8b663311b64f7608d55d8b04c"
  integrity sha512-PqdMf3Y2fLO9FsNPmMX+//2BF5SF8nEWspZdgl4kSt7UvHDRHVVfHvxsD7ULYzZrJDGRxR81Nq7TOFgwMnUang==
  dependencies:
    "@rollup/pluginutils" "^4.2.1"
    "@types/eslint" "^8.4.5"
    rollup "^2.77.2"

vite-plugin-externals@^0.6.2:
  version "0.6.2"
  resolved "https://tech.suitbim.com/verdaccio/vite-plugin-externals/-/vite-plugin-externals-0.6.2.tgz#b2201588ff4156696f7422bf649ec630bb6eb10b"
  integrity sha512-R5oVY8xDJjLXLTs2XDYzvYbc/RTZuIwOx2xcFbYf+/VXB6eJuatDgt8jzQ7kZ+IrgwQhe6tU8U2fTyy72C25CQ==
  dependencies:
    acorn "^8.4.0"
    es-module-lexer "^0.4.1"
    fs-extra "^10.0.0"
    magic-string "^0.25.7"

vite-plugin-html-config@^1.0.11:
  version "1.0.11"
  resolved "https://tech.suitbim.com/verdaccio/vite-plugin-html-config/-/vite-plugin-html-config-1.0.11.tgz#7018fb41d17ced527d79f76361e9a9cf587f80bc"
  integrity sha512-hUybhgI+/LQQ5q6xoMMsTvI4PBuQD/Wv6Z1vtDPVWjanS8weCIexXuLLYNGD/93f0v8W2hpNfXpmxgpZMahJ0g==

vite-plugin-resolve-externals@^0.2.2:
  version "0.2.2"
  resolved "https://tech.suitbim.com/verdaccio/vite-plugin-resolve-externals/-/vite-plugin-resolve-externals-0.2.2.tgz#5fadfa702b14044eddf464b5e7253a4189b37103"
  integrity sha512-RHwCA5z+oGwxlqVSJq43qjphpImq8N44NwqjUVqK3aH13R1C4JJM7ApRtOmkfU2x/paN7kdSwf252VVfKEVsQw==

vite@^4.3.9:
  version "4.5.3"
  resolved "https://tech.suitbim.com/verdaccio/vite/-/vite-4.5.3.tgz#d88a4529ea58bae97294c7e2e6f0eab39a50fb1a"
  integrity sha512-kQL23kMeX92v3ph7IauVkXkikdDRsYMGTVl5KY2E9OY4ONLvkHf04MDTbnfo6NKxZiDLWzVpP5oTa8hQD8U3dg==
  dependencies:
    esbuild "^0.18.10"
    postcss "^8.4.27"
    rollup "^3.27.1"
  optionalDependencies:
    fsevents "~2.3.2"

vue-demi@*, vue-demi@>=0.14.5, vue-demi@>=0.14.7:
  version "0.14.7"
  resolved "https://tech.suitbim.com/verdaccio/vue-demi/-/vue-demi-0.14.7.tgz#8317536b3ef74c5b09f268f7782e70194567d8f2"
  integrity sha512-EOG8KXDQNwkJILkx/gPcoL/7vH+hORoBaKgGe+6W7VFMvCYJfmF2dGbvgDroVnI8LU7/kTu8mbjRZGBU1z9NTA==

vue-demi@^0.12.5:
  version "0.12.5"
  resolved "https://tech.suitbim.com/verdaccio/vue-demi/-/vue-demi-0.12.5.tgz#8eeed566a7d86eb090209a11723f887d28aeb2d1"
  integrity sha512-BREuTgTYlUr0zw0EZn3hnhC3I6gPWv+Kwh4MCih6QcAeaTlaIX0DwOVN0wHej7hSvDPecz4jygy/idsgKfW58Q==

vue-eslint-parser@^9.4.2:
  version "9.4.2"
  resolved "https://tech.suitbim.com/verdaccio/vue-eslint-parser/-/vue-eslint-parser-9.4.2.tgz#02ffcce82042b082292f2d1672514615f0d95b6d"
  integrity sha512-Ry9oiGmCAK91HrKMtCrKFWmSFWvYkpGglCeFAIqDdr9zdXmMMpJOmUJS7WWsW7fX81h6mwHmUZCQQ1E0PkSwYQ==
  dependencies:
    debug "^4.3.4"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.6"

vue-router@^4.2.2:
  version "4.3.2"
  resolved "https://tech.suitbim.com/verdaccio/vue-router/-/vue-router-4.3.2.tgz#08096c7765dacc6832f58e35f7a081a8b34116a7"
  integrity sha512-hKQJ1vDAZ5LVkKEnHhmm1f9pMiWIBNGF5AwU67PdH7TyXCj/a4hTccuUuYCAMgJK6rO/NVYtQIEN3yL8CECa7Q==
  dependencies:
    "@vue/devtools-api" "^6.5.1"

vue3-video-play@1.3.1-beta.6:
  version "1.3.1-beta.6"
  resolved "https://tech.suitbim.com/verdaccio/vue3-video-play/-/vue3-video-play-1.3.1-beta.6.tgz#bca3f55a11053eaa37053835e4610c04d9cc509e"
  integrity sha512-Olrx2/LNAds7fuor/yX9ZKT9sOcwcfTt2g2YbbCrEaAmZ5Tb0hwBr5z+/CoLwELzzRzXCHPmWWoT0Wm5W/Nwpw==
  dependencies:
    hls.js "^1.0.10"
    throttle-debounce "^3.0.1"
    vue "^3.2.2"

"vue@^2.0.0 || >=3.0.0-rc.0", vue@^3.2.2, vue@^3.4.14:
  version "3.4.27"
  resolved "https://tech.suitbim.com/verdaccio/vue/-/vue-3.4.27.tgz#40b7d929d3e53f427f7f5945386234d2854cc2a1"
  integrity sha512-8s/56uK6r01r1icG/aEOHqyMVxd1bkYcSe9j8HcKtr/xTOFWvnzIVTehNW+5Yt89f+DLBe4A569pnZLS5HzAMA==
  dependencies:
    "@vue/compiler-dom" "3.4.27"
    "@vue/compiler-sfc" "3.4.27"
    "@vue/runtime-dom" "3.4.27"
    "@vue/server-renderer" "3.4.27"
    "@vue/shared" "3.4.27"

webpack-code-inspector-plugin@0.9.3:
  version "0.9.3"
  resolved "https://tech.suitbim.com/verdaccio/webpack-code-inspector-plugin/-/webpack-code-inspector-plugin-0.9.3.tgz#7de4a27e8135850ebe80c3fc8e4eea1394183243"
  integrity sha512-AyJPdrhLeAtbPDRCG7Aw7HUI970p3vMENfq7JbGkBFwGL2ITQ72Mgxrbr2eaYUCLazsTkQO/mzxjXq69469PvQ==
  dependencies:
    code-inspector-core "0.9.3"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://tech.suitbim.com/verdaccio/webpack-sources/-/webpack-sources-3.2.3.tgz#2d4daab8451fd4b240cc27055ff6a0c2ccea0cde"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-virtual-modules@^0.6.1:
  version "0.6.1"
  resolved "https://tech.suitbim.com/verdaccio/webpack-virtual-modules/-/webpack-virtual-modules-0.6.1.tgz#ac6fdb9c5adb8caecd82ec241c9631b7a3681b6f"
  integrity sha512-poXpCylU7ExuvZK8z+On3kX+S8o/2dQ/SVYueKA0D4WEMXROXgY8Ez50/bQEUmvoSMMrWcrJqCHuhAbsiwg7Dg==

which@^1.3.1:
  version "1.3.1"
  resolved "https://tech.suitbim.com/verdaccio/which/-/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://tech.suitbim.com/verdaccio/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://tech.suitbim.com/verdaccio/word-wrap/-/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

wrap-ansi@^8.0.1, wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://tech.suitbim.com/verdaccio/wrap-ansi/-/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://tech.suitbim.com/verdaccio/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^5.0.1:
  version "5.0.1"
  resolved "https://tech.suitbim.com/verdaccio/write-file-atomic/-/write-file-atomic-5.0.1.tgz#68df4717c55c6fa4281a7860b4c2ba0a6d2b11e7"
  integrity sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^7.5.5:
  version "7.5.9"
  resolved "https://tech.suitbim.com/verdaccio/ws/-/ws-7.5.9.tgz#54fa7db29f4c7cec68b1ddd3a89de099942bb591"
  integrity sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==

xml-name-validator@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/xml-name-validator/-/xml-name-validator-4.0.0.tgz#79a006e2e63149a8600f15430f0a4725d1524835"
  integrity sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://tech.suitbim.com/verdaccio/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://tech.suitbim.com/verdaccio/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://tech.suitbim.com/verdaccio/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@2.3.1:
  version "2.3.1"
  resolved "https://tech.suitbim.com/verdaccio/yaml/-/yaml-2.3.1.tgz#02fe0975d23cd441242aa7204e09fc28ac2ac33b"
  integrity sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==

yargs-parser@^20.2.9:
  version "20.2.9"
  resolved "https://tech.suitbim.com/verdaccio/yargs-parser/-/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://tech.suitbim.com/verdaccio/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

zrender@5.5.0:
  version "5.5.0"
  resolved "https://tech.suitbim.com/verdaccio/zrender/-/zrender-5.5.0.tgz#54d0d6c4eda81a96d9f60a9cd74dc48ea026bc1e"
  integrity sha512-O3MilSi/9mwoovx77m6ROZM7sXShR/O/JIanvzTwjN3FORfLSr81PsUGd7jlaYOeds9d8tw82oP44+3YucVo+w==
  dependencies:
    tslib "2.3.0"
