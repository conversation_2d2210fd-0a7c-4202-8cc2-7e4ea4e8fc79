<template>
  <div class="device-life-container">
    <div class="subtitle-container">
      <div v-show-ai-question @click="handleTitleClick">设备寿命</div>

      <SelectList class="btn-wrap" :optionList="optionList" v-model:selectValue="deviceSystemId" :clearable="false" filterable></SelectList>
    </div>
    <el-row>
      <el-col span="24" class="relative">
        <MyChart width="600px" height="240px" :option="options" style="position: sticky; z-index: 3; margin-top: 10px"></MyChart>
        <div class="barBg"></div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts'

  import SelectList from '@/components/SelectList/index.vue'
  import SwitchButton from '@/components/SwitchButton/index.vue'
  import MyChart from '@Common/components/MyChart/index.vue'

  import lib from '@/utils/lib'
  import vShowAiQuestion from '@/directives/showAiQuestion.ts'

  const showAIQuestion = computed(() => {
    return lib.store().storeScreenData.showAIQuestion
  })
  const handleTitleClick = () => {
    if (showAIQuestion.value) {
      lib.utils.sendQuestionToAIChat('设备寿命')
    }
  }
  const optionList = ref([])
  const deviceSystemId = ref(null)
  const options = reactive({
    grid: {
      top: '5%',
      left: '35%',
      right: '5%',
      bottom: '20%',
      width: '45%'
    },
    legend: {
      align: 'auto',
      data: [
        {
          name: '实际使用寿命',
          icon: 'rect',
          itemStyle: {
            color: '#4ebdff'
          }
        },
        {
          name: '设计寿命',
          icon: 'rect',
          itemStyle: {
            color: '#28f2f2'
          }
        }
      ],
      zlevel: 3,
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontFamily: 'Alibaba PuHuiTi'
      },
      bottom: '-2',
      selectedMode: false // 取消默认选中
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#fff',
          fontSize: 16,
          fontFamily: 'Alibaba PuHuiTi'
        }
      }
    },
    yAxis: [
      {
        type: 'category',
        // data: ['XX品牌XX型号', 'XX品牌XX型号', 'XX品牌XX型号', 'XX品牌XX型号', 'XX品牌XX型号']
        data: [],
        axisLabel: {
          textStyle: {
            color: '#ffff',
            fontSize: 18,
            fontFamily: 'Alibaba PuHuiTi'
          },
          // 超过 6 个字换行
          formatter: function (value) {
            const text = String(value)
            if (text.length > 6) {
              return `${text.slice(0, 6)}\n${text.slice(6)}`
            }
            return text
          }
        }
      }
    ],
    dataZoom: {
      type: 'slider',
      yAxisIndex: [0],
      startValue: 0,
      endValue: 1
    },
    series: [
      {
        name: '实际使用寿命',
        type: 'bar',
        barWidth: 30,
        zlevel: 1,
        itemStyle: {
          normal: {
            color: function (params) {
              return {
                type: 'linear',
                // 修改渐变方向为从上到下
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#4ebdff' // 最上边
                  },
                  {
                    offset: 0.5,
                    color: '#0369a8' // 上边的下边颜色
                  },
                  {
                    offset: 0.5,
                    color: '#2590d4' // 下边的上边颜色
                  },
                  {
                    offset: 1,
                    color: '#259cf4'
                  }
                ]
              }
            }
          }
        },
        label: {
          show: false
        },
        data: []
      },
      {
        zlevel: 2, // 确保 pictorialBar 在柱状图之上
        type: 'pictorialBar',
        symbolPosition: 'end',
        // 关联对应的柱状图系列索引
        barGap: '-100%',
        data: [],
        symbol: 'diamond', // 菱形
        symbolRotate: 90,
        symbolOffset: [15, -18],
        symbolSize: [30, 15],
        itemStyle: {
          normal: {
            borderWidth: 0,
            color: function (params) {
              return '#4ebdff'
            }
          }
        }
      },
      {
        name: '设计寿命',
        type: 'bar',
        barWidth: 30,
        zlevel: 1,
        itemStyle: {
          normal: {
            color: function (params) {
              return {
                type: 'linear',
                // 修改渐变方向为从上到下
                x: 0,
                x2: 0,
                y: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#28f2f2' // 最上边
                  },
                  {
                    offset: 0.5,
                    color: '#0496a5' // 上边的下边颜色
                  },
                  {
                    offset: 0.5,
                    color: '#1aadb4' // 下边的上边颜色
                  },
                  {
                    offset: 1,
                    color: '#1ecfd3'
                  }
                ]
              }
            }
          }
        },
        label: {
          show: false
        },
        data: []
      },
      {
        zlevel: 2, // 确保 pictorialBar 在柱状图之上
        type: 'pictorialBar',
        symbolPosition: 'end',
        // 关联对应的柱状图系列索引
        barGap: '-100%',
        data: [],
        symbol: 'diamond', // 菱形
        symbolRotate: 90,
        symbolOffset: [15, 18],
        symbolSize: [30, 15],
        itemStyle: {
          normal: {
            borderWidth: 0,
            color: function (params) {
              return '#28f2f2'
            }
          }
        }
      }
      // {
      //   name: '实际使用寿命',
      //   type: 'bar',
      //   z: 2,
      //   zlevel: 1,
      //   barWidth: 16,
      //   color: '#23CFFB',
      //   data: []
      // },
      // {
      //   name: '设计寿命',
      //   type: 'bar',
      //   // barGap: -1.2,
      //   barWidth: 16,
      //   color: '#3CE399',
      //   data: []
      // }
    ]
  })

  const deviceLifeStatisticsApi = () => {
    const params = {
      deviceSystemId: deviceSystemId.value
    }
    lib.api.bigscreenApi.deviceLifeStatistics(params).then((res) => {
      if (res.success && res.result) {
        // 过滤掉没有品牌名和型号名的数据
        const resultData = res.result.filter((item) => item.brandName && item.moedel)
        options.dataZoom.show = resultData.length > 2 ? true : false
        // y轴展示品牌和型号
        options.yAxis[0].data = resultData.map((item) => {
          return item.brandName + item.moedel
        })
        // const colors1 = ['#FF5800', '#6101FF', '#00FF67', '#FFA500', '#0198FF']
        // const colors2 = ['#FFC1A0', '#D4C2FF', '#75FFAD', '#FFCD71', '#4DC2FF']
        // const colors1 = ['#23CFFB']
        // const colors2 = ['#3CE399']

        options.series[0].data = resultData.map((item, ind) => item.realLifeMonth)
        options.series[1].data = resultData.map((item, ind) => item.realLifeMonth)
        options.series[2].data = resultData.map((item, ind) => item.designLifespanMonth)
        options.series[3].data = resultData.map((item, ind) => item.designLifespanMonth)
        const tooltip = {
          trigger: 'axis',
          formatter: (params) => {
            if (params.length === 0) return ''
            const name = params[0].name
            let realLifeMonth = 0
            let designLifeMonth = 0

            params.forEach((param) => {
              if (param.seriesName === '实际使用寿命') {
                realLifeMonth = param.value
              } else if (param.seriesName === '设计寿命') {
                designLifeMonth = param.value
              }
            })
            const remainingLifeMonth = resultData.find((_) => name.includes(_.moedel)).remaingLifeMonth
            const brandName = resultData.find((_) => name.includes(_.moedel)).brandName
            return `
            <div>
              <div><strong>${brandName}</strong></div>
              <div>实际使用寿命: ${realLifeMonth || '-'} 个月</div>
              <div>设计寿命: ${designLifeMonth || '-'} 个月</div>
              <div>剩余寿命: ${remainingLifeMonth || '-'} 个月</div>
            </div>
          `
          }
        }
        options.tooltip = tooltip
      }
    })
  }
  const deviceList = async () => {
    await lib.api.bigscreenApi.deviceSystemNotEmptyList({}).then((res) => {
      if (res.success && res.result) {
        optionList.value = res.result.filter((item) => item.leaf)
        deviceSystemId.value = optionList.value[0]?.id
      }
    })
  }
  onMounted(async () => {
    await deviceList()
  })
  watch(
    () => deviceSystemId.value,
    (value) => {
      if (value) {
        deviceLifeStatisticsApi()
      }
    },
    {
      immediate: true
    }
  )
</script>

<style lang="scss" scoped>
  .device-life-container {
    position: relative;
    width: 100%;
    height: 270px;
    margin-top: 42px;

    // background:rgb(0 133 255 / 30%);
    .subtitle-container {
      width: 628px;
      height: 31px;
      padding-left: 26px;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      font-weight: 400;
      line-height: 14px;
      color: #ecf6ff;
      background: url('@/assets/subheadline2.png') no-repeat;
      .btn-wrap {
        position: absolute;
        top: -10px;
        right: 0;
      }
    }
    .legend-box {
      margin-top: 60px;
      font-family: 'Alibaba PuHuiTii';
      color: #ffffff;
      .legend-item {
        display: flex;
        align-items: center;
        .legend-item-icon {
          width: 26px;
          height: 16px;
          margin-right: 10px;
          background: #00a3ff;
          border: 2px solid #cccccc;
          border-radius: 5px;
        }
      }
    }
    .barBg {
      position: absolute;
      bottom: -28px;
      z-index: 1;
      width: 648px;
      height: 68px;
      pointer-events: none;
      background: url('@/assets/ScreenRight/Perception/Device/barbg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
