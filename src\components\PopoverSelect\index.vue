<template>
  <div class="popover-select-container">
    <el-popover placement="bottom" trigger="hover" width="50px" popper-class="popover-select-item" effect="customized">
      <template #reference>
        <slot></slot>
      </template>
      <div class="sub-list-container">
        <div class="sub-item" :class="{ selected: subItem.selected }" v-for="(subItem, subIndex) in list" :key="subIndex" @click="handleSubitemClick(subItem)">
          {{ subItem.name }}
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
  const props = defineProps({
    width: {
      type: String,
      default: '50px'
    }
  })

  /**
   * @description: 例子
  ```
      [
        { id: 1, name: 'aaa', selected: false },
        { id: 2, name: 'bbb', selected: false },
        { id: 3, name: 'ccc', selected: false }
      ]
   ```
   */
  const list = defineModel()

  const handleSubitemClick = (val) => {
    list.value.forEach((item) => {
      item.selected = item.id === val.id
    })
  }
</script>

<style lang="scss" scoped>
  .popover-select-container {
  }
</style>
<style>
  .el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 0;
    background: rgb(2 17 27 / 71%);
    border: 1px solid #0080b6;
  }
  .el-popper.is-customized .el-popper__arrow::before {
    right: 0;
    background: rgb(2 17 27 / 71%);
  }
  .popover-select-item {
    .sub-list-container {
      position: relative;
      width: 250px;
      height: 150px;
      overflow-y: scroll;
      scrollbar-width: none;
      .sub-item {
        box-sizing: border-box;
        width: 100%;
        height: 36px;
        padding: 10px 16px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-weight: 400;
        line-height: 36px;
        color: #ffffff;
        cursor: pointer;
        &.selected {
          font-size: 20px;
          color: #43ddff;
        }
      }
    }
  }
</style>
