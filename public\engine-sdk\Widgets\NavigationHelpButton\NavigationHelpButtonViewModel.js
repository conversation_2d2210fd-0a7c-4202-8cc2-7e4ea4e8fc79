import knockout from"../../ThirdParty/knockout.js";import createCommand from"../createCommand.js";function NavigationHelpButtonViewModel(){this.showInstructions=!1;var t=this;this._command=createCommand((function(){t.showInstructions=!t.showInstructions})),this._showClick=createCommand((function(){t._touch=!1})),this._showTouch=createCommand((function(){t._touch=!0})),this._touch=!1,this.tooltip="Navigation Instructions",knockout.track(this,["tooltip","showInstructions","_touch"])}Object.defineProperties(NavigationHelpButtonViewModel.prototype,{command:{get:function(){return this._command}},showClick:{get:function(){return this._showClick}},showTouch:{get:function(){return this._showTouch}}});export default NavigationHelpButtonViewModel;