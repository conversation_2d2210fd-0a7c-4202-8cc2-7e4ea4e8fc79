/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./IndexDatatype-00859b8b","./Math-b5f4d889","./VertexFormat-86c096b8","./WallGeometryLibrary-bb4c5687","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./arrayRemoveDuplicates-5b666c82","./PolylinePipeline-1a06b90f","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./IntersectionTests-25cff68e","./Plane-a268aa11"],(function(e,t,n,i,a,o,r,s,l,m,u,d,p,c,f,y,g,h,b,C,x){"use strict";const A=new i.Cartesian3,_=new i.Cartesian3,E=new i.Cartesian3,w=new i.Cartesian3,F=new i.Cartesian3,v=new i.Cartesian3,L=new i.Cartesian3;function k(n){const a=(n=e.defaultValue(n,e.defaultValue.EMPTY_OBJECT)).positions,o=n.maximumHeights,r=n.minimumHeights,s=e.defaultValue(n.vertexFormat,m.VertexFormat.DEFAULT),u=e.defaultValue(n.granularity,l.CesiumMath.RADIANS_PER_DEGREE),d=e.defaultValue(n.ellipsoid,t.Ellipsoid.WGS84);this._positions=a,this._minimumHeights=r,this._maximumHeights=o,this._vertexFormat=m.VertexFormat.clone(s),this._granularity=u,this._ellipsoid=t.Ellipsoid.clone(d),this._workerName="createWallGeometry";let p=1+a.length*i.Cartesian3.packedLength+2;e.defined(r)&&(p+=r.length),e.defined(o)&&(p+=o.length),this.packedLength=p+t.Ellipsoid.packedLength+m.VertexFormat.packedLength+1}k.pack=function(n,a,o){let r;o=e.defaultValue(o,0);const s=n._positions;let l=s.length;for(a[o++]=l,r=0;r<l;++r,o+=i.Cartesian3.packedLength)i.Cartesian3.pack(s[r],a,o);const u=n._minimumHeights;if(l=e.defined(u)?u.length:0,a[o++]=l,e.defined(u))for(r=0;r<l;++r)a[o++]=u[r];const d=n._maximumHeights;if(l=e.defined(d)?d.length:0,a[o++]=l,e.defined(d))for(r=0;r<l;++r)a[o++]=d[r];return t.Ellipsoid.pack(n._ellipsoid,a,o),o+=t.Ellipsoid.packedLength,m.VertexFormat.pack(n._vertexFormat,a,o),a[o+=m.VertexFormat.packedLength]=n._granularity,a};const H=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),V=new m.VertexFormat,G={positions:void 0,minimumHeights:void 0,maximumHeights:void 0,ellipsoid:H,vertexFormat:V,granularity:void 0};return k.unpack=function(n,a,o){let r;a=e.defaultValue(a,0);let s=n[a++];const l=new Array(s);for(r=0;r<s;++r,a+=i.Cartesian3.packedLength)l[r]=i.Cartesian3.unpack(n,a);let u,d;if(s=n[a++],s>0)for(u=new Array(s),r=0;r<s;++r)u[r]=n[a++];if(s=n[a++],s>0)for(d=new Array(s),r=0;r<s;++r)d[r]=n[a++];const p=t.Ellipsoid.unpack(n,a,H);a+=t.Ellipsoid.packedLength;const c=m.VertexFormat.unpack(n,a,V),f=n[a+=m.VertexFormat.packedLength];return e.defined(o)?(o._positions=l,o._minimumHeights=u,o._maximumHeights=d,o._ellipsoid=t.Ellipsoid.clone(p,o._ellipsoid),o._vertexFormat=m.VertexFormat.clone(c,o._vertexFormat),o._granularity=f,o):(G.positions=l,G.minimumHeights=u,G.maximumHeights=d,G.granularity=f,new k(G))},k.fromConstantHeights=function(t){const n=(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions;let i,a;const o=t.minimumHeight,r=t.maximumHeight,s=e.defined(o),l=e.defined(r);if(s||l){const e=n.length;i=s?new Array(e):void 0,a=l?new Array(e):void 0;for(let t=0;t<e;++t)s&&(i[t]=o),l&&(a[t]=r)}return new k({positions:n,maximumHeights:a,minimumHeights:i,ellipsoid:t.ellipsoid,vertexFormat:t.vertexFormat})},k.createGeometry=function(t){const m=t._positions,d=t._minimumHeights,p=t._maximumHeights,c=t._vertexFormat,f=t._granularity,y=t._ellipsoid,g=u.WallGeometryLibrary.computePositions(y,m,p,d,f,!0);if(!e.defined(g))return;const h=g.bottomPositions,b=g.topPositions,C=g.numCorners;let x=b.length,k=2*x;const H=c.position?new Float64Array(k):void 0,V=c.normal?new Float32Array(k):void 0,G=c.tangent?new Float32Array(k):void 0,D=c.bitangent?new Float32Array(k):void 0,P=c.st?new Float32Array(k/3*2):void 0;let T,z=0,R=0,O=0,S=0,I=0,N=L,M=v,W=F,B=!0;x/=3;let U=0;const q=1/(x-C-1);for(T=0;T<x;++T){const e=3*T,t=i.Cartesian3.fromArray(b,e,A),n=i.Cartesian3.fromArray(h,e,_);if(c.position&&(H[z++]=n.x,H[z++]=n.y,H[z++]=n.z,H[z++]=t.x,H[z++]=t.y,H[z++]=t.z),c.st&&(P[I++]=U,P[I++]=0,P[I++]=U,P[I++]=1),c.normal||c.tangent||c.bitangent){let n=i.Cartesian3.clone(i.Cartesian3.ZERO,w);const a=i.Cartesian3.subtract(t,y.geodeticSurfaceNormal(t,_),_);if(T+1<x&&(n=i.Cartesian3.fromArray(b,e+3,w)),B){const e=i.Cartesian3.subtract(n,t,E),o=i.Cartesian3.subtract(a,t,A);N=i.Cartesian3.normalize(i.Cartesian3.cross(o,e,N),N),B=!1}i.Cartesian3.equalsEpsilon(t,n,l.CesiumMath.EPSILON10)?B=!0:(U+=q,c.tangent&&(M=i.Cartesian3.normalize(i.Cartesian3.subtract(n,t,M),M)),c.bitangent&&(W=i.Cartesian3.normalize(i.Cartesian3.cross(N,M,W),W))),c.normal&&(V[R++]=N.x,V[R++]=N.y,V[R++]=N.z,V[R++]=N.x,V[R++]=N.y,V[R++]=N.z),c.tangent&&(G[S++]=M.x,G[S++]=M.y,G[S++]=M.z,G[S++]=M.x,G[S++]=M.y,G[S++]=M.z),c.bitangent&&(D[O++]=W.x,D[O++]=W.y,D[O++]=W.z,D[O++]=W.x,D[O++]=W.y,D[O++]=W.z)}}const J=new r.GeometryAttributes;c.position&&(J.position=new o.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:H})),c.normal&&(J.normal=new o.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:V})),c.tangent&&(J.tangent=new o.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G})),c.bitangent&&(J.bitangent=new o.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:D})),c.st&&(J.st=new o.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:P}));const Y=k/3;k-=6*(C+1);const Z=s.IndexDatatype.createTypedArray(Y,k);let j=0;for(T=0;T<Y-2;T+=2){const e=T,t=T+2,n=i.Cartesian3.fromArray(H,3*e,A),a=i.Cartesian3.fromArray(H,3*t,_);if(i.Cartesian3.equalsEpsilon(n,a,l.CesiumMath.EPSILON10))continue;const o=T+1,r=T+3;Z[j++]=o,Z[j++]=e,Z[j++]=r,Z[j++]=r,Z[j++]=e,Z[j++]=t}return new o.Geometry({attributes:J,indices:Z,primitiveType:o.PrimitiveType.TRIANGLES,boundingSphere:new n.BoundingSphere.fromVertices(H)})},function(n,i){return e.defined(i)&&(n=k.unpack(n,i)),n._ellipsoid=t.Ellipsoid.clone(n._ellipsoid),k.createGeometry(n)}}));
