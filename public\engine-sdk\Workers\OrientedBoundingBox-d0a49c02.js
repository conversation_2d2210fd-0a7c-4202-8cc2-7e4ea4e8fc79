define(["exports","./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./Plane-1b1689fd","./EllipsoidTangentPlane-265ae537"],(function(a,e,t,n,r,i,s,o,C,u){"use strict";function c(a,t){this.center=n.Cartesian3.clone(e.defaultValue(a,n.Cartesian3.ZERO)),this.halfAxes=o.Matrix3.clone(e.defaultValue(t,o.Matrix3.ZERO))}c.packedLength=n.Cartesian3.packedLength+o.Matrix3.packedLength,c.pack=function(a,t,r){return r=e.defaultValue(r,0),n.Cartesian3.pack(a.center,t,r),o.Matrix3.pack(a.halfAxes,t,r+n.Cartesian3.packedLength),t},c.unpack=function(a,t,r){return t=e.defaultValue(t,0),e.defined(r)||(r=new c),n.Cartesian3.unpack(a,t,r.center),o.Matrix3.unpack(a,t+n.Cartesian3.packedLength,r.halfAxes),r};var d=new n.Cartesian3,l=new n.Cartesian3,h=new n.Cartesian3,m=new n.Cartesian3,f=new n.Cartesian3,x=new n.Cartesian3,M=new o.Matrix3,p={unitary:new o.Matrix3,diagonal:new o.Matrix3};c.fromPoints=function(a,t){if(e.defined(t)||(t=new c),!e.defined(a)||0===a.length)return t.halfAxes=o.Matrix3.ZERO,t.center=n.Cartesian3.ZERO,t;var r,i=a.length,s=n.Cartesian3.clone(a[0],d);for(r=1;r<i;r++)n.Cartesian3.add(s,a[r],s);var C=1/i;n.Cartesian3.multiplyByScalar(s,C,s);var u,w=0,g=0,b=0,y=0,O=0,v=0;for(r=0;r<i;r++)u=n.Cartesian3.subtract(a[r],s,l),w+=u.x*u.x,g+=u.x*u.y,b+=u.x*u.z,y+=u.y*u.y,O+=u.y*u.z,v+=u.z*u.z;w*=C,g*=C,b*=C,y*=C,O*=C,v*=C;var P=M;P[0]=w,P[1]=g,P[2]=b,P[3]=g,P[4]=y,P[5]=O,P[6]=b,P[7]=O,P[8]=v;var N=o.Matrix3.computeEigenDecomposition(P,p),A=o.Matrix3.clone(N.unitary,t.halfAxes),E=o.Matrix3.getColumn(A,0,m),R=o.Matrix3.getColumn(A,1,f),T=o.Matrix3.getColumn(A,2,x),I=-Number.MAX_VALUE,L=-Number.MAX_VALUE,S=-Number.MAX_VALUE,z=Number.MAX_VALUE,B=Number.MAX_VALUE,U=Number.MAX_VALUE;for(r=0;r<i;r++)u=a[r],I=Math.max(n.Cartesian3.dot(E,u),I),L=Math.max(n.Cartesian3.dot(R,u),L),S=Math.max(n.Cartesian3.dot(T,u),S),z=Math.min(n.Cartesian3.dot(E,u),z),B=Math.min(n.Cartesian3.dot(R,u),B),U=Math.min(n.Cartesian3.dot(T,u),U);E=n.Cartesian3.multiplyByScalar(E,.5*(z+I),E),R=n.Cartesian3.multiplyByScalar(R,.5*(B+L),R),T=n.Cartesian3.multiplyByScalar(T,.5*(U+S),T);var V=n.Cartesian3.add(E,R,t.center);n.Cartesian3.add(V,T,V);var D=h;return D.x=I-z,D.y=L-B,D.z=S-U,n.Cartesian3.multiplyByScalar(D,.5,D),o.Matrix3.multiplyByScale(t.halfAxes,D,t.halfAxes),t};var w=new n.Cartesian3,g=new n.Cartesian3;function b(a,r,i,s,C,u,d,l,h,m,f){if(!e.defined(C)||!e.defined(u)||!e.defined(d)||!e.defined(l)||!e.defined(h)||!e.defined(m))throw new t.DeveloperError("all extents (minimum/maximum X/Y/Z) are required.");e.defined(f)||(f=new c);var x=f.halfAxes;o.Matrix3.setColumn(x,0,r,x),o.Matrix3.setColumn(x,1,i,x),o.Matrix3.setColumn(x,2,s,x);var M=w;M.x=(C+u)/2,M.y=(d+l)/2,M.z=(h+m)/2;var p=g;p.x=(u-C)/2,p.y=(l-d)/2,p.z=(m-h)/2;var b=f.center;return M=o.Matrix3.multiplyByVector(x,M,M),n.Cartesian3.add(a,M,b),o.Matrix3.multiplyByScale(x,p,x),f}var y=new r.Cartographic,O=new n.Cartesian3,v=new r.Cartographic,P=new r.Cartographic,N=new r.Cartographic,A=new r.Cartographic,E=new r.Cartographic,R=new n.Cartesian3,T=new n.Cartesian3,I=new n.Cartesian3,L=new n.Cartesian3,S=new n.Cartesian3,z=new n.Cartesian2,B=new n.Cartesian2,U=new n.Cartesian2,V=new n.Cartesian2,D=new n.Cartesian2,_=new n.Cartesian3,k=new n.Cartesian3,W=new n.Cartesian3,q=new n.Cartesian3,X=new n.Cartesian2,j=new n.Cartesian3,Z=new n.Cartesian3,G=new n.Cartesian3,Y=new C.Plane(n.Cartesian3.UNIT_X,0);c.fromRectangle=function(a,i,s,o,c){if(!e.defined(a))throw new t.DeveloperError("rectangle is required");if(a.width<0||a.width>n.CesiumMath.TWO_PI)throw new t.DeveloperError("Rectangle width must be between 0 and 2*pi");if(a.height<0||a.height>n.CesiumMath.PI)throw new t.DeveloperError("Rectangle height must be between 0 and pi");if(e.defined(o)&&!n.CesiumMath.equalsEpsilon(o.radii.x,o.radii.y,n.CesiumMath.EPSILON15))throw new t.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");var d,l,h,m,f,x,M;if(i=e.defaultValue(i,0),s=e.defaultValue(s,0),o=e.defaultValue(o,r.Ellipsoid.WGS84),a.width<=n.CesiumMath.PI){var p=r.Rectangle.center(a,y),w=o.cartographicToCartesian(p,O),g=new u.EllipsoidTangentPlane(w,o);M=g.plane;var F=p.longitude,H=a.south<0&&a.north>0?0:p.latitude,J=r.Cartographic.fromRadians(F,a.north,s,v),K=r.Cartographic.fromRadians(a.west,a.north,s,P),Q=r.Cartographic.fromRadians(a.west,H,s,N),$=r.Cartographic.fromRadians(a.west,a.south,s,A),aa=r.Cartographic.fromRadians(F,a.south,s,E),ea=o.cartographicToCartesian(J,R),ta=o.cartographicToCartesian(K,T),na=o.cartographicToCartesian(Q,I),ra=o.cartographicToCartesian($,L),ia=o.cartographicToCartesian(aa,S),sa=g.projectPointToNearestOnPlane(ea,z),oa=g.projectPointToNearestOnPlane(ta,B),Ca=g.projectPointToNearestOnPlane(na,U),ua=g.projectPointToNearestOnPlane(ra,V),ca=g.projectPointToNearestOnPlane(ia,D);return d=Math.min(oa.x,Ca.x,ua.x),l=-d,m=Math.max(oa.y,sa.y),h=Math.min(ua.y,ca.y),K.height=$.height=i,ta=o.cartographicToCartesian(K,T),ra=o.cartographicToCartesian($,L),f=Math.min(C.Plane.getPointDistance(M,ta),C.Plane.getPointDistance(M,ra)),x=s,b(g.origin,g.xAxis,g.yAxis,g.zAxis,d,l,h,m,f,x,c)}var da=a.south>0,la=a.north<0,ha=da?a.south:la?a.north:0,ma=r.Rectangle.center(a,y).longitude,fa=n.Cartesian3.fromRadians(ma,ha,s,o,_);fa.z=0;var xa=Math.abs(fa.x)<n.CesiumMath.EPSILON10&&Math.abs(fa.y)<n.CesiumMath.EPSILON10,Ma=xa?n.Cartesian3.UNIT_X:n.Cartesian3.normalize(fa,k),pa=n.Cartesian3.UNIT_Z,wa=n.Cartesian3.cross(Ma,pa,W);M=C.Plane.fromPointNormal(fa,Ma,Y);var ga=n.Cartesian3.fromRadians(ma+n.CesiumMath.PI_OVER_TWO,ha,s,o,q);l=n.Cartesian3.dot(C.Plane.projectPointOntoPlane(M,ga,X),wa),d=-l,m=n.Cartesian3.fromRadians(0,a.north,la?i:s,o,j).z,h=n.Cartesian3.fromRadians(0,a.south,da?i:s,o,Z).z;var ba=n.Cartesian3.fromRadians(a.east,ha,s,o,G);return f=C.Plane.getPointDistance(M,ba),x=0,b(fa,wa,pa,Ma,d,l,h,m,f,x,c)},c.clone=function(a,t){if(e.defined(a))return e.defined(t)?(n.Cartesian3.clone(a.center,t.center),o.Matrix3.clone(a.halfAxes,t.halfAxes),t):new c(a.center,a.halfAxes)},c.intersectPlane=function(a,e){var t=a.center,r=e.normal,i=a.halfAxes,C=r.x,u=r.y,c=r.z,d=Math.abs(C*i[o.Matrix3.COLUMN0ROW0]+u*i[o.Matrix3.COLUMN0ROW1]+c*i[o.Matrix3.COLUMN0ROW2])+Math.abs(C*i[o.Matrix3.COLUMN1ROW0]+u*i[o.Matrix3.COLUMN1ROW1]+c*i[o.Matrix3.COLUMN1ROW2])+Math.abs(C*i[o.Matrix3.COLUMN2ROW0]+u*i[o.Matrix3.COLUMN2ROW1]+c*i[o.Matrix3.COLUMN2ROW2]),l=n.Cartesian3.dot(r,t)+e.distance;return l<=-d?s.Intersect.OUTSIDE:l>=d?s.Intersect.INSIDE:s.Intersect.INTERSECTING};var F=new n.Cartesian3,H=new n.Cartesian3,J=new n.Cartesian3,K=new n.Cartesian3;c.distanceSquaredTo=function(a,e){var t=n.Cartesian3.subtract(e,a.center,w),r=a.halfAxes,i=o.Matrix3.getColumn(r,0,F),s=o.Matrix3.getColumn(r,1,H),C=o.Matrix3.getColumn(r,2,J),u=n.Cartesian3.magnitude(i),c=n.Cartesian3.magnitude(s),d=n.Cartesian3.magnitude(C);n.Cartesian3.normalize(i,i),n.Cartesian3.normalize(s,s),n.Cartesian3.normalize(C,C);var l=K;l.x=n.Cartesian3.dot(t,i),l.y=n.Cartesian3.dot(t,s),l.z=n.Cartesian3.dot(t,C);var h,m=0;return l.x<-u?(h=l.x+u,m+=h*h):l.x>u&&(h=l.x-u,m+=h*h),l.y<-c?(h=l.y+c,m+=h*h):l.y>c&&(h=l.y-c,m+=h*h),l.z<-d?(h=l.z+d,m+=h*h):l.z>d&&(h=l.z-d,m+=h*h),m};var Q=new n.Cartesian3,$=new n.Cartesian3;c.computePlaneDistances=function(a,t,r,s){e.defined(s)||(s=new i.Interval);var C=Number.POSITIVE_INFINITY,u=Number.NEGATIVE_INFINITY,c=a.center,d=a.halfAxes,l=o.Matrix3.getColumn(d,0,F),h=o.Matrix3.getColumn(d,1,H),m=o.Matrix3.getColumn(d,2,J),f=n.Cartesian3.add(l,h,Q);n.Cartesian3.add(f,m,f),n.Cartesian3.add(f,c,f);var x=n.Cartesian3.subtract(f,t,$),M=n.Cartesian3.dot(r,x);return C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.add(c,l,f),n.Cartesian3.add(f,h,f),n.Cartesian3.subtract(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.add(c,l,f),n.Cartesian3.subtract(f,h,f),n.Cartesian3.add(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.add(c,l,f),n.Cartesian3.subtract(f,h,f),n.Cartesian3.subtract(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.subtract(c,l,f),n.Cartesian3.add(f,h,f),n.Cartesian3.add(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.subtract(c,l,f),n.Cartesian3.add(f,h,f),n.Cartesian3.subtract(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.subtract(c,l,f),n.Cartesian3.subtract(f,h,f),n.Cartesian3.add(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),n.Cartesian3.subtract(c,l,f),n.Cartesian3.subtract(f,h,f),n.Cartesian3.subtract(f,m,f),n.Cartesian3.subtract(f,t,x),M=n.Cartesian3.dot(r,x),C=Math.min(M,C),u=Math.max(M,u),s.start=C,s.stop=u,s};var aa=new i.BoundingSphere;c.isOccluded=function(a,e){var t=i.BoundingSphere.fromOrientedBoundingBox(a,aa);return!e.isBoundingSphereVisible(t)},c.prototype.intersectPlane=function(a){return c.intersectPlane(this,a)},c.prototype.distanceSquaredTo=function(a){return c.distanceSquaredTo(this,a)},c.prototype.computePlaneDistances=function(a,e,t){return c.computePlaneDistances(this,a,e,t)},c.prototype.isOccluded=function(a){return c.isOccluded(this,a)},c.equals=function(a,t){return a===t||e.defined(a)&&e.defined(t)&&n.Cartesian3.equals(a.center,t.center)&&o.Matrix3.equals(a.halfAxes,t.halfAxes)},c.prototype.clone=function(a){return c.clone(this,a)},c.prototype.equals=function(a){return c.equals(this,a)};var ea=new n.Cartesian3;c.prototype.intersectRay=function(a){var e=i.BoundingSphere.fromOrientedBoundingBox(this,aa),t=e.center,r=n.Cartesian3.subtract(t,a.origin,ea),s=n.Cartesian3.magnitude(n.Cartesian3.cross(r,a.direction,ea));return s<=e.radius},a.OrientedBoundingBox=c}));