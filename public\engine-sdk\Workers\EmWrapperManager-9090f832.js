define(["exports","./when-1807bd8d","./materem-a4c9ef74"],(function(e,n,r){"use strict";class s{static initWebAssembly(e){return o(e)}}function o(s){let o=n.when.defer();if(n.defined(e.emMod))o.resolve(!0);else{const n=new XMLHttpRequest;n.open("GET",s,!0),n.responseType="arraybuffer",n.send(),n.onload=function(){var s={};s["wasmBinary"]=n.response,s["onModuleLoaded"]=function(n){e.emMod=n,o.resolve(!0)},r.materem(s)}}return o.promise}e.EmWrapperManager=s}));