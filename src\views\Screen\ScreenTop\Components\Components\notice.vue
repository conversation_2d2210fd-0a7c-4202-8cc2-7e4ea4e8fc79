<template>
  <PopupBg title="通知" width="1815px" height="838px">
    <div class="notice-table-wrapper scroll-bar-style">
      <table class="notice-table">
        <thead>
          <tr class="notice-table-header">
            <th>序号</th>
            <th>升级时间</th>
            <th>信息</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in list" :key="item.id">
            <td>{{ index + 1 }}</td>
            <td>{{ formatTime(item.upgradeTime) }}</td>
            <td class="message-cell" v-html="processMessage(item.message)"></td>
          </tr>
        </tbody>
      </table>
    </div>
  </PopupBg>
</template>

<script setup lang="ts">
  import PopupBg from '@/components/PopupBg/index.vue'
  import { ref } from 'vue'
  import moment from 'moment'
  import lib from '@/utils/lib'

  // 假设list由接口获取，这里只保留结构
  const list = ref([])

  function formatTime(ts: number) {
    if (!ts) return ''
    return moment(ts).format('YYYY-MM-DD HH:mm:ss')
  }

  function processMessage(message: string) {
    if (!message) return ''

    // 检查是否包含有序列表
    if (message.includes('<ol>') && message.includes('<li>')) {
      // 分割消息，处理每个ol标签
      let result = message
      let olCounter = 0

      // 使用正则表达式匹配每个ol标签及其内容
      result = result.replace(/<ol>([\s\S]*?)<\/ol>/g, (match, olContent) => {
        olCounter++
        let liCounter = 1

        // 为当前ol标签内的每个li标签添加序号
        const processedOlContent = olContent.replace(/<li>/g, () => {
          return `<li style="position: relative; padding-left: 30px;"><span style="position: absolute; left: 0; color: #ffffff;">${liCounter++}. </span>`
        })

        return `<ol>${processedOlContent}</ol>`
      })

      return result
    }

    return message
  }

  onMounted(() => {
    lib.api.UpgradeMessageApi.list({ currPage: 1, pageSize: 1000 }).then((res) => {
      console.log('升级列表', res)
      if (res.success) {
        list.value = res.result.list
      }
    })
  })
</script>

<style lang="scss" scoped>
  .notice-table-wrapper {
    max-height: 700px;
    padding-right: 20px;
    padding-left: 20px;
    overflow: auto;
    .notice-table {
      width: 100%;
      text-align: center;
      border-collapse: collapse;
      .notice-table-header {
        background: linear-gradient(90deg, rgb(0 40 85 / 73%) 0%, rgb(4 86 117 / 94%) 53%, rgb(0 79 129 / 56%) 100%);
        th {
          padding: 8px;
          font-size: 30px;
          color: #ffffff;
          text-align: center;
          border: 1px solid #cccccc;
        }
      }
      td {
        padding: 8px;
        font-size: 28px;
        color: #ffffff;
        text-align: center;
        border: 1px solid #cccccc;
        &.message-cell {
          text-align: left;
          ol {
            padding-left: 0;
            margin: 0;
            list-style: none;
            li {
              margin-bottom: 8px;
              line-height: 1.4;
              text-align: left;
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
      th {
        padding: 8px;
        font-size: 30px;
        color: #ffffff;
        text-align: center;
        border: 1px solid #cccccc;
      }
    }
  }
</style>
