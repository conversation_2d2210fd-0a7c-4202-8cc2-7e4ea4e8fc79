import defined from"../../Core/defined.js";import destroyObject from"../../Core/destroyObject.js";import DeveloperError from"../../Core/DeveloperError.js";import knockout from"../../ThirdParty/knockout.js";import getElement from"../getElement.js";import HomeButtonViewModel from"./HomeButtonViewModel.js";function HomeButton(t,e,o){if(!defined(t))throw new DeveloperError("container is required.");t=getElement(t);var r=new HomeButtonViewModel(e,o);r._svgPath="M14,4l-10,8.75h20l-4.25-3.7188v-4.6562h-2.812v2.1875l-2.938-2.5625zm-7.0938,9.906v10.094h14.094v-10.094h-14.094zm2.1876,2.313h3.3122v4.25h-3.3122v-4.25zm5.8442,1.281h3.406v6.438h-3.406v-6.438z";var n=document.createElement("button");n.type="button",n.className="cesium-button cesium-toolbar-button cesium-home-button",n.setAttribute("data-bind","attr: { title: tooltip },click: command,cesiumSvgPath: { path: _svgPath, width: 28, height: 28 }"),t.appendChild(n),knockout.applyBindings(r,n),this._container=t,this._viewModel=r,this._element=n}Object.defineProperties(HomeButton.prototype,{container:{get:function(){return this._container}},viewModel:{get:function(){return this._viewModel}}}),HomeButton.prototype.isDestroyed=function(){return!1},HomeButton.prototype.destroy=function(){return knockout.cleanNode(this._element),this._container.removeChild(this._element),destroyObject(this)};export default HomeButton;