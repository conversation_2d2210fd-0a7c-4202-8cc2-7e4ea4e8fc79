/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./defined-3b3eb2ba","./Rectangle-9bffefe4","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889","./PolygonPipeline-805d6577","./RectangleGeometryLibrary-194ff03f","./RuntimeError-592f0d41","./Resource-41d99fe7","./combine-0bec9016","./WebGLConstants-433debbf","./EllipsoidRhumbLine-d5e7f3db"],(function(e,t,i,n,o,a,r,l,s,u,c,d,p,f,g,h,y){"use strict";const b=new i.BoundingSphere,m=new i.BoundingSphere,_=new n.Cartesian3,E=new t.Rectangle;function A(e,t){const i=e._ellipsoid,n=t.height,l=t.width,u=t.northCap,c=t.southCap;let p=n,f=2,g=0,h=4;u&&(f-=1,p-=1,g+=1,h-=2),c&&(f-=1,p-=1,g+=1,h-=2),g+=f*l+2*p-h;const y=new Float64Array(3*g);let b,m=0,E=0;const A=_;if(u)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,0,A),y[m++]=A.x,y[m++]=A.y,y[m++]=A.z;else for(b=0;b<l;b++)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,b,A),y[m++]=A.x,y[m++]=A.y,y[m++]=A.z;for(b=l-1,E=1;E<n;E++)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,b,A),y[m++]=A.x,y[m++]=A.y,y[m++]=A.z;if(E=n-1,!c)for(b=l-2;b>=0;b--)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,b,A),y[m++]=A.x,y[m++]=A.y,y[m++]=A.z;for(b=0,E=n-2;E>0;E--)d.RectangleGeometryLibrary.computePosition(t,i,!1,E,b,A),y[m++]=A.x,y[m++]=A.y,y[m++]=A.z;const G=y.length/3*2,R=s.IndexDatatype.createTypedArray(y.length/3,G);let P=0;for(let e=0;e<y.length/3-1;e++)R[P++]=e,R[P++]=e+1;R[P++]=y.length/3-1,R[P++]=0;const L=new a.Geometry({attributes:new r.GeometryAttributes,primitiveType:a.PrimitiveType.LINES});return L.attributes.position=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:y}),L.indices=R,L}function G(i){const n=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).rectangle,o=e.defaultValue(i.granularity,u.CesiumMath.RADIANS_PER_DEGREE),a=e.defaultValue(i.ellipsoid,t.Ellipsoid.WGS84),r=e.defaultValue(i.rotation,0),l=e.defaultValue(i.height,0),s=e.defaultValue(i.extrudedHeight,l);this._rectangle=t.Rectangle.clone(n),this._granularity=o,this._ellipsoid=a,this._surfaceHeight=Math.max(l,s),this._rotation=r,this._extrudedHeight=Math.min(l,s),this._offsetAttribute=i.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}G.packedLength=t.Rectangle.packedLength+t.Ellipsoid.packedLength+5,G.pack=function(i,n,o){return o=e.defaultValue(o,0),t.Rectangle.pack(i._rectangle,n,o),o+=t.Rectangle.packedLength,t.Ellipsoid.pack(i._ellipsoid,n,o),o+=t.Ellipsoid.packedLength,n[o++]=i._granularity,n[o++]=i._surfaceHeight,n[o++]=i._rotation,n[o++]=i._extrudedHeight,n[o]=e.defaultValue(i._offsetAttribute,-1),n};const R=new t.Rectangle,P=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),L={rectangle:R,ellipsoid:P,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};G.unpack=function(i,n,o){n=e.defaultValue(n,0);const a=t.Rectangle.unpack(i,n,R);n+=t.Rectangle.packedLength;const r=t.Ellipsoid.unpack(i,n,P);n+=t.Ellipsoid.packedLength;const l=i[n++],s=i[n++],u=i[n++],c=i[n++],d=i[n];return e.defined(o)?(o._rectangle=t.Rectangle.clone(a,o._rectangle),o._ellipsoid=t.Ellipsoid.clone(r,o._ellipsoid),o._surfaceHeight=s,o._rotation=u,o._extrudedHeight=c,o._offsetAttribute=-1===d?void 0:d,o):(L.granularity=l,L.height=s,L.rotation=u,L.extrudedHeight=c,L.offsetAttribute=-1===d?void 0:d,new G(L))};const v=new t.Cartographic;return G.createGeometry=function(t){const n=t._rectangle,r=t._ellipsoid,p=d.RectangleGeometryLibrary.computeOptions(n,t._granularity,t._rotation,0,E,v);let f,g;if(u.CesiumMath.equalsEpsilon(n.north,n.south,u.CesiumMath.EPSILON10)||u.CesiumMath.equalsEpsilon(n.east,n.west,u.CesiumMath.EPSILON10))return;const h=t._surfaceHeight,y=t._extrudedHeight;let _;if(!u.CesiumMath.equalsEpsilon(h,y,0,u.CesiumMath.EPSILON2)){if(f=function(e,t){const i=e._surfaceHeight,n=e._extrudedHeight,o=e._ellipsoid,a=n,r=i,l=A(e,t),u=t.height,d=t.width,p=c.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,r,o,!1);let f=p.length;const g=new Float64Array(2*f);g.set(p);const h=c.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,a,o);g.set(h,f),l.attributes.position.values=g;const y=t.northCap,b=t.southCap;let m=4;y&&(m-=1),b&&(m-=1);const _=2*(g.length/3+m),E=s.IndexDatatype.createTypedArray(g.length/3,_);f=g.length/6;let G,R=0;for(let e=0;e<f-1;e++)E[R++]=e,E[R++]=e+1,E[R++]=e+f,E[R++]=e+f+1;if(E[R++]=f-1,E[R++]=0,E[R++]=f+f-1,E[R++]=f,E[R++]=0,E[R++]=f,y)G=u-1;else{const e=d-1;E[R++]=e,E[R++]=e+f,G=d+u-2}if(E[R++]=G,E[R++]=G+f,!b){const e=d+G-1;E[R++]=e,E[R]=e+f}return l.indices=E,l}(t,p),e.defined(t._offsetAttribute)){const e=f.attributes.position.values.length/3;let i=new Uint8Array(e);t._offsetAttribute===l.GeometryOffsetAttribute.TOP?i=i.fill(1,0,e/2):(_=t._offsetAttribute===l.GeometryOffsetAttribute.NONE?0:1,i=i.fill(_)),f.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}const u=i.BoundingSphere.fromRectangle3D(n,r,h,m),d=i.BoundingSphere.fromRectangle3D(n,r,y,b);g=i.BoundingSphere.union(u,d)}else{if(f=A(t,p),f.attributes.position.values=c.PolygonPipeline.scaleToGeodeticHeight(f.attributes.position.values,h,r,!1),e.defined(t._offsetAttribute)){const e=f.attributes.position.values.length;_=t._offsetAttribute===l.GeometryOffsetAttribute.NONE?0:1;const i=new Uint8Array(e/3).fill(_);f.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}g=i.BoundingSphere.fromRectangle3D(n,r,h)}return new a.Geometry({attributes:f.attributes,indices:f.indices,primitiveType:a.PrimitiveType.LINES,boundingSphere:g,offsetAttribute:t._offsetAttribute})},function(i,n){return e.defined(n)&&(i=G.unpack(i,n)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),i._rectangle=t.Rectangle.clone(i._rectangle),G.createGeometry(i)}}));
