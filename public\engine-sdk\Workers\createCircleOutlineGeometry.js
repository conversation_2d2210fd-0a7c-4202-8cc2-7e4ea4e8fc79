define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./EllipseGeometryLibrary-98898859","./EllipseOutlineGeometry-4ce65568"],(function(e,i,t,r,n,l,s,o,a,u,c,d,m,p,y,f,G){"use strict";function h(t){t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT);var r=t.radius;i.Check.typeOf.number("radius",r);var n={center:t.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:t.ellipsoid,height:t.height,extrudedHeight:t.extrudedHeight,granularity:t.granularity,numberOfVerticalLines:t.numberOfVerticalLines};this._ellipseGeometry=new G.EllipseOutlineGeometry(n),this._workerName="createCircleOutlineGeometry"}h.packedLength=G.EllipseOutlineGeometry.packedLength,h.pack=function(e,t,r){return i.Check.typeOf.object("value",e),G.EllipseOutlineGeometry.pack(e._ellipseGeometry,t,r)};var _=new G.EllipseOutlineGeometry({center:new t.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),g={center:new t.Cartesian3,radius:void 0,ellipsoid:r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,numberOfVerticalLines:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0};function b(i,n){return e.defined(n)&&(i=h.unpack(i,n)),i._ellipseGeometry._center=t.Cartesian3.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=r.Ellipsoid.clone(i._ellipseGeometry._ellipsoid),h.createGeometry(i)}return h.unpack=function(i,n,l){var s=G.EllipseOutlineGeometry.unpack(i,n,_);return g.center=t.Cartesian3.clone(s._center,g.center),g.ellipsoid=r.Ellipsoid.clone(s._ellipsoid,g.ellipsoid),g.height=s._height,g.extrudedHeight=s._extrudedHeight,g.granularity=s._granularity,g.numberOfVerticalLines=s._numberOfVerticalLines,e.defined(l)?(g.semiMajorAxis=s._semiMajorAxis,g.semiMinorAxis=s._semiMinorAxis,l._ellipseGeometry=new G.EllipseOutlineGeometry(g),l):(g.radius=s._semiMajorAxis,new h(g))},h.createGeometry=function(e){return G.EllipseOutlineGeometry.createGeometry(e._ellipseGeometry)},b}));