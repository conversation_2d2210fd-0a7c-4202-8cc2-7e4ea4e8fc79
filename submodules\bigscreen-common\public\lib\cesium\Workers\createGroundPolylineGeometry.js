/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./Math-b5f4d889","./Resource-41d99fe7","./ArcType-e42cfb05","./arrayRemoveDuplicates-5b666c82","./ComponentDatatype-dad47320","./EllipsoidGeodesic-6493fad5","./EllipsoidRhumbLine-d5e7f3db","./EncodedCartesian3-6d30a00c","./Geometry-a94d02e6","./IntersectionTests-25cff68e","./Plane-a268aa11","./WebMercatorProjection-ce967e48","./RuntimeError-592f0d41","./combine-0bec9016","./WebGLConstants-433debbf"],(function(e,t,a,n,i,r,s,o,l,c,u,C,d,p,h,g,f,m,w){"use strict";function y(t){t=n.defaultValue(t,n.defaultValue.EMPTY_OBJECT),this._ellipsoid=n.defaultValue(t.ellipsoid,a.Ellipsoid.WGS84),this._rectangle=n.defaultValue(t.rectangle,a.Rectangle.MAX_VALUE),this._projection=new e.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=n.defaultValue(t.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=n.defaultValue(t.numberOfLevelZeroTilesY,1)}Object.defineProperties(y.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}}}),y.prototype.getNumberOfXTilesAtLevel=function(e){return this._numberOfLevelZeroTilesX<<e},y.prototype.getNumberOfYTilesAtLevel=function(e){return this._numberOfLevelZeroTilesY<<e},y.prototype.rectangleToNativeRectangle=function(e,t){const r=i.CesiumMath.toDegrees(e.west),s=i.CesiumMath.toDegrees(e.south),o=i.CesiumMath.toDegrees(e.east),l=i.CesiumMath.toDegrees(e.north);return n.defined(t)?(t.west=r,t.south=s,t.east=o,t.north=l,t):new a.Rectangle(r,s,o,l)},y.prototype.tileXYToNativeRectangle=function(e,t,a,n){const r=this.tileXYToRectangle(e,t,a,n);return r.west=i.CesiumMath.toDegrees(r.west),r.south=i.CesiumMath.toDegrees(r.south),r.east=i.CesiumMath.toDegrees(r.east),r.north=i.CesiumMath.toDegrees(r.north),r},y.prototype.tileXYToRectangle=function(e,t,i,r){const s=this._rectangle,o=this.getNumberOfXTilesAtLevel(i),l=this.getNumberOfYTilesAtLevel(i),c=s.width/o,u=e*c+s.west,C=(e+1)*c+s.west,d=s.height/l,p=s.north-t*d,h=s.north-(t+1)*d;return n.defined(r)||(r=new a.Rectangle(u,h,C,p)),r.west=u,r.south=h,r.east=C,r.north=p,r},y.prototype.positionToTileXY=function(e,r,s){const o=this._rectangle;if(!a.Rectangle.contains(o,e))return;const l=this.getNumberOfXTilesAtLevel(r),c=this.getNumberOfYTilesAtLevel(r),u=o.width/l,C=o.height/c;let d=e.longitude;o.east<o.west&&(d+=i.CesiumMath.TWO_PI);let p=(d-o.west)/u|0;p>=l&&(p=l-1);let h=(o.north-e.latitude)/C|0;return h>=c&&(h=c-1),n.defined(s)?(s.x=p,s.y=h,s):new t.Cartesian2(p,h)};const M=new t.Cartesian3,T=new t.Cartesian3,E=new a.Cartographic,_=new t.Cartesian3,b=new t.Cartesian3,O=new e.BoundingSphere,P=new y,k=[new a.Cartographic,new a.Cartographic,new a.Cartographic,new a.Cartographic],A=new t.Cartesian2,L={};function S(e){a.Cartographic.fromRadians(e.east,e.north,0,k[0]),a.Cartographic.fromRadians(e.west,e.north,0,k[1]),a.Cartographic.fromRadians(e.east,e.south,0,k[2]),a.Cartographic.fromRadians(e.west,e.south,0,k[3]);let t=0,n=0,i=0,r=0;const s=L._terrainHeightsMaxLevel;let o;for(o=0;o<=s;++o){let e=!1;for(let t=0;t<4;++t){const a=k[t];if(P.positionToTileXY(a,o,A),0===t)i=A.x,r=A.y;else if(i!==A.x||r!==A.y){e=!0;break}}if(e)break;t=i,n=r}if(0!==o)return{x:t,y:n,level:o>s?s:o-1}}L.initialize=function(){let t=L._initPromise;return n.defined(t)||(t=r.Resource.fetchJson(e.buildModuleUrl("Assets/approximateTerrainHeights.json")).then((function(e){L._terrainHeights=e})),L._initPromise=t),t},L.getMinimumMaximumHeights=function(e,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);const r=S(e);let s=L._defaultMinTerrainHeight,o=L._defaultMaxTerrainHeight;if(n.defined(r)){const l=`${r.level}-${r.x}-${r.y}`,c=L._terrainHeights[l];n.defined(c)&&(s=c[0],o=c[1]),i.cartographicToCartesian(a.Rectangle.northeast(e,E),M),i.cartographicToCartesian(a.Rectangle.southwest(e,E),T),t.Cartesian3.midpoint(T,M,_);const u=i.scaleToGeodeticSurface(_,b);if(n.defined(u)){const e=t.Cartesian3.distance(_,u);s=Math.min(s,-e)}else s=L._defaultMinTerrainHeight}return s=Math.max(L._defaultMinTerrainHeight,s),{minimumTerrainHeight:s,maximumTerrainHeight:o}},L.getBoundingSphere=function(t,i){i=n.defaultValue(i,a.Ellipsoid.WGS84);const r=S(t);let s=L._defaultMaxTerrainHeight;if(n.defined(r)){const e=`${r.level}-${r.x}-${r.y}`,t=L._terrainHeights[e];n.defined(t)&&(s=t[1])}const o=e.BoundingSphere.fromRectangle3D(t,i,0);return e.BoundingSphere.fromRectangle3D(t,i,s,O),e.BoundingSphere.union(o,O,o)},L._terrainHeightsMaxLevel=6,L._defaultMaxTerrainHeight=9e3,L._defaultMinTerrainHeight=-1e5,L._terrainHeights=void 0,L._initPromise=void 0,Object.defineProperties(L,{initialized:{get:function(){return n.defined(L._terrainHeights)}}});var I=L;const x=[e.GeographicProjection,g.WebMercatorProjection],N=x.length,R=Math.cos(i.CesiumMath.toRadians(30)),D=Math.cos(i.CesiumMath.toRadians(150)),v=0,z=1e3;function H(e){const t=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).positions;this.width=n.defaultValue(e.width,1),this._positions=t,this.granularity=n.defaultValue(e.granularity,9999),this.loop=n.defaultValue(e.loop,!1),this.arcType=n.defaultValue(e.arcType,s.ArcType.GEODESIC),this._ellipsoid=a.Ellipsoid.WGS84,this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(H.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+a.Ellipsoid.packedLength+1+1}}}),H.setProjectionAndEllipsoid=function(e,t){let a=0;for(let e=0;e<N;e++)if(t instanceof x[e]){a=e;break}e._projectionIndex=a,e._ellipsoid=t.ellipsoid};const B=new t.Cartesian3,j=new t.Cartesian3,G=new t.Cartesian3;function V(e,a,n,i,r){const s=Z(i,e,0,B),o=Z(i,e,n,j),l=Z(i,a,0,G),c=$(o,s,j),u=$(l,s,G);return t.Cartesian3.cross(u,c,r),t.Cartesian3.normalize(r,r)}const Y=new a.Cartographic,F=new t.Cartesian3,q=new t.Cartesian3,X=new t.Cartesian3;function W(e,a,n,i,r,o,l,C,d,p,h){if(0===r)return;let g;o===s.ArcType.GEODESIC?g=new c.EllipsoidGeodesic(e,a,l):o===s.ArcType.RHUMB&&(g=new u.EllipsoidRhumbLine(e,a,l));const f=g.surfaceDistance;if(f<r)return;const m=V(e,a,i,l,X),w=Math.ceil(f/r),y=f/w;let M=y;const T=w-1;let E=C.length;for(let e=0;e<T;e++){const e=g.interpolateUsingSurfaceDistance(M,Y),a=Z(l,e,n,F),r=Z(l,e,i,q);t.Cartesian3.pack(m,C,E),t.Cartesian3.pack(a,d,E),t.Cartesian3.pack(r,p,E),h.push(e.latitude),h.push(e.longitude),E+=3,M+=y}}const U=new a.Cartographic;function Z(e,t,n,i){return a.Cartographic.clone(t,U),U.height=n,a.Cartographic.toCartesian(U,e,i)}function $(e,a,n){return t.Cartesian3.subtract(e,a,n),t.Cartesian3.normalize(n,n),n}function J(e,a,n,i){return i=$(e,a,i),i=t.Cartesian3.cross(i,n,i),i=t.Cartesian3.normalize(i,i),i=t.Cartesian3.cross(n,i,i)}H.pack=function(e,i,r){let s=n.defaultValue(r,0);const o=e._positions,l=o.length;i[s++]=l;for(let e=0;e<l;++e){const a=o[e];t.Cartesian3.pack(a,i,s),s+=3}return i[s++]=e.granularity,i[s++]=e.loop?1:0,i[s++]=e.arcType,a.Ellipsoid.pack(e._ellipsoid,i,s),s+=a.Ellipsoid.packedLength,i[s++]=e._projectionIndex,i[s++]=e._scene3DOnly?1:0,i},H.unpack=function(e,i,r){let s=n.defaultValue(i,0);const o=e[s++],l=new Array(o);for(let a=0;a<o;a++)l[a]=t.Cartesian3.unpack(e,s),s+=3;const c=e[s++],u=1===e[s++],C=e[s++],d=a.Ellipsoid.unpack(e,s);s+=a.Ellipsoid.packedLength;const p=e[s++],h=1===e[s++];return n.defined(r)||(r=new H({positions:l})),r._positions=l,r.granularity=c,r.loop=u,r.arcType=C,r._ellipsoid=d,r._projectionIndex=p,r._scene3DOnly=h,r};const Q=new t.Cartesian3,K=new t.Cartesian3,ee=new t.Cartesian3,te=new t.Cartesian3;function ae(e,a,n,r,s){const o=$(n,a,te),l=J(e,a,o,Q),c=J(r,a,o,K);if(i.CesiumMath.equalsEpsilon(t.Cartesian3.dot(l,c),-1,i.CesiumMath.EPSILON5))return s=t.Cartesian3.cross(o,l,s),s=t.Cartesian3.normalize(s,s);s=t.Cartesian3.add(c,l,s),s=t.Cartesian3.normalize(s,s);const u=t.Cartesian3.cross(o,s,ee);return t.Cartesian3.dot(c,u)<0&&(s=t.Cartesian3.negate(s,s)),s}const ne=h.Plane.fromPointNormal(t.Cartesian3.ZERO,t.Cartesian3.UNIT_Y),ie=new t.Cartesian3,re=new t.Cartesian3,se=new t.Cartesian3,oe=new t.Cartesian3,le=new t.Cartesian3,ce=new t.Cartesian3,ue=new a.Cartographic,Ce=new a.Cartographic,de=new a.Cartographic;H.createGeometry=function(r){const c=!r._scene3DOnly;let h=r.loop;const g=r._ellipsoid,f=r.granularity,m=r.arcType,w=new x[r._projectionIndex](g),y=v,M=z;let T,E;const _=r._positions,b=_.length;let O,P,k,A;2===b&&(h=!1);const L=new u.EllipsoidRhumbLine(void 0,void 0,g);let S,N,D;const H=[_[0]];for(E=0;E<b-1;E++)O=_[E],P=_[E+1],S=p.IntersectionTests.lineSegmentPlane(O,P,ne,ce),!n.defined(S)||t.Cartesian3.equalsEpsilon(S,O,i.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(S,P,i.CesiumMath.EPSILON7)||(r.arcType===s.ArcType.GEODESIC?H.push(t.Cartesian3.clone(S)):r.arcType===s.ArcType.RHUMB&&(D=g.cartesianToCartographic(S,ue).longitude,k=g.cartesianToCartographic(O,ue),A=g.cartesianToCartographic(P,Ce),L.setEndPoints(k,A),N=L.findIntersectionWithLongitude(D,de),S=g.cartographicToCartesian(N,ce),!n.defined(S)||t.Cartesian3.equalsEpsilon(S,O,i.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(S,P,i.CesiumMath.EPSILON7)||H.push(t.Cartesian3.clone(S)))),H.push(P);h&&(O=_[b-1],P=_[0],S=p.IntersectionTests.lineSegmentPlane(O,P,ne,ce),!n.defined(S)||t.Cartesian3.equalsEpsilon(S,O,i.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(S,P,i.CesiumMath.EPSILON7)||(r.arcType===s.ArcType.GEODESIC?H.push(t.Cartesian3.clone(S)):r.arcType===s.ArcType.RHUMB&&(D=g.cartesianToCartographic(S,ue).longitude,k=g.cartesianToCartographic(O,ue),A=g.cartesianToCartographic(P,Ce),L.setEndPoints(k,A),N=L.findIntersectionWithLongitude(D,de),S=g.cartographicToCartesian(N,ce),!n.defined(S)||t.Cartesian3.equalsEpsilon(S,O,i.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(S,P,i.CesiumMath.EPSILON7)||H.push(t.Cartesian3.clone(S)))));let B=H.length,j=new Array(B);for(E=0;E<B;E++){const e=a.Cartographic.fromCartesian(H[E],g);e.height=0,j[E]=e}if(j=o.arrayRemoveDuplicates(j,a.Cartographic.equalsEpsilon),B=j.length,B<2)return;const G=[],Y=[],F=[],q=[];let X=ie,U=re,J=se,Q=oe,K=le;const ee=j[0],te=j[1];for(X=Z(g,j[B-1],y,X),Q=Z(g,te,y,Q),U=Z(g,ee,y,U),J=Z(g,ee,M,J),K=h?ae(X,U,J,Q,K):V(ee,te,M,g,K),t.Cartesian3.pack(K,Y,0),t.Cartesian3.pack(U,F,0),t.Cartesian3.pack(J,q,0),G.push(ee.latitude),G.push(ee.longitude),W(ee,te,y,M,f,m,g,Y,F,q,G),E=1;E<B-1;++E){X=t.Cartesian3.clone(U,X),U=t.Cartesian3.clone(Q,U);const e=j[E];Z(g,e,M,J),Z(g,j[E+1],y,Q),ae(X,U,J,Q,K),T=Y.length,t.Cartesian3.pack(K,Y,T),t.Cartesian3.pack(U,F,T),t.Cartesian3.pack(J,q,T),G.push(e.latitude),G.push(e.longitude),W(j[E],j[E+1],y,M,f,m,g,Y,F,q,G)}const pe=j[B-1],he=j[B-2];if(U=Z(g,pe,y,U),J=Z(g,pe,M,J),h){const e=j[0];X=Z(g,he,y,X),Q=Z(g,e,y,Q),K=ae(X,U,J,Q,K)}else K=V(he,pe,M,g,K);if(T=Y.length,t.Cartesian3.pack(K,Y,T),t.Cartesian3.pack(U,F,T),t.Cartesian3.pack(J,q,T),G.push(pe.latitude),G.push(pe.longitude),h){for(W(pe,ee,y,M,f,m,g,Y,F,q,G),T=Y.length,E=0;E<3;++E)Y[T+E]=Y[E],F[T+E]=F[E],q[T+E]=q[E];G.push(ee.latitude),G.push(ee.longitude)}return function(n,r,s,o,c,u,p){let h,g;const f=r._ellipsoid,m=s.length/3-1,w=8*m,y=4*w,M=36*m,T=w>65535?new Uint32Array(M):new Uint16Array(M),E=new Float64Array(3*w),_=new Float32Array(y),b=new Float32Array(y),O=new Float32Array(y),P=new Float32Array(y),k=new Float32Array(y);let A,L,S,x;p&&(A=new Float32Array(y),L=new Float32Array(y),S=new Float32Array(y),x=new Float32Array(2*w));const N=u.length/2;let D=0;const v=ke;v.height=0;const z=Ae;z.height=0;let H=Le,B=Se;if(p)for(g=0,h=1;h<N;h++)v.latitude=u[g],v.longitude=u[g+1],z.latitude=u[g+2],z.longitude=u[g+3],H=r.project(v,H),B=r.project(z,B),D+=t.Cartesian3.distance(H,B),g+=2;const j=o.length/3;B=t.Cartesian3.unpack(o,0,B);let G,V=0;for(g=3,h=1;h<j;h++)H=t.Cartesian3.clone(B,H),B=t.Cartesian3.unpack(o,g,B),V+=t.Cartesian3.distance(H,B),g+=3;g=3;let Y=0,F=0,q=0,X=0,W=!1,U=t.Cartesian3.unpack(s,0,xe),Z=t.Cartesian3.unpack(o,0,Se),J=t.Cartesian3.unpack(c,0,Re);if(n){fe(J,t.Cartesian3.unpack(s,s.length-6,Ie),U,Z)&&(J=t.Cartesian3.negate(J,J))}let Q=0,K=0,ee=0;for(h=0;h<m;h++){const e=t.Cartesian3.clone(U,Ie),n=t.Cartesian3.clone(Z,Le);let l,d,h,m,w=t.Cartesian3.clone(J,Ne);if(W&&(w=t.Cartesian3.negate(w,w)),U=t.Cartesian3.unpack(s,g,xe),Z=t.Cartesian3.unpack(o,g,Se),J=t.Cartesian3.unpack(c,g,Re),W=fe(J,e,U,Z),v.latitude=u[Y],v.longitude=u[Y+1],z.latitude=u[Y+2],z.longitude=u[Y+3],p){const e=Pe(v,z);l=r.project(v,Ge),d=r.project(z,Ve);const a=$(d,l,Ke);a.y=Math.abs(a.y),h=Ye,m=Fe,0===e||t.Cartesian3.dot(a,t.Cartesian3.UNIT_Y)>R?(h=Me(r,v,w,l,Ye),m=Me(r,z,J,d,Fe)):1===e?(m=Me(r,z,J,d,Fe),h.x=0,h.y=i.CesiumMath.sign(v.longitude-Math.abs(z.longitude)),h.z=0):(h=Me(r,v,w,l,Ye),m.x=0,m.y=i.CesiumMath.sign(v.longitude-z.longitude),m.z=0)}const y=t.Cartesian3.distance(n,Z),M=C.EncodedCartesian3.fromCartesian(e,Je),T=t.Cartesian3.subtract(U,e,qe),N=t.Cartesian3.normalize(T,Ue);let H=t.Cartesian3.subtract(n,e,Xe);H=t.Cartesian3.normalize(H,H);let B=t.Cartesian3.cross(N,H,Ue);B=t.Cartesian3.normalize(B,B);let j=t.Cartesian3.cross(H,w,Ze);j=t.Cartesian3.normalize(j,j);let te=t.Cartesian3.subtract(Z,U,We);te=t.Cartesian3.normalize(te,te);let ae=t.Cartesian3.cross(J,te,$e);ae=t.Cartesian3.normalize(ae,ae);const ne=y/V,ie=Q/V;let re,se,oe,le=0,ce=0,ue=0;if(p){le=t.Cartesian3.distance(l,d),re=C.EncodedCartesian3.fromCartesian(l,Qe),se=t.Cartesian3.subtract(d,l,Ke),oe=t.Cartesian3.normalize(se,et);const e=oe.x;oe.x=oe.y,oe.y=-e,ce=le/D,ue=K/D}for(G=0;G<8;G++){const e=X+4*G,a=F+2*G,n=e+3,i=G<4?1:-1,r=2===G||3===G||6===G||7===G?1:-1;t.Cartesian3.pack(M.high,_,e),_[n]=T.x,t.Cartesian3.pack(M.low,b,e),b[n]=T.y,t.Cartesian3.pack(j,O,e),O[n]=T.z,t.Cartesian3.pack(ae,P,e),P[n]=ne*i,t.Cartesian3.pack(B,k,e);let s=ie*r;0===s&&r<0&&(s=9),k[n]=s,p&&(A[e]=re.high.x,A[e+1]=re.high.y,A[e+2]=re.low.x,A[e+3]=re.low.y,S[e]=-h.y,S[e+1]=h.x,S[e+2]=m.y,S[e+3]=-m.x,L[e]=se.x,L[e+1]=se.y,L[e+2]=oe.x,L[e+3]=oe.y,x[a]=ce*i,s=ue*r,0===s&&r<0&&(s=9),x[a+1]=s)}const Ce=Be,de=je,pe=ze,he=He,ge=a.Rectangle.fromCartographicArray(De,ve),me=I.getMinimumMaximumHeights(ge,f),we=me.minimumTerrainHeight,ye=me.maximumTerrainHeight;ee+=we,ee+=ye,_e(e,n,we,ye,Ce,pe),_e(U,Z,we,ye,de,he);let Te=t.Cartesian3.multiplyByScalar(B,i.CesiumMath.EPSILON5,tt);t.Cartesian3.add(Ce,Te,Ce),t.Cartesian3.add(de,Te,de),t.Cartesian3.add(pe,Te,pe),t.Cartesian3.add(he,Te,he),Oe(Ce,de),Oe(pe,he),t.Cartesian3.pack(Ce,E,q),t.Cartesian3.pack(de,E,q+3),t.Cartesian3.pack(he,E,q+6),t.Cartesian3.pack(pe,E,q+9),Te=t.Cartesian3.multiplyByScalar(B,-2*i.CesiumMath.EPSILON5,tt),t.Cartesian3.add(Ce,Te,Ce),t.Cartesian3.add(de,Te,de),t.Cartesian3.add(pe,Te,pe),t.Cartesian3.add(he,Te,he),Oe(Ce,de),Oe(pe,he),t.Cartesian3.pack(Ce,E,q+12),t.Cartesian3.pack(de,E,q+15),t.Cartesian3.pack(he,E,q+18),t.Cartesian3.pack(pe,E,q+21),Y+=2,g+=3,F+=16,q+=24,X+=32,Q+=y,K+=le}g=0;let te=0;for(h=0;h<m;h++){for(G=0;G<it;G++)T[g+G]=nt[G]+te;te+=8,g+=it}const ae=at;e.BoundingSphere.fromVertices(s,t.Cartesian3.ZERO,3,ae[0]),e.BoundingSphere.fromVertices(o,t.Cartesian3.ZERO,3,ae[1]);const ne=e.BoundingSphere.fromBoundingSpheres(ae);ne.radius+=ee/(2*m);const ie={position:new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:E}),startHiAndForwardOffsetX:rt(_),startLoAndForwardOffsetY:rt(b),startNormalAndForwardOffsetZ:rt(O),endNormalAndTextureCoordinateNormalizationX:rt(P),rightNormalAndTextureCoordinateNormalizationY:rt(k)};p&&(ie.startHiLo2D=rt(A),ie.offsetAndRight2D=rt(L),ie.startEndNormals2D=rt(S),ie.texcoordNormalization2D=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:x}));return new d.Geometry({attributes:ie,indices:T,boundingSphere:ne})}(h,w,F,q,Y,G,c)};const pe=new t.Cartesian3,he=new a.Matrix3,ge=new e.Quaternion;function fe(n,r,s,o){const l=$(s,r,pe),c=t.Cartesian3.dot(l,n);if(c>R||c<D){const t=$(o,s,te),r=c<D?i.CesiumMath.PI_OVER_TWO:-i.CesiumMath.PI_OVER_TWO,l=e.Quaternion.fromAxisAngle(t,r,ge),u=a.Matrix3.fromQuaternion(l,he);return a.Matrix3.multiplyByVector(u,n,n),!0}return!1}const me=new a.Cartographic,we=new t.Cartesian3,ye=new t.Cartesian3;function Me(e,n,r,s,o){const l=a.Cartographic.toCartesian(n,e._ellipsoid,we);let c=t.Cartesian3.add(l,r,ye),u=!1;const C=e._ellipsoid;let d=C.cartesianToCartographic(c,me);Math.abs(n.longitude-d.longitude)>i.CesiumMath.PI_OVER_TWO&&(u=!0,c=t.Cartesian3.subtract(l,r,ye),d=C.cartesianToCartographic(c,me)),d.height=0;const p=e.project(d,o);return(o=t.Cartesian3.subtract(p,s,o)).z=0,o=t.Cartesian3.normalize(o,o),u&&t.Cartesian3.negate(o,o),o}const Te=new t.Cartesian3,Ee=new t.Cartesian3;function _e(e,a,n,i,r,s){const o=t.Cartesian3.subtract(a,e,Te);t.Cartesian3.normalize(o,o);const l=n-v;let c=t.Cartesian3.multiplyByScalar(o,l,Ee);t.Cartesian3.add(e,c,r);const u=i-z;c=t.Cartesian3.multiplyByScalar(o,u,Ee),t.Cartesian3.add(a,c,s)}const be=new t.Cartesian3;function Oe(e,a){const n=h.Plane.getPointDistance(ne,e),r=h.Plane.getPointDistance(ne,a);let s=be;i.CesiumMath.equalsEpsilon(n,0,i.CesiumMath.EPSILON2)?(s=$(a,e,s),t.Cartesian3.multiplyByScalar(s,i.CesiumMath.EPSILON2,s),t.Cartesian3.add(e,s,e)):i.CesiumMath.equalsEpsilon(r,0,i.CesiumMath.EPSILON2)&&(s=$(e,a,s),t.Cartesian3.multiplyByScalar(s,i.CesiumMath.EPSILON2,s),t.Cartesian3.add(a,s,a))}function Pe(e,t){const a=Math.abs(e.longitude),n=Math.abs(t.longitude);if(i.CesiumMath.equalsEpsilon(a,i.CesiumMath.PI,i.CesiumMath.EPSILON11)){const n=i.CesiumMath.sign(t.longitude);return e.longitude=n*(a-i.CesiumMath.EPSILON11),1}if(i.CesiumMath.equalsEpsilon(n,i.CesiumMath.PI,i.CesiumMath.EPSILON11)){const a=i.CesiumMath.sign(e.longitude);return t.longitude=a*(n-i.CesiumMath.EPSILON11),2}return 0}const ke=new a.Cartographic,Ae=new a.Cartographic,Le=new t.Cartesian3,Se=new t.Cartesian3,Ie=new t.Cartesian3,xe=new t.Cartesian3,Ne=new t.Cartesian3,Re=new t.Cartesian3,De=[ke,Ae],ve=new a.Rectangle,ze=new t.Cartesian3,He=new t.Cartesian3,Be=new t.Cartesian3,je=new t.Cartesian3,Ge=new t.Cartesian3,Ve=new t.Cartesian3,Ye=new t.Cartesian3,Fe=new t.Cartesian3,qe=new t.Cartesian3,Xe=new t.Cartesian3,We=new t.Cartesian3,Ue=new t.Cartesian3,Ze=new t.Cartesian3,$e=new t.Cartesian3,Je=new C.EncodedCartesian3,Qe=new C.EncodedCartesian3,Ke=new t.Cartesian3,et=new t.Cartesian3,tt=new t.Cartesian3,at=[new e.BoundingSphere,new e.BoundingSphere],nt=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],it=nt.length;function rt(e){return new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return H._projectNormal=Me,function(e,t){return I.initialize().then((function(){return n.defined(t)&&(e=H.unpack(e,t)),H.createGeometry(e)}))}}));
