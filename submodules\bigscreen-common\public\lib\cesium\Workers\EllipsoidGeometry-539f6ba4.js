/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./Rectangle-9bffefe4","./Geometry-a94d02e6","./GeometryAttributes-a8038b36","./GeometryOffsetAttribute-5a4c2801","./IndexDatatype-00859b8b","./Math-b5f4d889","./VertexFormat-86c096b8"],(function(t,e,a,n,i,r,o,s,m,u,c,l){"use strict";const f=new a.Cartesian3,d=new a.Cartesian3,C=new a.Cartesian3,p=new a.Cartesian3,y=new a.Cartesian3,_=new a.Cartesian3(1,1,1),h=Math.cos,b=Math.sin;function x(t){t=i.defaultValue(t,i.defaultValue.EMPTY_OBJECT);const e=i.defaultValue(t.radii,_),n=i.defaultValue(t.innerRadii,e),r=i.defaultValue(t.minimumClock,0),o=i.defaultValue(t.maximumClock,c.CesiumMath.TWO_PI),s=i.defaultValue(t.minimumCone,0),m=i.defaultValue(t.maximumCone,c.CesiumMath.PI),u=Math.round(i.defaultValue(t.stackPartitions,64)),f=Math.round(i.defaultValue(t.slicePartitions,64)),d=i.defaultValue(t.vertexFormat,l.VertexFormat.DEFAULT);this._radii=a.Cartesian3.clone(e),this._innerRadii=a.Cartesian3.clone(n),this._minimumClock=r,this._maximumClock=o,this._minimumCone=s,this._maximumCone=m,this._stackPartitions=u,this._slicePartitions=f,this._vertexFormat=l.VertexFormat.clone(d),this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidGeometry"}x.packedLength=2*a.Cartesian3.packedLength+l.VertexFormat.packedLength+7,x.pack=function(t,e,n){return n=i.defaultValue(n,0),a.Cartesian3.pack(t._radii,e,n),n+=a.Cartesian3.packedLength,a.Cartesian3.pack(t._innerRadii,e,n),n+=a.Cartesian3.packedLength,l.VertexFormat.pack(t._vertexFormat,e,n),n+=l.VertexFormat.packedLength,e[n++]=t._minimumClock,e[n++]=t._maximumClock,e[n++]=t._minimumCone,e[n++]=t._maximumCone,e[n++]=t._stackPartitions,e[n++]=t._slicePartitions,e[n]=i.defaultValue(t._offsetAttribute,-1),e};const A=new a.Cartesian3,k=new a.Cartesian3,w=new l.VertexFormat,g={radii:A,innerRadii:k,vertexFormat:w,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};let P;x.unpack=function(t,e,n){e=i.defaultValue(e,0);const r=a.Cartesian3.unpack(t,e,A);e+=a.Cartesian3.packedLength;const o=a.Cartesian3.unpack(t,e,k);e+=a.Cartesian3.packedLength;const s=l.VertexFormat.unpack(t,e,w);e+=l.VertexFormat.packedLength;const m=t[e++],u=t[e++],c=t[e++],f=t[e++],d=t[e++],C=t[e++],p=t[e];return i.defined(n)?(n._radii=a.Cartesian3.clone(r,n._radii),n._innerRadii=a.Cartesian3.clone(o,n._innerRadii),n._vertexFormat=l.VertexFormat.clone(s,n._vertexFormat),n._minimumClock=m,n._maximumClock=u,n._minimumCone=c,n._maximumCone=f,n._stackPartitions=d,n._slicePartitions=C,n._offsetAttribute=-1===p?void 0:p,n):(g.minimumClock=m,g.maximumClock=u,g.minimumCone=c,g.maximumCone=f,g.stackPartitions=d,g.slicePartitions=C,g.offsetAttribute=-1===p?void 0:p,new x(g))},x.createGeometry=function(t){const l=t._radii;if(l.x<=0||l.y<=0||l.z<=0)return;const _=t._innerRadii;if(_.x<=0||_.y<=0||_.z<=0)return;const x=t._minimumClock,A=t._maximumClock,k=t._minimumCone,w=t._maximumCone,g=t._vertexFormat;let P,v,F=t._slicePartitions+1,V=t._stackPartitions+1;F=Math.round(F*Math.abs(A-x)/c.CesiumMath.TWO_PI),V=Math.round(V*Math.abs(w-k)/c.CesiumMath.PI),F<2&&(F=2),V<2&&(V=2);let M=0;const T=[k],D=[x];for(P=0;P<V;P++)T.push(k+P*(w-k)/(V-1));for(T.push(w),v=0;v<F;v++)D.push(x+v*(A-x)/(F-1));D.push(A);const G=T.length,L=D.length;let O=0,I=1;const E=_.x!==l.x||_.y!==l.y||_.z!==l.z;let z=!1,N=!1,R=!1;E&&(I=2,k>0&&(z=!0,O+=F-1),w<Math.PI&&(N=!0,O+=F-1),(A-x)%c.CesiumMath.TWO_PI?(R=!0,O+=2*(V-1)+1):O+=1);const U=L*G*I,S=new Float64Array(3*U),B=new Array(U).fill(!1),W=new Array(U).fill(!1),Y=F*V*I,J=6*(Y+O+1-(F+V)*I),X=u.IndexDatatype.createTypedArray(Y,J),Z=g.normal?new Float32Array(3*U):void 0,j=g.tangent?new Float32Array(3*U):void 0,q=g.bitangent?new Float32Array(3*U):void 0,H=g.st?new Float32Array(2*U):void 0,K=new Array(G),Q=new Array(G);for(P=0;P<G;P++)K[P]=b(T[P]),Q[P]=h(T[P]);const $=new Array(L),tt=new Array(L);for(v=0;v<L;v++)tt[v]=h(D[v]),$[v]=b(D[v]);for(P=0;P<G;P++)for(v=0;v<L;v++)S[M++]=l.x*K[P]*tt[v],S[M++]=l.y*K[P]*$[v],S[M++]=l.z*Q[P];let et,at,nt,it,rt=U/2;if(E)for(P=0;P<G;P++)for(v=0;v<L;v++)S[M++]=_.x*K[P]*tt[v],S[M++]=_.y*K[P]*$[v],S[M++]=_.z*Q[P],B[rt]=!0,P>0&&P!==G-1&&0!==v&&v!==L-1&&(W[rt]=!0),rt++;for(M=0,P=1;P<G-2;P++)for(et=P*L,at=(P+1)*L,v=1;v<L-2;v++)X[M++]=at+v,X[M++]=at+v+1,X[M++]=et+v+1,X[M++]=at+v,X[M++]=et+v+1,X[M++]=et+v;if(E){const t=G*L;for(P=1;P<G-2;P++)for(et=t+P*L,at=t+(P+1)*L,v=1;v<L-2;v++)X[M++]=at+v,X[M++]=et+v,X[M++]=et+v+1,X[M++]=at+v,X[M++]=et+v+1,X[M++]=at+v+1}if(E){if(z)for(it=G*L,P=1;P<L-2;P++)X[M++]=P,X[M++]=P+1,X[M++]=it+P+1,X[M++]=P,X[M++]=it+P+1,X[M++]=it+P;if(N)for(nt=G*L-L,it=G*L*I-L,P=1;P<L-2;P++)X[M++]=nt+P+1,X[M++]=nt+P,X[M++]=it+P,X[M++]=nt+P+1,X[M++]=it+P,X[M++]=it+P+1}if(R){for(P=1;P<G-2;P++)it=L*G+L*P,nt=L*P,X[M++]=it,X[M++]=nt+L,X[M++]=nt,X[M++]=it,X[M++]=it+L,X[M++]=nt+L;for(P=1;P<G-2;P++)it=L*G+L*(P+1)-1,nt=L*(P+1)-1,X[M++]=nt+L,X[M++]=it,X[M++]=nt,X[M++]=nt+L,X[M++]=it+L,X[M++]=it}const ot=new s.GeometryAttributes;g.position&&(ot.position=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:S}));let st=0,mt=0,ut=0,ct=0;const lt=U/2;let ft;const dt=r.Ellipsoid.fromCartesian3(l),Ct=r.Ellipsoid.fromCartesian3(_);if(g.st||g.normal||g.tangent||g.bitangent){for(P=0;P<U;P++){ft=B[P]?Ct:dt;const t=a.Cartesian3.fromArray(S,3*P,f),e=ft.geodeticSurfaceNormal(t,d);if(W[P]&&a.Cartesian3.negate(e,e),g.st){const t=a.Cartesian2.negate(e,y);H[st++]=Math.atan2(t.y,t.x)/c.CesiumMath.TWO_PI+.5,H[st++]=Math.asin(e.z)/Math.PI+.5}if(g.normal&&(Z[mt++]=e.x,Z[mt++]=e.y,Z[mt++]=e.z),g.tangent||g.bitangent){const t=C;let n,i=0;if(B[P]&&(i=lt),n=!z&&P>=i&&P<i+2*L?a.Cartesian3.UNIT_X:a.Cartesian3.UNIT_Z,a.Cartesian3.cross(n,e,t),a.Cartesian3.normalize(t,t),g.tangent&&(j[ut++]=t.x,j[ut++]=t.y,j[ut++]=t.z),g.bitangent){const n=a.Cartesian3.cross(e,t,p);a.Cartesian3.normalize(n,n),q[ct++]=n.x,q[ct++]=n.y,q[ct++]=n.z}}}g.st&&(ot.st=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:H})),g.normal&&(ot.normal=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:Z})),g.tangent&&(ot.tangent=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),g.bitangent&&(ot.bitangent=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:q}))}if(i.defined(t._offsetAttribute)){const e=S.length,a=t._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1,i=new Uint8Array(e/3).fill(a);ot.applyOffset=new o.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}return new o.Geometry({attributes:ot,indices:X,primitiveType:o.PrimitiveType.TRIANGLES,boundingSphere:e.BoundingSphere.fromEllipsoid(dt),offsetAttribute:t._offsetAttribute})},x.getUnitEllipsoid=function(){return i.defined(P)||(P=x.createGeometry(new x({radii:new a.Cartesian3(1,1,1),vertexFormat:l.VertexFormat.POSITION_ONLY}))),P},t.EllipsoidGeometry=x}));
