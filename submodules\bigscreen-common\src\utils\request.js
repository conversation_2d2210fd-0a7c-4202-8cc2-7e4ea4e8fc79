import { getToken } from '@/utils/auth'

import axios from 'axios'
// create an axios instance
const service = axios.create({
  timeout: 0 // request timeout
  // baseURL: process.env.ADMIN_SERVER
})

// 请求前拦截
service.interceptors.request.use(config => {
  config.baseURL = import.meta.env.VITE_BASE_URL
  const token = getToken()
  token && (config.headers['x-auth-token'] = token)
  return config
}, error => {
  // Do something with request error
  // console.log(error) // for debug
  Promise.reject(error)
})

// 去请求后拦截
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.status === 1001) {
      router.push({ path: '/login' })
    }
    return res
  },
  error => {
    // console.log('err' + error) // for debug
    return Promise.reject(error)
  })
export default service
