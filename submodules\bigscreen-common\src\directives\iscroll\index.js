/*
 * @Author: ya<PERSON>hen <EMAIL>
 * @Date: 2024-03-11 16:02:38
 * @LastEditors: yaozhen <EMAIL>
 * @LastEditTime: 2024-03-12 11:03:28
 * @FilePath: \bigscreen-common\src\directives\iScroll\index.js
 * @Description:
 *
 */
/**
 * @description: isScroll指令（为了解决pc端滚动区域能像移动端一样，能够通过鼠标拖动滚动区域直接进行滚动）
 * @param {Object} option 属性同isScroll配置的属性一致
 */
import IScroll from 'iscroll'

const vIscroll = {
  mounted(el, binding, vnode) {
    let callBack
    let iscrollOptions = {
      scrollX: true
    }
    const option = binding.value && binding.value.option
    const func = binding.value && binding.value.instance
    // 判断输入参数
    const optionType = option ? [].toString.call(option) : undefined
    const funcType = func ? [].toString.call(func) : undefined
    // 兼容 google 浏览器拖动
    el.addEventListener('touchmove', function (e) {
      e.preventDefault()
    })
    // 将参数配置到new IScroll(el, iscrollOptions)中
    if (optionType === '[object Object]') {
      iscrollOptions = { ...iscrollOptions, ...option }
    }
    if (funcType === '[object Function]') {
      callBack = func
    }
    // 使用vnode绑定iscroll是为了让iscroll对象能够夸状态传递，避免iscroll重复建立
    // 这里面跟官方网站 const myScroll = new IScroll('#wrapper'，option) 初始化一样
    vnode.scroll = new IScroll(el, iscrollOptions)
    // 如果指令传递函数进来，把iscroll实例传递出去
    if (callBack) callBack(vnode.scroll)
  },
  updated(el, binding, vnode, oldVnode) {
    // 将scroll绑定到新的vnode上，避免多次绑定
    vnode.scroll = oldVnode.scroll
    // 使用 settimeout 让refresh跳到事件流结尾，保证refresh时数据已经更新完毕
    setTimeout(() => {
      vnode.scroll.refresh()
    }, 0)
  },
  unmounted(el, binding, vnode, oldVnode) {
    // 解除绑定时要把iscroll销毁
    if (!oldVnode?.scroll) return
    vnode.scroll = oldVnode.scroll
    vnode.scroll.destroy()
    vnode.scroll = null
  }
}
export default vIscroll

/* option基本属性：{
  preventDefault: false,  // 阻止浏览器滑动默认行为
  probeType: 3, //需要使用 iscroll-probe.js 才能生效 probeType ： 1 滚动不繁忙的时候触发 probeType ： 2 滚动时每隔一定时间触发 probeType ： 3   每滚动一像素触发一次
  mouseWheel: true, //是否监听鼠标滚轮事件。
  scrollX: true,  // 启动x轴滑动
  scrollY: true,  // 启动y轴滑动
  lockDirection: false,
  snap: false, //自动分割容器，用于制作走马灯效果等。Options.snap:true// 根据容器尺寸自动分割
  scrollbars: false, //是否显示默认滚动条
  freeScroll: true, //主要在上下左右滚动都生效时使用，可以向任意方向滚动。
  deceleration: 0.0001, //滚动动量减速越大越快，建议不大于 0.01,默认:0.0006
  disableMouse: true, //是否关闭鼠标事件探测。如知道运行在哪个平台，可以开启它来加速。
  disablePointer: true, //是否关闭指针事件探测。如知道运行在哪个平台，可以开启它来加速。
  disableTouch: false, //是否关闭触摸事件探测。如知道运行在哪个平台，可以开启它来加速。
  eventPassthrough: false, //使用 IScroll 的横轴滚动时，如想使用系统立轴滚动并在横轴上生效，请开启。
  bounce: false //是否启用弹力动画效果，关掉可以加速
} */
