<template>
  <div class="tabs-container">
    <div class="tabs-item" :class="{ selected: item.selected }" v-for="item in list" :key="item.id" @click="handleClick(item)">
      <div class="num">{{ item.num }}</div>
      <div class="name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script setup>
  import { watchEffect } from 'vue'
  import { useIntervalFn } from '@vueuse/core'

  import lib from '@/utils/lib.ts'

  defineOptions({
    name: 'Tabs'
  })
  const list = ref([
    { id: 1, name: '报警总数', num: '54', selected: true, data: {} },
    { id: 2, name: '沉降报警', num: '12', selected: false, data: {} },
    { id: 3, name: '收敛报警', num: '6', selected: false, data: {} },
    { id: 4, name: '变形报警', num: '12', selected: false, data: {} },
    { id: 5, name: '渗漏水报警', num: '24', selected: false, data: {} }
  ])

  const selectedAlert = computed(() => {
    return list.value.find((_) => _.selected)
  })
  watchEffect(() => {
    lib.bus.busMonitorRingChart.emit(selectedAlert.value.data)
  })

  const handleClick = (val) => {
    list.value.forEach((item) => {
      item.selected = item.id === val.id
    })
  }

  onMounted(() => {
    useIntervalFn(
      () => {
        getData()
      },
      1000 * 60,
      { immediateCallback: true }
    )
  })
  const getData = () => {
    lib.api.bigscreenApi.channelAlert({}).then((res) => {
      if (res.success) {
        const { electromechanicalDevice, structure } = res.result
        list.value.forEach((item) => {
          const alertItem = structure.find((_) => _.name === item.name)
          if (alertItem) {
            item.num = alertItem.value
            item.data = alertItem
          }
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .tabs-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 87px;
    .tabs-item {
      width: 110px;
      height: 87px;
      padding: 8px 5px;
      cursor: pointer;
      background: url('@/assets/ScreenRight/Perception/Structure/<EMAIL>');
      background-size: cover;
      .num {
        font-family: PangMenZhengDao;
        font-size: 32px;
        font-weight: 400;
        line-height: 34px;
        color: #5fc6ff;
        text-align: center;
      }
      .name {
        margin-top: 5px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        font-weight: 400;
        line-height: 24px;
        color: #c4e2f3;
        text-align: center;
      }
      &.selected {
        background: url('@/assets/ScreenRight/Perception/Structure/<EMAIL>');
        .num {
          color: #ffb800;
        }
        .name {
          color: #ffffff;
        }
      }
    }
  }
</style>
