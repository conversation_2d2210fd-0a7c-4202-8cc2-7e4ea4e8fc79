import { reactive } from 'vue'

import { bimProjectInfo, terrainBurrowInfo, viewInfo } from './data/data.js'
import { laneInfo } from './data/lanes.js'
import { toUe5, toUe5Viewpoint } from './tools.js'
import lib from '@/utils/lib.ts'
// import { addResponseEventListener } from '@/utils/webRtcVideo.js'
import { Config, PixelStreaming } from '@epicgames-ps/lib-pixelstreamingfrontend-ue5.4'
import { Application, PixelStreamingApplicationStyle } from '@epicgames-ps/lib-pixelstreamingfrontend-ui-ue5.4'

// import useStore from '@/store/index'
// import { ElMessage } from 'element-plus'
/**
 * @description: 点击模型数据
 */
const clickModel = reactive({ data: {}, updateCustomPOILocationInScreen: {} })
let followVehicleId = null
export default function useUe() {
  // const { storeUe } = useStore()

  /**
   * @description: 添加点光源
   */
  const addSpotLight = () => {
    const reqularList = [
      'HZSQJT41EZMQZM+.*',
      'HZSQJT43EZMQZM+.*',
      'HZSQJT52EZMQZM+.*',
      'HZSQJT54EZMQZM+.*',
      'HZSQJT41EZMZZM+.*',
      'HZSQJT43EZMZZM+.*',
      'HZSQJT52EZMZZM+.*',
      'HZSQJT54EZMZZM+.*'
    ]
    const obj = {
      list: reqularList.map((_) => {
        return {
          regular: _,
          pointLight: {
            isOn: true,
            relativePosition: { x: 0, y: 0, z: 0 }, // 距绑定模型的中心偏移
            castShadow: false, // 是否投射阴影
            intensity: 4000, // 光照强度
            lightColor: [255, 255, 255, 255], // 光源颜色
            attenuationRadius: 10000 // 光源衰减半径
          }
        }
      })
    }
    toUe5('addPointLightToModel', obj)
  }

  const initModel = () => {
    const modelRenderList = {
      models: [
        {
          modelId: 15443191, // 构件id
          color: [146, 148, 154, 0.3 * 255], // 非必填（有color的话是添加着色，没有color的话是取消着色）
          hide: false // 非必填,true为隐藏该构件，包括渲染和物理碰撞，false为取消隐藏
        }
      ]
    }
    toUe5('modelRender', modelRenderList)
    toUe5('weather', { type: 1 }) // (1：晴，2：少云，3：多云，4：阴，5：雾，6：小雨，7：雨，8：雷雨，9：小雪，10：雪，11：暴风雪

    // 调整太阳光亮度
    toUe5('skySetting', {
      sun: {
        sunLightColor: [255, 255, 255, 255], // 太阳光的颜色，默认白色
        sunLightIntensity: 6 // 浮点数，调整太阳光亮度
      },
      heightFog: {
        // 雾相关参数
        fogDensityClear: 0, // 天空中晴朗没有云时的雾密度,0则没有雾
        fogDensityCloudy: 0.114, // 天空多云时的雾密度
        fogDensityDistribution: 3.0, // 定义从晴朗到多云的分布曲线
        heightFogColorMultiplier: [255, 255, 255, 255], // 雾的颜色，会和环境光混合
        useVolumetricFog: false, // 启用/禁用在摄影机附近渲染的体积雾，其中雾可能会受到光源的影响，密度可能会随体积粒子的变化而变化。注意：“体积雾”与实时天光捕捉不兼容。
        fogStartDistanceWhenClear: 0.0, // 定义天空晴朗时雾（而不是体积雾）开始渲染的距离高度。
        fogStartDistanceWhenCloudyorFoggy: 0.0, // 定义天空多云或大雾时雾（而不是体积雾）开始渲染的距离高度。
        fogStartDistanceExponent: 1.0, // 定义两个起始距离变量之间的分布。
        fogColorStrengthDay: 1.0, // 控制白天雾向内散射颜色的强度，以及源自雾颜色的云束的颜色。
        fogColorStrengthNight: 1.0 // 控制夜晚雾向内散射颜色的强度，以及源自雾颜色的云束的颜色。
      }
    })
  }

  const handleEvent = (e) => {
    const data = JSON.parse(e)
    console.log('UE返回数据-------------------', data)
    if (data.mouseEvent) {
      clickModel.data = data.mouseEvent
      if (clickModel.data?.objectType === 'vehicle') {
        const object = clickModel.data.object
        if (object.iD === followVehicleId) {
          toUe5('stopFollowVehicle', {})
          followVehicleId = null
        } else {
          followVehicleId = object.iD
          toUe5('followVehicle', { vehicleId: object.iD, offLocation: { x: -1000, y: 0, z: 0 } })
        }
      }
    } else if (data.loadFinish) {
      lib.store().storeUe.setFinished()
      toUe5Viewpoint('默认')

      initModel()
      setTimeout(() => {
        toUe5('load3DTile', {
          id: 2, // 3DTile模型ID，由前端生成用于绑定控制
          isShow: true, // 是否显示模型
          // url: 'file:/D:/DZ_pddd/qj/3dtiles/tileset.json', // 加载模型的地址，可加载本地模型
          url: 'http://172.22.51.1:10000/3DTiles/tileset.json', // 加载模型的地址，可加载本地模型
          maximunScreenSpaceError: 4 // 加载精度，默认16，推荐4或8，越小精度越大
          // offRotation: { x: 0.14, y: 0, z: -0.25 },
          // offPosition: { x: -6.3, y: -13.5, z: 3 }
        })
      }, 2000)
      addSpotLight()
    } else if (data.updateCustomPOILocationInScreen) {
      const location = data.updateCustomPOILocationInScreen.location
      location.x = location.x * window.config.screenWidth
      location.y = location.y * window.config.screenHeight
      clickModel.updateCustomPOILocationInScreen = data.updateCustomPOILocationInScreen
    }
  }

  /**
   * @description: 初始化UE引擎
   * @return {*}
   */
  const initUE = () => {
    const PixelStreamingApplicationStyles = new PixelStreamingApplicationStyle()
    PixelStreamingApplicationStyles.applyStyleSheet()
    const config = new Config({
      useUrlParams: true
    })
    // 设置推流地址
    // config.setTextSetting("ss", "ws://" + '*************' + ':8080')
    config.setTextSetting('ss', window.config.wsUrl)
    // config.setTextSetting("ss", "ws://" + window.location.hostname + ':8080')
    // config.setFlagEnabled("AutoConnect", true);

    // 设置显示鼠标
    config.setFlagEnabled('HoveringMouse', true)
    // 自动播放
    config.setFlagEnabled('AutoPlayVideo', true)
    config.setFlagEnabled('StartVideoMuted', true)
    // 隐藏控制按钮
    config.setFlagEnabled('HideUI', true)
    // 画面在当前是视口内全屏
    // config.setFlagEnabled('MatchViewportRes', true)
    // 设置StreamerId
    // config.setOptionSettingValue('StreamerId', '1')
    // config.setOptionSettingValue('StreamerId', 'DefaultStreamer')

    const stream = new PixelStreaming(config)
    window['$stream'] = stream
    const application = new Application({
      stream,
      onColorModeChanged: (isLightMode) => PixelStreamingApplicationStyles.setColorMode(isLightMode)
    })
    // console.log("application", application);
    document.getElementById('player')?.appendChild(application.rootElement)
    // 默认进去点击 CLICK TO START 按钮
    document.getElementById('connectButton')?.click()
    document.getElementById('playButton')?.click()

    setTimeout(() => {
      const obj = {
        url: 'https://tech.suitbim.com/'
      }
      toUe5('login', obj)
      // toUe5('cesiumTerrain', { url: 'https://tech.suitbim.com/cim-data/model/QJTerrain/layer.json' })
      toUe5('cesiumTerrain', { url: 'http://172.22.51.1:10000/QJTerrain/layer.json' })

      toUe5('bimProjectInfo', bimProjectInfo)
      // setTimeout(() => {
      toUe5('showModelByView', viewInfo)
      // }, 200)
      // setTimeout(() => {
      toUe5('terrainBurrow', terrainBurrowInfo) // 挖洞
      // }, 500)
      toUe5('laneInfo', laneInfo)
      // setTimeout(() => {
      toUe5Viewpoint('默认')
      // }, 1000)
      // toUe5Viewpoint('默认')

      stream.addResponseEventListener('handle_responses', (val) => {
        handleEvent(val)
      })
    }, 5000)

    // addResponseEventListener('ue', handleEvent)
  }
  return {
    /**
     * @description: 初始化UE引擎
     */
    initUE,
    /**
     * @description: 点击的模型数据
     */
    clickModel
  }
}
