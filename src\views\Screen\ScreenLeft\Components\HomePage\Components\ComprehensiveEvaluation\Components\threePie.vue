<template>
  <div class="three-pie-container">
    <div class="value">{{ value }}分</div>
    <div class="title">{{ chartTitle }}</div>
    <MyChart ref="myChartRef" class="chart" width="300px" height="130px" :option="options"></MyChart>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, watchEffect } from 'vue'

  import MyChart from '@Common/components/MyChart/index.vue'
  import lib from '@/utils/lib'
  import 'echarts-gl'
  const props = defineProps({
    data: {
      type: Array,
      default: () => []
    },
    chartTitle: {
      type: String,
      default: '车辆'
    },
    value: {
      type: String,
      default: '0'
    }
  })
  const options = ref(null)
  const color = ['#A1994A', '#679FDB', '#29CED6', '#3755CB', '#579F7C']

  function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
    // 计算
    let midRatio = (startRatio + endRatio) / 2

    let startRadian = startRatio * Math.PI * 2
    let endRadian = endRatio * Math.PI * 2
    let midRadian = midRatio * Math.PI * 2

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1

    // 返回曲面参数方程
    // 添加缩放因子，数值越大，饼图越大
    const scaleFactor = 1.85

    // 定义统一的高度值
    const uniformHeight = 3

    return {
      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32
      },

      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },

      x: function (u, v) {
        if (u < startRadian) {
          return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
        }
        if (u > endRadian) {
          return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
      },

      y: function (u, v) {
        if (u < startRadian) {
          return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
        }
        if (u > endRadian) {
          return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate * scaleFactor
      },

      z: function (u, v) {
        // 使用统一的高度值
        return Math.sin(v) > 0 ? uniformHeight : -uniformHeight
      }
    }
  }

  function getPie3D(pieData, internalDiameterRatio) {
    let series = []
    let sumValue = 0
    let startValue = 0
    let endValue = 0
    let legendData = []
    let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value

      let seriesItem = {
        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
        value: typeof pieData[i].value === 'undefined' ? `series${i}` : pieData[i].value,
        type: 'surface',
        parametric: true,
        wireframe: {
          show: false
        },
        pieData: pieData[i],
        pieStatus: {
          selected: false,
          hovered: false,
          k: k
        }
      }

      if (typeof pieData[i].itemStyle != 'undefined') {
        let itemStyle = {}

        typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
        typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null

        seriesItem.itemStyle = itemStyle
      }
      seriesItem.label = {
        formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
        backgroundColor: '#eee',
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 4,
        // shadowBlur:3,
        // shadowOffsetX: 2,
        // shadowOffsetY: 2,
        // shadowColor: '#999',
        // padding: [0, 7],
        rich: {
          a: {
            color: '#999',
            lineHeight: 22,
            align: 'center'
          },
          // abg: {
          //     backgroundColor: '#333',
          //     width: '100%',
          //     align: 'right',
          //     height: 22,
          //     borderRadius: [4, 4, 0, 0]
          // },
          hr: {
            borderColor: '#aaa',
            width: '100%',
            borderWidth: 0.5,
            height: 0
          },
          b: {
            fontSize: 16,
            lineHeight: 33
          },
          per: {
            color: '#eee',
            backgroundColor: '#334455',
            padding: [2, 4],
            borderRadius: 2
          }
        }
      }
      series.push(seriesItem)
    }
    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value

      series[i].pieData.startRatio = startValue / sumValue
      series[i].pieData.endRatio = endValue / sumValue
      series[i].parametricEquation = getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, series[i].pieData.value)

      startValue = endValue
      legendData.push(series[i].name)
    }

    // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
    series.push({
      name: 'mouseoutSeries',
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false
      },
      itemStyle: {
        opacity: 0
      },
      parametricEquation: {
        u: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        v: {
          min: 0,
          max: Math.PI,
          step: Math.PI / 20
        },
        x: function (u, v) {
          return Math.sin(v) * Math.sin(u) + Math.sin(u)
        },
        y: function (u, v) {
          return Math.sin(v) * Math.cos(u) + Math.cos(u)
        },
        z: function (u, v) {
          return Math.cos(v) > 0 ? 0.1 : -0.1
        }
      }
    })

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    let option = {
      legend: {
        show: false
      },
      xAxis3D: {
        min: -1,
        max: 1
      },
      yAxis3D: {
        min: -1,
        max: 1
      },
      zAxis3D: {
        min: -1,
        max: 1
      },
      grid3D: {
        left: '-28%',
        top: '-35%',
        width: '130%',
        height: '120%',
        show: false,
        boxHeight: 5,
        viewControl: {
          // 调整视角角度
          alpha: 28,
          beta: 30,
          // 启用旋转、缩放和平移功能，方便查看完整饼图
          rotateSensitivity: 0,
          zoomSensitivity: 0,
          panSensitivity: 0,
          autoRotate: false
        },
        //后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
        postEffect: {
          //配置这项会出现锯齿，请自己去查看官方配置有办法解决
          enable: true,
          bloom: {
            enable: true,
            bloomIntensity: 0
          },
          SSAO: {
            enable: false,
            quality: 'medium',
            radius: 2
          }
        }
      },
      series: series
    }
    return option
  }

  const myChartRef = ref(null)
  let isSelected = false
  watchEffect(() => {
    if (props.data.length === 0) return
    let totalCount = 0
    props.data.forEach((item) => {
      totalCount += item.value // 计算总数
    })
    const data = props.data.map((item, index) => {
      return {
        name: item.name,
        value: parseInt((item.value / totalCount) * 100),
        itemStyle: item.itemStyle
      }
    })
    options.value = getPie3D(data, 0.85)
  })
</script>

<style scoped lang="scss">
  .three-pie-container {
    position: relative;
    display: inline-block;
    width: 300px;

    // height: 285px;
    height: 120px;
    margin-top: 15px;
    .value {
      position: absolute;
      top: 10px;
      left: -42px;
      z-index: 3;
      width: 100%;
      font-family: 'Source Han Sans CN';
      font-size: 30px;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
    }
    .title {
      position: absolute;
      top: 47px;
      left: -42px;
      width: 100%;
      font-family: 'Source Han Sans CN';
      font-size: 16px;
      font-weight: 400;
      color: #9fd1ff;
      text-align: center;
    }
    .chart {
      background: url('@/assets/ScreenLeft/ComprehensiveEvaluation/pieBg.png') no-repeat;
      background-position: 0 0;
      background-size: 220px 113px;
    }
  }
</style>
