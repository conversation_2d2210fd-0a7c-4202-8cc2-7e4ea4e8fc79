/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./ComponentDatatype-dad47320","./defined-3b3eb2ba","./EllipsoidRhumbLine-d5e7f3db","./Geometry-a94d02e6","./Math-b5f4d889","./WebGLConstants-433debbf"],(function(e,t,n,r,a,i,o,s,u){"use strict";var x={exports:{}};function p(e,t,n){n=n||2;var r,a,i,o,s,u,x,p=t&&t.length,h=p?t[0]*n:e.length,f=l(e,0,h,n,!0),c=[];if(!f||f.next===f.prev)return c;if(p&&(f=function(e,t,n,r){var a,i,o,s=[];for(a=0,i=t.length;a<i;a++)(o=l(e,t[a]*r,a<i-1?t[a+1]*r:e.length,r,!1))===o.next&&(o.steiner=!0),s.push(w(o));for(s.sort(m),a=0;a<s.length;a++)n=C(s[a],n);return n}(e,t,f,n)),e.length>80*n){r=i=e[0],a=o=e[1];for(var d=n;d<h;d+=n)(s=e[d])<r&&(r=s),(u=e[d+1])<a&&(a=u),s>i&&(i=s),u>o&&(o=u);x=0!==(x=Math.max(i-r,o-a))?32767/x:0}return y(f,c,n,r,a,x,0),c}function l(e,t,n,r,a){var i,o;if(a===B(e,t,n,r)>0)for(i=t;i<n;i+=r)o=G(i,e[i],e[i+1],o);else for(i=n-r;i>=t;i-=r)o=G(i,e[i],e[i+1],o);return o&&M(o,o.next)&&(O(o),o=o.next),o}function h(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!M(r,r.next)&&0!==E(r.prev,r,r.next))r=r.next;else{if(O(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function y(e,t,n,r,a,i,o){if(e){!o&&i&&function(e,t,n,r){var a=e;do{0===a.z&&(a.z=b(a.x,a.y,t,n,r)),a.prevZ=a.prev,a.nextZ=a.next,a=a.next}while(a!==e);a.prevZ.nextZ=null,a.prevZ=null,function(e){var t,n,r,a,i,o,s,u,x=1;do{for(n=e,e=null,i=null,o=0;n;){for(o++,r=n,s=0,t=0;t<x&&(s++,r=r.nextZ);t++);for(u=x;s>0||u>0&&r;)0!==s&&(0===u||!r||n.z<=r.z)?(a=n,n=n.nextZ,s--):(a=r,r=r.nextZ,u--),i?i.nextZ=a:e=a,a.prevZ=i,i=a;n=r}i.nextZ=null,x*=2}while(o>1)}(a)}(e,r,a,i);for(var s,u,x=e;e.prev!==e.next;)if(s=e.prev,u=e.next,i?c(e,r,a,i):f(e))t.push(s.i/n|0),t.push(e.i/n|0),t.push(u.i/n|0),O(e),e=u.next,x=u.next;else if((e=u)===x){o?1===o?y(e=d(h(e),t,n),t,n,r,a,i,2):2===o&&v(e,t,n,r,a,i):y(h(e),t,n,r,a,i,1);break}}}function f(e){var t=e.prev,n=e,r=e.next;if(E(t,n,r)>=0)return!1;for(var a=t.x,i=n.x,o=r.x,s=t.y,u=n.y,x=r.y,p=a<i?a<o?a:o:i<o?i:o,l=s<u?s<x?s:x:u<x?u:x,h=a>i?a>o?a:o:i>o?i:o,y=s>u?s>x?s:x:u>x?u:x,f=r.next;f!==t;){if(f.x>=p&&f.x<=h&&f.y>=l&&f.y<=y&&A(a,s,i,u,o,x,f.x,f.y)&&E(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}function c(e,t,n,r){var a=e.prev,i=e,o=e.next;if(E(a,i,o)>=0)return!1;for(var s=a.x,u=i.x,x=o.x,p=a.y,l=i.y,h=o.y,y=s<u?s<x?s:x:u<x?u:x,f=p<l?p<h?p:h:l<h?l:h,c=s>u?s>x?s:x:u>x?u:x,d=p>l?p>h?p:h:l>h?l:h,v=b(y,f,t,n,r),m=b(c,d,t,n,r),C=e.prevZ,g=e.nextZ;C&&C.z>=v&&g&&g.z<=m;){if(C.x>=y&&C.x<=c&&C.y>=f&&C.y<=d&&C!==a&&C!==o&&A(s,p,u,l,x,h,C.x,C.y)&&E(C.prev,C,C.next)>=0)return!1;if(C=C.prevZ,g.x>=y&&g.x<=c&&g.y>=f&&g.y<=d&&g!==a&&g!==o&&A(s,p,u,l,x,h,g.x,g.y)&&E(g.prev,g,g.next)>=0)return!1;g=g.nextZ}for(;C&&C.z>=v;){if(C.x>=y&&C.x<=c&&C.y>=f&&C.y<=d&&C!==a&&C!==o&&A(s,p,u,l,x,h,C.x,C.y)&&E(C.prev,C,C.next)>=0)return!1;C=C.prevZ}for(;g&&g.z<=m;){if(g.x>=y&&g.x<=c&&g.y>=f&&g.y<=d&&g!==a&&g!==o&&A(s,p,u,l,x,h,g.x,g.y)&&E(g.prev,g,g.next)>=0)return!1;g=g.nextZ}return!0}function d(e,t,n){var r=e;do{var a=r.prev,i=r.next.next;!M(a,i)&&Z(a,r,r.next,i)&&R(a,i)&&R(i,a)&&(t.push(a.i/n|0),t.push(r.i/n|0),t.push(i.i/n|0),O(r),O(r.next),r=e=i),r=r.next}while(r!==e);return h(r)}function v(e,t,n,r,a,i){var o=e;do{for(var s=o.next.next;s!==o.prev;){if(o.i!==s.i&&S(o,s)){var u=D(o,s);return o=h(o,o.next),u=h(u,u.next),y(o,t,n,r,a,i,0),void y(u,t,n,r,a,i,0)}s=s.next}o=o.next}while(o!==e)}function m(e,t){return e.x-t.x}function C(e,t){var n=function(e,t){var n,r=t,a=e.x,i=e.y,o=-1/0;do{if(i<=r.y&&i>=r.next.y&&r.next.y!==r.y){var s=r.x+(i-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(s<=a&&s>o&&(o=s,n=r.x<r.next.x?r:r.next,s===a))return n}r=r.next}while(r!==t);if(!n)return null;var u,x=n,p=n.x,l=n.y,h=1/0;r=n;do{a>=r.x&&r.x>=p&&a!==r.x&&A(i<l?a:o,i,p,l,i<l?o:a,i,r.x,r.y)&&(u=Math.abs(i-r.y)/(a-r.x),R(r,e)&&(u<h||u===h&&(r.x>n.x||r.x===n.x&&g(n,r)))&&(n=r,h=u)),r=r.next}while(r!==x);return n}(e,t);if(!n)return t;var r=D(n,e);return h(r,r.next),h(n,n.next)}function g(e,t){return E(e.prev,e,t.prev)<0&&E(t.next,e,e.next)<0}function b(e,t,n,r,a){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*a|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*a|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function w(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function A(e,t,n,r,a,i,o,s){return(a-o)*(t-s)>=(e-o)*(i-s)&&(e-o)*(r-s)>=(n-o)*(t-s)&&(n-o)*(i-s)>=(a-o)*(r-s)}function S(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&Z(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(R(e,t)&&R(t,e)&&function(e,t){var n=e,r=!1,a=(e.x+t.x)/2,i=(e.y+t.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&a<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(E(e.prev,e,t.prev)||E(e,t.prev,t))||M(e,t)&&E(e.prev,e,e.next)>0&&E(t.prev,t,t.next)>0)}function E(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function M(e,t){return e.x===t.x&&e.y===t.y}function Z(e,t,n,r){var a=L(E(e,t,n)),i=L(E(e,t,r)),o=L(E(n,r,e)),s=L(E(n,r,t));return a!==i&&o!==s||(!(0!==a||!z(e,n,t))||(!(0!==i||!z(e,r,t))||(!(0!==o||!z(n,e,r))||!(0!==s||!z(n,t,r)))))}function z(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function L(e){return e>0?1:e<0?-1:0}function R(e,t){return E(e.prev,e,e.next)<0?E(e,t,e.next)>=0&&E(e,e.prev,t)>=0:E(e,t,e.prev)<0||E(e,e.next,t)<0}function D(e,t){var n=new T(e.i,e.x,e.y),r=new T(t.i,t.x,t.y),a=e.next,i=t.prev;return e.next=t,t.prev=e,n.next=a,a.prev=n,r.next=n,n.prev=r,i.next=r,r.prev=i,r}function G(e,t,n,r){var a=new T(e,t,n);return r?(a.next=r.next,a.prev=r,r.next.prev=a,r.next=a):(a.prev=a,a.next=a),a}function O(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function T(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function B(e,t,n,r){for(var a=0,i=t,o=n-r;i<n;i+=r)a+=(e[o]-e[i])*(e[i+1]+e[o+1]),o=i;return a}x.exports=p,x.exports.default=p,p.deviation=function(e,t,n,r){var a=t&&t.length,i=a?t[0]*n:e.length,o=Math.abs(B(e,0,i,n));if(a)for(var s=0,u=t.length;s<u;s++){var x=t[s]*n,p=s<u-1?t[s+1]*n:e.length;o-=Math.abs(B(e,x,p,n))}var l=0;for(s=0;s<r.length;s+=3){var h=r[s]*n,y=r[s+1]*n,f=r[s+2]*n;l+=Math.abs((e[h]-e[f])*(e[y+1]-e[h+1])-(e[h]-e[y])*(e[f+1]-e[h+1]))}return 0===o&&0===l?0:Math.abs((l-o)/o)},p.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,a=0;a<e.length;a++){for(var i=0;i<e[a].length;i++)for(var o=0;o<t;o++)n.vertices.push(e[a][i][o]);a>0&&(r+=e[a-1].length,n.holes.push(r))}return n};const W={CLOCKWISE:u.WebGLConstants.CW,COUNTER_CLOCKWISE:u.WebGLConstants.CCW,validate:function(e){return e===W.CLOCKWISE||e===W.COUNTER_CLOCKWISE}};var P=Object.freeze(W);const $=new t.Cartesian3,I=new t.Cartesian3,N={computeArea2D:function(e){const t=e.length;let n=0;for(let r=t-1,a=0;a<t;r=a++){const t=e[r],i=e[a];n+=t.x*i.y-i.x*t.y}return.5*n},computeWindingOrder2D:function(e){return N.computeArea2D(e)>0?P.COUNTER_CLOCKWISE:P.CLOCKWISE},triangulate:function(e,n){const r=t.Cartesian2.packArray(e);return x.exports(r,n,2)}},U=new t.Cartesian3,_=new t.Cartesian3,K=new t.Cartesian3,F=new t.Cartesian3,V=new t.Cartesian3,k=new t.Cartesian3,q=new t.Cartesian3,j=new t.Cartesian2,H=new t.Cartesian2,J=new t.Cartesian2,Q=new t.Cartesian2;N.computeSubdivision=function(e,n,i,u,x){x=a.defaultValue(x,s.CesiumMath.RADIANS_PER_DEGREE);const p=a.defined(u),l=i.slice(0);let h;const y=n.length,f=new Array(3*y),c=new Array(2*y);let d=0,v=0;for(h=0;h<y;h++){const e=n[h];if(f[d++]=e.x,f[d++]=e.y,f[d++]=e.z,p){const e=u[h];c[v++]=e.x,c[v++]=e.y}}const m=[],C={},g=e.maximumRadius,b=s.CesiumMath.chordLength(x,g),w=b*b;for(;l.length>0;){const e=l.pop(),n=l.pop(),r=l.pop(),i=t.Cartesian3.fromArray(f,3*r,U),o=t.Cartesian3.fromArray(f,3*n,_),s=t.Cartesian3.fromArray(f,3*e,K);let u,x,y;p&&(u=t.Cartesian2.fromArray(c,2*r,j),x=t.Cartesian2.fromArray(c,2*n,H),y=t.Cartesian2.fromArray(c,2*e,J));const d=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(i,F),g,F),v=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(o,V),g,V),b=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(s,k),g,k),A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(d,v,q)),S=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(v,b,q)),E=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(b,d,q)),M=Math.max(A,S,E);let Z,z,L;M>w?A===M?(Z=`${Math.min(r,n)} ${Math.max(r,n)}`,h=C[Z],a.defined(h)||(z=t.Cartesian3.add(i,o,q),t.Cartesian3.multiplyByScalar(z,.5,z),f.push(z.x,z.y,z.z),h=f.length/3-1,C[Z]=h,p&&(L=t.Cartesian2.add(u,x,Q),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),l.push(r,h,e),l.push(h,n,e)):S===M?(Z=`${Math.min(n,e)} ${Math.max(n,e)}`,h=C[Z],a.defined(h)||(z=t.Cartesian3.add(o,s,q),t.Cartesian3.multiplyByScalar(z,.5,z),f.push(z.x,z.y,z.z),h=f.length/3-1,C[Z]=h,p&&(L=t.Cartesian2.add(x,y,Q),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),l.push(n,h,r),l.push(h,e,r)):E===M&&(Z=`${Math.min(e,r)} ${Math.max(e,r)}`,h=C[Z],a.defined(h)||(z=t.Cartesian3.add(s,i,q),t.Cartesian3.multiplyByScalar(z,.5,z),f.push(z.x,z.y,z.z),h=f.length/3-1,C[Z]=h,p&&(L=t.Cartesian2.add(y,u,Q),t.Cartesian2.multiplyByScalar(L,.5,L),c.push(L.x,L.y))),l.push(e,h,n),l.push(h,r,n)):(m.push(r),m.push(n),m.push(e))}const A={attributes:{position:new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f})},indices:m,primitiveType:o.PrimitiveType.TRIANGLES};return p&&(A.attributes.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:c})),new o.Geometry(A)};const X=new n.Cartographic,Y=new n.Cartographic,ee=new n.Cartographic,te=new n.Cartographic;N.computeRhumbLineSubdivision=function(e,n,u,x,p){p=a.defaultValue(p,s.CesiumMath.RADIANS_PER_DEGREE);const l=a.defined(x),h=u.slice(0);let y;const f=n.length,c=new Array(3*f),d=new Array(2*f);let v=0,m=0;for(y=0;y<f;y++){const e=n[y];if(c[v++]=e.x,c[v++]=e.y,c[v++]=e.z,l){const e=x[y];d[m++]=e.x,d[m++]=e.y}}const C=[],g={},b=e.maximumRadius,w=s.CesiumMath.chordLength(p,b),A=new i.EllipsoidRhumbLine(void 0,void 0,e),S=new i.EllipsoidRhumbLine(void 0,void 0,e),E=new i.EllipsoidRhumbLine(void 0,void 0,e);for(;h.length>0;){const n=h.pop(),r=h.pop(),i=h.pop(),o=t.Cartesian3.fromArray(c,3*i,U),s=t.Cartesian3.fromArray(c,3*r,_),u=t.Cartesian3.fromArray(c,3*n,K);let x,p,f;l&&(x=t.Cartesian2.fromArray(d,2*i,j),p=t.Cartesian2.fromArray(d,2*r,H),f=t.Cartesian2.fromArray(d,2*n,J));const v=e.cartesianToCartographic(o,X),m=e.cartesianToCartographic(s,Y),b=e.cartesianToCartographic(u,ee);A.setEndPoints(v,m);const M=A.surfaceDistance;S.setEndPoints(m,b);const Z=S.surfaceDistance;E.setEndPoints(b,v);const z=E.surfaceDistance,L=Math.max(M,Z,z);let R,D,G,O,T;L>w?M===L?(R=`${Math.min(i,r)} ${Math.max(i,r)}`,y=g[R],a.defined(y)||(D=A.interpolateUsingFraction(.5,te),G=.5*(v.height+m.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),c.push(O.x,O.y,O.z),y=c.length/3-1,g[R]=y,l&&(T=t.Cartesian2.add(x,p,Q),t.Cartesian2.multiplyByScalar(T,.5,T),d.push(T.x,T.y))),h.push(i,y,n),h.push(y,r,n)):Z===L?(R=`${Math.min(r,n)} ${Math.max(r,n)}`,y=g[R],a.defined(y)||(D=S.interpolateUsingFraction(.5,te),G=.5*(m.height+b.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),c.push(O.x,O.y,O.z),y=c.length/3-1,g[R]=y,l&&(T=t.Cartesian2.add(p,f,Q),t.Cartesian2.multiplyByScalar(T,.5,T),d.push(T.x,T.y))),h.push(r,y,i),h.push(y,n,i)):z===L&&(R=`${Math.min(n,i)} ${Math.max(n,i)}`,y=g[R],a.defined(y)||(D=E.interpolateUsingFraction(.5,te),G=.5*(b.height+v.height),O=t.Cartesian3.fromRadians(D.longitude,D.latitude,G,e,q),c.push(O.x,O.y,O.z),y=c.length/3-1,g[R]=y,l&&(T=t.Cartesian2.add(f,x,Q),t.Cartesian2.multiplyByScalar(T,.5,T),d.push(T.x,T.y))),h.push(n,y,r),h.push(y,i,r)):(C.push(i),C.push(r),C.push(n))}const M={attributes:{position:new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:C,primitiveType:o.PrimitiveType.TRIANGLES};return l&&(M.attributes.st=new o.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:d})),new o.Geometry(M)},N.scaleToGeodeticHeight=function(e,r,i,o){i=a.defaultValue(i,n.Ellipsoid.WGS84);let s=$,u=I;if(r=a.defaultValue(r,0),o=a.defaultValue(o,!0),a.defined(e)){const n=e.length;for(let a=0;a<n;a+=3)t.Cartesian3.fromArray(e,a,u),o&&(u=i.scaleToGeodeticSurface(u,u)),0!==r&&(s=i.geodeticSurfaceNormal(u,s),t.Cartesian3.multiplyByScalar(s,r,s),t.Cartesian3.add(u,s,u)),e[a]=u.x,e[a+1]=u.y,e[a+2]=u.z}return e};var ne=N;e.PolygonPipeline=ne,e.WindingOrder=P}));
