define(["./when-1807bd8d","./Check-1951f41f","./Cartesian2-d051b2ac","./Rectangle-ab00c4f4","./BoundingSphere-f821e080","./Transforms-3ef1852a","./Matrix4-a50b021f","./RuntimeError-d0e509ca","./WebGLConstants-0cf30984","./ComponentDatatype-44dd9bc1","./GeometryAttribute-2104918f","./PrimitiveType-ec02f806","./GeometryAttributes-898891ba","./IndexDatatype-e1c63859","./GeometryOffsetAttribute-30ce4d84","./EllipsoidOutlineGeometry-e1e71118"],(function(e,i,t,r,n,o,s,a,d,l,c,u,p,m,f,y){"use strict";function G(i){var r=e.defaultValue(i.radius,1),n=new t.Cartesian3(r,r,r),o={radii:n,stackPartitions:i.stackPartitions,slicePartitions:i.slicePartitions,subdivisions:i.subdivisions};this._ellipsoidGeometry=new y.EllipsoidOutlineGeometry(o),this._workerName="createSphereOutlineGeometry"}G.packedLength=y.EllipsoidOutlineGeometry.packedLength,G.pack=function(e,t,r){return i.Check.typeOf.object("value",e),y.EllipsoidOutlineGeometry.pack(e._ellipsoidGeometry,t,r)};var b=new y.EllipsoidOutlineGeometry,k={radius:void 0,radii:new t.Cartesian3,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};function v(i,t){return e.defined(t)&&(i=G.unpack(i,t)),G.createGeometry(i)}return G.unpack=function(i,r,n){var o=y.EllipsoidOutlineGeometry.unpack(i,r,b);return k.stackPartitions=o._stackPartitions,k.slicePartitions=o._slicePartitions,k.subdivisions=o._subdivisions,e.defined(n)?(t.Cartesian3.clone(o._radii,k.radii),n._ellipsoidGeometry=new y.EllipsoidOutlineGeometry(k),n):(k.radius=o._radii.x,new G(k))},G.createGeometry=function(e){return y.EllipsoidOutlineGeometry.createGeometry(e._ellipsoidGeometry)},v}));