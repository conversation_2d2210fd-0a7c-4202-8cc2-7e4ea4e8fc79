/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.0.27
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-42ed7720","./Cartesian3-bb0e6278","./Rectangle-9bffefe4","./defined-3b3eb2ba","./EllipsoidTangentPlane-c2c2ef6e","./Math-b5f4d889","./Plane-a268aa11"],(function(a,t,e,n,r,i,s,o){"use strict";function c(a,t){this.center=e.Cartesian3.clone(r.defaultValue(a,e.Cartesian3.ZERO)),this.halfAxes=n.Matrix3.clone(r.defaultValue(t,n.Matrix3.ZERO))}c.packedLength=e.Cartesian3.packedLength+n.Matrix3.packedLength,c.pack=function(a,t,i){return i=r.defaultValue(i,0),e.Cartesian3.pack(a.center,t,i),n.Matrix3.pack(a.halfAxes,t,i+e.Cartesian3.packedLength),t},c.unpack=function(a,t,i){return t=r.defaultValue(t,0),r.defined(i)||(i=new c),e.Cartesian3.unpack(a,t,i.center),n.Matrix3.unpack(a,t+e.Cartesian3.packedLength,i.halfAxes),i};const C=new e.Cartesian3,u=new e.Cartesian3,l=new e.Cartesian3,d=new e.Cartesian3,h=new e.Cartesian3,x=new e.Cartesian3,m=new n.Matrix3,f={unitary:new n.Matrix3,diagonal:new n.Matrix3};c.fromPoints=function(a,t){if(r.defined(t)||(t=new c),!r.defined(a)||0===a.length)return t.halfAxes=n.Matrix3.ZERO,t.center=e.Cartesian3.ZERO,t;let i;const s=a.length,o=e.Cartesian3.clone(a[0],C);for(i=1;i<s;i++)e.Cartesian3.add(o,a[i],o);const M=1/s;e.Cartesian3.multiplyByScalar(o,M,o);let p,w=0,g=0,b=0,y=0,N=0,T=0;for(i=0;i<s;i++)p=e.Cartesian3.subtract(a[i],o,u),w+=p.x*p.x,g+=p.x*p.y,b+=p.x*p.z,y+=p.y*p.y,N+=p.y*p.z,T+=p.z*p.z;w*=M,g*=M,b*=M,y*=M,N*=M,T*=M;const O=m;O[0]=w,O[1]=g,O[2]=b,O[3]=g,O[4]=y,O[5]=N,O[6]=b,O[7]=N,O[8]=T;const A=n.Matrix3.computeEigenDecomposition(O,f),P=n.Matrix3.clone(A.unitary,t.halfAxes);let I=n.Matrix3.getColumn(P,0,d),R=n.Matrix3.getColumn(P,1,h),E=n.Matrix3.getColumn(P,2,x),S=-Number.MAX_VALUE,U=-Number.MAX_VALUE,L=-Number.MAX_VALUE,z=Number.MAX_VALUE,B=Number.MAX_VALUE,V=Number.MAX_VALUE;for(i=0;i<s;i++)p=a[i],S=Math.max(e.Cartesian3.dot(I,p),S),U=Math.max(e.Cartesian3.dot(R,p),U),L=Math.max(e.Cartesian3.dot(E,p),L),z=Math.min(e.Cartesian3.dot(I,p),z),B=Math.min(e.Cartesian3.dot(R,p),B),V=Math.min(e.Cartesian3.dot(E,p),V);I=e.Cartesian3.multiplyByScalar(I,.5*(z+S),I),R=e.Cartesian3.multiplyByScalar(R,.5*(B+U),R),E=e.Cartesian3.multiplyByScalar(E,.5*(V+L),E);const _=e.Cartesian3.add(I,R,t.center);e.Cartesian3.add(_,E,_);const k=l;return k.x=S-z,k.y=U-B,k.z=L-V,e.Cartesian3.multiplyByScalar(k,.5,k),n.Matrix3.multiplyByScale(t.halfAxes,k,t.halfAxes),t};const M=new e.Cartesian3,p=new e.Cartesian3;function w(a,t,i,s,o,C,u,l,d,h,x){r.defined(x)||(x=new c);const m=x.halfAxes;n.Matrix3.setColumn(m,0,t,m),n.Matrix3.setColumn(m,1,i,m),n.Matrix3.setColumn(m,2,s,m);let f=M;f.x=(o+C)/2,f.y=(u+l)/2,f.z=(d+h)/2;const w=p;w.x=(C-o)/2,w.y=(l-u)/2,w.z=(h-d)/2;const g=x.center;return f=n.Matrix3.multiplyByVector(m,f,f),e.Cartesian3.add(a,f,g),n.Matrix3.multiplyByScale(m,w,m),x}const g=new n.Cartographic,b=new e.Cartesian3,y=new n.Cartographic,N=new n.Cartographic,T=new n.Cartographic,O=new n.Cartographic,A=new n.Cartographic,P=new e.Cartesian3,I=new e.Cartesian3,R=new e.Cartesian3,E=new e.Cartesian3,S=new e.Cartesian3,U=new e.Cartesian2,L=new e.Cartesian2,z=new e.Cartesian2,B=new e.Cartesian2,V=new e.Cartesian2,_=new e.Cartesian3,k=new e.Cartesian3,W=new e.Cartesian3,X=new e.Cartesian3,q=new e.Cartesian2,D=new e.Cartesian3,j=new e.Cartesian3,Z=new e.Cartesian3,v=new o.Plane(e.Cartesian3.UNIT_X,0);c.fromRectangle=function(a,t,c,C,u){let l,d,h,x,m,f,M;if(t=r.defaultValue(t,0),c=r.defaultValue(c,0),C=r.defaultValue(C,n.Ellipsoid.WGS84),a.width<=s.CesiumMath.PI){const e=n.Rectangle.center(a,g),r=C.cartographicToCartesian(e,b),s=new i.EllipsoidTangentPlane(r,C);M=s.plane;const p=e.longitude,_=a.south<0&&a.north>0?0:e.latitude,k=n.Cartographic.fromRadians(p,a.north,c,y),W=n.Cartographic.fromRadians(a.west,a.north,c,N),X=n.Cartographic.fromRadians(a.west,_,c,T),q=n.Cartographic.fromRadians(a.west,a.south,c,O),D=n.Cartographic.fromRadians(p,a.south,c,A),j=C.cartographicToCartesian(k,P);let Z=C.cartographicToCartesian(W,I);const v=C.cartographicToCartesian(X,R);let Y=C.cartographicToCartesian(q,E);const G=C.cartographicToCartesian(D,S),F=s.projectPointToNearestOnPlane(j,U),H=s.projectPointToNearestOnPlane(Z,L),J=s.projectPointToNearestOnPlane(v,z),K=s.projectPointToNearestOnPlane(Y,B),Q=s.projectPointToNearestOnPlane(G,V);return l=Math.min(H.x,J.x,K.x),d=-l,x=Math.max(H.y,F.y),h=Math.min(K.y,Q.y),W.height=q.height=t,Z=C.cartographicToCartesian(W,I),Y=C.cartographicToCartesian(q,E),m=Math.min(o.Plane.getPointDistance(M,Z),o.Plane.getPointDistance(M,Y)),f=c,w(s.origin,s.xAxis,s.yAxis,s.zAxis,l,d,h,x,m,f,u)}const p=a.south>0,Y=a.north<0,G=p?a.south:Y?a.north:0,F=n.Rectangle.center(a,g).longitude,H=e.Cartesian3.fromRadians(F,G,c,C,_);H.z=0;const J=Math.abs(H.x)<s.CesiumMath.EPSILON10&&Math.abs(H.y)<s.CesiumMath.EPSILON10?e.Cartesian3.UNIT_X:e.Cartesian3.normalize(H,k),K=e.Cartesian3.UNIT_Z,Q=e.Cartesian3.cross(J,K,W);M=o.Plane.fromPointNormal(H,J,v);const $=e.Cartesian3.fromRadians(F+s.CesiumMath.PI_OVER_TWO,G,c,C,X);d=e.Cartesian3.dot(o.Plane.projectPointOntoPlane(M,$,q),Q),l=-d,x=e.Cartesian3.fromRadians(0,a.north,Y?t:c,C,D).z,h=e.Cartesian3.fromRadians(0,a.south,p?t:c,C,j).z;const aa=e.Cartesian3.fromRadians(a.east,G,c,C,Z);return m=o.Plane.getPointDistance(M,aa),f=0,w(H,Q,K,J,l,d,h,x,m,f,u)},c.fromTransformation=function(a,t){return r.defined(t)||(t=new c),t.center=n.Matrix4.getTranslation(a,t.center),t.halfAxes=n.Matrix4.getMatrix3(a,t.halfAxes),t.halfAxes=n.Matrix3.multiplyByScalar(t.halfAxes,.5,t.halfAxes),t},c.clone=function(a,t){if(r.defined(a))return r.defined(t)?(e.Cartesian3.clone(a.center,t.center),n.Matrix3.clone(a.halfAxes,t.halfAxes),t):new c(a.center,a.halfAxes)},c.intersectPlane=function(a,r){const i=a.center,s=r.normal,o=a.halfAxes,c=s.x,C=s.y,u=s.z,l=Math.abs(c*o[n.Matrix3.COLUMN0ROW0]+C*o[n.Matrix3.COLUMN0ROW1]+u*o[n.Matrix3.COLUMN0ROW2])+Math.abs(c*o[n.Matrix3.COLUMN1ROW0]+C*o[n.Matrix3.COLUMN1ROW1]+u*o[n.Matrix3.COLUMN1ROW2])+Math.abs(c*o[n.Matrix3.COLUMN2ROW0]+C*o[n.Matrix3.COLUMN2ROW1]+u*o[n.Matrix3.COLUMN2ROW2]),d=e.Cartesian3.dot(s,i)+r.distance;return d<=-l?t.Intersect.OUTSIDE:d>=l?t.Intersect.INSIDE:t.Intersect.INTERSECTING};const Y=new e.Cartesian3,G=new e.Cartesian3,F=new e.Cartesian3,H=new e.Cartesian3,J=new e.Cartesian3,K=new e.Cartesian3;c.distanceSquaredTo=function(a,t){const r=e.Cartesian3.subtract(t,a.center,M),i=a.halfAxes;let o=n.Matrix3.getColumn(i,0,Y),c=n.Matrix3.getColumn(i,1,G),C=n.Matrix3.getColumn(i,2,F);const u=e.Cartesian3.magnitude(o),l=e.Cartesian3.magnitude(c),d=e.Cartesian3.magnitude(C);let h=!0,x=!0,m=!0;u>0?e.Cartesian3.divideByScalar(o,u,o):h=!1,l>0?e.Cartesian3.divideByScalar(c,l,c):x=!1,d>0?e.Cartesian3.divideByScalar(C,d,C):m=!1;const f=!h+!x+!m;let p,w,g;if(1===f){let a=o;p=c,w=C,x?m||(a=C,w=o):(a=c,p=o),g=e.Cartesian3.cross(p,w,J),a===o?o=g:a===c?c=g:a===C&&(C=g)}else if(2===f){p=o,x?p=c:m&&(p=C);let a=e.Cartesian3.UNIT_Y;a.equalsEpsilon(p,s.CesiumMath.EPSILON3)&&(a=e.Cartesian3.UNIT_X),w=e.Cartesian3.cross(p,a,H),e.Cartesian3.normalize(w,w),g=e.Cartesian3.cross(p,w,J),e.Cartesian3.normalize(g,g),p===o?(c=w,C=g):p===c?(C=w,o=g):p===C&&(o=w,c=g)}else 3===f&&(o=e.Cartesian3.UNIT_X,c=e.Cartesian3.UNIT_Y,C=e.Cartesian3.UNIT_Z);const b=K;b.x=e.Cartesian3.dot(r,o),b.y=e.Cartesian3.dot(r,c),b.z=e.Cartesian3.dot(r,C);let y,N=0;return b.x<-u?(y=b.x+u,N+=y*y):b.x>u&&(y=b.x-u,N+=y*y),b.y<-l?(y=b.y+l,N+=y*y):b.y>l&&(y=b.y-l,N+=y*y),b.z<-d?(y=b.z+d,N+=y*y):b.z>d&&(y=b.z-d,N+=y*y),N};const Q=new e.Cartesian3,$=new e.Cartesian3;c.computePlaneDistances=function(a,i,s,o){r.defined(o)||(o=new t.Interval);let c=Number.POSITIVE_INFINITY,C=Number.NEGATIVE_INFINITY;const u=a.center,l=a.halfAxes,d=n.Matrix3.getColumn(l,0,Y),h=n.Matrix3.getColumn(l,1,G),x=n.Matrix3.getColumn(l,2,F),m=e.Cartesian3.add(d,h,Q);e.Cartesian3.add(m,x,m),e.Cartesian3.add(m,u,m);const f=e.Cartesian3.subtract(m,i,$);let M=e.Cartesian3.dot(s,f);return c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.add(u,d,m),e.Cartesian3.add(m,h,m),e.Cartesian3.subtract(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.add(u,d,m),e.Cartesian3.subtract(m,h,m),e.Cartesian3.add(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.add(u,d,m),e.Cartesian3.subtract(m,h,m),e.Cartesian3.subtract(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.subtract(u,d,m),e.Cartesian3.add(m,h,m),e.Cartesian3.add(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.subtract(u,d,m),e.Cartesian3.add(m,h,m),e.Cartesian3.subtract(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.subtract(u,d,m),e.Cartesian3.subtract(m,h,m),e.Cartesian3.add(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),e.Cartesian3.subtract(u,d,m),e.Cartesian3.subtract(m,h,m),e.Cartesian3.subtract(m,x,m),e.Cartesian3.subtract(m,i,f),M=e.Cartesian3.dot(s,f),c=Math.min(M,c),C=Math.max(M,C),o.start=c,o.stop=C,o};const aa=new e.Cartesian3,ta=new e.Cartesian3,ea=new e.Cartesian3;c.computeCorners=function(a,t){r.defined(t)||(t=[new e.Cartesian3,new e.Cartesian3,new e.Cartesian3,new e.Cartesian3,new e.Cartesian3,new e.Cartesian3,new e.Cartesian3,new e.Cartesian3]);const i=a.center,s=a.halfAxes,o=n.Matrix3.getColumn(s,0,aa),c=n.Matrix3.getColumn(s,1,ta),C=n.Matrix3.getColumn(s,2,ea);return e.Cartesian3.clone(i,t[0]),e.Cartesian3.subtract(t[0],o,t[0]),e.Cartesian3.subtract(t[0],c,t[0]),e.Cartesian3.subtract(t[0],C,t[0]),e.Cartesian3.clone(i,t[1]),e.Cartesian3.subtract(t[1],o,t[1]),e.Cartesian3.subtract(t[1],c,t[1]),e.Cartesian3.add(t[1],C,t[1]),e.Cartesian3.clone(i,t[2]),e.Cartesian3.subtract(t[2],o,t[2]),e.Cartesian3.add(t[2],c,t[2]),e.Cartesian3.subtract(t[2],C,t[2]),e.Cartesian3.clone(i,t[3]),e.Cartesian3.subtract(t[3],o,t[3]),e.Cartesian3.add(t[3],c,t[3]),e.Cartesian3.add(t[3],C,t[3]),e.Cartesian3.clone(i,t[4]),e.Cartesian3.add(t[4],o,t[4]),e.Cartesian3.subtract(t[4],c,t[4]),e.Cartesian3.subtract(t[4],C,t[4]),e.Cartesian3.clone(i,t[5]),e.Cartesian3.add(t[5],o,t[5]),e.Cartesian3.subtract(t[5],c,t[5]),e.Cartesian3.add(t[5],C,t[5]),e.Cartesian3.clone(i,t[6]),e.Cartesian3.add(t[6],o,t[6]),e.Cartesian3.add(t[6],c,t[6]),e.Cartesian3.subtract(t[6],C,t[6]),e.Cartesian3.clone(i,t[7]),e.Cartesian3.add(t[7],o,t[7]),e.Cartesian3.add(t[7],c,t[7]),e.Cartesian3.add(t[7],C,t[7]),t};const na=new n.Matrix3;c.computeTransformation=function(a,t){r.defined(t)||(t=new n.Matrix4);const e=a.center,i=n.Matrix3.multiplyByUniformScale(a.halfAxes,2,na);return n.Matrix4.fromRotationTranslation(i,e,t)};const ra=new t.BoundingSphere;c.isOccluded=function(a,e){const n=t.BoundingSphere.fromOrientedBoundingBox(a,ra);return!e.isBoundingSphereVisible(n)},c.prototype.intersectPlane=function(a){return c.intersectPlane(this,a)},c.prototype.distanceSquaredTo=function(a){return c.distanceSquaredTo(this,a)},c.prototype.computePlaneDistances=function(a,t,e){return c.computePlaneDistances(this,a,t,e)},c.prototype.computeCorners=function(a){return c.computeCorners(this,a)},c.prototype.computeTransformation=function(a){return c.computeTransformation(this,a)},c.prototype.isOccluded=function(a){return c.isOccluded(this,a)},c.equals=function(a,t){return a===t||r.defined(a)&&r.defined(t)&&e.Cartesian3.equals(a.center,t.center)&&n.Matrix3.equals(a.halfAxes,t.halfAxes)},c.prototype.clone=function(a){return c.clone(this,a)},c.prototype.equals=function(a){return c.equals(this,a)},a.OrientedBoundingBox=c}));
