var Module=function(){var t="undefined"!==typeof document&&document.currentScript?document.currentScript.src:void 0;return"undefined"!==typeof __filename&&(t=t||__filename),function(e){e=e||{};var r;e="undefined"!==typeof e?e:{};e["ready"]=new Promise((function(t,e){r=t,e}));var n=!1,p=!1;e["onRuntimeInitialized"]=function(){n=!0,p&&"function"===typeof e["onModuleLoaded"]&&e["onModuleLoaded"](e)},e["onModuleParsed"]=function(){p=!0,n&&"function"===typeof e["onModuleLoaded"]&&e["onModuleLoaded"](e)};var i,o={};for(i in e)e.hasOwnProperty(i)&&(o[i]=e[i]);var _=[],a="./this.program",c=function(t,e){throw e},u=!1,l=!1,s=!1,y=!1;u="object"===typeof window,l="function"===typeof importScripts,s="object"===typeof process&&"object"===typeof process.versions&&"string"===typeof process.versions.node,y=!u&&!s&&!l;var d,m,f,b,S="";function v(t){return e["locateFile"]?e["locateFile"](t,S):S+t}s?(S=l?require("path").dirname(S)+"/":__dirname+"/",d=function(t,e){return f||(f=require("fs")),b||(b=require("path")),t=b["normalize"](t),f["readFileSync"](t,e?null:"utf8")},m=function(t){var e=d(t,!0);return e.buffer||(e=new Uint8Array(e)),M(e.buffer),e},process["argv"].length>1&&(a=process["argv"][1].replace(/\\/g,"/")),_=process["argv"].slice(2),process["on"]("uncaughtException",(function(t){if(!(t instanceof lo))throw t})),process["on"]("unhandledRejection",bt),c=function(t){process["exit"](t)},e["inspect"]=function(){return"[Emscripten Module object]"}):y?("undefined"!=typeof read&&(d=function(t){return read(t)}),m=function(t){var e;return"function"===typeof readbuffer?new Uint8Array(readbuffer(t)):(e=read(t,"binary"),M("object"===typeof e),e)},"undefined"!=typeof scriptArgs?_=scriptArgs:"undefined"!=typeof arguments&&(_=arguments),"function"===typeof quit&&(c=function(t){quit(t)}),"undefined"!==typeof print&&("undefined"===typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!==typeof printErr?printErr:print)):(u||l)&&(l?S=self.location.href:document.currentScript&&(S=document.currentScript.src),t&&(S=t),S=0!==S.indexOf("blob:")?S.substr(0,S.lastIndexOf("/")+1):"",d=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},l&&(m=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),function(t,e,r){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?e(n.response):r()},n.onerror=r,n.send(null)});var h=e["print"]||console.log.bind(console),B=e["printErr"]||console.warn.bind(console);for(i in o)o.hasOwnProperty(i)&&(e[i]=o[i]);o=null,e["arguments"]&&(_=e["arguments"]),e["thisProgram"]&&(a=e["thisProgram"]),e["quit"]&&(c=e["quit"]);var g,L,P,I=0,j=function(t){I=t},G=function(){return I};e["wasmBinary"]&&(g=e["wasmBinary"]),e["noExitRuntime"]&&(L=e["noExitRuntime"]),"object"!==typeof WebAssembly&&B("no native wasm support detected");var x=new WebAssembly.Table({initial:1611,maximum:1611,element:"anyfunc"}),C=!1;function M(t,e){t||bt("Assertion failed: "+e)}function R(t){var r=e["_"+t];return M(r,"Cannot call unknown function "+t+", make sure it is exported"),r}function w(t,e,r,n,p){var i={string:function(t){var e=0;if(null!==t&&void 0!==t&&0!==t){var r=1+(t.length<<2);e=Ni(r),N(t,e,r)}return e},array:function(t){var e=Ni(t.length);return k(t,e),e}};function o(t){return"string"===e?A(t):"boolean"===e?Boolean(t):t}var _=R(t),a=[],c=0;if(n)for(var u=0;u<n.length;u++){var l=i[r[u]];l?(0===c&&(c=Ei()),a[u]=l(n[u])):a[u]=n[u]}var s=_.apply(null,a);return s=o(s),0!==c&&Oi(c),s}function V(t,e,r,n){r=r||[];var p=r.every((function(t){return"number"===t})),i="string"!==e;return i&&p&&!n?R(t):function(){return w(t,e,r,arguments,n)}}var T="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;function D(t,e,r){var n=e+r,p=e;while(t[p]&&!(p>=n))++p;if(p-e>16&&t.subarray&&T)return T.decode(t.subarray(e,p));var i="";while(e<p){var o=t[e++];if(128&o){var _=63&t[e++];if(192!=(224&o)){var a=63&t[e++];if(o=224==(240&o)?(15&o)<<12|_<<6|a:(7&o)<<18|_<<12|a<<6|63&t[e++],o<65536)i+=String.fromCharCode(o);else{var c=o-65536;i+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else i+=String.fromCharCode((31&o)<<6|_)}else i+=String.fromCharCode(o)}return i}function A(t,e){return t?D(H,t,e):""}function E(t,e,r,n){if(!(n>0))return 0;for(var p=r,i=r+n-1,o=0;o<t.length;++o){var _=t.charCodeAt(o);if(_>=55296&&_<=57343){var a=t.charCodeAt(++o);_=65536+((1023&_)<<10)|1023&a}if(_<=127){if(r>=i)break;e[r++]=_}else if(_<=2047){if(r+1>=i)break;e[r++]=192|_>>6,e[r++]=128|63&_}else if(_<=65535){if(r+2>=i)break;e[r++]=224|_>>12,e[r++]=128|_>>6&63,e[r++]=128|63&_}else{if(r+3>=i)break;e[r++]=240|_>>18,e[r++]=128|_>>12&63,e[r++]=128|_>>6&63,e[r++]=128|63&_}}return e[r]=0,r-p}function N(t,e,r){return E(t,H,e,r)}function O(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&t.charCodeAt(++r)),n<=127?++e:e+=n<=2047?2:n<=65535?3:4}return e}function k(t,e){W.set(t,e)}function z(t,e,r){for(var n=0;n<t.length;++n)W[e++>>0]=t.charCodeAt(n);r||(W[e>>0]=0)}var F,W,H,U,Y,q,X=65536;function J(t,e){return t%e>0&&(t+=e-t%e),t}function Z(t){F=t,e["HEAP8"]=W=new Int8Array(t),e["HEAP16"]=new Int16Array(t),e["HEAP32"]=U=new Int32Array(t),e["HEAPU8"]=H=new Uint8Array(t),e["HEAPU16"]=new Uint16Array(t),e["HEAPU32"]=new Uint32Array(t),e["HEAPF32"]=Y=new Float32Array(t),e["HEAPF64"]=q=new Float64Array(t)}var K=5328992,Q=85936,$=e["INITIAL_MEMORY"]||33554432;function tt(t){while(t.length>0){var r=t.shift();if("function"!=typeof r){var n=r.func;"number"===typeof n?void 0===r.arg?e["dynCall_v"](n):e["dynCall_vi"](n,r.arg):n(void 0===r.arg?null:r.arg)}else r(e)}}P=e["wasmMemory"]?e["wasmMemory"]:new WebAssembly.Memory({initial:$/X,maximum:2147483648/X}),P&&(F=P.buffer),$=F.byteLength,Z(F),U[Q>>2]=K;var et=[],rt=[],nt=[],pt=[];function it(){if(e["preRun"]){"function"==typeof e["preRun"]&&(e["preRun"]=[e["preRun"]]);while(e["preRun"].length)ut(e["preRun"].shift())}tt(et)}function ot(){!0,tt(rt)}function _t(){tt(nt)}function at(){!0}function ct(){if(e["postRun"]){"function"==typeof e["postRun"]&&(e["postRun"]=[e["postRun"]]);while(e["postRun"].length)lt(e["postRun"].shift())}tt(pt)}function ut(t){et.unshift(t)}function lt(t){pt.unshift(t)}var st=0,yt=null,dt=null;function mt(t){st++,e["monitorRunDependencies"]&&e["monitorRunDependencies"](st)}function ft(t){if(st--,e["monitorRunDependencies"]&&e["monitorRunDependencies"](st),0==st&&(null!==yt&&(clearInterval(yt),yt=null),dt)){var r=dt;dt=null,r()}}function bt(t){throw e["onAbort"]&&e["onAbort"](t),t+="",h(t),B(t),C=!0,1,t="abort("+t+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(t)}function St(t,e){return String.prototype.startsWith?t.startsWith(e):0===t.indexOf(e)}e["preloadedImages"]={},e["preloadedAudios"]={};var vt="data:application/octet-stream;base64,";function ht(t){return St(t,vt)}var Bt="file://";function gt(t){return St(t,Bt)}var Lt="materem.wasm";function Pt(){try{if(g)return new Uint8Array(g);if(m)return m(Lt);throw"both async and sync fetching of the wasm failed"}catch(B){bt(B)}}function It(){return g||!u&&!l||"function"!==typeof fetch||gt(Lt)?new Promise((function(t,e){t(Pt())})):fetch(Lt,{credentials:"same-origin"}).then((function(t){if(!t["ok"])throw"failed to load wasm binary file at '"+Lt+"'";return t["arrayBuffer"]()})).catch((function(){return Pt()}))}function jt(){var t={a:he};function r(t,r){var n=t.exports;e["asm"]=n,ft("wasm-instantiate")}function n(t){r(t["instance"])}function p(e){return It().then((function(e){return WebAssembly.instantiate(e,t)})).then(e,(function(t){B("failed to asynchronously prepare wasm: "+t),bt(t)}))}function i(){if(g||"function"!==typeof WebAssembly.instantiateStreaming||ht(Lt)||gt(Lt)||"function"!==typeof fetch)return p(n);fetch(Lt,{credentials:"same-origin"}).then((function(e){var r=WebAssembly.instantiateStreaming(e,t);return r.then(n,(function(t){B("wasm streaming compile failed: "+t),B("falling back to ArrayBuffer instantiation"),p(n)}))}))}if(mt("wasm-instantiate"),e["instantiateWasm"])try{var o=e["instantiateWasm"](t,r);return o}catch(_){return B("Module.instantiateWasm callback failed with error: "+_),!1}return i(),{}}function Gt(t){return oi(t)}ht(Lt)||(Lt=v(Lt)),rt.push({func:function(){Le()}});var xt={},Ct=[];function Mt(t){if(t){var e=xt[t];e.refcount++}}function Rt(t){if(!t||xt[t])return t;for(var e in xt)for(var r=+e,n=xt[r].adjusted,p=n.length,i=0;i<p;i++)if(n[i]===t)return r;return t}function wt(t){var e=xt[t];return e&&!e.caught&&(e.caught=!0,ci.uncaught_exceptions--),e&&(e.rethrown=!1),Ct.push(t),Mt(Rt(t)),t}var Vt=0;function Tt(t){return ii(t)}function Dt(t){if(t){var r=xt[t];r.refcount--,0!==r.refcount||r.rethrown||(r.destructor&&e["dynCall_ii"](r.destructor,t),delete xt[t],Tt(t))}}function At(){ai(0);var t=Ct.pop();t&&(Dt(Rt(t)),Vt=0)}function Et(){var t=Vt;if(!t)return 0|(j(0),0);var e=xt[t],r=e.type;if(!r)return 0|(j(0),t);var n=Array.prototype.slice.call(arguments),p=(li(r),86096);U[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&ui(n[i],r,t))return t=U[t>>2],e.adjusted.push(t),0|(j(n[i]),t);return t=U[t>>2],0|(j(r),t)}function Nt(){var t=Vt;if(!t)return 0|(j(0),0);var e=xt[t],r=e.type;if(!r)return 0|(j(0),t);var n=Array.prototype.slice.call(arguments),p=(li(r),86096);U[p>>2]=t,t=p;for(var i=0;i<n.length;i++)if(n[i]&&ui(n[i],r,t))return t=U[t>>2],e.adjusted.push(t),0|(j(n[i]),t);return t=U[t>>2],0|(j(r),t)}function Ot(){var t=Ct.pop();throw t=Rt(t),xt[t].rethrown||(Ct.push(t),xt[t].rethrown=!0),Vt=t,t}function kt(t,e,r){throw xt[t]={ptr:t,adjusted:[t],type:e,destructor:r,refcount:0,caught:!1,rethrown:!1},Vt=t,"uncaught_exception"in ci?ci.uncaught_exceptions++:ci.uncaught_exceptions=1,t}function zt(){return ci.uncaught_exceptions}function Ft(t){return U[_i()>>2]=t,t}function Wt(t,e){return Ft(63),-1}function Ht(t){throw Vt||(Vt=t),t}var Ut={mappings:{},buffers:[null,[],[]],printChar:function(t,e){var r=Ut.buffers[t];0===e||10===e?((1===t?h:B)(D(r,0)),r.length=0):r.push(e)},varargs:void 0,get:function(){Ut.varargs+=4;var t=U[Ut.varargs-4>>2];return t},getStr:function(t){var e=A(t);return e},get64:function(t,e){return t}};function Yt(t,e){if(-1===(0|t)||0===e)return-28;var r=Ut.mappings[t];return r?(e===r.len&&(Ut.mappings[t]=null,r.allocated&&ii(r.malloc)),0):0}function qt(t,e){return Yt(t,e)}function Xt(){bt()}function Jt(t,e,r){H.copyWithin(t,e,e+r)}function Zt(){return H.length}function Kt(t){try{return P.grow(t-F.byteLength+65535>>>16),Z(P.buffer),1}catch(e){}}function Qt(t){t>>>=0;var e=Zt(),r=65536,n=2147483648;if(t>n)return!1;for(var p=16777216,i=1;i<=4;i*=2){var o=e*(1+.2/i);o=Math.min(o,t+100663296);var _=Math.min(n,J(Math.max(p,t,o),r)),a=Kt(_);if(a)return!0}return!1}var $t={};function te(){return a||"./this.program"}function ee(){if(!ee.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"===typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:te()};for(var e in $t)t[e]=$t[e];var r=[];for(var e in t)r.push(e+"="+t[e]);ee.strings=r}return ee.strings}function re(t,e){var r=0;return ee().forEach((function(n,p){var i=e+r;U[t+4*p>>2]=i,z(n,i),r+=n.length+1})),0}function ne(t,e){var r=ee();U[t>>2]=r.length;var n=0;return r.forEach((function(t){n+=t.length+1})),U[e>>2]=n,0}function pe(t){yo(t)}function ie(t){return 0}function oe(t,e,r,n){var p=Ut.getStreamFromFD(t),i=Ut.doReadv(p,e,r);return U[n>>2]=i,0}function _e(t,e,r,n,p){}function ae(t,e,r,n){for(var p=0,i=0;i<r;i++){for(var o=U[e+8*i>>2],_=U[e+(8*i+4)>>2],a=0;a<_;a++)Ut.printChar(t,H[o+a]);p+=_}return U[n>>2]=p,0}function ce(){return 0|G()}function ue(t){return t}function le(t){return t%4===0&&(t%100!==0||t%400===0)}function se(t,e){for(var r=0,n=0;n<=e;r+=t[n++]);return r}var ye=[31,29,31,30,31,30,31,31,30,31,30,31],de=[31,28,31,30,31,30,31,31,30,31,30,31];function me(t,e){var r=new Date(t.getTime());while(e>0){var n=le(r.getFullYear()),p=r.getMonth(),i=(n?ye:de)[p];if(!(e>i-r.getDate()))return r.setDate(r.getDate()+e),r;e-=i-r.getDate()+1,r.setDate(1),p<11?r.setMonth(p+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1))}return r}function fe(t,e,r,n){var p=U[n+40>>2],i={tm_sec:U[n>>2],tm_min:U[n+4>>2],tm_hour:U[n+8>>2],tm_mday:U[n+12>>2],tm_mon:U[n+16>>2],tm_year:U[n+20>>2],tm_wday:U[n+24>>2],tm_yday:U[n+28>>2],tm_isdst:U[n+32>>2],tm_gmtoff:U[n+36>>2],tm_zone:p?A(p):""},o=A(r),_={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var a in _)o=o.replace(new RegExp(a,"g"),_[a]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],u=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(t,e,r){var n="number"===typeof t?t.toString():t||"";while(n.length<e)n=r[0]+n;return n}function s(t,e){return l(t,e,"0")}function y(t,e){function r(t){return t<0?-1:t>0?1:0}var n;return 0===(n=r(t.getFullYear()-e.getFullYear()))&&0===(n=r(t.getMonth()-e.getMonth()))&&(n=r(t.getDate()-e.getDate())),n}function d(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function m(t){var e=me(new Date(t.tm_year+1900,0,1),t.tm_yday),r=new Date(e.getFullYear(),0,4),n=new Date(e.getFullYear()+1,0,4),p=d(r),i=d(n);return y(p,e)<=0?y(i,e)<=0?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var f={"%a":function(t){return c[t.tm_wday].substring(0,3)},"%A":function(t){return c[t.tm_wday]},"%b":function(t){return u[t.tm_mon].substring(0,3)},"%B":function(t){return u[t.tm_mon]},"%C":function(t){var e=t.tm_year+1900;return s(e/100|0,2)},"%d":function(t){return s(t.tm_mday,2)},"%e":function(t){return l(t.tm_mday,2," ")},"%g":function(t){return m(t).toString().substring(2)},"%G":function(t){return m(t)},"%H":function(t){return s(t.tm_hour,2)},"%I":function(t){var e=t.tm_hour;return 0==e?e=12:e>12&&(e-=12),s(e,2)},"%j":function(t){return s(t.tm_mday+se(le(t.tm_year+1900)?ye:de,t.tm_mon-1),3)},"%m":function(t){return s(t.tm_mon+1,2)},"%M":function(t){return s(t.tm_min,2)},"%n":function(){return"\n"},"%p":function(t){return t.tm_hour>=0&&t.tm_hour<12?"AM":"PM"},"%S":function(t){return s(t.tm_sec,2)},"%t":function(){return"\t"},"%u":function(t){return t.tm_wday||7},"%U":function(t){var e=new Date(t.tm_year+1900,0,1),r=0===e.getDay()?e:me(e,7-e.getDay()),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=se(le(n.getFullYear())?ye:de,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%V":function(t){var e,r=new Date(t.tm_year+1900,0,4),n=new Date(t.tm_year+1901,0,4),p=d(r),i=d(n),o=me(new Date(t.tm_year+1900,0,1),t.tm_yday);return y(o,p)<0?"53":y(i,o)<=0?"01":(e=p.getFullYear()<t.tm_year+1900?t.tm_yday+32-p.getDate():t.tm_yday+1-p.getDate(),s(Math.ceil(e/7),2))},"%w":function(t){return t.tm_wday},"%W":function(t){var e=new Date(t.tm_year,0,1),r=1===e.getDay()?e:me(e,0===e.getDay()?1:7-e.getDay()+1),n=new Date(t.tm_year+1900,t.tm_mon,t.tm_mday);if(y(r,n)<0){var p=se(le(n.getFullYear())?ye:de,n.getMonth()-1)-31,i=31-r.getDate(),o=i+p+n.getDate();return s(Math.ceil(o/7),2)}return 0===y(r,e)?"01":"00"},"%y":function(t){return(t.tm_year+1900).toString().substring(2)},"%Y":function(t){return t.tm_year+1900},"%z":function(t){var e=t.tm_gmtoff,r=e>=0;return e=Math.abs(e)/60,e=e/60*100+e%60,(r?"+":"-")+String("0000"+e).slice(-4)},"%Z":function(t){return t.tm_zone},"%%":function(){return"%"}};for(var a in f)o.indexOf(a)>=0&&(o=o.replace(new RegExp(a,"g"),f[a](i)));var b=ve(o,!1);return b.length>e?0:(k(b,t),b.length-1)}function be(t,e,r,n){return fe(t,e,r,n)}function Se(t){var e=Date.now()/1e3|0;return t&&(U[t>>2]=e),e}function ve(t,e,r){var n=r>0?r:O(t)+1,p=new Array(n),i=E(t,p,0,p.length);return e&&(p.length=i),p}var he={m:Gt,q:wt,t:At,b:Et,l:Nt,n:Tt,G:Ot,u:kt,V:zt,T:Wt,f:Ht,S:qt,J:Xt,Q:Jt,v:Qt,W:re,X:ne,C:pe,Y:ie,U:oe,K:_e,F:ae,a:ce,x:to,L:uo,c:Wi,E:eo,d:ki,g:Ui,I:Qi,o:Xi,M:po,y:Ji,p:Zi,H:io,O:$i,B:_o,i:zi,h:Fi,e:Hi,D:no,k:Yi,j:qi,s:Ki,r:oo,w:ao,N:ro,A:co,P:ue,memory:P,R:be,table:x,z:Se},Be=jt();e["asm"]=Be;var ge,Le=e["___wasm_call_ctors"]=function(){return(Le=e["___wasm_call_ctors"]=e["asm"]["Z"]).apply(null,arguments)},Pe=(e["___em_js__array_bounds_check_error"]=function(){return(e["___em_js__array_bounds_check_error"]=e["asm"]["_"]).apply(null,arguments)},e["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=function(){return(Pe=e["_emscripten_bind_MaterPrimitiveDecoder_MaterPrimitiveDecoder_0"]=e["asm"]["$"]).apply(null,arguments)}),Ie=e["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=function(){return(Ie=e["_emscripten_bind_MaterPrimitiveDecoder_Decode_2"]=e["asm"]["aa"]).apply(null,arguments)},je=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=function(){return(je=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtNum_0"]=e["asm"]["ba"]).apply(null,arguments)},Ge=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=function(){return(Ge=e["_emscripten_bind_MaterPrimitiveDecoder_GetPtVal_2"]=e["asm"]["ca"]).apply(null,arguments)},xe=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=function(){return(xe=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveUV_0"]=e["asm"]["da"]).apply(null,arguments)},Ce=e["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=function(){return(Ce=e["_emscripten_bind_MaterPrimitiveDecoder_GetUVVal_2"]=e["asm"]["ea"]).apply(null,arguments)},Me=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=function(){return(Me=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveNormal_0"]=e["asm"]["fa"]).apply(null,arguments)},Re=e["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=function(){return(Re=e["_emscripten_bind_MaterPrimitiveDecoder_GetNormalVal_2"]=e["asm"]["ga"]).apply(null,arguments)},we=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=function(){return(we=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveBatchId_0"]=e["asm"]["ha"]).apply(null,arguments)},Ve=e["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=function(){return(Ve=e["_emscripten_bind_MaterPrimitiveDecoder_GetBatchId_1"]=e["asm"]["ia"]).apply(null,arguments)},Te=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=function(){return(Te=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveMaterialId_0"]=e["asm"]["ja"]).apply(null,arguments)},De=e["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=function(){return(De=e["_emscripten_bind_MaterPrimitiveDecoder_GetMaterialId_1"]=e["asm"]["ka"]).apply(null,arguments)},Ae=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=function(){return(Ae=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveOutlineCoord_0"]=e["asm"]["la"]).apply(null,arguments)},Ee=e["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=function(){return(Ee=e["_emscripten_bind_MaterPrimitiveDecoder_GetOutlineCoord_1"]=e["asm"]["ma"]).apply(null,arguments)},Ne=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=function(){return(Ne=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndexNum_0"]=e["asm"]["na"]).apply(null,arguments)},Oe=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=function(){return(Oe=e["_emscripten_bind_MaterPrimitiveDecoder_GetIndex_1"]=e["asm"]["oa"]).apply(null,arguments)},ke=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=function(){return(ke=e["_emscripten_bind_MaterPrimitiveDecoder_IsHaveEdgeCheck_0"]=e["asm"]["pa"]).apply(null,arguments)},ze=e["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=function(){return(ze=e["_emscripten_bind_MaterPrimitiveDecoder_GetEdgeCheck_1"]=e["asm"]["qa"]).apply(null,arguments)},Fe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=function(){return(Fe=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceNum_0"]=e["asm"]["ra"]).apply(null,arguments)},We=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=function(){return(We=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceMatVal_3"]=e["asm"]["sa"]).apply(null,arguments)},He=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=function(){return(He=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchNum_0"]=e["asm"]["ta"]).apply(null,arguments)},Ue=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=function(){return(Ue=e["_emscripten_bind_MaterPrimitiveDecoder_GetInstanceBatchId_1"]=e["asm"]["ua"]).apply(null,arguments)},Ye=e["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=function(){return(Ye=e["_emscripten_bind_MaterPrimitiveDecoder___destroy___0"]=e["asm"]["va"]).apply(null,arguments)},qe=e["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=function(){return(qe=e["_emscripten_bind_LBPlanishAry_LBPlanishAry_0"]=e["asm"]["wa"]).apply(null,arguments)},Xe=e["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=function(){return(Xe=e["_emscripten_bind_LBPlanishAry_SetPlanishNum_1"]=e["asm"]["xa"]).apply(null,arguments)},Je=e["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=function(){return(Je=e["_emscripten_bind_LBPlanishAry_SetPlanishPtNum_2"]=e["asm"]["ya"]).apply(null,arguments)},Ze=e["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=function(){return(Ze=e["_emscripten_bind_LBPlanishAry_SetPlanishPtVal_4"]=e["asm"]["za"]).apply(null,arguments)},Ke=e["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=function(){return(Ke=e["_emscripten_bind_LBPlanishAry_SetPlanishBot_2"]=e["asm"]["Aa"]).apply(null,arguments)},Qe=e["_emscripten_bind_LBPlanishAry___destroy___0"]=function(){return(Qe=e["_emscripten_bind_LBPlanishAry___destroy___0"]=e["asm"]["Ba"]).apply(null,arguments)},$e=e["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=function(){return($e=e["_emscripten_bind_LBProj4Wrapper_LBProj4Wrapper_0"]=e["asm"]["Ca"]).apply(null,arguments)},tr=e["_emscripten_bind_LBProj4Wrapper_Init_2"]=function(){return(tr=e["_emscripten_bind_LBProj4Wrapper_Init_2"]=e["asm"]["Da"]).apply(null,arguments)},er=e["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=function(){return(er=e["_emscripten_bind_LBProj4Wrapper_TranformAry_2"]=e["asm"]["Ea"]).apply(null,arguments)},rr=e["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=function(){return(rr=e["_emscripten_bind_LBProj4Wrapper_InverseTranformAry_2"]=e["asm"]["Fa"]).apply(null,arguments)},nr=e["_emscripten_bind_LBProj4Wrapper___destroy___0"]=function(){return(nr=e["_emscripten_bind_LBProj4Wrapper___destroy___0"]=e["asm"]["Ga"]).apply(null,arguments)},pr=e["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=function(){return(pr=e["_emscripten_bind_LBSpaBody_LBSpaBody_0"]=e["asm"]["Ha"]).apply(null,arguments)},ir=e["_emscripten_bind_LBSpaBody_Init_2"]=function(){return(ir=e["_emscripten_bind_LBSpaBody_Init_2"]=e["asm"]["Ia"]).apply(null,arguments)},or=e["_emscripten_bind_LBSpaBody_GetTriangle_2"]=function(){return(or=e["_emscripten_bind_LBSpaBody_GetTriangle_2"]=e["asm"]["Ja"]).apply(null,arguments)},_r=e["_emscripten_bind_LBSpaBody_CheckReference_1"]=function(){return(_r=e["_emscripten_bind_LBSpaBody_CheckReference_1"]=e["asm"]["Ka"]).apply(null,arguments)},ar=e["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=function(){return(ar=e["_emscripten_bind_LBSpaBody_ComputeUnion_1"]=e["asm"]["La"]).apply(null,arguments)},cr=e["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=function(){return(cr=e["_emscripten_bind_LBSpaBody_ComputeIntersection_1"]=e["asm"]["Ma"]).apply(null,arguments)},ur=e["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=function(){return(ur=e["_emscripten_bind_LBSpaBody_ComputeDifference_1"]=e["asm"]["Na"]).apply(null,arguments)},lr=e["_emscripten_bind_LBSpaBody_GetVolume_0"]=function(){return(lr=e["_emscripten_bind_LBSpaBody_GetVolume_0"]=e["asm"]["Oa"]).apply(null,arguments)},sr=e["_emscripten_bind_LBSpaBody___destroy___0"]=function(){return(sr=e["_emscripten_bind_LBSpaBody___destroy___0"]=e["asm"]["Pa"]).apply(null,arguments)},yr=e["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=function(){return(yr=e["_emscripten_bind_LBSpaPrimitiveCluster_SetIndexMatrix_2"]=e["asm"]["Qa"]).apply(null,arguments)},dr=e["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=function(){return(dr=e["_emscripten_bind_LBSpaPrimitiveCluster_RemoveAllMatrix_0"]=e["asm"]["Ra"]).apply(null,arguments)},mr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=function(){return(mr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableIndexSelected_2"]=e["asm"]["Sa"]).apply(null,arguments)},fr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=function(){return(fr=e["_emscripten_bind_LBSpaPrimitiveCluster_EnableAllIndexSelected_1"]=e["asm"]["Ta"]).apply(null,arguments)},br=e["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=function(){return(br=e["_emscripten_bind_LBSpaPrimitiveCluster___destroy___0"]=e["asm"]["Ua"]).apply(null,arguments)},Sr=e["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=function(){return(Sr=e["_emscripten_bind_LBEdgeFormer_LBEdgeFormer_0"]=e["asm"]["Va"]).apply(null,arguments)},vr=e["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=function(){return(vr=e["_emscripten_bind_LBEdgeFormer_SetPtAry_2"]=e["asm"]["Wa"]).apply(null,arguments)},hr=e["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=function(){return(hr=e["_emscripten_bind_LBEdgeFormer_SetIndexAry_2"]=e["asm"]["Xa"]).apply(null,arguments)},Br=e["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=function(){return(Br=e["_emscripten_bind_LBEdgeFormer_FormEdge_1"]=e["asm"]["Ya"]).apply(null,arguments)},gr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=function(){return(gr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexArySize_0"]=e["asm"]["Za"]).apply(null,arguments)},Lr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=function(){return(Lr=e["_emscripten_bind_LBEdgeFormer_GetEdgeIndexAryVal_1"]=e["asm"]["_a"]).apply(null,arguments)},Pr=e["_emscripten_bind_LBEdgeFormer___destroy___0"]=function(){return(Pr=e["_emscripten_bind_LBEdgeFormer___destroy___0"]=e["asm"]["$a"]).apply(null,arguments)},Ir=e["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(Ir=e["_emscripten_bind_VoidPtr___destroy___0"]=e["asm"]["ab"]).apply(null,arguments)},jr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=function(){return(jr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetResultId_0"]=e["asm"]["bb"]).apply(null,arguments)},Gr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=function(){return(Gr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt0_0"]=e["asm"]["cb"]).apply(null,arguments)},xr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=function(){return(xr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPt1_0"]=e["asm"]["db"]).apply(null,arguments)},Cr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=function(){return(Cr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetSegPt_0"]=e["asm"]["eb"]).apply(null,arguments)},Mr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=function(){return(Mr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickPt_0"]=e["asm"]["fb"]).apply(null,arguments)},Rr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=function(){return(Rr=e["_emscripten_bind_LBSpaSelectSegResultItem_GetPickDist_0"]=e["asm"]["gb"]).apply(null,arguments)},wr=e["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=function(){return(wr=e["_emscripten_bind_LBSpaSelectSegResultItem___destroy___0"]=e["asm"]["hb"]).apply(null,arguments)},Vr=e["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=function(){return(Vr=e["_emscripten_bind_LBSpaVec2_LBSpaVec2_0"]=e["asm"]["ib"]).apply(null,arguments)},Tr=e["_emscripten_bind_LBSpaVec2_get_x_0"]=function(){return(Tr=e["_emscripten_bind_LBSpaVec2_get_x_0"]=e["asm"]["jb"]).apply(null,arguments)},Dr=e["_emscripten_bind_LBSpaVec2_set_x_1"]=function(){return(Dr=e["_emscripten_bind_LBSpaVec2_set_x_1"]=e["asm"]["kb"]).apply(null,arguments)},Ar=e["_emscripten_bind_LBSpaVec2_get_y_0"]=function(){return(Ar=e["_emscripten_bind_LBSpaVec2_get_y_0"]=e["asm"]["lb"]).apply(null,arguments)},Er=e["_emscripten_bind_LBSpaVec2_set_y_1"]=function(){return(Er=e["_emscripten_bind_LBSpaVec2_set_y_1"]=e["asm"]["mb"]).apply(null,arguments)},Nr=e["_emscripten_bind_LBSpaVec2___destroy___0"]=function(){return(Nr=e["_emscripten_bind_LBSpaVec2___destroy___0"]=e["asm"]["nb"]).apply(null,arguments)},Or=e["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=function(){return(Or=e["_emscripten_bind_LBSpaSelectResult_LBSpaSelectResult_0"]=e["asm"]["ob"]).apply(null,arguments)},kr=e["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=function(){return(kr=e["_emscripten_bind_LBSpaSelectResult_ClearAll_0"]=e["asm"]["pb"]).apply(null,arguments)},zr=e["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=function(){return(zr=e["_emscripten_bind_LBSpaSelectResult_GetResultIdSize_0"]=e["asm"]["qb"]).apply(null,arguments)},Fr=e["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=function(){return(Fr=e["_emscripten_bind_LBSpaSelectResult_GetResultId_1"]=e["asm"]["rb"]).apply(null,arguments)},Wr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=function(){return(Wr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElemSize_0"]=e["asm"]["sb"]).apply(null,arguments)},Hr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=function(){return(Hr=e["_emscripten_bind_LBSpaSelectResult_GetTriResultElem_1"]=e["asm"]["tb"]).apply(null,arguments)},Ur=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=function(){return(Ur=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElemSize_0"]=e["asm"]["ub"]).apply(null,arguments)},Yr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=function(){return(Yr=e["_emscripten_bind_LBSpaSelectResult_GetSegResultElem_1"]=e["asm"]["vb"]).apply(null,arguments)},qr=e["_emscripten_bind_LBSpaSelectResult___destroy___0"]=function(){return(qr=e["_emscripten_bind_LBSpaSelectResult___destroy___0"]=e["asm"]["wb"]).apply(null,arguments)},Xr=e["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=function(){return(Xr=e["_emscripten_bind_LBSpaVec4_LBSpaVec4_0"]=e["asm"]["xb"]).apply(null,arguments)},Jr=e["_emscripten_bind_LBSpaVec4_get_x_0"]=function(){return(Jr=e["_emscripten_bind_LBSpaVec4_get_x_0"]=e["asm"]["yb"]).apply(null,arguments)},Zr=e["_emscripten_bind_LBSpaVec4_set_x_1"]=function(){return(Zr=e["_emscripten_bind_LBSpaVec4_set_x_1"]=e["asm"]["zb"]).apply(null,arguments)},Kr=e["_emscripten_bind_LBSpaVec4_get_y_0"]=function(){return(Kr=e["_emscripten_bind_LBSpaVec4_get_y_0"]=e["asm"]["Ab"]).apply(null,arguments)},Qr=e["_emscripten_bind_LBSpaVec4_set_y_1"]=function(){return(Qr=e["_emscripten_bind_LBSpaVec4_set_y_1"]=e["asm"]["Bb"]).apply(null,arguments)},$r=e["_emscripten_bind_LBSpaVec4_get_z_0"]=function(){return($r=e["_emscripten_bind_LBSpaVec4_get_z_0"]=e["asm"]["Cb"]).apply(null,arguments)},tn=e["_emscripten_bind_LBSpaVec4_set_z_1"]=function(){return(tn=e["_emscripten_bind_LBSpaVec4_set_z_1"]=e["asm"]["Db"]).apply(null,arguments)},en=e["_emscripten_bind_LBSpaVec4_get_w_0"]=function(){return(en=e["_emscripten_bind_LBSpaVec4_get_w_0"]=e["asm"]["Eb"]).apply(null,arguments)},rn=e["_emscripten_bind_LBSpaVec4_set_w_1"]=function(){return(rn=e["_emscripten_bind_LBSpaVec4_set_w_1"]=e["asm"]["Fb"]).apply(null,arguments)},nn=e["_emscripten_bind_LBSpaVec4___destroy___0"]=function(){return(nn=e["_emscripten_bind_LBSpaVec4___destroy___0"]=e["asm"]["Gb"]).apply(null,arguments)},pn=e["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=function(){return(pn=e["_emscripten_bind_LBSpaGeoTool_LBSpaGeoTool_0"]=e["asm"]["Hb"]).apply(null,arguments)},on=e["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=function(){return(on=e["_emscripten_bind_LBSpaGeoTool_GetRaySegIntersection_6"]=e["asm"]["Ib"]).apply(null,arguments)},_n=e["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=function(){return(_n=e["_emscripten_bind_LBSpaGeoTool_GetTwoSegIntersection_5"]=e["asm"]["Jb"]).apply(null,arguments)},an=e["_emscripten_bind_LBSpaGeoTool___destroy___0"]=function(){return(an=e["_emscripten_bind_LBSpaGeoTool___destroy___0"]=e["asm"]["Kb"]).apply(null,arguments)},cn=e["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=function(){return(cn=e["_emscripten_bind_LBSpaMat_LBSpaMat_0"]=e["asm"]["Lb"]).apply(null,arguments)},un=e["_emscripten_bind_LBSpaMat_At_1"]=function(){return(un=e["_emscripten_bind_LBSpaMat_At_1"]=e["asm"]["Mb"]).apply(null,arguments)},ln=e["_emscripten_bind_LBSpaMat___destroy___0"]=function(){return(ln=e["_emscripten_bind_LBSpaMat___destroy___0"]=e["asm"]["Nb"]).apply(null,arguments)},sn=e["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=function(){return(sn=e["_emscripten_bind_LBSpaSerial_LBSpaSerial_0"]=e["asm"]["Ob"]).apply(null,arguments)},yn=e["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=function(){return(yn=e["_emscripten_bind_LBSpaSerial_WriteSpatial_1"]=e["asm"]["Pb"]).apply(null,arguments)},dn=e["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=function(){return(dn=e["_emscripten_bind_LBSpaSerial_WriteTriangle_1"]=e["asm"]["Qb"]).apply(null,arguments)},mn=e["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=function(){return(mn=e["_emscripten_bind_LBSpaSerial_GetBufferSize_0"]=e["asm"]["Rb"]).apply(null,arguments)},fn=e["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=function(){return(fn=e["_emscripten_bind_LBSpaSerial_GetBufferVal_1"]=e["asm"]["Sb"]).apply(null,arguments)},bn=e["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=function(){return(bn=e["_emscripten_bind_LBSpaSerial_ReadSpatial_2"]=e["asm"]["Tb"]).apply(null,arguments)},Sn=e["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=function(){return(Sn=e["_emscripten_bind_LBSpaSerial_ReadTriangle_2"]=e["asm"]["Ub"]).apply(null,arguments)},vn=e["_emscripten_bind_LBSpaSerial___destroy___0"]=function(){return(vn=e["_emscripten_bind_LBSpaSerial___destroy___0"]=e["asm"]["Vb"]).apply(null,arguments)},hn=e["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=function(){return(hn=e["_emscripten_bind_LBSpaMgr_LBSpaMgr_0"]=e["asm"]["Wb"]).apply(null,arguments)},Bn=e["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=function(){return(Bn=e["_emscripten_bind_LBSpaMgr_CreateTriangleSpatial_1"]=e["asm"]["Xb"]).apply(null,arguments)},gn=e["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=function(){return(gn=e["_emscripten_bind_LBSpaMgr_CreateStepLineSpatial_1"]=e["asm"]["Yb"]).apply(null,arguments)},Ln=e["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=function(){return(Ln=e["_emscripten_bind_LBSpaMgr_CreatePrimitiveCluster_1"]=e["asm"]["Zb"]).apply(null,arguments)},Pn=e["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=function(){return(Pn=e["_emscripten_bind_LBSpaMgr_AddPrimitiveSpatial_1"]=e["asm"]["_b"]).apply(null,arguments)},In=e["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=function(){return(In=e["_emscripten_bind_LBSpaMgr_GetPrimitiveCluster_1"]=e["asm"]["$b"]).apply(null,arguments)},jn=e["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=function(){return(jn=e["_emscripten_bind_LBSpaMgr_EnablePrimitiveSelected_2"]=e["asm"]["ac"]).apply(null,arguments)},Gn=e["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=function(){return(Gn=e["_emscripten_bind_LBSpaMgr_IsPrimitiveSelected_1"]=e["asm"]["bc"]).apply(null,arguments)},xn=e["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=function(){return(xn=e["_emscripten_bind_LBSpaMgr_EnableAllPrimitiveSelected_1"]=e["asm"]["cc"]).apply(null,arguments)},Cn=e["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=function(){return(Cn=e["_emscripten_bind_LBSpaMgr_SetPrimitiveSpatialMat_2"]=e["asm"]["dc"]).apply(null,arguments)},Mn=e["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=function(){return(Mn=e["_emscripten_bind_LBSpaMgr_RemovePrimitiveSpatial_1"]=e["asm"]["ec"]).apply(null,arguments)},Rn=e["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=function(){return(Rn=e["_emscripten_bind_LBSpaMgr_RemoveAllPrimitiveSpatial_0"]=e["asm"]["fc"]).apply(null,arguments)},wn=e["_emscripten_bind_LBSpaMgr_Select_2"]=function(){return(wn=e["_emscripten_bind_LBSpaMgr_Select_2"]=e["asm"]["gc"]).apply(null,arguments)},Vn=e["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=function(){return(Vn=e["_emscripten_bind_LBSpaMgr_GetNumOfRemoveItems_0"]=e["asm"]["hc"]).apply(null,arguments)},Tn=e["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=function(){return(Tn=e["_emscripten_bind_LBSpaMgr_GetRemoveItemAt_1"]=e["asm"]["ic"]).apply(null,arguments)},Dn=e["_emscripten_bind_LBSpaMgr___destroy___0"]=function(){return(Dn=e["_emscripten_bind_LBSpaMgr___destroy___0"]=e["asm"]["jc"]).apply(null,arguments)},An=e["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=function(){return(An=e["_emscripten_bind_LBSpaSkirtInfo_LBSpaSkirtInfo_0"]=e["asm"]["kc"]).apply(null,arguments)},En=e["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=function(){return(En=e["_emscripten_bind_LBSpaSkirtInfo_get_iPtSectIndex_0"]=e["asm"]["lc"]).apply(null,arguments)},Nn=e["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=function(){return(Nn=e["_emscripten_bind_LBSpaSkirtInfo_set_iPtSectIndex_1"]=e["asm"]["mc"]).apply(null,arguments)},On=e["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=function(){return(On=e["_emscripten_bind_LBSpaSkirtInfo_get_iIndexSectIndex_0"]=e["asm"]["nc"]).apply(null,arguments)},kn=e["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=function(){return(kn=e["_emscripten_bind_LBSpaSkirtInfo_set_iIndexSectIndex_1"]=e["asm"]["oc"]).apply(null,arguments)},zn=e["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=function(){return(zn=e["_emscripten_bind_LBSpaSkirtInfo___destroy___0"]=e["asm"]["pc"]).apply(null,arguments)},Fn=e["_emscripten_bind_LBDeal_LBDeal_0"]=function(){return(Fn=e["_emscripten_bind_LBDeal_LBDeal_0"]=e["asm"]["qc"]).apply(null,arguments)},Wn=e["_emscripten_bind_LBDeal_Init_4"]=function(){return(Wn=e["_emscripten_bind_LBDeal_Init_4"]=e["asm"]["rc"]).apply(null,arguments)},Hn=e["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=function(){return(Hn=e["_emscripten_bind_LBDeal_ComputeProjToCartesian_4"]=e["asm"]["sc"]).apply(null,arguments)},Un=e["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=function(){return(Un=e["_emscripten_bind_LBDeal_ComputeCartesianToProj_5"]=e["asm"]["tc"]).apply(null,arguments)},Yn=e["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=function(){return(Yn=e["_emscripten_bind_LBDeal_TranformDegreeToProj_3"]=e["asm"]["uc"]).apply(null,arguments)},qn=e["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=function(){return(qn=e["_emscripten_bind_LBDeal_TranformProjToDegree_3"]=e["asm"]["vc"]).apply(null,arguments)},Xn=e["_emscripten_bind_LBDeal___destroy___0"]=function(){return(Xn=e["_emscripten_bind_LBDeal___destroy___0"]=e["asm"]["wc"]).apply(null,arguments)},Jn=e["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=function(){return(Jn=e["_emscripten_bind_LBSpaSelectResultId_get_iPrimitiveId_0"]=e["asm"]["xc"]).apply(null,arguments)},Zn=e["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=function(){return(Zn=e["_emscripten_bind_LBSpaSelectResultId_set_iPrimitiveId_1"]=e["asm"]["yc"]).apply(null,arguments)},Kn=e["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=function(){return(Kn=e["_emscripten_bind_LBSpaSelectResultId_get_bCluster_0"]=e["asm"]["zc"]).apply(null,arguments)},Qn=e["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=function(){return(Qn=e["_emscripten_bind_LBSpaSelectResultId_set_bCluster_1"]=e["asm"]["Ac"]).apply(null,arguments)},$n=e["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=function(){return($n=e["_emscripten_bind_LBSpaSelectResultId_get_iBatchId_0"]=e["asm"]["Bc"]).apply(null,arguments)},tp=e["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=function(){return(tp=e["_emscripten_bind_LBSpaSelectResultId_set_iBatchId_1"]=e["asm"]["Cc"]).apply(null,arguments)},ep=e["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=function(){return(ep=e["_emscripten_bind_LBSpaSelectResultId___destroy___0"]=e["asm"]["Dc"]).apply(null,arguments)},rp=e["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=function(){return(rp=e["_emscripten_bind_LBSpaPrimitiveSpatial___destroy___0"]=e["asm"]["Ec"]).apply(null,arguments)},np=e["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=function(){return(np=e["_emscripten_bind_LBSpaSelectTriResultItem_GetResultId_0"]=e["asm"]["Fc"]).apply(null,arguments)},pp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=function(){return(pp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt0_0"]=e["asm"]["Gc"]).apply(null,arguments)},ip=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=function(){return(ip=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt1_0"]=e["asm"]["Hc"]).apply(null,arguments)},op=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=function(){return(op=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPt2_0"]=e["asm"]["Ic"]).apply(null,arguments)},_p=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=function(){return(_p=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickPt_0"]=e["asm"]["Jc"]).apply(null,arguments)},ap=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=function(){return(ap=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickNormal_0"]=e["asm"]["Kc"]).apply(null,arguments)},cp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=function(){return(cp=e["_emscripten_bind_LBSpaSelectTriResultItem_GetPickDist_0"]=e["asm"]["Lc"]).apply(null,arguments)},up=e["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=function(){return(up=e["_emscripten_bind_LBSpaSelectTriResultItem___destroy___0"]=e["asm"]["Mc"]).apply(null,arguments)},lp=e["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=function(){return(lp=e["_emscripten_bind_LBSpaVec_LBSpaVec_0"]=e["asm"]["Nc"]).apply(null,arguments)},sp=e["_emscripten_bind_LBSpaVec_get_x_0"]=function(){return(sp=e["_emscripten_bind_LBSpaVec_get_x_0"]=e["asm"]["Oc"]).apply(null,arguments)},yp=e["_emscripten_bind_LBSpaVec_set_x_1"]=function(){return(yp=e["_emscripten_bind_LBSpaVec_set_x_1"]=e["asm"]["Pc"]).apply(null,arguments)},dp=e["_emscripten_bind_LBSpaVec_get_y_0"]=function(){return(dp=e["_emscripten_bind_LBSpaVec_get_y_0"]=e["asm"]["Qc"]).apply(null,arguments)},mp=e["_emscripten_bind_LBSpaVec_set_y_1"]=function(){return(mp=e["_emscripten_bind_LBSpaVec_set_y_1"]=e["asm"]["Rc"]).apply(null,arguments)},fp=e["_emscripten_bind_LBSpaVec_get_z_0"]=function(){return(fp=e["_emscripten_bind_LBSpaVec_get_z_0"]=e["asm"]["Sc"]).apply(null,arguments)},bp=e["_emscripten_bind_LBSpaVec_set_z_1"]=function(){return(bp=e["_emscripten_bind_LBSpaVec_set_z_1"]=e["asm"]["Tc"]).apply(null,arguments)},Sp=e["_emscripten_bind_LBSpaVec___destroy___0"]=function(){return(Sp=e["_emscripten_bind_LBSpaVec___destroy___0"]=e["asm"]["Uc"]).apply(null,arguments)},vp=e["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=function(){return(vp=e["_emscripten_bind_LBSpaTriangle_LBSpaTriangle_0"]=e["asm"]["Vc"]).apply(null,arguments)},hp=e["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=function(){return(hp=e["_emscripten_bind_LBSpaTriangle_SetPtNum_3"]=e["asm"]["Wc"]).apply(null,arguments)},Bp=e["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=function(){return(Bp=e["_emscripten_bind_LBSpaTriangle_SetPtVal_4"]=e["asm"]["Xc"]).apply(null,arguments)},gp=e["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=function(){return(gp=e["_emscripten_bind_LBSpaTriangle_SetUVVal_3"]=e["asm"]["Yc"]).apply(null,arguments)},Lp=e["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=function(){return(Lp=e["_emscripten_bind_LBSpaTriangle_SetNormVal_4"]=e["asm"]["Zc"]).apply(null,arguments)},Pp=e["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=function(){return(Pp=e["_emscripten_bind_LBSpaTriangle_SetIndexNum_1"]=e["asm"]["_c"]).apply(null,arguments)},Ip=e["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=function(){return(Ip=e["_emscripten_bind_LBSpaTriangle_SetIndexVal_2"]=e["asm"]["$c"]).apply(null,arguments)},jp=e["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=function(){return(jp=e["_emscripten_bind_LBSpaTriangle_AddTrangle_1"]=e["asm"]["ad"]).apply(null,arguments)},Gp=e["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=function(){return(Gp=e["_emscripten_bind_LBSpaTriangle_GetPtNum_0"]=e["asm"]["bd"]).apply(null,arguments)},xp=e["_emscripten_bind_LBSpaTriangle_GetPt_1"]=function(){return(xp=e["_emscripten_bind_LBSpaTriangle_GetPt_1"]=e["asm"]["cd"]).apply(null,arguments)},Cp=e["_emscripten_bind_LBSpaTriangle_GetUV_1"]=function(){return(Cp=e["_emscripten_bind_LBSpaTriangle_GetUV_1"]=e["asm"]["dd"]).apply(null,arguments)},Mp=e["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=function(){return(Mp=e["_emscripten_bind_LBSpaTriangle_GetNorm_1"]=e["asm"]["ed"]).apply(null,arguments)},Rp=e["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=function(){return(Rp=e["_emscripten_bind_LBSpaTriangle_GetIndexNum_0"]=e["asm"]["fd"]).apply(null,arguments)},wp=e["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=function(){return(wp=e["_emscripten_bind_LBSpaTriangle_GetIndex_1"]=e["asm"]["gd"]).apply(null,arguments)},Vp=e["_emscripten_bind_LBSpaTriangle___destroy___0"]=function(){return(Vp=e["_emscripten_bind_LBSpaTriangle___destroy___0"]=e["asm"]["hd"]).apply(null,arguments)},Tp=e["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=function(){return(Tp=e["_emscripten_bind_LBSpaPrimitive_LBSpaPrimitive_0"]=e["asm"]["id"]).apply(null,arguments)},Dp=e["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=function(){return(Dp=e["_emscripten_bind_LBSpaPrimitive_SetPtValNum_2"]=e["asm"]["jd"]).apply(null,arguments)},Ap=e["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=function(){return(Ap=e["_emscripten_bind_LBSpaPrimitive_SetPtValVal_2"]=e["asm"]["kd"]).apply(null,arguments)},Ep=e["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=function(){return(Ep=e["_emscripten_bind_LBSpaPrimitive_SetBatchIdVal_2"]=e["asm"]["ld"]).apply(null,arguments)},Np=e["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=function(){return(Np=e["_emscripten_bind_LBSpaPrimitive_SetIndexNum_2"]=e["asm"]["md"]).apply(null,arguments)},Op=e["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=function(){return(Op=e["_emscripten_bind_LBSpaPrimitive_SetIndexVal_2"]=e["asm"]["nd"]).apply(null,arguments)},kp=e["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=function(){return(kp=e["_emscripten_bind_LBSpaPrimitive_SetEdgeCheckVal_2"]=e["asm"]["od"]).apply(null,arguments)},zp=e["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=function(){return(zp=e["_emscripten_bind_LBSpaPrimitive_InitIndexByPt_0"]=e["asm"]["pd"]).apply(null,arguments)},Fp=e["_emscripten_bind_LBSpaPrimitive___destroy___0"]=function(){return(Fp=e["_emscripten_bind_LBSpaPrimitive___destroy___0"]=e["asm"]["qd"]).apply(null,arguments)},Wp=e["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=function(){return(Wp=e["_emscripten_bind_LBSpaSelectCondition_LBSpaSelectCondition_0"]=e["asm"]["rd"]).apply(null,arguments)},Hp=e["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=function(){return(Hp=e["_emscripten_bind_LBSpaSelectCondition_SetBox_6"]=e["asm"]["sd"]).apply(null,arguments)},Up=e["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=function(){return(Up=e["_emscripten_bind_LBSpaSelectCondition_SetRay_9"]=e["asm"]["td"]).apply(null,arguments)},Yp=e["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=function(){return(Yp=e["_emscripten_bind_LBSpaSelectCondition_SetWedge_10"]=e["asm"]["ud"]).apply(null,arguments)},qp=e["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=function(){return(qp=e["_emscripten_bind_LBSpaSelectCondition_SetWedgeByBufferedPoints_3"]=e["asm"]["vd"]).apply(null,arguments)},Xp=e["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=function(){return(Xp=e["_emscripten_bind_LBSpaSelectCondition_ClearBuffer_0"]=e["asm"]["wd"]).apply(null,arguments)},Jp=e["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=function(){return(Jp=e["_emscripten_bind_LBSpaSelectCondition_AddBuffer_3"]=e["asm"]["xd"]).apply(null,arguments)},Zp=e["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=function(){return(Zp=e["_emscripten_bind_LBSpaSelectCondition___destroy___0"]=e["asm"]["yd"]).apply(null,arguments)},Kp=e["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=function(){return(Kp=e["_emscripten_bind_LBSpaBoxMgr_LBSpaBoxMgr_0"]=e["asm"]["zd"]).apply(null,arguments)},Qp=e["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=function(){return(Qp=e["_emscripten_bind_LBSpaBoxMgr_InsertBox_7"]=e["asm"]["Ad"]).apply(null,arguments)},$p=e["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=function(){return($p=e["_emscripten_bind_LBSpaBoxMgr_RemoveBox_1"]=e["asm"]["Bd"]).apply(null,arguments)},ti=e["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=function(){return(ti=e["_emscripten_bind_LBSpaBoxMgr_SetSelectBox_6"]=e["asm"]["Cd"]).apply(null,arguments)},ei=e["_emscripten_bind_LBSpaBoxMgr_Select_0"]=function(){return(ei=e["_emscripten_bind_LBSpaBoxMgr_Select_0"]=e["asm"]["Dd"]).apply(null,arguments)},ri=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=function(){return(ri=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedIdSize_0"]=e["asm"]["Ed"]).apply(null,arguments)},ni=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=function(){return(ni=e["_emscripten_bind_LBSpaBoxMgr_GetSelectedId_1"]=e["asm"]["Fd"]).apply(null,arguments)},pi=e["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=function(){return(pi=e["_emscripten_bind_LBSpaBoxMgr___destroy___0"]=e["asm"]["Gd"]).apply(null,arguments)},ii=e["_free"]=function(){return(ii=e["_free"]=e["asm"]["Hd"]).apply(null,arguments)},oi=e["_malloc"]=function(){return(oi=e["_malloc"]=e["asm"]["Id"]).apply(null,arguments)},_i=e["___errno_location"]=function(){return(_i=e["___errno_location"]=e["asm"]["Jd"]).apply(null,arguments)},ai=e["_setThrew"]=function(){return(ai=e["_setThrew"]=e["asm"]["Kd"]).apply(null,arguments)},ci=e["__ZSt18uncaught_exceptionv"]=function(){return(ci=e["__ZSt18uncaught_exceptionv"]=e["asm"]["Ld"]).apply(null,arguments)},ui=e["___cxa_can_catch"]=function(){return(ui=e["___cxa_can_catch"]=e["asm"]["Md"]).apply(null,arguments)},li=e["___cxa_is_pointer_type"]=function(){return(li=e["___cxa_is_pointer_type"]=e["asm"]["Nd"]).apply(null,arguments)},si=e["dynCall_v"]=function(){return(si=e["dynCall_v"]=e["asm"]["Od"]).apply(null,arguments)},yi=e["dynCall_vi"]=function(){return(yi=e["dynCall_vi"]=e["asm"]["Pd"]).apply(null,arguments)},di=e["dynCall_vii"]=function(){return(di=e["dynCall_vii"]=e["asm"]["Qd"]).apply(null,arguments)},mi=e["dynCall_viii"]=function(){return(mi=e["dynCall_viii"]=e["asm"]["Rd"]).apply(null,arguments)},fi=e["dynCall_viiii"]=function(){return(fi=e["dynCall_viiii"]=e["asm"]["Sd"]).apply(null,arguments)},bi=e["dynCall_viiiii"]=function(){return(bi=e["dynCall_viiiii"]=e["asm"]["Td"]).apply(null,arguments)},Si=e["dynCall_viiiiiii"]=function(){return(Si=e["dynCall_viiiiiii"]=e["asm"]["Ud"]).apply(null,arguments)},vi=e["dynCall_viiiiiiiiii"]=function(){return(vi=e["dynCall_viiiiiiiiii"]=e["asm"]["Vd"]).apply(null,arguments)},hi=e["dynCall_viiiiiiiiiii"]=function(){return(hi=e["dynCall_viiiiiiiiiii"]=e["asm"]["Wd"]).apply(null,arguments)},Bi=e["dynCall_viiiiiiiiiiiiiii"]=function(){return(Bi=e["dynCall_viiiiiiiiiiiiiii"]=e["asm"]["Xd"]).apply(null,arguments)},gi=e["dynCall_viidd"]=function(){return(gi=e["dynCall_viidd"]=e["asm"]["Yd"]).apply(null,arguments)},Li=e["dynCall_i"]=function(){return(Li=e["dynCall_i"]=e["asm"]["Zd"]).apply(null,arguments)},Pi=e["dynCall_ii"]=function(){return(Pi=e["dynCall_ii"]=e["asm"]["_d"]).apply(null,arguments)},Ii=e["dynCall_iii"]=function(){return(Ii=e["dynCall_iii"]=e["asm"]["$d"]).apply(null,arguments)},ji=e["dynCall_iiii"]=function(){return(ji=e["dynCall_iiii"]=e["asm"]["ae"]).apply(null,arguments)},Gi=e["dynCall_iiiii"]=function(){return(Gi=e["dynCall_iiiii"]=e["asm"]["be"]).apply(null,arguments)},xi=e["dynCall_iiiiii"]=function(){return(xi=e["dynCall_iiiiii"]=e["asm"]["ce"]).apply(null,arguments)},Ci=e["dynCall_iiiiiii"]=function(){return(Ci=e["dynCall_iiiiiii"]=e["asm"]["de"]).apply(null,arguments)},Mi=e["dynCall_iiiiiiii"]=function(){return(Mi=e["dynCall_iiiiiiii"]=e["asm"]["ee"]).apply(null,arguments)},Ri=e["dynCall_iiiiiiiii"]=function(){return(Ri=e["dynCall_iiiiiiiii"]=e["asm"]["fe"]).apply(null,arguments)},wi=e["dynCall_iiiiiiiiiiii"]=function(){return(wi=e["dynCall_iiiiiiiiiiii"]=e["asm"]["ge"]).apply(null,arguments)},Vi=e["dynCall_iiiiid"]=function(){return(Vi=e["dynCall_iiiiid"]=e["asm"]["he"]).apply(null,arguments)},Ti=e["dynCall_iiiidiii"]=function(){return(Ti=e["dynCall_iiiidiii"]=e["asm"]["ie"]).apply(null,arguments)},Di=e["dynCall_iidd"]=function(){return(Di=e["dynCall_iidd"]=e["asm"]["je"]).apply(null,arguments)},Ai=e["dynCall_diii"]=function(){return(Ai=e["dynCall_diii"]=e["asm"]["ke"]).apply(null,arguments)},Ei=e["stackSave"]=function(){return(Ei=e["stackSave"]=e["asm"]["le"]).apply(null,arguments)},Ni=e["stackAlloc"]=function(){return(Ni=e["stackAlloc"]=e["asm"]["me"]).apply(null,arguments)},Oi=e["stackRestore"]=function(){return(Oi=e["stackRestore"]=e["asm"]["ne"]).apply(null,arguments)};function ki(t,e,r){var n=Ei();try{return Ii(t,e,r)}catch(p){if(Oi(n),p!==p+0&&"longjmp"!==p)throw p;ai(1,0)}}function zi(t){var e=Ei();try{si(t)}catch(r){if(Oi(e),r!==r+0&&"longjmp"!==r)throw r;ai(1,0)}}function Fi(t,e){var r=Ei();try{yi(t,e)}catch(n){if(Oi(r),n!==n+0&&"longjmp"!==n)throw n;ai(1,0)}}function Wi(t,e){var r=Ei();try{return Pi(t,e)}catch(n){if(Oi(r),n!==n+0&&"longjmp"!==n)throw n;ai(1,0)}}function Hi(t,e,r){var n=Ei();try{di(t,e,r)}catch(p){if(Oi(n),p!==p+0&&"longjmp"!==p)throw p;ai(1,0)}}function Ui(t,e,r,n){var p=Ei();try{return ji(t,e,r,n)}catch(i){if(Oi(p),i!==i+0&&"longjmp"!==i)throw i;ai(1,0)}}function Yi(t,e,r,n){var p=Ei();try{mi(t,e,r,n)}catch(i){if(Oi(p),i!==i+0&&"longjmp"!==i)throw i;ai(1,0)}}function qi(t,e,r,n,p){var i=Ei();try{fi(t,e,r,n,p)}catch(o){if(Oi(i),o!==o+0&&"longjmp"!==o)throw o;ai(1,0)}}function Xi(t,e,r,n,p){var i=Ei();try{return Gi(t,e,r,n,p)}catch(o){if(Oi(i),o!==o+0&&"longjmp"!==o)throw o;ai(1,0)}}function Ji(t,e,r,n,p,i){var o=Ei();try{return xi(t,e,r,n,p,i)}catch(_){if(Oi(o),_!==_+0&&"longjmp"!==_)throw _;ai(1,0)}}function Zi(t,e,r,n,p,i,o){var _=Ei();try{return Ci(t,e,r,n,p,i,o)}catch(a){if(Oi(_),a!==a+0&&"longjmp"!==a)throw a;ai(1,0)}}function Ki(t,e,r,n,p,i){var o=Ei();try{bi(t,e,r,n,p,i)}catch(_){if(Oi(o),_!==_+0&&"longjmp"!==_)throw _;ai(1,0)}}function Qi(t,e,r,n,p,i,o,_){var a=Ei();try{return Ti(t,e,r,n,p,i,o,_)}catch(c){if(Oi(a),c!==c+0&&"longjmp"!==c)throw c;ai(1,0)}}function $i(t,e,r,n,p,i,o,_,a){var c=Ei();try{return Ri(t,e,r,n,p,i,o,_,a)}catch(u){if(Oi(c),u!==u+0&&"longjmp"!==u)throw u;ai(1,0)}}function to(t,e,r,n){var p=Ei();try{return Ai(t,e,r,n)}catch(i){if(Oi(p),i!==i+0&&"longjmp"!==i)throw i;ai(1,0)}}function eo(t,e,r,n){var p=Ei();try{return Di(t,e,r,n)}catch(i){if(Oi(p),i!==i+0&&"longjmp"!==i)throw i;ai(1,0)}}function ro(t,e,r,n,p,i,o,_,a,c,u,l){var s=Ei();try{hi(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Oi(s),y!==y+0&&"longjmp"!==y)throw y;ai(1,0)}}function no(t,e,r,n,p){var i=Ei();try{gi(t,e,r,n,p)}catch(o){if(Oi(i),o!==o+0&&"longjmp"!==o)throw o;ai(1,0)}}function po(t,e,r,n,p,i){var o=Ei();try{return Vi(t,e,r,n,p,i)}catch(_){if(Oi(o),_!==_+0&&"longjmp"!==_)throw _;ai(1,0)}}function io(t,e,r,n,p,i,o,_){var a=Ei();try{return Mi(t,e,r,n,p,i,o,_)}catch(c){if(Oi(a),c!==c+0&&"longjmp"!==c)throw c;ai(1,0)}}function oo(t,e,r,n,p,i,o,_){var a=Ei();try{Si(t,e,r,n,p,i,o,_)}catch(c){if(Oi(a),c!==c+0&&"longjmp"!==c)throw c;ai(1,0)}}function _o(t,e,r,n,p,i,o,_,a,c,u,l){var s=Ei();try{return wi(t,e,r,n,p,i,o,_,a,c,u,l)}catch(y){if(Oi(s),y!==y+0&&"longjmp"!==y)throw y;ai(1,0)}}function ao(t,e,r,n,p,i,o,_,a,c,u){var l=Ei();try{vi(t,e,r,n,p,i,o,_,a,c,u)}catch(s){if(Oi(l),s!==s+0&&"longjmp"!==s)throw s;ai(1,0)}}function co(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m){var f=Ei();try{Bi(t,e,r,n,p,i,o,_,a,c,u,l,s,y,d,m)}catch(b){if(Oi(f),b!==b+0&&"longjmp"!==b)throw b;ai(1,0)}}function uo(t){var e=Ei();try{return Li(t)}catch(r){if(Oi(e),r!==r+0&&"longjmp"!==r)throw r;ai(1,0)}}function lo(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function so(t){function n(){ge||(ge=!0,e["calledRun"]=!0,C||(ot(),_t(),r(e),e["onRuntimeInitialized"]&&e["onRuntimeInitialized"](),ct()))}t=t||_,st>0||(it(),st>0||(e["setStatus"]?(e["setStatus"]("Running..."),setTimeout((function(){setTimeout((function(){e["setStatus"]("")}),1),n()}),1)):n()))}function yo(t,r){r&&L&&0===t||(L||(C=!0,t,at(),e["onExit"]&&e["onExit"](t)),c(t,new lo(t)))}if(e["asm"]=Be,e["ccall"]=w,e["cwrap"]=V,dt=function t(){ge||so(),ge||(dt=t)},e["run"]=so,e["preInit"]){"function"==typeof e["preInit"]&&(e["preInit"]=[e["preInit"]]);while(e["preInit"].length>0)e["preInit"].pop()()}function mo(){}function fo(t){return(t||mo).__cache__}function bo(t,e){var r=fo(e),n=r[t];return n||(n=Object.create((e||mo).prototype),n.ptr=t,r[t]=n)}function So(t,e){return bo(t.ptr,e)}function vo(t){if(!t["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";t["__destroy__"](),delete fo(t.__class__)[t.ptr]}function ho(t,e){return t.ptr===e.ptr}function Bo(t){return t.ptr}function go(t){return t.__class__}L=!0,so(),mo.prototype=Object.create(mo.prototype),mo.prototype.constructor=mo,mo.prototype.__class__=mo,mo.__cache__={},e["WrapperObject"]=mo,e["getCache"]=fo,e["wrapPointer"]=bo,e["castObject"]=So,e["NULL"]=bo(0),e["destroy"]=vo,e["compare"]=ho,e["getPointer"]=Bo,e["getClass"]=go;var Lo={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(Lo.needed){for(var t=0;t<Lo.temps.length;t++)e["_free"](Lo.temps[t]);Lo.temps.length=0,e["_free"](Lo.buffer),Lo.buffer=0,Lo.size+=Lo.needed,Lo.needed=0}Lo.buffer||(Lo.size+=128,Lo.buffer=e["_malloc"](Lo.size),M(Lo.buffer)),Lo.pos=0},alloc:function(t,r){M(Lo.buffer);var n,p=r.BYTES_PER_ELEMENT,i=t.length*p;return i=i+7&-8,Lo.pos+i>=Lo.size?(M(i>0),Lo.needed+=i,n=e["_malloc"](i),Lo.temps.push(n)):(n=Lo.buffer+Lo.pos,Lo.pos+=i),n},copy:function(t,e,r){r>>>=0;var n=e.BYTES_PER_ELEMENT;switch(n){case 2:r>>>=1;break;case 4:r>>>=2;break;case 8:r>>>=3;break}for(var p=0;p<t.length;p++)e[r+p]=t[p]}};function Po(t){if("string"===typeof t){var e=ve(t),r=Lo.alloc(e,W);return Lo.copy(e,W,r),r}return t}function Io(t){if("object"===typeof t){var e=Lo.alloc(t,W);return Lo.copy(t,W,e),e}return t}function jo(t){if("object"===typeof t){var e=Lo.alloc(t,U);return Lo.copy(t,U,e),e}return t}function Go(t){if("object"===typeof t){var e=Lo.alloc(t,Y);return Lo.copy(t,Y,e),e}return t}function xo(t){if("object"===typeof t){var e=Lo.alloc(t,q);return Lo.copy(t,q,e),e}return t}function Co(){this.ptr=Pe(),fo(Co)[this.ptr]=this}function Mo(){this.ptr=qe(),fo(Mo)[this.ptr]=this}function Ro(){this.ptr=$e(),fo(Ro)[this.ptr]=this}function wo(){this.ptr=pr(),fo(wo)[this.ptr]=this}function Vo(){throw"cannot construct a LBSpaPrimitiveCluster, no constructor in IDL"}function To(){this.ptr=Sr(),fo(To)[this.ptr]=this}function Do(){throw"cannot construct a VoidPtr, no constructor in IDL"}function Ao(){throw"cannot construct a LBSpaSelectSegResultItem, no constructor in IDL"}function Eo(){this.ptr=Vr(),fo(Eo)[this.ptr]=this}function No(){this.ptr=Or(),fo(No)[this.ptr]=this}function Oo(){this.ptr=Xr(),fo(Oo)[this.ptr]=this}function ko(){this.ptr=pn(),fo(ko)[this.ptr]=this}function zo(){this.ptr=cn(),fo(zo)[this.ptr]=this}function Fo(){this.ptr=sn(),fo(Fo)[this.ptr]=this}function Wo(){this.ptr=hn(),fo(Wo)[this.ptr]=this}function Ho(){this.ptr=An(),fo(Ho)[this.ptr]=this}function Uo(){this.ptr=Fn(),fo(Uo)[this.ptr]=this}function Yo(){throw"cannot construct a LBSpaSelectResultId, no constructor in IDL"}function qo(){throw"cannot construct a LBSpaPrimitiveSpatial, no constructor in IDL"}function Xo(){throw"cannot construct a LBSpaSelectTriResultItem, no constructor in IDL"}function Jo(){this.ptr=lp(),fo(Jo)[this.ptr]=this}function Zo(){this.ptr=vp(),fo(Zo)[this.ptr]=this}function Ko(){this.ptr=Tp(),fo(Ko)[this.ptr]=this}function Qo(){this.ptr=Wp(),fo(Qo)[this.ptr]=this}function $o(){this.ptr=Kp(),fo($o)[this.ptr]=this}return Co.prototype=Object.create(mo.prototype),Co.prototype.constructor=Co,Co.prototype.__class__=Co,Co.__cache__={},e["MaterPrimitiveDecoder"]=Co,Co.prototype["Decode"]=Co.prototype.Decode=function(t,e){var r=this.ptr;return Lo.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),!!Ie(r,t,e)},Co.prototype["GetPtNum"]=Co.prototype.GetPtNum=function(){var t=this.ptr;return je(t)},Co.prototype["GetPtVal"]=Co.prototype.GetPtVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ge(r,t,e)},Co.prototype["IsHaveUV"]=Co.prototype.IsHaveUV=function(){var t=this.ptr;return!!xe(t)},Co.prototype["GetUVVal"]=Co.prototype.GetUVVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ce(r,t,e)},Co.prototype["IsHaveNormal"]=Co.prototype.IsHaveNormal=function(){var t=this.ptr;return!!Me(t)},Co.prototype["GetNormalVal"]=Co.prototype.GetNormalVal=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Re(r,t,e)},Co.prototype["IsHaveBatchId"]=Co.prototype.IsHaveBatchId=function(){var t=this.ptr;return!!we(t)},Co.prototype["GetBatchId"]=Co.prototype.GetBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ve(e,t)},Co.prototype["IsHaveMaterialId"]=Co.prototype.IsHaveMaterialId=function(){var t=this.ptr;return!!Te(t)},Co.prototype["GetMaterialId"]=Co.prototype.GetMaterialId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),De(e,t)},Co.prototype["IsHaveOutlineCoord"]=Co.prototype.IsHaveOutlineCoord=function(){var t=this.ptr;return!!Ae(t)},Co.prototype["GetOutlineCoord"]=Co.prototype.GetOutlineCoord=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ee(e,t)},Co.prototype["GetIndexNum"]=Co.prototype.GetIndexNum=function(){var t=this.ptr;return Ne(t)},Co.prototype["GetIndex"]=Co.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Oe(e,t)},Co.prototype["IsHaveEdgeCheck"]=Co.prototype.IsHaveEdgeCheck=function(){var t=this.ptr;return!!ke(t)},Co.prototype["GetEdgeCheck"]=Co.prototype.GetEdgeCheck=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),ze(e,t)},Co.prototype["GetInstanceNum"]=Co.prototype.GetInstanceNum=function(){var t=this.ptr;return Fe(t)},Co.prototype["GetInstanceMatVal"]=Co.prototype.GetInstanceMatVal=function(t,e,r){var n=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),We(n,t,e,r)},Co.prototype["GetInstanceBatchNum"]=Co.prototype.GetInstanceBatchNum=function(){var t=this.ptr;return He(t)},Co.prototype["GetInstanceBatchId"]=Co.prototype.GetInstanceBatchId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Ue(e,t)},Co.prototype["__destroy__"]=Co.prototype.__destroy__=function(){var t=this.ptr;Ye(t)},Mo.prototype=Object.create(mo.prototype),Mo.prototype.constructor=Mo,Mo.prototype.__class__=Mo,Mo.__cache__={},e["LBPlanishAry"]=Mo,Mo.prototype["SetPlanishNum"]=Mo.prototype.SetPlanishNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Xe(e,t)},Mo.prototype["SetPlanishPtNum"]=Mo.prototype.SetPlanishPtNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Je(r,t,e)},Mo.prototype["SetPlanishPtVal"]=Mo.prototype.SetPlanishPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),Ze(p,t,e,r,n)},Mo.prototype["SetPlanishBot"]=Mo.prototype.SetPlanishBot=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ke(r,t,e)},Mo.prototype["__destroy__"]=Mo.prototype.__destroy__=function(){var t=this.ptr;Qe(t)},Ro.prototype=Object.create(mo.prototype),Ro.prototype.constructor=Ro,Ro.prototype.__class__=Ro,Ro.__cache__={},e["LBProj4Wrapper"]=Ro,Ro.prototype["Init"]=Ro.prototype.Init=function(t,e){var r=this.ptr;return Lo.prepare(),t=t&&"object"===typeof t?t.ptr:Po(t),e=e&&"object"===typeof e?e.ptr:Po(e),!!tr(r,t,e)},Ro.prototype["TranformAry"]=Ro.prototype.TranformAry=function(t,e){var r=this.ptr;Lo.prepare(),"object"==typeof t&&(t=xo(t)),e&&"object"===typeof e&&(e=e.ptr),er(r,t,e)},Ro.prototype["InverseTranformAry"]=Ro.prototype.InverseTranformAry=function(t,e){var r=this.ptr;Lo.prepare(),"object"==typeof t&&(t=xo(t)),e&&"object"===typeof e&&(e=e.ptr),rr(r,t,e)},Ro.prototype["__destroy__"]=Ro.prototype.__destroy__=function(){var t=this.ptr;nr(t)},wo.prototype=Object.create(mo.prototype),wo.prototype.constructor=wo,wo.prototype.__class__=wo,wo.__cache__={},e["LBSpaBody"]=wo,wo.prototype["Init"]=wo.prototype.Init=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!ir(r,t,e)},wo.prototype["GetTriangle"]=wo.prototype.GetTriangle=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!or(r,t,e)},wo.prototype["CheckReference"]=wo.prototype.CheckReference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!_r(e,t)},wo.prototype["ComputeUnion"]=wo.prototype.ComputeUnion=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!ar(e,t)},wo.prototype["ComputeIntersection"]=wo.prototype.ComputeIntersection=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!cr(e,t)},wo.prototype["ComputeDifference"]=wo.prototype.ComputeDifference=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!ur(e,t)},wo.prototype["GetVolume"]=wo.prototype.GetVolume=function(){var t=this.ptr;return lr(t)},wo.prototype["__destroy__"]=wo.prototype.__destroy__=function(){var t=this.ptr;sr(t)},Vo.prototype=Object.create(mo.prototype),Vo.prototype.constructor=Vo,Vo.prototype.__class__=Vo,Vo.__cache__={},e["LBSpaPrimitiveCluster"]=Vo,Vo.prototype["SetIndexMatrix"]=Vo.prototype.SetIndexMatrix=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),yr(r,t,e)},Vo.prototype["RemoveAllMatrix"]=Vo.prototype.RemoveAllMatrix=function(){var t=this.ptr;dr(t)},Vo.prototype["EnableIndexSelected"]=Vo.prototype.EnableIndexSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),mr(r,t,e)},Vo.prototype["EnableAllIndexSelected"]=Vo.prototype.EnableAllIndexSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),fr(e,t)},Vo.prototype["__destroy__"]=Vo.prototype.__destroy__=function(){var t=this.ptr;br(t)},To.prototype=Object.create(mo.prototype),To.prototype.constructor=To,To.prototype.__class__=To,To.__cache__={},e["LBEdgeFormer"]=To,To.prototype["SetPtAry"]=To.prototype.SetPtAry=function(t,e){var r=this.ptr;Lo.prepare(),"object"==typeof t&&(t=xo(t)),e&&"object"===typeof e&&(e=e.ptr),vr(r,t,e)},To.prototype["SetIndexAry"]=To.prototype.SetIndexAry=function(t,e){var r=this.ptr;Lo.prepare(),"object"==typeof t&&(t=jo(t)),e&&"object"===typeof e&&(e=e.ptr),hr(r,t,e)},To.prototype["FormEdge"]=To.prototype.FormEdge=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Br(e,t)},To.prototype["GetEdgeIndexArySize"]=To.prototype.GetEdgeIndexArySize=function(){var t=this.ptr;return gr(t)},To.prototype["GetEdgeIndexAryVal"]=To.prototype.GetEdgeIndexAryVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Lr(e,t)},To.prototype["__destroy__"]=To.prototype.__destroy__=function(){var t=this.ptr;Pr(t)},Do.prototype=Object.create(mo.prototype),Do.prototype.constructor=Do,Do.prototype.__class__=Do,Do.__cache__={},e["VoidPtr"]=Do,Do.prototype["__destroy__"]=Do.prototype.__destroy__=function(){var t=this.ptr;Ir(t)},Ao.prototype=Object.create(mo.prototype),Ao.prototype.constructor=Ao,Ao.prototype.__class__=Ao,Ao.__cache__={},e["LBSpaSelectSegResultItem"]=Ao,Ao.prototype["GetResultId"]=Ao.prototype.GetResultId=function(){var t=this.ptr;return bo(jr(t),Yo)},Ao.prototype["GetPt0"]=Ao.prototype.GetPt0=function(){var t=this.ptr;return bo(Gr(t),Jo)},Ao.prototype["GetPt1"]=Ao.prototype.GetPt1=function(){var t=this.ptr;return bo(xr(t),Jo)},Ao.prototype["GetSegPt"]=Ao.prototype.GetSegPt=function(){var t=this.ptr;return bo(Cr(t),Jo)},Ao.prototype["GetPickPt"]=Ao.prototype.GetPickPt=function(){var t=this.ptr;return bo(Mr(t),Jo)},Ao.prototype["GetPickDist"]=Ao.prototype.GetPickDist=function(){var t=this.ptr;return Rr(t)},Ao.prototype["__destroy__"]=Ao.prototype.__destroy__=function(){var t=this.ptr;wr(t)},Eo.prototype=Object.create(mo.prototype),Eo.prototype.constructor=Eo,Eo.prototype.__class__=Eo,Eo.__cache__={},e["LBSpaVec2"]=Eo,Eo.prototype["get_x"]=Eo.prototype.get_x=function(){var t=this.ptr;return Tr(t)},Eo.prototype["set_x"]=Eo.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Dr(e,t)},Object.defineProperty(Eo.prototype,"x",{get:Eo.prototype.get_x,set:Eo.prototype.set_x}),Eo.prototype["get_y"]=Eo.prototype.get_y=function(){var t=this.ptr;return Ar(t)},Eo.prototype["set_y"]=Eo.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Er(e,t)},Object.defineProperty(Eo.prototype,"y",{get:Eo.prototype.get_y,set:Eo.prototype.set_y}),Eo.prototype["__destroy__"]=Eo.prototype.__destroy__=function(){var t=this.ptr;Nr(t)},No.prototype=Object.create(mo.prototype),No.prototype.constructor=No,No.prototype.__class__=No,No.__cache__={},e["LBSpaSelectResult"]=No,No.prototype["ClearAll"]=No.prototype.ClearAll=function(){var t=this.ptr;kr(t)},No.prototype["GetResultIdSize"]=No.prototype.GetResultIdSize=function(){var t=this.ptr;return zr(t)},No.prototype["GetResultId"]=No.prototype.GetResultId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Fr(e,t),Yo)},No.prototype["GetTriResultElemSize"]=No.prototype.GetTriResultElemSize=function(){var t=this.ptr;return Wr(t)},No.prototype["GetTriResultElem"]=No.prototype.GetTriResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Hr(e,t),Xo)},No.prototype["GetSegResultElemSize"]=No.prototype.GetSegResultElemSize=function(){var t=this.ptr;return Ur(t)},No.prototype["GetSegResultElem"]=No.prototype.GetSegResultElem=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Yr(e,t),Ao)},No.prototype["__destroy__"]=No.prototype.__destroy__=function(){var t=this.ptr;qr(t)},Oo.prototype=Object.create(mo.prototype),Oo.prototype.constructor=Oo,Oo.prototype.__class__=Oo,Oo.__cache__={},e["LBSpaVec4"]=Oo,Oo.prototype["get_x"]=Oo.prototype.get_x=function(){var t=this.ptr;return Jr(t)},Oo.prototype["set_x"]=Oo.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Zr(e,t)},Object.defineProperty(Oo.prototype,"x",{get:Oo.prototype.get_x,set:Oo.prototype.set_x}),Oo.prototype["get_y"]=Oo.prototype.get_y=function(){var t=this.ptr;return Kr(t)},Oo.prototype["set_y"]=Oo.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Qr(e,t)},Object.defineProperty(Oo.prototype,"y",{get:Oo.prototype.get_y,set:Oo.prototype.set_y}),Oo.prototype["get_z"]=Oo.prototype.get_z=function(){var t=this.ptr;return $r(t)},Oo.prototype["set_z"]=Oo.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),tn(e,t)},Object.defineProperty(Oo.prototype,"z",{get:Oo.prototype.get_z,set:Oo.prototype.set_z}),Oo.prototype["get_w"]=Oo.prototype.get_w=function(){var t=this.ptr;return en(t)},Oo.prototype["set_w"]=Oo.prototype.set_w=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),rn(e,t)},Object.defineProperty(Oo.prototype,"w",{get:Oo.prototype.get_w,set:Oo.prototype.set_w}),Oo.prototype["__destroy__"]=Oo.prototype.__destroy__=function(){var t=this.ptr;nn(t)},ko.prototype=Object.create(mo.prototype),ko.prototype.constructor=ko,ko.prototype.__class__=ko,ko.__cache__={},e["LBSpaGeoTool"]=ko,ko.prototype["GetRaySegIntersection"]=ko.prototype.GetRaySegIntersection=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),on(o,t,e,r,n,p,i)},ko.prototype["GetTwoSegIntersection"]=ko.prototype.GetTwoSegIntersection=function(t,e,r,n,p){var i=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),!!_n(i,t,e,r,n,p)},ko.prototype["__destroy__"]=ko.prototype.__destroy__=function(){var t=this.ptr;an(t)},zo.prototype=Object.create(mo.prototype),zo.prototype.constructor=zo,zo.prototype.__class__=zo,zo.__cache__={},e["LBSpaMat"]=zo,zo.prototype["At"]=zo.prototype.At=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(un(e,t),Oo)},zo.prototype["__destroy__"]=zo.prototype.__destroy__=function(){var t=this.ptr;ln(t)},Fo.prototype=Object.create(mo.prototype),Fo.prototype.constructor=Fo,Fo.prototype.__class__=Fo,Fo.__cache__={},e["LBSpaSerial"]=Fo,Fo.prototype["WriteSpatial"]=Fo.prototype.WriteSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),yn(e,t)},Fo.prototype["WriteTriangle"]=Fo.prototype.WriteTriangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),dn(e,t)},Fo.prototype["GetBufferSize"]=Fo.prototype.GetBufferSize=function(){var t=this.ptr;return mn(t)},Fo.prototype["GetBufferVal"]=Fo.prototype.GetBufferVal=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),fn(e,t)},Fo.prototype["ReadSpatial"]=Fo.prototype.ReadSpatial=function(t,e){var r=this.ptr;return Lo.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),bo(bn(r,t,e),qo)},Fo.prototype["ReadTriangle"]=Fo.prototype.ReadTriangle=function(t,e){var r=this.ptr;return Lo.prepare(),"object"==typeof t&&(t=Io(t)),e&&"object"===typeof e&&(e=e.ptr),bo(Sn(r,t,e),Zo)},Fo.prototype["__destroy__"]=Fo.prototype.__destroy__=function(){var t=this.ptr;vn(t)},Wo.prototype=Object.create(mo.prototype),Wo.prototype.constructor=Wo,Wo.prototype.__class__=Wo,Wo.__cache__={},e["LBSpaMgr"]=Wo,Wo.prototype["CreateTriangleSpatial"]=Wo.prototype.CreateTriangleSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Bn(e,t),qo)},Wo.prototype["CreateStepLineSpatial"]=Wo.prototype.CreateStepLineSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(gn(e,t),qo)},Wo.prototype["CreatePrimitiveCluster"]=Wo.prototype.CreatePrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Ln(e,t),Vo)},Wo.prototype["AddPrimitiveSpatial"]=Wo.prototype.AddPrimitiveSpatial=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),Pn(e,t)},Wo.prototype["GetPrimitiveCluster"]=Wo.prototype.GetPrimitiveCluster=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(In(e,t),Vo)},Wo.prototype["EnablePrimitiveSelected"]=Wo.prototype.EnablePrimitiveSelected=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),jn(r,t,e)},Wo.prototype["IsPrimitiveSelected"]=Wo.prototype.IsPrimitiveSelected=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),!!Gn(e,t)},Wo.prototype["EnableAllPrimitiveSelected"]=Wo.prototype.EnableAllPrimitiveSelected=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),xn(e,t)},Wo.prototype["SetPrimitiveSpatialMat"]=Wo.prototype.SetPrimitiveSpatialMat=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!Cn(r,t,e)},Wo.prototype["RemovePrimitiveSpatial"]=Wo.prototype.RemovePrimitiveSpatial=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Mn(e,t)},Wo.prototype["RemoveAllPrimitiveSpatial"]=Wo.prototype.RemoveAllPrimitiveSpatial=function(){var t=this.ptr;Rn(t)},Wo.prototype["Select"]=Wo.prototype.Select=function(t,e){var r=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),!!wn(r,t,e)},Wo.prototype["GetNumOfRemoveItems"]=Wo.prototype.GetNumOfRemoveItems=function(){var t=this.ptr;return Vn(t)},Wo.prototype["GetRemoveItemAt"]=Wo.prototype.GetRemoveItemAt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Tn(e,t),Yo)},Wo.prototype["__destroy__"]=Wo.prototype.__destroy__=function(){var t=this.ptr;Dn(t)},Ho.prototype=Object.create(mo.prototype),Ho.prototype.constructor=Ho,Ho.prototype.__class__=Ho,Ho.__cache__={},e["LBSpaSkirtInfo"]=Ho,Ho.prototype["get_iPtSectIndex"]=Ho.prototype.get_iPtSectIndex=function(){var t=this.ptr;return En(t)},Ho.prototype["set_iPtSectIndex"]=Ho.prototype.set_iPtSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Nn(e,t)},Object.defineProperty(Ho.prototype,"iPtSectIndex",{get:Ho.prototype.get_iPtSectIndex,set:Ho.prototype.set_iPtSectIndex}),Ho.prototype["get_iIndexSectIndex"]=Ho.prototype.get_iIndexSectIndex=function(){var t=this.ptr;return On(t)},Ho.prototype["set_iIndexSectIndex"]=Ho.prototype.set_iIndexSectIndex=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),kn(e,t)},Object.defineProperty(Ho.prototype,"iIndexSectIndex",{get:Ho.prototype.get_iIndexSectIndex,set:Ho.prototype.set_iIndexSectIndex}),Ho.prototype["__destroy__"]=Ho.prototype.__destroy__=function(){var t=this.ptr;zn(t)},Uo.prototype=Object.create(mo.prototype),Uo.prototype.constructor=Uo,Uo.prototype.__class__=Uo,Uo.__cache__={},e["LBDeal"]=Uo,Uo.prototype["Init"]=Uo.prototype.Init=function(t,e,r,n){var p=this.ptr;return Lo.prepare(),t=t&&"object"===typeof t?t.ptr:Po(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),!!Wn(p,t,e,r,n)},Uo.prototype["ComputeProjToCartesian"]=Uo.prototype.ComputeProjToCartesian=function(t,e,r,n){var p=this.ptr;Lo.prepare(),"object"==typeof t&&(t=Go(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=xo(r)),n&&"object"===typeof n&&(n=n.ptr),Hn(p,t,e,r,n)},Uo.prototype["ComputeCartesianToProj"]=Uo.prototype.ComputeCartesianToProj=function(t,e,r,n,p){var i=this.ptr;Lo.prepare(),"object"==typeof t&&(t=Go(t)),e&&"object"===typeof e&&(e=e.ptr),"object"==typeof r&&(r=xo(r)),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),Un(i,t,e,r,n,p)},Uo.prototype["TranformDegreeToProj"]=Uo.prototype.TranformDegreeToProj=function(t,e,r){var n=this.ptr;Lo.prepare(),"object"==typeof t&&(t=xo(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Yn(n,t,e,r)},Uo.prototype["TranformProjToDegree"]=Uo.prototype.TranformProjToDegree=function(t,e,r){var n=this.ptr;Lo.prepare(),"object"==typeof t&&(t=xo(t)),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),qn(n,t,e,r)},Uo.prototype["__destroy__"]=Uo.prototype.__destroy__=function(){var t=this.ptr;Xn(t)},Yo.prototype=Object.create(mo.prototype),Yo.prototype.constructor=Yo,Yo.prototype.__class__=Yo,Yo.__cache__={},e["LBSpaSelectResultId"]=Yo,Yo.prototype["get_iPrimitiveId"]=Yo.prototype.get_iPrimitiveId=function(){var t=this.ptr;return Jn(t)},Yo.prototype["set_iPrimitiveId"]=Yo.prototype.set_iPrimitiveId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Zn(e,t)},Object.defineProperty(Yo.prototype,"iPrimitiveId",{get:Yo.prototype.get_iPrimitiveId,set:Yo.prototype.set_iPrimitiveId}),Yo.prototype["get_bCluster"]=Yo.prototype.get_bCluster=function(){var t=this.ptr;return!!Kn(t)},Yo.prototype["set_bCluster"]=Yo.prototype.set_bCluster=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Qn(e,t)},Object.defineProperty(Yo.prototype,"bCluster",{get:Yo.prototype.get_bCluster,set:Yo.prototype.set_bCluster}),Yo.prototype["get_iBatchId"]=Yo.prototype.get_iBatchId=function(){var t=this.ptr;return $n(t)},Yo.prototype["set_iBatchId"]=Yo.prototype.set_iBatchId=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),tp(e,t)},Object.defineProperty(Yo.prototype,"iBatchId",{get:Yo.prototype.get_iBatchId,set:Yo.prototype.set_iBatchId}),Yo.prototype["__destroy__"]=Yo.prototype.__destroy__=function(){var t=this.ptr;ep(t)},qo.prototype=Object.create(mo.prototype),qo.prototype.constructor=qo,qo.prototype.__class__=qo,qo.__cache__={},e["LBSpaPrimitiveSpatial"]=qo,qo.prototype["__destroy__"]=qo.prototype.__destroy__=function(){var t=this.ptr;rp(t)},Xo.prototype=Object.create(mo.prototype),Xo.prototype.constructor=Xo,Xo.prototype.__class__=Xo,Xo.__cache__={},e["LBSpaSelectTriResultItem"]=Xo,Xo.prototype["GetResultId"]=Xo.prototype.GetResultId=function(){var t=this.ptr;return bo(np(t),Yo)},Xo.prototype["GetPt0"]=Xo.prototype.GetPt0=function(){var t=this.ptr;return bo(pp(t),Jo)},Xo.prototype["GetPt1"]=Xo.prototype.GetPt1=function(){var t=this.ptr;return bo(ip(t),Jo)},Xo.prototype["GetPt2"]=Xo.prototype.GetPt2=function(){var t=this.ptr;return bo(op(t),Jo)},Xo.prototype["GetPickPt"]=Xo.prototype.GetPickPt=function(){var t=this.ptr;return bo(_p(t),Jo)},Xo.prototype["GetPickNormal"]=Xo.prototype.GetPickNormal=function(){var t=this.ptr;return bo(ap(t),Jo)},Xo.prototype["GetPickDist"]=Xo.prototype.GetPickDist=function(){var t=this.ptr;return cp(t)},Xo.prototype["__destroy__"]=Xo.prototype.__destroy__=function(){var t=this.ptr;up(t)},Jo.prototype=Object.create(mo.prototype),Jo.prototype.constructor=Jo,Jo.prototype.__class__=Jo,Jo.__cache__={},e["LBSpaVec"]=Jo,Jo.prototype["get_x"]=Jo.prototype.get_x=function(){var t=this.ptr;return sp(t)},Jo.prototype["set_x"]=Jo.prototype.set_x=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),yp(e,t)},Object.defineProperty(Jo.prototype,"x",{get:Jo.prototype.get_x,set:Jo.prototype.set_x}),Jo.prototype["get_y"]=Jo.prototype.get_y=function(){var t=this.ptr;return dp(t)},Jo.prototype["set_y"]=Jo.prototype.set_y=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),mp(e,t)},Object.defineProperty(Jo.prototype,"y",{get:Jo.prototype.get_y,set:Jo.prototype.set_y}),Jo.prototype["get_z"]=Jo.prototype.get_z=function(){var t=this.ptr;return fp(t)},Jo.prototype["set_z"]=Jo.prototype.set_z=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),bp(e,t)},Object.defineProperty(Jo.prototype,"z",{get:Jo.prototype.get_z,set:Jo.prototype.set_z}),Jo.prototype["__destroy__"]=Jo.prototype.__destroy__=function(){var t=this.ptr;Sp(t)},Zo.prototype=Object.create(mo.prototype),Zo.prototype.constructor=Zo,Zo.prototype.__class__=Zo,Zo.__cache__={},e["LBSpaTriangle"]=Zo,Zo.prototype["SetPtNum"]=Zo.prototype.SetPtNum=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),hp(n,t,e,r)},Zo.prototype["SetPtVal"]=Zo.prototype.SetPtVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),Bp(p,t,e,r,n)},Zo.prototype["SetUVVal"]=Zo.prototype.SetUVVal=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),gp(n,t,e,r)},Zo.prototype["SetNormVal"]=Zo.prototype.SetNormVal=function(t,e,r,n){var p=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),Lp(p,t,e,r,n)},Zo.prototype["SetIndexNum"]=Zo.prototype.SetIndexNum=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),Pp(e,t)},Zo.prototype["SetIndexVal"]=Zo.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ip(r,t,e)},Zo.prototype["AddTrangle"]=Zo.prototype.AddTrangle=function(t){var e=this.ptr;t&&"object"===typeof t&&(t=t.ptr),jp(e,t)},Zo.prototype["GetPtNum"]=Zo.prototype.GetPtNum=function(){var t=this.ptr;return Gp(t)},Zo.prototype["GetPt"]=Zo.prototype.GetPt=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(xp(e,t),Jo)},Zo.prototype["GetUV"]=Zo.prototype.GetUV=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Cp(e,t),Eo)},Zo.prototype["GetNorm"]=Zo.prototype.GetNorm=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),bo(Mp(e,t),Jo)},Zo.prototype["GetIndexNum"]=Zo.prototype.GetIndexNum=function(){var t=this.ptr;return Rp(t)},Zo.prototype["GetIndex"]=Zo.prototype.GetIndex=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),wp(e,t)},Zo.prototype["__destroy__"]=Zo.prototype.__destroy__=function(){var t=this.ptr;Vp(t)},Ko.prototype=Object.create(mo.prototype),Ko.prototype.constructor=Ko,Ko.prototype.__class__=Ko,Ko.__cache__={},e["LBSpaPrimitive"]=Ko,Ko.prototype["SetPtValNum"]=Ko.prototype.SetPtValNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Dp(r,t,e)},Ko.prototype["SetPtValVal"]=Ko.prototype.SetPtValVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ap(r,t,e)},Ko.prototype["SetBatchIdVal"]=Ko.prototype.SetBatchIdVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Ep(r,t,e)},Ko.prototype["SetIndexNum"]=Ko.prototype.SetIndexNum=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Np(r,t,e)},Ko.prototype["SetIndexVal"]=Ko.prototype.SetIndexVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),Op(r,t,e)},Ko.prototype["SetEdgeCheckVal"]=Ko.prototype.SetEdgeCheckVal=function(t,e){var r=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),kp(r,t,e)},Ko.prototype["InitIndexByPt"]=Ko.prototype.InitIndexByPt=function(){var t=this.ptr;zp(t)},Ko.prototype["__destroy__"]=Ko.prototype.__destroy__=function(){var t=this.ptr;Fp(t)},Qo.prototype=Object.create(mo.prototype),Qo.prototype.constructor=Qo,Qo.prototype.__class__=Qo,Qo.__cache__={},e["LBSpaSelectCondition"]=Qo,Qo.prototype["SetBox"]=Qo.prototype.SetBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),Hp(o,t,e,r,n,p,i)},Qo.prototype["SetRay"]=Qo.prototype.SetRay=function(t,e,r,n,p,i,o,_,a){var c=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),Up(c,t,e,r,n,p,i,o,_,a)},Qo.prototype["SetWedge"]=Qo.prototype.SetWedge=function(t,e,r,n,p,i,o,_,a,c){var u=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),_&&"object"===typeof _&&(_=_.ptr),a&&"object"===typeof a&&(a=a.ptr),c&&"object"===typeof c&&(c=c.ptr),Yp(u,t,e,r,n,p,i,o,_,a,c)},Qo.prototype["SetWedgeByBufferedPoints"]=Qo.prototype.SetWedgeByBufferedPoints=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),qp(n,t,e,r)},Qo.prototype["ClearBuffer"]=Qo.prototype.ClearBuffer=function(){var t=this.ptr;Xp(t)},Qo.prototype["AddBuffer"]=Qo.prototype.AddBuffer=function(t,e,r){var n=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),Jp(n,t,e,r)},Qo.prototype["__destroy__"]=Qo.prototype.__destroy__=function(){var t=this.ptr;Zp(t)},$o.prototype=Object.create(mo.prototype),$o.prototype.constructor=$o,$o.prototype.__class__=$o,$o.__cache__={},e["LBSpaBoxMgr"]=$o,$o.prototype["InsertBox"]=$o.prototype.InsertBox=function(t,e,r,n,p,i,o){var _=this.ptr;Lo.prepare(),t=t&&"object"===typeof t?t.ptr:Po(t),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),o&&"object"===typeof o&&(o=o.ptr),Qp(_,t,e,r,n,p,i,o)},$o.prototype["RemoveBox"]=$o.prototype.RemoveBox=function(t){var e=this.ptr;Lo.prepare(),t=t&&"object"===typeof t?t.ptr:Po(t),$p(e,t)},$o.prototype["SetSelectBox"]=$o.prototype.SetSelectBox=function(t,e,r,n,p,i){var o=this.ptr;t&&"object"===typeof t&&(t=t.ptr),e&&"object"===typeof e&&(e=e.ptr),r&&"object"===typeof r&&(r=r.ptr),n&&"object"===typeof n&&(n=n.ptr),p&&"object"===typeof p&&(p=p.ptr),i&&"object"===typeof i&&(i=i.ptr),ti(o,t,e,r,n,p,i)},$o.prototype["Select"]=$o.prototype.Select=function(){var t=this.ptr;ei(t)},$o.prototype["GetSelectedIdSize"]=$o.prototype.GetSelectedIdSize=function(){var t=this.ptr;return ri(t)},$o.prototype["GetSelectedId"]=$o.prototype.GetSelectedId=function(t){var e=this.ptr;return t&&"object"===typeof t&&(t=t.ptr),A(ni(e,t))},$o.prototype["__destroy__"]=$o.prototype.__destroy__=function(){var t=this.ptr;pi(t)},"function"===typeof e["onModuleParsed"]&&e["onModuleParsed"](),e.ready}}();export default Module;