import * as echarts from 'echarts'

export const line1 = {
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    top: '20',
    right: '50',
    data: ['安全交底率', '计划执行率'],
    textStyle: {
      color: '#fff',
      fontSize: 16
    }
  },
  xAxis: {
    type: 'category',
    boundaryGap: false, // 坐标轴两边留白
    data: ['02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '20:00'],
    axisLabel: {
      interval: 0,
      textStyle: {
        color: '#93B8E2',
        fontSize: 14
      }
    }
  },
  yAxis: [
    {
      type: 'value',
      splitNumber: 5,
      axisLabel: {
        textStyle: {
          color: '#93B8E2',
          fontSize: 14
        },
        formatter: `{value}` + '%'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#2E4867',
          type: 'dashed'
        }
      }
    }
  ],
  series: [
    {
      name: '安全交底率',
      type: 'line',
      itemStyle: {
        normal: {
          color: '#FDCB00',
          lineStyle: {
            color: '#FDCB00',
            width: 1
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: 'rgba(253, 203, 0, 0)'
              },
              {
                offset: 1,
                color: 'rgba(253, 203, 0, 0.55)'
              }
            ])
          }
        }
      },
      data: [67, 88, 55, 45, 65, 55, 85, 75, 65]
    },
    {
      name: '计划执行率',
      type: 'line',
      itemStyle: {
        normal: {
          color: '#00C1FA',
          lineStyle: {
            color: '#00C1FA',
            width: 1
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: 'rgba(0, 193, 250, 0)'
              },
              {
                offset: 1,
                color: 'rgba(0, 193, 250, 0.57)'
              }
            ])
          }
        }
      },
      data: [88, 60, 85, 65, 55, 90, 45, 55, 98]
    }
  ]
}

export const line2 = {
  grid: {
    top: '30px',
    bottom: '10px',
    left: '10px',
    right: '30px',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    top: '20',
    right: '50',
    data: ['计划执行率'],
    textStyle: {
      color: '#fff',
      fontSize: 14
    }
  },
  xAxis: {
    type: 'category',
    boundaryGap: true, // 坐标轴两边留白
    data: ['某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路', '某某路'],
    axisLabel: {
      interval: 0,
      //	margin:15,
      textStyle: {
        color: '#93B8E2',
        fontSize: 12
      }
    },
    axisTick: {
      show: false
    }
  },
  yAxis: [
    {
      type: 'value',
      splitNumber: 5,
      axisLabel: {
        textStyle: {
          color: '#a8aab0',
          fontSize: 12
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#2E4867',
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    }
  ],
  series: [
    {
      name: '计划执行率',
      type: 'line',
      smooth: true,
      symbolSize: 5,
      itemStyle: {
        normal: {
          color: '#00FF07',
          lineStyle: {
            color: '#00FF07',
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: 'rgba(0, 255, 7, 0)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 7, 0.53)'
              }
            ])
          }
        }
      },
      data: [58, 60, 85, 65, 55, 90, 45, 55, 98]
    }
  ]
}

export const line3 = {
  grid: {
    left: '20%',
    right: '15%'
  },
  xAxis: [
    {
      type: 'value',
      name: '车速(km/h)',
      inverse: true,
      nameTextStyle: {
        verticalAlign: 'bottom',
        align: 'right',
        padding: [0, 0, -18, 0]
      },
      splitLine: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#7191C0',
          width: 1,
          type: 'solid'
        }
      }
    },
    {
      type: 'value',
      name: '流量(辆)',
      inverse: true,
      nameTextStyle: {
        verticalAlign: 'top',
        align: 'right',
        padding: [-18, 0, 0, 0]
      },
      splitLine: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#7191C0',
          width: 1,
          type: 'solid'
        }
      }
    }
  ],
  yAxis: {
    position: 'right',
    type: 'category',
    boundaryGap: false,
    name: '时间',
    data: [
      '2023-08-20 09:05',
      '2023-08-20 09:06',
      '2023-08-20 09:07',
      '2023-08-20 09:08',
      '2023-08-20 09:09',
      '2023-08-20 09:10',
      '2023-08-20 09:11',
      '2023-08-20 09:12',
      '2023-08-20 09:13',
      '2023-08-20 09:14',
      '2023-08-20 09:15',
      '2023-08-20 09:16',
      '2023-08-20 09:17',
      '2023-08-20 09:18',
      '2023-08-20 09:19',
      '2023-08-20 09:20',
      '2023-08-20 09:21',
      '2023-08-20 09:22',
      '2023-08-20 09:23',
      '2023-08-20 09:24',
      '2023-08-20 09:25',
      '2023-08-20 09:26',
      '2023-08-20 09:27',
      '2023-08-20 09:28',
      '2023-08-20 09:29',
      '2023-08-20 09:30',
      '2023-08-20 09:31',
      '2023-08-20 09:32',
      '2023-08-20 09:33',
      '2023-08-20 09:34',
      '2023-08-20 09:35',
      '2023-08-20 09:36',
      '2023-08-20 09:37',
      '2023-08-20 09:38',
      '2023-08-20 09:39',
      '2023-08-20 09:40',
      '2023-08-20 09:41',
      '2023-08-20 09:42',
      '2023-08-20 09:43',
      '2023-08-20 09:44',
      '2023-08-20 09:45',
      '2023-08-20 09:46',
      '2023-08-20 09:47',
      '2023-08-20 09:48',
      '2023-08-20 09:49',
      '2023-08-20 09:50',
      '2023-08-20 09:51'
    ],
    inverse: true,
    axisLine: {
      show: true,
      lineStyle: {
        color: '#7191C0',
        width: 1,
        type: 'solid'
      }
    },
    splitLine: {
      show: false
    },
    show: false
  },
  series: [
    {
      lineStyle: {
        color: '#fdbe41'
      },
      itemStyle: {
        color: '#fdbe41'
      },
      name: '流量(辆)',
      type: 'line',
      xAxisIndex: 1,
      data: [
        '11',
        '11',
        '10',
        '15',
        '17',
        '14',
        '14',
        '10',
        '10',
        '16',
        '10',
        '12',
        '16',
        '13',
        '11',
        '11',
        '10',
        '15',
        '17',
        '14',
        '14',
        '10',
        '10',
        '16',
        '10',
        '12',
        '16',
        '13',
        '11',
        '11',
        '10',
        '15',
        '17',
        '14',
        '14',
        '10',
        '10',
        '16',
        '10',
        '12',
        '16',
        '13',
        '11',
        '11',
        '10',
        '15',
        '17'
      ],
      markArea: {
        itemStyle: {
          color: 'rgba(24, 58, 107, .3)'
        },
        data: [
          [
            {
              yAxis: 'min'
            },
            {
              yAxis: 'max'
            }
          ]
        ]
      }
    },
    {
      lineStyle: {
        color: '#00CEFA'
      },
      itemStyle: {
        color: '#00CEFA'
      },
      name: '车速(km/h)',
      type: 'line',
      xAxisIndex: 0,
      data: [
        '67.9',
        '72.3',
        '75',
        '72.5',
        '72.3',
        '70',
        '77.3',
        '78.1',
        '75.9',
        '75',
        '70.1',
        '73.6',
        '74',
        '76.1',
        '67.9',
        '72.3',
        '75',
        '72.5',
        '72.3',
        '70',
        '77.3',
        '78.1',
        '75.9',
        '75',
        '70.1',
        '73.6',
        '74',
        '76.1',
        '67.9',
        '72.3',
        '75',
        '72.5',
        '72.3',
        '70',
        '77.3',
        '78.1',
        '75.9',
        '75',
        '70.1',
        '73.6',
        '74',
        '76.1',
        '67.9',
        '72.3',
        '75',
        '72.5',
        '72.3'
      ],
      markArea: {
        itemStyle: {
          color: 'rgba(24, 58, 107, .3)'
        },
        data: [
          [
            {
              yAxis: 'min'
            },
            {
              yAxis: 'max'
            }
          ]
        ]
      }
    }
  ],
  tooltip: {
    trigger: 'axis',
    extraCssText: 'border-color: #000000;',
    textStyle: {
      color: '#fff'
    },
    backgroundColor: 'rgba(0, 0, 0, 0.5)'
  },
  legend: {
    textStyle: {
      color: '#ffffff'
    }
  }
}
export const line4 = {
  legend: {
    left: 'center',
    itemWidth: 10,
    itemHeight: 2,
    data: [
      {
        name: '不采取养护措施',
        icon: 'rect',
        textStyle: {
          color: '#FFFFFF'
        }
      },
      {
        name: '仅预防性养护措施',
        icon: 'rect',
        textStyle: {
          color: '#FFFFFF'
        }
      },
      {
        name: '预防性与小中修同时',
        icon: 'rect',
        textStyle: {
          color: '#FFFFFF'
        }
      }
    ]
  },
  tooltip: {
    trigger: 'axis'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '0%',
    top: '35%',
    containLabel: true
  },
  xAxis: {
    axisLine: {
      lineStyle: {
        color: '#93B8E2'
      }
    },
    axisTick: {
      show: false
    },
    type: 'category',
    data: [2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037]
  },
  yAxis: {
    type: 'value',
    name: 'PCI',
    nameTextStyle: {
      color: '#fff',
      align: 'right'
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#2E4867'
      }
    },
    axisLine: {
      lineStyle: {
        color: '#93B8E2'
      }
    }
  },
  series: [
    {
      name: '不采取养护措施',
      data: [4, 5, 6, 1, 2, 31, 1, 2, 1, 2, 31, 1, 2, 31, 1],
      type: 'line'
    },
    {
      name: '仅预防性养护措施',
      data: [4, 5, 6, 4, 5, 6, 4, 5, 4, 5, 6, 4, 5, 6, 4],
      type: 'line'
    },
    {
      name: '预防性与小中修同时',
      data: [4, 5, 6, 7, 8, 31, 7, 8, 7, 8, 31, 7, 8, 31, 7],
      type: 'line'
    }
  ],
  color: ['#FEAE0C', '#00B2FE', '#1DFF1C']
}
export const line5 = {
  legend: {
    data: ['能耗'],
    icon: 'rect',
    itemHeight: 2,
    itemWidth: 15,
    textStyle: {
      fontSize: 14,
      color: '#fff'
    },
    right: '30'
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisTick: {
      show: false
    },
    axisLine: {
      lineStyle: {
        color: '#93B8E2',
        width: 0.5
      }
    },
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    axisLabel: {
      textStyle: {
        color: '#93B8E2',
        fontSize: 14,
        fontFamily: 'Alibaba-PuHuiTi-R'
      }
    }
  },
  yAxis: {
    type: 'value',
    name: '能耗/kwh',
    nameTextStyle: {
      color: '#93B8E2',
      fontSize: 15,
      fontFamily: 'Alibaba-PuHuiTi-R'
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        width: 0.5
      }
    },
    axisLabel: {
      textStyle: {
        color: '#93B8E2',
        fontSize: 14,
        fontFamily: 'Alibaba-PuHuiTi-R'
      }
    }
  },
  series: [
    {
      name: '能耗',
      data: [89, 92, 65, 70, 76, 76, 60, 70, 65, 72, 68, 80],
      type: 'line',
      smooth: true,
      itemStyle: {
        normal: {
          color: '#2FEB21'
        }
      },
      lineStyle: {
        normal: {
          color: '#2FEB21',
          width: 1
        }
      },
      areaStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0.1, color: 'rgba(47,235,33, 0.3)' },
            { offset: 0.5, color: 'rgba(0,255,127, 0.1)' },
            { offset: 1, color: 'rgba(0,0,0, 0.1)' }
          ])
        }
      }
    }
  ]
}
export const line6 = {
  legend: {
    top: 15,
    right: 240,
    itemWidth: 12,
    itemHeight: 8,
    itemGap: 20,
    icon: 'rect',
    data: ['气体', '流速', '水质'],
    textStyle: {
      color: '#ffffff',
      fontSize: 12
    }
  },
  grid: {
    left: '5%',
    right: '4%',
    bottom: '5%',
    containLabel: true
  },
  xAxis: {
    axisTick: {
      show: false
    },
    type: 'category',
    boundaryGap: false,
    axisLabel: {
      textStyle: {
        color: '#93B8E2'
      }
    },
    data: ['11:01', '11:02', '11:03', '11:04', '11:05', '11:06', '11:07', '11:08', '11:09', '11:10']
  },
  yAxis: {
    name: '浊度/NTU',
    type: 'value',
    min: 4.0,
    max: 4.8,
    axisLabel: {
      textStyle: {
        color: '#93B8E2',
        fontSize: 14,
        fontFamily: 'Alibaba-PuHuiTi-R'
      },
      formatter: function (value, index) {
        return value.toFixed(1)
      }
    },
    axisLine: {
      show: false,
      lineStyle: {
        color: '#93B8E2'
      }
    },

    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        width: 0.5
      }
    }
    /* axisLabel: {
        formatter: '{value}' // 标签格式
      } */
  },
  series: [
    {
      name: '气体',
      type: 'line',
      data: [4.8, 4.6, 4.6, 4.4, 4.7, 4.3, 4.0, 4.4, 4.2, 4.5]
    },
    {
      name: '流速',
      type: 'line',
      color: '#ECB40F',
      data: [4.5, 4.2, 4.4, 4.8, 4.4, 4.4, 4.4, 4.3, 4.4, 4.4]
    },
    {
      name: '水质',
      type: 'line',
      data: [4.6, 4.7, 4.5, 4.5, 4.6, 4.4, 4.8, 4.6, 4.0, 4.7]
    }
  ]
}

export const line7 = {
  baseOption: {
    timeline: {
      axisType: 'category',
      data: [
        { range: ['2023-11-30 00:00:00', '2023-11-30 01:00:00'], value: '2023-11-30 00:00:00' },
        { range: ['2023-11-30 01:00:00', '2023-11-30 02:00:00'], value: '2023-11-30 01:00:00' },
        { range: ['2023-11-30 02:00:00', '2023-11-30 03:00:00'], value: '2023-11-30 02:00:00' },
        { range: ['2023-11-30 03:00:00', '2023-11-30 04:00:00'], value: '2023-11-30 03:00:00' },
        { range: ['2023-11-30 04:00:00', '2023-11-30 05:00:00'], value: '2023-11-30 04:00:00' },
        { range: ['2023-11-30 05:00:00', '2023-11-30 06:00:00'], value: '2023-11-30 05:00:00' },
        { range: ['2023-11-30 06:00:00', '2023-11-30 07:00:00'], value: '2023-11-30 06:00:00' },
        { range: ['2023-11-30 07:00:00', '2023-11-30 08:00:00'], value: '2023-11-30 07:00:00' },
        { range: ['2023-11-30 08:00:00', '2023-11-30 09:00:00'], value: '2023-11-30 08:00:00' },
        { range: ['2023-11-30 09:00:00', '2023-11-30 10:00:00'], value: '2023-11-30 09:00:00' },
        { range: ['2023-11-30 10:00:00', '2023-11-30 10:28:02'], value: '2023-11-30 10:00:00' }
      ],
      left: 20,
      right: 20,
      autoPlay: true,
      playInterval: 1500,
      tooltip: {
        formatter: {
          _custom: {
            type: 'function',
            display: '<span style="opacity:.5;">function</span> formatter(param)',
            tooltip: "<pre>function formatter(param) {\n                return param.data.range[0] + ' \\u81F3 ' + param.data.range[1];\n              }</pre>",
            _reviveId: 28730
          }
        }
      }
    },
    legend: {},
    grid: { top: 40, left: 60, right: 40, bottom: 80 },
    xAxis: {
      type: 'category',
      data: ['南塔塔梁结合部', '斜拉桥主跨南侧四分之一点', '斜拉桥主跨跨中', '斜拉桥主跨北侧四分之一点', '北塔塔梁结合部'],
      splitLine: { show: false },
      axisLine: { lineStyle: {} }
    },
    yAxis: { type: 'value', axisLine: { show: false }, axisTick: { show: false } },
    title: {
      textAlign: 'center',
      textVerticalAlign: 'middle',
      itemGap: 5,
      textStyle: { fontSize: 20, fontWeight: 'bold', color: 'rgba(7, 111, 149, 1)' },
      subtextStyle: { fontSize: 12, fontWeight: 'normal', color: 'rgba(7, 111, 149, 1)' },
      top: '30%',
      left: '28%'
    },
    tooltip: { show: true, trigger: 'axis', axisPointer: { type: 'none' } },
    series: [
      {
        type: 'line',
        smooth: true,
        data: [],
        symbolSize: 6,
        itemStyle: { normal: { color: 'rgb(89, 117, 200)', borderWidth: 3 } },
        markLine: {
          silent: true,
          symbolSize: [0, 0],
          data: [{ yAxis: 47, lineStyle: { normal: { color: 'red' } } }],
          animation: false,
          lineStyle: { width: 3 }
        },
        lineStyle: { width: 3 }
      }
    ]
  },
  options: [
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -15.35, 24.87, 38.69, 48.55], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -13.07, 29.09, 41.71, 49.5], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -13.27, 28.98, 42.13, 49.94], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -13.27, 28.82, 42.02, 49.84], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -12.38, 30.23, 43.23, 49.13], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -13.45, 28.85, 41.9, 48.78], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -14.24, 26.41, 40.48, 49.3], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -20.54, 13.43, 32.72, 48.42], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -19.47, 17.42, 34.46, 48.21], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -18.15, 19.07, 35.99, 49.67], name: '测点数据', markLine: { data: [] } }]
    },
    {
      title: { subtextStyle: { fontSize: 14, fontWeight: 'normal' }, left: 'center' },
      series: [{ type: 'line', data: [0, -19.8, 16.68, 33.91, 47.85], name: '测点数据', markLine: { data: [] } }]
    }
  ]
}
