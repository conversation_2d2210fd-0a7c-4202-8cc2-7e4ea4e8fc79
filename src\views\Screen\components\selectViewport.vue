<template>
  <div class="select-view-port-container">
    <el-tree-select
      v-model="selectedViewport"
      :data="data"
      check-strictly
      :render-after-expand="false"
      popper-class="select-view-port-popper"
      class="select-view-port"
      default-expand-all
      @blur="handleBlur"
      @visible-change="handleVisibleChange"
      @change="handleChange" />
  </div>
</template>

<script setup lang="ts">
  import lib from '@/utils/lib'
  const selectedViewport = ref('默认视口')
  const data = ref([
    {
      label: '默认',
      value: '默认视口'
    },
    {
      label: '项目部',
      value: '项目部#2',
      children: [
        { label: '仓库', value: '仓库#2.1', children: [{ label: '仓库内部', value: '仓库内部#2.1.1' }] },
        { label: '宿舍', value: '宿舍#2.2' }
      ]
    },
    {
      label: '隧道',
      value: '隧道#3',
      children: [
        { label: '江南入口', value: '江南入口#3.1' },
        { label: '江北入口', value: '江北入口#3.2' },

        { label: '江南入口风机', value: '江南入口风机' },
        { label: '东线江中段', value: '东线江中段' },

        {
          label: '风井',
          value: '风井#3.3',
          children: [
            { label: '江南风井', value: '江南风井#3.3.1' },
            { label: '江北风井', value: '江北风井#3.3.2' }
          ]
        }
      ]
    }
  ])
  const handleChange = (value: string) => {
    console.log(value)
    lib._engineController.gotoViewportByName(value)
    setTimeout(() => {
      emits('clearShow')
    }, 150)
  }

  const setViewPort = (value: string) => {
    selectedViewport.value = value
  }
  const emits = defineEmits(['clearShow'])
  const handleBlur = () => {
    emits('clearShow')
  }
  const handleVisibleChange = (visible: boolean) => {
    console.log(visible)
    emits('clearShow', !visible)
  }
  defineExpose({ setViewPort })
</script>

<style scoped>
  .select-view-port-container {
    position: absolute;
    right: -210px;
    bottom: 23px;
    z-index: 999;
    width: 200px;
    height: 40px;
    .select-view-port {
      width: 200px;
      height: 40px;
    }

    --el-fill-color-blank: #0c0c0c !important;
    --el-select-input-focus-border-color: #ffffff;
    --el-border-color: #ffffff;
    :deep(.el-select__wrapper) {
      color: #ffffff;
    }
    :deep(.el-input__wrapper) {
      background: url('@/assets/CommonPopup/selectList2.png') no-repeat;
      background-size: 100% 40px;
      box-shadow: none;
    }
    :deep(.el-input__inner) {
      height: 40px;
      font-size: 24px;
      color: #ffffff;
      background-color: transparent !important;
      border: none;
      box-shadow: none;
    }
    :deep(.el-select__placeholder) {
      color: #ffffffff;
    }
    :deep(.el-input__suffix-inner) {
      scale: 1.5;
    }
  }
</style>
<style>
  .select-view-port-popper {
    z-index: 9999;
    background: #093161;

    --el-fill-color-blank: transparent;
    --el-fill-color-light: transparent;
    --el-text-color-regular: #ffffff;
    --el-border-color-light: #47b7ff;
  }
</style>
